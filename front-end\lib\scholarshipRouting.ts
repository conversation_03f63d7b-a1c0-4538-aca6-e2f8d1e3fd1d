import { User } from '@/types/auth'

export interface ScholarshipRoutingConfig {
  category: 'primary' | 'secondary' | 'university'
  scholarshipId?: string
  redirectUrl?: string
}

export interface RoutingResult {
  shouldRedirect: boolean
  redirectUrl?: string
  errorMessage?: string
  requiresAuth: boolean
  requiredRole?: string
}

/**
 * Scholarship Category-Based Routing Service
 * Handles intelligent routing based on scholarship categories and user roles
 */
export class ScholarshipRoutingService {
  
  /**
   * Determine routing based on scholarship category and user authentication
   */
  static determineRouting(config: ScholarshipRoutingConfig, user?: User | null): RoutingResult {
    const { category, scholarshipId, redirectUrl } = config

    // Check if user is authenticated
    if (!user) {
      return this.handleUnauthenticatedUser(category, scholarshipId, redirectUrl)
    }

    // Check role-based access for authenticated users
    return this.handleAuthenticatedUser(category, user, scholarshipId, redirectUrl)
  }

  /**
   * Handle routing for unauthenticated users
   */
  private static handleUnauthenticatedUser(
    category: string, 
    scholarshipId?: string, 
    redirectUrl?: string
  ): RoutingResult {
    const currentUrl = redirectUrl || window.location.pathname + window.location.search
    
    // Store redirect URL for after login
    if (typeof window !== 'undefined') {
      localStorage.setItem('redirectAfterLogin', currentUrl)
    }

    // Determine appropriate login route based on category
    let loginUrl = '/auth/login'
    let errorMessage = 'Please log in to apply for scholarships'
    let requiredRole = 'user'

    if (category === 'primary' || category === 'secondary') {
      loginUrl = '/auth/login?type=partner'
      errorMessage = 'Partner Organizations (Schools) must log in to apply for Primary/Secondary scholarships on behalf of students'
      requiredRole = 'partner_organization'
    } else if (category === 'university') {
      loginUrl = '/auth/login?type=student'
      errorMessage = 'Students must log in to apply for University scholarships'
      requiredRole = 'student'
    }

    // Add scholarship context to login URL
    if (scholarshipId) {
      loginUrl += `&scholarship_id=${scholarshipId}&category=${category}`
    }

    return {
      shouldRedirect: true,
      redirectUrl: loginUrl,
      errorMessage,
      requiresAuth: true,
      requiredRole
    }
  }

  /**
   * Handle routing for authenticated users
   */
  private static handleAuthenticatedUser(
    category: string, 
    user: User, 
    scholarshipId?: string, 
    redirectUrl?: string
  ): RoutingResult {
    const userRole = user.role
    const isPartnerOrg = userRole === 'partner_organization' || !!user.partner_organization_id
    const isIndividualStudent = !user.partner_organization_id && (userRole === 'user' || userRole === 'student')

    // Check Primary/Secondary category access
    if ((category === 'primary' || category === 'secondary') && !isPartnerOrg) {
      return {
        shouldRedirect: true,
        redirectUrl: '/dashboard',
        errorMessage: 'Primary and Secondary scholarship applications must be submitted by Partner Organizations (Schools)',
        requiresAuth: true,
        requiredRole: 'partner_organization'
      }
    }

    // Check University category access
    if (category === 'university' && !isIndividualStudent) {
      return {
        shouldRedirect: true,
        redirectUrl: '/dashboard',
        errorMessage: 'University scholarship applications must be submitted by individual students',
        requiresAuth: true,
        requiredRole: 'student'
      }
    }

    // User has appropriate access - determine target dashboard
    let targetUrl = this.getTargetDashboard(category, userRole, scholarshipId)

    return {
      shouldRedirect: false,
      redirectUrl: targetUrl,
      requiresAuth: true
    }
  }

  /**
   * Get appropriate dashboard URL based on category and user role
   */
  private static getTargetDashboard(category: string, userRole: string, scholarshipId?: string): string {
    let baseUrl = ''

    if (category === 'primary' || category === 'secondary') {
      baseUrl = '/dashboard/partner'
    } else if (category === 'university') {
      baseUrl = '/dashboard/student'
    } else {
      baseUrl = '/dashboard'
    }

    // Add scholarship context if provided
    if (scholarshipId) {
      baseUrl += `?scholarship_id=${scholarshipId}&category=${category}`
    }

    return baseUrl
  }

  /**
   * Handle scholarship application page routing
   */
  static handleScholarshipApplicationRouting(
    scholarshipId: string, 
    category: string, 
    user?: User | null
  ): RoutingResult {
    return this.determineRouting({
      category: category as 'primary' | 'secondary' | 'university',
      scholarshipId,
      redirectUrl: `/scholarships/${scholarshipId}/apply`
    }, user)
  }

  /**
   * Handle general scholarship page routing
   */
  static handleScholarshipPageRouting(
    category: string, 
    user?: User | null,
    scholarshipId?: string
  ): RoutingResult {
    return this.determineRouting({
      category: category as 'primary' | 'secondary' | 'university',
      scholarshipId,
      redirectUrl: scholarshipId ? `/scholarships/${scholarshipId}` : '/scholarships'
    }, user)
  }

  /**
   * Get appropriate application form URL based on category and user
   */
  static getApplicationFormUrl(
    scholarshipId: string, 
    category: string, 
    user?: User | null
  ): string {
    if (!user) {
      return `/auth/login?scholarship_id=${scholarshipId}&category=${category}`
    }

    const userRole = user.role
    const isPartnerOrg = userRole === 'partner_organization' || !!user.partner_organization_id

    if ((category === 'primary' || category === 'secondary') && isPartnerOrg) {
      return `/dashboard/partner/applications/new?scholarship_id=${scholarshipId}&category=${category}`
    } else if (category === 'university' && !user.partner_organization_id) {
      return `/dashboard/student/applications/new?scholarship_id=${scholarshipId}&category=${category}`
    } else {
      // Fallback to general application page
      return `/scholarship-application?scholarship_id=${scholarshipId}&category=${category}`
    }
  }

  /**
   * Validate user access to scholarship category
   */
  static validateUserAccess(category: string, user?: User | null): {
    hasAccess: boolean
    errorMessage?: string
    requiredRole?: string
  } {
    if (!user) {
      return {
        hasAccess: false,
        errorMessage: 'Authentication required',
        requiredRole: category === 'university' ? 'student' : 'partner_organization'
      }
    }

    const userRole = user.role
    const isPartnerOrg = userRole === 'partner_organization' || !!user.partner_organization_id
    const isIndividualStudent = !user.partner_organization_id && (userRole === 'user' || userRole === 'student')

    if ((category === 'primary' || category === 'secondary') && !isPartnerOrg) {
      return {
        hasAccess: false,
        errorMessage: 'Primary and Secondary scholarship applications must be submitted by Partner Organizations (Schools)',
        requiredRole: 'partner_organization'
      }
    }

    if (category === 'university' && !isIndividualStudent) {
      return {
        hasAccess: false,
        errorMessage: 'University scholarship applications must be submitted by individual students',
        requiredRole: 'student'
      }
    }

    return { hasAccess: true }
  }

  /**
   * Get category-specific messaging
   */
  static getCategoryMessages(category: string): {
    title: string
    description: string
    loginPrompt: string
    accessRequirement: string
  } {
    switch (category) {
      case 'primary':
        return {
          title: 'Primary School Scholarships',
          description: 'Scholarships for students in grades 1-6, applied for by their schools',
          loginPrompt: 'Schools must log in to apply on behalf of their students',
          accessRequirement: 'Partner Organization (School) account required'
        }
      
      case 'secondary':
        return {
          title: 'Secondary School Scholarships',
          description: 'Scholarships for students in grades 7-12, applied for by their schools',
          loginPrompt: 'Schools must log in to apply on behalf of their students',
          accessRequirement: 'Partner Organization (School) account required'
        }
      
      case 'university':
        return {
          title: 'University Scholarships',
          description: 'Scholarships for university students, applied for individually',
          loginPrompt: 'Students must log in to apply for university scholarships',
          accessRequirement: 'Individual student account required'
        }
      
      default:
        return {
          title: 'Scholarships',
          description: 'Educational scholarships and financial aid opportunities',
          loginPrompt: 'Please log in to apply for scholarships',
          accessRequirement: 'User account required'
        }
    }
  }

  /**
   * Handle post-login redirect
   */
  static handlePostLoginRedirect(): string | null {
    if (typeof window === 'undefined') return null
    
    const redirectUrl = localStorage.getItem('redirectAfterLogin')
    if (redirectUrl) {
      localStorage.removeItem('redirectAfterLogin')
      return redirectUrl
    }
    
    return null
  }
}
