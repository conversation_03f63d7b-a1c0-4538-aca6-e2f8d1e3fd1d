 # 🔧 HLTKKQ Foundation - Complete Backend API Guide

## Overview

This guide provides comprehensive documentation for the <PERSON>vel backend API that powers the HLTKKQ Foundation NGO platform. The backend serves as a RESTful API with robust authentication, user management, content management, and NGO-specific functionality.

## API Base Information

- **Base URL**: `http://localhost:8000/api/v1` (Development)
- **Authentication**: <PERSON><PERSON> Sanctum (Bearer Token)
- **Response Format**: JSON
- **API Version**: v1

## Authentication System

### Authentication Endpoints

#### Register User
```http
POST /api/v1/register
Content-Type: application/json

{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>", 
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "phone_number": "+2348012345678",
  "user_type": "student|partner|volunteer",
  "preferences": {
    "educational_institution": "University of Lagos",
    "course_of_study": "Computer Science"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 1,
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "email": "<EMAIL>",
      "role": "user",
      "status": "active",
      "short_id": "HLTKKQ0001"
    },
    "access_token": "1|abc123def456..."
  }
}
```

#### Login
```http
POST /api/v1/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Logout
```http
POST /api/v1/logout
Authorization: Bearer {token}
```

#### Get Current User
```http
GET /api/v1/user
Authorization: Bearer {token}
```

### Protected Route Pattern
All authenticated endpoints require the Authorization header:
```http
Authorization: Bearer {your-access-token}
```

## User Management API

### Profile Management

#### Get User Profile
```http
GET /api/v1/profile
Authorization: Bearer {token}
```

#### Update Profile
```http
PUT /api/v1/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+2348012345678",
  "address": "123 Main Street, Lagos",
  "city": "Lagos",
  "state": "Lagos State",
  "preferences": {
    "notifications": true,
    "newsletter": true
  }
}
```

#### Upload Avatar
```http
POST /api/v1/profile/avatar
Authorization: Bearer {token}
Content-Type: multipart/form-data

avatar: [file]
```

#### Change Password
```http
PUT /api/v1/profile/password
Authorization: Bearer {token}
Content-Type: application/json

{
  "current_password": "oldpassword",
  "password": "newpassword",
  "password_confirmation": "newpassword"
}
```

## Blog Management API

### Public Blog Endpoints

#### Get Blog Posts
```http
GET /api/v1/blog/posts?page=1&per_page=15&category=education&search=scholarship
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 15)
- `category`: Filter by category slug
- `search`: Search in title and content

**Response:**
```json
{
  "success": true,
  "posts": {
    "data": [
      {
        "id": 1,
        "title": "Educational Opportunities in Nigeria",
        "slug": "educational-opportunities-nigeria",
        "excerpt": "Exploring various educational opportunities...",
        "featured_image_url": "http://localhost:8000/storage/blog/image.jpg",
        "author": {
          "id": 1,
          "full_name": "Admin User"
        },
        "category": {
          "id": 1,
          "name": "Education",
          "slug": "education"
        },
        "published_at": "2024-01-15T10:00:00Z",
        "reading_time": "5 min read",
        "views_count": 150,
        "tags": ["education", "nigeria", "opportunities"]
      }
    ],
    "current_page": 1,
    "last_page": 3,
    "total": 45
  }
}
```

#### Get Single Blog Post
```http
GET /api/v1/blog/posts/{slug}
```

#### Get Blog Categories
```http
GET /api/v1/blog/categories
```

### Authenticated Blog Endpoints

#### Comment on Blog Post
```http
POST /api/v1/blog/posts/{id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Great article! Very informative.",
  "parent_id": null
}
```

## Event Management API

### Public Event Endpoints

#### Get Events
```http
GET /api/v1/events?page=1&type=fundraising&upcoming=true
```

**Query Parameters:**
- `type`: Filter by event type (fundraising, volunteer, awareness, training)
- `upcoming`: Show only upcoming events (true/false)
- `search`: Search in title and description

#### Get Single Event
```http
GET /api/v1/events/{id}
```

### Authenticated Event Endpoints

#### Register for Event
```http
POST /api/v1/events/{id}/register
Authorization: Bearer {token}
Content-Type: application/json

{
  "additional_info": "Dietary restrictions: Vegetarian"
}
```

#### Get User's Event Registrations
```http
GET /api/v1/events/my-registrations
Authorization: Bearer {token}
```

## Donation Management API

### Public Donation Endpoints

#### Get Donation Campaigns
```http
GET /api/v1/donations/campaigns
```

#### Get Single Campaign
```http
GET /api/v1/donations/campaigns/{id}
```

### Authenticated Donation Endpoints

#### Make Donation
```http
POST /api/v1/donations
Authorization: Bearer {token}
Content-Type: application/json

{
  "campaign_id": 1,
  "amount": 50000,
  "payment_method": "paystack",
  "donor_name": "John Doe",
  "donor_email": "<EMAIL>",
  "anonymous": false,
  "message": "Keep up the great work!"
}
```

#### Get User's Donations
```http
GET /api/v1/donations/my-donations
Authorization: Bearer {token}
```

## Volunteer Management API

### Public Volunteer Endpoints

#### Get Volunteer Opportunities
```http
GET /api/v1/volunteer/opportunities
```

### Authenticated Volunteer Endpoints

#### Submit Volunteer Application
```http
POST /api/v1/volunteer/apply
Authorization: Bearer {token}
Content-Type: application/json

{
  "skills": ["Teaching", "Event Management", "Social Media"],
  "interests": ["Education", "Community Development"],
  "availability": {
    "weekdays": ["Monday", "Wednesday", "Friday"],
    "weekends": true,
    "time_slots": ["Morning", "Evening"]
  },
  "experience": "I have 2 years of experience working with children...",
  "motivation": "I want to give back to my community...",
  "emergency_contact": {
    "name": "Jane Doe",
    "relationship": "Sister", 
    "phone": "+2348087654321"
  }
}
```

#### Log Volunteer Hours
```http
POST /api/v1/volunteer/hours
Authorization: Bearer {token}
Content-Type: application/json

{
  "date": "2024-01-15",
  "hours": 4,
  "activity": "Teaching mathematics to primary school students",
  "location": "Community Center",
  "description": "Taught basic arithmetic to 15 students aged 8-12"
}
```

#### Get Volunteer Hours
```http
GET /api/v1/volunteer/hours?month=2024-01
Authorization: Bearer {token}
```

## Scholarship Management API

### Public Scholarship Endpoints

#### Get Scholarships
```http
GET /api/v1/scholarships?status=active&level=undergraduate
```

#### Get Single Scholarship
```http
GET /api/v1/scholarships/{id}
```

### Authenticated Scholarship Endpoints

#### Apply for Scholarship
```http
POST /api/v1/scholarships/{id}/apply
Authorization: Bearer {token}
Content-Type: application/json

{
  "application_data": {
    "personal_statement": "I am passionate about...",
    "academic_achievements": "First Class honors...",
    "financial_need": "My family income is...",
    "career_goals": "I aspire to become..."
  },
  "documents": {
    "transcript": "base64_encoded_file",
    "recommendation_letter": "base64_encoded_file"
  }
}
```

#### Get User's Applications
```http
GET /api/v1/scholarships/my-applications
Authorization: Bearer {token}
```

## Contact & Newsletter API

### Contact Form
```http
POST /api/v1/contact
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+2348012345678",
  "subject": "General Inquiry",
  "message": "I would like to know more about your programs."
}
```

### Newsletter Subscription
```http
POST /api/v1/newsletter/subscribe
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe"
}
```

## Admin API Endpoints

All admin endpoints require admin role and are prefixed with `/api/v1/admin/`.

### Dashboard Analytics
```http
GET /api/v1/admin/dashboard
Authorization: Bearer {admin-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_users": 1250,
    "total_donations": 850000,
    "active_volunteers": 45,
    "pending_applications": 12,
    "recent_activities": [...],
    "monthly_stats": {...}
  }
}
```

### User Management
```http
GET /api/v1/admin/users?page=1&role=volunteer&status=active
PUT /api/v1/admin/users/{id}/toggle-status
POST /api/v1/admin/users/{id}/assign-role
```

### Content Management
```http
GET /api/v1/admin/blog/posts
POST /api/v1/admin/blog/posts
PUT /api/v1/admin/blog/posts/{id}
DELETE /api/v1/admin/blog/posts/{id}
```

## Error Handling

### Error Response Format
All API errors follow this consistent format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Server Error

### Common Error Examples

#### Validation Error (422)
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "email": ["The email field is required."],
    "password": ["The password must be at least 8 characters."]
  }
}
```

#### Authentication Error (401)
```json
{
  "success": false,
  "message": "Unauthenticated."
}
```

## Rate Limiting

- **Public endpoints**: 60 requests per minute
- **Authenticated endpoints**: 120 requests per minute
- **Admin endpoints**: 300 requests per minute

## File Upload Guidelines

### Supported File Types
- **Images**: JPG, PNG, GIF (max 2MB)
- **Documents**: PDF, DOC, DOCX (max 5MB)
- **Avatars**: JPG, PNG (max 1MB)

### Upload Response Format
```json
{
  "success": true,
  "data": {
    "file_path": "uploads/documents/file.pdf",
    "file_url": "http://localhost:8000/storage/uploads/documents/file.pdf",
    "file_size": 1024000,
    "mime_type": "application/pdf"
  }
}
```

## Database Schema Overview

### Core Tables
- `users` - User accounts and profiles
- `volunteers` - Volunteer applications and data
- `blog_posts` - Blog content
- `blog_categories` - Blog categories
- `blog_comments` - Blog comments
- `events` - Event management
- `event_registrations` - Event registrations
- `donations` - Donation records
- `donation_campaigns` - Fundraising campaigns
- `scholarships` - Scholarship programs
- `scholarship_applications` - Applications
- `contact_messages` - Contact form submissions
- `newsletter_subscribers` - Newsletter subscriptions

### Key Relationships
- Users can have multiple volunteer applications
- Users can register for multiple events
- Users can make multiple donations
- Blog posts belong to categories and have comments
- Scholarships have multiple applications

## Testing the API

### Using cURL

```bash
# Test login
curl -X POST http://localhost:8000/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test authenticated endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/profile

# Test blog posts
curl "http://localhost:8000/api/v1/blog/posts?page=1&per_page=5"
```

### Using Postman
1. Import the API collection
2. Set environment variables for base URL and token
3. Test authentication flow first
4. Use bearer token for protected endpoints

## API Documentation Tools

- **L5 Swagger**: Available at `/api/documentation`
- **Postman Collection**: Available in project root
- **API Tests**: Run with `php artisan test`

## Best Practices

### API Request Guidelines
1. Always include `Accept: application/json` header
2. Use appropriate HTTP methods (GET, POST, PUT, DELETE)
3. Include authentication token for protected routes
4. Handle pagination for list endpoints
5. Implement proper error handling

### Response Handling
1. Check `success` field before processing data
2. Handle different HTTP status codes appropriately
3. Display user-friendly error messages
4. Implement loading states for async operations

### Security Considerations
1. Never log sensitive data (passwords, tokens)
2. Validate all input data
3. Use HTTPS in production
4. Implement proper CORS settings
5. Rate limit API requests

---

> **🔧 This API powers the mission of educational empowerment in Nigeria. Every endpoint is designed to support the NGO's noble goals of transforming lives through technology and education.**