<?php $__env->startSection('title', 'Edit Donation'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Donation</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.donations.index')); ?>">Donations</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('admin.donations.show', $donation->id)); ?>" class="btn btn-info">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Donation #<?php echo e($donation->id); ?></h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.donations.update', $donation->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_status" class="font-weight-bold">Payment Status <span class="text-danger">*</span></label>
                                    <select class="form-control <?php $__errorArgs = ['payment_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="payment_status" name="payment_status" required>
                                        <option value="">Select Status</option>
                                        <option value="pending" <?php echo e(old('payment_status', $donation->payment_status) === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                        <option value="completed" <?php echo e(old('payment_status', $donation->payment_status) === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                        <option value="failed" <?php echo e(old('payment_status', $donation->payment_status) === 'failed' ? 'selected' : ''); ?>>Failed</option>
                                        <option value="refunded" <?php echo e(old('payment_status', $donation->payment_status) === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                                    </select>
                                    <?php $__errorArgs = ['payment_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Amount (Read Only)</label>
                                    <input type="text" class="form-control" value="₦<?php echo e(number_format($donation->amount, 2)); ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes" class="font-weight-bold">Admin Notes</label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="4" placeholder="Add any administrative notes about this donation..."><?php echo e(old('notes', $donation->notes)); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="form-text text-muted">These notes are for internal use only and will not be visible to the donor.</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Donation
                            </button>
                            <a href="<?php echo e(route('admin.donations.show', $donation->id)); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Donation Information (Read Only) -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Information</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Donation ID:</label>
                        <p class="text-muted">#<?php echo e($donation->id); ?></p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Donor:</label>
                        <p class="text-muted">
                            <?php if($donation->donor): ?>
                                <?php echo e($donation->donor->first_name); ?> <?php echo e($donation->donor->last_name); ?>

                            <?php else: ?>
                                <?php echo e($donation->is_anonymous ? 'Anonymous' : 'N/A'); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Type:</label>
                        <p class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', $donation->donation_type))); ?></p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Payment Method:</label>
                        <p class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', $donation->payment_method))); ?></p>
                    </div>
                    <?php if($donation->purpose): ?>
                    <div class="form-group">
                        <label class="font-weight-bold">Purpose:</label>
                        <p class="text-muted"><?php echo e($donation->purpose); ?></p>
                    </div>
                    <?php endif; ?>
                    <div class="form-group">
                        <label class="font-weight-bold">Created:</label>
                        <p class="text-muted"><?php echo e($donation->created_at->format('M d, Y h:i A')); ?></p>
                    </div>
                </div>
            </div>

            <?php if($donation->campaign): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Campaign:</label>
                        <p class="text-muted"><?php echo e($donation->campaign->title); ?></p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Goal:</label>
                        <p class="text-muted">₦<?php echo e(number_format($donation->campaign->goal_amount, 2)); ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/donations/edit.blade.php ENDPATH**/ ?>