<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class PopulateUserQrCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:populate-qr-codes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate existing users with short IDs and QR codes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to populate users with QR codes...');

        // Get all users without short_id
        $users = User::whereNull('short_id')->orWhereNull('qr_code_url')->get();

        if ($users->count() === 0) {
            $this->info('All users already have QR codes.');
            return 0;
        }

        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();

        foreach ($users as $user) {
            // Generate short ID if missing
            if (empty($user->short_id)) {
                $user->short_id = User::generateShortId();
            }

            // Generate QR code if missing
            if (empty($user->qr_code_url)) {
                $user->qr_code_url = $user->generateQrCodeUrl();
            }

            $user->save();
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Successfully populated {$users->count()} users with QR codes.");

        return 0;
    }
}
