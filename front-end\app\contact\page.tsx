"use client"

import type React from "react"

import { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Send,
  MessageSquare,
  Heart,
  CheckCircle,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
} from "lucide-react"
import { useSettings } from "@/hooks/useSettings"

export default function ContactPage() {
  const { settings, loading: settingsLoading } = useSettings()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  // Get dynamic settings or use defaults
  const appName = settings?.app_name || 'Laravel NGO Foundation'
  const contactEmail = settings?.contact_email || '<EMAIL>'
  const contactPhone = settings?.contact_phone || '+234 ************'
  
  // Address information
  const organizationAddress = settings?.organization_address
  const organizationCity = settings?.organization_city
  const organizationState = settings?.organization_state
  const organizationCountry = settings?.organization_country
  const organizationPostalCode = settings?.organization_postal_code
  
  // Construct full address
  const fullAddress = [
    organizationAddress,
    organizationCity,
    organizationState,
    organizationCountry,
    organizationPostalCode
  ].filter(Boolean).join(', ')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      setIsSubmitted(true)
      setFormData({ name: "", email: "", subject: "", message: "" })
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              {settings?.app_logo ? (
                <div className="relative h-20 w-20 overflow-hidden rounded-full shadow-lg">
                  <img 
                    src={settings.app_logo} 
                    alt={`${appName} Logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="relative h-20 w-20 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg">
                  <MessageSquare className="absolute inset-0 m-auto h-10 w-10 text-white" />
                </div>
              )}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-green-800 dark:text-green-200 mb-6">Get in Touch</h1>
            <p className="text-xl text-green-700 dark:text-green-300 max-w-2xl mx-auto">
              We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
          </div>
        </section>

        <div className="max-w-7xl mx-auto px-4 pb-20">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="shadow-xl border-green-200 dark:border-green-800">
                <CardHeader>
                  <CardTitle className="text-2xl text-green-800 dark:text-green-200">Send us a Message</CardTitle>
                  <CardDescription className="text-green-600 dark:text-green-400">
                    Fill out the form below and we'll get back to you within 24 hours.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isSubmitted ? (
                    <div className="text-center py-8">
                      <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
                        Message Sent Successfully!
                      </h3>
                      <p className="text-green-600 dark:text-green-400">
                        Thank you for reaching out. We'll get back to you soon.
                      </p>
                      <Button onClick={() => setIsSubmitted(false)} className="mt-4 bg-green-600 hover:bg-green-700">
                        Send Another Message
                      </Button>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-green-800 dark:text-green-200">
                            Full Name *
                          </Label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                            className="border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-green-800 dark:text-green-200">
                            Email Address *
                          </Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                            className="border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subject" className="text-green-800 dark:text-green-200">
                          Subject *
                        </Label>
                        <Input
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="What is this regarding?"
                          className="border-green-200 dark:border-green-700 focus:border-green-500"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message" className="text-green-800 dark:text-green-200">
                          Message *
                        </Label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          placeholder="Tell us more about your inquiry..."
                          rows={6}
                          className="border-green-200 dark:border-green-700 focus:border-green-500"
                          required
                        />
                      </div>

                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            Sending Message...
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Send className="h-4 w-4" />
                            Send Message
                          </div>
                        )}
                      </Button>
                    </form>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              {/* Office Information */}
              <Card className="shadow-xl border-green-200 dark:border-green-800">
                <CardHeader>
                  <CardTitle className="text-xl text-green-800 dark:text-green-200">Office Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {fullAddress && (
                    <div className="flex items-start gap-3">
                      <MapPin className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-green-800 dark:text-green-200">Address</p>
                        <p className="text-green-600 dark:text-green-400 text-sm">
                          {fullAddress}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-800 dark:text-green-200">Phone</p>
                      <a 
                        href={`tel:${contactPhone}`}
                        className="text-green-600 dark:text-green-400 text-sm hover:text-green-700 transition-colors"
                      >
                        {contactPhone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-800 dark:text-green-200">Email</p>
                      <a 
                        href={`mailto:${contactEmail}`}
                        className="text-green-600 dark:text-green-400 text-sm hover:text-green-700 transition-colors"
                      >
                        {contactEmail}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-800 dark:text-green-200">Office Hours</p>
                      <p className="text-green-600 dark:text-green-400 text-sm">
                        Monday - Friday: 9:00 AM - 5:00 PM<br />
                        Saturday: 10:00 AM - 2:00 PM<br />
                        Sunday: Closed
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Contact Options */}
              <Card className="shadow-xl border-green-200 dark:border-green-800">
                <CardHeader>
                  <CardTitle className="text-xl text-green-800 dark:text-green-200">Quick Contact</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Call Us
                    </Button>
                    <Button
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Email Us
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Social Media */}
              <Card className="shadow-xl border-green-200 dark:border-green-800">
                <CardHeader>
                  <CardTitle className="text-xl text-green-800 dark:text-green-200">Follow Us</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-3">
                    <Button
                      size="icon"
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Facebook className="h-4 w-4 text-blue-600" />
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Twitter className="h-4 w-4 text-blue-400" />
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Instagram className="h-4 w-4 text-pink-600" />
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      className="border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20"
                    >
                      <Linkedin className="h-4 w-4 text-blue-700" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Emergency Contact */}
              <Card className="shadow-xl border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-950/20">
                <CardHeader>
                  <CardTitle className="text-xl text-amber-800 dark:text-amber-200 flex items-center gap-2">
                    <Heart className="h-5 w-5" />
                    Emergency Support
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-amber-700 dark:text-amber-300 text-sm mb-3">
                    For urgent educational support or emergency assistance:
                  </p>
                  <Badge
                    variant="secondary"
                    className="bg-amber-200 text-amber-800 dark:bg-amber-800 dark:text-amber-200"
                  >
                    24/7 Hotline: +234 800 KOFA (5632)
                  </Badge>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
