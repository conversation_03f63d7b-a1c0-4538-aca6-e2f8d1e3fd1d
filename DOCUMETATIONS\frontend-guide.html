<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLTKKQ Foundation - Frontend Development Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #3b82f6;
            --primary-cyan: #06b6d4;
            --dark-bg: #1e293b;
            --light-bg: #f8fafc;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-cyan) 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .code-block {
            background: #2d3748;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .code-block pre {
            color: #e2e8f0;
            margin: 0;
        }
        
        .component-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .tech-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0.25rem;
        }
        
        .tech-nextjs { background: #000000; color: white; }
        .tech-react { background: #61dafb; color: black; }
        .tech-typescript { background: #3178c6; color: white; }
        .tech-tailwind { background: #06b6d4; color: white; }
        .tech-shadcn { background: #18181b; color: white; }
        
        .toc-sidebar {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .section-divider {
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-cyan));
            margin: 3rem 0;
        }
        
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .folder-structure {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .file-icon { color: #fbbf24; }
        .folder-icon { color: #3b82f6; }
    </style>
</head>
<body data-bs-spy="scroll" data-bs-target="#frontend-nav">
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="complete-documentation.html">
                <i class="fas fa-arrow-left me-2"></i>
                <i class="fas fa-heart text-warning me-2"></i>
                HLTKKQ Foundation
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#overview">Overview</a></li>
                    <li class="nav-item"><a class="nav-link" href="#structure">Structure</a></li>
                    <li class="nav-item"><a class="nav-link" href="#components">Components</a></li>
                    <li class="nav-item"><a class="nav-link" href="#state">State Management</a></li>
                    <li class="nav-item"><a class="nav-link" href="#styling">Styling</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <i class="fab fa-react fa-4x text-white mb-4"></i>
                    <h1 class="display-3 fw-bold mb-4">Frontend Development Guide</h1>
                    <p class="lead mb-4">
                        Complete guide for building the Next.js 15 frontend application with TypeScript, Tailwind CSS, and modern React patterns
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="tech-badge tech-nextjs">Next.js 15</span>
                        <span class="tech-badge tech-react">React 19</span>
                        <span class="tech-badge tech-typescript">TypeScript</span>
                        <span class="tech-badge tech-tailwind">Tailwind CSS</span>
                        <span class="tech-badge tech-shadcn">shadcn/ui</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="toc-sidebar p-3">
                    <h6 class="text-muted text-uppercase fw-bold mb-3">Frontend Guide</h6>
                    <nav id="frontend-nav" class="nav nav-pills flex-column">
                        <a class="nav-link" href="#overview">Overview</a>
                        <a class="nav-link" href="#setup">Getting Started</a>
                        <a class="nav-link" href="#structure">Project Structure</a>
                        <a class="nav-link" href="#components">Components</a>
                        <a class="nav-link" href="#pages">Pages & Routing</a>
                        <a class="nav-link" href="#state">State Management</a>
                        <a class="nav-link" href="#api">API Integration</a>
                        <a class="nav-link" href="#styling">Styling Guide</a>
                        <a class="nav-link" href="#forms">Forms & Validation</a>
                        <a class="nav-link" href="#auth">Authentication</a>
                        <a class="nav-link" href="#deployment">Deployment</a>
                    </nav>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                
                <!-- Overview -->
                <section id="overview" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-info-circle text-primary me-3"></i>
                        Frontend Overview
                    </h2>
                    
                    <div class="row g-4 mb-4">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="text-primary mb-3">Next.js 15 Modern Frontend</h4>
                                    <p class="lead">
                                        The HLTKKQ Foundation frontend is built with Next.js 15, featuring the new App Router, React 19, TypeScript, and a modern component-based architecture.
                                    </p>
                                    <ul class="list-unstyled mt-3">
                                        <li><i class="fas fa-check text-success me-2"></i>Server-side rendering (SSR) and static generation (SSG)</li>
                                        <li><i class="fas fa-check text-success me-2"></i>TypeScript for type safety and better development experience</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Tailwind CSS for utility-first styling</li>
                                        <li><i class="fas fa-check text-success me-2"></i>shadcn/ui for consistent, accessible components</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Responsive design with mobile-first approach</li>
                                        <li><i class="fas fa-check text-success me-2"></i>State management with Zustand and React hooks</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Tech Stack</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Framework:</strong></td>
                                            <td>Next.js 15</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Language:</strong></td>
                                            <td>TypeScript 5.x</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Styling:</strong></td>
                                            <td>Tailwind CSS 3.4+</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Components:</strong></td>
                                            <td>shadcn/ui</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Icons:</strong></td>
                                            <td>Lucide React</td>
                                        </tr>
                                        <tr>
                                            <td><strong>State:</strong></td>
                                            <td>Zustand</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Key Features -->
                    <h4 class="mb-4">Frontend Features</h4>
                    <div class="row g-4">
                        <div class="col-md-6 col-lg-3">
                            <div class="card feature-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                                    <h6 class="card-title">Responsive Design</h6>
                                    <p class="card-text text-muted small">Mobile-first approach with Tailwind breakpoints</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="card feature-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-bolt fa-3x text-warning mb-3"></i>
                                    <h6 class="card-title">Performance</h6>
                                    <p class="card-text text-muted small">Optimized with SSR, code splitting, and image optimization</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="card feature-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                                    <h6 class="card-title">Type Safety</h6>
                                    <p class="card-text text-muted small">Full TypeScript coverage with strict mode</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="card feature-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-universal-access fa-3x text-info mb-3"></i>
                                    <h6 class="card-title">Accessibility</h6>
                                    <p class="card-text text-muted small">WCAG compliant with semantic HTML and ARIA</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Getting Started -->
                <section id="setup" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-rocket text-success me-3"></i>
                        Getting Started
                    </h2>

                    <!-- Installation -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-download me-2"></i>Installation & Setup</h5>
                        </div>
                        <div class="card-body">
                            <div class="code-block">
                                <pre><code># Navigate to frontend directory
cd front-end

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local

# Start development server
npm run dev

# Frontend runs on http://localhost:3000</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Environment Variables -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Environment Configuration</h5>
                        </div>
                        <div class="card-body">
                            <p>Configure your <code>.env.local</code> file with the following variables:</p>
                            <div class="code-block">
                                <pre><code># .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_STORAGE_URL=http://localhost:8000/storage</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Available Scripts -->
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-terminal me-2"></i>Available Scripts</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="p-3 bg-light rounded">
                                        <code class="text-success">npm run dev</code>
                                        <p class="small mb-0 mt-1">Start development server with hot reload</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-3 bg-light rounded">
                                        <code class="text-primary">npm run build</code>
                                        <p class="small mb-0 mt-1">Build optimized production bundle</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-3 bg-light rounded">
                                        <code class="text-warning">npm run start</code>
                                        <p class="small mb-0 mt-1">Start production server</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-3 bg-light rounded">
                                        <code class="text-info">npm run lint</code>
                                        <p class="small mb-0 mt-1">Run ESLint code analysis</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Project Structure -->
                <section id="structure" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-sitemap text-warning me-3"></i>
                        Project Structure
                    </h2>

                    <div class="row g-4">
                        <div class="col-lg-6">
                            <h4 class="mb-3">Directory Overview</h4>
                            <div class="folder-structure">
<pre>front-end/
├── <i class="fas fa-folder folder-icon"></i> app/                     # Next.js App Router
│   ├── <i class="fas fa-folder folder-icon"></i> auth/               # Authentication pages
│   │   ├── <i class="fas fa-file file-icon"></i> login/page.tsx
│   │   └── <i class="fas fa-file file-icon"></i> register/page.tsx
│   ├── <i class="fas fa-folder folder-icon"></i> dashboard/          # User dashboards
│   │   ├── <i class="fas fa-file file-icon"></i> admin/page.tsx
│   │   ├── <i class="fas fa-file file-icon"></i> student/page.tsx
│   │   ├── <i class="fas fa-file file-icon"></i> volunteer/page.tsx
│   │   └── <i class="fas fa-file file-icon"></i> partner/page.tsx
│   ├── <i class="fas fa-folder folder-icon"></i> blog/              # Blog pages
│   │   ├── <i class="fas fa-file file-icon"></i> page.tsx
│   │   └── <i class="fas fa-file file-icon"></i> [slug]/page.tsx
│   ├── <i class="fas fa-file file-icon"></i> layout.tsx          # Root layout
│   ├── <i class="fas fa-file file-icon"></i> page.tsx            # Homepage
│   └── <i class="fas fa-file file-icon"></i> globals.css         # Global styles
├── <i class="fas fa-folder folder-icon"></i> components/           # Reusable components
│   ├── <i class="fas fa-folder folder-icon"></i> ui/               # shadcn/ui components
│   ├── <i class="fas fa-folder folder-icon"></i> dashboard/        # Dashboard components
│   ├── <i class="fas fa-file file-icon"></i> navigation.tsx
│   └── <i class="fas fa-file file-icon"></i> footer.tsx
├── <i class="fas fa-folder folder-icon"></i> hooks/                # Custom React hooks
├── <i class="fas fa-folder folder-icon"></i> lib/                  # Utilities and configs
│   ├── <i class="fas fa-file file-icon"></i> api.ts              # API client
│   ├── <i class="fas fa-file file-icon"></i> utils.ts            # Helper functions
│   └── <i class="fas fa-file file-icon"></i> validations.ts      # Form validations
└── <i class="fas fa-folder folder-icon"></i> styles/               # Additional styles</pre>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <h4 class="mb-3">Key Directories</h4>
                            <div class="accordion" id="structureAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#app-dir">
                                            <i class="fas fa-folder text-primary me-2"></i>app/ Directory
                                        </button>
                                    </h2>
                                    <div id="app-dir" class="accordion-collapse collapse show" data-bs-parent="#structureAccordion">
                                        <div class="accordion-body">
                                            <p>Contains all pages using Next.js 15 App Router structure:</p>
                                            <ul class="small">
                                                <li><strong>layout.tsx:</strong> Root layout component</li>
                                                <li><strong>page.tsx:</strong> Page components for each route</li>
                                                <li><strong>loading.tsx:</strong> Loading UI components</li>
                                                <li><strong>error.tsx:</strong> Error boundary components</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#components-dir">
                                            <i class="fas fa-puzzle-piece text-success me-2"></i>components/ Directory
                                        </button>
                                    </h2>
                                    <div id="components-dir" class="accordion-collapse collapse" data-bs-parent="#structureAccordion">
                                        <div class="accordion-body">
                                            <p>Reusable UI components organized by feature:</p>
                                            <ul class="small">
                                                <li><strong>ui/:</strong> shadcn/ui base components</li>
                                                <li><strong>dashboard/:</strong> Dashboard-specific components</li>
                                                <li><strong>forms/:</strong> Form components and inputs</li>
                                                <li><strong>layout/:</strong> Layout components</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#lib-dir">
                                            <i class="fas fa-tools text-warning me-2"></i>lib/ Directory
                                        </button>
                                    </h2>
                                    <div id="lib-dir" class="accordion-collapse collapse" data-bs-parent="#structureAccordion">
                                        <div class="accordion-body">
                                            <p>Utility functions and configurations:</p>
                                            <ul class="small">
                                                <li><strong>api.ts:</strong> API client and request functions</li>
                                                <li><strong>utils.ts:</strong> Common utility functions</li>
                                                <li><strong>validations.ts:</strong> Form validation schemas</li>
                                                <li><strong>auth.ts:</strong> Authentication utilities</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Component Development -->
                <section id="components" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-puzzle-piece text-info me-3"></i>
                        Component Development
                    </h2>

                    <!-- Component Structure -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-code me-2"></i>Component Structure Pattern</h5>
                        </div>
                        <div class="card-body">
                            <p>Follow this pattern for creating new components:</p>
                            <div class="code-block">
                                <pre><code class="language-typescript">// components/example-component.tsx
import { cn } from "@/lib/utils"

interface ExampleComponentProps {
  className?: string
  children?: React.ReactNode
  variant?: 'default' | 'secondary'
  disabled?: boolean
}

export function ExampleComponent({ 
  className, 
  children, 
  variant = 'default',
  disabled = false
}: ExampleComponentProps) {
  return (
    &lt;div 
      className={cn(
        "base-classes",
        variant === 'secondary' && "secondary-classes",
        disabled && "disabled-classes",
        className
      )}
    &gt;
      {children}
    &lt;/div&gt;
  )
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Page Component Pattern -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-file-code me-2"></i>Page Component Pattern</h5>
                        </div>
                        <div class="card-body">
                            <p>Standard pattern for page components with data fetching:</p>
                            <div class="code-block">
                                <pre><code class="language-typescript">// app/example/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { apiClient } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface ExampleData {
  id: number
  title: string
  description: string
}

export default function ExamplePage() {
  const { user, isAuthenticated } = useAuth()
  const [data, setData] = useState&lt;ExampleData[]&gt;([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState&lt;string | null&gt;(null)

  useEffect(() =&gt; {
    const fetchData = async () =&gt; {
      try {
        setLoading(true)
        const response = await apiClient.getData()
        setData(response.data)
      } catch (err) {
        setError('Failed to load data')
        console.error('Fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      &lt;div className="flex items-center justify-center min-h-screen"&gt;
        &lt;div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"&gt;&lt;/div&gt;
      &lt;/div&gt;
    )
  }

  if (error) {
    return (
      &lt;div className="container mx-auto px-4 py-8"&gt;
        &lt;Card className="border-red-200"&gt;
          &lt;CardContent className="p-6 text-center"&gt;
            &lt;p className="text-red-600"&gt;{error}&lt;/p&gt;
            &lt;Button onClick={() =&gt; window.location.reload()} className="mt-4"&gt;
              Try Again
            &lt;/Button&gt;
          &lt;/CardContent&gt;
        &lt;/Card&gt;
      &lt;/div&gt;
    )
  }

  return (
    &lt;div className="container mx-auto px-4 py-8"&gt;
      &lt;div className="mb-8"&gt;
        &lt;h1 className="text-3xl font-bold text-gray-900"&gt;Example Page&lt;/h1&gt;
        &lt;p className="text-gray-600 mt-2"&gt;Welcome, {user?.first_name}!&lt;/p&gt;
      &lt;/div&gt;

      &lt;div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"&gt;
        {data.map((item) =&gt; (
          &lt;Card key={item.id}&gt;
            &lt;CardHeader&gt;
              &lt;CardTitle&gt;{item.title}&lt;/CardTitle&gt;
            &lt;/CardHeader&gt;
            &lt;CardContent&gt;
              &lt;p&gt;{item.description}&lt;/p&gt;
            &lt;/CardContent&gt;
          &lt;/Card&gt;
        ))}
      &lt;/div&gt;
    &lt;/div&gt;
  )
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Using shadcn/ui Components -->
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Using shadcn/ui Components</h5>
                        </div>
                        <div class="card-body">
                            <p>The project uses shadcn/ui for consistent, accessible components:</p>
                            <div class="code-block">
                                <pre><code class="language-typescript">import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

export function ExampleForm() {
  return (
    &lt;Card className="w-full max-w-md"&gt;
      &lt;CardHeader&gt;
        &lt;CardTitle&gt;Example Form&lt;/CardTitle&gt;
      &lt;/CardHeader&gt;
      &lt;CardContent className="space-y-4"&gt;
        &lt;Input placeholder="Enter your name" /&gt;
        &lt;Button className="w-full"&gt;Submit&lt;/Button&gt;
        
        &lt;Dialog&gt;
          &lt;DialogTrigger asChild&gt;
            &lt;Button variant="outline"&gt;Open Dialog&lt;/Button&gt;
          &lt;/DialogTrigger&gt;
          &lt;DialogContent&gt;
            &lt;DialogHeader&gt;
              &lt;DialogTitle&gt;Dialog Title&lt;/DialogTitle&gt;
            &lt;/DialogHeader&gt;
            &lt;p&gt;Dialog content here&lt;/p&gt;
          &lt;/DialogContent&gt;
        &lt;/Dialog&gt;

        &lt;Alert&gt;
          &lt;AlertTitle&gt;Success!&lt;/AlertTitle&gt;
          &lt;AlertDescription&gt;
            Your form has been submitted successfully.
          &lt;/AlertDescription&gt;
        &lt;/Alert&gt;
      &lt;/CardContent&gt;
    &lt;/Card&gt;
  )
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Continue with more sections... -->

            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html> 