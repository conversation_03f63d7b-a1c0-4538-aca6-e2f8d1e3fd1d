<?php

namespace App\Console\Commands;

use App\Services\ProgressionNotificationService;
use App\Models\PartnerOrganization;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendProgressionNotifications extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'progression:notify 
                            {--type=eligibility : Type of notification (eligibility, summary)}
                            {--organization= : Specific organization ID to process}
                            {--send-emails : Actually send email notifications}';

    /**
     * The console command description.
     */
    protected $description = 'Send progression notifications to partner organizations';

    protected $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(ProgressionNotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $organizationId = $this->option('organization');
        $sendEmails = $this->option('send-emails');

        $this->info("Starting progression notifications...");
        $this->info("Type: {$type}");
        
        if ($organizationId) {
            $this->info("Organization ID: {$organizationId}");
        }
        
        if (!$sendEmails) {
            $this->warn("Running in dry-run mode. Use --send-emails to actually send notifications.");
        }

        try {
            switch ($type) {
                case 'eligibility':
                    return $this->sendEligibilityNotifications($organizationId, $sendEmails);
                
                case 'summary':
                    return $this->sendSummaryNotifications($sendEmails);
                
                default:
                    $this->error("Invalid notification type. Use 'eligibility' or 'summary'.");
                    return 1;
            }

        } catch (\Exception $e) {
            $this->error("Failed to send notifications: " . $e->getMessage());
            Log::error("Progression notification command failed", [
                'error' => $e->getMessage(),
                'type' => $type,
                'organization_id' => $organizationId
            ]);
            return 1;
        }
    }

    /**
     * Send eligibility notifications
     */
    private function sendEligibilityNotifications(?string $organizationId, bool $sendEmails): int
    {
        $this->info("Sending progression eligibility notifications...");

        $partnerOrg = null;
        if ($organizationId) {
            $partnerOrg = PartnerOrganization::find($organizationId);
            if (!$partnerOrg) {
                $this->error("Partner organization not found with ID: {$organizationId}");
                return 1;
            }
            $this->info("Processing organization: {$partnerOrg->name}");
        } else {
            $this->info("Processing all partner organizations...");
        }

        if ($sendEmails) {
            $results = $this->notificationService->sendProgressionEligibilityNotifications($partnerOrg);
        } else {
            // Dry run - just show what would be processed
            $results = $this->dryRunEligibilityNotifications($partnerOrg);
        }

        $this->displayResults($results, 'Eligibility Notifications');

        return 0;
    }

    /**
     * Send summary notifications
     */
    private function sendSummaryNotifications(bool $sendEmails): int
    {
        $this->info("Sending daily progression summaries...");

        if ($sendEmails) {
            $results = $this->notificationService->sendDailyProgressionSummary();
        } else {
            // Dry run - just show what would be processed
            $results = $this->dryRunSummaryNotifications();
        }

        $this->displayResults($results, 'Daily Summaries');

        return 0;
    }

    /**
     * Dry run for eligibility notifications
     */
    private function dryRunEligibilityNotifications(?PartnerOrganization $partnerOrg): array
    {
        $results = [
            'notifications_sent' => 0,
            'organizations_notified' => 0,
            'eligible_students' => 0,
            'errors' => [],
        ];

        if ($partnerOrg) {
            $organizations = collect([$partnerOrg]);
        } else {
            $organizations = PartnerOrganization::with('user')->get();
        }

        $studentProgressionService = app(\App\Services\StudentProgressionService::class);

        foreach ($organizations as $organization) {
            try {
                $eligibleStudents = $studentProgressionService->getEligibleStudents($organization);
                
                if ($eligibleStudents->isEmpty()) {
                    continue;
                }

                $results['eligible_students'] += $eligibleStudents->count();
                $results['organizations_notified']++;
                $results['notifications_sent'] += $eligibleStudents->count();

                $this->line("  • {$organization->name}: {$eligibleStudents->count()} eligible students");

                // Show summary if multiple students
                if ($eligibleStudents->count() > 1) {
                    $results['notifications_sent']++;
                    $this->line("    + 1 summary notification");
                }

            } catch (\Exception $e) {
                $results['errors'][] = [
                    'organization_id' => $organization->id,
                    'organization_name' => $organization->name,
                    'error' => $e->getMessage(),
                ];
                
                $this->error("  • {$organization->name}: Error - {$e->getMessage()}");
            }
        }

        return $results;
    }

    /**
     * Dry run for summary notifications
     */
    private function dryRunSummaryNotifications(): array
    {
        $results = [
            'summaries_sent' => 0,
            'organizations_processed' => 0,
            'errors' => [],
        ];

        $organizations = PartnerOrganization::with('user')->get();

        foreach ($organizations as $organization) {
            try {
                // Simulate summary generation
                $totalStudents = \App\Models\Student::where('school_id', $organization->id)->count();
                $hasActivity = $totalStudents > 0; // Simplified check for dry run

                if ($hasActivity) {
                    $results['summaries_sent']++;
                    $this->line("  • {$organization->name}: Summary would be sent ({$totalStudents} students)");
                } else {
                    $this->line("  • {$organization->name}: No activity, no summary needed");
                }

                $results['organizations_processed']++;

            } catch (\Exception $e) {
                $results['errors'][] = [
                    'organization_id' => $organization->id,
                    'organization_name' => $organization->name,
                    'error' => $e->getMessage(),
                ];
                
                $this->error("  • {$organization->name}: Error - {$e->getMessage()}");
            }
        }

        return $results;
    }

    /**
     * Display command results
     */
    private function displayResults(array $results, string $type): void
    {
        $this->info("\n{$type} Results:");
        $this->info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        if (isset($results['notifications_sent'])) {
            $this->info("Notifications sent: {$results['notifications_sent']}");
        }
        
        if (isset($results['summaries_sent'])) {
            $this->info("Summaries sent: {$results['summaries_sent']}");
        }
        
        if (isset($results['organizations_notified'])) {
            $this->info("Organizations notified: {$results['organizations_notified']}");
        }
        
        if (isset($results['organizations_processed'])) {
            $this->info("Organizations processed: {$results['organizations_processed']}");
        }
        
        if (isset($results['eligible_students'])) {
            $this->info("Eligible students: {$results['eligible_students']}");
        }

        if (!empty($results['errors'])) {
            $this->warn("\nErrors encountered:");
            foreach ($results['errors'] as $error) {
                $this->error("• {$error['organization_name']}: {$error['error']}");
            }
        }

        $this->info("\nCompleted successfully!");
    }
}
