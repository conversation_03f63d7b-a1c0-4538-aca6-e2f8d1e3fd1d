<?php

echo "=== Debugging Scholarship Data Structure ===\n\n";

// Test individual scholarship to see exact data structure
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/api/v1/public-scholarships/5');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        $scholarship = $data['data'];
        
        echo "Scholarship Data Structure:\n";
        echo "========================\n";
        
        foreach ($scholarship as $key => $value) {
            echo "$key: ";
            if (is_array($value)) {
                echo "Array with " . count($value) . " items\n";
                if ($key === 'documents_required' || $key === 'custom_fields') {
                    echo "  Content: " . json_encode($value, JSON_PRETTY_PRINT) . "\n";
                }
            } elseif (is_object($value)) {
                echo "Object\n";
            } elseif (is_null($value)) {
                echo "NULL\n";
            } else {
                echo gettype($value) . " - " . (strlen($value) > 100 ? substr($value, 0, 100) . "..." : $value) . "\n";
            }
        }
        
        echo "\n=== Specific Field Analysis ===\n";
        echo "documents_required type: " . gettype($scholarship['documents_required'] ?? 'NOT_SET') . "\n";
        echo "documents_required value: " . json_encode($scholarship['documents_required'] ?? 'NOT_SET') . "\n";
        
        echo "\napplication_instructions type: " . gettype($scholarship['application_instructions'] ?? 'NOT_SET') . "\n";
        echo "application_instructions value: " . json_encode($scholarship['application_instructions'] ?? 'NOT_SET') . "\n";
        
        echo "\neligibility_criteria type: " . gettype($scholarship['eligibility_criteria'] ?? 'NOT_SET') . "\n";
        echo "eligibility_criteria value: " . json_encode($scholarship['eligibility_criteria'] ?? 'NOT_SET') . "\n";
        
        echo "\nrequirements type: " . gettype($scholarship['requirements'] ?? 'NOT_SET') . "\n";
        echo "requirements value: " . json_encode($scholarship['requirements'] ?? 'NOT_SET') . "\n";
        
    } else {
        echo "API Error: " . ($data['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "HTTP Error: $httpCode\n";
}
