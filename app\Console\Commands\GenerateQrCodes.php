<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class GenerateQrCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qr:generate {--force : Force regenerate all QR codes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate QR codes for users who don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        
        if ($force) {
            $users = User::all();
            $this->info('Force regenerating QR codes for all users...');
        } else {
            $users = User::whereNull('qr_code_url')->orWhere('qr_code_url', '')->get();
            $this->info('Generating QR codes for users without them...');
        }

        $count = 0;
        $errors = 0;

        foreach ($users as $user) {
            try {
                $this->line("Processing user: {$user->first_name} {$user->last_name} (ID: {$user->short_id})");
                
                $qrUrl = $user->generateQrCodeUrl();
                $user->qr_code_url = $qrUrl;
                $user->save();
                
                $count++;
                $this->info("✓ Generated QR code for {$user->first_name} {$user->last_name}");
            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Failed to generate QR code for {$user->first_name} {$user->last_name}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("QR Code generation completed!");
        $this->info("✓ Successfully generated: {$count}");
        $this->error("✗ Errors: {$errors}");
        
        return Command::SUCCESS;
    }
}
