'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Building2, 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  User,
  Globe,
  MapPin
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface PartnerRegistrationProps {
  onRegistrationComplete: () => void;
}

export default function PartnerRegistration({ onRegistrationComplete }: PartnerRegistrationProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const [formData, setFormData] = useState({
    organization_name: '',
    organization_type: '',
    registration_number: '',
    contact_person: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    city: '',
    state: '',
    country: '',
    website: '',
    description: '',
    areas_of_focus: [] as string[],
    partnership_interests: [] as string[],
    resources_to_share: [] as string[],
    expected_collaboration: ''
  });

  const [files, setFiles] = useState<File[]>([]);

  const organizationTypes = [
    { value: 'school', label: 'Educational Institution' },
    { value: 'ngo', label: 'Non-Governmental Organization' },
    { value: 'government', label: 'Government Agency' },
    { value: 'corporate', label: 'Corporate Organization' },
    { value: 'international', label: 'International Organization' }
  ];

  const focusAreas = [
    'Education', 'Health', 'Environment', 'Technology', 'Community Development',
    'Youth Empowerment', 'Women Empowerment', 'Poverty Alleviation', 'Research',
    'Capacity Building', 'Advocacy', 'Emergency Response'
  ];

  const partnershipTypes = [
    'Joint Programs', 'Resource Sharing', 'Event Co-hosting', 'Research Collaboration',
    'Funding Partnership', 'Training & Capacity Building', 'Technical Assistance',
    'Advocacy Campaigns', 'Knowledge Exchange'
  ];

  const resourceTypes = [
    'Financial Resources', 'Human Resources', 'Technical Expertise', 'Infrastructure',
    'Equipment', 'Training Materials', 'Research Data', 'Networks & Connections',
    'Marketing Support', 'Legal Support'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayChange = (field: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...(prev[field as keyof typeof prev] as string[]), value]
        : (prev[field as keyof typeof prev] as string[]).filter(item => item !== value)
    }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFiles(Array.from(event.target.files));
    }
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.organization_name && formData.organization_type && 
                 formData.contact_person && formData.contact_email);
      case 2:
        return !!(formData.address && formData.city && formData.state && formData.country);
      case 3:
        return !!(formData.description && formData.areas_of_focus.length > 0 && 
                 formData.partnership_interests.length > 0);
      case 4:
        return !!formData.expected_collaboration;
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5));
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) {
      toast.error('Please complete all required fields');
      return;
    }

    try {
      setLoading(true);
      setUploadProgress(0);

      const submitData = new FormData();
      
      // Add form data
      Object.entries(formData).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          submitData.append(key, JSON.stringify(value));
        } else {
          submitData.append(key, value);
        }
      });

      // Add files
      files.forEach((file, index) => {
        submitData.append(`verification_documents[${index}]`, file);
      });

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await apiClient.post('/partners/register', submitData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.success) {
        toast.success('Partner registration submitted successfully!');
        setCurrentStep(5);
        setTimeout(() => {
          onRegistrationComplete();
        }, 2000);
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration failed:', error);
      toast.error(error.response?.data?.message || 'Failed to submit registration');
      setUploadProgress(0);
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Building2 className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-semibold">Organization Information</h3>
              <p className="text-gray-600">Tell us about your organization</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="org_name">Organization Name *</Label>
                <Input
                  id="org_name"
                  value={formData.organization_name}
                  onChange={(e) => handleInputChange('organization_name', e.target.value)}
                  placeholder="Enter organization name"
                />
              </div>
              <div>
                <Label htmlFor="org_type">Organization Type *</Label>
                <Select value={formData.organization_type} onValueChange={(value) => handleInputChange('organization_type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {organizationTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="reg_number">Registration Number</Label>
                <Input
                  id="reg_number"
                  value={formData.registration_number}
                  onChange={(e) => handleInputChange('registration_number', e.target.value)}
                  placeholder="Official registration number"
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contact_person">Contact Person *</Label>
                <Input
                  id="contact_person"
                  value={formData.contact_person}
                  onChange={(e) => handleInputChange('contact_person', e.target.value)}
                  placeholder="Primary contact name"
                />
              </div>
              <div>
                <Label htmlFor="contact_email">Contact Email *</Label>
                <Input
                  id="contact_email"
                  type="email"
                  value={formData.contact_email}
                  onChange={(e) => handleInputChange('contact_email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="contact_phone">Contact Phone *</Label>
                <Input
                  id="contact_phone"
                  type="tel"
                  value={formData.contact_phone}
                  onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                  placeholder="+234 xxx xxx xxxx"
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <MapPin className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-semibold">Location Information</h3>
              <p className="text-gray-600">Where is your organization located?</p>
            </div>

            <div>
              <Label htmlFor="address">Address *</Label>
              <Textarea
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Full address of your organization"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="City"
                />
              </div>
              <div>
                <Label htmlFor="state">State *</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  placeholder="State/Province"
                />
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  placeholder="Country"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <User className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-semibold">Organization Details</h3>
              <p className="text-gray-600">Tell us about your work and interests</p>
            </div>

            <div>
              <Label htmlFor="description">Organization Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your organization's mission, vision, and activities"
                rows={4}
              />
            </div>

            <div>
              <Label>Areas of Focus * (Select all that apply)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {focusAreas.map(area => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={`focus_${area}`}
                      checked={formData.areas_of_focus.includes(area)}
                      onCheckedChange={(checked) => handleArrayChange('areas_of_focus', area, checked as boolean)}
                    />
                    <Label htmlFor={`focus_${area}`} className="text-sm">{area}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>Partnership Interests * (Select all that apply)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {partnershipTypes.map(type => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`partnership_${type}`}
                      checked={formData.partnership_interests.includes(type)}
                      onCheckedChange={(checked) => handleArrayChange('partnership_interests', type, checked as boolean)}
                    />
                    <Label htmlFor={`partnership_${type}`} className="text-sm">{type}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>Resources to Share (Optional)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {resourceTypes.map(resource => (
                  <div key={resource} className="flex items-center space-x-2">
                    <Checkbox
                      id={`resource_${resource}`}
                      checked={formData.resources_to_share.includes(resource)}
                      onCheckedChange={(checked) => handleArrayChange('resources_to_share', resource, checked as boolean)}
                    />
                    <Label htmlFor={`resource_${resource}`} className="text-sm">{resource}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <FileText className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-semibold">Collaboration & Documents</h3>
              <p className="text-gray-600">Final details and verification documents</p>
            </div>

            <div>
              <Label htmlFor="expected_collaboration">Expected Collaboration *</Label>
              <Textarea
                id="expected_collaboration"
                value={formData.expected_collaboration}
                onChange={(e) => handleInputChange('expected_collaboration', e.target.value)}
                placeholder="Describe what you hope to achieve through this partnership"
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="documents">Verification Documents (Optional)</Label>
              <Input
                id="documents"
                type="file"
                multiple
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={handleFileChange}
                className="mt-2"
              />
              <p className="text-sm text-gray-500 mt-1">
                Upload registration certificates, licenses, or other verification documents
              </p>
              {files.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium">Selected files:</p>
                  <ul className="text-sm text-gray-600">
                    {files.map((file, index) => (
                      <li key={index}>• {file.name}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="text-center space-y-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <div>
              <h3 className="text-xl font-semibold text-green-800">Registration Submitted!</h3>
              <p className="text-gray-600 mt-2">
                Your partner registration has been submitted successfully. 
                We'll review your application and get back to you within 3-5 business days.
              </p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900">What's Next?</h4>
              <ul className="text-sm text-blue-800 mt-2 space-y-1">
                <li>• Our team will review your application</li>
                <li>• We may contact you for additional information</li>
                <li>• You'll receive an email notification about the decision</li>
                <li>• Once approved, you can access all partner features</li>
              </ul>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Partner Registration</CardTitle>
        <CardDescription>
          Join our network of partners and start collaborating with us
        </CardDescription>
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>Step {currentStep} of 5</span>
            <span>{Math.round((currentStep / 5) * 100)}% Complete</span>
          </div>
          <Progress value={(currentStep / 5) * 100} />
        </div>
      </CardHeader>
      <CardContent>
        {renderStep()}

        {loading && uploadProgress > 0 && (
          <div className="mt-6">
            <div className="flex justify-between text-sm mb-2">
              <span>Submitting registration...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} />
          </div>
        )}

        {currentStep < 5 && (
          <div className="flex justify-between mt-8">
            <Button 
              variant="outline" 
              onClick={prevStep}
              disabled={currentStep === 1 || loading}
            >
              Previous
            </Button>
            {currentStep === 4 ? (
              <Button 
                onClick={handleSubmit}
                disabled={loading || !validateStep(currentStep)}
              >
                {loading ? 'Submitting...' : 'Submit Registration'}
              </Button>
            ) : (
              <Button 
                onClick={nextStep}
                disabled={!validateStep(currentStep)}
              >
                Next
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
