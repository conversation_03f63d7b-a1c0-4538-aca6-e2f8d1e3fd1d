# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# For Hybrid Deployment Architecture (Next.js + Laravel)
# =============================================================================

APP_NAME="HALIMAKQ Foundation"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://api.mydomain.com

# Frontend URL (Next.js application)
FRONTEND_URL=https://www.mydomain.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=halimakq_foundation
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# Cache Configuration
BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# PayStack Configuration (Production)
PAYSTACK_PUBLIC_KEY=pk_live_your_public_key_here
PAYSTACK_SECRET_KEY=sk_live_your_secret_key_here
PAYSTACK_CALLBACK_URL=https://www.mydomain.com/donation/success

# File Upload Configuration
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# CORS Configuration for Next.js Frontend
SANCTUM_STATEFUL_DOMAINS=www.mydomain.com,mydomain.com
SESSION_DOMAIN=.mydomain.com

# API Rate Limiting
API_RATE_LIMIT=60

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Security
BCRYPT_ROUNDS=12
