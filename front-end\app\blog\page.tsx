"use client"

import Link from "next/link"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Calendar, User, Clock, ArrowRight, Filter, Tag, Loader2 } from "lucide-react"
import { useState, useEffect } from "react"

// Type definitions
interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  featured_image_url?: string
  reading_time?: string
  author: {
    id: number
    name: string
    avatar?: string
  }
  category?: {
    id: number
    name: string
    slug: string
    color?: string
  }
  tags: string[]
  published_at: string
  comments_count?: number
}

interface BlogCategory {
  id: number
  name: string
  slug: string
  posts_count: number
}

interface BlogResponse {
  posts: {
    data: BlogPost[]
    current_page: number
    per_page: number
    total: number
    last_page: number
  }
  success: boolean
}

interface CategoriesResponse {
  success: boolean
  data: BlogCategory[]
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "";

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalPosts, setTotalPosts] = useState(0)

  // Fetch blog posts
  const fetchPosts = async (page = 1, search = "", category = "") => {
    try {
      setLoading(true)
      setError(null)
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '9',
        ...(search && { search }),
        ...(category && category !== 'All' && { category })
      })

      const response = await fetch(`${API_BASE_URL}/blog/posts?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const text = await response.text()
      let data: BlogResponse
      
      try {
        data = JSON.parse(text)
      } catch (parseError) {
        console.error('Response was not valid JSON:', text.substring(0, 200))
        throw new Error('Server returned invalid response')
      }

      if (data.success) {
        setPosts(data.posts.data || [])
        setCurrentPage(data.posts.current_page || 1)
        setTotalPages(data.posts.last_page || 1)
        setTotalPosts(data.posts.total || 0)
      } else {
        throw new Error('API request failed')
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      setError('Failed to load blog posts. Please check if the server is running.')
      setPosts([])
    } finally {
      setLoading(false)
    }
  }

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/blog/categories`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const text = await response.text()
      let data: CategoriesResponse
      
      try {
        data = JSON.parse(text)
      } catch (parseError) {
        console.error('Categories response was not valid JSON:', text.substring(0, 200))
        throw new Error('Server returned invalid response')
      }

      if (data.success) {
        setCategories(data.data || [])
      } else {
        throw new Error('Failed to fetch categories')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchPosts()
    fetchCategories()
  }, [])

  // Handle search and filter changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchPosts(1, searchTerm, selectedCategory)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm, selectedCategory])

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Get reading time or default
  const getReadingTime = (post: BlogPost) => {
    return post.reading_time || '5 min read'
  }

  // Get fallback image
  const getPostImage = (post: BlogPost) => {
    if (post.featured_image_url) {
      return post.featured_image_url;
    }
    
    // Return unique placeholder based on post ID
    const placeholders = [
      'https://images.unsplash.com/photo-1529070538774-1843cb3265df', // Impact/community
      'https://images.unsplash.com/photo-1593113598332-cd288d649433', // Education
      'https://images.unsplash.com/photo-**********-cd4628902d4a', // Healthcare
      'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40', // Environment
      'https://images.unsplash.com/photo-1517048676732-d65bc937f952', // Community work
      'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c', // Volunteering
      'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca', // Charity
      'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b', // Nature/environment
      'https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a', // Help/support
      'https://images.unsplash.com/photo-1484712401471-05c7215830eb' // Community building
    ];

    // Use post ID to get consistent but different images
    const index = post.id % placeholders.length;
    return placeholders[index] + '?w=800&h=500&fit=crop&crop=center';
  }

  const allCategories = ["All", ...(categories || []).map(cat => cat.name)]
  const featuredPost = posts && posts.length > 0 ? posts[0] : null // First post as featured
  const regularPosts = posts && posts.length > 1 ? posts.slice(1) : [] // Rest of the posts

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container relative px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Stories of <span className="text-amber-300">Impact</span>
            </h1>
            <p className="text-xl md:text-2xl text-green-100 mb-8 leading-relaxed">
              Discover the transformative stories behind our work. Read about communities we serve, 
              challenges we overcome, and victories we celebrate together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full">
                Latest Stories
              </Button>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white px-8 py-3 rounded-full font-semibold">
                  Become a Volunteer
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <main className="flex-1 bg-gray-50 dark:bg-gray-900">
        {/* Search and Filter Section */}
        <section className="py-12 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="max-w-6xl mx-auto">
              <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search articles, authors, topics..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 rounded-full border-2 border-gray-200 focus:border-green-500 text-lg"
                  />
                </div>

                {/* Category Filters */}
                <div className="flex flex-wrap gap-2">
                  {allCategories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className={`transition-all duration-200 rounded-full px-4 py-2 ${
                        selectedCategory === category
                          ? "bg-green-600 hover:bg-green-700 text-white"
                          : "border-gray-300 hover:bg-green-50 hover:border-green-300 text-gray-600"
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Results count */}
              <div className="mt-4 text-gray-600 dark:text-gray-400">
                {loading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Loading articles...
                  </div>
                ) : (
                  `Showing ${posts.length} of ${totalPosts} articles`
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Loading State */}
        {loading && (
          <section className="py-16">
            <div className="container px-4 sm:px-6 lg:px-8">
              <div className="max-w-6xl mx-auto">
                <div className="flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-green-600 mr-2" />
                  <span className="text-lg text-gray-600">Loading articles...</span>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Error State */}
        {error && !loading && (
          <section className="py-16">
            <div className="container px-4 sm:px-6 lg:px-8">
              <div className="max-w-6xl mx-auto">
                <div className="text-center bg-red-50 border border-red-200 rounded-lg p-8">
                  <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg className="h-12 w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-semibold text-red-900 mb-2">Unable to Load Blog Posts</h3>
                  <p className="text-red-700 mb-6">
                    {error}
                  </p>
                  <Button 
                    onClick={() => {
                      setError(null)
                      fetchPosts()
                      fetchCategories()
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white rounded-full px-6 py-2"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Featured Article */}
        {!loading && !error && featuredPost && selectedCategory === "All" && !searchTerm && (
          <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-900">
            <div className="container px-4 sm:px-6 lg:px-8">
              <div className="max-w-6xl mx-auto">
                <div className="mb-8">
                  <Badge className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-4 py-2 rounded-full text-sm">
                    ✨ Featured Story
                  </Badge>
                </div>
                
                <Card className="overflow-hidden shadow-2xl rounded-3xl bg-white dark:bg-gray-800 border-0">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                    <div className="relative h-80 lg:h-full">
                      <Image
                        src={getPostImage(featuredPost)}
                        alt={featuredPost.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 50vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent lg:bg-gradient-to-r"></div>
                    </div>
                    
                    <CardContent className="p-8 lg:p-12 flex flex-col justify-center">
                      <div className="flex items-center gap-4 mb-4">
                        {featuredPost.category && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800 rounded-full px-3 py-1">
                            {featuredPost.category.name}
                          </Badge>
                        )}
                        <div className="flex items-center text-gray-500 text-sm">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(featuredPost.published_at)}
                        </div>
                      </div>
                      
                      <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
                        {featuredPost.title}
                      </h2>
                      
                      <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                        {featuredPost.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            {featuredPost.author.avatar ? (
                              <Image
                                src={featuredPost.author.avatar}
                                alt={featuredPost.author.name}
                                width={40}
                                height={40}
                                className="rounded-full"
                              />
                            ) : (
                              <User className="h-5 w-5 text-green-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{featuredPost.author.name}</p>
                            <div className="flex items-center text-gray-500 text-sm">
                              <Clock className="h-3 w-3 mr-1" />
                              {getReadingTime(featuredPost)}
                            </div>
                          </div>
                        </div>
                        
                        <Link href={`/blog/${featuredPost.slug}`}>
                          <Button className="bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group">
                            Read Story
                            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              </div>
            </div>
          </section>
        )}

        {/* Articles Grid */}
        {!loading && !error && (
          <section className="py-16">
            <div className="container px-4 sm:px-6 lg:px-8">
              <div className="max-w-6xl mx-auto">
                {posts.length > 0 ? (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {(selectedCategory === "All" && !searchTerm ? regularPosts : posts).map((post) => (
                        <Card key={post.slug} className="group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 rounded-2xl bg-white dark:bg-gray-800 border-0 hover:-translate-y-2">
                          <Link href={`/blog/${post.slug}`}>
                            <div className="relative h-48 overflow-hidden">
                              <Image
                                src={getPostImage(post)}
                                alt={post.title}
                                fill
                                className="object-cover group-hover:scale-110 transition-transform duration-300"
                                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                              />
                              {post.category && (
                                <div className="absolute top-4 left-4">
                                  <Badge className="bg-white/90 text-gray-800 rounded-full px-3 py-1 text-xs font-medium">
                                    {post.category.name}
                                  </Badge>
                                </div>
                              )}
                            </div>
                            
                            <CardContent className="p-6">
                              <div className="flex items-center gap-2 text-gray-500 text-sm mb-3">
                                <Calendar className="h-4 w-4" />
                                <span>{formatDate(post.published_at)}</span>
                                <span>•</span>
                                <Clock className="h-4 w-4" />
                                <span>{getReadingTime(post)}</span>
                              </div>
                              
                              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-green-600 transition-colors line-clamp-2">
                                {post.title}
                              </h3>
                              
                              <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
                                {post.excerpt}
                              </p>
                              
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    {post.author.avatar ? (
                                      <Image
                                        src={post.author.avatar}
                                        alt={post.author.name}
                                        width={32}
                                        height={32}
                                        className="rounded-full"
                                      />
                                    ) : (
                                      <User className="h-4 w-4 text-green-600" />
                                    )}
                                  </div>
                                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{post.author.name}</span>
                                </div>
                                
                                <div className="flex items-center text-green-600 font-medium group-hover:text-green-700 transition-colors">
                                  <span className="text-sm">Read more</span>
                                  <ArrowRight className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                                </div>
                              </div>
                              
                              {post.tags && post.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-4">
                                  {post.tags.slice(0, 3).map((tag) => (
                                    <Badge key={tag} variant="outline" className="text-xs px-2 py-1 rounded-full border-gray-300 text-gray-600">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </CardContent>
                          </Link>
                        </Card>
                      ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="flex justify-center mt-12">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => fetchPosts(currentPage - 1, searchTerm, selectedCategory)}
                            disabled={currentPage === 1}
                            className="rounded-full"
                          >
                            Previous
                          </Button>
                          
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            const page = i + 1
                            return (
                              <Button
                                key={page}
                                variant={currentPage === page ? "default" : "outline"}
                                onClick={() => fetchPosts(page, searchTerm, selectedCategory)}
                                className="rounded-full w-10 h-10 p-0"
                              >
                                {page}
                              </Button>
                            )
                          })}
                          
                          <Button
                            variant="outline"
                            onClick={() => fetchPosts(currentPage + 1, searchTerm, selectedCategory)}
                            disabled={currentPage === totalPages}
                            className="rounded-full"
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Search className="h-12 w-12 text-gray-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">No articles found</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Try adjusting your search terms or category filters
                    </p>
                    <Button 
                      onClick={() => {
                        setSearchTerm("")
                        setSelectedCategory("All")
                      }}
                      variant="outline"
                      className="rounded-full px-6 py-2"
                    >
                      Clear Filters
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </section>
        )}

        {/* Newsletter Section */}
        <section className="py-16 bg-green-600 text-white">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Stay Updated with Our Stories</h2>
              <p className="text-xl text-green-100 mb-8">
                Get the latest impact stories, news, and insights delivered directly to your inbox
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <Input
                  placeholder="Enter your email"
                  className="bg-white text-gray-900 border-0 rounded-full px-6 py-3"
                />
                <Button className="bg-amber-500 hover:bg-amber-600 text-black font-semibold rounded-full px-8 py-3">
                  Subscribe
                </Button>
              </div>
              <p className="text-sm text-green-200 mt-4">
                Join 5,000+ supporters staying informed about our impact
              </p>
            </div>
          </div>
        </section>
      </main>
    </>
  )
}
