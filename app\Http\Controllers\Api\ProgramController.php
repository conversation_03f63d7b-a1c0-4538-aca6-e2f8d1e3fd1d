<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Programs",
 *     description="Program management endpoints"
 * )
 */
class ProgramController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/programs",
     *     summary="Get list of active programs",
     *     tags={"Programs"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by program status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"planning","active","completed","suspended"})
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Programs retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');
        $search = $request->get('search');

        $query = Program::with(['creator:id,first_name,last_name']);

        // For public access, only show active and completed programs
        if ($status && in_array($status, ['active', 'completed'])) {
            $query->withStatus($status);
        } else {
            $query->whereIn('status', ['active', 'completed']);
        }

        if ($search) {
            $query->search($search);
        }

        $programs = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $programs
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/programs/{slug}",
     *     summary="Get program details",
     *     tags={"Programs"},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         description="Program slug",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Program details retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Program not found"
     *     )
     * )
     */
    public function show($slug): JsonResponse
    {
        $program = Program::whereIn('status', ['active', 'completed'])
            ->with(['creator:id,first_name,last_name,email'])
            ->where('slug', $slug)
            ->firstOrFail();

        $data = [
            'program' => $program,
            'is_active' => $program->isActive(),
            'is_completed' => $program->isCompleted(),
            'duration_days' => $this->calculateDurationDays($program),
            'progress_percentage' => $this->calculateProgressPercentage($program),
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get featured programs (for homepage)
     * 
     * @OA\Get(
     *     path="/api/v1/programs/featured",
     *     summary="Get featured programs",
     *     tags={"Programs"},
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Number of programs to return",
     *         required=false,
     *         @OA\Schema(type="integer", example=6)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Featured programs retrieved successfully"
     *     )
     * )
     */
    public function featured(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 6);

        $programs = Program::active()
            ->with(['creator:id,first_name,last_name'])
            ->whereNotNull('featured_image')
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $programs
        ]);
    }

    /**
     * Get program statistics
     * 
     * @OA\Get(
     *     path="/api/v1/programs/stats",
     *     summary="Get program statistics",
     *     tags={"Programs"},
     *     @OA\Response(
     *         response=200,
     *         description="Program statistics retrieved successfully"
     *     )
     * )
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_programs' => Program::whereIn('status', ['active', 'completed'])->count(),
            'active_programs' => Program::active()->count(),
            'completed_programs' => Program::completed()->count(),
            'total_budget' => Program::whereIn('status', ['active', 'completed'])->sum('budget'),
            'people_impacted' => $this->calculateTotalPeopleImpacted(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Calculate program duration in days
     */
    private function calculateDurationDays(Program $program): ?int
    {
        if (!$program->start_date || !$program->end_date) {
            return null;
        }

        return $program->start_date->diffInDays($program->end_date);
    }

    /**
     * Calculate program progress percentage
     */
    private function calculateProgressPercentage(Program $program): float
    {
        if (!$program->start_date || !$program->end_date) {
            return 0;
        }

        $today = now();
        
        if ($today < $program->start_date) {
            return 0;
        }
        
        if ($today > $program->end_date) {
            return 100;
        }

        $totalDays = $program->start_date->diffInDays($program->end_date);
        $daysPassed = $program->start_date->diffInDays($today);
        
        return $totalDays > 0 ? min(100, ($daysPassed / $totalDays) * 100) : 0;
    }

    /**
     * Calculate total people impacted across all programs
     */
    private function calculateTotalPeopleImpacted(): int
    {
        return Program::whereIn('status', ['active', 'completed'])
            ->whereNotNull('impact_metrics')
            ->get()
            ->sum(function ($program) {
                $metrics = $program->impact_metrics ?? [];
                return $metrics['people_reached'] ?? 0;
            });
    }
}
