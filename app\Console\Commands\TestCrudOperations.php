<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\User;
use Illuminate\Support\Str;

class TestCrudOperations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:crud-operations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all CRUD operations for admin blog functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Admin Blog CRUD Operations...');
        $this->newLine();

        $errors = [];

        // Test Blog Categories CRUD
        $this->info('🔍 Testing Blog Categories CRUD...');
        $errors = array_merge($errors, $this->testBlogCategoriesCrud());

        // Test Blog Posts CRUD
        $this->info('🔍 Testing Blog Posts CRUD...');
        $errors = array_merge($errors, $this->testBlogPostsCrud());

        // Test Blog Comments CRUD
        $this->info('🔍 Testing Blog Comments CRUD...');
        $errors = array_merge($errors, $this->testBlogCommentsCrud());

        // Summary
        $this->newLine();
        if (empty($errors)) {
            $this->info('✅ All CRUD operations are working correctly!');
        } else {
            $this->error('❌ Found issues with CRUD operations:');
            foreach ($errors as $error) {
                $this->line('  - ' . $error);
            }
        }

        return empty($errors) ? 0 : 1;
    }

    private function testBlogCategoriesCrud()
    {
        $errors = [];

        try {
            // Test Create
            $category = BlogCategory::create([
                'name' => 'Test Category ' . time(),
                'slug' => 'test-category-' . time(),
                'description' => 'Test description',
                'color' => '#ff5722',
                'is_active' => true,
                'sort_order' => 1
            ]);
            $this->line('  ✅ Category CREATE: Success');

            // Test Read
            $foundCategory = BlogCategory::find($category->id);
            if ($foundCategory) {
                $this->line('  ✅ Category READ: Success');
            } else {
                $errors[] = 'Category READ: Failed to find created category';
            }

            // Test Update
            $foundCategory->update(['name' => 'Updated Test Category']);
            if ($foundCategory->fresh()->name === 'Updated Test Category') {
                $this->line('  ✅ Category UPDATE: Success');
            } else {
                $errors[] = 'Category UPDATE: Failed to update category';
            }

            // Test Delete
            $foundCategory->delete();
            if (!BlogCategory::find($category->id)) {
                $this->line('  ✅ Category DELETE: Success');
            } else {
                $errors[] = 'Category DELETE: Failed to delete category';
            }

        } catch (\Exception $e) {
            $errors[] = 'Category CRUD Error: ' . $e->getMessage();
        }

        return $errors;
    }

    private function testBlogPostsCrud()
    {
        $errors = [];

        try {
            // Get or create a user for author
            $user = User::where('role', 'admin')->first();
            if (!$user) {
                $user = User::factory()->create(['role' => 'admin']);
            }

            // Get or create a category
            $category = BlogCategory::first();
            if (!$category) {
                $category = BlogCategory::create([
                    'name' => 'Default Category',
                    'slug' => 'default-category',
                    'is_active' => true
                ]);
            }

            // Test Create
            $post = BlogPost::create([
                'title' => 'Test Post ' . time(),
                'slug' => 'test-post-' . time(),
                'content' => 'This is test content for the blog post.',
                'excerpt' => 'Test excerpt',
                'author_id' => $user->id,
                'category_id' => $category->id,
                'status' => 'draft',
                'published_at' => null
            ]);
            $this->line('  ✅ Post CREATE: Success');

            // Test Read
            $foundPost = BlogPost::with(['author', 'category'])->find($post->id);
            if ($foundPost && $foundPost->author && $foundPost->category) {
                $this->line('  ✅ Post READ: Success (with relationships)');
            } else {
                $errors[] = 'Post READ: Failed to load post with relationships';
            }

            // Test Update
            $foundPost->update(['status' => 'published', 'published_at' => now()]);
            if ($foundPost->fresh()->status === 'published') {
                $this->line('  ✅ Post UPDATE: Success');
            } else {
                $errors[] = 'Post UPDATE: Failed to update post';
            }

            // Test Delete
            $foundPost->delete();
            if (!BlogPost::find($post->id)) {
                $this->line('  ✅ Post DELETE: Success');
            } else {
                $errors[] = 'Post DELETE: Failed to delete post';
            }

        } catch (\Exception $e) {
            $errors[] = 'Post CRUD Error: ' . $e->getMessage();
        }

        return $errors;
    }

    private function testBlogCommentsCrud()
    {
        $errors = [];

        try {
            // Get or create required data
            $user = User::first();
            if (!$user) {
                $user = User::factory()->create();
            }

            $post = BlogPost::first();
            if (!$post) {
                $category = BlogCategory::first() ?: BlogCategory::create([
                    'name' => 'Default Category',
                    'slug' => 'default-category',
                    'is_active' => true
                ]);

                $post = BlogPost::create([
                    'title' => 'Test Post for Comments',
                    'slug' => 'test-post-comments',
                    'content' => 'Test content',
                    'author_id' => $user->id,
                    'category_id' => $category->id,
                    'status' => 'published',
                    'published_at' => now()
                ]);
            }

            // Test Create
            $comment = BlogComment::create([
                'post_id' => $post->id,
                'user_id' => $user->id,
                'content' => 'This is a test comment ' . time(),
                'status' => 'pending'
            ]);
            $this->line('  ✅ Comment CREATE: Success');

            // Test Read
            $foundComment = BlogComment::with(['user', 'post'])->find($comment->id);
            if ($foundComment && $foundComment->user && $foundComment->post) {
                $this->line('  ✅ Comment READ: Success (with relationships)');
            } else {
                $errors[] = 'Comment READ: Failed to load comment with relationships';
            }

            // Test Update (Status change)
            $foundComment->update(['status' => 'approved']);
            if ($foundComment->fresh()->status === 'approved') {
                $this->line('  ✅ Comment UPDATE: Success');
            } else {
                $errors[] = 'Comment UPDATE: Failed to update comment status';
            }

            // Test Delete
            $foundComment->delete();
            if (!BlogComment::find($comment->id)) {
                $this->line('  ✅ Comment DELETE: Success');
            } else {
                $errors[] = 'Comment DELETE: Failed to delete comment';
            }

        } catch (\Exception $e) {
            $errors[] = 'Comment CRUD Error: ' . $e->getMessage();
        }

        return $errors;
    }
}
