@extends('layouts.admin')

@section('title', 'Create Volunteer Opportunity')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create New Volunteer Opportunity</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.volunteers.opportunities.index') }}">Volunteer Opportunities</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.volunteers.opportunities.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Opportunity Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required value="{{ old('title') }}">
                                <div class="form-text">Enter a clear and descriptive title for the volunteer opportunity</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="education" {{ old('category') == 'education' ? 'selected' : '' }}>Education</option>
                                    <option value="health" {{ old('category') == 'health' ? 'selected' : '' }}>Health</option>
                                    <option value="environment" {{ old('category') == 'environment' ? 'selected' : '' }}>Environment</option>
                                    <option value="community" {{ old('category') == 'community' ? 'selected' : '' }}>Community</option>
                                    <option value="events" {{ old('category') == 'events' ? 'selected' : '' }}>Events</option>
                                    <option value="administration" {{ old('category') == 'administration' ? 'selected' : '' }}>Administration</option>
                                    <option value="fundraising" {{ old('category') == 'fundraising' ? 'selected' : '' }}>Fundraising</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="time_commitment" class="form-label">Time Commitment <span class="text-danger">*</span></label>
                                <select class="form-select" id="time_commitment" name="time_commitment" required>
                                    <option value="">Select Time Commitment</option>
                                    <option value="one-time" {{ old('time_commitment') == 'one-time' ? 'selected' : '' }}>One Time</option>
                                    <option value="weekly" {{ old('time_commitment') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                    <option value="monthly" {{ old('time_commitment') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="flexible" {{ old('time_commitment') == 'flexible' ? 'selected' : '' }}>Flexible</option>
                                    <option value="ongoing" {{ old('time_commitment') == 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" value="{{ old('location') }}" placeholder="e.g., Lagos, Nigeria or Remote">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="max_volunteers" class="form-label">Maximum Volunteers</label>
                                <input type="number" class="form-control" id="max_volunteers" name="max_volunteers" min="1" value="{{ old('max_volunteers') }}">
                                <div class="form-text">Leave empty for unlimited volunteers</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="application_deadline" name="application_deadline" required value="{{ old('application_deadline') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ old('start_date') }}">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ old('end_date') }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required>{{ old('description') }}</textarea>
                            <div class="form-text">Provide detailed information about the volunteer opportunity</div>
                        </div>

                        <div class="mb-3">
                            <label for="required_skills" class="form-label">Required Skills</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="communication" id="skill_communication">
                                        <label class="form-check-label" for="skill_communication">Communication</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="leadership" id="skill_leadership">
                                        <label class="form-check-label" for="skill_leadership">Leadership</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="teaching" id="skill_teaching">
                                        <label class="form-check-label" for="skill_teaching">Teaching</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="computer_skills" id="skill_computer">
                                        <label class="form-check-label" for="skill_computer">Computer Skills</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="fundraising" id="skill_fundraising">
                                        <label class="form-check-label" for="skill_fundraising">Fundraising</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="event_planning" id="skill_event_planning">
                                        <label class="form-check-label" for="skill_event_planning">Event Planning</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="social_media" id="skill_social_media">
                                        <label class="form-check-label" for="skill_social_media">Social Media</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="photography" id="skill_photography">
                                        <label class="form-check-label" for="skill_photography">Photography</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="required_skills[]" value="writing" id="skill_writing">
                                        <label class="form-check-label" for="skill_writing">Writing</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active Opportunity
                                    </label>
                                    <div class="form-text">Uncheck to save as draft</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        Featured Opportunity
                                    </label>
                                    <div class="form-text">Featured opportunities appear prominently</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.volunteers.opportunities.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Opportunities
                            </a>
                            <div>
                                <button type="submit" name="action" value="draft" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-save me-2"></i>Save as Draft
                                </button>
                                <button type="submit" name="action" value="publish" class="btn btn-primary">
                                    <i class="fas fa-rocket me-2"></i>Create & Publish
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Tips</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips for Success</h6>
                        <ul class="mb-0 small">
                            <li>Use clear, specific titles</li>
                            <li>Set realistic deadlines</li>
                            <li>Define required skills clearly</li>
                            <li>Specify time commitment</li>
                            <li>Include location details</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Volunteer Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ \App\Models\VolunteerOpportunity::where('is_active', true)->count() }}</h4>
                                <small class="text-muted">Active Opportunities</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ \App\Models\Volunteer::where('application_status', 'approved')->count() }}</h4>
                            <small class="text-muted">Active Volunteers</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('application_deadline').setAttribute('min', today);
    document.getElementById('start_date').setAttribute('min', today);
    document.getElementById('end_date').setAttribute('min', today);
    
    // Update end date minimum when start date changes
    document.getElementById('start_date').addEventListener('change', function() {
        document.getElementById('end_date').setAttribute('min', this.value);
    });
});
</script>
@endsection
