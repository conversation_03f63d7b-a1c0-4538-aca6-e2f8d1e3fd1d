<!-- Primary School Application Form -->
<div class="space-y-8">
    <!-- Student Information -->
    <div>
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Student Information
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="student_full_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Student Full Name <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="student_full_name" 
                    name="student_full_name" 
                    value="{{ old('student_full_name') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_full_name') border-red-500 @enderror"
                    required
                >
                @error('student_full_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="student_age" class="block text-sm font-medium text-gray-700 mb-2">
                    Student Age <span class="text-red-500">*</span>
                </label>
                <input 
                    type="number" 
                    id="student_age" 
                    name="student_age" 
                    value="{{ old('student_age') }}"
                    min="5" 
                    max="15"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_age') border-red-500 @enderror"
                    required
                >
                @error('student_age')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="student_gender" class="block text-sm font-medium text-gray-700 mb-2">
                    Gender <span class="text-red-500">*</span>
                </label>
                <select 
                    id="student_gender" 
                    name="student_gender" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_gender') border-red-500 @enderror"
                    required
                >
                    <option value="">Select Gender</option>
                    <option value="male" {{ old('student_gender') === 'male' ? 'selected' : '' }}>Male</option>
                    <option value="female" {{ old('student_gender') === 'female' ? 'selected' : '' }}>Female</option>
                </select>
                @error('student_gender')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="student_grade" class="block text-sm font-medium text-gray-700 mb-2">
                    Current Grade/Class <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="student_grade" 
                    name="student_grade" 
                    value="{{ old('student_grade') }}"
                    placeholder="e.g., Grade 1, Primary 3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_grade') border-red-500 @enderror"
                    required
                >
                @error('student_grade')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="student_date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth <span class="text-red-500">*</span>
                </label>
                <input 
                    type="date" 
                    id="student_date_of_birth" 
                    name="student_date_of_birth" 
                    value="{{ old('student_date_of_birth') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_date_of_birth') border-red-500 @enderror"
                    required
                >
                @error('student_date_of_birth')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- Parent/Guardian Information -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Parent/Guardian Information
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="parent_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Parent/Guardian Full Name <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="parent_name" 
                    name="parent_name" 
                    value="{{ old('parent_name') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('parent_name') border-red-500 @enderror"
                    required
                >
                @error('parent_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="parent_relationship" class="block text-sm font-medium text-gray-700 mb-2">
                    Relationship to Student <span class="text-red-500">*</span>
                </label>
                <select 
                    id="parent_relationship" 
                    name="parent_relationship" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('parent_relationship') border-red-500 @enderror"
                    required
                >
                    <option value="">Select Relationship</option>
                    <option value="father" {{ old('parent_relationship') === 'father' ? 'selected' : '' }}>Father</option>
                    <option value="mother" {{ old('parent_relationship') === 'mother' ? 'selected' : '' }}>Mother</option>
                    <option value="guardian" {{ old('parent_relationship') === 'guardian' ? 'selected' : '' }}>Guardian</option>
                    <option value="uncle" {{ old('parent_relationship') === 'uncle' ? 'selected' : '' }}>Uncle</option>
                    <option value="aunt" {{ old('parent_relationship') === 'aunt' ? 'selected' : '' }}>Aunt</option>
                    <option value="grandparent" {{ old('parent_relationship') === 'grandparent' ? 'selected' : '' }}>Grandparent</option>
                    <option value="other" {{ old('parent_relationship') === 'other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('parent_relationship')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="parent_phone" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                </label>
                <input 
                    type="tel" 
                    id="parent_phone" 
                    name="parent_phone" 
                    value="{{ old('parent_phone') }}"
                    placeholder="e.g., 08012345678"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('parent_phone') border-red-500 @enderror"
                    required
                >
                @error('parent_phone')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="parent_email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address (Optional)
                </label>
                <input 
                    type="email" 
                    id="parent_email" 
                    name="parent_email" 
                    value="{{ old('parent_email') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('parent_email') border-red-500 @enderror"
                >
                @error('parent_email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="md:col-span-2">
                <label for="home_address" class="block text-sm font-medium text-gray-700 mb-2">
                    Home Address <span class="text-red-500">*</span>
                </label>
                <textarea 
                    id="home_address" 
                    name="home_address" 
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('home_address') border-red-500 @enderror"
                    required
                >{{ old('home_address') }}</textarea>
                @error('home_address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- School Information -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            School Information
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="school_account_number" class="block text-sm font-medium text-gray-700 mb-2">
                    School Account Number <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="school_account_number" 
                    name="school_account_number" 
                    value="{{ old('school_account_number') }}"
                    placeholder="School's bank account number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('school_account_number') border-red-500 @enderror"
                    required
                >
                @error('school_account_number')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="headmaster_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Headmaster/Principal Name <span class="text-red-500">*</span>
                </label>
                <input 
                    type="text" 
                    id="headmaster_name" 
                    name="headmaster_name" 
                    value="{{ old('headmaster_name') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('headmaster_name') border-red-500 @enderror"
                    required
                >
                @error('headmaster_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="current_school_fee" class="block text-sm font-medium text-gray-700 mb-2">
                    Current School Fee (₦) <span class="text-red-500">*</span>
                </label>
                <input 
                    type="number" 
                    id="current_school_fee" 
                    name="current_school_fee" 
                    value="{{ old('current_school_fee') }}"
                    min="0"
                    step="0.01"
                    placeholder="e.g., 50000"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('current_school_fee') border-red-500 @enderror"
                    required
                >
                @error('current_school_fee')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Additional Information
        </h3>
        
        <div class="space-y-6">
            <div>
                <label for="financial_need_explanation" class="block text-sm font-medium text-gray-700 mb-2">
                    Explain Your Financial Need <span class="text-red-500">*</span>
                </label>
                <textarea 
                    id="financial_need_explanation" 
                    name="financial_need_explanation" 
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('financial_need_explanation') border-red-500 @enderror"
                    placeholder="Please describe your family's financial situation and why you need this scholarship..."
                    required
                >{{ old('financial_need_explanation') }}</textarea>
                @error('financial_need_explanation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="supporting_information" class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Supporting Information (Optional)
                </label>
                <textarea 
                    id="supporting_information" 
                    name="supporting_information" 
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('supporting_information') border-red-500 @enderror"
                    placeholder="Any additional information that supports your application..."
                >{{ old('supporting_information') }}</textarea>
                @error('supporting_information')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- File Uploads -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Required Documents
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="student_photo" class="block text-sm font-medium text-gray-700 mb-2">
                    Student Photo <span class="text-red-500">*</span>
                </label>
                <input 
                    type="file" 
                    id="student_photo" 
                    name="student_photo" 
                    accept="image/jpeg,image/png,image/jpg"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_photo') border-red-500 @enderror"
                    required
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('student_photo')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="parent_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Parent/Guardian ID Card <span class="text-red-500">*</span>
                </label>
                <input 
                    type="file" 
                    id="parent_id" 
                    name="parent_id" 
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('parent_id') border-red-500 @enderror"
                    required
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('parent_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="school_fee_receipt" class="block text-sm font-medium text-gray-700 mb-2">
                    School Fee Receipt (Optional)
                </label>
                <input 
                    type="file" 
                    id="school_fee_receipt" 
                    name="school_fee_receipt" 
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('school_fee_receipt') border-red-500 @enderror"
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('school_fee_receipt')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>
</div>
