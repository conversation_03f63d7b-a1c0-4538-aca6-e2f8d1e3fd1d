<?php

echo "=== COMPLETE INTEGRATION TEST ===\n\n";

// Test 1: API Endpoints with Dynamic Data
echo "1. Testing API Endpoints with Dynamic Data...\n";

try {
    // Test public scholarships endpoint
    $response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $scholarships = $data['data']['data'];
        echo "   ✓ Public scholarships API: " . count($scholarships) . " scholarships\n";
        
        // Test individual scholarship endpoint
        if (!empty($scholarships)) {
            $firstId = $scholarships[0]['id'];
            $detailResponse = file_get_contents("http://127.0.0.1:8000/api/v1/public-scholarships/{$firstId}");
            $detailData = json_decode($detailResponse, true);
            
            if ($detailData && $detailData['success']) {
                $scholarship = $detailData['data'];
                echo "   ✓ Individual scholarship API working\n";
                echo "     - Title: {$scholarship['title']}\n";
                echo "     - Category: {$scholarship['category']}\n";
                echo "     - Custom fields: " . count($scholarship['custom_fields'] ?? []) . "\n";
                
                // Check category instructions
                if (isset($scholarship['category_instructions'])) {
                    echo "     - Category instructions: Present\n";
                    echo "       * Filled by: {$scholarship['category_instructions']['filled_by']}\n";
                } else {
                    echo "     - Category instructions: Missing\n";
                }
            } else {
                echo "   ✗ Individual scholarship API failed\n";
            }
        }
    } else {
        echo "   ✗ Public scholarships API failed\n";
    }
} catch (Exception $e) {
    echo "   ✗ API test error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Category Filtering
echo "2. Testing Category Filtering...\n";

$categories = ['primary', 'secondary', 'university'];
foreach ($categories as $category) {
    try {
        $response = file_get_contents("http://127.0.0.1:8000/api/v1/public-scholarships?category={$category}");
        $data = json_decode($response, true);
        
        if ($data && $data['success']) {
            $count = count($data['data']['data']);
            echo "   ✓ {$category} category: {$count} scholarships\n";
            
            // Verify all scholarships match the category
            $allCorrect = true;
            foreach ($data['data']['data'] as $scholarship) {
                if ($scholarship['category'] !== $category) {
                    $allCorrect = false;
                    break;
                }
            }
            
            if ($allCorrect) {
                echo "     - All scholarships match category filter\n";
            } else {
                echo "     - Some scholarships don't match category filter\n";
            }
        } else {
            echo "   ✗ {$category} category filter failed\n";
        }
    } catch (Exception $e) {
        echo "   ✗ {$category} category error: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 3: Frontend Accessibility
echo "3. Testing Frontend Accessibility...\n";

$frontendUrls = [
    'http://localhost:3000/scholarships' => 'Scholarships listing page',
    'http://localhost:3000/scholarships/1' => 'Individual scholarship page',
    'http://localhost:3000/scholarship-application' => 'Application form page',
    'http://localhost:3000/scholarship-application?category=primary' => 'Primary category pre-selected',
    'http://localhost:3000/scholarship-application?category=secondary' => 'Secondary category pre-selected',
    'http://localhost:3000/scholarship-application?category=university' => 'University category pre-selected',
];

foreach ($frontendUrls as $url => $description) {
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            echo "   ✓ {$description}: Accessible\n";
            
            // Check for specific content indicators
            if (strpos($url, 'scholarships') !== false && strpos($url, 'application') === false) {
                if (strpos($response, 'scholarship') !== false || strpos($response, 'Scholarship') !== false) {
                    echo "     - Contains scholarship content\n";
                }
            }
            
            if (strpos($url, 'scholarship-application') !== false) {
                if (strpos($response, 'application') !== false || strpos($response, 'Application') !== false) {
                    echo "     - Contains application form content\n";
                }
            }
        } else {
            echo "   ⚠ {$description}: Not accessible (server may be down)\n";
        }
    } catch (Exception $e) {
        echo "   ⚠ {$description}: Error - " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 4: URL Parameter Handling
echo "4. Testing URL Parameter Handling...\n";

// Test redirect URLs that should work
$redirectTests = [
    'scholarship-application?category=primary&scholarship_id=1' => 'Primary with scholarship ID',
    'scholarship-application?category=secondary&scholarship_id=2' => 'Secondary with scholarship ID',
    'scholarship-application?category=university&scholarship_id=3' => 'University with scholarship ID',
];

foreach ($redirectTests as $path => $description) {
    $url = "http://localhost:3000/{$path}";
    echo "   Testing: {$description}\n";
    echo "     URL: {$url}\n";
    
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 3,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            echo "     ✓ URL accessible\n";
        } else {
            echo "     ⚠ URL not accessible\n";
        }
    } catch (Exception $e) {
        echo "     ⚠ Error: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 5: Database Dynamic Data Verification
echo "5. Final Database Dynamic Data Verification...\n";

try {
    // Check if we have varying data
    $response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $scholarships = $data['data']['data'];
        
        // Check for data variety
        $amounts = array_unique(array_column($scholarships, 'amount'));
        $categories = array_unique(array_column($scholarships, 'category'));
        $deadlines = array_unique(array_column($scholarships, 'application_deadline'));
        
        echo "   ✓ Unique amounts: " . count($amounts) . "\n";
        echo "   ✓ Different categories: " . count($categories) . " (" . implode(', ', $categories) . ")\n";
        echo "   ✓ Different deadlines: " . count($deadlines) . "\n";
        
        // Check custom fields
        $hasCustomFields = false;
        foreach ($scholarships as $scholarship) {
            if (isset($scholarship['custom_fields']) && !empty($scholarship['custom_fields'])) {
                $hasCustomFields = true;
                break;
            }
        }
        
        if ($hasCustomFields) {
            echo "   ✓ Custom fields present in API responses\n";
        } else {
            echo "   ⚠ Custom fields missing from API responses\n";
        }
        
        // Check category instructions
        $hasInstructions = false;
        foreach ($scholarships as $scholarship) {
            if (isset($scholarship['category_instructions'])) {
                $hasInstructions = true;
                break;
            }
        }
        
        if ($hasInstructions) {
            echo "   ✓ Category instructions present\n";
        } else {
            echo "   ⚠ Category instructions missing\n";
        }
        
    } else {
        echo "   ✗ Failed to fetch scholarship data\n";
    }
} catch (Exception $e) {
    echo "   ✗ Database verification error: " . $e->getMessage() . "\n";
}

echo "\n";

echo "=== INTEGRATION TEST SUMMARY ===\n";
echo "✓ = Working correctly\n";
echo "⚠ = Potential issues or needs attention\n";
echo "✗ = Error or not working\n\n";

echo "Key Features Tested:\n";
echo "1. API endpoints serving dynamic data from database\n";
echo "2. Category filtering working correctly\n";
echo "3. Frontend pages accessible\n";
echo "4. URL parameter handling for category pre-selection\n";
echo "5. Custom fields and category instructions included\n\n";

echo "Next Steps:\n";
echo "- Test the Apply button redirect functionality manually\n";
echo "- Verify category tabs are pre-selected based on URL parameters\n";
echo "- Test form submission with dynamic fields\n";
echo "- Ensure mobile responsiveness\n";

?>
