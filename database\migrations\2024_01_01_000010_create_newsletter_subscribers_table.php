<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('newsletter_subscribers', function (Blueprint $table) {
            $table->id();
            $table->string('email', 100)->unique();
            $table->enum('status', ['subscribed', 'unsubscribed'])->default('subscribed');
            $table->string('subscription_source', 100)->nullable();
            $table->json('preferences')->nullable();
            $table->string('verification_token')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('subscribed_at')->default(now());
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['email', 'status']);
            $table->index('status');
            $table->index('subscribed_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('newsletter_subscribers');
    }
}; 