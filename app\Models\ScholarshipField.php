<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScholarshipField extends Model
{
    use HasFactory;

    protected $fillable = [
        'scholarship_id',
        'field_name',
        'field_label',
        'field_type',
        'field_options',
        'is_required',
        'field_order',
        'is_active',
    ];

    protected $casts = [
        'field_options' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'field_order' => 'integer',
    ];

    /**
     * Get the scholarship that owns this field.
     */
    public function scholarship(): BelongsTo
    {
        return $this->belongsTo(Scholarship::class);
    }

    /**
     * Scope active fields
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope ordered fields
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('field_order');
    }
}
