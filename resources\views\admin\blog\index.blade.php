@extends('layouts.admin')

@section('title', 'Blog Management Dashboard')

@section('styles')
<style>
    .stats-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .loading-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #6c757d;
    }
    .error-message {
        color: #dc3545;
        text-align: center;
        padding: 1rem;
        border: 1px solid #dc3545;
        border-radius: .25rem;
        background-color: #f8d7da;
    }
    .list-group-item small {
        font-size: 0.85em;
    }
    .category-progress {
        height: 8px;
        border-radius: 4px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    @if(isset($error))
        <div class="alert alert-danger">{{ $error }}</div>
    @endif

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Blog Dashboard</h1>
        <a href="{{ route('admin.blog.posts.create') }}" class="btn btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50 me-2"></i>Create New Post
        </a>
    </div>

    <!-- Quick Actions Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Manage Posts</div>
                            <a href="{{ route('admin.blog.posts') }}" class="btn btn-sm btn-outline-primary mt-2">View Posts</a>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-blog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Categories</div>
                             <a href="{{ route('admin.blog.categories') }}" class="btn btn-sm btn-outline-info mt-2">Manage Categories</a>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Comments</div>
                            <a href="{{ route('admin.blog.comments') }}" class="btn btn-sm btn-outline-warning mt-2">View Comments</a>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Analytics (Soon)</div>
                            <button onclick="alert('Blog analytics coming soon!')" class="btn btn-sm btn-outline-success mt-2">View Analytics</button>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Statistics Row -->
    @if(isset($dashboardData))
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Posts</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['total_posts']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-newspaper fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Published Posts</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['published_posts']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-check-circle fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Draft Posts</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['draft_posts']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-edit fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Views</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['total_views']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-eye fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
         <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Total Categories</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['total_categories']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-sitemap fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
         <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Total Comments</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($dashboardData['stats']['total_comments']) }}</div>
                        </div>
                        <div class="col-auto"><i class="fas fa-comment-dots fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Posts -->
        <div class="col-lg-7 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Blog Posts</h6>
                    <a href="{{ route('admin.blog.posts') }}" class="btn btn-sm btn-primary shadow-sm">View All Posts</a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @if(isset($dashboardData['recent_posts']) && count($dashboardData['recent_posts']) > 0)
                            @foreach($dashboardData['recent_posts'] as $post)
                                <a href="{{ route('admin.blog.posts') }}#post-{{ $post['id'] }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 font-weight-bold">{{ Str::limit($post['title'], 60) }}</h6>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($post['created_at'])->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-1 small text-muted">
                                        By {{ $post['author']['name'] }}
                                        @if($post['category']) | In {{ $post['category'] }} @endif
                                    </p>
                                    <small class="text-info">{{ ucfirst($post['status']) }} | <i class="fas fa-eye fa-xs"></i> {{ number_format($post['views_count']) }} views</small>
                                </a>
                            @endforeach
                        @else
                            <p class="text-center text-muted p-3">No recent posts found.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories & Recent Comments -->
        <div class="col-lg-5 mb-4">
            <!-- Popular Categories -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Categories</h6>
                </div>
                <div class="card-body">
                    @if(isset($dashboardData['categories']) && count($dashboardData['categories']) > 0)
                        @php
                            $sortedCategories = collect($dashboardData['categories'])->sortByDesc('posts_count')->take(5);
                            $maxPosts = $sortedCategories->max('posts_count') ?: 1;
                        @endphp
                        @foreach($sortedCategories as $category)
                            @php
                                $percentage = $maxPosts > 0 ? (($category['posts_count'] ?? 0) / $maxPosts) * 100 : 0;
                            @endphp
                            <div>
                                <h4 class="small font-weight-bold">
                                    {{ Str::limit($category['name'], 25) }} 
                                    <span class="float-right">{{ number_format($category['posts_count']) }} posts</span>
                                </h4>
                                <div class="progress mb-2 category-progress">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ $percentage }}%; background-color: {{ $category['color'] ?? '#4e73df' }}" 
                                         aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p class="text-center text-muted p-3">No categories found.</p>
                    @endif
                </div>
            </div>

            <!-- Recent Comments -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Comments</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @if(isset($dashboardData['recent_comments']) && count($dashboardData['recent_comments']) > 0)
                            @foreach($dashboardData['recent_comments'] as $comment)
                                <a href="{{ route('admin.blog.comments') }}#comment-{{ $comment['id'] }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 small">
                                            <i class="fas fa-user fa-xs me-1"></i>{{ $comment['user']['name'] }}
                                            <span class="text-muted">on</span> 
                                            "{{ Str::limit($comment['post']['title'], 25) }}"
                                        </h6>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($comment['created_at'])->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-1 small text-muted">{{ Str::limit($comment['content'], 80) }}</p>
                                    <small class="badge bg-{{ $comment['status'] === 'approved' ? 'success' : ($comment['status'] === 'pending' ? 'warning' : 'danger') }}">{{ $comment['status'] }}</small>
                                </a>
                            @endforeach
                        @else
                            <p class="text-center text-muted p-3">No recent comments.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
class BlogDashboardManager {
    constructor() {
        this.csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        this.apiBaseUrl = '/admin/api/blog'; // Updated base URL for blog specific admin APIs

        // Element selectors
        this.statsElements = {
            totalPosts: document.getElementById('totalPosts'),
            publishedPosts: document.getElementById('publishedPosts'),
            draftPosts: document.getElementById('draftPosts'),
            totalViews: document.getElementById('totalViews'),
            totalCategories: document.getElementById('totalCategories'),
            totalComments: document.getElementById('totalComments'),
        };
        this.recentPostsContainer = document.getElementById('recentPostsContainer');
        this.popularCategoriesContainer = document.getElementById('popularCategoriesContainer');
        this.recentCommentsContainer = document.getElementById('recentCommentsContainer');
        this.blogStatsRow = document.getElementById('blogStatsRow'); // To show/hide all stats cards
    }

    async fetchData(endpoint) {
        try {
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: response.statusText }));
                throw new Error(`HTTP error ${response.status}: ${errorData.message}`);
            }
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message || 'API request failed');
            }
            return data.data;
        } catch (error) {
            console.error(`Error fetching ${endpoint}:`, error);
            this.showGlobalError(`Failed to load data for ${endpoint.substring(1)}. ${error.message}`);
            return null; // Return null to allow graceful handling by callers
        }
    }

    showGlobalError(message) {
        // For simplicity, we'll use an alert. In a real app, you might have a dedicated error display area.
        // alert(`Dashboard Error: ${message}`);
        // Or update specific sections with error messages
        if(this.recentPostsContainer) this.recentPostsContainer.innerHTML = this.renderErrorMessage(message);
        if(this.popularCategoriesContainer) this.popularCategoriesContainer.innerHTML = this.renderErrorMessage(message);
        if(this.recentCommentsContainer) this.recentCommentsContainer.innerHTML = this.renderErrorMessage(message);
        
        // Hide stats or show error placeholders for stats
        Object.values(this.statsElements).forEach(el => {
            if (el) el.textContent = 'Error';
        });

    }

    renderErrorMessage(message, containerId = null) {
        const fullMessage = `An error occurred: ${message}. Please try refreshing the page or contact support if the issue persists.`;
        const errorHtml = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle fa-lg me-2"></i>
                ${fullMessage}
                <button class="btn btn-sm btn-danger mt-2" onclick="location.reload()">Refresh Page</button>
            </div>`;
        if (containerId) {
            const container = document.getElementById(containerId);
            if (container) container.innerHTML = errorHtml;
        }
        return errorHtml;
    }
    
    renderLoadingPlaceholder(container, message = "Loading...") {
        if (container) {
            container.innerHTML = `
                <div class="loading-placeholder">
                    <div class="spinner-border text-primary spinner-border-sm" role="status"></div> 
                    <span class="ms-2">${message}</span>
                </div>`;
        }
    }

    formatNumber(num) {
        return num !== null && num !== undefined ? parseInt(num).toLocaleString() : '0';
    }

    timeAgo(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) { const m = Math.floor(diffInSeconds / 60); return `${m} min${m > 1 ? 's' : ''} ago`; }
        if (diffInSeconds < 86400) { const h = Math.floor(diffInSeconds / 3600); return `${h} hour${h > 1 ? 's' : ''} ago`; }
        const d = Math.floor(diffInSeconds / 86400); return `${d} day${d > 1 ? 's' : ''} ago`;
    }
    
    truncateText(text, length = 50) {
        if (!text) return '';
        return text.length > length ? text.substring(0, length) + '...' : text;
    }

    renderStats(stats) {
        if (!stats) {
             Object.values(this.statsElements).forEach(el => { if(el) el.textContent = 'N/A'; });
            return;
        }
        this.statsElements.totalPosts.textContent = this.formatNumber(stats.total_posts);
        this.statsElements.publishedPosts.textContent = this.formatNumber(stats.published_posts);
        this.statsElements.draftPosts.textContent = this.formatNumber(stats.draft_posts);
        this.statsElements.totalViews.textContent = this.formatNumber(stats.total_views);
        this.statsElements.totalCategories.textContent = this.formatNumber(stats.total_categories);
        this.statsElements.totalComments.textContent = this.formatNumber(stats.total_comments);
    }

    renderRecentPosts(posts) {
        if (!this.recentPostsContainer) return;
        if (!posts || posts.length === 0) {
            this.recentPostsContainer.innerHTML = '<p class="text-center text-muted p-3">No recent posts found.</p>';
            return;
        }
        this.recentPostsContainer.innerHTML = posts.map(post => `
            <a href="/admin/blog/posts#post-${post.id}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 font-weight-bold">${this.truncateText(post.title, 60)}</h6>
                    <small class="text-muted">${this.timeAgo(post.created_at)}</small>
                </div>
                <p class="mb-1 small text-muted">
                    By ${post.author ? this.truncateText(post.author.name, 30) : 'N/A'}
                    ${post.category ? ` | In ${this.truncateText(post.category, 20)}` : ''}
                </p>
                <small class="text-info">${post.status ? post.status.charAt(0).toUpperCase() + post.status.slice(1) : 'N/A'} | <i class="fas fa-eye fa-xs"></i> ${this.formatNumber(post.views_count)} views</small>
            </a>
        `).join('');
    }

    renderPopularCategories(categories) {
        if (!this.popularCategoriesContainer) return;
        if (!categories || categories.length === 0) {
            this.popularCategoriesContainer.innerHTML = '<p class="text-center text-muted p-3">No categories found.</p>';
            return;
        }
        // Sort categories by posts_count descending, then take top 5
        const sortedCategories = [...categories].sort((a, b) => (b.posts_count || 0) - (a.posts_count || 0)).slice(0, 5);
        const maxPosts = Math.max(...sortedCategories.map(cat => cat.posts_count || 0), 1); // Avoid division by zero

        this.popularCategoriesContainer.innerHTML = sortedCategories.map(category => {
            const percentage = maxPosts > 0 ? ((category.posts_count || 0) / maxPosts) * 100 : 0;
            return `
                <div>
                    <h4 class="small font-weight-bold">
                        ${this.truncateText(category.name, 25)} 
                        <span class="float-right">${this.formatNumber(category.posts_count)} posts</span>
                    </h4>
                    <div class="progress mb-2 category-progress">
                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%; background-color: ${category.color || '#4e73df'}" 
                             aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderRecentComments(comments) {
        if (!this.recentCommentsContainer) return;
        if (!comments || comments.length === 0) {
            this.recentCommentsContainer.innerHTML = '<p class="text-center text-muted p-3">No recent comments.</p>';
            return;
        }
        this.recentCommentsContainer.innerHTML = comments.map(comment => `
            <a href="/admin/blog/comments#comment-${comment.id}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 small">
                        <i class="fas fa-user fa-xs me-1"></i>${comment.user ? this.truncateText(comment.user.name, 20) : 'Anonymous'} 
                        <span class="text-muted">on</span> 
                        "${comment.post ? this.truncateText(comment.post.title, 25) : 'a post'}"
                    </h6>
                    <small class="text-muted">${this.timeAgo(comment.created_at)}</small>
                </div>
                <p class="mb-1 small text-muted">${this.truncateText(comment.content, 80)}</p>
                 <small class="badge bg-${comment.status === 'approved' ? 'success' : (comment.status === 'pending' ? 'warning' : 'danger')}">${comment.status}</small>
            </a>
        `).join('');
    }

    async init() {
        this.renderLoadingPlaceholder(this.recentPostsContainer, 'Loading recent posts...');
        this.renderLoadingPlaceholder(this.popularCategoriesContainer, 'Loading categories...');
        this.renderLoadingPlaceholder(this.recentCommentsContainer, 'Loading comments...');

        const dashboardData = await this.fetchData('/dashboard');
        
        if (dashboardData) {
            if (dashboardData.stats) this.renderStats(dashboardData.stats);
            if (dashboardData.recent_posts) this.renderRecentPosts(dashboardData.recent_posts);
            if (dashboardData.categories) this.renderPopularCategories(dashboardData.categories);
            if (dashboardData.recent_comments) this.renderRecentComments(dashboardData.recent_comments);
        } else {
            // Global error already shown by fetchData, or specific containers show errors if they fail individually
            this.renderStats(null); // Show N/A or error for stats if global data failed
            this.recentPostsContainer.innerHTML = this.renderErrorMessage('Failed to load recent posts data.', 'recentPostsContainer');
            this.popularCategoriesContainer.innerHTML = this.renderErrorMessage('Failed to load categories data.', 'popularCategoriesContainer');
            this.recentCommentsContainer.innerHTML = this.renderErrorMessage('Failed to load comments data.', 'recentCommentsContainer');
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    window.blogDashboardManager = new BlogDashboardManager();
    window.blogDashboardManager.init();
});
</script>
@endpush 