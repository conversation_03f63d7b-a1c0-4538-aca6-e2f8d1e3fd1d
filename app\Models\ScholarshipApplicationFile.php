<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ScholarshipApplicationFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'application_id',
        'field_name',
        'original_name',
        'stored_name',
        'file_path',
        'mime_type',
        'file_size',
        'file_hash',
        'is_verified',
        'admin_notes',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_verified' => 'boolean',
    ];

    /**
     * Get the application that owns this file
     */
    public function application()
    {
        return $this->belongsTo(ScholarshipApplication::class, 'application_id');
    }

    /**
     * Get the full file URL
     */
    public function getFileUrlAttribute()
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get the file size in human readable format
     */
    public function getFileSizeHumanAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage()
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is a PDF
     */
    public function isPdf()
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Check if file exists in storage
     */
    public function fileExists()
    {
        return Storage::exists($this->file_path);
    }

    /**
     * Delete file from storage
     */
    public function deleteFile()
    {
        if ($this->fileExists()) {
            Storage::delete($this->file_path);
        }
    }

    /**
     * Boot method to handle file deletion
     */
    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function ($file) {
            $file->deleteFile();
        });
    }

    /**
     * Scope for verified files
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for unverified files
     */
    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }
}
