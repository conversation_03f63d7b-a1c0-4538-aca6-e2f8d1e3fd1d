"use client"

import { useEffect } from "react"
import Link from "next/link"
import { <PERSON>Left, Heart } from "lucide-react"

export default function LoginPage() {
  useEffect(() => {
    // Get Laravel base URL from environment
    const laravelBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

    // Redirect to Laravel login page
    window.location.href = `${laravelBaseUrl}/login`
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-amber-50">
      <div className="w-full max-w-md mx-auto p-6">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="relative h-12 w-12 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
              <Heart className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">HLTKKQ Foundation</h1>
          </div>
          <p className="text-green-600 font-medium">Empowering Communities Through Education</p>
        </div>

        {/* Loading State */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Redirecting to Login</h2>
          <p className="text-gray-600 mb-6">Please wait while we redirect you to the login page...</p>

          {/* Manual redirect link in case JavaScript fails */}
          <div className="text-sm text-gray-500">
            <p>If you're not redirected automatically,</p>
            <a
              href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}/login`}
              className="text-green-600 hover:text-green-700 font-medium underline"
            >
              click here to login
            </a>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-6">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}