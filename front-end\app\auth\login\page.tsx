"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Heart,
  Mail,
  Lock,
  ArrowLeft,
  Eye,
  EyeOff,
  Facebook,
  Twitter,
  GraduationCap,
  BookOpen,
  Users,
} from "lucide-react"
import { apiClient } from "@/lib/api"
import { useSettings } from "@/hooks/useSettings"


export default function LoginPage() {
  const router = useRouter()
  const { settings, loading: settingsLoading } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [acceptTerms, setAcceptTerms] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Registration form state
  const [registerData, setRegisterData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
  })

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      console.log('Attempting login for:', email) // Debug logging
      const data = await apiClient.login(email, password)
      console.log('Login response:', data) // Debug logging

      if (data.success && data.data?.access_token) {
        // Store the token and user data
        localStorage.setItem("authToken", data.data.access_token)
        
        // Handle different user data formats from Laravel response
        const userData = data.data.user || data.data
        if (userData) {
          localStorage.setItem('user', JSON.stringify(userData))
          console.log('User data stored:', userData) // Debug logging
        } else {
          console.warn('No user data received in login response')
        }

        // Handle successful login
        console.log('Login successful, redirecting...')
        alert(data.message || "Login successful!") // Or use a toast notification

        // Check for stored redirect URL
        const redirectUrl = localStorage.getItem('redirectAfterLogin')
        if (redirectUrl) {
          localStorage.removeItem('redirectAfterLogin')
          router.push(redirectUrl)
        } else {
          // Role-based dashboard routing
          const userRole = userData.role
          const userPreferences = userData.preferences

          if (userRole === 'partner_organization' || userData.partner_organization_id) {
            router.push("/dashboard") // Partner Organization Dashboard
          } else if (userRole === 'admin') {
            router.push("/dashboard") // Admin Dashboard
          } else if (userRole === 'volunteer') {
            router.push("/dashboard") // Volunteer Dashboard
          } else if (userPreferences?.user_type === 'student' || userRole === 'student') {
            router.push("/dashboard") // Student Dashboard
          } else {
            router.push("/dashboard") // Default User Dashboard
          }
        }
      } else {
        // Handle errors
        console.error('Login failed:', data)
        alert(data.message || "Login failed. Please check your credentials.") // Or use a toast notification
        if (data.errors) {
          console.error("Validation errors:", data.errors)
          // You could display these errors to the user
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      alert("An unexpected error occurred. Please try again.") // Or use a toast notification
    } finally {
      setIsLoading(false)
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!registerData.firstName || !registerData.lastName || !registerData.email || !registerData.password) {
      alert("Please fill in all required fields")
      return
    }

    if (registerData.password !== registerData.confirmPassword) {
      alert("Passwords do not match")
      return
    }

    if (registerData.password.length < 8) {
      alert("Password must be at least 8 characters long")
      return
    }

    if (!acceptTerms) {
      alert("Please accept the Terms of Service and Privacy Policy")
      return
    }

    setIsLoading(true)

    try {
      console.log('Attempting registration for:', registerData.email)
      
             const userData = {
         first_name: registerData.firstName,
         last_name: registerData.lastName,
         email: registerData.email,
         password: registerData.password,
         password_confirmation: registerData.confirmPassword,
       }

      const data = await apiClient.register(userData)
      console.log('Registration response:', data)

      if (data.success) {
        alert("Registration successful! Please check your email for verification.")
        // Clear form
        setRegisterData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          confirmPassword: "",
        })
        setAcceptTerms(false)
        // Optionally redirect to login tab or dashboard
        // router.push("/auth/login")
      } else {
        console.error('Registration failed:', data)
        alert(data.message || "Registration failed. Please try again.")
        if (data.errors) {
          console.error("Validation errors:", data.errors)
          // Display specific field errors
          const errorMessages = Object.values(data.errors).flat().join('\n')
          alert(`Validation errors:\n${errorMessages}`)
        }
      }
    } catch (error) {
      console.error("Registration error:", error)
      alert("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleRegisterInputChange = (field: string, value: string) => {
    setRegisterData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Get app name and logo from settings or use defaults
  const appName = settings?.app_name || 'Laravel NGO Foundation'
  const appLogo = settings?.app_logo

  return (
    <div className="flex flex-col bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">

      <main className="flex-1 flex items-center justify-center py-12 px-4">
        <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Login Form */}
          <Card className="w-full max-w-md mx-auto shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95">
            <Tabs defaultValue="login" className="w-full">
              <TabsList className="grid w-full grid-cols-2 bg-green-100 dark:bg-green-900">
                <TabsTrigger value="login" className="rounded-xl">
                  Login
                </TabsTrigger>
                <TabsTrigger value="register" className="rounded-xl">
                  Register
                </TabsTrigger>
              </TabsList>

              <TabsContent value="login">
                <CardHeader className="text-center pb-6">
                  <div className="flex justify-center mb-4">
                    {appLogo ? (
                      <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float">
                        <img 
                          src={appLogo} 
                          alt={`${appName} Logo`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float">
                        <Heart className="absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md" />
                      </div>
                    )}
                  </div>
                  <CardTitle className="text-2xl font-bold text-green-800 dark:text-green-200">Welcome Back</CardTitle>
                  <CardDescription className="text-green-600 dark:text-green-400">
                    Sign in to continue your educational journey with {appName}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <form onSubmit={handleLogin} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-green-800 dark:text-green-200">
                        Email
                      </Label>
                      <div className="relative group">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200" />
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="password" className="text-green-800 dark:text-green-200">
                          Password
                        </Label>
                        <Link
                          href="#"
                          onClick={() => alert("Password reset instructions will be sent to your email")}
                          className="text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 hover:underline"
                        >
                          Forgot password?
                        </Link>
                      </div>
                      <div className="relative group">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600"
                          required
                        />
                        {isClient && (
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200"
                            suppressHydrationWarning
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
                          </button>
                        )}
                      </div>
                    </div>

                    {isClient && (
                      <div className="flex items-center space-x-2" suppressHydrationWarning>
                        <Checkbox
                          id="remember"
                          checked={rememberMe}
                          onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                          className="border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                          suppressHydrationWarning
                        />
                        <Label htmlFor="remember" className="text-sm font-normal text-green-700 dark:text-green-300">
                          Remember me for 30 days
                        </Label>
                      </div>
                    )}

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Signing in...
                        </div>
                      ) : (
                        "Sign In"
                      )}
                    </Button>
                  </form>

                  <div className="mt-6">
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-green-200 dark:border-green-700"></div>
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-white dark:bg-gray-900 px-2 text-green-600 dark:text-green-400">
                          Or continue with
                        </span>
                      </div>
                    </div>

                    {isClient && (
                      <div className="mt-4 grid grid-cols-2 gap-3" suppressHydrationWarning>
                        <Button
                          variant="outline"
                          className="rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105"
                          suppressHydrationWarning
                        >
                          <Facebook className="mr-2 h-4 w-4 text-blue-600" />
                          Facebook
                        </Button>
                        <Button
                          variant="outline"
                          className="rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105"
                          suppressHydrationWarning
                        >
                          <Twitter className="mr-2 h-4 w-4 text-blue-400" />
                          Twitter
                        </Button>
                      </div>
                    )}
                  </div>
                  {isClient && (
                    <div className="mt-6 text-center" suppressHydrationWarning>
                      <p className="text-sm text-green-600 dark:text-green-400 mb-3">Having trouble signing in?</p>
                      <div className="space-y-2">
                        <Button
                          variant="ghost"
                          className="w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                          onClick={() => alert("Password reset link will be sent to your email")}
                          suppressHydrationWarning
                        >
                          Reset Password
                        </Button>
                        <Button
                          variant="ghost"
                          className="w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                          onClick={() => alert("Please contact support for account recovery")}
                          suppressHydrationWarning
                        >
                          Account Recovery
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </TabsContent>

              <TabsContent value="register">
                <CardHeader className="text-center pb-6">
                  <div className="flex justify-center mb-4">
                    {appLogo ? (
                      <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float">
                        <img 
                          src={appLogo} 
                          alt={`${appName} Logo`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float">
                        <Heart className="absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md" />
                      </div>
                    )}
                  </div>
                  <CardTitle className="text-2xl font-bold text-green-800 dark:text-green-200">
                    Join {appName}
                  </CardTitle>
                  <CardDescription className="text-green-600 dark:text-green-400">
                    Create an account to support education across Nigeria, helping students, underprivileged people, and those in need.
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <form onSubmit={handleRegister} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="first-name" className="text-green-800 dark:text-green-200">
                          First Name *
                        </Label>
                        <Input
                          id="first-name"
                          placeholder="John"
                          value={registerData.firstName}
                          onChange={(e) => handleRegisterInputChange('firstName', e.target.value)}
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="last-name" className="text-green-800 dark:text-green-200">
                          Last Name *
                        </Label>
                        <Input
                          id="last-name"
                          placeholder="Doe"
                          value={registerData.lastName}
                          onChange={(e) => handleRegisterInputChange('lastName', e.target.value)}
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="register-email" className="text-green-800 dark:text-green-200">
                        Email *
                      </Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                        <Input
                          id="register-email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={registerData.email}
                          onChange={(e) => handleRegisterInputChange('email', e.target.value)}
                          className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="register-password" className="text-green-800 dark:text-green-200">
                        Password * (min. 8 characters)
                      </Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                        <Input
                          id="register-password"
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          value={registerData.password}
                          onChange={(e) => handleRegisterInputChange('password', e.target.value)}
                          className="pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200"
                          required
                          minLength={8}
                        />
                        {isClient && (
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200"
                            suppressHydrationWarning
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirm-password" className="text-green-800 dark:text-green-200">
                        Confirm Password *
                      </Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                        <Input
                          id="confirm-password"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="••••••••"
                          value={registerData.confirmPassword}
                          onChange={(e) => handleRegisterInputChange('confirmPassword', e.target.value)}
                          className="pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200"
                          required
                        />
                        {isClient && (
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200"
                            suppressHydrationWarning
                          >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            <span className="sr-only">{showConfirmPassword ? "Hide password" : "Show password"}</span>
                          </button>
                        )}
                      </div>
                    </div>

                    {isClient && (
                      <div className="flex items-center space-x-2" suppressHydrationWarning>
                        <Checkbox
                          id="terms"
                          checked={acceptTerms}
                          onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
                          className="border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                          suppressHydrationWarning
                          required
                        />
                        <Label htmlFor="terms" className="text-sm font-normal text-green-700 dark:text-green-300">
                          I agree to the{" "}
                          <Link
                            href="/terms"
                            className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline"
                          >
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link
                            href="/privacy"
                            className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline"
                          >
                            Privacy Policy
                          </Link>
                        </Label>
                      </div>
                    )}

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Creating Account...
                        </div>
                      ) : (
                        "Create Account"
                      )}
                    </Button>
                  </form>
                </CardContent>
              </TabsContent>
            </Tabs>

            <CardFooter className="flex justify-center pb-6">
              <Link
                href="/"
                className="flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group"
              >
                <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
                Back to home
              </Link>
            </CardFooter>
          </Card>

          {/* Right side - Branding and Information */}
          <div className="hidden lg:flex flex-col items-center justify-center space-y-8 text-center">
            {/* Animated Logo */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 to-green-700/30 rounded-full blur-3xl scale-150"></div>
              <div className="relative h-48 w-48 flex items-center justify-center">
                {appLogo ? (
                  <div className="relative h-32 w-32 overflow-hidden rounded-full shadow-2xl animate-float">
                    <img 
                      src={appLogo} 
                      alt={`${appName} Logo`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="relative h-32 w-32 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-2xl animate-float">
                    <Heart className="absolute inset-0 m-auto h-16 w-16 text-white drop-shadow-lg" />
                  </div>
                )}
              </div>
            </div>

            {/* Mission Statement */}
            <div className="max-w-md space-y-6">
              <h2 className="text-4xl font-bold text-green-800 dark:text-green-200">
                {appName}
              </h2>
              <p className="text-lg text-green-700 dark:text-green-300 leading-relaxed">
                {settings?.site_description || "Join our mission to empower Nigeria through education. By creating an account, you'll be able to track your donations, apply for scholarships, and stay updated on our impact as we support students, underprivileged people, and those in need across the nation."}
              </p>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 gap-4 pt-6">
                <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <GraduationCap className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-green-800 dark:text-green-200">Scholarship Applications</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Apply for educational grants</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <BookOpen className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-green-800 dark:text-green-200">Impact Tracking</div>
                    <div className="text-sm text-green-600 dark:text-green-400">See your donation impact</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-green-800 dark:text-green-200">Community Access</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Connect with beneficiaries</div>
                  </div>
                </div>
              </div>

              {/* Community Stats */}
              <div className="pt-6">
                <div className="flex -space-x-2 overflow-hidden justify-center mb-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="inline-block h-12 w-12 rounded-full border-2 border-white dark:border-gray-800 overflow-hidden shadow-lg"
                    >
                      <Image
                        src={`/placeholder.svg?height=100&width=100&text=User${i}`}
                        alt={`Community member ${i}`}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Join over <span className="font-bold text-green-700 dark:text-green-300">1,200+</span> students and
                  supporters
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
