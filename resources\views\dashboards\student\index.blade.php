@extends('layouts.app')

@section('title', 'Student Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                    ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                    ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                    ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                ]"
            />
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
        <!-- Welcome Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Welcome, {{ Auth::user()->first_name }}!</h1>
                    <p class="text-gray-600 mt-2">Manage your scholarship applications and track your progress.</p>
                    @if(Auth::user()->university_name)
                        <p class="text-sm text-indigo-600 mt-1">{{ Auth::user()->university_name }} - {{ Auth::user()->course_of_study }}</p>
                    @endif
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Student ID</p>
                    <p class="text-lg font-semibold text-indigo-600">{{ Auth::user()->student_id ?? 'Not Set' }}</p>
                </div>
            </div>
        </div>

        <!-- Application Status -->
        @php
            $activeApplication = Auth::user()->activeScholarshipApplication();
        @endphp

        @if($activeApplication)
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Your Scholarship Application</h2>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $activeApplication->scholarship->title ?? 'University Scholarship' }}</h3>
                        <p class="text-gray-600">Applied on: {{ $activeApplication->created_at->format('M d, Y') }}</p>
                    </div>
                    <div class="text-right">
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($activeApplication->status === 'approved') bg-green-100 text-green-800
                            @elseif($activeApplication->status === 'rejected') bg-red-100 text-red-800
                            @elseif($activeApplication->status === 'under_review') bg-yellow-100 text-yellow-800
                            @else bg-blue-100 text-blue-800
                            @endif">
                            {{ ucfirst(str_replace('_', ' ', $activeApplication->status)) }}
                        </span>
                    </div>
                </div>

                @if($activeApplication->status === 'approved')
                    <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                        </div>
                        <div class="mt-2">
                            <a href="#" class="text-green-600 hover:text-green-800 font-medium">Download Approval Letter (PDF) →</a>
                        </div>
                    </div>
                @elseif($activeApplication->status === 'rejected')
                    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span class="text-red-800 font-medium">Your application was not approved this time.</span>
                        </div>
                        @if($activeApplication->rejection_reason)
                            <p class="text-red-700 mt-2">{{ $activeApplication->rejection_reason }}</p>
                        @endif
                    </div>
                @endif
            </div>
        @else
            <!-- No Active Application -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Scholarship Application</h2>
                <div class="text-center py-8">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No Active Application</h3>
                    <p class="text-gray-600 mb-4">You haven't applied for any scholarships yet. University students can apply for one scholarship.</p>
                    <a href="{{ route('scholarships') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Browse Scholarships
                    </a>
                </div>
            </div>
        @endif

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <x-dashboard.stat-card
                    title="Available Scholarships"
                    value="{{ \App\Models\Scholarship::where('status', 'active')->where('category', 'university')->count() }}"
                    icon="graduation-cap"
                    color="blue"
                />

                <x-dashboard.stat-card
                    title="My Applications"
                    value="{{ Auth::user()->scholarshipApplications()->count() }}"
                    icon="file-alt"
                    color="green"
                />

                <x-dashboard.stat-card
                    title="Application Status"
                    value="{{ Auth::user()->scholarshipApplications()->latest()->first()->status ?? 'None' }}"
                    icon="chart-line"
                    color="purple"
                />

                <x-dashboard.stat-card
                    title="Profile Completion"
                    value="{{ Auth::user()->getProfileCompletionPercentage() }}%"
                    icon="user-check"
                    color="orange"
                />
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <x-dashboard.card
                    title="University Scholarships"
                    subtitle="Browse available scholarships"
                    icon="graduation-cap"
                    iconColor="blue"
                    :href="route('scholarships')"
                />

                <x-dashboard.card
                    title="Academic Profile"
                    subtitle="Update your information"
                    icon="user-graduate"
                    iconColor="green"
                    href="/student/profile"
                />

                <x-dashboard.card
                    title="Support Center"
                    subtitle="Get help and support"
                    icon="life-ring"
                    iconColor="purple"
                    :href="route('contact')"
                />
            </div>

            <!-- Student Information and Resources -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Academic Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Academic Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">University:</span>
                            <span class="font-medium">{{ Auth::user()->university_name ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Course of Study:</span>
                            <span class="font-medium">{{ Auth::user()->course_of_study ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Year of Study:</span>
                            <span class="font-medium">{{ Auth::user()->year_of_study ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Student ID:</span>
                            <span class="font-medium">{{ Auth::user()->student_id ?? 'Not specified' }}</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="/student/profile" class="text-indigo-600 hover:text-indigo-800 font-medium">Update Information →</a>
                    </div>
                </div>

                <!-- Application Guidelines -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Application Guidelines</h2>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">1</span>
                            </div>
                            <p class="text-gray-700">University students can apply for one scholarship at a time.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">2</span>
                            </div>
                            <p class="text-gray-700">Ensure all required documents are uploaded before submission.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">3</span>
                            </div>
                            <p class="text-gray-700">You can track your application status from this dashboard.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">4</span>
                            </div>
                            <p class="text-gray-700">Approved applications will generate a PDF approval letter.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Management -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Document Management</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-file-pdf text-red-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Academic Transcripts</h3>
                        <p class="text-sm text-gray-600 mt-1">Upload your official transcripts</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-id-card text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Identification</h3>
                        <p class="text-sm text-gray-600 mt-1">Student ID and personal documents</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-certificate text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Certificates</h3>
                        <p class="text-sm text-gray-600 mt-1">Awards and achievement certificates</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
