@extends('layouts.app')

@section('title', 'Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                    ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                    ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                    ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                ]"
            />
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
        <!-- Welcome Header -->
        <div class="relative bg-gradient-to-br from-green-600 to-green-800 text-white rounded-2xl shadow-xl p-8 mb-8 overflow-hidden">
            <div class="absolute inset-0 bg-black opacity-10"></div>
            <div class="relative z-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-4 lg:mb-0">
                        <h1 class="text-3xl lg:text-4xl font-bold mb-2">Welcome, {{ Auth::user()->first_name }}!</h1>
                        <p class="text-green-100 text-lg mb-2">Manage your scholarship applications and track your progress.</p>
                        @if(Auth::user()->university_name)
                            <p class="text-green-200 text-sm">{{ Auth::user()->university_name }} - {{ Auth::user()->course_of_study }}</p>
                        @endif
                    </div>
                    <div class="text-left lg:text-right">
                        <p class="text-green-200 text-sm">Student ID</p>
                        <p class="text-xl font-bold text-white">{{ Auth::user()->student_id ?? 'Not Set' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Status -->
        @php
            $activeApplication = Auth::user()->activeScholarshipApplication();
        @endphp

        @if($activeApplication)
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Your Scholarship Application</h2>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $activeApplication->scholarship->title ?? 'University Scholarship' }}</h3>
                        <p class="text-gray-600">Applied on: {{ $activeApplication->created_at->format('M d, Y') }}</p>
                    </div>
                    <div class="text-right">
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($activeApplication->status === 'approved') bg-green-100 text-green-800
                            @elseif($activeApplication->status === 'rejected') bg-red-100 text-red-800
                            @elseif($activeApplication->status === 'under_review') bg-yellow-100 text-yellow-800
                            @else bg-blue-100 text-blue-800
                            @endif">
                            {{ ucfirst(str_replace('_', ' ', $activeApplication->status)) }}
                        </span>
                    </div>
                </div>

                @if($activeApplication->status === 'approved')
                    <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                        </div>
                        <div class="mt-2">
                            <a href="#" class="text-green-600 hover:text-green-800 font-medium">Download Approval Letter (PDF) →</a>
                        </div>
                    </div>
                @elseif($activeApplication->status === 'rejected')
                    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span class="text-red-800 font-medium">Your application was not approved this time.</span>
                        </div>
                        @if($activeApplication->rejection_reason)
                            <p class="text-red-700 mt-2">{{ $activeApplication->rejection_reason }}</p>
                        @endif
                    </div>
                @endif
            </div>
        @else
            <!-- No Active Application -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Scholarship Application</h2>
                <div class="text-center py-8">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No Active Application</h3>
                    <p class="text-gray-600 mb-4">You haven't applied for any scholarships yet. University students can apply for one scholarship.</p>
                    <a href="{{ route('scholarships') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Browse Scholarships
                    </a>
                </div>
            </div>
        @endif

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Available Scholarships Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-graduation-cap text-green-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-green-600 mb-2">{{ \App\Models\Scholarship::where('status', 'active')->where('category', 'university')->count() }}</div>
                    <div class="text-sm text-gray-600">Available Scholarships</div>
                </div>

                <!-- My Applications Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-file-alt text-amber-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-amber-600 mb-2">{{ Auth::user()->scholarshipApplications()->count() }}</div>
                    <div class="text-sm text-gray-600">My Applications</div>
                </div>

                <!-- Application Status Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-blue-600"></i>
                    </div>
                    <div class="text-lg font-bold text-blue-600 mb-2">{{ ucfirst(str_replace('_', ' ', Auth::user()->scholarshipApplications()->latest()->first()->status ?? 'None')) }}</div>
                    <div class="text-sm text-gray-600">Application Status</div>
                </div>

                <!-- Profile Completion Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-user-check text-purple-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-purple-600 mb-2">{{ Auth::user()->getProfileCompletionPercentage() }}%</div>
                    <div class="text-sm text-gray-600">Profile Completion</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- University Scholarships Action -->
                <a href="{{ route('scholarships') }}" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-graduation-cap h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">University Scholarships</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Browse available scholarships for university students.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            Browse Scholarships
                        </div>
                    </div>
                </a>

                <!-- Academic Profile Action -->
                <a href="/student/profile" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-user-graduate h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">Academic Profile</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Update your academic information and credentials.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            Update Profile
                        </div>
                    </div>
                </a>

                <!-- Support Center Action -->
                <a href="{{ route('contact') }}" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-life-ring h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">Support Center</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Get help and support for your applications.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            Get Support
                        </div>
                    </div>
                </a>
            </div>

            <!-- Student Information and Resources -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Academic Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Academic Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">University:</span>
                            <span class="font-medium">{{ Auth::user()->university_name ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Course of Study:</span>
                            <span class="font-medium">{{ Auth::user()->course_of_study ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Year of Study:</span>
                            <span class="font-medium">{{ Auth::user()->year_of_study ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Student ID:</span>
                            <span class="font-medium">{{ Auth::user()->student_id ?? 'Not specified' }}</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="/student/profile" class="text-indigo-600 hover:text-indigo-800 font-medium">Update Information →</a>
                    </div>
                </div>

                <!-- Application Guidelines -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Application Guidelines</h2>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">1</span>
                            </div>
                            <p class="text-gray-700">University students can apply for one scholarship at a time.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">2</span>
                            </div>
                            <p class="text-gray-700">Ensure all required documents are uploaded before submission.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">3</span>
                            </div>
                            <p class="text-gray-700">You can track your application status from this dashboard.</p>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-blue-600 text-sm font-bold">4</span>
                            </div>
                            <p class="text-gray-700">Approved applications will generate a PDF approval letter.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Applications -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Applications</h2>
                @php
                    $recentApplications = Auth::user()->scholarshipApplications()->with('scholarship')->latest()->limit(5)->get();
                @endphp

                @if($recentApplications->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scholarship</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentApplications as $application)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $application->scholarship->title ?? 'N/A' }}</div>
                                        <div class="text-sm text-gray-500">{{ ucfirst($application->scholarship->category ?? 'N/A') }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($application->status === 'approved')
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle mr-1"></i> Approved
                                            </span>
                                        @elseif($application->status === 'rejected')
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                <i class="fas fa-times-circle mr-1"></i> Rejected
                                            </span>
                                        @elseif($application->status === 'pending')
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-clock mr-1"></i> Pending
                                            </span>
                                        @else
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                <i class="fas fa-question-circle mr-1"></i> {{ ucfirst($application->status) }}
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $application->submitted_at ? $application->submitted_at->format('M j, Y') : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        @if($application->status === 'approved')
                                            <a href="/api/v1/scholarship-applications/{{ $application->id }}/pdf"
                                               class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mr-2"
                                               target="_blank">
                                                <i class="fas fa-download mr-1"></i> Download PDF
                                            </a>
                                        @endif
                                        <a href="/student/applications/{{ $application->id }}"
                                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <a href="/student/applications" class="text-indigo-600 hover:text-indigo-800 font-medium">View All Applications →</a>
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500 mb-4">You haven't submitted any applications yet.</p>
                        <a href="/scholarships" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Browse Scholarships
                        </a>
                    </div>
                @endif
            </div>

            <!-- Document Management -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Document Management</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-file-pdf text-red-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Academic Transcripts</h3>
                        <p class="text-sm text-gray-600 mt-1">Upload your official transcripts</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-id-card text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Identification</h3>
                        <p class="text-sm text-gray-600 mt-1">Student ID and personal documents</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-certificate text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Certificates</h3>
                        <p class="text-sm text-gray-600 mt-1">Awards and achievement certificates</p>
                        <a href="/student/documents" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Manage Documents →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
