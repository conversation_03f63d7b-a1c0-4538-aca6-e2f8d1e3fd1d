@extends('layouts.app')

@section('title', 'Student Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Welcome Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Welcome, {{ Auth::user()->first_name }}!</h1>
                    <p class="text-gray-600 mt-2">Manage your scholarship applications and track your progress.</p>
                    @if(Auth::user()->university_name)
                        <p class="text-sm text-indigo-600 mt-1">{{ Auth::user()->university_name }} - {{ Auth::user()->course_of_study }}</p>
                    @endif
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Student ID</p>
                    <p class="text-lg font-semibold text-indigo-600">{{ Auth::user()->student_id ?? 'Not Set' }}</p>
                </div>
            </div>
        </div>

        <!-- Application Status -->
        @php
            $activeApplication = Auth::user()->activeScholarshipApplication();
        @endphp

        @if($activeApplication)
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Your Scholarship Application</h2>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $activeApplication->scholarship->title ?? 'University Scholarship' }}</h3>
                        <p class="text-gray-600">Applied on: {{ $activeApplication->created_at->format('M d, Y') }}</p>
                    </div>
                    <div class="text-right">
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($activeApplication->status === 'approved') bg-green-100 text-green-800
                            @elseif($activeApplication->status === 'rejected') bg-red-100 text-red-800
                            @elseif($activeApplication->status === 'under_review') bg-yellow-100 text-yellow-800
                            @else bg-blue-100 text-blue-800
                            @endif">
                            {{ ucfirst(str_replace('_', ' ', $activeApplication->status)) }}
                        </span>
                    </div>
                </div>

                @if($activeApplication->status === 'approved')
                    <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                        </div>
                        <div class="mt-2">
                            <a href="#" class="text-green-600 hover:text-green-800 font-medium">Download Approval Letter (PDF) →</a>
                        </div>
                    </div>
                @elseif($activeApplication->status === 'rejected')
                    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span class="text-red-800 font-medium">Your application was not approved this time.</span>
                        </div>
                        @if($activeApplication->rejection_reason)
                            <p class="text-red-700 mt-2">{{ $activeApplication->rejection_reason }}</p>
                        @endif
                    </div>
                @endif
            </div>
        @else
            <!-- No Active Application -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Scholarship Application</h2>
                <div class="text-center py-8">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No Active Application</h3>
                    <p class="text-gray-600 mb-4">You haven't applied for any scholarships yet. University students can apply for one scholarship.</p>
                    <a href="{{ route('scholarships') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Browse Scholarships
                    </a>
                </div>
            </div>
        @endif

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- View Scholarships -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">University Scholarships</h3>
                        <p class="text-sm text-gray-600">Browse available scholarships</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ route('scholarships') }}" class="text-blue-600 hover:text-blue-800 font-medium">View Scholarships →</a>
                </div>
            </div>

            <!-- Profile -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Profile</h3>
                        <p class="text-sm text-gray-600">Update your information</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-green-600 hover:text-green-800 font-medium">Edit Profile →</a>
                </div>
            </div>

            <!-- Support -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Support</h3>
                        <p class="text-sm text-gray-600">Get help and support</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ route('contact') }}" class="text-purple-600 hover:text-purple-800 font-medium">Contact Support →</a>
                </div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Academic Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Academic Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">University:</span>
                        <span class="font-medium">{{ Auth::user()->university_name ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Course of Study:</span>
                        <span class="font-medium">{{ Auth::user()->course_of_study ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Year of Study:</span>
                        <span class="font-medium">{{ Auth::user()->year_of_study ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Student ID:</span>
                        <span class="font-medium">{{ Auth::user()->student_id ?? 'Not specified' }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">Update Information →</a>
                </div>
            </div>

            <!-- Application Guidelines -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Application Guidelines</h2>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-blue-600 text-sm font-bold">1</span>
                        </div>
                        <p class="text-gray-700">University students can apply for one scholarship at a time.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-blue-600 text-sm font-bold">2</span>
                        </div>
                        <p class="text-gray-700">Ensure all required documents are uploaded before submission.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-blue-600 text-sm font-bold">3</span>
                        </div>
                        <p class="text-gray-700">You can track your application status from this dashboard.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span class="text-blue-600 text-sm font-bold">4</span>
                        </div>
                        <p class="text-gray-700">Approved applications will generate a PDF approval letter.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
