"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from './use-auth'

export type UserRole = 'admin' | 'partner_organization' | 'volunteer' | 'student' | 'user'
export type UserType = 'admin' | 'partner' | 'volunteer' | 'student' | 'user'

interface UseRoleGuardOptions {
  allowedRoles?: UserRole[]
  allowedTypes?: UserType[]
  redirectTo?: string
  requireAuth?: boolean
}

export const useRoleGuard = (options: UseRoleGuardOptions = {}) => {
  const {
    allowedRoles = [],
    allowedTypes = [],
    redirectTo = '/auth/login',
    requireAuth = true,
  } = options

  const router = useRouter()
  const { user, loading } = useAuth()

  const getUserType = (userData: any): UserType => {
    // Check user preferences first (from registration)
    if (userData?.preferences?.user_type) {
      return userData.preferences.user_type
    }
    
    // Fall back to role-based routing
    if (userData?.role === 'partner_organization') {
      return 'partner'
    } else if (userData?.role === 'volunteer') {
      return 'volunteer'
    } else if (userData?.role === 'admin') {
      return 'admin'
    } else if (userData?.role === 'student') {
      return 'student'
    } else {
      // For regular users, check if they have specific preferences
      // If user has partner_organization_id, they're linked to an institution
      if (userData?.partner_organization_id) {
        return 'partner'
      }
      return 'user'
    }
  }

  const hasAccess = (userData: any): boolean => {
    if (!userData && requireAuth) {
      return false
    }

    // If no restrictions specified, allow access
    if (allowedRoles.length === 0 && allowedTypes.length === 0) {
      return true
    }

    const userRole = userData?.role as UserRole
    const userType = getUserType(userData)

    // Check role-based access
    if (allowedRoles.length > 0 && !allowedRoles.includes(userRole)) {
      return false
    }

    // Check type-based access
    if (allowedTypes.length > 0 && !allowedTypes.includes(userType)) {
      return false
    }

    return true
  }

  useEffect(() => {
    if (loading) return

    const checkAccess = () => {
      if (requireAuth && !user) {
        router.push(redirectTo)
        return
      }

      if (!hasAccess(user)) {
        // Redirect to appropriate dashboard based on user type
        const userType = getUserType(user)
        switch (userType) {
          case 'partner':
            router.push('/dashboard')
            break
          case 'student':
            router.push('/dashboard')
            break
          case 'volunteer':
            router.push('/dashboard')
            break
          case 'admin':
            router.push('/dashboard')
            break
          default:
            router.push('/dashboard')
        }
      }
    }

    checkAccess()
  }, [user, loading, requireAuth, redirectTo, router, allowedRoles, allowedTypes])

  return {
    isAuthenticated: !!user,
    isLoading: loading,
    hasAccess: hasAccess(user),
    user,
    userType: getUserType(user),
    userRole: user?.role as UserRole,
  }
}

// Convenience hooks for specific roles
export const usePartnerGuard = (redirectTo?: string) => 
  useRoleGuard({ 
    allowedRoles: ['partner_organization'], 
    allowedTypes: ['partner'],
    redirectTo 
  })

export const useStudentGuard = (redirectTo?: string) => 
  useRoleGuard({ 
    allowedRoles: ['student', 'user'], 
    allowedTypes: ['student'],
    redirectTo 
  })

export const useVolunteerGuard = (redirectTo?: string) => 
  useRoleGuard({ 
    allowedRoles: ['volunteer'], 
    allowedTypes: ['volunteer'],
    redirectTo 
  })

export const useAdminGuard = (redirectTo?: string) => 
  useRoleGuard({ 
    allowedRoles: ['admin'], 
    allowedTypes: ['admin'],
    redirectTo 
  })
