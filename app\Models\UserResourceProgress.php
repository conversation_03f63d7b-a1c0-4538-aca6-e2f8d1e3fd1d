<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserResourceProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'resource_id',
        'progress_percentage',
        'time_spent',
        'is_completed',
        'last_accessed_at'
    ];

    protected $casts = [
        'progress_percentage' => 'decimal:2',
        'time_spent' => 'integer',
        'is_completed' => 'boolean',
        'last_accessed_at' => 'datetime'
    ];

    /**
     * Get the user that owns the progress
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the resource for this progress
     */
    public function resource()
    {
        return $this->belongsTo(EducationalResource::class, 'resource_id');
    }

    /**
     * Scope for completed progress
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope for in-progress items
     */
    public function scopeInProgress($query)
    {
        return $query->where('is_completed', false)
                    ->where('progress_percentage', '>', 0);
    }

    /**
     * Get formatted time spent
     */
    public function getFormattedTimeSpentAttribute()
    {
        if (!$this->time_spent) {
            return '0 minutes';
        }

        $hours = intval($this->time_spent / 60);
        $minutes = $this->time_spent % 60;

        if ($hours > 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . 
                   ($minutes > 0 ? ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '') : '');
        }

        return $minutes . ' minute' . ($minutes > 1 ? 's' : '');
    }

    /**
     * Mark as completed
     */
    public function markCompleted()
    {
        $this->update([
            'is_completed' => true,
            'progress_percentage' => 100,
            'last_accessed_at' => now()
        ]);
    }

    /**
     * Update progress
     */
    public function updateProgress($percentage, $timeSpent = null)
    {
        $data = [
            'progress_percentage' => min(100, max(0, $percentage)),
            'last_accessed_at' => now()
        ];

        if ($timeSpent !== null) {
            $data['time_spent'] = $this->time_spent + $timeSpent;
        }

        if ($percentage >= 100) {
            $data['is_completed'] = true;
        }

        $this->update($data);
    }
}
