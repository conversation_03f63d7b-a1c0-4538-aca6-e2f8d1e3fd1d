<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->boot();

echo "Checking user data...\n";
echo "====================\n\n";

// Check the specific user
$email = '<EMAIL>';
$user = App\Models\User::where('email', $email)->first();

if ($user) {
    echo "User found:\n";
    echo "ID: " . $user->id . "\n";
    echo "Email: " . $user->email . "\n";
    echo "Name: " . $user->first_name . " " . $user->last_name . "\n";
    echo "Role: " . $user->role . "\n";
    echo "Partner Org ID: " . $user->partner_organization_id . "\n";
    echo "Status: " . $user->status . "\n";
    echo "Created: " . $user->created_at . "\n";
    echo "\n";
} else {
    echo "User not found with email: $email\n\n";
}

// Check all partner organization users
echo "All partner organization users:\n";
echo "===============================\n";
$partnerUsers = App\Models\User::where('role', 'partner_organization')->get();

foreach ($partnerUsers as $user) {
    echo "ID: {$user->id}, Email: {$user->email}, Name: {$user->first_name} {$user->last_name}, Partner Org: {$user->partner_organization_id}\n";
}

echo "\nTotal partner users: " . $partnerUsers->count() . "\n";

// Test login credentials
echo "\nTesting login credentials...\n";
echo "============================\n";

$user = App\Models\User::where('email', $email)->first();
if ($user && Hash::check('password123', $user->password)) {
    echo "✓ Password is correct for user: {$user->email}\n";
    echo "✓ User role: {$user->role}\n";
    echo "✓ Partner organization ID: {$user->partner_organization_id}\n";
} else {
    echo "✗ Password is incorrect or user not found\n";
}
