'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BookOpen,
  GraduationCap,
  School,
  User,
  Phone,
  Mail,
  MapPin,
  Building,
  FileUp,
  Loader2,
  Upload,
  CheckCircle,
  AlertTriangle,
  Users
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'

// Form data interfaces for Primary and Secondary applications
interface PrimaryApplicationFormData {
  // Student Information
  student_full_name: string
  student_age: string
  student_gender: string
  student_grade: string
  student_date_of_birth: string
  
  // Parent/Guardian Information
  parent_name: string
  parent_phone: string
  parent_email: string
  parent_relationship: string
  home_address: string
  
  // School Information (filled by Partner Organization)
  school_account_number: string
  headmaster_name: string
  current_school_fee: string
  
  // Application Details
  reason_for_scholarship: string
  financial_need_explanation: string
  supporting_information: string
}

interface SecondaryApplicationFormData {
  // Student Information
  student_full_name: string
  student_age: string
  student_gender: string
  student_grade: string
  student_date_of_birth: string
  
  // Student Contact (for secondary students)
  student_phone: string
  student_email: string
  
  // Parent/Guardian Information
  parent_name: string
  parent_phone: string
  parent_email: string
  home_address: string
  
  // School Information
  principal_name: string
  principal_phone: string
  school_account_number: string
  current_school_fee: string
  
  // Academic Information
  current_subjects: string
  academic_performance: string
  career_aspirations: string
  
  // Application Details
  reason_for_scholarship: string
  financial_need_explanation: string
  extracurricular_activities: string
}

interface PartnerScholarshipApplicationFormProps {
  scholarshipId: string
  scholarshipTitle: string
  scholarshipCategory: 'primary' | 'secondary'
  onSuccess?: () => void
}

export default function PartnerScholarshipApplicationForm({
  scholarshipId,
  scholarshipTitle,
  scholarshipCategory,
  onSuccess
}: PartnerScholarshipApplicationFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<string>('')
  const [students, setStudents] = useState<any[]>([])
  const [isNewStudent, setIsNewStudent] = useState(false)
  
  const [primaryFormData, setPrimaryFormData] = useState<PrimaryApplicationFormData>({
    student_full_name: '',
    student_age: '',
    student_gender: '',
    student_grade: '',
    student_date_of_birth: '',
    parent_name: '',
    parent_phone: '',
    parent_email: '',
    parent_relationship: '',
    home_address: '',
    school_account_number: '',
    headmaster_name: '',
    current_school_fee: '',
    reason_for_scholarship: '',
    financial_need_explanation: '',
    supporting_information: ''
  })

  const [secondaryFormData, setSecondaryFormData] = useState<SecondaryApplicationFormData>({
    student_full_name: '',
    student_age: '',
    student_gender: '',
    student_grade: '',
    student_date_of_birth: '',
    student_phone: '',
    student_email: '',
    parent_name: '',
    parent_phone: '',
    parent_email: '',
    home_address: '',
    principal_name: '',
    principal_phone: '',
    school_account_number: '',
    current_school_fee: '',
    current_subjects: '',
    academic_performance: '',
    career_aspirations: '',
    reason_for_scholarship: '',
    financial_need_explanation: '',
    extracurricular_activities: ''
  })

  const [files, setFiles] = useState<{[key: string]: File | null}>({
    student_photo: null,
    birth_certificate: null,
    school_id_card: null,
    parent_id: null,
    academic_records: null,
    supporting_documents: null
  })

  // Load existing students for the Partner Organization
  useEffect(() => {
    const loadStudents = async () => {
      try {
        const response = await apiClient.get('/api/v1/partner-students')
        if (response.data.success) {
          setStudents(response.data.data.data || [])
        }
      } catch (error) {
        console.error('Failed to load students:', error)
      }
    }
    loadStudents()
  }, [])

  const handleInputChange = (field: string, value: string) => {
    if (scholarshipCategory === 'primary') {
      setPrimaryFormData(prev => ({ ...prev, [field]: value }))
    } else {
      setSecondaryFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleFileChange = (field: string, file: File | null) => {
    setFiles(prev => ({ ...prev, [field]: file }))
  }

  const handleStudentSelection = (studentId: string) => {
    if (studentId === 'new') {
      setIsNewStudent(true)
      setSelectedStudent('')
    } else {
      setIsNewStudent(false)
      setSelectedStudent(studentId)
      
      // Pre-fill form with selected student data
      const student = students.find(s => s.id.toString() === studentId)
      if (student) {
        const commonData = {
          student_full_name: student.full_name,
          student_age: student.age?.toString() || '',
          student_gender: student.gender || '',
          student_date_of_birth: student.date_of_birth || '',
          parent_name: student.parent_name || '',
          parent_phone: student.parent_phone || '',
          parent_email: student.parent_email || '',
          home_address: student.home_address || ''
        }
        
        if (scholarshipCategory === 'primary') {
          setPrimaryFormData(prev => ({
            ...prev,
            ...commonData,
            parent_relationship: 'parent',
            student_grade: student.progressions?.[0]?.grade_level || ''
          }))
        } else {
          setSecondaryFormData(prev => ({
            ...prev,
            ...commonData,
            student_grade: student.progressions?.[0]?.grade_level || ''
          }))
        }
      }
    }
  }

  const validateForm = (): boolean => {
    const currentFormData = scholarshipCategory === 'primary' ? primaryFormData : secondaryFormData
    
    // Basic validation
    if (!currentFormData.student_full_name || !currentFormData.student_age || 
        !currentFormData.parent_name || !currentFormData.parent_phone ||
        !currentFormData.reason_for_scholarship) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return false
    }

    // File validation
    if (!files.student_photo || !files.birth_certificate) {
      toast({
        title: "File Upload Required",
        description: "Please upload student photo and birth certificate",
        variant: "destructive",
      })
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsLoading(true)
    
    try {
      const formData = new FormData()
      const currentData = scholarshipCategory === 'primary' ? primaryFormData : secondaryFormData
      
      // Add form data
      Object.entries(currentData).forEach(([key, value]) => {
        formData.append(key, value)
      })
      
      // Add files
      Object.entries(files).forEach(([key, file]) => {
        if (file) {
          formData.append(key, file)
        }
      })
      
      // Add metadata
      formData.append('scholarship_category', scholarshipCategory)
      if (selectedStudent && !isNewStudent) {
        formData.append('existing_student_id', selectedStudent)
      }

      const response = await apiClient.post(`/api/v1/partner-scholarships/${scholarshipId}/apply`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      if (response.data.success) {
        toast({
          title: "Application Submitted Successfully",
          description: `${scholarshipCategory.charAt(0).toUpperCase() + scholarshipCategory.slice(1)} scholarship application has been submitted for review.`,
        })
        
        if (onSuccess) {
          onSuccess()
        } else {
          router.push('/dashboard/partner')
        }
      } else {
        throw new Error(response.data.message || 'Application submission failed')
      }
    } catch (error: any) {
      console.error('Application submission error:', error)
      toast({
        title: "Submission Failed",
        description: error.response?.data?.message || error.message || "Failed to submit application. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="text-2xl font-bold flex items-center gap-2">
              <School className="h-6 w-6" />
              {scholarshipCategory === 'primary' ? 'Primary' : 'Secondary'} Scholarship Application
            </CardTitle>
            <CardDescription className="text-blue-100">
              Apply for "{scholarshipTitle}" on behalf of your student
            </CardDescription>
          </CardHeader>

          <CardContent className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Student Selection Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Select Student
                </h3>

                <Select onValueChange={handleStudentSelection}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an existing student or add new" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">Add New Student</SelectItem>
                    {students.map((student) => (
                      <SelectItem key={student.id} value={student.id.toString()}>
                        {student.full_name} - {student.progressions?.[0]?.grade_level || 'No grade'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Render appropriate form based on category */}
              {scholarshipCategory === 'primary' ? (
                <PrimaryApplicationForm
                  formData={primaryFormData}
                  onInputChange={handleInputChange}
                  files={files}
                  onFileChange={handleFileChange}
                />
              ) : (
                <SecondaryApplicationForm
                  formData={secondaryFormData}
                  onInputChange={handleInputChange}
                  files={files}
                  onFileChange={handleFileChange}
                />
              )}

              {/* Submit Button */}
              <div className="flex justify-end pt-6">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 px-8 py-3 text-lg font-semibold"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Submitting Application...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-5 w-5" />
                      Submit Application
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Primary Application Form Component (Grades 1-6)
interface PrimaryApplicationFormProps {
  formData: PrimaryApplicationFormData
  onInputChange: (field: string, value: string) => void
  files: {[key: string]: File | null}
  onFileChange: (field: string, file: File | null) => void
}

function PrimaryApplicationForm({ formData, onInputChange, files, onFileChange }: PrimaryApplicationFormProps) {
  return (
    <div className="space-y-8">
      {/* Student Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <User className="h-5 w-5" />
          Student Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="student_full_name">Student Full Name *</Label>
            <Input
              id="student_full_name"
              value={formData.student_full_name}
              onChange={(e) => onInputChange('student_full_name', e.target.value)}
              placeholder="Enter student's full name"
              required
            />
          </div>

          <div>
            <Label htmlFor="student_age">Age *</Label>
            <Input
              id="student_age"
              type="number"
              min="5"
              max="15"
              value={formData.student_age}
              onChange={(e) => onInputChange('student_age', e.target.value)}
              placeholder="Student's age"
              required
            />
          </div>

          <div>
            <Label htmlFor="student_gender">Gender</Label>
            <Select onValueChange={(value) => onInputChange('student_gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="not_specified">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="student_grade">Current Grade *</Label>
            <Select onValueChange={(value) => onInputChange('student_grade', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select grade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="primary_1">Primary 1</SelectItem>
                <SelectItem value="primary_2">Primary 2</SelectItem>
                <SelectItem value="primary_3">Primary 3</SelectItem>
                <SelectItem value="primary_4">Primary 4</SelectItem>
                <SelectItem value="primary_5">Primary 5</SelectItem>
                <SelectItem value="primary_6">Primary 6</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="student_date_of_birth">Date of Birth *</Label>
            <Input
              id="student_date_of_birth"
              type="date"
              value={formData.student_date_of_birth}
              onChange={(e) => onInputChange('student_date_of_birth', e.target.value)}
              required
            />
          </div>
        </div>
      </div>

      {/* Parent/Guardian Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Users className="h-5 w-5" />
          Parent/Guardian Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="parent_name">Parent/Guardian Name *</Label>
            <Input
              id="parent_name"
              value={formData.parent_name}
              onChange={(e) => onInputChange('parent_name', e.target.value)}
              placeholder="Full name of parent/guardian"
              required
            />
          </div>

          <div>
            <Label htmlFor="parent_phone">Phone Number *</Label>
            <Input
              id="parent_phone"
              type="tel"
              value={formData.parent_phone}
              onChange={(e) => onInputChange('parent_phone', e.target.value)}
              placeholder="Parent/guardian phone number"
              required
            />
          </div>

          <div>
            <Label htmlFor="parent_email">Email Address</Label>
            <Input
              id="parent_email"
              type="email"
              value={formData.parent_email}
              onChange={(e) => onInputChange('parent_email', e.target.value)}
              placeholder="Parent/guardian email"
            />
          </div>

          <div>
            <Label htmlFor="parent_relationship">Relationship to Student</Label>
            <Select onValueChange={(value) => onInputChange('parent_relationship', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select relationship" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="father">Father</SelectItem>
                <SelectItem value="mother">Mother</SelectItem>
                <SelectItem value="guardian">Guardian</SelectItem>
                <SelectItem value="relative">Relative</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="home_address">Home Address</Label>
            <Textarea
              id="home_address"
              value={formData.home_address}
              onChange={(e) => onInputChange('home_address', e.target.value)}
              placeholder="Complete home address"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* School Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <School className="h-5 w-5" />
          School Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="headmaster_name">Headmaster/Principal Name *</Label>
            <Input
              id="headmaster_name"
              value={formData.headmaster_name}
              onChange={(e) => onInputChange('headmaster_name', e.target.value)}
              placeholder="Name of school head"
              required
            />
          </div>

          <div>
            <Label htmlFor="school_account_number">School Account Number *</Label>
            <Input
              id="school_account_number"
              value={formData.school_account_number}
              onChange={(e) => onInputChange('school_account_number', e.target.value)}
              placeholder="School bank account number"
              required
            />
          </div>

          <div>
            <Label htmlFor="current_school_fee">Current School Fee (₦)</Label>
            <Input
              id="current_school_fee"
              type="number"
              value={formData.current_school_fee}
              onChange={(e) => onInputChange('current_school_fee', e.target.value)}
              placeholder="Annual school fee amount"
            />
          </div>
        </div>
      </div>

      {/* Application Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <FileUp className="h-5 w-5" />
          Application Details
        </h3>

        <div className="space-y-4">
          <div>
            <Label htmlFor="reason_for_scholarship">Reason for Scholarship Application *</Label>
            <Textarea
              id="reason_for_scholarship"
              value={formData.reason_for_scholarship}
              onChange={(e) => onInputChange('reason_for_scholarship', e.target.value)}
              placeholder="Explain why this student needs the scholarship"
              rows={4}
              required
            />
          </div>

          <div>
            <Label htmlFor="financial_need_explanation">Financial Need Explanation</Label>
            <Textarea
              id="financial_need_explanation"
              value={formData.financial_need_explanation}
              onChange={(e) => onInputChange('financial_need_explanation', e.target.value)}
              placeholder="Describe the family's financial situation"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="supporting_information">Additional Supporting Information</Label>
            <Textarea
              id="supporting_information"
              value={formData.supporting_information}
              onChange={(e) => onInputChange('supporting_information', e.target.value)}
              placeholder="Any additional information that supports this application"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* File Uploads */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Required Documents
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="student_photo">Student Photo *</Label>
            <Input
              id="student_photo"
              type="file"
              accept="image/*"
              onChange={(e) => onFileChange('student_photo', e.target.files?.[0] || null)}
              required
            />
          </div>

          <div>
            <Label htmlFor="birth_certificate">Birth Certificate *</Label>
            <Input
              id="birth_certificate"
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => onFileChange('birth_certificate', e.target.files?.[0] || null)}
              required
            />
          </div>

          <div>
            <Label htmlFor="school_id_card">School ID Card</Label>
            <Input
              id="school_id_card"
              type="file"
              accept="image/*,.pdf"
              onChange={(e) => onFileChange('school_id_card', e.target.files?.[0] || null)}
            />
          </div>

          <div>
            <Label htmlFor="parent_id">Parent/Guardian ID</Label>
            <Input
              id="parent_id"
              type="file"
              accept="image/*,.pdf"
              onChange={(e) => onFileChange('parent_id', e.target.files?.[0] || null)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

// Secondary Application Form Component (Grades 7-12)
interface SecondaryApplicationFormProps {
  formData: SecondaryApplicationFormData
  onInputChange: (field: string, value: string) => void
  files: {[key: string]: File | null}
  onFileChange: (field: string, file: File | null) => void
}

function SecondaryApplicationForm({ formData, onInputChange, files, onFileChange }: SecondaryApplicationFormProps) {
  return (
    <div className="space-y-8">
      {/* Student Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <User className="h-5 w-5" />
          Student Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="student_full_name">Student Full Name *</Label>
            <Input
              id="student_full_name"
              value={formData.student_full_name}
              onChange={(e) => onInputChange('student_full_name', e.target.value)}
              placeholder="Enter student's full name"
              required
            />
          </div>

          <div>
            <Label htmlFor="student_age">Age *</Label>
            <Input
              id="student_age"
              type="number"
              min="10"
              max="20"
              value={formData.student_age}
              onChange={(e) => onInputChange('student_age', e.target.value)}
              placeholder="Student's age"
              required
            />
          </div>

          <div>
            <Label htmlFor="student_gender">Gender</Label>
            <Select onValueChange={(value) => onInputChange('student_gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="not_specified">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="student_grade">Current Grade *</Label>
            <Select onValueChange={(value) => onInputChange('student_grade', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select grade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="secondary_1">Secondary 1 (JSS 1)</SelectItem>
                <SelectItem value="secondary_2">Secondary 2 (JSS 2)</SelectItem>
                <SelectItem value="secondary_3">Secondary 3 (JSS 3)</SelectItem>
                <SelectItem value="secondary_4">Secondary 4 (SS 1)</SelectItem>
                <SelectItem value="secondary_5">Secondary 5 (SS 2)</SelectItem>
                <SelectItem value="secondary_6">Secondary 6 (SS 3)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="student_date_of_birth">Date of Birth *</Label>
            <Input
              id="student_date_of_birth"
              type="date"
              value={formData.student_date_of_birth}
              onChange={(e) => onInputChange('student_date_of_birth', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="student_phone">Student Phone Number</Label>
            <Input
              id="student_phone"
              type="tel"
              value={formData.student_phone}
              onChange={(e) => onInputChange('student_phone', e.target.value)}
              placeholder="Student's phone number (if available)"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="student_email">Student Email Address</Label>
            <Input
              id="student_email"
              type="email"
              value={formData.student_email}
              onChange={(e) => onInputChange('student_email', e.target.value)}
              placeholder="Student's email address (if available)"
            />
          </div>
        </div>
      </div>

      {/* Parent/Guardian Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Users className="h-5 w-5" />
          Parent/Guardian Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="parent_name">Parent/Guardian Name *</Label>
            <Input
              id="parent_name"
              value={formData.parent_name}
              onChange={(e) => onInputChange('parent_name', e.target.value)}
              placeholder="Full name of parent/guardian"
              required
            />
          </div>

          <div>
            <Label htmlFor="parent_phone">Parent Phone Number *</Label>
            <Input
              id="parent_phone"
              type="tel"
              value={formData.parent_phone}
              onChange={(e) => onInputChange('parent_phone', e.target.value)}
              placeholder="Parent/guardian phone number"
              required
            />
          </div>

          <div>
            <Label htmlFor="parent_email">Parent Email Address</Label>
            <Input
              id="parent_email"
              type="email"
              value={formData.parent_email}
              onChange={(e) => onInputChange('parent_email', e.target.value)}
              placeholder="Parent/guardian email"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="home_address">Home Address</Label>
            <Textarea
              id="home_address"
              value={formData.home_address}
              onChange={(e) => onInputChange('home_address', e.target.value)}
              placeholder="Complete home address"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* School Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <School className="h-5 w-5" />
          School Information
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="principal_name">Principal Name *</Label>
            <Input
              id="principal_name"
              value={formData.principal_name}
              onChange={(e) => onInputChange('principal_name', e.target.value)}
              placeholder="Name of school principal"
              required
            />
          </div>

          <div>
            <Label htmlFor="principal_phone">Principal Phone Number</Label>
            <Input
              id="principal_phone"
              type="tel"
              value={formData.principal_phone}
              onChange={(e) => onInputChange('principal_phone', e.target.value)}
              placeholder="Principal's phone number"
            />
          </div>

          <div>
            <Label htmlFor="school_account_number">School Account Number *</Label>
            <Input
              id="school_account_number"
              value={formData.school_account_number}
              onChange={(e) => onInputChange('school_account_number', e.target.value)}
              placeholder="School bank account number"
              required
            />
          </div>

          <div>
            <Label htmlFor="current_school_fee">Current School Fee (₦)</Label>
            <Input
              id="current_school_fee"
              type="number"
              value={formData.current_school_fee}
              onChange={(e) => onInputChange('current_school_fee', e.target.value)}
              placeholder="Annual school fee amount"
            />
          </div>
        </div>
      </div>

      {/* Academic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Academic Information
        </h3>

        <div className="space-y-4">
          <div>
            <Label htmlFor="current_subjects">Current Subjects</Label>
            <Textarea
              id="current_subjects"
              value={formData.current_subjects}
              onChange={(e) => onInputChange('current_subjects', e.target.value)}
              placeholder="List the subjects the student is currently taking"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="academic_performance">Academic Performance</Label>
            <Textarea
              id="academic_performance"
              value={formData.academic_performance}
              onChange={(e) => onInputChange('academic_performance', e.target.value)}
              placeholder="Describe the student's academic performance and achievements"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="career_aspirations">Career Aspirations</Label>
            <Textarea
              id="career_aspirations"
              value={formData.career_aspirations}
              onChange={(e) => onInputChange('career_aspirations', e.target.value)}
              placeholder="What does the student want to become in the future?"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Application Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <FileUp className="h-5 w-5" />
          Application Details
        </h3>

        <div className="space-y-4">
          <div>
            <Label htmlFor="reason_for_scholarship">Reason for Scholarship Application *</Label>
            <Textarea
              id="reason_for_scholarship"
              value={formData.reason_for_scholarship}
              onChange={(e) => onInputChange('reason_for_scholarship', e.target.value)}
              placeholder="Explain why this student needs the scholarship"
              rows={4}
              required
            />
          </div>

          <div>
            <Label htmlFor="financial_need_explanation">Financial Need Explanation</Label>
            <Textarea
              id="financial_need_explanation"
              value={formData.financial_need_explanation}
              onChange={(e) => onInputChange('financial_need_explanation', e.target.value)}
              placeholder="Describe the family's financial situation"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="extracurricular_activities">Extracurricular Activities</Label>
            <Textarea
              id="extracurricular_activities"
              value={formData.extracurricular_activities}
              onChange={(e) => onInputChange('extracurricular_activities', e.target.value)}
              placeholder="List any clubs, sports, or activities the student participates in"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* File Uploads */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Required Documents
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="student_photo">Student Photo *</Label>
            <Input
              id="student_photo"
              type="file"
              accept="image/*"
              onChange={(e) => onFileChange('student_photo', e.target.files?.[0] || null)}
              required
            />
          </div>

          <div>
            <Label htmlFor="birth_certificate">Birth Certificate *</Label>
            <Input
              id="birth_certificate"
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => onFileChange('birth_certificate', e.target.files?.[0] || null)}
              required
            />
          </div>

          <div>
            <Label htmlFor="academic_records">Academic Records/Report Cards</Label>
            <Input
              id="academic_records"
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => onFileChange('academic_records', e.target.files?.[0] || null)}
            />
          </div>

          <div>
            <Label htmlFor="school_id_card">School ID Card</Label>
            <Input
              id="school_id_card"
              type="file"
              accept="image/*,.pdf"
              onChange={(e) => onFileChange('school_id_card', e.target.files?.[0] || null)}
            />
          </div>

          <div>
            <Label htmlFor="parent_id">Parent/Guardian ID</Label>
            <Input
              id="parent_id"
              type="file"
              accept="image/*,.pdf"
              onChange={(e) => onFileChange('parent_id', e.target.files?.[0] || null)}
            />
          </div>

          <div>
            <Label htmlFor="supporting_documents">Additional Supporting Documents</Label>
            <Input
              id="supporting_documents"
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => onFileChange('supporting_documents', e.target.files?.[0] || null)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
