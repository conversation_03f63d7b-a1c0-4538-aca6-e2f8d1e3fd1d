'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Share2, 
  Download, 
  Upload, 
  FileText, 
  Image, 
  Video, 
  File, 
  Users, 
  Calendar,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface Resource {
  id: number;
  title: string;
  description: string;
  type: 'document' | 'image' | 'video' | 'template' | 'guide';
  category: string;
  file_url?: string;
  file_size?: number;
  shared_by: string;
  shared_with: string[];
  access_level: 'public' | 'partners' | 'private';
  downloads: number;
  created_at: string;
  updated_at: string;
}

interface ResourceFormData {
  title: string;
  description: string;
  type: string;
  category: string;
  access_level: string;
  shared_with: string[];
}

export default function ResourceSharing() {
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [formData, setFormData] = useState<ResourceFormData>({
    title: '',
    description: '',
    type: '',
    category: '',
    access_level: 'partners',
    shared_with: []
  });

  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    try {
      // This would be the actual API call
      // const response = await apiClient.get('/partner/resources');
      
      // Mock data for now
      const mockResources: Resource[] = [
        {
          id: 1,
          title: 'Partnership Agreement Template',
          description: 'Standard template for partnership agreements',
          type: 'document',
          category: 'Legal',
          file_url: '/documents/partnership-template.pdf',
          file_size: 245760,
          shared_by: 'HLTKKQ Foundation',
          shared_with: ['All Partners'],
          access_level: 'partners',
          downloads: 45,
          created_at: '2024-01-15',
          updated_at: '2024-01-15'
        },
        {
          id: 2,
          title: 'Program Implementation Guide',
          description: 'Comprehensive guide for implementing education programs',
          type: 'guide',
          category: 'Education',
          file_url: '/documents/implementation-guide.pdf',
          file_size: 1024000,
          shared_by: 'Education Partners NGO',
          shared_with: ['Education Partners'],
          access_level: 'partners',
          downloads: 23,
          created_at: '2024-02-01',
          updated_at: '2024-02-01'
        },
        {
          id: 3,
          title: 'Community Outreach Video',
          description: 'Best practices for community engagement',
          type: 'video',
          category: 'Training',
          file_url: '/videos/outreach-training.mp4',
          file_size: 52428800,
          shared_by: 'Community Development Center',
          shared_with: ['All Partners'],
          access_level: 'public',
          downloads: 67,
          created_at: '2024-01-20',
          updated_at: '2024-01-20'
        },
        {
          id: 4,
          title: 'Budget Planning Spreadsheet',
          description: 'Template for project budget planning and tracking',
          type: 'template',
          category: 'Finance',
          file_url: '/templates/budget-template.xlsx',
          file_size: 87040,
          shared_by: 'Financial Management Partners',
          shared_with: ['Finance Team'],
          access_level: 'private',
          downloads: 12,
          created_at: '2024-02-10',
          updated_at: '2024-02-10'
        }
      ];
      
      setResources(mockResources);
    } catch (error) {
      console.error('Error fetching resources:', error);
      toast.error('Failed to load resources');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateResource = async () => {
    try {
      // This would be the actual API call
      // const response = await apiClient.post('/partner/resources', formData);
      
      // Mock creation
      const newResource: Resource = {
        id: Date.now(),
        ...formData,
        shared_by: 'Current Organization',
        downloads: 0,
        created_at: new Date().toISOString().split('T')[0],
        updated_at: new Date().toISOString().split('T')[0]
      };
      
      setResources(prev => [...prev, newResource]);
      setIsCreateDialogOpen(false);
      resetForm();
      toast.success('Resource shared successfully');
    } catch (error) {
      console.error('Error creating resource:', error);
      toast.error('Failed to share resource');
    }
  };

  const handleDownloadResource = async (resourceId: number) => {
    try {
      // This would be the actual download logic
      // const response = await apiClient.get(`/partner/resources/${resourceId}/download`);
      
      // Mock download
      setResources(prev => prev.map(r => 
        r.id === resourceId 
          ? { ...r, downloads: r.downloads + 1 }
          : r
      ));
      toast.success('Resource downloaded');
    } catch (error) {
      console.error('Error downloading resource:', error);
      toast.error('Failed to download resource');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: '',
      category: '',
      access_level: 'partners',
      shared_with: []
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="h-5 w-5 text-blue-600" />;
      case 'image':
        return <Image className="h-5 w-5 text-green-600" />;
      case 'video':
        return <Video className="h-5 w-5 text-purple-600" />;
      case 'template':
        return <File className="h-5 w-5 text-orange-600" />;
      case 'guide':
        return <FileText className="h-5 w-5 text-indigo-600" />;
      default:
        return <File className="h-5 w-5 text-gray-600" />;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'public':
        return 'bg-green-100 text-green-800';
      case 'partners':
        return 'bg-blue-100 text-blue-800';
      case 'private':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || resource.category === filterCategory;
    const matchesType = filterType === 'all' || resource.type === filterType;
    
    return matchesSearch && matchesCategory && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Resource Sharing</h2>
          <p className="text-gray-600">Share and access partnership resources</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Share Resource
        </Button>
      </div>

      {/* Resource Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Resources</p>
                <p className="text-2xl font-bold">{resources.length}</p>
              </div>
              <Share2 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Downloads</p>
                <p className="text-2xl font-bold">
                  {resources.reduce((sum, r) => sum + r.downloads, 0)}
                </p>
              </div>
              <Download className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Public Resources</p>
                <p className="text-2xl font-bold">
                  {resources.filter(r => r.access_level === 'public').length}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold">
                  {new Set(resources.map(r => r.category)).size}
                </p>
              </div>
              <FileText className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <Input
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Legal">Legal</SelectItem>
                <SelectItem value="Education">Education</SelectItem>
                <SelectItem value="Training">Training</SelectItem>
                <SelectItem value="Finance">Finance</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="document">Documents</SelectItem>
                <SelectItem value="template">Templates</SelectItem>
                <SelectItem value="guide">Guides</SelectItem>
                <SelectItem value="video">Videos</SelectItem>
                <SelectItem value="image">Images</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Resources Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map((resource) => (
          <Card key={resource.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  {getTypeIcon(resource.type)}
                  <div>
                    <CardTitle className="text-lg">{resource.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {resource.category} • {resource.shared_by}
                    </CardDescription>
                  </div>
                </div>
                <Badge className={getAccessLevelColor(resource.access_level)}>
                  {resource.access_level}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {resource.description}
              </p>

              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>{formatFileSize(resource.file_size || 0)}</span>
                <span>{resource.downloads} downloads</span>
                <span>{new Date(resource.created_at).toLocaleDateString()}</span>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => setSelectedResource(resource)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Button>
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => handleDownloadResource(resource.id)}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredResources.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No resources found</h3>
            <p className="text-gray-600">
              {searchTerm || filterCategory !== 'all' || filterType !== 'all'
                ? 'Try adjusting your search or filters'
                : 'No resources have been shared yet'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Create Resource Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Share New Resource</DialogTitle>
            <DialogDescription>
              Share a resource with other partners
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Resource Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter resource title"
                />
              </div>
              <div>
                <Label htmlFor="type">Resource Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="document">Document</SelectItem>
                    <SelectItem value="template">Template</SelectItem>
                    <SelectItem value="guide">Guide</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                    <SelectItem value="image">Image</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Legal">Legal</SelectItem>
                    <SelectItem value="Education">Education</SelectItem>
                    <SelectItem value="Training">Training</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Healthcare">Healthcare</SelectItem>
                    <SelectItem value="Community">Community</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="access_level">Access Level</Label>
                <Select
                  value={formData.access_level}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, access_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select access level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="partners">Partners Only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the resource"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="file">Upload File</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  PDF, DOC, XLS, PPT, MP4, JPG, PNG (max 50MB)
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateResource}>
                Share Resource
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* View Resource Dialog */}
      {selectedResource && (
        <Dialog open={!!selectedResource} onOpenChange={() => setSelectedResource(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                {getTypeIcon(selectedResource.type)}
                <span>{selectedResource.title}</span>
              </DialogTitle>
              <DialogDescription>
                {selectedResource.category} • Shared by {selectedResource.shared_by}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Type</Label>
                  <p className="text-sm capitalize">{selectedResource.type}</p>
                </div>
                <div>
                  <Label>Access Level</Label>
                  <Badge className={getAccessLevelColor(selectedResource.access_level)}>
                    {selectedResource.access_level}
                  </Badge>
                </div>
                <div>
                  <Label>File Size</Label>
                  <p className="text-sm">{formatFileSize(selectedResource.file_size || 0)}</p>
                </div>
                <div>
                  <Label>Downloads</Label>
                  <p className="text-sm">{selectedResource.downloads}</p>
                </div>
                <div>
                  <Label>Created</Label>
                  <p className="text-sm">{new Date(selectedResource.created_at).toLocaleDateString()}</p>
                </div>
                <div>
                  <Label>Last Updated</Label>
                  <p className="text-sm">{new Date(selectedResource.updated_at).toLocaleDateString()}</p>
                </div>
              </div>

              <div>
                <Label>Description</Label>
                <p className="text-sm bg-gray-50 p-3 rounded-lg mt-1">
                  {selectedResource.description}
                </p>
              </div>

              <div>
                <Label>Shared With</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedResource.shared_with.map((group, index) => (
                    <Badge key={index} variant="outline">
                      {group}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setSelectedResource(null)}>
                  Close
                </Button>
                <Button onClick={() => handleDownloadResource(selectedResource.id)}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
