<?php

// Comprehensive NGO Admin Dashboard Testing Script
$baseUrl = 'http://localhost:8000/api/v1';
$adminBaseUrl = 'http://localhost:8000/admin';

// Test results storage
$results = [
    'api_tests' => [],
    'admin_panel_tests' => [],
    'summary' => []
];

// Get admin token
function getAdminToken($baseUrl) {
    $loginData = [
        'email' => '<EMAIL>',
        'password' => 'password'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        $data = json_decode($response, true);
        return $data['data']['access_token'] ?? null;
    }
    return null;
}

// Test API endpoint
function testApiEndpoint($url, $token = null, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $headers = ['Content-Type: application/json', 'Accept: application/json'];
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'success' => $httpCode >= 200 && $httpCode < 300
    ];
}

// Test admin panel page
function testAdminPage($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'success' => $httpCode >= 200 && $httpCode < 300 && !preg_match('/\b(fatal error|exception|parse error|syntax error)\b/i', $response)
    ];
}

echo "🚀 Starting Comprehensive NGO Admin Dashboard Testing...\n\n";

// Get admin token
echo "🔐 Getting admin authentication token...\n";
$token = getAdminToken($baseUrl);
if (!$token) {
    echo "❌ Failed to get admin token. Exiting.\n";
    exit(1);
}
echo "✅ Admin token obtained successfully!\n\n";

// Define modules to test
$modules = [
    'Dashboard' => [
        'api_endpoints' => [
            '/admin/dashboard',
            '/admin/dashboard/analytics',
            '/admin/dashboard/stats'
        ],
        'admin_pages' => [
            '/dashboard'
        ]
    ],
    'Users' => [
        'api_endpoints' => [
            '/admin/users',
            '/admin/users/export'
        ],
        'admin_pages' => [
            '/users',
            '/users/create'
        ]
    ],
    'Programs' => [
        'api_endpoints' => [
            '/admin/programs'
        ],
        'admin_pages' => [
            '/programs',
            '/programs/create'
        ]
    ],
    'Events' => [
        'api_endpoints' => [
            '/admin/events',
            '/admin/events/statistics'
        ],
        'admin_pages' => [
            '/events',
            '/events/create'
        ]
    ],
    'Volunteers' => [
        'api_endpoints' => [
            '/admin/volunteers'
        ],
        'admin_pages' => [
            '/volunteers',
            '/volunteers/create',
            '/volunteers/opportunities'
        ]
    ],
    'Donations' => [
        'api_endpoints' => [
            '/admin/donations'
        ],
        'admin_pages' => [
            '/donations',
            '/donations/create'
        ]
    ],
    'Blog' => [
        'api_endpoints' => [
            '/admin/blog',
            '/admin/blog/posts'
        ],
        'admin_pages' => [
            '/blog',
            '/blog/posts/create'
        ]
    ],
    'Newsletter' => [
        'api_endpoints' => [
            '/admin/newsletter/subscribers'
        ],
        'admin_pages' => [
            '/newsletter'
        ]
    ],
    'Contact' => [
        'api_endpoints' => [
            '/admin/contact-messages'
        ],
        'admin_pages' => [
            '/contact'
        ]
    ],
    'Scholarships' => [
        'api_endpoints' => [
            '/admin/scholarships',
            '/admin/scholarship-applications'
        ],
        'admin_pages' => [
            '/scholarships',
            '/scholarships/create'
        ]
    ],
    'Team' => [
        'api_endpoints' => [
            '/admin/team'
        ],
        'admin_pages' => [
            '/team',
            '/team/create'
        ]
    ],
    'Analytics' => [
        'api_endpoints' => [
            '/admin/dashboard/analytics'
        ],
        'admin_pages' => [
            '/analytics'
        ]
    ],
    'Settings' => [
        'api_endpoints' => [],
        'admin_pages' => [
            '/settings'
        ]
    ]
];

// Test each module
foreach ($modules as $moduleName => $moduleConfig) {
    echo "📋 Testing Module: $moduleName\n";
    echo str_repeat("-", 50) . "\n";
    
    $moduleResults = [
        'api_success' => 0,
        'api_total' => 0,
        'admin_success' => 0,
        'admin_total' => 0,
        'issues' => []
    ];
    
    // Test API endpoints
    if (!empty($moduleConfig['api_endpoints'])) {
        echo "🔌 Testing API Endpoints:\n";
        foreach ($moduleConfig['api_endpoints'] as $endpoint) {
            $moduleResults['api_total']++;
            $result = testApiEndpoint($baseUrl . $endpoint, $token);
            
            if ($result['success']) {
                echo "  ✅ $endpoint - Status: {$result['status_code']}\n";
                $moduleResults['api_success']++;
            } else {
                echo "  ❌ $endpoint - Status: {$result['status_code']}\n";
                $moduleResults['issues'][] = "API $endpoint failed with status {$result['status_code']}";
            }
        }
    }
    
    // Test admin panel pages
    if (!empty($moduleConfig['admin_pages'])) {
        echo "🖥️  Testing Admin Panel Pages:\n";
        foreach ($moduleConfig['admin_pages'] as $page) {
            $moduleResults['admin_total']++;
            $result = testAdminPage($adminBaseUrl . $page);
            
            if ($result['success']) {
                echo "  ✅ $page - Status: {$result['status_code']}\n";
                $moduleResults['admin_success']++;
            } else {
                echo "  ❌ $page - Status: {$result['status_code']}\n";
                $moduleResults['issues'][] = "Admin page $page failed with status {$result['status_code']}";
            }
        }
    }
    
    $results['api_tests'][$moduleName] = $moduleResults;
    echo "\n";
}

// Generate summary
echo "📊 TESTING SUMMARY\n";
echo str_repeat("=", 60) . "\n";

$totalApiSuccess = 0;
$totalApiTests = 0;
$totalAdminSuccess = 0;
$totalAdminTests = 0;
$allIssues = [];

foreach ($results['api_tests'] as $module => $result) {
    $totalApiSuccess += $result['api_success'];
    $totalApiTests += $result['api_total'];
    $totalAdminSuccess += $result['admin_success'];
    $totalAdminTests += $result['admin_total'];
    $allIssues = array_merge($allIssues, $result['issues']);
    
    $apiRate = $result['api_total'] > 0 ? round(($result['api_success'] / $result['api_total']) * 100, 1) : 100;
    $adminRate = $result['admin_total'] > 0 ? round(($result['admin_success'] / $result['admin_total']) * 100, 1) : 100;
    
    echo sprintf("%-15s API: %d/%d (%s%%) | Admin: %d/%d (%s%%)\n", 
        $module, 
        $result['api_success'], $result['api_total'], $apiRate,
        $result['admin_success'], $result['admin_total'], $adminRate
    );
}

echo str_repeat("-", 60) . "\n";
$overallApiRate = $totalApiTests > 0 ? round(($totalApiSuccess / $totalApiTests) * 100, 1) : 100;
$overallAdminRate = $totalAdminTests > 0 ? round(($totalAdminSuccess / $totalAdminTests) * 100, 1) : 100;

echo sprintf("%-15s API: %d/%d (%s%%) | Admin: %d/%d (%s%%)\n", 
    "OVERALL", 
    $totalApiSuccess, $totalApiTests, $overallApiRate,
    $totalAdminSuccess, $totalAdminTests, $overallAdminRate
);

if (!empty($allIssues)) {
    echo "\n🚨 ISSUES FOUND:\n";
    foreach ($allIssues as $issue) {
        echo "  • $issue\n";
    }
} else {
    echo "\n🎉 ALL TESTS PASSED! No issues found.\n";
}

echo "\n✅ Testing completed!\n";
