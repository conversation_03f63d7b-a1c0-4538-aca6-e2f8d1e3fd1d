<?php

namespace App\Policies;

use App\Models\User;
use App\Models\PartnerOrganization;
use App\Models\Student;
use Illuminate\Auth\Access\HandlesAuthorization;

class PartnerOrganizationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any partner organizations.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can view the partner organization.
     */
    public function view(User $user, PartnerOrganization $partnerOrganization): bool
    {
        // Admins can view all partner organizations
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can only view their own data
        if ($user->role === 'partner_organization') {
            return $user->partner_organization_id === $partnerOrganization->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create partner organizations.
     */
    public function create(User $user): bool
    {
        // Only admins can create partner organizations
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the partner organization.
     */
    public function update(User $user, PartnerOrganization $partnerOrganization): bool
    {
        // Admins can update all partner organizations
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can update their own data
        if ($user->role === 'partner_organization') {
            return $user->partner_organization_id === $partnerOrganization->id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the partner organization.
     */
    public function delete(User $user, PartnerOrganization $partnerOrganization): bool
    {
        // Only admins can delete partner organizations
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can manage students for the partner organization.
     */
    public function manageStudents(User $user, PartnerOrganization $partnerOrganization): bool
    {
        // Admins can manage students for any partner organization
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can manage their own students
        if ($user->role === 'partner_organization') {
            return $user->partner_organization_id === $partnerOrganization->id;
        }

        return false;
    }

    /**
     * Determine whether the user can view dashboard data for the partner organization.
     */
    public function viewDashboard(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->view($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can view statistics for the partner organization.
     */
    public function viewStatistics(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->view($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can manage scholarship applications for the partner organization.
     */
    public function manageApplications(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->manageStudents($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can view progression analytics for the partner organization.
     */
    public function viewProgressionAnalytics(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->view($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can export data for the partner organization.
     */
    public function exportData(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->view($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can perform bulk operations for the partner organization.
     */
    public function bulkOperations(User $user, PartnerOrganization $partnerOrganization): bool
    {
        // Admins can perform bulk operations for any partner organization
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can perform bulk operations for their own data
        if ($user->role === 'partner_organization') {
            return $user->partner_organization_id === $partnerOrganization->id;
        }

        return false;
    }

    /**
     * Determine whether the user can access a specific student's data.
     */
    public function accessStudentData(User $user, Student $student): bool
    {
        // Admins can access any student's data
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can access their own students' data
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can only access their own data
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create students for the partner organization.
     */
    public function createStudent(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->manageStudents($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can update student information.
     */
    public function updateStudent(User $user, Student $student): bool
    {
        return $this->accessStudentData($user, $student);
    }

    /**
     * Determine whether the user can delete students.
     */
    public function deleteStudent(User $user, Student $student): bool
    {
        // Only admins can delete students
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can delete their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        return false;
    }

    /**
     * Determine whether the user can view reports for the partner organization.
     */
    public function viewReports(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->view($user, $partnerOrganization);
    }

    /**
     * Determine whether the user can manage institutional settings.
     */
    public function manageSettings(User $user, PartnerOrganization $partnerOrganization): bool
    {
        return $this->update($user, $partnerOrganization);
    }
}
