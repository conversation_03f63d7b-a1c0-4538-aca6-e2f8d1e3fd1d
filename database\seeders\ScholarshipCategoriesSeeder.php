<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ScholarshipCategory;
use App\Models\ScholarshipTemplate;
use App\Models\User;

class ScholarshipCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create scholarship categories
        $categories = [
            [
                'name' => 'Primary School Scholarship',
                'slug' => 'primary',
                'description' => 'Scholarships for primary school students (ages 6-12). Applications are typically filled by parents or guardians.',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'student_class', 'field_label' => 'Student Class', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_name', 'field_label' => 'Parent/Guardian Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_phone', 'field_label' => 'Parent/Guardian Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'headmaster_name', 'field_label' => 'Headmaster Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'student_photo', 'field_label' => 'Student Photo', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'school_fee', 'field_label' => 'School Fee Amount', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true],
                ],
                'validation_rules' => [
                    'student_age' => ['min:5', 'max:13'],
                    'school_fee' => ['min:0'],
                    'student_photo' => ['mimes:jpeg,png,jpg', 'max:2048'],
                ],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Secondary School Scholarship',
                'slug' => 'secondary',
                'description' => 'Scholarships for secondary school students (ages 13-18). Applications are filled by students with parental consent.',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'student_class', 'field_label' => 'Student Class', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_name', 'field_label' => 'Parent/Guardian Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_phone', 'field_label' => 'Parent/Guardian Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'principal_name', 'field_label' => 'Principal/Financial Officer Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'student_photo', 'field_label' => 'Student Photo', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'school_fee', 'field_label' => 'School Fee Amount', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true],
                ],
                'validation_rules' => [
                    'student_age' => ['min:12', 'max:20'],
                    'school_fee' => ['min:0'],
                    'student_photo' => ['mimes:jpeg,png,jpg', 'max:2048'],
                ],
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'University Scholarship',
                'slug' => 'university',
                'description' => 'Scholarships for university students (ages 18+). Applications are filled by students themselves.',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'matric_number', 'field_label' => 'Matriculation Number', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_email', 'field_label' => 'Student Email', 'field_type' => 'email', 'is_required' => true],
                    ['field_name' => 'student_phone', 'field_label' => 'Student Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'student_id_card', 'field_label' => 'Student ID Card', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'remita_payment', 'field_label' => 'Remita Payment Receipt', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'supporting_docs', 'field_label' => 'Supporting Documents', 'field_type' => 'file', 'is_required' => false],
                ],
                'validation_rules' => [
                    'student_age' => ['min:16', 'max:35'],
                    'student_email' => ['email'],
                    'student_id_card' => ['mimes:jpeg,png,jpg,pdf', 'max:2048'],
                    'remita_payment' => ['mimes:jpeg,png,jpg,pdf', 'max:2048'],
                    'supporting_docs' => ['mimes:jpeg,png,jpg,pdf', 'max:5120'],
                ],
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($categories as $categoryData) {
            ScholarshipCategory::updateOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // Create default templates
        $adminUser = User::where('role', 'admin')->first();
        if (!$adminUser) {
            $adminUser = User::first(); // Fallback to first user
        }

        if ($adminUser) {
            $templates = [
                [
                    'name' => 'Standard Primary School Application',
                    'category' => 'primary',
                    'description' => 'Standard template for primary school scholarship applications with all required fields',
                    'default_fields' => $categories[0]['default_fields'],
                    'validation_rules' => $categories[0]['validation_rules'],
                    'settings' => [
                        'allow_multiple_files' => false,
                        'max_file_size' => 2048,
                        'allowed_file_types' => ['jpeg', 'png', 'jpg'],
                    ],
                    'is_active' => true,
                    'created_by' => $adminUser->id,
                ],
                [
                    'name' => 'Standard Secondary School Application',
                    'category' => 'secondary',
                    'description' => 'Standard template for secondary school scholarship applications with all required fields',
                    'default_fields' => $categories[1]['default_fields'],
                    'validation_rules' => $categories[1]['validation_rules'],
                    'settings' => [
                        'allow_multiple_files' => false,
                        'max_file_size' => 2048,
                        'allowed_file_types' => ['jpeg', 'png', 'jpg'],
                    ],
                    'is_active' => true,
                    'created_by' => $adminUser->id,
                ],
                [
                    'name' => 'Standard University Application',
                    'category' => 'university',
                    'description' => 'Standard template for university scholarship applications with all required fields',
                    'default_fields' => $categories[2]['default_fields'],
                    'validation_rules' => $categories[2]['validation_rules'],
                    'settings' => [
                        'allow_multiple_files' => true,
                        'max_file_size' => 5120,
                        'allowed_file_types' => ['jpeg', 'png', 'jpg', 'pdf'],
                    ],
                    'is_active' => true,
                    'created_by' => $adminUser->id,
                ],
            ];

            foreach ($templates as $templateData) {
                ScholarshipTemplate::updateOrCreate(
                    ['name' => $templateData['name']],
                    $templateData
                );
            }
        }

        $this->command->info('Scholarship categories and templates seeded successfully!');
    }
}
