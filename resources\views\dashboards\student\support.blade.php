@extends('layouts.dashboard')

@section('title', 'Support & Settings - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/support')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/support')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Support & Settings</h1>
                            <p class="text-green-100 text-sm lg:text-base">Get help and manage your account settings</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-user-graduate mr-2"></i>
                                Edit Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support & Settings Content -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Support Section -->
                        <div class="space-y-6">
                            <!-- Contact Support -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                                <div class="p-6 border-b border-gray-100">
                                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i class="fas fa-life-ring text-green-600 mr-2"></i>
                                        Contact Support
                                    </h2>
                                    <p class="text-gray-600 text-sm mt-1">Need help? Get in touch with our support team</p>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <div class="flex items-center p-4 bg-green-50 rounded-lg">
                                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                                <i class="fas fa-envelope text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">Email Support</p>
                                                <p class="text-sm text-gray-600">{{ $settings['support_email'] ?? '<EMAIL>' }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                                <i class="fas fa-phone text-blue-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">Phone Support</p>
                                                <p class="text-sm text-gray-600">{{ $settings['support_phone'] ?? '+234 ************' }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center p-4 bg-purple-50 rounded-lg">
                                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                                <i class="fas fa-clock text-purple-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">Support Hours</p>
                                                <p class="text-sm text-gray-600">Monday - Friday, 9:00 AM - 5:00 PM</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- FAQ Section -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                                <div class="p-6 border-b border-gray-100">
                                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i class="fas fa-question-circle text-green-600 mr-2"></i>
                                        Frequently Asked Questions
                                    </h2>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <div class="border border-gray-200 rounded-lg">
                                            <button onclick="toggleFaq(1)" class="w-full text-left p-4 hover:bg-gray-50 transition-colors duration-200">
                                                <div class="flex items-center justify-between">
                                                    <span class="font-medium text-gray-900">How do I apply for scholarships?</span>
                                                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform duration-200" id="faq-icon-1"></i>
                                                </div>
                                            </button>
                                            <div id="faq-content-1" class="hidden px-4 pb-4">
                                                <p class="text-gray-600 text-sm">Browse available scholarships in the "Available Scholarships" section, click on a scholarship to view details, then click "Apply Now" to submit your application with required documents.</p>
                                            </div>
                                        </div>

                                        <div class="border border-gray-200 rounded-lg">
                                            <button onclick="toggleFaq(2)" class="w-full text-left p-4 hover:bg-gray-50 transition-colors duration-200">
                                                <div class="flex items-center justify-between">
                                                    <span class="font-medium text-gray-900">How can I track my application status?</span>
                                                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform duration-200" id="faq-icon-2"></i>
                                                </div>
                                            </button>
                                            <div id="faq-content-2" class="hidden px-4 pb-4">
                                                <p class="text-gray-600 text-sm">Visit the "My Applications" section to view all your submitted applications and their current status. You'll receive email notifications when your application status changes.</p>
                                            </div>
                                        </div>

                                        <div class="border border-gray-200 rounded-lg">
                                            <button onclick="toggleFaq(3)" class="w-full text-left p-4 hover:bg-gray-50 transition-colors duration-200">
                                                <div class="flex items-center justify-between">
                                                    <span class="font-medium text-gray-900">What documents do I need to upload?</span>
                                                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform duration-200" id="faq-icon-3"></i>
                                                </div>
                                            </button>
                                            <div id="faq-content-3" class="hidden px-4 pb-4">
                                                <p class="text-gray-600 text-sm">Typically required documents include academic transcripts, passport photograph, ID card or certificate, and any additional certificates. Specific requirements are listed for each scholarship.</p>
                                            </div>
                                        </div>

                                        <div class="border border-gray-200 rounded-lg">
                                            <button onclick="toggleFaq(4)" class="w-full text-left p-4 hover:bg-gray-50 transition-colors duration-200">
                                                <div class="flex items-center justify-between">
                                                    <span class="font-medium text-gray-900">How do I update my profile information?</span>
                                                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform duration-200" id="faq-icon-4"></i>
                                                </div>
                                            </button>
                                            <div id="faq-content-4" class="hidden px-4 pb-4">
                                                <p class="text-gray-600 text-sm">Go to the "Academic Profile" section to update your personal information, university details, and academic records. Make sure to save your changes after editing.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Section -->
                        <div class="space-y-6">
                            <!-- Account Settings -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                                <div class="p-6 border-b border-gray-100">
                                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i class="fas fa-cog text-green-600 mr-2"></i>
                                        Account Settings
                                    </h2>
                                    <p class="text-gray-600 text-sm mt-1">Manage your account preferences</p>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900">Email Notifications</p>
                                                <p class="text-sm text-gray-600">Receive updates about your applications</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                            </label>
                                        </div>

                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900">SMS Notifications</p>
                                                <p class="text-sm text-gray-600">Get text messages for important updates</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer">
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                            </label>
                                        </div>

                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900">Newsletter</p>
                                                <p class="text-sm text-gray-600">Receive our monthly newsletter</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Settings -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                                <div class="p-6 border-b border-gray-100">
                                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i class="fas fa-shield-alt text-green-600 mr-2"></i>
                                        Security Settings
                                    </h2>
                                    <p class="text-gray-600 text-sm mt-1">Manage your account security</p>
                                </div>
                                <div class="p-6">
                                    <div class="space-y-4">
                                        <a href="#" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fas fa-key text-blue-600"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900">Change Password</p>
                                                    <p class="text-sm text-gray-600">Update your account password</p>
                                                </div>
                                            </div>
                                            <i class="fas fa-chevron-right text-gray-400"></i>
                                        </a>

                                        <a href="#" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fas fa-mobile-alt text-green-600"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900">Two-Factor Authentication</p>
                                                    <p class="text-sm text-gray-600">Add extra security to your account</p>
                                                </div>
                                            </div>
                                            <i class="fas fa-chevron-right text-gray-400"></i>
                                        </a>

                                        <a href="#" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fas fa-history text-purple-600"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900">Login History</p>
                                                    <p class="text-sm text-gray-600">View your recent login activity</p>
                                                </div>
                                            </div>
                                            <i class="fas fa-chevron-right text-gray-400"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                                <div class="p-6 border-b border-gray-100">
                                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                        <i class="fas fa-bolt text-green-600 mr-2"></i>
                                        Quick Actions
                                    </h2>
                                </div>
                                <div class="p-6">
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <a href="/student/profile" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                                <i class="fas fa-user-edit text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">Edit Profile</p>
                                                <p class="text-sm text-gray-600">Update your information</p>
                                            </div>
                                        </a>

                                        <a href="/student/documents" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                                <i class="fas fa-folder text-blue-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900">Manage Documents</p>
                                                <p class="text-sm text-gray-600">View your files</p>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function toggleFaq(id) {
    const content = document.getElementById(`faq-content-${id}`);
    const icon = document.getElementById(`faq-icon-${id}`);
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.classList.add('rotate-180');
    } else {
        content.classList.add('hidden');
        icon.classList.remove('rotate-180');
    }
}
</script>
@endsection
