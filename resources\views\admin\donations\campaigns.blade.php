@extends('layouts.admin')

@section('title', 'Donation Campaigns')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Donation Campaigns</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.donations.index') }}">Donations</a></li>
                        <li class="breadcrumb-item active">Campaigns</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.donations.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Campaign
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?status=active">Active</a></li>
                            <li><a class="dropdown-item" href="?status=completed">Completed</a></li>
                            <li><a class="dropdown-item" href="?status=paused">Paused</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?">All Campaigns</a></li>
                        </ul>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportCampaigns()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Campaigns</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ method_exists($campaigns, 'total') ? $campaigns->total() : $campaigns->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Campaigns</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $campaigns->where('status', 'active')->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Raised</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₦{{ number_format($campaigns->sum('total_raised'), 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Goal Achievement</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format(($campaigns->sum('total_raised') / max($campaigns->sum('goal_amount'), 1)) * 100, 1) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-target fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Donation Campaigns</h6>
        </div>
        <div class="card-body">
            @if($campaigns->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" id="campaignsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Campaign</th>
                                <th>Goal Amount</th>
                                <th>Raised</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>End Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($campaigns as $campaign)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($campaign->image)
                                            <img src="{{ asset('storage/' . $campaign->image) }}" alt="{{ $campaign->title }}" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-primary rounded d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                <i class="fas fa-bullhorn text-white"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $campaign->title }}</h6>
                                            <small class="text-muted">{{ Str::limit($campaign->description, 50) }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>₦{{ number_format($campaign->goal_amount, 2) }}</td>
                                <td>₦{{ number_format($campaign->total_raised ?? 0, 2) }}</td>
                                <td>
                                    @php
                                        $progress = ($campaign->total_raised / max($campaign->goal_amount, 1)) * 100;
                                    @endphp
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ min($progress, 100) }}%" aria-valuenow="{{ $progress }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ number_format($progress, 1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @switch($campaign->status)
                                        @case('active')
                                            <span class="badge bg-success">Active</span>
                                            @break
                                        @case('completed')
                                            <span class="badge bg-primary">Completed</span>
                                            @break
                                        @case('paused')
                                            <span class="badge bg-warning">Paused</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($campaign->status) }}</span>
                                    @endswitch
                                </td>
                                <td>{{ $campaign->end_date ? $campaign->end_date->format('M d, Y') : 'No end date' }}</td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="viewCampaign({{ $campaign->id }})">
                                                <i class="fas fa-eye me-2"></i>View Details
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-chart-bar me-2"></i>Analytics
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            @if($campaign->status === 'active')
                                                <li><a class="dropdown-item text-warning" href="#" onclick="pauseCampaign({{ $campaign->id }})">
                                                    <i class="fas fa-pause me-2"></i>Pause
                                                </a></li>
                                            @elseif($campaign->status === 'paused')
                                                <li><a class="dropdown-item text-success" href="#" onclick="resumeCampaign({{ $campaign->id }})">
                                                    <i class="fas fa-play me-2"></i>Resume
                                                </a></li>
                                            @endif
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteCampaign({{ $campaign->id }})">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if(method_exists($campaigns, 'links'))
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="text-muted">
                            Showing {{ $campaigns->firstItem() }} to {{ $campaigns->lastItem() }} of {{ $campaigns->total() }} campaigns
                        </p>
                    </div>
                    <div>
                        {{ $campaigns->links() }}
                    </div>
                </div>
                @endif
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No campaigns found</h5>
                    <p class="text-muted">Create your first donation campaign to get started.</p>
                    <a href="{{ route('admin.donations.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Campaign
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function viewCampaign(campaignId) {
    // Implement view campaign functionality
    console.log('View campaign:', campaignId);
}

function pauseCampaign(campaignId) {
    if (confirm('Are you sure you want to pause this campaign?')) {
        // Implement pause campaign functionality
        console.log('Pause campaign:', campaignId);
    }
}

function resumeCampaign(campaignId) {
    if (confirm('Are you sure you want to resume this campaign?')) {
        // Implement resume campaign functionality
        console.log('Resume campaign:', campaignId);
    }
}

function deleteCampaign(campaignId) {
    if (confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
        // Implement delete campaign functionality
        console.log('Delete campaign:', campaignId);
    }
}

function exportCampaigns() {
    // Implement export functionality
    console.log('Export campaigns');
}
</script>
@endsection
