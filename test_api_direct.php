<?php

// Test the API endpoint directly
$baseUrl = 'http://127.0.0.1:8000';

echo "=== Testing API Endpoint Directly ===\n\n";

// Create a context for the HTTP request
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Accept: application/json',
            'Content-Type: application/json'
        ]
    ]
]);

// Test public scholarships endpoint
$response = file_get_contents($baseUrl . '/api/v1/public-scholarships', false, $context);

if ($response === false) {
    echo "Failed to get response from API\n";
    exit;
}

echo "Raw Response:\n";
echo $response . "\n\n";

$data = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "JSON decode error: " . json_last_error_msg() . "\n";
    exit;
}

echo "Decoded Response Structure:\n";
echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
echo "Message: " . ($data['message'] ?? 'N/A') . "\n";

if (isset($data['data'])) {
    if (is_array($data['data'])) {
        echo "Data type: Array with " . count($data['data']) . " items\n";
        
        if (!empty($data['data'])) {
            echo "\nFirst scholarship data:\n";
            $first = $data['data'][0];
            print_r($first);
        }
    } else {
        echo "Data type: " . gettype($data['data']) . "\n";
        echo "Data content:\n";
        print_r($data['data']);
    }
} else {
    echo "No 'data' key in response\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test individual scholarship
echo "Testing individual scholarship (ID 5):\n";
$response2 = file_get_contents($baseUrl . '/api/v1/public-scholarships/5', false, $context);

if ($response2 !== false) {
    $data2 = json_decode($response2, true);
    echo "Individual scholarship success: " . ($data2['success'] ? 'true' : 'false') . "\n";
    
    if (isset($data2['data'])) {
        echo "Title: " . $data2['data']['title'] . "\n";
        echo "Amount: ₦" . number_format($data2['data']['amount']) . "\n";
        echo "Custom fields count: " . count($data2['data']['custom_fields'] ?? []) . "\n";
    }
} else {
    echo "Failed to get individual scholarship\n";
}
