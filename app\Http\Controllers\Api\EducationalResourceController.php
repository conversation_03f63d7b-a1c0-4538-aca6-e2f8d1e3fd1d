<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EducationalResource;
use App\Models\UserResourceProgress;
use App\Models\UserResourceBookmark;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

/**
 * @OA\Tag(
 *     name="Educational Resources",
 *     description="API Endpoints for Educational Resources"
 * )
 */
class EducationalResourceController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/educational-resources",
     *     summary="Get educational resources",
     *     tags={"Educational Resources"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by category",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by type (video, pdf, document)",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search in title and description",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Resources retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = EducationalResource::where('is_active', true);

            // Filter by category
            if ($request->has('category')) {
                $query->where('category', $request->category);
            }

            // Filter by type
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            // Search functionality
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('tags', 'like', "%{$search}%");
                });
            }

            $perPage = $request->get('per_page', 15);
            $resources = $query->orderBy('created_at', 'desc')->paginate($perPage);

            // Add user progress and bookmark status if authenticated
            if (Auth::check()) {
                $userId = Auth::id();
                foreach ($resources as $resource) {
                    $progress = UserResourceProgress::where('user_id', $userId)
                        ->where('resource_id', $resource->id)
                        ->first();
                    
                    $resource->user_progress = $progress ? $progress->progress_percentage : 0;
                    $resource->is_completed = $progress ? $progress->is_completed : false;
                    $resource->is_bookmarked = UserResourceBookmark::where('user_id', $userId)
                        ->where('resource_id', $resource->id)
                        ->exists();
                }
            }

            return response()->json([
                'success' => true,
                'data' => $resources
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve resources',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific educational resource
     */
    public function show($id): JsonResponse
    {
        try {
            $resource = EducationalResource::where('is_active', true)->findOrFail($id);

            // Add user progress and bookmark status if authenticated
            if (Auth::check()) {
                $userId = Auth::id();
                $progress = UserResourceProgress::where('user_id', $userId)
                    ->where('resource_id', $resource->id)
                    ->first();
                
                $resource->user_progress = $progress ? $progress->progress_percentage : 0;
                $resource->is_completed = $progress ? $progress->is_completed : false;
                $resource->is_bookmarked = UserResourceBookmark::where('user_id', $userId)
                    ->where('resource_id', $resource->id)
                    ->exists();
                $resource->last_accessed = $progress ? $progress->last_accessed_at : null;
            }

            return response()->json([
                'success' => true,
                'data' => $resource
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update user progress for a resource
     */
    public function updateProgress(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'progress_percentage' => 'required|numeric|min:0|max:100',
                'time_spent' => 'nullable|integer|min:0',
                'is_completed' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $resource = EducationalResource::findOrFail($id);
            $userId = Auth::id();

            $progress = UserResourceProgress::updateOrCreate(
                [
                    'user_id' => $userId,
                    'resource_id' => $resource->id
                ],
                [
                    'progress_percentage' => $request->progress_percentage,
                    'time_spent' => $request->time_spent ?? 0,
                    'is_completed' => $request->progress_percentage >= 100 || $request->is_completed,
                    'last_accessed_at' => now()
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Progress updated successfully',
                'data' => $progress
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update progress',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle bookmark for a resource
     */
    public function toggleBookmark($id): JsonResponse
    {
        try {
            $resource = EducationalResource::findOrFail($id);
            $userId = Auth::id();

            $bookmark = UserResourceBookmark::where('user_id', $userId)
                ->where('resource_id', $resource->id)
                ->first();

            if ($bookmark) {
                $bookmark->delete();
                $isBookmarked = false;
                $message = 'Resource removed from bookmarks';
            } else {
                UserResourceBookmark::create([
                    'user_id' => $userId,
                    'resource_id' => $resource->id
                ]);
                $isBookmarked = true;
                $message = 'Resource added to bookmarks';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => ['is_bookmarked' => $isBookmarked]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle bookmark',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's bookmarked resources
     */
    public function getBookmarks(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $perPage = $request->get('per_page', 15);

            $bookmarks = UserResourceBookmark::with('resource')
                ->where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $bookmarks
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve bookmarks',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's progress summary
     */
    public function getProgressSummary(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $totalResources = EducationalResource::where('is_active', true)->count();
            $accessedResources = UserResourceProgress::where('user_id', $userId)->count();
            $completedResources = UserResourceProgress::where('user_id', $userId)
                ->where('is_completed', true)
                ->count();
            $totalTimeSpent = UserResourceProgress::where('user_id', $userId)
                ->sum('time_spent');
            $bookmarkedResources = UserResourceBookmark::where('user_id', $userId)->count();

            $averageProgress = UserResourceProgress::where('user_id', $userId)
                ->avg('progress_percentage') ?? 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_resources' => $totalResources,
                    'accessed_resources' => $accessedResources,
                    'completed_resources' => $completedResources,
                    'completion_rate' => $totalResources > 0 ? round(($completedResources / $totalResources) * 100, 2) : 0,
                    'total_time_spent' => $totalTimeSpent,
                    'average_progress' => round($averageProgress, 2),
                    'bookmarked_resources' => $bookmarkedResources
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve progress summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download resource file
     */
    public function download($id): JsonResponse
    {
        try {
            $resource = EducationalResource::findOrFail($id);
            
            if (!$resource->file_path || !Storage::disk('public')->exists($resource->file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            // Track download
            if (Auth::check()) {
                $userId = Auth::id();
                UserResourceProgress::updateOrCreate(
                    [
                        'user_id' => $userId,
                        'resource_id' => $resource->id
                    ],
                    [
                        'last_accessed_at' => now()
                    ]
                );
            }

            $filePath = Storage::disk('public')->path($resource->file_path);
            $fileName = $resource->title . '.' . pathinfo($resource->file_path, PATHINFO_EXTENSION);

            return response()->json([
                'success' => true,
                'data' => [
                    'download_url' => asset('storage/' . $resource->file_path),
                    'file_name' => $fileName,
                    'file_size' => Storage::disk('public')->size($resource->file_path)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download resource',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
