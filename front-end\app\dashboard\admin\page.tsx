'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Users,
  DollarSign,
  Calendar,
  FileText,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Plus,
  Check,
  X,
  Search,
  Filter,
  Download,
  BarChart3,
  PieChart,
  Activity,
  Bell,
  Settings,
  UserCheck,
  GraduationCap,
  Heart,
  Building,
  ChevronDown,
  ChevronUp,
  MoreHorizontal,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Target,
  Globe,
  Mail,
  Phone,
  MapPin,
  Star,
  Award,
  Briefcase,
  BookOpen,
  MessageSquare,
  UserPlus,
  Shield,
  Database,
  BarChart,
  LineChart,
  Layers,
  Grid,
  List,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';
import ScholarshipManagement from '@/components/admin/ScholarshipManagement';
import UserManagement from '@/components/admin/UserManagement';

interface DashboardStats {
  total_users: number;
  new_users_this_month: number;
  active_volunteers: number;
  pending_volunteers: number;
  total_donations: number;
  donations_this_month: number;
  active_programs: number;
  upcoming_events: number;
  published_blog_posts: number;
  pending_blog_posts: number;
  unread_messages: number;
  newsletter_subscribers: number;
  scholarship_applications: number;
}

interface ScholarshipApplication {
  id: number;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  scholarship: {
    id: number;
    title: string;
    amount: number;
  };
  application_status: string;
  submitted_at: string;
  reviewed_at?: string;
  review_notes?: string;
  application_data: any;
}

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  last_login_at?: string;
  email_verified_at?: string;
  phone_number?: string;
  preferences?: any;
}

interface Volunteer {
  id: number;
  user: User;
  application_status: 'pending' | 'approved' | 'rejected';
  skills: string[];
  availability: string;
  experience: string;
  motivation: string;
  applied_at: string;
  reviewed_at?: string;
  reviewed_by?: number;
}

interface Program {
  id: number;
  title: string;
  description: string;
  status: 'active' | 'inactive' | 'completed';
  start_date: string;
  end_date?: string;
  budget: number;
  participants_count: number;
  category: string;
  location: string;
}

interface Event {
  id: number;
  title: string;
  description: string;
  start_datetime: string;
  end_datetime: string;
  location: string;
  max_participants?: number;
  current_participants: number;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  event_type: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  status: 'draft' | 'published' | 'archived';
  author: User;
  category: string;
  featured_image?: string;
  views_count: number;
  likes_count: number;
  created_at: string;
  published_at?: string;
}

interface ContactMessage {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied';
  created_at: string;
  replied_at?: string;
  replied_by?: number;
}

interface AnalyticsData {
  user_growth: { month: string; count: number }[];
  donation_trends: { month: string; amount: number }[];
  volunteer_engagement: { month: string; hours: number }[];
  program_performance: { program: string; completion_rate: number }[];
}

interface FilterOptions {
  search: string;
  status: string;
  dateRange: string;
  category: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface BulkAction {
  action: string;
  selectedItems: number[];
}

export default function AdminDashboard() {
  // Core state
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Entity states
  const [scholarshipApplications, setScholarshipApplications] = useState<ScholarshipApplication[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [volunteers, setVolunteers] = useState<Volunteer[]>([]);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);

  // UI state
  const [selectedApplication, setSelectedApplication] = useState<ScholarshipApplication | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [bulkActionDialogOpen, setBulkActionDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // Filter and search state
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    status: 'all',
    dateRange: 'all',
    category: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [bulkAction, setBulkAction] = useState<BulkAction>({
    action: '',
    selectedItems: []
  });

  // Form state
  const [reviewData, setReviewData] = useState({
    application_status: '',
    review_notes: '',
    award_amount: ''
  });

  const [userFormData, setUserFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    role: 'user',
    status: 'active'
  });

  useEffect(() => {
    fetchAllData();
  }, []);

  useEffect(() => {
    if (activeTab !== 'overview') {
      fetchTabData(activeTab);
    }
  }, [activeTab, filters]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchDashboardData(),
        fetchScholarshipApplications(),
        fetchAnalyticsData()
      ]);
    } catch (error) {
      console.error('Error fetching initial data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    try {
      await fetchAllData();
      toast.success('Dashboard data refreshed');
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  const fetchDashboardData = async () => {
    try {
      const response = await apiClient.getAdminDashboard();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  };

  const fetchScholarshipApplications = async () => {
    try {
      const response = await apiClient.getScholarshipApplications({
        page: 1,
        per_page: 10,
        status: 'all'
      });
      if (response.success) {
        setScholarshipApplications(response.data?.data || []);
      }
    } catch (error) {
      console.error('Error fetching scholarship applications:', error);
      throw error;
    }
  };

  const fetchAnalyticsData = async () => {
    try {
      const response = await apiClient.getAdminAnalytics({ days: 180 });
      if (response.success && response.data) {
        setAnalyticsData(response.data);
      } else {
        // Fallback to mock data if API fails
        const mockAnalytics: AnalyticsData = {
          user_growth: [
            { month: 'Jan', count: 120 },
            { month: 'Feb', count: 150 },
            { month: 'Mar', count: 180 },
            { month: 'Apr', count: 220 },
            { month: 'May', count: 280 },
            { month: 'Jun', count: 320 }
          ],
          donation_trends: [
            { month: 'Jan', amount: 15000 },
            { month: 'Feb', amount: 18000 },
            { month: 'Mar', amount: 22000 },
            { month: 'Apr', amount: 25000 },
            { month: 'May', amount: 30000 },
            { month: 'Jun', amount: 35000 }
          ],
          volunteer_engagement: [
            { month: 'Jan', hours: 450 },
            { month: 'Feb', hours: 520 },
            { month: 'Mar', hours: 680 },
            { month: 'Apr', hours: 750 },
            { month: 'May', hours: 820 },
            { month: 'Jun', hours: 950 }
          ],
          program_performance: [
            { program: 'Education Support', completion_rate: 85 },
            { program: 'Healthcare Access', completion_rate: 92 },
            { program: 'Community Development', completion_rate: 78 },
            { program: 'Women Empowerment', completion_rate: 88 }
          ]
        };
        setAnalyticsData(mockAnalytics);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      // Use mock data as fallback
      const mockAnalytics: AnalyticsData = {
        user_growth: [
          { month: 'Jan', count: 120 },
          { month: 'Feb', count: 150 },
          { month: 'Mar', count: 180 },
          { month: 'Apr', count: 220 },
          { month: 'May', count: 280 },
          { month: 'Jun', count: 320 }
        ],
        donation_trends: [
          { month: 'Jan', amount: 15000 },
          { month: 'Feb', amount: 18000 },
          { month: 'Mar', amount: 22000 },
          { month: 'Apr', amount: 25000 },
          { month: 'May', amount: 30000 },
          { month: 'Jun', amount: 35000 }
        ],
        volunteer_engagement: [
          { month: 'Jan', hours: 450 },
          { month: 'Feb', hours: 520 },
          { month: 'Mar', hours: 680 },
          { month: 'Apr', hours: 750 },
          { month: 'May', hours: 820 },
          { month: 'Jun', hours: 950 }
        ],
        program_performance: [
          { program: 'Education Support', completion_rate: 85 },
          { program: 'Healthcare Access', completion_rate: 92 },
          { program: 'Community Development', completion_rate: 78 },
          { program: 'Women Empowerment', completion_rate: 88 }
        ]
      };
      setAnalyticsData(mockAnalytics);
      throw error;
    }
  };

  const fetchTabData = async (tab: string) => {
    try {
      switch (tab) {
        case 'users':
          await fetchUsers();
          break;
        case 'volunteers':
          await fetchVolunteers();
          break;
        case 'programs':
          await fetchPrograms();
          break;
        case 'events':
          await fetchEvents();
          break;
        case 'blog':
          await fetchBlogPosts();
          break;
        case 'messages':
          await fetchContactMessages();
          break;
      }
    } catch (error) {
      console.error(`Error fetching ${tab} data:`, error);
      toast.error(`Failed to load ${tab} data`);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await apiClient.getAdminUsers({
        page: 1,
        per_page: 50,
        status: 'all',
        role: 'all'
      });

      if (response.success) {
        setUsers(response.data?.data || []);
      } else {
        // Fallback to mock data if API fails
        const mockUsers: User[] = [
          {
            id: 1,
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            role: 'user',
            status: 'active',
            created_at: '2024-01-15',
            last_login_at: '2024-06-01',
            email_verified_at: '2024-01-15'
          },
          {
            id: 2,
            first_name: 'Jane',
            last_name: 'Smith',
            email: '<EMAIL>',
            role: 'volunteer',
            status: 'active',
            created_at: '2024-02-01',
            last_login_at: '2024-05-30',
            email_verified_at: '2024-02-01'
          }
        ];
        setUsers(mockUsers);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      // Use mock data as fallback
      const mockUsers: User[] = [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          created_at: '2024-01-15',
          last_login_at: '2024-06-01',
          email_verified_at: '2024-01-15'
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          role: 'volunteer',
          status: 'active',
          created_at: '2024-02-01',
          last_login_at: '2024-05-30',
          email_verified_at: '2024-02-01'
        }
      ];
      setUsers(mockUsers);
      throw error;
    }
  };

  const fetchVolunteers = async () => {
    try {
      // Mock data - replace with actual API call
      const mockVolunteers: Volunteer[] = [];
      setVolunteers(mockVolunteers);
    } catch (error) {
      console.error('Error fetching volunteers:', error);
      throw error;
    }
  };

  const fetchPrograms = async () => {
    try {
      // Mock data - replace with actual API call
      const mockPrograms: Program[] = [];
      setPrograms(mockPrograms);
    } catch (error) {
      console.error('Error fetching programs:', error);
      throw error;
    }
  };

  const fetchEvents = async () => {
    try {
      // Mock data - replace with actual API call
      const mockEvents: Event[] = [];
      setEvents(mockEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
      throw error;
    }
  };

  const fetchBlogPosts = async () => {
    try {
      // Mock data - replace with actual API call
      const mockBlogPosts: BlogPost[] = [];
      setBlogPosts(mockBlogPosts);
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      throw error;
    }
  };

  const fetchContactMessages = async () => {
    try {
      // Mock data - replace with actual API call
      const mockMessages: ContactMessage[] = [];
      setContactMessages(mockMessages);
    } catch (error) {
      console.error('Error fetching contact messages:', error);
      throw error;
    }
  };

  // Action handlers
  const handleReviewApplication = async (applicationId: number, reviewData: any) => {
    try {
      const response = await apiClient.reviewScholarshipApplication(applicationId, reviewData);
      if (response.success) {
        toast.success('Application reviewed successfully');
        fetchScholarshipApplications();
      }
    } catch (error: any) {
      console.error('Error reviewing application:', error);
      toast.error(error.response?.data?.message || 'Failed to review application');
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction.action || selectedItems.length === 0) return;

    try {
      let response;

      if (activeTab === 'scholarships') {
        response = await apiClient.bulkActionScholarshipApplications({
          action: bulkAction.action,
          application_ids: selectedItems
        });
      } else if (activeTab === 'users') {
        response = await apiClient.bulkActionUsers({
          action: bulkAction.action,
          user_ids: selectedItems
        });
      } else if (activeTab === 'events') {
        response = await apiClient.bulkActionEvents({
          action: bulkAction.action,
          event_ids: selectedItems
        });
      } else {
        // Fallback for other tabs
        console.log('Performing bulk action:', bulkAction.action, 'on items:', selectedItems);
        await new Promise(resolve => setTimeout(resolve, 1000));
        response = { success: true };
      }

      if (response.success) {
        switch (bulkAction.action) {
          case 'approve':
            toast.success(`Approved ${selectedItems.length} applications`);
            break;
          case 'reject':
            toast.success(`Rejected ${selectedItems.length} applications`);
            break;
          case 'delete':
            toast.success(`Deleted ${selectedItems.length} items`);
            break;
          default:
            toast.success(`Performed ${bulkAction.action} on ${selectedItems.length} items`);
        }

        setSelectedItems([]);
        setBulkActionDialogOpen(false);
        setBulkAction({ action: '', selectedItems: [] });

        // Refresh data based on current tab
        if (activeTab === 'scholarships') {
          fetchScholarshipApplications();
        } else {
          fetchTabData(activeTab);
        }
      }
    } catch (error: any) {
      console.error('Error performing bulk action:', error);
      toast.error('Failed to perform bulk action');
    }
  };

  const handleCreateUser = async (userData?: any) => {
    try {
      const dataToSubmit = userData || {
        ...userFormData,
        password: 'TempPassword123!', // Temporary password - should be changed on first login
        password_confirmation: 'TempPassword123!'
      };

      const response = await apiClient.createAdminUser(dataToSubmit);

      if (response.success) {
        toast.success('User created successfully');
        setUserDialogOpen(false);
        setUserFormData({
          first_name: '',
          last_name: '',
          email: '',
          role: 'user',
          status: 'active'
        });
        fetchUsers();
      }
    } catch (error: any) {
      console.error('Error creating user:', error);
      toast.error('Failed to create user');
    }
  };

  const handleUpdateUser = async (userId: number, userData: any) => {
    try {
      const response = await apiClient.updateAdminUser(userId, userData);

      if (response.success) {
        toast.success('User updated successfully');
        fetchUsers();
      }
    } catch (error: any) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      const response = await apiClient.deleteAdminUser(userId);

      if (response.success) {
        toast.success('User deleted successfully');
        fetchUsers();
      }
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  const handleDeleteItem = async (itemType: string, itemId: number) => {
    if (!confirm(`Are you sure you want to delete this ${itemType}?`)) return;

    try {
      // Mock deletion - replace with actual API call
      console.log('Deleting:', itemType, itemId);
      toast.success(`${itemType} deleted successfully`);

      // Refresh appropriate data
      switch (itemType) {
        case 'user':
          fetchUsers();
          break;
        case 'application':
          fetchScholarshipApplications();
          break;
        default:
          fetchTabData(activeTab);
      }
    } catch (error: any) {
      console.error(`Error deleting ${itemType}:`, error);
      toast.error(`Failed to delete ${itemType}`);
    }
  };

  // Filter and search handlers
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      switch (activeTab) {
        case 'scholarships':
          setSelectedItems(scholarshipApplications.map(app => app.id));
          break;
        case 'users':
          setSelectedItems(users.map(user => user.id));
          break;
        default:
          setSelectedItems([]);
      }
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: number, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const resetFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      dateRange: 'all',
      category: 'all',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  };

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'published':
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
      case 'submitted':
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
      case 'inactive':
      case 'suspended':
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'draft':
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'ongoing':
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'published':
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
      case 'inactive':
      case 'suspended':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
      case 'submitted':
      case 'under_review':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'draft':
      case 'archived':
        return <FileText className="h-4 w-4 text-gray-600" />;
      case 'ongoing':
      case 'upcoming':
        return <Activity className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'volunteer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'partner':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'student':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getGrowthPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const getGrowthIcon = (percentage: number) => {
    if (percentage > 0) {
      return <ArrowUpRight className="h-4 w-4 text-green-600" />;
    } else if (percentage < 0) {
      return <ArrowDownRight className="h-4 w-4 text-red-600" />;
    }
    return <div className="h-4 w-4" />;
  };

  const filterData = <T extends { [key: string]: any }>(
    data: T[],
    searchFields: string[],
    statusField?: string
  ): T[] => {
    return data.filter(item => {
      // Search filter
      if (filters.search) {
        const searchMatch = searchFields.some(field =>
          item[field]?.toString().toLowerCase().includes(filters.search.toLowerCase())
        );
        if (!searchMatch) return false;
      }

      // Status filter
      if (filters.status !== 'all' && statusField) {
        if (item[statusField] !== filters.status) return false;
      }

      return true;
    }).sort((a, b) => {
      const aValue = a[filters.sortBy];
      const bValue = b[filters.sortBy];

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Heart className="h-6 w-6 text-blue-600 animate-pulse" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">Loading Admin Dashboard</h3>
              <p className="text-sm text-gray-600">Preparing your management interface...</p>
            </div>
            <div className="flex space-x-1 justify-center">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto space-y-6 p-6">
        {/* Enhanced Header */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="h-16 w-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 h-6 w-6 bg-green-500 rounded-full flex items-center justify-center">
                  <div className="h-2 w-2 bg-white rounded-full"></div>
                </div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Admin Dashboard
                </h1>
                <p className="text-gray-600 font-medium">Comprehensive NGO Platform Management</p>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-1 text-sm text-green-600">
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>System Online</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={refreshing}
                className="bg-white/50 hover:bg-white/80 border-gray-200"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="bg-white/50 hover:bg-white/80 border-gray-200"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="bg-white/50 hover:bg-white/80 border-gray-200"
              >
                <Bell className="h-4 w-4 mr-2" />
                Notifications
                {stats && stats.unread_messages > 0 && (
                  <Badge className="ml-2 bg-red-500 text-white text-xs px-1.5 py-0.5">
                    {stats.unread_messages}
                  </Badge>
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="bg-white/50 hover:bg-white/80 border-gray-200"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Users Card */}
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-semibold text-blue-900">Total Users</CardTitle>
                <div className="h-10 w-10 bg-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-900 mb-1">
                  {stats.total_users.toLocaleString()}
                </div>
                <div className="flex items-center space-x-2">
                  {getGrowthIcon(12)}
                  <p className="text-sm text-blue-700 font-medium">
                    +{stats.new_users_this_month} this month
                  </p>
                </div>
                <Progress value={75} className="mt-3 h-2" />
              </CardContent>
            </Card>

            {/* Total Donations Card */}
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-semibold text-green-900">Total Donations</CardTitle>
                <div className="h-10 w-10 bg-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-900 mb-1">
                  {formatCurrency(stats.total_donations)}
                </div>
                <div className="flex items-center space-x-2">
                  {getGrowthIcon(8.5)}
                  <p className="text-sm text-green-700 font-medium">
                    {formatCurrency(stats.donations_this_month)} this month
                  </p>
                </div>
                <Progress value={85} className="mt-3 h-2" />
              </CardContent>
            </Card>

            {/* Active Volunteers Card */}
            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-semibold text-purple-900">Active Volunteers</CardTitle>
                <div className="h-10 w-10 bg-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <UserCheck className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-900 mb-1">
                  {stats.active_volunteers}
                </div>
                <div className="flex items-center space-x-2">
                  {getGrowthIcon(15)}
                  <p className="text-sm text-purple-700 font-medium">
                    {stats.pending_volunteers} pending approval
                  </p>
                </div>
                <Progress value={60} className="mt-3 h-2" />
              </CardContent>
            </Card>

            {/* Scholarship Applications Card */}
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-semibold text-orange-900">Scholarship Applications</CardTitle>
                <div className="h-10 w-10 bg-orange-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-900 mb-1">
                  {stats.scholarship_applications}
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <p className="text-sm text-orange-700 font-medium">
                    Pending review
                  </p>
                </div>
                <Progress value={40} className="mt-3 h-2" />
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Stats Row */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.active_programs}</div>
                <p className="text-sm text-gray-600">Active Programs</p>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.upcoming_events}</div>
                <p className="text-sm text-gray-600">Upcoming Events</p>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.published_blog_posts}</div>
                <p className="text-sm text-gray-600">Published Posts</p>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.pending_blog_posts}</div>
                <p className="text-sm text-gray-600">Draft Posts</p>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.unread_messages}</div>
                <p className="text-sm text-gray-600">Unread Messages</p>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.newsletter_subscribers}</div>
                <p className="text-sm text-gray-600">Subscribers</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Navigation Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-2">
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-8 gap-1 bg-transparent">
              <TabsTrigger
                value="overview"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="scholarships"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <GraduationCap className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Scholarships</span>
              </TabsTrigger>
              <TabsTrigger
                value="users"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <Users className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Users</span>
              </TabsTrigger>
              <TabsTrigger
                value="volunteers"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <UserCheck className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Volunteers</span>
              </TabsTrigger>
              <TabsTrigger
                value="programs"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <Target className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Programs</span>
              </TabsTrigger>
              <TabsTrigger
                value="events"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <Calendar className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Events</span>
              </TabsTrigger>
              <TabsTrigger
                value="blog"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Blog</span>
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-xl font-medium"
              >
                <LineChart className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Analytics</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Recent Activity */}
              <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center text-lg font-semibold">
                        <Activity className="h-5 w-5 mr-2 text-blue-600" />
                        Recent Activity
                      </CardTitle>
                      <CardDescription>Latest platform activities and updates</CardDescription>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View All
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-4 p-3 rounded-lg bg-green-50 border border-green-200">
                      <div className="h-8 w-8 bg-green-600 rounded-full flex items-center justify-center">
                        <GraduationCap className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-900">New scholarship application submitted</p>
                        <p className="text-xs text-green-700">Sarah Johnson applied for Education Support Scholarship</p>
                        <p className="text-xs text-green-600 mt-1">2 minutes ago</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 p-3 rounded-lg bg-blue-50 border border-blue-200">
                      <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <UserCheck className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">Volunteer application approved</p>
                        <p className="text-xs text-blue-700">Michael Chen approved for Community Outreach program</p>
                        <p className="text-xs text-blue-600 mt-1">1 hour ago</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 p-3 rounded-lg bg-purple-50 border border-purple-200">
                      <div className="h-8 w-8 bg-purple-600 rounded-full flex items-center justify-center">
                        <DollarSign className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-purple-900">New donation received</p>
                        <p className="text-xs text-purple-700">₦50,000 donation from Anonymous Donor</p>
                        <p className="text-xs text-purple-600 mt-1">3 hours ago</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 p-3 rounded-lg bg-orange-50 border border-orange-200">
                      <div className="h-8 w-8 bg-orange-600 rounded-full flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-orange-900">Event registration opened</p>
                        <p className="text-xs text-orange-700">Community Health Workshop - 50 spots available</p>
                        <p className="text-xs text-orange-600 mt-1">5 hours ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg font-semibold">
                    <Zap className="h-5 w-5 mr-2 text-yellow-600" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>Common administrative tasks</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button
                      className="w-full justify-start h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                      onClick={() => setUserDialogOpen(true)}
                    >
                      <UserPlus className="h-5 w-5 mr-3" />
                      Add New User
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start h-12 border-green-200 hover:bg-green-50"
                      onClick={() => setActiveTab('scholarships')}
                    >
                      <GraduationCap className="h-5 w-5 mr-3 text-green-600" />
                      Review Applications
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start h-12 border-purple-200 hover:bg-purple-50"
                      onClick={() => setActiveTab('events')}
                    >
                      <Calendar className="h-5 w-5 mr-3 text-purple-600" />
                      Create Event
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start h-12 border-orange-200 hover:bg-orange-50"
                    >
                      <FileText className="h-5 w-5 mr-3 text-orange-600" />
                      Generate Report
                    </Button>

                    <Separator className="my-4" />

                    <Button
                      variant="ghost"
                      className="w-full justify-start h-10 text-gray-600 hover:text-gray-900"
                    >
                      <Settings className="h-4 w-4 mr-3" />
                      System Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* System Status and Alerts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg font-semibold">
                    <Shield className="h-5 w-5 mr-2 text-green-600" />
                    System Status
                  </CardTitle>
                  <CardDescription>Platform health and performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Database</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">API Services</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Online</Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-3 w-3 bg-yellow-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Email Service</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">Degraded</Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">File Storage</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Operational</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg font-semibold">
                    <Bell className="h-5 w-5 mr-2 text-red-600" />
                    Pending Actions
                  </CardTitle>
                  <CardDescription>Items requiring your attention</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <div className="flex items-center space-x-3">
                        <AlertCircle className="h-5 w-5 text-red-600" />
                        <div>
                          <p className="text-sm font-medium text-red-900">Scholarship Applications</p>
                          <p className="text-xs text-red-700">{stats?.scholarship_applications} pending review</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
                        Review
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div className="flex items-center space-x-3">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                          <p className="text-sm font-medium text-yellow-900">Volunteer Applications</p>
                          <p className="text-xs text-yellow-700">{stats?.pending_volunteers} awaiting approval</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="border-yellow-300 text-yellow-700 hover:bg-yellow-100">
                        Review
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center space-x-3">
                        <MessageSquare className="h-5 w-5 text-blue-600" />
                        <div>
                          <p className="text-sm font-medium text-blue-900">Unread Messages</p>
                          <p className="text-xs text-blue-700">{stats?.unread_messages} new messages</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="scholarships" className="space-y-6">
            <ScholarshipManagement
              applications={scholarshipApplications}
              onReview={handleReviewApplication}
              onBulkAction={handleBulkAction}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
              onSelectAll={handleSelectAll}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <UserManagement
              users={users}
              onCreateUser={handleCreateUser}
              onUpdateUser={handleUpdateUser}
              onDeleteUser={handleDeleteUser}
              onBulkAction={handleBulkAction}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
              onSelectAll={handleSelectAll}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </TabsContent>

          <TabsContent value="volunteers" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Volunteer Management</h2>
                <p className="text-gray-600">Manage volunteer applications and assignments</p>
              </div>

              <div className="flex items-center space-x-3">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export List
                </Button>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Volunteer
                </Button>
              </div>
            </div>

            {/* Volunteer Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Total Volunteers</p>
                      <p className="text-3xl font-bold text-blue-900">{stats?.active_volunteers || 0}</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-900">Pending Applications</p>
                      <p className="text-3xl font-bold text-yellow-900">{stats?.pending_volunteers || 0}</p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Active This Month</p>
                      <p className="text-3xl font-bold text-green-900">45</p>
                    </div>
                    <Activity className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">Total Hours</p>
                      <p className="text-3xl font-bold text-purple-900">2,847</p>
                    </div>
                    <Clock className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Volunteer Applications Table */}
            <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
              <CardHeader>
                <CardTitle>Recent Applications</CardTitle>
                <CardDescription>Volunteer applications awaiting review</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-yellow-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">JD</span>
                      </div>
                      <div>
                        <p className="font-medium text-yellow-900">John Doe</p>
                        <p className="text-sm text-yellow-700">Community Outreach • Applied 2 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" className="border-yellow-300 text-yellow-700">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="border-red-300 text-red-700">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">SM</span>
                      </div>
                      <div>
                        <p className="font-medium text-blue-900">Sarah Miller</p>
                        <p className="text-sm text-blue-700">Education Support • Applied 1 week ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" className="border-blue-300 text-blue-700">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline" className="border-red-300 text-red-700">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="programs" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Program Management</h2>
                <p className="text-gray-600">Manage NGO programs and initiatives</p>
              </div>

              <div className="flex items-center space-x-3">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Programs
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Program
                </Button>
              </div>
            </div>

            {/* Program Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Active Programs</p>
                      <p className="text-3xl font-bold text-green-900">{stats?.active_programs || 0}</p>
                    </div>
                    <Target className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Total Beneficiaries</p>
                      <p className="text-3xl font-bold text-blue-900">1,247</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">Success Rate</p>
                      <p className="text-3xl font-bold text-purple-900">88.7%</p>
                    </div>
                    <Award className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-900">Total Budget</p>
                      <p className="text-3xl font-bold text-orange-900">₦2.5M</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Programs Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">Education Support Program</CardTitle>
                      <CardDescription>Providing educational resources to underprivileged children</CardDescription>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Beneficiaries</p>
                        <p className="text-lg font-bold">450</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Budget</p>
                        <p className="text-lg font-bold">₦800K</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Duration</p>
                        <p className="text-lg font-bold">12 months</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Progress</p>
                        <p className="text-lg font-bold">75%</p>
                      </div>
                    </div>
                    <Progress value={75} className="h-2" />
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">Healthcare Access Initiative</CardTitle>
                      <CardDescription>Mobile health clinics for rural communities</CardDescription>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Active</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Beneficiaries</p>
                        <p className="text-lg font-bold">320</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Budget</p>
                        <p className="text-lg font-bold">₦1.2M</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Duration</p>
                        <p className="text-lg font-bold">18 months</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Progress</p>
                        <p className="text-lg font-bold">60%</p>
                      </div>
                    </div>
                    <Progress value={60} className="h-2" />
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">Women Empowerment Program</CardTitle>
                      <CardDescription>Skills training and microfinance for women</CardDescription>
                    </div>
                    <Badge className="bg-purple-100 text-purple-800">Planning</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Target Beneficiaries</p>
                        <p className="text-lg font-bold">200</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Budget</p>
                        <p className="text-lg font-bold">₦500K</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Duration</p>
                        <p className="text-lg font-bold">6 months</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Progress</p>
                        <p className="text-lg font-bold">25%</p>
                      </div>
                    </div>
                    <Progress value={25} className="h-2" />
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">Community Development</CardTitle>
                      <CardDescription>Infrastructure and capacity building projects</CardDescription>
                    </div>
                    <Badge className="bg-gray-100 text-gray-800">Completed</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Beneficiaries</p>
                        <p className="text-lg font-bold">277</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Budget</p>
                        <p className="text-lg font-bold">₦900K</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Duration</p>
                        <p className="text-lg font-bold">24 months</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Progress</p>
                        <p className="text-lg font-bold">100%</p>
                      </div>
                    </div>
                    <Progress value={100} className="h-2" />
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View Report
                      </Button>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Event Management</h2>
                <p className="text-gray-600">Manage events and community activities</p>
              </div>

              <div className="flex items-center space-x-3">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Events
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Event
                </Button>
              </div>
            </div>

            {/* Event Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Upcoming Events</p>
                      <p className="text-3xl font-bold text-blue-900">{stats?.upcoming_events || 0}</p>
                    </div>
                    <Calendar className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Total Registrations</p>
                      <p className="text-3xl font-bold text-green-900">847</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">This Month</p>
                      <p className="text-3xl font-bold text-purple-900">12</p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-900">Attendance Rate</p>
                      <p className="text-3xl font-bold text-orange-900">92%</p>
                    </div>
                    <Star className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Upcoming Events */}
            <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
                <CardDescription>Events scheduled for the next 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                        <Calendar className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-blue-900">Community Health Workshop</p>
                        <p className="text-sm text-blue-700">June 15, 2024 • 10:00 AM - 4:00 PM</p>
                        <p className="text-sm text-blue-600">Lagos Community Center • 50 registered</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-green-100 text-green-800">50/50 Full</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-green-900">Scholarship Award Ceremony</p>
                        <p className="text-sm text-green-700">June 20, 2024 • 2:00 PM - 6:00 PM</p>
                        <p className="text-sm text-green-600">HLTKKQ Foundation Hall • 120 registered</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-yellow-100 text-yellow-800">120/150</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                        <Heart className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-purple-900">Volunteer Appreciation Day</p>
                        <p className="text-sm text-purple-700">June 25, 2024 • 12:00 PM - 8:00 PM</p>
                        <p className="text-sm text-purple-600">City Park • 85 registered</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-blue-100 text-blue-800">85/100</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="blog" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Blog Management</h2>
                <p className="text-gray-600">Manage blog posts and content</p>
              </div>

              <div className="flex items-center space-x-3">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Posts
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  New Post
                </Button>
              </div>
            </div>

            {/* Blog Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Published Posts</p>
                      <p className="text-3xl font-bold text-green-900">{stats?.published_blog_posts || 0}</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-900">Draft Posts</p>
                      <p className="text-3xl font-bold text-yellow-900">{stats?.pending_blog_posts || 0}</p>
                    </div>
                    <FileText className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Total Views</p>
                      <p className="text-3xl font-bold text-blue-900">12.5K</p>
                    </div>
                    <Eye className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">Engagement Rate</p>
                      <p className="text-3xl font-bold text-purple-900">8.7%</p>
                    </div>
                    <MessageSquare className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Posts */}
            <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
              <CardHeader>
                <CardTitle>Recent Posts</CardTitle>
                <CardDescription>Latest blog posts and articles</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                        <BookOpen className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-green-900">Impact of Education Programs in Rural Communities</p>
                        <p className="text-sm text-green-700">Published 2 days ago • 1.2K views • 45 likes</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-green-100 text-green-800">Published</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-yellow-600 rounded-xl flex items-center justify-center">
                        <FileText className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-yellow-900">Volunteer Stories: Making a Difference</p>
                        <p className="text-sm text-yellow-700">Draft • Last edited 1 hour ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-yellow-100 text-yellow-800">Draft</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                        <Heart className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-blue-900">Healthcare Access: Breaking Barriers</p>
                        <p className="text-sm text-blue-700">Published 1 week ago • 2.1K views • 78 likes</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-green-100 text-green-800">Published</Badge>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Analytics Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
                <p className="text-gray-600">Comprehensive platform insights and performance metrics</p>
              </div>

              <div className="flex items-center space-x-3">
                <Select defaultValue="30">
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 3 months</SelectItem>
                    <SelectItem value="365">Last year</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </Button>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-900">User Growth Rate</p>
                      <p className="text-3xl font-bold text-blue-900">+12.5%</p>
                      <p className="text-sm text-blue-700">vs last month</p>
                    </div>
                    <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <Progress value={75} className="mt-4 h-2" />
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-900">Donation Growth</p>
                      <p className="text-3xl font-bold text-green-900">+8.5%</p>
                      <p className="text-sm text-green-700">vs last month</p>
                    </div>
                    <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                      <DollarSign className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <Progress value={85} className="mt-4 h-2" />
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-900">Volunteer Engagement</p>
                      <p className="text-3xl font-bold text-purple-900">+15.2%</p>
                      <p className="text-sm text-purple-700">vs last month</p>
                    </div>
                    <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                      <Activity className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <Progress value={92} className="mt-4 h-2" />
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-900">Program Success Rate</p>
                      <p className="text-3xl font-bold text-orange-900">88.7%</p>
                      <p className="text-sm text-orange-700">completion rate</p>
                    </div>
                    <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                      <Target className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <Progress value={89} className="mt-4 h-2" />
                </CardContent>
              </Card>
            </div>

            {/* Charts and Detailed Analytics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart className="h-5 w-5 mr-2 text-blue-600" />
                    User Growth Trends
                  </CardTitle>
                  <CardDescription>Monthly user registration and engagement</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.user_growth.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.month}</span>
                        <div className="flex items-center space-x-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${(item.count / 320) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-12 text-right">{item.count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <LineChart className="h-5 w-5 mr-2 text-green-600" />
                    Donation Trends
                  </CardTitle>
                  <CardDescription>Monthly donation amounts and patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.donation_trends.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.month}</span>
                        <div className="flex items-center space-x-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${(item.amount / 35000) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-16 text-right">{formatCurrency(item.amount)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="h-5 w-5 mr-2 text-purple-600" />
                    Program Performance
                  </CardTitle>
                  <CardDescription>Success rates by program type</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.program_performance.map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{item.program}</span>
                          <span className="text-sm text-gray-600">{item.completion_rate}%</span>
                        </div>
                        <Progress value={item.completion_rate} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2 text-orange-600" />
                    Volunteer Engagement
                  </CardTitle>
                  <CardDescription>Monthly volunteer hours and participation</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData?.volunteer_engagement.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{item.month}</span>
                        <div className="flex items-center space-x-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-orange-600 h-2 rounded-full"
                              style={{ width: `${(item.hours / 950) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-12 text-right">{item.hours}h</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Insights */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg">Top Performing Programs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="font-medium text-green-900">Education Support</p>
                        <p className="text-sm text-green-700">92% success rate</p>
                      </div>
                      <Award className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <p className="font-medium text-blue-900">Healthcare Access</p>
                        <p className="text-sm text-blue-700">88% success rate</p>
                      </div>
                      <Heart className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div>
                        <p className="font-medium text-purple-900">Women Empowerment</p>
                        <p className="text-sm text-purple-700">85% success rate</p>
                      </div>
                      <Star className="h-5 w-5 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg">Geographic Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Lagos State</span>
                      <span className="text-sm text-gray-600">35%</span>
                    </div>
                    <Progress value={35} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Abuja FCT</span>
                      <span className="text-sm text-gray-600">25%</span>
                    </div>
                    <Progress value={25} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Kano State</span>
                      <span className="text-sm text-gray-600">20%</span>
                    </div>
                    <Progress value={20} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Others</span>
                      <span className="text-sm text-gray-600">20%</span>
                    </div>
                    <Progress value={20} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
                <CardHeader>
                  <CardTitle className="text-lg">Platform Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">System Uptime</span>
                      </div>
                      <span className="text-sm text-green-600">99.9%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium">Response Time</span>
                      </div>
                      <span className="text-sm text-blue-600">120ms</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                        <span className="text-sm font-medium">Active Sessions</span>
                      </div>
                      <span className="text-sm text-purple-600">1,247</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-3 w-3 bg-orange-500 rounded-full"></div>
                        <span className="text-sm font-medium">Error Rate</span>
                      </div>
                      <span className="text-sm text-orange-600">0.1%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* User Creation Dialog */}
        <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>Create a new user account</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    value={userFormData.first_name}
                    onChange={(e) => setUserFormData(prev => ({ ...prev, first_name: e.target.value }))}
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    value={userFormData.last_name}
                    onChange={(e) => setUserFormData(prev => ({ ...prev, last_name: e.target.value }))}
                    placeholder="Enter last name"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={userFormData.email}
                  onChange={(e) => setUserFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter email address"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select
                    value={userFormData.role}
                    onValueChange={(value) => setUserFormData(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="student">Student</SelectItem>
                      <SelectItem value="volunteer">Volunteer</SelectItem>
                      <SelectItem value="partner">Partner</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={userFormData.status}
                    onValueChange={(value) => setUserFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setUserDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateUser}>
                  Create User
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
