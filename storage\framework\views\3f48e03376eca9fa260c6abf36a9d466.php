

<?php $__env->startSection('title', 'Team Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Team Management</h1>
        <a href="<?php echo e(route('admin.team.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Team Member
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Team Members</div>
                            <div class="h4"><?php echo e(method_exists($teamMembers, 'total') ? $teamMembers->total() : $teamMembers->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Members</div>
                            <div class="h4"><?php echo e($teamMembers->where('is_active', true)->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Departments</div>
                            <div class="h4"><?php echo e($teamMembers->pluck('department')->unique()->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Leadership</div>
                            <div class="h4"><?php echo e($teamMembers->whereIn('position', ['Director', 'Manager', 'Coordinator'])->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Members Grid/Table Toggle -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Team Members</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary active" id="gridView">
                        <i class="fas fa-th"></i> Grid
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="listView">
                        <i class="fas fa-list"></i> List
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search team members..." id="searchTeam">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterDepartment">
                        <option value="">All Departments</option>
                        <option value="leadership">Leadership</option>
                        <option value="programs">Programs</option>
                        <option value="outreach">Outreach</option>
                        <option value="operations">Operations</option>
                        <option value="finance">Finance</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>

            <!-- Grid View -->
            <div id="gridContainer">
                <div class="row">
                    <?php $__empty_1 = true; $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 team-member-card">
                            <div class="position-relative">
                                <?php if($member->photo): ?>
                                    <img src="<?php echo e($member->photo); ?>" class="card-img-top" alt="<?php echo e($member->name); ?>" style="height: 250px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                        <i class="fas fa-user fa-4x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <?php if(!$member->is_active): ?>
                                    <span class="position-absolute top-0 end-0 badge bg-secondary m-2">Inactive</span>
                                <?php endif; ?>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?php echo e($member->name); ?></h5>
                                <p class="card-text">
                                    <strong><?php echo e($member->position); ?></strong>
                                    <?php if($member->department): ?>
                                        <br><small class="text-muted"><?php echo e($member->department); ?></small>
                                    <?php endif; ?>
                                </p>
                                <?php if($member->bio): ?>
                                    <p class="card-text text-muted small"><?php echo e(Str::limit($member->bio, 100)); ?></p>
                                <?php endif; ?>
                                
                                <!-- Social Links -->
                                <?php if($member->email || $member->linkedin_url || $member->twitter_url): ?>
                                    <div class="mt-auto">
                                        <div class="d-flex gap-2">
                                            <?php if($member->email): ?>
                                                <a href="mailto:<?php echo e($member->email); ?>" class="btn btn-sm btn-outline-primary" title="Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->linkedin_url): ?>
                                                <a href="<?php echo e($member->linkedin_url); ?>" target="_blank" class="btn btn-sm btn-outline-info" title="LinkedIn">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->twitter_url): ?>
                                                <a href="<?php echo e($member->twitter_url); ?>" target="_blank" class="btn btn-sm btn-outline-info" title="Twitter">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="<?php echo e(route('admin.team.show', $member->id)); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="<?php echo e(route('admin.team.edit', $member->id)); ?>" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteMember(<?php echo e($member->id); ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No team members found</p>
                            <a href="<?php echo e(route('admin.team.create')); ?>" class="btn btn-primary">Add Your First Team Member</a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- List View (Initially Hidden) -->
            <div id="listContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Photo</th>
                                <th>Name</th>
                                <th>Position</th>
                                <th>Department</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($member->photo): ?>
                                        <img src="<?php echo e($member->photo); ?>" alt="<?php echo e($member->name); ?>" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                    <?php else: ?>
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold"><?php echo e($member->name); ?></div>
                                        <small class="text-muted"><?php echo e($member->slug); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <strong><?php echo e($member->position); ?></strong>
                                </td>
                                <td>
                                    <?php if($member->department): ?>
                                        <span class="badge bg-secondary"><?php echo e($member->department); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Not specified</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($member->email): ?>
                                        <div>
                                            <a href="mailto:<?php echo e($member->email); ?>" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i><?php echo e($member->email); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <div class="mt-1">
                                        <?php if($member->linkedin_url): ?>
                                            <a href="<?php echo e($member->linkedin_url); ?>" target="_blank" class="text-decoration-none me-2">
                                                <i class="fab fa-linkedin text-primary"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if($member->twitter_url): ?>
                                            <a href="<?php echo e($member->twitter_url); ?>" target="_blank" class="text-decoration-none">
                                                <i class="fab fa-twitter text-info"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($member->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($member->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.team.show', $member->id)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.team.edit', $member->id)); ?>" class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMember(<?php echo e($member->id); ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No team members found</p>
                                    <a href="<?php echo e(route('admin.team.create')); ?>" class="btn btn-primary">Add Your First Team Member</a>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Showing <?php echo e($teamMembers->firstItem()); ?> to <?php echo e($teamMembers->lastItem()); ?> of <?php echo e($teamMembers->total()); ?> results
                </div>
                <?php echo e($teamMembers->links()); ?>

            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this team member? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteMember">Delete Member</button>
            </div>
        </div>
    </div>
</div>

<script>
// View toggle functionality
document.getElementById('gridView').addEventListener('click', function() {
    document.getElementById('gridContainer').style.display = 'block';
    document.getElementById('listContainer').style.display = 'none';
    this.classList.add('active');
    document.getElementById('listView').classList.remove('active');
});

document.getElementById('listView').addEventListener('click', function() {
    document.getElementById('gridContainer').style.display = 'none';
    document.getElementById('listContainer').style.display = 'block';
    this.classList.add('active');
    document.getElementById('gridView').classList.remove('active');
});

function deleteMember(memberId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteMemberModal'));
    modal.show();
    
    document.getElementById('confirmDeleteMember').onclick = function() {
        console.log('Delete member:', memberId);
        modal.hide();
        // Add AJAX call to delete team member
        // After success, remove the card/row from the page
    };
}

// Search functionality
document.getElementById('searchTeam').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const cards = document.querySelectorAll('.team-member-card');
    const rows = document.querySelectorAll('#listContainer tbody tr');
    
    // Filter grid view
    cards.forEach(card => {
        const name = card.querySelector('.card-title').textContent.toLowerCase();
        const position = card.querySelector('.card-text strong').textContent.toLowerCase();
        
        if (name.includes(searchTerm) || position.includes(searchTerm)) {
            card.closest('.col-xl-3').style.display = 'block';
        } else {
            card.closest('.col-xl-3').style.display = 'none';
        }
    });
    
    // Filter list view
    rows.forEach(row => {
        if (row.cells.length > 1) { // Skip empty state row
            const name = row.cells[1].textContent.toLowerCase();
            const position = row.cells[2].textContent.toLowerCase();
            
            if (name.includes(searchTerm) || position.includes(searchTerm)) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        }
    });
});

// Filter functionality
document.getElementById('filterDepartment').addEventListener('change', function() {
    console.log('Filter department:', this.value);
    // Implement department filtering
});

document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
    // Implement status filtering
});

// Add hover effects for team member cards
document.querySelectorAll('.team-member-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.transition = 'transform 0.3s ease';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
});
</script>

<style>
.team-member-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/team/index.blade.php ENDPATH**/ ?>