<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Donation;
use App\Models\DonationCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Donations",
 *     description="Donation and campaign management endpoints"
 * )
 */
class DonationController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/donations",
     *     summary="Create a new donation",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"amount","currency"},
     *             @OA\Property(property="amount", type="number", format="float", example=50.00),
     *             @OA\Property(property="currency", type="string", example="NGN"),
     *             @OA\Property(property="donation_type", type="string", enum={"one-time","recurring"}, example="one-time"),
     *             @OA\Property(property="frequency", type="string", enum={"monthly","quarterly","annually"}, example="monthly"),
     *             @OA\Property(property="purpose", type="string", example="Education Program"),
     *             @OA\Property(property="campaign_id", type="integer", example=1),
     *             @OA\Property(property="is_anonymous", type="boolean", example=false),
     *             @OA\Property(property="notes", type="string", example="Keep up the good work!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Donation created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Donation created successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:1',
            'currency' => 'required|string|size:3',
            'donation_type' => 'required|in:one-time,recurring',
            'frequency' => 'required_if:donation_type,recurring|in:monthly,quarterly,annually',
            'purpose' => 'nullable|string|max:200',
            'campaign_id' => 'nullable|exists:donation_campaigns,id',
            'is_anonymous' => 'boolean',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        $donation = Donation::create([
            'donor_id' => $user->id,
            'donor_email' => $user->email,
            'donor_name' => $user->full_name,
            'amount' => $request->amount,
            'currency' => $request->currency,
            'donation_type' => $request->donation_type,
            'frequency' => $request->frequency,
            'purpose' => $request->purpose,
            'campaign_id' => $request->campaign_id,
            'transaction_reference' => 'TXN_' . Str::random(16),
            'payment_status' => 'pending',
            'is_anonymous' => $request->boolean('is_anonymous'),
            'notes' => $request->notes,
        ]);

        // TODO: Initialize payment with Paystack
        // TODO: Return payment URL for frontend

        return response()->json([
            'success' => true,
            'message' => 'Donation created successfully',
            'data' => [
                'donation' => $donation,
                'payment_url' => 'https://paystack.com/pay/' . $donation->transaction_reference, // Mock URL
            ]
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/my-donations",
     *     summary="Get user's donation history",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Donations retrieved successfully"
     *     )
     * )
     */
    public function myDonations(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 15);

        $donations = $user->donations()
            ->with(['campaign'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $donations
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/{id}/receipt",
     *     summary="Get donation receipt",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Donation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Receipt retrieved successfully"
     *     )
     * )
     */
    public function receipt($id): JsonResponse
    {
        $user = Auth::user();
        $donation = $user->donations()->findOrFail($id);

        if (!$donation->isCompleted()) {
            return response()->json([
                'success' => false,
                'message' => 'Receipt not available for pending donations'
            ], 400);
        }

        if (!$donation->receipt_number) {
            $donation->generateReceiptNumber();
        }

        return response()->json([
            'success' => true,
            'data' => [
                'donation' => $donation,
                'receipt_url' => url("/api/v1/donations/{$donation->id}/receipt/download"),
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/campaigns",
     *     summary="Get active donation campaigns",
     *     tags={"Donations"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Campaigns retrieved successfully"
     *     )
     * )
     */
    public function campaigns(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);

        $campaigns = DonationCampaign::active()
            ->with(['creator'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $campaigns
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/campaigns/{id}",
     *     summary="Get campaign details",
     *     tags={"Donations"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Campaign ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Campaign details retrieved successfully"
     *     )
     * )
     */
    public function showCampaign($id): JsonResponse
    {
        $campaign = DonationCampaign::with(['creator', 'completedDonations'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'campaign' => $campaign,
                'recent_donations' => $campaign->completedDonations()
                    ->with(['donor'])
                    ->where('is_anonymous', false)
                    ->latest()
                    ->take(10)
                    ->get(),
                'top_donors' => $campaign->completedDonations()
                    ->selectRaw('donor_id, donor_name, SUM(amount) as total_amount')
                    ->where('is_anonymous', false)
                    ->groupBy('donor_id', 'donor_name')
                    ->orderBy('total_amount', 'desc')
                    ->take(5)
                    ->get(),
            ]
        ]);
    }

    /**
     * Handle payment callback from Paystack
     */
    public function paymentCallback(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string',
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid callback data'
            ], 422);
        }

        $donation = Donation::where('transaction_reference', $request->reference)->first();

        if (!$donation) {
            return response()->json([
                'success' => false,
                'message' => 'Donation not found'
            ], 404);
        }

        // TODO: Verify payment with Paystack API
        // For now, just update based on callback status
        
        if ($request->status === 'success') {
            $donation->markAsCompleted();
            
            // Update campaign raised amount if applicable
            if ($donation->campaign_id) {
                $donation->campaign->addDonation($donation->amount);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'data' => $donation
            ]);
        } else {
            $donation->update(['payment_status' => 'failed']);
            
            return response()->json([
                'success' => false,
                'message' => 'Payment failed'
            ], 400);
        }
    }
}
