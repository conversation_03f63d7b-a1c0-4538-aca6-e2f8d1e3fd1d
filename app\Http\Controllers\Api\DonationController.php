<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Donation;
use App\Models\DonationCampaign;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Donations",
 *     description="Donation and campaign management endpoints"
 * )
 */
class DonationController extends Controller
{
    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }
    /**
     * @OA\Post(
     *     path="/api/v1/donations",
     *     summary="Create a new donation",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"amount","currency"},
     *             @OA\Property(property="amount", type="number", format="float", example=50.00),
     *             @OA\Property(property="currency", type="string", example="NGN"),
     *             @OA\Property(property="donation_type", type="string", enum={"one-time","recurring"}, example="one-time"),
     *             @OA\Property(property="frequency", type="string", enum={"monthly","quarterly","annually"}, example="monthly"),
     *             @OA\Property(property="purpose", type="string", example="Education Program"),
     *             @OA\Property(property="campaign_id", type="integer", example=1),
     *             @OA\Property(property="is_anonymous", type="boolean", example=false),
     *             @OA\Property(property="notes", type="string", example="Keep up the good work!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Donation created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Donation created successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'donor_name' => 'required|string|max:100',
            'donor_email' => 'required|email|max:100',
            'donor_phone' => 'nullable|string|max:20',
            'amount' => 'required|numeric|min:100',
            'currency' => 'nullable|string|size:3',
            'donation_type' => 'required|in:one-time,recurring',
            'frequency' => 'required_if:donation_type,recurring|in:monthly,quarterly,annually',
            'purpose' => 'nullable|string|max:200',
            'campaign_id' => 'nullable|exists:donation_campaigns,id',
            'payment_method' => 'required|in:paystack,bank_transfer',
            'is_anonymous' => 'boolean',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get authenticated user if available, otherwise allow anonymous donations
        $user = Auth::user();

        $donation = Donation::create([
            'donor_id' => $user ? $user->id : null,
            'donor_email' => $request->donor_email,
            'donor_name' => $request->donor_name,
            'donor_phone' => $request->donor_phone,
            'amount' => $request->amount,
            'currency' => $request->currency ?? 'NGN',
            'donation_type' => $request->donation_type,
            'frequency' => $request->frequency,
            'purpose' => $request->purpose,
            'campaign_id' => $request->campaign_id,
            'payment_method' => $request->payment_method,
            'payment_provider' => $request->payment_method === 'paystack' ? 'paystack' : 'bank_transfer',
            'transaction_reference' => 'TXN_' . strtoupper(Str::random(16)),
            'payment_status' => $request->payment_method === 'bank_transfer' ? 'pending' : 'pending',
            'is_anonymous' => $request->boolean('is_anonymous'),
            'notes' => $request->notes,
        ]);

        // Handle different payment methods
        if ($request->payment_method === 'paystack') {
            // Initialize payment with Paystack
            $paymentResult = $this->paystackService->initializePayment(
                $request->donor_email,
                $request->amount,
                $donation->transaction_reference,
                [
                    'donation_id' => $donation->id,
                    'donor_name' => $request->donor_name,
                    'purpose' => $request->purpose,
                    'campaign_id' => $request->campaign_id,
                ]
            );

            if (!$paymentResult['status']) {
                // Delete the donation if payment initialization fails
                $donation->delete();
                return response()->json([
                    'success' => false,
                    'message' => 'Payment initialization failed: ' . $paymentResult['message']
                ], 400);
            }

            // Update donation with PayStack reference
            $donation->update([
                'transaction_reference' => $paymentResult['reference'],
                'payment_metadata' => json_encode($paymentResult['data']),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Donation created successfully. Redirecting to payment...',
                'data' => [
                    'donation' => $donation,
                    'payment_url' => $paymentResult['authorization_url'],
                    'transaction_reference' => $paymentResult['reference'],
                    'access_code' => $paymentResult['access_code'],
                ]
            ], 201);
        } else {
            // Bank transfer - return bank details from settings
            $settings = \App\Models\Setting::getPublicSettings();

            return response()->json([
                'success' => true,
                'message' => 'Donation created successfully. Please complete the bank transfer.',
                'data' => [
                    'donation' => $donation,
                    'payment_method' => 'bank_transfer',
                    'bank_details' => [
                        'account_name' => $settings['donation_account_name'] ?? 'HALIMAKQ Foundation',
                        'account_number' => $settings['donation_account_number'] ?? '**********',
                        'bank_name' => $settings['donation_bank_name'] ?? 'First Bank of Nigeria',
                    ],
                    'transaction_reference' => $donation->transaction_reference,
                    'instructions' => 'Please use your email address as the transfer reference for easy identification.'
                ]
            ], 201);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/my-donations",
     *     summary="Get user's donation history",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Donations retrieved successfully"
     *     )
     * )
     */
    public function myDonations(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 15);

        $donations = $user->donations()
            ->with(['campaign'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $donations
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/{id}/receipt",
     *     summary="Get donation receipt",
     *     tags={"Donations"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Donation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Receipt retrieved successfully"
     *     )
     * )
     */
    public function receipt($id): JsonResponse
    {
        $user = Auth::user();
        $donation = $user->donations()->findOrFail($id);

        if (!$donation->isCompleted()) {
            return response()->json([
                'success' => false,
                'message' => 'Receipt not available for pending donations'
            ], 400);
        }

        if (!$donation->receipt_number) {
            $donation->generateReceiptNumber();
        }

        return response()->json([
            'success' => true,
            'data' => [
                'donation' => $donation,
                'receipt_url' => url("/api/v1/donations/{$donation->id}/receipt/download"),
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/campaigns",
     *     summary="Get active donation campaigns",
     *     tags={"Donations"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Campaigns retrieved successfully"
     *     )
     * )
     */
    public function campaigns(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);

        $campaigns = DonationCampaign::active()
            ->with(['creator'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $campaigns
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/donations/campaigns/{id}",
     *     summary="Get campaign details",
     *     tags={"Donations"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Campaign ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Campaign details retrieved successfully"
     *     )
     * )
     */
    public function showCampaign($id): JsonResponse
    {
        $campaign = DonationCampaign::with(['creator', 'completedDonations'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'campaign' => $campaign,
                'recent_donations' => $campaign->completedDonations()
                    ->with(['donor'])
                    ->where('is_anonymous', false)
                    ->latest()
                    ->take(10)
                    ->get(),
                'top_donors' => $campaign->completedDonations()
                    ->selectRaw('donor_id, donor_name, SUM(amount) as total_amount')
                    ->where('is_anonymous', false)
                    ->groupBy('donor_id', 'donor_name')
                    ->orderBy('total_amount', 'desc')
                    ->take(5)
                    ->get(),
            ]
        ]);
    }

    /**
     * Verify payment status
     */
    public function verifyPayment($reference): JsonResponse
    {
        $donation = Donation::where('transaction_reference', $reference)->first();

        if (!$donation) {
            return response()->json([
                'success' => false,
                'message' => 'Donation not found'
            ], 404);
        }

        // Verify payment with Paystack API if it's a PayStack payment
        if ($donation->payment_method === 'paystack' && $donation->payment_status === 'pending') {
            $verificationResult = $this->paystackService->verifyPayment($reference);

            if ($verificationResult['status']) {
                // Payment successful - update donation
                $donation->update([
                    'payment_status' => 'completed',
                    'paid_at' => now(),
                    'payment_metadata' => json_encode($verificationResult['data']),
                ]);

                // Generate receipt number if not exists
                if (!$donation->receipt_number) {
                    $donation->generateReceiptNumber();
                }
            } else {
                // Payment failed
                $donation->update([
                    'payment_status' => 'failed',
                    'payment_metadata' => json_encode(['error' => $verificationResult['message']]),
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment status retrieved',
            'data' => [
                'donation' => $donation->fresh(), // Get updated donation
                'status' => $donation->payment_status,
                'is_completed' => $donation->isCompleted(),
                'verification_result' => $donation->payment_method === 'paystack' ? $verificationResult ?? null : null,
            ]
        ]);
    }

    /**
     * Handle payment callback from Paystack
     */
    public function paymentCallback(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string',
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid callback data'
            ], 422);
        }

        $donation = Donation::where('transaction_reference', $request->reference)->first();

        if (!$donation) {
            return response()->json([
                'success' => false,
                'message' => 'Donation not found'
            ], 404);
        }

        // Verify payment with Paystack API
        $verificationResult = $this->paystackService->verifyPayment($request->reference);

        if ($verificationResult['status']) {
            // Payment successful
            $donation->update([
                'payment_status' => 'completed',
                'paid_at' => now(),
                'payment_metadata' => json_encode($verificationResult['data']),
            ]);

            // Generate receipt number if not exists
            if (!$donation->receipt_number) {
                $donation->generateReceiptNumber();
            }

            // Update campaign raised amount if applicable
            if ($donation->campaign_id) {
                $donation->campaign->increment('raised_amount', $donation->amount);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'data' => $donation->fresh()
            ]);
        } else {
            // Payment verification failed
            $donation->update([
                'payment_status' => 'failed',
                'payment_metadata' => json_encode(['error' => $verificationResult['message']]),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed: ' . $verificationResult['message']
            ], 400);
        }
    }

    /**
     * Handle PayStack webhook
     */
    public function webhook(Request $request): JsonResponse
    {
        // Validate webhook signature
        $signature = $request->header('x-paystack-signature');
        $input = $request->getContent();

        if (!$this->paystackService->validateWebhook($input, $signature)) {
            return response()->json(['message' => 'Invalid signature'], 400);
        }

        $event = json_decode($input, true);

        if ($event['event'] === 'charge.success') {
            $reference = $event['data']['reference'];
            $donation = Donation::where('transaction_reference', $reference)->first();

            if ($donation && $donation->payment_status === 'pending') {
                $donation->update([
                    'payment_status' => 'completed',
                    'paid_at' => now(),
                    'payment_metadata' => json_encode($event['data']),
                ]);

                // Generate receipt number if not exists
                if (!$donation->receipt_number) {
                    $donation->generateReceiptNumber();
                }

                // Update campaign raised amount if applicable
                if ($donation->campaign_id) {
                    $donation->campaign->increment('raised_amount', $donation->amount);
                }
            }
        }

        return response()->json(['message' => 'Webhook processed'], 200);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/donations/{id}/upload-receipt",
     *     summary="Upload receipt for bank transfer donation",
     *     tags={"Donations"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="receipt",
     *                     type="string",
     *                     format="binary",
     *                     description="Receipt file (JPG, PNG, PDF)"
     *                 ),
     *                 @OA\Property(
     *                     property="sender_name",
     *                     type="string",
     *                     description="Name of the sender"
     *                 ),
     *                 @OA\Property(
     *                     property="sender_bank",
     *                     type="string",
     *                     description="Sender's bank name"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Receipt uploaded successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Receipt uploaded successfully"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="receipt_path", type="string", example="receipts/donation_123_receipt.pdf")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Donation not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function uploadReceipt(Request $request, $id): JsonResponse
    {
        try {
            $donation = Donation::findOrFail($id);

            // Validate that this is a bank transfer donation
            if ($donation->payment_method !== 'bank_transfer') {
                return response()->json([
                    'success' => false,
                    'message' => 'Receipt upload is only available for bank transfer donations'
                ], 400);
            }

            // Validate the request
            $validator = Validator::make($request->all(), [
                'receipt' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
                'sender_name' => 'required|string|max:255',
                'sender_bank' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle file upload
            if ($request->hasFile('receipt')) {
                $file = $request->file('receipt');
                $filename = 'donation_' . $donation->id . '_receipt_' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('receipts', $filename, 'public');

                // Update donation with receipt information
                $donation->update([
                    'receipt_path' => $path,
                    'sender_name' => $request->sender_name,
                    'sender_bank' => $request->sender_bank,
                    'payment_status' => 'pending_verification', // Admin needs to verify
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Receipt uploaded successfully. Your donation is now pending admin verification.',
                    'data' => [
                        'receipt_path' => $path,
                        'status' => 'pending_verification'
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No receipt file provided'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload receipt: ' . $e->getMessage()
            ], 500);
        }
    }
}
