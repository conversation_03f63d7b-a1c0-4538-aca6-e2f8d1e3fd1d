import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { AnimatedSection } from "@/components/animated-section"

export function BlogNewsletter() {
  return (
    <AnimatedSection className="py-20 bg-green-900 text-white">
      <div className="container">
        <div className="max-w-2xl mx-auto text-center space-y-6">
          <h2 className="text-3xl font-bold">Stay Updated</h2>
          <p className="text-green-100">
            Subscribe to our newsletter to receive the latest stories, updates, and insights from our work across
            Nigeria.
          </p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              placeholder="Enter your email"
              className="bg-white/20 border-green-700 placeholder:text-green-200/50 text-white flex-1 rounded-l-full rounded-r-full sm:rounded-r-none"
            />
            <Button className="bg-amber-500 hover:bg-amber-600 text-green-950 transition-all duration-200 hover:scale-105 rounded-full sm:rounded-l-none">
              Subscribe
            </Button>
          </form>
        </div>
      </div>
    </AnimatedSection>
  )
}
