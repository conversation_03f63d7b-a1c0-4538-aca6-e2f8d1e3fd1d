<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('event_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['registered', 'attended', 'cancelled'])->default('registered');
            $table->timestamp('registered_at')->default(now());
            $table->timestamp('checked_in_at')->nullable();
            $table->json('additional_info')->nullable();
            $table->timestamps();
            
            // Unique constraint
            $table->unique(['event_id', 'user_id'], 'unique_registration');
            
            // Indexes
            $table->index(['event_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('registered_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('event_registrations');
    }
}; 