<?php

namespace App\Services;

use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use App\Models\User;
use App\Notifications\StudentProgressionNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;
use Exception;

class ProgressionNotificationService
{
    protected $studentProgressionService;
    protected $scholarshipEligibilityService;

    public function __construct(
        StudentProgressionService $studentProgressionService = null,
        ScholarshipEligibilityService $scholarshipEligibilityService = null
    ) {
        $this->studentProgressionService = $studentProgressionService ?? new StudentProgressionService();
        $this->scholarshipEligibilityService = $scholarshipEligibilityService ?? new ScholarshipEligibilityService();
    }

    /**
     * Send progression eligibility notifications
     */
    public function sendProgressionEligibilityNotifications(PartnerOrganization $partnerOrg = null): array
    {
        try {
            $results = [
                'notifications_sent' => 0,
                'organizations_notified' => 0,
                'eligible_students' => 0,
                'errors' => [],
            ];

            if ($partnerOrg) {
                $organizations = collect([$partnerOrg]);
            } else {
                $organizations = PartnerOrganization::with('user')->get();
            }

            foreach ($organizations as $organization) {
                try {
                    $eligibleStudents = $this->studentProgressionService->getEligibleStudents($organization);
                    
                    if ($eligibleStudents->isEmpty()) {
                        continue;
                    }

                    $results['eligible_students'] += $eligibleStudents->count();
                    $results['organizations_notified']++;

                    // Send individual notifications for each eligible student
                    foreach ($eligibleStudents as $student) {
                        $this->sendProgressionEligibilityNotification($student, $organization);
                        $results['notifications_sent']++;
                    }

                    // Send summary notification if multiple students are eligible
                    if ($eligibleStudents->count() > 1) {
                        $this->sendBulkProgressionSummaryNotification($organization, $eligibleStudents);
                        $results['notifications_sent']++;
                    }

                } catch (Exception $e) {
                    $results['errors'][] = [
                        'organization_id' => $organization->id,
                        'organization_name' => $organization->name,
                        'error' => $e->getMessage(),
                    ];
                    
                    Log::error("Failed to send progression notifications for organization", [
                        'organization_id' => $organization->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Progression eligibility notifications completed", $results);
            return $results;

        } catch (Exception $e) {
            Log::error("Failed to send progression eligibility notifications", [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send notification when progression is completed
     */
    public function sendProgressionCompletedNotification(StudentProgression $progression): void
    {
        try {
            $student = $progression->student;
            $partnerOrg = $student->school;
            
            if (!$partnerOrg || !$partnerOrg->user) {
                Log::warning("No partner organization or user found for progression completion notification", [
                    'progression_id' => $progression->id,
                    'student_id' => $student->id
                ]);
                return;
            }

            $partnerOrg->user->notify(new StudentProgressionNotification(
                $student,
                $progression,
                'progression_completed'
            ));

            // Check for new scholarship eligibility after progression
            $this->checkAndNotifyScholarshipEligibility($student);

            Log::info("Progression completed notification sent", [
                'progression_id' => $progression->id,
                'student_id' => $student->id,
                'organization_id' => $partnerOrg->id
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send progression completed notification", [
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send notification when progression is pending
     */
    public function sendProgressionPendingNotification(StudentProgression $progression): void
    {
        try {
            $student = $progression->student;
            $partnerOrg = $student->school;
            
            if (!$partnerOrg || !$partnerOrg->user) {
                Log::warning("No partner organization or user found for progression pending notification", [
                    'progression_id' => $progression->id,
                    'student_id' => $student->id
                ]);
                return;
            }

            $partnerOrg->user->notify(new StudentProgressionNotification(
                $student,
                $progression,
                'progression_pending'
            ));

            Log::info("Progression pending notification sent", [
                'progression_id' => $progression->id,
                'student_id' => $student->id,
                'organization_id' => $partnerOrg->id
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send progression pending notification", [
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send scholarship eligibility notification
     */
    public function sendScholarshipEligibilityNotification(Student $student, array $scholarships = []): void
    {
        try {
            $partnerOrg = $student->school;
            
            if (!$partnerOrg || !$partnerOrg->user) {
                Log::warning("No partner organization or user found for scholarship eligibility notification", [
                    'student_id' => $student->id
                ]);
                return;
            }

            $additionalData = [
                'scholarship_count' => count($scholarships),
                'scholarships' => $scholarships,
                'academic_year' => $this->getCurrentAcademicYear(),
            ];

            $partnerOrg->user->notify(new StudentProgressionNotification(
                $student,
                null,
                'scholarship_eligible',
                $additionalData
            ));

            Log::info("Scholarship eligibility notification sent", [
                'student_id' => $student->id,
                'organization_id' => $partnerOrg->id,
                'scholarship_count' => count($scholarships)
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send scholarship eligibility notification", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send daily progression summary to all partner organizations
     */
    public function sendDailyProgressionSummary(): array
    {
        try {
            $results = [
                'summaries_sent' => 0,
                'organizations_processed' => 0,
                'errors' => [],
            ];

            $organizations = PartnerOrganization::with('user')->get();

            foreach ($organizations as $organization) {
                try {
                    $summary = $this->generateOrganizationProgressionSummary($organization);
                    
                    if ($summary['has_activity']) {
                        $this->sendBulkProgressionSummaryNotification($organization, null, $summary);
                        $results['summaries_sent']++;
                    }

                    $results['organizations_processed']++;

                } catch (Exception $e) {
                    $results['errors'][] = [
                        'organization_id' => $organization->id,
                        'organization_name' => $organization->name,
                        'error' => $e->getMessage(),
                    ];
                    
                    Log::error("Failed to send daily summary for organization", [
                        'organization_id' => $organization->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Daily progression summaries completed", $results);
            return $results;

        } catch (Exception $e) {
            Log::error("Failed to send daily progression summaries", [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Private helper methods
     */
    private function sendProgressionEligibilityNotification(Student $student, PartnerOrganization $partnerOrg): void
    {
        if (!$partnerOrg->user) {
            return;
        }

        $additionalData = [
            'academic_year' => $this->getCurrentAcademicYear(),
            'next_grade' => $this->getNextGrade($student->grade_level),
        ];

        $partnerOrg->user->notify(new StudentProgressionNotification(
            $student,
            null,
            'progression_eligible',
            $additionalData
        ));
    }

    private function sendBulkProgressionSummaryNotification(PartnerOrganization $partnerOrg, $eligibleStudents = null, array $summary = null): void
    {
        if (!$partnerOrg->user) {
            return;
        }

        if ($summary) {
            $additionalData = $summary;
        } else {
            $additionalData = [
                'total_students' => Student::where('school_id', $partnerOrg->id)->count(),
                'eligible_students' => $eligibleStudents ? $eligibleStudents->count() : 0,
                'completed_progressions' => StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                    $query->where('school_id', $partnerOrg->id);
                })->where('status', 'completed')->whereDate('completed_at', today())->count(),
                'academic_year' => $this->getCurrentAcademicYear(),
            ];
        }

        // Use a dummy student for bulk notifications
        $dummyStudent = new Student(['full_name' => 'Multiple Students', 'grade_level' => 'various']);

        $partnerOrg->user->notify(new StudentProgressionNotification(
            $dummyStudent,
            null,
            'bulk_progression_summary',
            $additionalData
        ));
    }

    private function checkAndNotifyScholarshipEligibility(Student $student): void
    {
        try {
            $eligibleScholarships = $this->scholarshipEligibilityService->checkEligibilityAfterProgression($student);
            
            if (!empty($eligibleScholarships)) {
                $this->sendScholarshipEligibilityNotification($student, $eligibleScholarships);
            }
        } catch (Exception $e) {
            Log::error("Failed to check scholarship eligibility after progression", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function generateOrganizationProgressionSummary(PartnerOrganization $organization): array
    {
        $today = Carbon::today();
        
        $totalStudents = Student::where('school_id', $organization->id)->count();
        $eligibleStudents = $this->studentProgressionService->getEligibleStudents($organization)->count();
        $completedToday = StudentProgression::whereHas('student', function ($query) use ($organization) {
            $query->where('school_id', $organization->id);
        })->where('status', 'completed')->whereDate('completed_at', $today)->count();
        
        $pendingProgressions = StudentProgression::whereHas('student', function ($query) use ($organization) {
            $query->where('school_id', $organization->id);
        })->where('status', 'pending')->count();

        return [
            'total_students' => $totalStudents,
            'eligible_students' => $eligibleStudents,
            'completed_progressions' => $completedToday,
            'pending_progressions' => $pendingProgressions,
            'academic_year' => $this->getCurrentAcademicYear(),
            'has_activity' => $eligibleStudents > 0 || $completedToday > 0 || $pendingProgressions > 0,
        ];
    }

    private function getCurrentAcademicYear(): string
    {
        $currentDate = Carbon::now();
        $year = $currentDate->year;
        return $currentDate->month >= 9 ? $year . '/' . ($year + 1) : ($year - 1) . '/' . $year;
    }

    private function getNextGrade(string $currentGrade): string
    {
        $gradeProgression = [
            'primary_1' => 'primary_2', 'primary_2' => 'primary_3', 'primary_3' => 'primary_4',
            'primary_4' => 'primary_5', 'primary_5' => 'primary_6', 'primary_6' => 'secondary_1',
            'secondary_1' => 'secondary_2', 'secondary_2' => 'secondary_3', 'secondary_3' => 'secondary_4',
            'secondary_4' => 'secondary_5', 'secondary_5' => 'secondary_6', 'secondary_6' => 'university',
        ];

        return $gradeProgression[$currentGrade] ?? $currentGrade;
    }
}
