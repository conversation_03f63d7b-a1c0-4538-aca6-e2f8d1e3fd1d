<?php $__env->startSection('title', 'Program Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Program Details</h1>
        <div>
            <a href="<?php echo e(route('admin.programs.index')); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Programs
            </a>
            <a href="<?php echo e(route('admin.programs.edit', $program->id)); ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Program
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Program Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h4 class="text-primary"><?php echo e($program->title); ?></h4>
                        <?php if($program->category): ?>
                            <span class="badge bg-secondary mb-3"><?php echo e(ucfirst(str_replace('_', ' ', $program->category))); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted">Description</h6>
                        <p class="text-justify"><?php echo e($program->description); ?></p>
                    </div>

                    <?php if($program->objectives): ?>
                    <div class="mb-4">
                        <h6 class="text-muted">Objectives</h6>
                        <?php if(is_array($program->objectives)): ?>
                            <ul class="list-unstyled">
                                <?php $__currentLoopData = $program->objectives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i><?php echo e($objective); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php else: ?>
                            <p><?php echo e($program->objectives); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if($program->target_beneficiaries): ?>
                    <div class="mb-4">
                        <h6 class="text-muted">Target Beneficiaries</h6>
                        <p><?php echo e($program->target_beneficiaries); ?></p>
                    </div>
                    <?php endif; ?>

                    <?php if($program->location): ?>
                    <div class="mb-4">
                        <h6 class="text-muted">Location</h6>
                        <p><i class="fas fa-map-marker-alt text-danger me-2"></i>
                            <?php if(is_array($program->location)): ?>
                                <?php echo e($program->location['address'] ?? 'Location not specified'); ?>

                            <?php else: ?>
                                <?php echo e($program->location); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <?php endif; ?>

                    <!-- Timeline -->
                    <?php if($program->start_date || $program->end_date): ?>
                    <div class="mb-4">
                        <h6 class="text-muted">Timeline</h6>
                        <div class="row">
                            <?php if($program->start_date): ?>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-success me-2"></i>
                                    <div>
                                        <small class="text-muted">Start Date</small>
                                        <p class="mb-0"><?php echo e($program->start_date->format('F j, Y')); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php if($program->end_date): ?>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-stop-circle text-danger me-2"></i>
                                    <div>
                                        <small class="text-muted">End Date</small>
                                        <p class="mb-0"><?php echo e($program->end_date->format('F j, Y')); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Featured Image Card -->
            <?php if($program->featured_image): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo e(asset('storage/' . $program->featured_image)); ?>" alt="<?php echo e($program->title); ?>" class="img-fluid rounded" style="max-height: 400px;">
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Status</h6>
                </div>
                <div class="card-body text-center">
                    <span class="badge bg-<?php echo e($program->status === 'active' ? 'success' : ($program->status === 'completed' ? 'info' : ($program->status === 'suspended' ? 'danger' : 'warning'))); ?> p-3 mb-3" style="font-size: 1.2rem;">
                        <i class="fas fa-<?php echo e($program->status === 'active' ? 'play' : ($program->status === 'completed' ? 'check' : ($program->status === 'suspended' ? 'pause' : 'clock'))); ?> me-2"></i>
                        <?php echo e(ucfirst($program->status)); ?>

                    </span>
                    <p class="text-muted mb-0">Current program status</p>
                </div>
            </div>

            <!-- Budget Card -->
            <?php if($program->budget): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Budget Information</h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-success mb-2">₦<?php echo e(number_format($program->budget)); ?></h3>
                    <p class="text-muted mb-0">Total Program Budget</p>
                </div>
            </div>
            <?php endif; ?>

            <!-- Program Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Program ID</small>
                        <p class="mb-0">#<?php echo e($program->id); ?></p>
                    </div>
                    <?php if($program->slug): ?>
                    <div class="mb-3">
                        <small class="text-muted">Slug</small>
                        <p class="mb-0"><?php echo e($program->slug); ?></p>
                    </div>
                    <?php endif; ?>
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0"><?php echo e($program->created_at->format('F j, Y g:i A')); ?></p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0"><?php echo e($program->updated_at->format('F j, Y g:i A')); ?></p>
                    </div>
                    <?php if($program->creator): ?>
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0"><?php echo e($program->creator->first_name); ?> <?php echo e($program->creator->last_name); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.programs.edit', $program->id)); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Program
                        </a>
                        <?php if($program->status !== 'active'): ?>
                        <form method="POST" action="<?php echo e(route('admin.programs.update', $program->id)); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <input type="hidden" name="status" value="active">
                            <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to activate this program?')">
                                <i class="fas fa-play me-2"></i>Activate Program
                            </button>
                        </form>
                        <?php endif; ?>
                        <?php if($program->status === 'active'): ?>
                        <form method="POST" action="<?php echo e(route('admin.programs.update', $program->id)); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <input type="hidden" name="status" value="completed">
                            <button type="submit" class="btn btn-outline-info w-100" onclick="return confirm('Are you sure you want to mark this program as completed?')">
                                <i class="fas fa-check me-2"></i>Mark Completed
                            </button>
                        </form>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('admin.programs.destroy', $program->id)); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this program? This action cannot be undone.')">
                                <i class="fas fa-trash me-2"></i>Delete Program
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/programs/show.blade.php ENDPATH**/ ?>