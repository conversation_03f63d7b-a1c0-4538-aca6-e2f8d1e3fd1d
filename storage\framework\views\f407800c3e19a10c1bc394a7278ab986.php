

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">Dashboard</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Modern Welcome Section -->
<div class="dashboard-header mb-6">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="welcome-content">
                    <h1 class="dashboard-title">
                        Welcome back, <span class="text-green"><?php echo e(session('admin_user.first_name', 'Admin')); ?></span>!
                        <!-- <span class="wave-emoji">👋</span> -->
                    </h1>
                    <p class="dashboard-subtitle">
                        Here's your HALIMAKQ Foundation overview for today
                    </p>
                    <div class="dashboard-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <?php echo e(now()->format('l, F j, Y')); ?>

                        </span>
                        <span class="meta-divider">•</span>
                        <span class="meta-item status-online">
                            <i class="fas fa-circle me-2"></i>
                            System Online
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="dashboard-actions">
                    <div class="action-buttons">
                        <button class="btn btn-primary btn-modern" data-bs-toggle="modal" data-bs-target="#quickActionModal">
                            <i class="fas fa-plus me-2"></i>
                            Quick Action
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary btn-modern dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-2"></i>
                                Export Data
                            </button>
                            <ul class="dropdown-menu dropdown-menu-modern">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="exportDashboardData('csv')">
                                        <i class="fas fa-file-csv me-3 text-success"></i>
                                        <div>
                                            <div class="fw-medium">Export CSV</div>
                                            <small class="text-muted">Comma-separated values</small>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="exportDashboardData('pdf')">
                                        <i class="fas fa-file-pdf me-3 text-danger"></i>
                                        <div>
                                            <div class="fw-medium">Export PDF</div>
                                            <small class="text-muted">Portable document format</small>
                                        </div>
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="exportDashboardData('excel')">
                                        <i class="fas fa-file-excel me-3 text-success"></i>
                                        <div>
                                            <div class="fw-medium">Export Excel</div>
                                            <small class="text-muted">Microsoft Excel format</small>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clean Stats Grid -->
<div class="stats-grid mb-5">
    <div class="container-fluid">
        <div class="row g-3">
            <!-- Total Users Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Total Users</div>
                                <div class="stat-value"><?php echo e(number_format($stats['totalUsers'])); ?></div>
                                <?php if($stats['newUsersThisMonth'] > 0): ?>
                                <div class="stat-trend">
                                    <small class="text-success">+<?php echo e($stats['newUsersThisMonth']); ?> this month</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Donations Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-hand-holding-heart text-success"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Total Donations</div>
                                <div class="stat-value">₦<?php echo e(number_format($stats['totalDonations'])); ?></div>
                                <?php if($stats['donationsThisMonth'] > 0): ?>
                                <div class="stat-trend">
                                    <small class="text-success">₦<?php echo e(number_format($stats['donationsThisMonth'])); ?> this month</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Programs Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-project-diagram text-warning"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Active Programs</div>
                                <div class="stat-value"><?php echo e($stats['activePrograms']); ?></div>
                                <div class="stat-trend">
                                    <small class="text-muted">Educational programs</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Volunteers Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-hands-helping text-info"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Active Volunteers</div>
                                <div class="stat-value"><?php echo e($stats['totalVolunteers']); ?></div>
                                <?php if($stats['pendingVolunteers'] > 0): ?>
                                <div class="stat-trend">
                                    <small class="text-warning"><?php echo e($stats['pendingVolunteers']); ?> pending</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Second Row of Stats -->
        <div class="row g-3 mt-2">
            <!-- Scholarship Applications Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-graduation-cap text-purple"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Scholarship Applications</div>
                                <div class="stat-value"><?php echo e($stats['totalScholarshipApplications']); ?></div>
                                <?php if($stats['scholarshipApplications'] > 0): ?>
                                <div class="stat-trend">
                                    <small class="text-warning"><?php echo e($stats['scholarshipApplications']); ?> pending</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Scholarships Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-award text-gold"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Active Scholarships</div>
                                <div class="stat-value"><?php echo e($stats['activeScholarships']); ?></div>
                                <div class="stat-trend">
                                    <small class="text-muted">Open for applications</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approved Scholarships Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Approved Scholarships</div>
                                <div class="stat-value"><?php echo e($stats['approvedScholarships']); ?></div>
                                <div class="stat-trend">
                                    <small class="text-muted">This year</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="col-xl-3 col-lg-6 col-md-6">
                <div class="stat-card stat-card-action">
                    <div class="stat-card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3">
                                <i class="fas fa-plus-circle text-primary"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Quick Actions</div>
                                <div class="action-buttons mt-2">
                                    <a href="<?php echo e(route('admin.scholarships.create')); ?>" class="btn btn-sm btn-outline-primary me-2">Add Scholarship</a>
                                    <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-sm btn-outline-secondary">Add User</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(139, 69, 19, 0.1); color: #8b4513;">
                    <i class="fas fa-hands-helping fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics & Activity Section -->
<div class="analytics-section mb-6">
    <div class="container-fluid">
        <div class="row g-4">
            <!-- Analytics Chart -->
            <div class="col-xl-8 col-lg-7">
                <div class="analytics-card">
                    <div class="analytics-header">
                        <div class="analytics-title">
                            <div class="analytics-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <h3 class="analytics-heading">Donation Analytics</h3>
                                <p class="analytics-description">Track donation trends and performance</p>
                            </div>
                        </div>
                        <div class="analytics-controls">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="chartPeriod" id="period30" autocomplete="off" checked>
                                <label class="btn btn-outline-primary btn-sm" for="period30">30D</label>

                                <input type="radio" class="btn-check" name="chartPeriod" id="period6m" autocomplete="off">
                                <label class="btn btn-outline-primary btn-sm" for="period6m">6M</label>

                                <input type="radio" class="btn-check" name="chartPeriod" id="period12m" autocomplete="off">
                                <label class="btn btn-outline-primary btn-sm" for="period12m">12M</label>
                            </div>
                        </div>
                    </div>
                    <div class="analytics-body">
                        <div class="chart-container">
                            <canvas id="donationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Feed -->
            <div class="col-xl-4 col-lg-5">
                <div class="activity-card">
                    <div class="activity-header">
                        <div class="activity-title">
                            <div class="activity-icon">
                                <i class="fas fa-pulse"></i>
                            </div>
                            <div>
                                <h3 class="activity-heading">Live Activity</h3>
                                <p class="activity-description">Recent platform activities</p>
                            </div>
                        </div>
                        <button class="btn btn-ghost btn-sm">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                    <div class="activity-body">
                        <div class="activity-feed">
                            <?php $__empty_1 = true; $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="activity-item">
                                <div class="activity-item-icon">
                                    <div class="activity-item-icon-wrapper activity-<?php echo e($activity['type'] ?? 'default'); ?>">
                                        <i class="fas fa-<?php echo e($activity['icon']); ?>"></i>
                                    </div>
                                </div>
                                <div class="activity-item-content">
                                    <div class="activity-item-text"><?php echo e($activity['description']); ?></div>
                                    <div class="activity-item-time">
                                        <?php echo e(\Carbon\Carbon::parse($activity['timestamp'])->diffForHumans()); ?>

                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="activity-empty">
                                <div class="activity-empty-icon">
                                    <i class="fas fa-inbox"></i>
                                </div>
                                <div class="activity-empty-text">No recent activity</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="activity-footer">
                        <a href="#" class="activity-link">
                            View all activities <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Management Section -->
<div class="management-section mb-6">
    <div class="container-fluid">
        <div class="row g-4">
            <!-- Quick Actions -->
            <div class="col-xl-4 col-lg-6">
                <div class="quick-actions-card">
                    <div class="quick-actions-header">
                        <div class="quick-actions-title">
                            <div class="quick-actions-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div>
                                <h3 class="quick-actions-heading">Quick Actions</h3>
                                <p class="quick-actions-description">Create new content quickly</p>
                            </div>
                        </div>
                    </div>
                    <div class="quick-actions-body">
                        <div class="quick-actions-grid">
                            <a href="<?php echo e(route('admin.donations.create')); ?>" class="quick-action-item">
                                <div class="quick-action-icon bg-success-subtle">
                                    <i class="fas fa-hand-holding-heart"></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">New Donation</div>
                                    <div class="quick-action-subtitle">Record donation</div>
                                </div>
                                <div class="quick-action-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>

                            <a href="<?php echo e(route('admin.events.create')); ?>" class="quick-action-item">
                                <div class="quick-action-icon bg-warning-subtle">
                                    <i class="fas fa-calendar-plus"></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">New Event</div>
                                    <div class="quick-action-subtitle">Schedule event</div>
                                </div>
                                <div class="quick-action-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>

                            <a href="<?php echo e(route('admin.programs.create')); ?>" class="quick-action-item">
                                <div class="quick-action-icon bg-info-subtle">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">New Program</div>
                                    <div class="quick-action-subtitle">Create program</div>
                                </div>
                                <div class="quick-action-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>

                            <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="quick-action-item">
                                <div class="quick-action-icon bg-primary-subtle">
                                    <i class="fas fa-blog"></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">New Post</div>
                                    <div class="quick-action-subtitle">Write article</div>
                                </div>
                                <div class="quick-action-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Donations -->
            <div class="col-xl-8 col-lg-6">
                <div class="donations-card">
                    <div class="donations-header">
                        <div class="donations-title">
                            <div class="donations-icon">
                                <i class="fas fa-hand-holding-heart"></i>
                            </div>
                            <div>
                                <h3 class="donations-heading">Recent Donations</h3>
                                <p class="donations-description">Latest donation activities</p>
                            </div>
                        </div>
                        <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-ghost btn-sm">
                            View All <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                    <div class="donations-body">
                        <div class="donations-table-container">
                            <?php $__empty_1 = true; $__currentLoopData = $recentDonations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="donation-item">
                                <div class="donation-avatar">
                                    <?php if($donation->donor): ?>
                                        <img src="<?php echo e($donation->donor->profile_picture_url ?? asset('images/default-avatar.svg')); ?>"
                                             alt="<?php echo e($donation->donor->full_name); ?>"
                                             class="donation-avatar-img">
                                    <?php else: ?>
                                        <div class="donation-avatar-placeholder">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="donation-content">
                                    <div class="donation-donor">
                                        <div class="donation-name">
                                            <?php echo e($donation->donor->full_name ?? $donation->donor_name ?? 'Anonymous'); ?>

                                        </div>
                                        <div class="donation-email">
                                            <?php echo e($donation->donor->email ?? $donation->donor_email ?? 'N/A'); ?>

                                        </div>
                                    </div>
                                    <div class="donation-details">
                                        <div class="donation-campaign">
                                            <?php echo e($donation->campaign->name ?? 'General Fund'); ?>

                                        </div>
                                        <div class="donation-date">
                                            <?php echo e($donation->created_at->format('M d, Y')); ?>

                                        </div>
                                    </div>
                                </div>
                                <div class="donation-amount">
                                    <div class="donation-amount-value">₦<?php echo e(number_format($donation->amount)); ?></div>
                                    <div class="donation-status">
                                        <?php
                                            $statusConfig = match($donation->payment_status) {
                                                'completed' => ['class' => 'success', 'icon' => 'check-circle'],
                                                'pending' => ['class' => 'warning', 'icon' => 'clock'],
                                                'failed' => ['class' => 'danger', 'icon' => 'times-circle'],
                                                'refunded' => ['class' => 'info', 'icon' => 'undo'],
                                                default => ['class' => 'secondary', 'icon' => 'question-circle']
                                            };
                                        ?>
                                        <span class="donation-status-badge status-<?php echo e($statusConfig['class']); ?>">
                                            <i class="fas fa-<?php echo e($statusConfig['icon']); ?> me-1"></i>
                                            <?php echo e(ucfirst($donation->payment_status)); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="donations-empty">
                                <div class="donations-empty-icon">
                                    <i class="fas fa-hand-holding-heart"></i>
                                </div>
                                <div class="donations-empty-text">No recent donations</div>
                                <div class="donations-empty-subtitle">Donations will appear here once received</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Initialize Donation Chart
const ctx = document.getElementById('donationChart').getContext('2d');
const donationChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($donationStats->pluck('month')); ?>,
        datasets: [{
            label: 'Donations',
            data: <?php echo json_encode($donationStats->pluck('amount')); ?>,
            borderColor: '#2563eb',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₦' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Export Dashboard Data
function exportDashboardData(format) {
    showNotification(`Preparing ${format.toUpperCase()} export...`, 'info');

    // Show loading state
    const exportBtn = event.target.closest('.dropdown-item');
    if (exportBtn) {
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';

        // Simulate export process
        setTimeout(() => {
            showNotification(`Dashboard data exported as ${format.toUpperCase()}!`, 'success');
            exportBtn.innerHTML = originalText;

            // Create download link
            const link = document.createElement('a');
            link.href = `<?php echo e(route('admin.dashboard')); ?>?export=${format}&period=${document.getElementById('timePeriod')?.value || '30'}`;
            link.download = `dashboard-${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, 2000);
    }
}

// Enhanced Dashboard Functions
function updateDashboard() {
    const timePeriod = document.getElementById('timePeriod')?.value || '30';
    const programType = document.getElementById('programType')?.value || 'all';
    const location = document.getElementById('location')?.value || 'all';

    showNotification('Updating dashboard...', 'info');

    // Show loading state
    const updateBtn = document.querySelector('button[onclick="updateDashboard()"]');
    if (updateBtn) {
        const originalText = updateBtn.innerHTML;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        updateBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            showNotification('Dashboard updated successfully!', 'success');
            updateBtn.innerHTML = originalText;
            updateBtn.disabled = false;

            // Here you would make actual API calls to update the data
            loadRealtimeData();
        }, 1500);
    }
}

function resetFilters() {
    if (document.getElementById('timePeriod')) {
        document.getElementById('timePeriod').value = '30';
    }
    if (document.getElementById('programType')) {
        document.getElementById('programType').value = 'all';
    }
    if (document.getElementById('location')) {
        document.getElementById('location').value = 'all';
    }

    // Trigger Select2 update if available
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').trigger('change');
    }

    showNotification('Filters reset to default', 'info');
    updateDashboard();
}

function loadRealtimeData() {
    // Simulate real-time data updates
    const statsNumbers = document.querySelectorAll('.stats-number');
    statsNumbers.forEach(stat => {
        const currentText = stat.textContent.replace(/[₦,]/g, '');
        const currentValue = parseInt(currentText) || 0;
        const variation = Math.floor(Math.random() * 20) - 10; // -10 to +10
        const newValue = Math.max(0, currentValue + variation);

        if (stat.textContent.includes('₦')) {
            animateValue(stat, currentValue, newValue, 1000, '₦');
        } else {
            animateValue(stat, currentValue, newValue, 1000);
        }
    });
}

function animateValue(element, start, end, duration, prefix = '') {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = prefix + new Intl.NumberFormat().format(current);

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// Initialize enhanced dashboard features
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 dropdowns
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }

    // Auto-refresh every 5 minutes
    setInterval(loadRealtimeData, 300000);

    // Add hover effects to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>