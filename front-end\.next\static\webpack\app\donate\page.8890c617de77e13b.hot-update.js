"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/donation-form.tsx":
/*!**************************************!*\
  !*** ./components/donation-form.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DonationForm: () => (/* binding */ DonationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./bank-transfer-receipt */ \"(app-pages-browser)/./components/bank-transfer-receipt.tsx\");\n/* __next_internal_client_entry_do_not_use__ DonationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DonationForm() {\n    var _campaigns_find;\n    _s();\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings)();\n    const [selectedAmount, setSelectedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customAmount, setCustomAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [donationType, setDonationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"one-time\");\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [campaignsLoading, setCampaignsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        donor_name: '',\n        donor_email: '',\n        donor_phone: '',\n        amount: 0,\n        currency: 'NGN',\n        donation_type: 'one-time',\n        payment_method: 'paystack',\n        is_anonymous: false,\n        notes: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [donationId, setDonationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [termsAccepted, setTermsAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load donation campaigns\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DonationForm.useEffect\": ()=>{\n            const loadCampaigns = {\n                \"DonationForm.useEffect.loadCampaigns\": async ()=>{\n                    try {\n                        setCampaignsLoading(true);\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.getDonationCampaigns();\n                        if (response.success && response.data) {\n                            const campaignsData = Array.isArray(response.data) ? response.data : response.data.data || [];\n                            setCampaigns(campaignsData);\n                        }\n                    } catch (error) {\n                        console.error('Error loading campaigns:', error);\n                    } finally{\n                        setCampaignsLoading(false);\n                    }\n                }\n            }[\"DonationForm.useEffect.loadCampaigns\"];\n            loadCampaigns();\n        }\n    }[\"DonationForm.useEffect\"], []);\n    const handleAmountSelect = (amount)=>{\n        setSelectedAmount(amount);\n        setCustomAmount(\"\");\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleCustomAmountChange = (value)=>{\n        const amount = Number.parseInt(value) || 0;\n        setCustomAmount(value);\n        setSelectedAmount(null);\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleDonationTypeChange = (value)=>{\n        const type = value;\n        setDonationType(type);\n        setFormData((prev)=>({\n                ...prev,\n                donation_type: type,\n                frequency: type === 'recurring' ? 'monthly' : undefined\n            }));\n    };\n    const finalAmount = selectedAmount || Number.parseInt(customAmount) || 0;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!finalAmount || finalAmount < _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT) {\n            setSubmitError(\"Please enter a valid donation amount (minimum ₦\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT.toLocaleString(), \")\"));\n            return;\n        }\n        if (!formData.donor_name || !formData.donor_email) {\n            setSubmitError('Please fill in all required fields');\n            return;\n        }\n        if (!termsAccepted) {\n            setSubmitError('Please accept the terms and conditions');\n            return;\n        }\n        setIsSubmitting(true);\n        setSubmitError(null);\n        try {\n            const donationData = {\n                ...formData,\n                amount: finalAmount\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.createDonation(donationData);\n            if (response.success && response.data) {\n                // Store donation ID for receipt upload\n                setDonationId(response.data.id);\n                if (formData.payment_method === 'paystack' && response.data.payment_url) {\n                    // Redirect to Paystack payment page\n                    window.location.href = response.data.payment_url;\n                } else {\n                    // Bank transfer success - show receipt upload\n                    setSubmitSuccess(true);\n                }\n            } else {\n                setSubmitError(response.message || 'Failed to process donation');\n            }\n        } catch (error) {\n            console.error('Donation submission error:', error);\n            setSubmitError('An error occurred while processing your donation. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (submitSuccess) {\n        // For bank transfers, show receipt upload component\n        if (formData.payment_method === 'bank_transfer' && donationId) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-blue-200 bg-blue-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Bank Transfer Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Number:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_number) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Bank Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_bank_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: [\n                                                            \"₦\",\n                                                            finalAmount.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                                        className: \"mt-4 border-blue-200 bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                                className: \"text-blue-700\",\n                                                children: \"Please complete your bank transfer using the details above, then upload your receipt below for verification.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__.BankTransferReceipt, {\n                        donationId: donationId,\n                        onSuccess: ()=>{\n                            // Receipt uploaded successfully\n                            console.log('Receipt uploaded successfully');\n                        },\n                        onError: (error)=>{\n                            console.error('Receipt upload error:', error);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this);\n        }\n        // For other payment methods or fallback\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-green-600 mb-4\",\n                        children: \"Thank You!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Your donation has been received successfully.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Make Another Donation\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-2xl text-center\",\n                    children: \"Complete Your Donation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-8\",\n                    children: [\n                        submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                            className: \"border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                    className: \"text-red-600\",\n                                    children: submitError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: donationType,\n                            onValueChange: handleDonationTypeChange,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"one-time\",\n                                            className: \"rounded-l-xl\",\n                                            children: \"One-Time Donation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"recurring\",\n                                            className: \"rounded-r-xl\",\n                                            children: \"Monthly Giving\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"one-time\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Select Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        5000,\n                                                        10000,\n                                                        25000,\n                                                        50000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"amount-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"amount-\".concat(amount),\n                                                                    className: \"flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        amount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-amount\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Or enter custom amount (minimum ₦100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-amount\",\n                                                    type: \"number\",\n                                                    min: \"100\",\n                                                    placeholder: \"Enter amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"recurring\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-amber-50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-amber-800 dark:text-amber-200 mb-2\",\n                                                    children: \"Monthly Giving Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-amber-700 dark:text-amber-300\",\n                                                    children: \"Join our monthly giving program and provide sustained support for our ongoing projects. You can modify or cancel your recurring donation at any time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Monthly Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        2000,\n                                                        5000,\n                                                        10000,\n                                                        20000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"monthly-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"monthly-\".concat(amount),\n                                                                    className: \"flex flex-col items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₦\",\n                                                                                amount.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"/month\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-monthly\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Or enter custom monthly amount (minimum ₦100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-monthly\",\n                                                    type: \"number\",\n                                                    min: \"100\",\n                                                    placeholder: \"Enter monthly amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Frequency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    value: formData.frequency || 'monthly',\n                                                    onValueChange: (value)=>handleInputChange('frequency', value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"monthly\",\n                                                                    children: \"Monthly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"quarterly\",\n                                                                    children: \"Quarterly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"annually\",\n                                                                    children: \"Annually\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Donor Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-name\",\n                                                            children: [\n                                                                \"Full Name \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 55\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-name\",\n                                                            placeholder: \"Enter your full name\",\n                                                            value: formData.donor_name,\n                                                            onChange: (e)=>handleInputChange('donor_name', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-email\",\n                                                            children: [\n                                                                \"Email Address \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 60\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-email\",\n                                                            type: \"email\",\n                                                            placeholder: \"Enter your email\",\n                                                            value: formData.donor_email,\n                                                            onChange: (e)=>handleInputChange('donor_email', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-phone\",\n                                                            children: \"Phone Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-phone\",\n                                                            placeholder: \"Enter your phone number\",\n                                                            value: formData.donor_phone,\n                                                            onChange: (e)=>handleInputChange('donor_phone', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"purpose\",\n                                                            children: \"Purpose (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"purpose\",\n                                                            placeholder: \"e.g., Education, Healthcare, General\",\n                                                            value: formData.purpose || '',\n                                                            onChange: (e)=>handleInputChange('purpose', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"anonymous\",\n                                                    checked: formData.is_anonymous,\n                                                    onCheckedChange: (checked)=>handleInputChange('is_anonymous', checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"anonymous\",\n                                                    className: \"text-sm\",\n                                                    children: \"Make this donation anonymous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Payment Method\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                            value: formData.payment_method,\n                                            onValueChange: (value)=>handleInputChange('payment_method', value),\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"paystack\",\n                                                            id: \"paystack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"paystack\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Pay with Card (Paystack)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Secure online payment with debit/credit card\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"bank_transfer\",\n                                                            id: \"bank_transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bank_transfer\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Bank Transfer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 511,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Transfer directly to our bank account\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 13\n                                        }, this),\n                                        formData.payment_method === 'bank_transfer' && !settingsLoading && settings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-950/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Bank Account Details\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_name || 'HALIMAKQ Foundation'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Number:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_number || '**********'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Bank Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_bank_name || 'First Bank of Nigeria'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-4\",\n                                                    children: \"Please use your email address as the transfer reference for easy identification.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 11\n                                }, this),\n                                finalAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 pt-6 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 dark:bg-green-950/20 p-6 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-800 dark:text-green-200 mb-4\",\n                                                    children: \"Donation Summary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: donationType === \"recurring\" ? \"\".concat(formData.frequency || 'Monthly', \" Recurring\") : \"One-Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        formData.campaign_id && campaigns.find((c)=>c.id === formData.campaign_id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Campaign:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: (_campaigns_find = campaigns.find((c)=>c.id === formData.campaign_id)) === null || _campaigns_find === void 0 ? void 0 : _campaigns_find.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Payment Method:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: formData.payment_method === 'paystack' ? 'Card Payment' : 'Bank Transfer'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-green-600 pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"terms\",\n                                                    checked: termsAccepted,\n                                                    onCheckedChange: (checked)=>setTermsAccepted(checked === true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"terms\",\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        \"I agree to the\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Terms of Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        \"and\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Privacy Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"submit\",\n                                            size: \"lg\",\n                                            disabled: isSubmitting || finalAmount < 100 || !termsAccepted || !formData.donor_name || !formData.donor_email,\n                                            className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-lg py-6 transition-all duration-200 hover:scale-105 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Processing...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    formData.payment_method === 'paystack' ? 'Pay Now' : 'Submit Donation',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground text-center mt-4\",\n                                    children: \"Your donation is secure and encrypted. We never store your payment information.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(DonationForm, \"8/3rt3UtNsd7/Wz7WV++9wGRHI8=\", false, function() {\n    return [\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings\n    ];\n});\n_c = DonationForm;\nvar _c;\n$RefreshReg$(_c, \"DonationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/donation-form.tsx\n"));

/***/ })

});