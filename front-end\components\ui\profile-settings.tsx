"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  Camera, 
  Lock, 
  Save, 
  Loader2, 
  Upload,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Download
} from "lucide-react"
import { apiClient } from "@/lib/api"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { downloadIdCardAsPDF } from "@/lib/pdf-utils"
import { useSettings } from "@/hooks/useSettings"

interface ProfileSettingsProps {
  user: {
    id: number
    short_id?: string
    first_name: string
    last_name: string
    email: string
    phone_number?: string
    date_of_birth?: string
    gender?: string
    address?: string
    city?: string
    state?: string
    country?: string
    role: string
    status: string
    profile_picture_url?: string
    preferences?: any
    created_at?: string
    qr_code_url?: string
  }
  onUserUpdate?: (updatedUser: any) => void
}

export default function ProfileSettings({ user, onUserUpdate }: ProfileSettingsProps) {
  // Form states
  const [profileData, setProfileData] = useState({
    first_name: user.first_name,
    last_name: user.last_name,
    phone_number: user.phone_number || '',
    date_of_birth: user.date_of_birth || '',
    gender: user.gender || '',
    address: user.address || '',
    city: user.city || '',
    state: user.state || '',
    country: user.country || 'Nigeria'
  })
  
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    new_password_confirmation: ''
  })

  // Loading states
  const [profileLoading, setProfileLoading] = useState(false)
  const [passwordLoading, setPasswordLoading] = useState(false)
  const [avatarLoading, setAvatarLoading] = useState(false)
  const [qrCodeLoading, setQrCodeLoading] = useState(false)
  
  // Message states
  const [profileMessage, setProfileMessage] = useState<{type: 'success' | 'error', text: string} | null>(null)
  const [passwordMessage, setPasswordMessage] = useState<{type: 'success' | 'error', text: string} | null>(null)
  const [avatarMessage, setAvatarMessage] = useState<{type: 'success' | 'error', text: string} | null>(null)
  
  // UI states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // Get settings for app name
  const { settings } = useSettings()

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setProfileLoading(true)
    setProfileMessage(null)

    try {
      const response = await apiClient.updateProfile(profileData)
      
      if (response.success) {
        setProfileMessage({ type: 'success', text: 'Profile updated successfully!' })
        // Note: User management is now handled by Laravel
      } else {
        setProfileMessage({
          type: 'error',
          text: response.message || 'Failed to update profile'
        })
      }
    } catch (error) {
      console.error('Profile update error:', error)
      setProfileMessage({ type: 'error', text: 'An error occurred while updating profile' })
    } finally {
      setProfileLoading(false)
    }
  }

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setPasswordLoading(true)
    setPasswordMessage(null)

    if (passwordData.new_password !== passwordData.new_password_confirmation) {
      setPasswordMessage({ type: 'error', text: 'New passwords do not match' })
      setPasswordLoading(false)
      return
    }

    try {
      const response = await apiClient.request('/profile/password', {
        method: 'PUT',
        body: JSON.stringify(passwordData)
      })
      
      if (response.success) {
        setPasswordMessage({ type: 'success', text: 'Password changed successfully!' })
        setPasswordData({
          current_password: '',
          new_password: '',
          new_password_confirmation: ''
        })
      } else {
        setPasswordMessage({ 
          type: 'error', 
          text: response.message || 'Failed to change password' 
        })
      }
    } catch (error) {
      console.error('Password update error:', error)
      setPasswordMessage({ type: 'error', text: 'An error occurred while changing password' })
    } finally {
      setPasswordLoading(false)
    }
  }

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setAvatarMessage({ type: 'error', text: 'Please select an image file' })
      return
    }

    // Validate file size (2MB)
    if (file.size > 2 * 1024 * 1024) {
      setAvatarMessage({ type: 'error', text: 'Image size must be less than 2MB' })
      return
    }

    setAvatarLoading(true)
    setAvatarMessage(null)

    try {
      const formData = new FormData()
      formData.append('avatar', file)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/profile/avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Accept': 'application/json'
        },
        body: formData
      })

      const result = await response.json()
      
      if (result.success) {
        setAvatarMessage({ type: 'success', text: 'Avatar updated successfully!' })
        // Update user data
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        const updatedUser = { ...currentUser, profile_picture_url: result.avatar_url }
        localStorage.setItem('user', JSON.stringify(updatedUser))
        if (onUserUpdate) {
          onUserUpdate(updatedUser)
        }
        // Force page refresh to show new avatar
        window.location.reload()
      } else {
        setAvatarMessage({ 
          type: 'error', 
          text: result.message || 'Failed to upload avatar' 
        })
      }
    } catch (error) {
      console.error('Avatar upload error:', error)
      setAvatarMessage({ type: 'error', text: 'An error occurred while uploading avatar' })
    } finally {
      setAvatarLoading(false)
    }
  }

  const generateNewQrCode = async () => {
    setQrCodeLoading(true)
    try {
      const response = await apiClient.request('/profile/generate-qr', {
        method: 'POST'
      })
      
      if (response.success) {
        setAvatarMessage({ type: 'success', text: 'QR Code regenerated successfully!' })
        // Update user data
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        const updatedUser = { ...currentUser, qr_code_url: response.data.qr_code_url }
        localStorage.setItem('user', JSON.stringify(updatedUser))
        if (onUserUpdate) {
          onUserUpdate(updatedUser)
        }
        // Force page refresh to show new QR code
        window.location.reload()
      } else {
        setAvatarMessage({ 
          type: 'error', 
          text: response.message || 'Failed to regenerate QR code' 
        })
      }
    } catch (error) {
      console.error('QR code generation error:', error)
      setAvatarMessage({ type: 'error', text: 'An error occurred while generating QR code' })
    } finally {
      setQrCodeLoading(false)
    }
  }

  const downloadIdCard = async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) {
        setAvatarMessage({ type: 'error', text: 'Please login to download ID card' })
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/profile/id-card/pdf`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/pdf'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to download ID card')
      }

      // Create blob and download
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      const appName = settings?.app_name || 'Laravel_NGO'
      a.download = `${user.first_name}_${user.last_name}_${appName.replace(/\s+/g, '_')}_ID_Card.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      setAvatarMessage({ type: 'success', text: 'ID card downloaded successfully!' })
    } catch (error) {
      console.error('Failed to download ID card:', error)
      setAvatarMessage({ type: 'error', text: 'Failed to download ID card' })
    }
  }

  return (
    <div className="space-y-6 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Profile Settings</h2>
          <p className="text-gray-600">Manage your account information and preferences</p>
        </div>
        <Badge variant="outline" className="w-fit">
          ID: {user.short_id}
        </Badge>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="avatar">Avatar</TabsTrigger>
          <TabsTrigger value="qrcode">QR Code</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
              <CardDescription>
                Update your personal details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profileMessage && (
                <Alert className={`mb-6 ${profileMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                  {profileMessage.type === 'success' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={profileMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                    {profileMessage.text}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleProfileUpdate} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="first_name">First Name *</Label>
                    <Input
                      id="first_name"
                      value={profileData.first_name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, first_name: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="last_name">Last Name *</Label>
                    <Input
                      id="last_name"
                      value={profileData.last_name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, last_name: e.target.value }))}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone_number">Phone Number</Label>
                    <Input
                      id="phone_number"
                      type="tel"
                      value={profileData.phone_number}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone_number: e.target.value }))}
                      placeholder="+234"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={profileData.date_of_birth}
                      onChange={(e) => setProfileData(prev => ({ ...prev, date_of_birth: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select value={profileData.gender} onValueChange={(value) => setProfileData(prev => ({ ...prev, gender: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={profileData.address}
                    onChange={(e) => setProfileData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="Enter your full address"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={profileData.city}
                      onChange={(e) => setProfileData(prev => ({ ...prev, city: e.target.value }))}
                      placeholder="e.g., Lagos"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={profileData.state}
                      onChange={(e) => setProfileData(prev => ({ ...prev, state: e.target.value }))}
                      placeholder="e.g., Lagos State"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      value={profileData.country}
                      onChange={(e) => setProfileData(prev => ({ ...prev, country: e.target.value }))}
                      placeholder="e.g., Nigeria"
                    />
                  </div>
                </div>

                <Button 
                  type="submit" 
                  disabled={profileLoading}
                  className="w-full sm:w-auto"
                >
                  {profileLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Change Password
              </CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              {passwordMessage && (
                <Alert className={`mb-6 ${passwordMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                  {passwordMessage.type === 'success' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={passwordMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                    {passwordMessage.text}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handlePasswordUpdate} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="current_password">Current Password *</Label>
                  <div className="relative">
                    <Input
                      id="current_password"
                      type={showCurrentPassword ? 'text' : 'password'}
                      value={passwordData.current_password}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, current_password: e.target.value }))}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new_password">New Password *</Label>
                  <div className="relative">
                    <Input
                      id="new_password"
                      type={showNewPassword ? 'text' : 'password'}
                      value={passwordData.new_password}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                      required
                      minLength={8}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">Password must be at least 8 characters long</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new_password_confirmation">Confirm New Password *</Label>
                  <div className="relative">
                    <Input
                      id="new_password_confirmation"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={passwordData.new_password_confirmation}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, new_password_confirmation: e.target.value }))}
                      required
                      minLength={8}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <Button 
                  type="submit" 
                  disabled={passwordLoading}
                  className="w-full sm:w-auto"
                >
                  {passwordLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Lock className="mr-2 h-4 w-4" />
                      Change Password
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="avatar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5" />
                Profile Picture
              </CardTitle>
              <CardDescription>
                Upload a new profile picture (max 2MB, JPEG/PNG)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {avatarMessage && (
                <Alert className={`mb-6 ${avatarMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                  {avatarMessage.type === 'success' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={avatarMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                    {avatarMessage.text}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
                <div className="relative">
                  <div className="w-24 h-24 sm:w-32 sm:h-32 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                    {user.profile_picture_url ? (
                      <img
                        src={user.profile_picture_url}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/img/default-avatar.svg';
                        }}
                      />
                    ) : (
                      <User className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400" />
                    )}
                  </div>
                  {avatarLoading && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                      <Loader2 className="h-6 w-6 text-white animate-spin" />
                    </div>
                  )}
                </div>

                <div className="flex flex-col space-y-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={avatarLoading}
                    variant="outline"
                    className="w-full sm:w-auto"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload New Picture
                  </Button>
                  <p className="text-sm text-gray-500 text-center sm:text-left">
                    JPEG, PNG up to 2MB
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="qrcode" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M16 12h.01M12 8h.01M16 8h.01" />
                </svg>
                QR Code & ID Card
              </CardTitle>
              <CardDescription>
                Your unique QR code for identification and digital ID card
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* QR Code Section */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-4">Your QR Code</h3>
                  <div className="bg-white p-4 rounded-lg border inline-block">
                    {user.qr_code_url ? (
                      <img
                        src={user.qr_code_url}
                        alt={`QR Code for ${user.first_name} ${user.last_name}`}
                        className="w-48 h-48 object-contain"
                        onError={(e) => {
                          // Show fallback message on QR code error
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent && !parent.querySelector('.qr-error')) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'qr-error w-48 h-48 bg-gray-100 flex items-center justify-center text-gray-500 text-sm';
                            errorDiv.textContent = 'QR Code not available';
                            parent.appendChild(errorDiv);
                          }
                        }}
                      />
                    ) : (
                      <div className="w-48 h-48 bg-gray-100 flex items-center justify-center rounded-lg">
                        <span className="text-gray-500">No QR Code</span>
                      </div>
                    )}
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 mb-2">ID: {user.short_id}</p>
                    <Button
                      onClick={generateNewQrCode}
                      variant="outline"
                      size="sm"
                      disabled={qrCodeLoading}
                    >
                      {qrCodeLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      )}
                      Regenerate QR Code
                    </Button>
                  </div>
                </div>

                {/* Digital ID Card Preview */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-4">Digital ID Card</h3>
                  <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white max-w-sm mx-auto">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center overflow-hidden">
                        {user.profile_picture_url ? (
                          <img
                            src={user.profile_picture_url}
                            alt="Profile"
                            className="w-10 h-10 rounded-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/img/default-avatar.svg';
                            }}
                          />
                        ) : (
                          <User className="h-6 w-6 text-green-600" />
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-xs opacity-80">Member ID</p>
                        <p className="font-mono text-sm">{user.short_id}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <h4 className="font-bold text-lg">{user.first_name} {user.last_name}</h4>
                      <p className="text-sm opacity-90">{user.email}</p>
                      <p className="text-xs mt-2 opacity-80">Member since {new Date().getFullYear()}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" size="sm" onClick={downloadIdCard}>
                      <Download className="mr-2 h-4 w-4" />
                      Download ID Card
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 