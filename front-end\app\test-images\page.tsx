"use client"

import { useEffect, useState } from 'react'
import { apiClient } from '@/lib/api'
import { useSettings } from '@/hooks/useSettings'

export default function TestImagesPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const { settings, loading: settingsLoading } = useSettings()

  useEffect(() => {
    // Authentication is now handled by Laravel - no client-side user fetching needed
    setLoading(false)
  }, [])

  if (loading || settingsLoading) {
    return <div className="p-8">Loading...</div>
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-8">Image Loading Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Settings Test */}
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Settings API Response</h2>
          <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(settings, null, 2)}
          </pre>
          
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Logo Display:</h3>
            {settings?.app_logo ? (
              <div>
                <p className="text-sm text-gray-600 mb-2">URL: {settings.app_logo}</p>
                <img 
                  src={settings.app_logo} 
                  alt="Logo"
                  className="w-16 h-16 object-cover border"
                  onLoad={() => console.log('Logo loaded successfully')}
                  onError={(e) => {
                    console.error('Logo failed to load:', settings.app_logo)
                    console.log('Error event:', e)
                  }}
                />
              </div>
            ) : (
              <p className="text-red-500">No logo URL found</p>
            )}
          </div>
        </div>

        {/* User Test */}
        <div className="border p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">User API Response</h2>
          <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
          
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Avatar Display:</h3>
            {user?.profile_picture_url ? (
              <div>
                <p className="text-sm text-gray-600 mb-2">URL: {user.profile_picture_url}</p>
                <img 
                  src={user.profile_picture_url} 
                  alt="Avatar"
                  className="w-16 h-16 object-cover border rounded-full"
                  onLoad={() => console.log('Avatar loaded successfully')}
                  onError={(e) => {
                    console.error('Avatar failed to load:', user.profile_picture_url)
                    console.log('Error event:', e)
                  }}
                />
              </div>
            ) : (
              <p className="text-red-500">No avatar URL found</p>
            )}
          </div>
        </div>
      </div>

      {/* Direct API Test */}
      <div className="border p-6 rounded-lg mt-8">
        <h2 className="text-xl font-semibold mb-4">Direct API Tests</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Default Avatar (SVG):</h3>
            <p className="text-sm text-gray-600 mb-2">URL: http://localhost:8000/api/v1/images/default-avatar.svg</p>
            <img 
              src="http://localhost:8000/api/v1/images/default-avatar.svg" 
              alt="Default Avatar"
              className="w-16 h-16 object-cover border rounded-full"
              onLoad={() => console.log('Default avatar loaded successfully')}
              onError={(e) => {
                console.error('Default avatar failed to load')
                console.log('Error event:', e)
              }}
            />
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Test CORS:</h3>
            <button 
              onClick={async () => {
                try {
                  const response = await fetch('http://localhost:8000/api/v1/settings')
                  const data = await response.json()
                  console.log('CORS test successful:', data)
                  alert('CORS test successful - check console')
                } catch (error) {
                  console.error('CORS test failed:', error)
                  alert('CORS test failed - check console')
                }
              }}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Test CORS API Call
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 