<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BlockedIpMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $ip = $request->ip();
        
        // Check if IP is blocked
        if ($this->isIpBlocked($ip)) {
            $this->logBlockedAttempt($request);
            
            return response()->json([
                'success' => false,
                'message' => 'Access denied',
                'error' => 'IP_BLOCKED',
                'error_code' => 'IP_BLOCKED'
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if IP is blocked
     */
    private function isIpBlocked(string $ip): bool
    {
        $key = "blocked_ip:{$ip}";
        return Cache::has($key);
    }

    /**
     * Log blocked access attempt
     */
    private function logBlockedAttempt(Request $request): void
    {
        Log::channel('security')->warning('Blocked IP attempted access', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
