<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Scholarship extends Model
{
    use HasFactory, HasSlug;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'category',
        'description',
        'eligibility_criteria',
        'application_instructions',
        'terms_conditions',
        'amount',
        'application_deadline',
        'application_start_date',
        'requirements',
        'status',
        'is_open',
        'is_featured',
        'max_applicants',
        'current_applicants',
        'contact_email',
        'documents_required',
        'custom_fields',
        'image',
        'notification_settings',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'application_deadline' => 'date',
        'application_start_date' => 'date',
        'requirements' => 'array',
        'documents_required' => 'array',
        'custom_fields' => 'array',
        'notification_settings' => 'array',
        'max_applicants' => 'integer',
        'current_applicants' => 'integer',
        'is_open' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the scholarship applications.
     */
    public function applications()
    {
        return $this->hasMany(ScholarshipApplication::class);
    }

    /**
     * Get the scholarship fields.
     */
    public function fields()
    {
        return $this->hasMany(ScholarshipField::class);
    }

    /**
     * Get active scholarship fields ordered by field_order.
     */
    public function activeFields()
    {
        return $this->hasMany(ScholarshipField::class)->active()->ordered();
    }

    /**
     * Get the user who created this scholarship.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this scholarship.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the category details.
     */
    public function categoryDetails()
    {
        return $this->belongsTo(ScholarshipCategory::class, 'category', 'slug');
    }

    /**
     * Check if scholarship is open for applications.
     */
    public function isOpen(): bool
    {
        return $this->status === 'open' && 
               $this->is_open &&
               $this->application_deadline >= now()->toDateString() &&
               (!$this->max_applicants || $this->current_applicants < $this->max_applicants);
    }

    /**
     * Check if scholarship is closed.
     */
    public function isClosed(): bool
    {
        return $this->status === 'closed' || 
               $this->application_deadline < now()->toDateString() ||
               ($this->max_applicants && $this->current_applicants >= $this->max_applicants);
    }

    /**
     * Check if deadline has passed.
     */
    public function isDeadlinePassed(): bool
    {
        return $this->application_deadline < now()->toDateString();
    }

    /**
     * Check if at capacity.
     */
    public function isAtCapacity(): bool
    {
        return $this->max_applicants && $this->current_applicants >= $this->max_applicants;
    }

    /**
     * Scope a query to only include open scholarships.
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open')
                    ->where('is_open', true)
                    ->where('application_deadline', '>=', now()->toDateString());
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include closed scholarships.
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'closed')
                    ->orWhere('application_deadline', '<', now()->toDateString());
    }

    /**
     * Increment application count.
     */
    public function incrementApplications(): void
    {
        $this->increment('current_applicants');
    }

    /**
     * Decrement application count.
     */
    public function decrementApplications(): void
    {
        $this->decrement('current_applicants');
    }

    /**
     * Get the custom fields for this scholarship (alias for fields).
     */
    public function customFields()
    {
        return $this->fields();
    }
}
