<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Integration Guide - HLTKKQ Foundation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <div class="sidebar-header">
                        <h3>Frontend Integration</h3>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#prerequisites">
                                <i class="fas fa-check-circle"></i> Prerequisites
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#environment">
                                <i class="fas fa-cog"></i> Environment Setup
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#axios">
                                <i class="fas fa-code"></i> Axios Configuration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#auth">
                                <i class="fas fa-lock"></i> Authentication Setup
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#hooks">
                                <i class="fas fa-link"></i> Custom Hooks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#testing">
                                <i class="fas fa-vial"></i> Testing
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1>Frontend Integration Guide</h1>
                </div>

                <!-- Prerequisites Section -->
                <section id="prerequisites" class="mb-5">
                    <h2>Prerequisites</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Required Software</h5>
                            <ul>
                                <li>Node.js 18+ and NPM</li>
                                <li>Next.js 14+</li>
                                <li>TypeScript</li>
                                <li>Laravel Backend running locally</li>
                                <li>Modern web browser</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Environment Setup Section -->
                <section id="environment" class="mb-5">
                    <h2>Environment Setup</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>1. Create .env.local File</h5>
                            <pre><code># API Configuration
NEXT_PUBLIC_API_URL=http://hltkkq.test
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Authentication
NEXT_PUBLIC_AUTH_ENABLED=true
NEXT_PUBLIC_AUTH_REDIRECT=/dashboard

# Feature Flags
NEXT_PUBLIC_ENABLE_DONATIONS=true
NEXT_PUBLIC_ENABLE_VOLUNTEER=true
NEXT_PUBLIC_ENABLE_BLOG=true</code></pre>

                            <h5>2. Install Dependencies</h5>
                            <pre><code>npm install axios zustand @tanstack/react-query</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Axios Configuration Section -->
                <section id="axios" class="mb-5">
                    <h2>Axios Configuration</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>1. Create Axios Instance</h5>
                            <pre><code>// lib/axios.ts
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const csrfToken = localStorage.getItem('auth-storage')
      ? JSON.parse(localStorage.getItem('auth-storage')!).state.csrfToken
      : null;

    if (csrfToken) {
      config.headers['X-CSRF-TOKEN'] = csrfToken;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 419) {
      // CSRF token mismatch - refresh the token
      try {
        await axios.get('/sanctum/csrf-cookie');
        const csrfToken = document.cookie
          .split('; ')
          .find(row => row.startsWith('XSRF-TOKEN='))
          ?.split('=')[1];

        if (csrfToken) {
          error.config.headers['X-CSRF-TOKEN'] = decodeURIComponent(csrfToken);
          return api(error.config);
        }
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }
    return Promise.reject(error);
  }
);

export default api;</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Authentication Setup Section -->
                <section id="auth" class="mb-5">
                    <h2>Authentication Setup</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>1. Create Auth Store</h5>
                            <pre><code>// hooks/use-auth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role: 'admin' | 'volunteer' | 'donor' | 'user';
  profile_picture?: string;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  csrfToken: string | null;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setCsrfToken: (token: string | null) => void;
  logout: () => void;
}

export const useAuth = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      loading: true,
      csrfToken: null,
      setUser: (user) => set({ user }),
      setLoading: (loading) => set({ loading }),
      setCsrfToken: (token) => set({ csrfToken: token }),
      logout: () => set({ user: null, csrfToken: null }),
    }),
    {
      name: 'auth-storage',
    }
  )
);</code></pre>

                            <h5>2. Create Session Auth Hook</h5>
                            <pre><code>// hooks/use-session-auth.ts
import { useState } from 'react';
import { useAuth } from './use-auth';
import api from '@/lib/axios';

export const useSessionAuth = () => {
  const { setUser, setLoading, setCsrfToken, logout } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const login = async (credentials: { email: string; password: string }) => {
    try {
      setLoading(true);
      setError(null);

      // Get CSRF token first
      const csrfResponse = await api.get('/sanctum/csrf-cookie');
      const csrfToken = csrfResponse.headers['x-csrf-token'];
      setCsrfToken(csrfToken);

      // Attempt login
      const response = await api.post('/api/auth/login', credentials);
      setUser(response.data.user);
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ... register, logout, and checkAuth methods
};</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Custom Hooks Section -->
                <section id="hooks" class="mb-5">
                    <h2>Custom Hooks</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>1. Auth Guard Hook</h5>
                            <pre><code>// hooks/use-auth-guard.ts
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './use-auth';
import { useSessionAuth } from './use-session-auth';

export const useAuthGuard = (options: {
  redirectTo?: string;
  requireAuth?: boolean;
} = {}) => {
  const {
    redirectTo = '/login',
    requireAuth = true,
  } = options;

  const router = useRouter();
  const { user, loading } = useAuth();
  const { checkAuth } = useSessionAuth();

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        await checkAuth();
        
        if (requireAuth && !user) {
          router.push(redirectTo);
        } else if (!requireAuth && user) {
          router.push('/dashboard');
        }
      } catch (error) {
        if (requireAuth) {
          router.push(redirectTo);
        }
      }
    };

    if (!loading) {
      checkAuthentication();
    }
  }, [user, loading, requireAuth, redirectTo, router, checkAuth]);

  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  };
};</code></pre>

                            <h5>2. Auth Form Hook</h5>
                            <pre><code>// hooks/use-auth-form.ts
import { useState } from 'react';
import { useSessionAuth } from './use-session-auth';
import { useRouter } from 'next/navigation';

export const useAuthForm = (options: {
  onSuccess?: () => void;
  redirectTo?: string;
  mode?: 'login' | 'register';
} = {}) => {
  const {
    onSuccess,
    redirectTo = '/dashboard',
    mode = 'login',
  } = options;

  const router = useRouter();
  const { login, register, error: authError } = useSessionAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // ... form validation and submission logic
};</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Testing Section -->
                <section id="testing" class="mb-5">
                    <h2>Testing Integration</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>1. Test Authentication Flow</h5>
                            <pre><code>// tests/auth.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { useSessionAuth } from '@/hooks/use-session-auth';
import api from '@/lib/axios';

jest.mock('@/lib/axios');

describe('Authentication', () => {
  it('should handle login successfully', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
    };

    (api.post as jest.Mock).mockResolvedValueOnce({
      data: { user: mockUser },
    });

    const { result } = renderHook(() => useSessionAuth());

    await act(async () => {
      await result.current.login({
        email: '<EMAIL>',
        password: 'password',
      });
    });

    expect(result.current.error).toBeNull();
  });
});</code></pre>

                            <h5>2. Test Protected Routes</h5>
                            <pre><code>// tests/auth-guard.test.ts
import { renderHook } from '@testing-library/react-hooks';
import { useAuthGuard } from '@/hooks/use-auth-guard';
import { useRouter } from 'next/navigation';

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

describe('Auth Guard', () => {
  it('should redirect to login when not authenticated', () => {
    const mockRouter = {
      push: jest.fn(),
    };

    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    renderHook(() => useAuthGuard());

    expect(mockRouter.push).toHaveBeenCalledWith('/login');
  });
});</code></pre>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html> 