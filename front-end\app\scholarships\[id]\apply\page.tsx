'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  School, 
  GraduationCap, 
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Upload,
  FileText,
  User,
  Loader2,
  Send
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
import { useScholarshipApplicationRouting } from '@/hooks/useScholarshipRouting'
import PartnerScholarshipApplicationForm from '@/components/scholarship/PartnerScholarshipApplicationForm'
import UniversityScholarshipApplicationForm from '@/components/scholarship/UniversityScholarshipApplicationForm'

interface Scholarship {
  id: number
  title: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  amount: number
  application_deadline: string
  status: string
  category_instructions?: {
    title: string
    age_range: string
    filled_by: string
    instruction: string
    required_info: string[]
  }
  custom_fields?: Array<{
    id: number
    field_name: string
    field_type: string
    is_required: boolean
    field_options?: string[]
    validation_rules?: any
  }>
  form_metadata?: {
    validation_rules: any
    field_constraints: any
    has_file_uploads: boolean
  }
}

const categoryConfig = {
  primary: {
    icon: BookOpen,
    title: "Primary School",
    color: "bg-blue-500",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700"
  },
  secondary: {
    icon: School,
    title: "Secondary School", 
    color: "bg-green-500",
    bgColor: "bg-green-50",
    textColor: "text-green-700"
  },
  university: {
    icon: GraduationCap,
    title: "University",
    color: "bg-purple-500",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700"
  }
}

export default function ScholarshipApplicationPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()

  const [scholarship, setScholarship] = useState<Scholarship | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [files, setFiles] = useState<Record<string, File>>({})

  // Use scholarship routing hook for access control
  const routing = useScholarshipApplicationRouting(
    params.id as string,
    scholarship?.category || 'primary'
  )

  useEffect(() => {
    if (params.id) {
      fetchScholarship(params.id as string)
    }
  }, [params.id])

  const fetchScholarship = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getPublicScholarship(id)
      
      if (response.success && response.data) {
        setScholarship(response.data)
        // Initialize form data with empty values for all custom fields
        const initialFormData: Record<string, any> = {}
        response.data.custom_fields?.forEach((field: any) => {
          initialFormData[field.field_name] = field.field_type === 'checkbox' ? [] : ''
        })
        setFormData(initialFormData)
      } else {
        throw new Error(response.message || 'Scholarship not found')
      }
    } catch (error) {
      console.error('Error fetching scholarship:', error)
      setError('Failed to load scholarship details. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  const handleFileChange = (fieldName: string, file: File | null) => {
    if (file) {
      setFiles(prev => ({
        ...prev,
        [fieldName]: file
      }))
    } else {
      setFiles(prev => {
        const newFiles = { ...prev }
        delete newFiles[fieldName]
        return newFiles
      })
    }
  }

  const validateForm = () => {
    if (!scholarship?.custom_fields) return true

    for (const field of scholarship.custom_fields) {
      if (field.is_required) {
        const value = formData[field.field_name]
        if (!value || (Array.isArray(value) && value.length === 0)) {
          toast({
            title: "Validation Error",
            description: `${field.field_name} is required`,
            variant: "destructive"
          })
          return false
        }
      }
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    try {
      setSubmitting(true)
      
      // Create FormData for file uploads
      const submitData = new FormData()
      submitData.append('scholarship_id', scholarship!.id.toString())
      
      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          submitData.append(key, JSON.stringify(value))
        } else {
          submitData.append(key, value.toString())
        }
      })
      
      // Add files
      Object.entries(files).forEach(([key, file]) => {
        submitData.append(key, file)
      })
      
      // Submit application (this would need to be implemented in the API client)
      // const response = await apiClient.submitScholarshipApplication(submitData)
      
      // For now, simulate success
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast({
        title: "Application Submitted!",
        description: "Your scholarship application has been submitted successfully.",
        variant: "default"
      })
      
      // Redirect to success page or back to scholarships
      router.push('/scholarships?submitted=true')
      
    } catch (error) {
      console.error('Error submitting application:', error)
      toast({
        title: "Submission Failed",
        description: "Failed to submit your application. Please try again.",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  const renderField = (field: any) => {
    const value = formData[field.field_name] || ''
    
    switch (field.field_type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return (
          <Input
            type={field.field_type === 'number' ? 'number' : field.field_type === 'email' ? 'email' : 'text'}
            value={value}
            onChange={(e) => handleInputChange(field.field_name, e.target.value)}
            placeholder={`Enter ${field.field_name.toLowerCase()}`}
            required={field.is_required}
          />
        )
      
      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleInputChange(field.field_name, e.target.value)}
            placeholder={`Enter ${field.field_name.toLowerCase()}`}
            required={field.is_required}
            rows={4}
          />
        )
      
      case 'select':
        return (
          <Select value={value} onValueChange={(val) => handleInputChange(field.field_name, val)}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${field.field_name.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.field_options?.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'file':
        return (
          <div className="space-y-2">
            <Input
              type="file"
              onChange={(e) => handleFileChange(field.field_name, e.target.files?.[0] || null)}
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              required={field.is_required}
            />
            <p className="text-xs text-gray-500">
              Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB)
            </p>
          </div>
        )
      
      case 'checkbox':
        return (
          <div className="space-y-2">
            {field.field_options?.map((option: string) => (
              <label key={option} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={Array.isArray(value) && value.includes(option)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(value) ? value : []
                    if (e.target.checked) {
                      handleInputChange(field.field_name, [...currentValues, option])
                    } else {
                      handleInputChange(field.field_name, currentValues.filter((v: string) => v !== option))
                    }
                  }}
                  className="rounded"
                />
                <span className="text-sm">{option}</span>
              </label>
            ))}
          </div>
        )
      
      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleInputChange(field.field_name, e.target.value)}
            placeholder={`Enter ${field.field_name.toLowerCase()}`}
            required={field.is_required}
          />
        )
    }
  }

  // Show loading state for both scholarship data and routing
  if (loading || routing.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">
              {loading ? 'Loading application form...' : 'Checking access permissions...'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show access denied message if routing indicates no access
  if (!routing.hasAccess && routing.errorMessage) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Access Required</h3>
            <p className="text-gray-600 mb-6">{routing.errorMessage}</p>
            <div className="flex gap-4 justify-center">
              <Button onClick={() => routing.navigateToLogin()} className="bg-green-600 hover:bg-green-700">
                {routing.requiredRole === 'partner_organization' ? 'School Login' : 'Student Login'}
              </Button>
              <Button onClick={() => router.back()} variant="outline">
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !scholarship) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Application</h3>
            <p className="text-gray-600 mb-6">{error || 'Scholarship not found'}</p>
            <div className="flex gap-4 justify-center">
              <Button onClick={() => router.back()} variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
              <Button onClick={() => fetchScholarship(params.id as string)}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const config = categoryConfig[scholarship.category]
  const IconComponent = config.icon

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Scholarship
          </Button>
        </div>

        {/* Partner Organization Application */}
        {user?.role === 'partner_organization' && (scholarship.category === 'primary' || scholarship.category === 'secondary') ? (
          <PartnerScholarshipApplicationForm
            scholarshipId={params.id as string}
            scholarshipTitle={scholarship.title}
            scholarshipCategory={scholarship.category as 'primary' | 'secondary'}
            onSuccess={() => {
              toast({
                title: "Application Submitted Successfully",
                description: "Your scholarship application has been submitted for review.",
              })
              router.push('/dashboard/partner?tab=applications')
            }}
          />
        ) : user?.role === 'student' && scholarship.category === 'university' ? (
          /* University Student Application */
          <UniversityScholarshipApplicationForm
            scholarshipId={params.id as string}
            scholarshipTitle={scholarship.title}
            onSuccess={() => {
              toast({
                title: "Application Submitted Successfully",
                description: "Your university scholarship application has been submitted for review.",
              })
              router.push('/dashboard/student?tab=university')
            }}
          />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Individual Student Application Form */}
            <div className="lg:col-span-2">
              <Card>
              <CardHeader>
                <div className="flex items-center gap-3 mb-4">
                  <div className={`p-2 rounded-lg ${config.bgColor}`}>
                    <IconComponent className={`h-6 w-6 ${config.textColor}`} />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Apply for Scholarship</CardTitle>
                    <CardDescription>{scholarship.title}</CardDescription>
                  </div>
                </div>
                
                {scholarship.category_instructions && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      Important Instructions
                    </h4>
                    <p className="text-blue-800 dark:text-blue-200 text-sm mb-2">
                      <strong>Who fills this form:</strong> {scholarship.category_instructions.filled_by}
                    </p>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      {scholarship.category_instructions.instruction}
                    </p>
                  </div>
                )}
              </CardHeader>
              
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {scholarship.custom_fields?.map((field) => (
                    <div key={field.id} className="space-y-2">
                      <Label htmlFor={field.field_name} className="flex items-center gap-2">
                        {field.field_name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        {field.is_required && (
                          <Badge variant="outline" className="text-xs">Required</Badge>
                        )}
                      </Label>
                      {renderField(field)}
                    </div>
                  ))}
                  
                  <div className="pt-6 border-t">
                    <Button 
                      type="submit" 
                      disabled={submitting}
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                      size="lg"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Submitting Application...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Submit Application
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Scholarship Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Scholarship Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Award Amount</p>
                  <p className="font-semibold text-green-600">
                    ₦{scholarship.amount.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Category</p>
                  <Badge variant="outline">{config.title}</Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Application Deadline</p>
                  <p className="font-semibold">
                    {new Date(scholarship.application_deadline).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Required Information */}
            {scholarship.category_instructions?.required_info && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Required Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {scholarship.category_instructions.required_info.map((info, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        {info}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Help */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Having trouble with your application?
                </p>
                <Link href="/contact">
                  <Button variant="outline" className="w-full">
                    Contact Support
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
        )}
      </div>
    </div>
  )
}
