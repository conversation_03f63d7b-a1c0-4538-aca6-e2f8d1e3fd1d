<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewsletterCampaign extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'subject',
        'content',
        'preview_text',
        'recipient_type',
        'estimated_recipients',
        'actual_recipients',
        'status',
        'scheduled_at',
        'sent_at',
        'from_name',
        'from_email',
        'template',
        'open_count',
        'click_count',
        'unsubscribe_count',
        'bounce_count',
        'created_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'estimated_recipients' => 'integer',
        'actual_recipients' => 'integer',
        'open_count' => 'integer',
        'click_count' => 'integer',
        'unsubscribe_count' => 'integer',
        'bounce_count' => 'integer',
    ];

    /**
     * Get the user who created this campaign
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Check if campaign is draft
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if campaign is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if campaign is sent
     */
    public function isSent(): bool
    {
        return $this->status === 'sent';
    }

    /**
     * Get open rate percentage
     */
    public function getOpenRateAttribute(): float
    {
        if ($this->actual_recipients <= 0) {
            return 0;
        }
        return ($this->open_count / $this->actual_recipients) * 100;
    }

    /**
     * Get click rate percentage
     */
    public function getClickRateAttribute(): float
    {
        if ($this->actual_recipients <= 0) {
            return 0;
        }
        return ($this->click_count / $this->actual_recipients) * 100;
    }

    /**
     * Get unsubscribe rate percentage
     */
    public function getUnsubscribeRateAttribute(): float
    {
        if ($this->actual_recipients <= 0) {
            return 0;
        }
        return ($this->unsubscribe_count / $this->actual_recipients) * 100;
    }

    /**
     * Scope for draft campaigns
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope for scheduled campaigns
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for sent campaigns
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope for campaigns ready to send
     */
    public function scopeReadyToSend($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_at', '<=', now());
    }

    /**
     * Mark campaign as sent
     */
    public function markAsSent(int $recipientCount = null): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'actual_recipients' => $recipientCount ?? $this->estimated_recipients
        ]);
    }

    /**
     * Increment open count
     */
    public function incrementOpenCount(): void
    {
        $this->increment('open_count');
    }

    /**
     * Increment click count
     */
    public function incrementClickCount(): void
    {
        $this->increment('click_count');
    }

    /**
     * Increment unsubscribe count
     */
    public function incrementUnsubscribeCount(): void
    {
        $this->increment('unsubscribe_count');
    }

    /**
     * Increment bounce count
     */
    public function incrementBounceCount(): void
    {
        $this->increment('bounce_count');
    }
}
