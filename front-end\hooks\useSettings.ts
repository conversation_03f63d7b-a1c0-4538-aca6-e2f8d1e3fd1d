import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface Settings {
  app_name: string;
  app_logo?: string;
  site_description: string;
  contact_email: string;
  contact_phone: string;
  organization_address?: string;
  organization_city?: string;
  organization_state?: string;
  organization_country?: string;
  organization_postal_code?: string;
  social_facebook?: string;
  social_twitter?: string;
  social_instagram?: string;
  social_linkedin?: string;
}

export const useSettings = () => {
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await apiClient.getSettings();
        
        if (response.success) {
          console.log('Settings loaded:', response.data);
          setSettings(response.data);
        } else {
          throw new Error('Invalid settings response');
        }
      } catch (err) {
        console.error('Error fetching settings:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        // Set fallback settings
        setSettings({
          app_name: 'HLTKKQ Foundation',
          site_description: 'Transforming Lives, Building Communities',
          contact_email: '<EMAIL>',
          contact_phone: '+234 ************'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  return { settings, loading, error };
}; 