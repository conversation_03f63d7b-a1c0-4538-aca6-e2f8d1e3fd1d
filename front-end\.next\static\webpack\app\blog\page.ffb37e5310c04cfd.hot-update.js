"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./app/blog/page.tsx":
/*!***************************!*\
  !*** ./app/blog/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Loader2,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar _process_env_NEXT_PUBLIC_API_URL;\nconst API_BASE_URL = (_process_env_NEXT_PUBLIC_API_URL = \"http://localhost:8000/api/v1\") !== null && _process_env_NEXT_PUBLIC_API_URL !== void 0 ? _process_env_NEXT_PUBLIC_API_URL : \"\";\nfunction BlogPage() {\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"All\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(1);\n    const [totalPosts, setTotalPosts] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    // Fetch blog posts\n    const fetchPosts = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, search = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"\", category = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"\";\n        try {\n            setLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: page.toString(),\n                per_page: '9',\n                ...search && {\n                    search\n                },\n                ...category && category !== 'All' && {\n                    category\n                }\n            });\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/blog/posts?\").concat(params));\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const text = await response.text();\n            let data;\n            try {\n                data = JSON.parse(text);\n            } catch (parseError) {\n                console.error('Response was not valid JSON:', text.substring(0, 200));\n                throw new Error('Server returned invalid response');\n            }\n            if (data.success) {\n                setPosts(data.posts.data || []);\n                setCurrentPage(data.posts.current_page || 1);\n                setTotalPages(data.posts.last_page || 1);\n                setTotalPosts(data.posts.total || 0);\n            } else {\n                throw new Error('API request failed');\n            }\n        } catch (error) {\n            console.error('Error fetching posts:', error);\n            setError('Failed to load blog posts. Please check if the server is running.');\n            setPosts([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch categories\n    const fetchCategories = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/blog/categories\"));\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const text = await response.text();\n            let data;\n            try {\n                data = JSON.parse(text);\n            } catch (parseError) {\n                console.error('Categories response was not valid JSON:', text.substring(0, 200));\n                throw new Error('Server returned invalid response');\n            }\n            if (data.success) {\n                setCategories(data.data || []);\n            } else {\n                throw new Error('Failed to fetch categories');\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        }\n    };\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"BlogPage.useEffect\": ()=>{\n            fetchPosts();\n            fetchCategories();\n        }\n    }[\"BlogPage.useEffect\"], []);\n    // Handle search and filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"BlogPage.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"BlogPage.useEffect.timeoutId\": ()=>{\n                    fetchPosts(1, searchTerm, selectedCategory);\n                }\n            }[\"BlogPage.useEffect.timeoutId\"], 300);\n            return ({\n                \"BlogPage.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"BlogPage.useEffect\"];\n        }\n    }[\"BlogPage.useEffect\"], [\n        searchTerm,\n        selectedCategory\n    ]);\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    // Get reading time or default\n    const getReadingTime = (post)=>{\n        return post.reading_time || '5 min read';\n    };\n    // Get fallback image\n    const getPostImage = (post)=>{\n        if (post.featured_image_url) {\n            return post.featured_image_url;\n        }\n        // Return unique placeholder based on post ID\n        const placeholders = [\n            'https://images.unsplash.com/photo-1529070538774-1843cb3265df',\n            'https://images.unsplash.com/photo-1593113598332-cd288d649433',\n            'https://images.unsplash.com/photo-1559027615-cd4628902d4a',\n            'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40',\n            'https://images.unsplash.com/photo-1517048676732-d65bc937f952',\n            'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c',\n            'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca',\n            'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b',\n            'https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a',\n            'https://images.unsplash.com/photo-1484712401471-05c7215830eb' // Community building\n        ];\n        // Use post ID to get consistent but different images\n        const index = post.id % placeholders.length;\n        return placeholders[index] + '?w=800&h=500&fit=crop&crop=center';\n    };\n    const allCategories = [\n        \"All\",\n        ...(categories || []).map((cat)=>cat.name)\n    ];\n    const featuredPost = posts && posts.length > 0 ? posts[0] : null // First post as featured\n    ;\n    const regularPosts = posts && posts.length > 1 ? posts.slice(1) : [] // Rest of the posts\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white py-20 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container relative px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n                                    children: [\n                                        \"Stories of \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-amber-300\",\n                                            children: \"Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 26\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-green-100 mb-8 leading-relaxed\",\n                                    children: \"Discover the transformative stories behind our work. Read about communities we serve, challenges we overcome, and victories we celebrate together.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full\",\n                                            children: \"Latest Stories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/contact\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white px-8 py-3 rounded-full font-semibold\",\n                                                children: \"Become a Volunteer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 bg-gray-50 dark:bg-gray-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-12 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col lg:flex-row gap-6 items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex-1 max-w-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Search articles, authors, topics...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-12 pr-4 py-3 rounded-full border-2 border-gray-200 focus:border-green-500 text-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"default\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"transition-all duration-200 rounded-full px-4 py-2 \".concat(selectedCategory === category ? \"bg-green-600 hover:bg-green-700 text-white\" : \"border-gray-300 hover:bg-green-50 hover:border-green-300 text-gray-600\"),\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-gray-600 dark:text-gray-400\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading articles...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this) : \"Showing \".concat(posts.length, \" of \").concat(totalPosts, \" articles\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 animate-spin text-green-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg text-gray-600\",\n                                            children: \"Loading articles...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this),\n                    error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center bg-red-50 border border-red-200 rounded-lg p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-12 w-12 text-red-600\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-red-900 mb-2\",\n                                            children: \"Unable to Load Blog Posts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700 mb-6\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>{\n                                                setError(null);\n                                                fetchPosts();\n                                                fetchCategories();\n                                            },\n                                            className: \"bg-red-600 hover:bg-red-700 text-white rounded-full px-6 py-2\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && featuredPost && selectedCategory === \"All\" && !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            className: \"bg-amber-500 hover:bg-amber-600 text-black font-semibold px-4 py-2 rounded-full text-sm\",\n                                            children: \"✨ Featured Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"overflow-hidden shadow-2xl rounded-3xl bg-white dark:bg-gray-800 border-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-80 lg:h-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: getPostImage(featuredPost),\n                                                            alt: featuredPost.title,\n                                                            fill: true,\n                                                            className: \"object-cover\",\n                                                            sizes: \"(max-width: 768px) 100vw, 50vw\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent lg:bg-gradient-to-r\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                    className: \"p-8 lg:p-12 flex flex-col justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mb-4\",\n                                                            children: [\n                                                                featuredPost.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"bg-green-100 text-green-800 rounded-full px-3 py-1\",\n                                                                    children: featuredPost.category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-gray-500 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        formatDate(featuredPost.published_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 leading-tight\",\n                                                            children: featuredPost.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\",\n                                                            children: featuredPost.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: featuredPost.author.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                src: featuredPost.author.avatar,\n                                                                                alt: featuredPost.author.name,\n                                                                                width: 40,\n                                                                                height: 40,\n                                                                                className: \"rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 379,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                                                    children: featuredPost.author.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                    lineNumber: 391,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center text-gray-500 text-sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                            lineNumber: 393,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        getReadingTime(featuredPost)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                    lineNumber: 392,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: \"/blog/\".concat(featuredPost.slug),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group\",\n                                                                        children: [\n                                                                            \"Read Story\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 402,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: posts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                            children: (selectedCategory === \"All\" && !searchTerm ? regularPosts : posts).map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                    className: \"group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 rounded-2xl bg-white dark:bg-gray-800 border-0 hover:-translate-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/blog/\".concat(post.slug),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: getPostImage(post),\n                                                                        alt: post.title,\n                                                                        fill: true,\n                                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-300\",\n                                                                        sizes: \"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    post.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-4 left-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"bg-white/90 text-gray-800 rounded-full px-3 py-1 text-xs font-medium\",\n                                                                            children: post.category.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                                className: \"p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-gray-500 text-sm mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 444,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: formatDate(post.published_at)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"•\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: getReadingTime(post)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-green-600 transition-colors line-clamp-2\",\n                                                                        children: post.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed\",\n                                                                        children: post.excerpt\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                                        children: post.author.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                            src: post.author.avatar,\n                                                                                            alt: post.author.name,\n                                                                                            width: 32,\n                                                                                            height: 32,\n                                                                                            className: \"rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                            lineNumber: 463,\n                                                                                            columnNumber: 39\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                            lineNumber: 471,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                        lineNumber: 461,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                                                        children: post.author.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                        lineNumber: 474,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-green-600 font-medium group-hover:text-green-700 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"Read more\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                        lineNumber: 478,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                        lineNumber: 479,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    post.tags && post.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-1 mt-4\",\n                                                                        children: post.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"text-xs px-2 py-1 rounded-full border-gray-300 text-gray-600\",\n                                                                                children: tag\n                                                                            }, tag, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 37\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, post.slug, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 21\n                                        }, this),\n                                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mt-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>fetchPosts(currentPage - 1, searchTerm, selectedCategory),\n                                                        disabled: currentPage === 1,\n                                                        className: \"rounded-full\",\n                                                        children: \"Previous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    Array.from({\n                                                        length: Math.min(5, totalPages)\n                                                    }, (_, i)=>{\n                                                        const page = i + 1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: currentPage === page ? \"default\" : \"outline\",\n                                                            onClick: ()=>fetchPosts(page, searchTerm, selectedCategory),\n                                                            className: \"rounded-full w-10 h-10 p-0\",\n                                                            children: page\n                                                        }, page, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 31\n                                                        }, this);\n                                                    }),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>fetchPosts(currentPage + 1, searchTerm, selectedCategory),\n                                                        disabled: currentPage === totalPages,\n                                                        className: \"rounded-full\",\n                                                        children: \"Next\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Loader2_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"No articles found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                            children: \"Try adjusting your search terms or category filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>{\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"All\");\n                                            },\n                                            variant: \"outline\",\n                                            className: \"rounded-full px-6 py-2\",\n                                            children: \"Clear Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-green-600 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                        children: \"Stay Updated with Our Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 mb-8\",\n                                        children: \"Get the latest impact stories, news, and insights delivered directly to your inbox\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"Enter your email\",\n                                                className: \"bg-white text-gray-900 border-0 rounded-full px-6 py-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                className: \"bg-amber-500 hover:bg-amber-600 text-black font-semibold rounded-full px-8 py-3\",\n                                                children: \"Subscribe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-200 mt-4\",\n                                        children: \"Join 5,000+ supporters staying informed about our impact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(BlogPage, \"yXb84VKRYyYmvNL4oG8x4TjaV2Y=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/page.tsx\n"));

/***/ })

});