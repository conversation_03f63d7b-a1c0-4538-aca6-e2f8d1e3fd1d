<?php $__env->startSection('title', 'Donation Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Donation Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.donations.index')); ?>">Donations</a></li>
                    <li class="breadcrumb-item active">Details</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('admin.donations.edit', $donation->id)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Donation
            </a>
            <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Donation Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Information</h6>
                    <span class="badge badge-<?php echo e($donation->payment_status === 'completed' ? 'success' : ($donation->payment_status === 'pending' ? 'warning' : 'danger')); ?>">
                        <?php echo e(ucfirst($donation->payment_status)); ?>

                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Donation ID:</label>
                                <p class="text-muted">#<?php echo e($donation->id); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Amount:</label>
                                <p class="text-success font-weight-bold">₦<?php echo e(number_format($donation->amount, 2)); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Currency:</label>
                                <p class="text-muted"><?php echo e(strtoupper($donation->currency)); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Donation Type:</label>
                                <p class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', $donation->donation_type))); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Payment Method:</label>
                                <p class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', $donation->payment_method))); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Payment Status:</label>
                                <p class="text-muted"><?php echo e(ucfirst($donation->payment_status)); ?></p>
                            </div>
                        </div>
                        <?php if($donation->purpose): ?>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="font-weight-bold">Purpose:</label>
                                <p class="text-muted"><?php echo e($donation->purpose); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if($donation->notes): ?>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="font-weight-bold">Notes:</label>
                                <p class="text-muted"><?php echo e($donation->notes); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Created:</label>
                                <p class="text-muted"><?php echo e($donation->created_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Last Updated:</label>
                                <p class="text-muted"><?php echo e($donation->updated_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Donor Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donor Information</h6>
                </div>
                <div class="card-body">
                    <?php if($donation->donor): ?>
                        <div class="form-group">
                            <label class="font-weight-bold">Name:</label>
                            <p class="text-muted"><?php echo e($donation->donor->first_name); ?> <?php echo e($donation->donor->last_name); ?></p>
                        </div>
                        <div class="form-group">
                            <label class="font-weight-bold">Email:</label>
                            <p class="text-muted"><?php echo e($donation->donor->email); ?></p>
                        </div>
                        <?php if($donation->donor->phone): ?>
                        <div class="form-group">
                            <label class="font-weight-bold">Phone:</label>
                            <p class="text-muted"><?php echo e($donation->donor->phone); ?></p>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <p class="text-muted"><?php echo e($donation->is_anonymous ? 'Anonymous Donor' : 'Donor information not available'); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if($donation->campaign): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Information</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Campaign:</label>
                        <p class="text-muted"><?php echo e($donation->campaign->title); ?></p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Goal:</label>
                        <p class="text-muted">₦<?php echo e(number_format($donation->campaign->goal_amount, 2)); ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/donations/show.blade.php ENDPATH**/ ?>