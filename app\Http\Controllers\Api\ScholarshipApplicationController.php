<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ScholarshipApplication;
use App\Models\Scholarship;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

class ScholarshipApplicationController extends Controller
{
    /**
     * Get all applications (admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = ScholarshipApplication::with(['scholarship', 'user']);

        // Filter by scholarship
        if ($request->has('scholarship_id')) {
            $query->where('scholarship_id', $request->scholarship_id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->has('category')) {
            $query->whereHas('scholarship', function ($q) use ($request) {
                $q->where('category', $request->category);
            });
        }

        $applications = $query->orderBy('created_at', 'desc')
                            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $applications
        ]);
    }

    /**
     * Show specific application (admin or owner)
     */
    public function show($id): JsonResponse
    {
        $application = ScholarshipApplication::with(['scholarship', 'user', 'reviewer'])
                                           ->findOrFail($id);

        // Check if user can view this application
        $user = Auth::user();
        if (!$user->hasRole('admin') && $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this application'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $application
        ]);
    }

    /**
     * Update application status (admin only)
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        $application = ScholarshipApplication::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:pending,under_review,approved,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $reviewer = Auth::user();
        $data = $validator->validated();
        $data['reviewed_by'] = $reviewer->id;
        $data['reviewed_at'] = now();

        $application->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Application status updated successfully',
            'data' => $application->fresh()
        ]);
    }

    /**
     * Approve application (admin only)
     */
    public function approve(Request $request, $id): JsonResponse
    {
        $application = ScholarshipApplication::findOrFail($id);
        $reviewer = Auth::user();

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $application->approve($reviewer, $request->admin_notes);

        return response()->json([
            'success' => true,
            'message' => 'Application approved successfully',
            'data' => $application->fresh()
        ]);
    }

    /**
     * Reject application (admin only)
     */
    public function reject(Request $request, $id): JsonResponse
    {
        $application = ScholarshipApplication::findOrFail($id);
        $reviewer = Auth::user();

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $application->reject($reviewer, $request->admin_notes);

        return response()->json([
            'success' => true,
            'message' => 'Application rejected successfully',
            'data' => $application->fresh()
        ]);
    }

    /**
     * Delete application (admin only)
     */
    public function destroy($id): JsonResponse
    {
        $application = ScholarshipApplication::findOrFail($id);
        
        // Decrement scholarship application count
        $application->scholarship->decrementApplications();
        
        $application->delete();

        return response()->json([
            'success' => true,
            'message' => 'Application deleted successfully'
        ]);
    }

    /**
     * Get applications by scholarship (admin only)
     */
    public function byScholarship($scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        
        $applications = ScholarshipApplication::with(['user'])
                                            ->where('scholarship_id', $scholarship->id)
                                            ->orderBy('created_at', 'desc')
                                            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'scholarship' => $scholarship,
                'applications' => $applications,
                'statistics' => [
                    'total' => $applications->count(),
                    'pending' => $applications->where('status', 'pending')->count(),
                    'under_review' => $applications->where('status', 'under_review')->count(),
                    'approved' => $applications->where('status', 'approved')->count(),
                    'rejected' => $applications->where('status', 'rejected')->count(),
                ]
            ]
        ]);
    }

    /**
     * Bulk approve applications (admin only)
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'application_ids' => 'required|array|min:1',
            'application_ids.*' => 'integer|exists:scholarship_applications,id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $reviewer = Auth::user();
        $approvedCount = 0;

        foreach ($request->application_ids as $applicationId) {
            $application = ScholarshipApplication::find($applicationId);
            if ($application && $application->status === 'pending') {
                $application->approve($reviewer, $request->admin_notes);
                $approvedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully approved {$approvedCount} applications"
        ]);
    }

    /**
     * Bulk reject applications (admin only)
     */
    public function bulkReject(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'application_ids' => 'required|array|min:1',
            'application_ids.*' => 'integer|exists:scholarship_applications,id',
            'admin_notes' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $reviewer = Auth::user();
        $rejectedCount = 0;

        foreach ($request->application_ids as $applicationId) {
            $application = ScholarshipApplication::find($applicationId);
            if ($application && $application->status === 'pending') {
                $application->reject($reviewer, $request->admin_notes);
                $rejectedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully rejected {$rejectedCount} applications"
        ]);
    }

    /**
     * Export applications data (admin only)
     */
    public function export(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        
        $applications = ScholarshipApplication::with(['user'])
                                            ->where('scholarship_id', $scholarship->id)
                                            ->get();

        $exportData = $applications->map(function ($application) {
            return [
                'application_id' => $application->application_id,
                'applicant_name' => $application->user->name,
                'applicant_email' => $application->user->email,
                'status' => $application->status,
                'submitted_at' => $application->submitted_at,
                'form_data' => $application->form_data,
                'admin_notes' => $application->admin_notes,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'scholarship' => $scholarship->title,
                'export_date' => now(),
                'applications' => $exportData
            ]
        ]);
    }
}
