<?php

// Debug the API response format
$baseUrl = 'http://127.0.0.1:8000';

echo "=== Debugging API Response Format ===\n\n";

// Get the raw response
$response = file_get_contents($baseUrl . '/api/v1/public-scholarships');
echo "Raw API Response:\n";
echo $response . "\n\n";

$data = json_decode($response, true);
echo "Decoded JSON structure:\n";
print_r($data);

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test individual scholarship
$response2 = file_get_contents($baseUrl . '/api/v1/public-scholarships/5');
echo "Individual scholarship response:\n";
echo $response2 . "\n\n";
