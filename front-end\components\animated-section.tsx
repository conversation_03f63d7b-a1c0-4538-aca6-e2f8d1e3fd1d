"use client"

import { useEffect, useState } from "react"
import type { ReactNode } from "react"
import { useInView } from "react-intersection-observer"

interface AnimatedSectionProps {
  children: ReactNode
  className?: string
  id?: string
}

export function AnimatedSection({ children, className = "", id }: AnimatedSectionProps) {
  const [hasAnimated, setHasAnimated] = useState(false)
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  })

  useEffect(() => {
    if (inView && !hasAnimated) {
      setHasAnimated(true)
    }
  }, [inView, hasAnimated])

  return (
    <section
      ref={ref}
      id={id}
      className={`${className} transition-opacity duration-500 ${
        hasAnimated ? "opacity-100 animate-fade-in-up" : "opacity-0"
      }`}
    >
      {children}
    </section>
  )
}
