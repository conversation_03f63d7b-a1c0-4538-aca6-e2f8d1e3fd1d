<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the role enum to include partner_organization
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'volunteer', 'donor', 'user', 'partner_organization') DEFAULT 'user'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the role enum to original values
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'volunteer', 'donor', 'user') DEFAULT 'user'");
    }
};
