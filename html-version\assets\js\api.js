/**
 * HLTKKQ Foundation - API Client
 * Vanilla JavaScript API client for Laravel backend integration
 */

// Configuration
const API_CONFIG = {
    baseURL: 'http://localhost:8000',
    apiVersion: '/api/v1',
    timeout: 30000
};

// Utility function to extract array data from paginated or direct responses
function extractArrayData(response) {
    if (!response || !response.data) return [];
    
    // If data is already an array, return it
    if (Array.isArray(response.data)) {
        return response.data;
    }
    
    // If data has a data property (paginated response), return that array
    if (response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
    }
    
    // Default to empty array
    return [];
}

// Main API Client Class
class ApiClient {
    constructor(baseURL = API_CONFIG.baseURL) {
        this.baseURL = baseURL;
    }

    // Core request method
    async request(endpoint, options = {}) {
        // Construct URL
        const url = this.baseURL.includes('/api/v1')
            ? `${this.baseURL}${endpoint}`
            : `${this.baseURL}${API_CONFIG.apiVersion}${endpoint}`;

        // Default headers
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...options.headers
        };

        // Add authentication header if token exists
        const token = localStorage.getItem('authToken');
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        // Request configuration
        const config = {
            headers,
            ...options
        };

        try {
            console.log(`API Request: ${options.method || 'GET'} ${url}`);
            
            const response = await fetch(url, config);
            const data = await response.json();
            
            console.log(`API Response for ${endpoint}:`, data);

            // Handle 401 Unauthorized - redirect to login
            if (response.status === 401) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                window.location.href = '/auth/login.html';
                return;
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // HTTP method shortcuts
    async get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }

    async post(endpoint, data = null, options = {}) {
        const config = {
            ...options,
            method: 'POST'
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        return this.request(endpoint, config);
    }

    async put(endpoint, data = null, options = {}) {
        const config = {
            ...options,
            method: 'PUT'
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        return this.request(endpoint, config);
    }

    async patch(endpoint, data = null, options = {}) {
        const config = {
            ...options,
            method: 'PATCH'
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        return this.request(endpoint, config);
    }

    async delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }

    // Authentication endpoints
    async login(email, password) {
        const response = await this.request('/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });

        // Store token and user data if login successful
        if (response.success && response.data?.access_token) {
            localStorage.setItem('authToken', response.data.access_token);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response;
    }

    async register(userData) {
        const response = await this.request('/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });

        // Store token and user data if registration successful
        if (response.success && response.data?.token) {
            localStorage.setItem('authToken', response.data.token);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response;
    }

    async logout() {
        try {
            await this.request('/logout', { method: 'POST' });
        } catch (error) {
            console.error('Logout request failed:', error);
        } finally {
            // Always clear local storage
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = '/index.html';
        }
    }

    // User profile endpoints
    async getCurrentUser() {
        return this.get('/user');
    }

    async updateProfile(profileData) {
        return this.put('/profile', profileData);
    }

    // Settings endpoints
    async getPublicSettings() {
        return this.get('/settings');
    }
}

// Create global API client instance
const apiClient = new ApiClient();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, apiClient, extractArrayData };
} else {
    window.ApiClient = ApiClient;
    window.apiClient = apiClient;
    window.extractArrayData = extractArrayData;
}
