import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './use-auth';

interface UseAuthGuardOptions {
  redirectTo?: string;
  requireAuth?: boolean;
}

export const useAuthGuard = (options: UseAuthGuardOptions = {}) => {
  const {
    redirectTo = '/auth/login',
    requireAuth = true,
  } = options;

  const router = useRouter();
  const { user, loading, checkAuth } = useAuth();

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        await checkAuth();

        if (requireAuth && !user) {
          router.push(redirectTo);
        } else if (!requireAuth && user) {
          // If user is authenticated but shouldn't be (e.g., login page)
          router.push('/dashboard');
        }
      } catch (error) {
        if (requireAuth) {
          router.push(redirectTo);
        }
      }
    };

    if (!loading) {
      checkAuthentication();
    }
  }, [user, loading, requireAuth, redirectTo, router, checkAuth]);

  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  };
};