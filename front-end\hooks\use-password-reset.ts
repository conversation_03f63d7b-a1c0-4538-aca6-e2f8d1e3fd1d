import { useState } from 'react';
import axios from 'axios';
import { useRouter } from 'next/navigation';

interface UsePasswordResetOptions {
  onSuccess?: () => void;
  redirectTo?: string;
}

export const usePasswordReset = (options: UsePasswordResetOptions = {}) => {
  const {
    onSuccess,
    redirectTo = '/login',
  } = options;

  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const requestReset = async (email: string) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setMessage(null);

      await axios.post('/api/auth/forgot-password', { email });
      setMessage('Password reset instructions have been sent to your email');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to send reset instructions');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetPassword = async (data: {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
  }) => {
    try {
      setIsSubmitting(true);
      setError(null);
      setMessage(null);

      if (data.password !== data.password_confirmation) {
        setError('Passwords do not match');
        return;
      }

      if (data.password.length < 8) {
        setError('Password must be at least 8 characters');
        return;
      }

      await axios.post('/api/auth/reset-password', data);
      setMessage('Password has been reset successfully');
      
      onSuccess?.();
      setTimeout(() => {
        router.push(redirectTo);
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reset password');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    requestReset,
    resetPassword,
    isSubmitting,
    error,
    message,
  };
}; 