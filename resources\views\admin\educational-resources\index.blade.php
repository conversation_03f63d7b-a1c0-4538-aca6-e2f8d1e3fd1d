@extends('layouts.admin')

@section('title', 'Educational Resources')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Educational Resources Management</h1>
        <div>
            <a href="{{ route('admin.educational-resources.create') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Add New Resource
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Resources</div>
                            <div class="h4">{{ $stats['total'] }}</div>
                        </div>
                        <div>
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active</div>
                            <div class="h4">{{ $stats['active'] }}</div>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Video Courses</div>
                            <div class="h4">{{ $stats['video_courses'] }}</div>
                        </div>
                        <div>
                            <i class="fas fa-video fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Featured</div>
                            <div class="h4">{{ $stats['featured'] }}</div>
                        </div>
                        <div>
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resources Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Educational Resources</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($resources as $resource)
                        <tr>
                            <td>
                                <strong>{{ $resource->title }}</strong>
                                @if($resource->is_featured)
                                    <span class="badge bg-warning ms-2">Featured</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ ucfirst($resource->type) }}</span>
                            </td>
                            <td>{{ $resource->category ?? 'General' }}</td>
                            <td>
                                <span class="badge bg-{{ $resource->is_active ? 'success' : 'secondary' }}">
                                    {{ $resource->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                @if($resource->is_featured)
                                    <span class="badge bg-warning ms-1">Featured</span>
                                @endif
                            </td>
                            <td>{{ $resource->created_at->format('M d, Y') }}</td>
                            <td>
                                <a href="{{ route('admin.educational-resources.show', $resource->id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.educational-resources.edit', $resource->id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ route('admin.educational-resources.destroy', $resource->id) }}" style="display: inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">No educational resources found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            @if($resources->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $resources->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
