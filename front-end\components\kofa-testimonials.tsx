"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Quote, <PERSON> } from "lucide-react"

interface Testimonial {
  id: number
  name: string
  role: string
  location: string
  quote: string
  image: string
  rating: number
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "Amina S.",
    role: "Scholarship Recipient",
    location: "ABU Zaria",
    quote: "Thanks to Kofa Foundation, I'm now studying Medicine at ABU Zaria. My dream came true!",
    image: "/placeholder.svg?height=200&width=200",
    rating: 5,
  },
  {
    id: 2,
    name: "Mr. <PERSON>",
    role: "Headteacher",
    location: "GS Kofan Doka",
    quote: "The books donated to our school library have improved literacy rates by 40%.",
    image: "/placeholder.svg?height=200&width=200",
    rating: 5,
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Primary School Student",
    location: "Kaduna State",
    quote: "I love my new school uniform and books. Now I can learn better and my friends think I look smart!",
    image: "/placeholder.svg?height=200&width=200",
    rating: 5,
  },
  {
    id: 4,
    name: "Malam Ibrahim",
    role: "Community Leader",
    location: "Katsina State",
    quote:
      "The adult literacy program has empowered our community. Many parents can now help their children with homework.",
    image: "/placeholder.svg?height=200&width=200",
    rating: 5,
  },
]

export function KofaTestimonials() {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [autoplay, setAutoplay] = useState(true)
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }))
  }

  const goToPrevious = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1))
    setTimeout(() => setIsAnimating(false), 500)
  }, [isAnimating])

  const goToNext = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1))
    setTimeout(() => setIsAnimating(false), 500)
  }, [isAnimating])

  useEffect(() => {
    if (!autoplay) return

    const interval = setInterval(goToNext, 5000)
    return () => clearInterval(interval)
  }, [autoplay, goToNext])

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`h-4 w-4 ${i < rating ? "text-amber-400 fill-current" : "text-gray-300"}`} />
    ))
  }

  return (
    <div className="relative max-w-4xl mx-auto">
      <div className="absolute top-1/2 -left-4 md:-left-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
          onClick={goToPrevious}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronLeft className="h-5 w-5" />
          <span className="sr-only">Previous testimonial</span>
        </Button>
      </div>

      <div className="overflow-hidden rounded-2xl">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${activeIndex * 100}%)`,
          }}
        >
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="w-full flex-shrink-0">
              <Card className="border-0 shadow-none overflow-hidden bg-transparent">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                    <div className="relative">
                      <div className="relative aspect-square overflow-hidden rounded-2xl shadow-xl">
                        <div
                          className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${
                            imagesLoaded[testimonial.id] ? "opacity-0" : "opacity-100"
                          }`}
                        />
                        <Image
                          src={testimonial.image || "/placeholder.svg"}
                          alt={testimonial.name}
                          fill
                          sizes="(max-width: 768px) 100vw, 33vw"
                          className={`object-cover transition-opacity duration-300 ${
                            imagesLoaded[testimonial.id] ? "opacity-100" : "opacity-0"
                          }`}
                          onLoad={() => handleImageLoad(testimonial.id)}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <div className="absolute bottom-4 left-4 right-4 text-white">
                          <div className="font-bold text-lg">{testimonial.name}</div>
                          <div className="text-sm opacity-90">{testimonial.role}</div>
                          <div className="text-xs opacity-80">{testimonial.location}</div>
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2 flex flex-col justify-center space-y-6">
                      <Quote className="h-12 w-12 text-green-600/30" />
                      <blockquote className="text-xl md:text-2xl text-muted-foreground italic leading-relaxed">
                        "{testimonial.quote}"
                      </blockquote>
                      <div className="flex items-center gap-2">{renderStars(testimonial.rating)}</div>
                      <div className="flex items-center gap-2">
                        {testimonials.map((_, index) => (
                          <button
                            key={index}
                            className={`h-2 rounded-full transition-all duration-300 ${
                              index === activeIndex ? "w-8 bg-green-600" : "w-2 bg-green-200 dark:bg-green-800"
                            }`}
                            onClick={() => {
                              setActiveIndex(index)
                            }}
                            aria-label={`Go to testimonial ${index + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      <div className="absolute top-1/2 -right-4 md:-right-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
          onClick={goToNext}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronRight className="h-5 w-5" />
          <span className="sr-only">Next testimonial</span>
        </Button>
      </div>
    </div>
  )
}
