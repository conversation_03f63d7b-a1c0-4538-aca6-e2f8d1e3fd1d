<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('text'); // text, image, json, boolean, etc.
            $table->text('description')->nullable();
            $table->string('group')->default('general'); // general, appearance, email, etc.
            $table->boolean('is_public')->default(false); // Whether this setting can be accessed publicly
            $table->timestamps();
        });

        // Insert default settings
        DB::table('settings')->insert([
            [
                'key' => 'app_name',
                'value' => 'Laravel NGO',
                'type' => 'text',
                'description' => 'Application name displayed throughout the site',
                'group' => 'general',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'app_logo',
                'value' => null,
                'type' => 'image',
                'description' => 'Application logo displayed in header',
                'group' => 'appearance',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_description',
                'value' => 'Making a difference in communities worldwide',
                'type' => 'text',
                'description' => 'Site description for meta tags and general use',
                'group' => 'general',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'description' => 'Primary contact email for the organization',
                'group' => 'general',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'contact_phone',
                'value' => '+234 ************',
                'type' => 'text',
                'description' => 'Primary contact phone number',
                'group' => 'general',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
