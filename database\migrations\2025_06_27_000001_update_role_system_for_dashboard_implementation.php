<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First expand the role enum to include all old and new values
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'volunteer', 'donor', 'user', 'partner_organization', 'normal', 'student', 'partner') DEFAULT 'normal'");

        // Now update existing users to use the new role names
        DB::statement("
            UPDATE users SET role = CASE
                WHEN role = 'user' THEN 'normal'
                WHEN role = 'volunteer' THEN 'normal'
                WHEN role = 'donor' THEN 'normal'
                WHEN role = 'partner_organization' THEN 'partner'
                WHEN role = 'admin' THEN 'admin'
                ELSE 'normal'
            END
        ");

        // Finally, set the role enum to only the new values
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('normal', 'student', 'partner', 'admin') DEFAULT 'normal'");

        // Update role_id mapping to match new system
        DB::statement("
            UPDATE users SET role_id = CASE
                WHEN role = 'normal' THEN 1
                WHEN role = 'student' THEN 2
                WHEN role = 'partner' THEN 3
                WHEN role = 'admin' THEN 4
                ELSE 1
            END
        ");

        // Create students table for partner organizations to manage their students
        Schema::create('partner_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('partner_id')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->string('class');
            $table->enum('gender', ['male', 'female']);
            $table->integer('age');
            $table->string('photo')->nullable();
            $table->string('parent_contact');
            $table->string('parent_name')->nullable();
            $table->text('address')->nullable();
            $table->json('application_history')->nullable(); // Track scholarship applications
            $table->enum('status', ['active', 'graduated', 'transferred', 'inactive'])->default('active');
            $table->timestamps();
            
            // Indexes
            $table->index(['partner_id', 'status']);
            $table->index('class');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the students table
        Schema::dropIfExists('partner_students');
        
        // Revert role enum back to original
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'volunteer', 'donor', 'user', 'partner_organization') DEFAULT 'user'");
        
        // Revert role names
        DB::statement("
            UPDATE users SET role = CASE
                WHEN role = 'normal' THEN 'user'
                WHEN role = 'student' THEN 'user'
                WHEN role = 'partner' THEN 'partner_organization'
                WHEN role = 'admin' THEN 'admin'
                ELSE 'user'
            END
        ");
    }
};
