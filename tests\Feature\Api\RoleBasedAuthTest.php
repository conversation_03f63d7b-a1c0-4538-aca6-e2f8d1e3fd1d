<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\Scholarship;
use Laravel\Sanctum\Sanctum;

class RoleBasedAuthTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $partnerUser;
    protected $studentUser;
    protected $partnerOrganization;

    protected function setUp(): void
    {
        parent::setUp();

        // Create partner organization
        $this->partnerOrganization = PartnerOrganization::factory()->create();

        // Create users with different roles
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->partnerUser = User::factory()->create([
            'role' => 'partner_organization',
            'partner_organization_id' => $this->partnerOrganization->id
        ]);
        $this->studentUser = User::factory()->create(['role' => 'user']);
    }

    /** @test */
    public function role_based_middleware_blocks_unauthorized_roles()
    {
        // Test admin-only endpoint with non-admin user
        Sanctum::actingAs($this->partnerUser);
        
        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(403);

        // Test partner-only endpoint with student user
        Sanctum::actingAs($this->studentUser);
        
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function role_based_middleware_allows_authorized_roles()
    {
        // Test admin access to analytics
        Sanctum::actingAs($this->adminUser);
        
        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(200);

        // Test partner access to dashboard
        Sanctum::actingAs($this->partnerUser);
        
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(200);
    }

    /** @test */
    public function scholarship_category_middleware_enforces_category_access()
    {
        $primaryScholarship = Scholarship::factory()->create(['category' => 'primary']);
        $universityScholarship = Scholarship::factory()->create(['category' => 'university']);

        // Partner should access primary but not university
        Sanctum::actingAs($this->partnerUser);
        
        $student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        // This should work (primary scholarship)
        $response = $this->postJson('/api/v1/multi-year-applications/create', [
            'student_id' => $student->id,
            'scholarship_id' => $primaryScholarship->id,
            'academic_year' => now()->year,
            'application_data' => ['test' => 'data']
        ]);
        
        // Should fail validation but not authorization (422 vs 403)
        $response->assertStatus(422); // Validation error, not auth error

        // This should fail authorization (university scholarship)
        $response = $this->postJson('/api/v1/multi-year-applications/create', [
            'student_id' => $student->id,
            'scholarship_id' => $universityScholarship->id,
            'academic_year' => now()->year,
            'application_data' => ['test' => 'data']
        ]);
        
        $response->assertStatus(403); // Authorization error
    }

    /** @test */
    public function student_access_control_enforces_ownership()
    {
        $partnerStudent = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id,
            'user_id' => $this->studentUser->id
        ]);

        $otherStudent = Student::factory()->create([
            'school_id' => 999, // Different school
            'user_id' => User::factory()->create(['role' => 'user'])->id
        ]);

        // Student should access own data
        Sanctum::actingAs($this->studentUser);
        
        $response = $this->getJson("/api/v1/student-progression/student/{$partnerStudent->id}/history");
        $response->assertStatus(200);

        // Student should not access other student's data
        $response = $this->getJson("/api/v1/student-progression/student/{$otherStudent->id}/history");
        $response->assertStatus(403);
    }

    /** @test */
    public function partner_access_control_enforces_school_ownership()
    {
        $ownStudent = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        $otherStudent = Student::factory()->create([
            'school_id' => 999 // Different school
        ]);

        // Partner should access own school's students
        Sanctum::actingAs($this->partnerUser);
        
        $response = $this->getJson("/api/v1/student-progression/student/{$ownStudent->id}/history");
        $response->assertStatus(200);

        // Partner should not access other school's students
        $response = $this->getJson("/api/v1/student-progression/student/{$otherStudent->id}/history");
        $response->assertStatus(403);
    }

    /** @test */
    public function admin_has_full_access()
    {
        $student = Student::factory()->create();

        Sanctum::actingAs($this->adminUser);

        // Admin should access any student data
        $response = $this->getJson("/api/v1/student-progression/student/{$student->id}/history");
        $response->assertStatus(200);

        // Admin should access analytics
        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(200);

        // Admin should access multi-year analytics
        $response = $this->getJson('/api/v1/multi-year-applications/analytics');
        $response->assertStatus(200);
    }

    /** @test */
    public function unauthenticated_requests_are_rejected()
    {
        $endpoints = [
            '/api/v1/partner-dashboard',
            '/api/v1/student-progression/analytics',
            '/api/v1/multi-year-applications/analytics'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            $response->assertStatus(401);
        }
    }

    /** @test */
    public function middleware_returns_proper_error_messages()
    {
        // Test role-based auth error message
        Sanctum::actingAs($this->studentUser);
        
        $response = $this->getJson('/api/v1/student-progression/analytics');
        
        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Insufficient permissions',
                    'error' => 'Unauthorized'
                ]);

        // Test unauthenticated error message
        $response = $this->getJson('/api/v1/partner-dashboard');
        
        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Authentication required',
                    'error' => 'Unauthenticated'
                ]);
    }

    /** @test */
    public function category_middleware_provides_detailed_error_info()
    {
        $universityScholarship = Scholarship::factory()->create(['category' => 'university']);
        $student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        Sanctum::actingAs($this->partnerUser);

        $response = $this->postJson('/api/v1/multi-year-applications/create', [
            'student_id' => $student->id,
            'scholarship_id' => $universityScholarship->id,
            'academic_year' => now()->year,
            'application_data' => ['test' => 'data']
        ]);

        $response->assertStatus(403)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'scholarship_category',
                    'user_role'
                ]);
    }
}
