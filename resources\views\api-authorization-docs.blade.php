<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGO Platform API - Authorization Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-y: auto;
            z-index: 1000;
        }
        .main-content {
            margin-left: 280px;
            padding: 2rem;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white !important;
        }
        .endpoint-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1.5rem;
        }
        .method-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .role-badge {
            font-size: 0.7rem;
            margin: 0.1rem;
        }
        .policy-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar p-3">
        <h4 class="text-white mb-4">API Authorization</h4>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="#overview">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#roles">User Roles</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#middleware">Middleware</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#policies">Policies</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#gates">Gates</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#endpoints">Protected Endpoints</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#examples">Usage Examples</a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Overview Section -->
            <section id="overview" class="mb-5">
                <h1 class="mb-4">NGO Platform API Authorization System</h1>
                <div class="alert alert-info">
                    <h5>🔐 Comprehensive Role-Based Access Control</h5>
                    <p>This documentation covers the complete authorization system for the NGO Platform API, including role-based access control, middleware protection, policies, and gates.</p>
                </div>
            </section>

            <!-- User Roles Section -->
            <section id="roles" class="mb-5">
                <h2>User Roles</h2>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5>👑 Admin</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Role:</strong> <code>admin</code></p>
                                <p><strong>Access Level:</strong> Full system access</p>
                                <ul class="small">
                                    <li>All scholarship categories</li>
                                    <li>All student data</li>
                                    <li>System analytics</li>
                                    <li>Bulk operations</li>
                                    <li>User management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5>🏫 Partner Organization</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Role:</strong> <code>partner_organization</code></p>
                                <p><strong>Access Level:</strong> Institutional access</p>
                                <ul class="small">
                                    <li>Primary & Secondary scholarships</li>
                                    <li>Own students only</li>
                                    <li>Institutional analytics</li>
                                    <li>Student progression</li>
                                    <li>Application management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h5>🎓 Student</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Role:</strong> <code>user</code></p>
                                <p><strong>Access Level:</strong> Personal access</p>
                                <ul class="small">
                                    <li>University scholarships only</li>
                                    <li>Own data only</li>
                                    <li>Personal applications</li>
                                    <li>Educational resources</li>
                                    <li>Application tracking</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Middleware Section -->
            <section id="middleware" class="mb-5">
                <h2>Authorization Middleware</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="endpoint-card card">
                            <div class="card-header">
                                <h5>RoleBasedApiAuth</h5>
                                <span class="badge bg-primary">role.auth</span>
                            </div>
                            <div class="card-body">
                                <p>Enforces role-based access control for API endpoints.</p>
                                <pre><code class="language-php">// Usage in routes
Route::middleware('role.auth:admin,partner_organization')

// Multiple roles (OR logic)
Route::middleware('role.auth:admin,user')</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="endpoint-card card">
                            <div class="card-header">
                                <h5>ScholarshipCategoryAuth</h5>
                                <span class="badge bg-success">scholarship.category</span>
                            </div>
                            <div class="card-body">
                                <p>Enforces category-based access for scholarship operations.</p>
                                <pre><code class="language-php">// Usage in routes
Route::middleware('scholarship.category')

// Automatically checks:
// - Partner: primary, secondary
// - Student: university</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Policies Section -->
            <section id="policies" class="mb-5">
                <h2>Authorization Policies</h2>
                <div class="policy-section">
                    <h4>ScholarshipApplicationPolicy</h4>
                    <p>Controls access to scholarship application operations.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Available Methods:</h6>
                            <ul class="small">
                                <li><code>viewAny()</code> - View application lists</li>
                                <li><code>view()</code> - View specific application</li>
                                <li><code>create()</code> - Create new applications</li>
                                <li><code>update()</code> - Update applications</li>
                                <li><code>delete()</code> - Delete applications</li>
                                <li><code>review()</code> - Approve/reject applications</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Special Methods:</h6>
                            <ul class="small">
                                <li><code>createForStudent()</code> - Create for specific student</li>
                                <li><code>viewStatistics()</code> - View application stats</li>
                                <li><code>export()</code> - Export application data</li>
                                <li><code>bulkOperations()</code> - Bulk actions</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="policy-section">
                    <h4>StudentProgressionPolicy</h4>
                    <p>Controls access to student progression tracking.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Core Methods:</h6>
                            <ul class="small">
                                <li><code>viewAny()</code> - View progression lists</li>
                                <li><code>view()</code> - View specific progression</li>
                                <li><code>create()</code> - Create progressions</li>
                                <li><code>update()</code> - Update progressions</li>
                                <li><code>delete()</code> - Delete progressions</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Advanced Methods:</h6>
                            <ul class="small">
                                <li><code>review()</code> - Approve progressions</li>
                                <li><code>viewAnalytics()</code> - View analytics</li>
                                <li><code>bulkOperations()</code> - Bulk operations</li>
                                <li><code>viewStudentHistory()</code> - Student history</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Gates Section -->
            <section id="gates" class="mb-5">
                <h2>Authorization Gates</h2>
                <div class="row">
                    <div class="col-md-4">
                        <h5>Dashboard Access</h5>
                        <ul class="small">
                            <li><code>access-admin-panel</code></li>
                            <li><code>access-partner-dashboard</code></li>
                            <li><code>access-student-dashboard</code></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>Scholarship Operations</h5>
                        <ul class="small">
                            <li><code>manage-scholarships</code></li>
                            <li><code>review-applications</code></li>
                            <li><code>access-primary-scholarships</code></li>
                            <li><code>access-secondary-scholarships</code></li>
                            <li><code>access-university-scholarships</code></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>System Operations</h5>
                        <ul class="small">
                            <li><code>bulk-operations</code></li>
                            <li><code>view-analytics</code></li>
                            <li><code>export-data</code></li>
                            <li><code>manage-system-settings</code></li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Protected Endpoints Section -->
            <section id="endpoints" class="mb-5">
                <h2>Protected API Endpoints</h2>
                
                <div class="endpoint-card card">
                    <div class="card-header">
                        <h5>Partner Organization Endpoints</h5>
                        <span class="role-badge badge bg-success">partner_organization</span>
                        <span class="role-badge badge bg-primary">admin</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>Endpoint</th>
                                        <th>Description</th>
                                        <th>Additional Middleware</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="method-badge badge bg-info">GET</span></td>
                                        <td><code>/api/v1/partner-dashboard</code></td>
                                        <td>Partner dashboard data</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td><span class="method-badge badge bg-info">GET</span></td>
                                        <td><code>/api/v1/partner-scholarships/available</code></td>
                                        <td>Available scholarships</td>
                                        <td>scholarship.category</td>
                                    </tr>
                                    <tr>
                                        <td><span class="method-badge badge bg-success">POST</span></td>
                                        <td><code>/api/v1/partner-scholarships/{id}/apply</code></td>
                                        <td>Apply for scholarship</td>
                                        <td>scholarship.category</td>
                                    </tr>
                                    <tr>
                                        <td><span class="method-badge badge bg-info">GET</span></td>
                                        <td><code>/api/v1/student-progression/analytics</code></td>
                                        <td>Progression analytics</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="endpoint-card card">
                    <div class="card-header">
                        <h5>Student Endpoints</h5>
                        <span class="role-badge badge bg-warning text-dark">user</span>
                        <span class="role-badge badge bg-primary">admin</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>Endpoint</th>
                                        <th>Description</th>
                                        <th>Additional Middleware</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="method-badge badge bg-info">GET</span></td>
                                        <td><code>/api/v1/student-scholarships/available</code></td>
                                        <td>Available university scholarships</td>
                                        <td>scholarship.category</td>
                                    </tr>
                                    <tr>
                                        <td><span class="method-badge badge bg-success">POST</span></td>
                                        <td><code>/api/v1/student-scholarships/{id}/apply</code></td>
                                        <td>Apply for scholarship</td>
                                        <td>scholarship.category</td>
                                    </tr>
                                    <tr>
                                        <td><span class="method-badge badge bg-info">GET</span></td>
                                        <td><code>/api/v1/multi-year-applications/student/{id}/history</code></td>
                                        <td>Student application history</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Usage Examples Section -->
            <section id="examples" class="mb-5">
                <h2>Usage Examples</h2>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Authentication Headers</h5>
                    </div>
                    <div class="card-body">
                        <pre><code class="language-javascript">// Required headers for all protected endpoints
const headers = {
    'Authorization': 'Bearer YOUR_API_TOKEN',
    'Accept': 'application/json',
    'Content-Type': 'application/json'
};</code></pre>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Partner Organization Example</h5>
                    </div>
                    <div class="card-body">
                        <pre><code class="language-javascript">// Partner applying for scholarship on behalf of student
const response = await fetch('/api/v1/partner-scholarships/123/apply', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
        student_id: 456,
        application_data: {
            // Application fields
        }
    })
});</code></pre>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Error Responses</h5>
                    </div>
                    <div class="card-body">
                        <pre><code class="language-json">// 401 Unauthorized (missing/invalid token)
{
    "success": false,
    "message": "Authentication required",
    "error": "Unauthenticated"
}

// 403 Forbidden (insufficient permissions)
{
    "success": false,
    "message": "Insufficient permissions",
    "error": "Unauthorized",
    "required_roles": ["admin", "partner_organization"],
    "user_role": "user"
}</code></pre>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
