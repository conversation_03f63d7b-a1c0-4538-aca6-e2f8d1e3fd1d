const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL

interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
}

// Utility function to extract array data from paginated or direct responses
const extractArrayData = (response: any): any[] => {
  if (!response || !response.data) return []
  
  // If data is already an array, return it
  if (Array.isArray(response.data)) {
    return response.data
  }
  
  // If data has a data property (paginated response), return that array
  if (response.data.data && Array.isArray(response.data.data)) {
    return response.data.data
  }
  
  // Default to empty array
  return []
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL ?? "") {
    this.baseURL = baseURL
  }

  async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Use the base URL directly if it already contains /api/v1, otherwise add it
    const url = this.baseURL.includes('/api/v1')
      ? `${this.baseURL}${endpoint}`
      : `${this.baseURL}/api/v1${endpoint}`

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // Authentication is now handled by Laravel - no token needed for public API calls

    try {
      console.log(`API Request: ${options.method || 'GET'} ${url}`) // Debug logging
      const response = await fetch(url, config)
      const data = await response.json()
      console.log(`API Response for ${endpoint}:`, data) // Debug logging

      // Authentication is handled by Laravel - no need to handle 401s here

      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // HTTP method shortcuts
  async get<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const config: RequestInit = {
      ...options,
      method: 'POST',
    }

    if (data) {
      config.body = JSON.stringify(data)
    }

    return this.request<T>(endpoint, config)
  }

  async put<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const config: RequestInit = {
      ...options,
      method: 'PUT',
    }

    if (data) {
      config.body = JSON.stringify(data)
    }

    return this.request<T>(endpoint, config)
  }

  async patch<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const config: RequestInit = {
      ...options,
      method: 'PATCH',
    }

    if (data) {
      config.body = JSON.stringify(data)
    }

    return this.request<T>(endpoint, config)
  }

  async delete<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }

  // Authentication is now handled by Laravel

  async register(userData: {
    first_name: string
    last_name: string
    email: string
    password: string
    password_confirmation: string
    phone_number?: string
    address?: string
    date_of_birth?: string
    city?: string
    state?: string
    country?: string
    user_type?: 'student' | 'partner' | 'volunteer'
    // Additional data for specific user types
    additional_data?: any
  }) {
    // First, register the basic user
    const response = await this.request('/register', {
      method: 'POST',
      body: JSON.stringify({
        first_name: userData.first_name,
        last_name: userData.last_name,
        email: userData.email,
        password: userData.password,
        password_confirmation: userData.password_confirmation,
        phone_number: userData.phone_number,
        address: userData.address,
        date_of_birth: userData.date_of_birth,
        city: userData.city,
        state: userData.state,
        country: userData.country,
      }),
    })

    // Store token and user data if registration successful
    if (response.success && response.data?.token) {
      if (typeof window !== 'undefined') {
        localStorage.setItem('authToken', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))
      }
    }

    // If registration is successful, handle user type specific data
    if (response.success && userData.user_type && userData.additional_data) {
      try {
        if (userData.user_type === 'volunteer') {
          // Submit volunteer application
          await this.applyAsVolunteer(userData.additional_data)
        } else if (userData.user_type === 'student' || userData.user_type === 'partner') {
          // Store additional data in user preferences
          const preferences = {
            user_type: userData.user_type,
            profile_data: userData.additional_data,
            profile_completed: true
          }
          await this.updateUserPreferences(preferences)
        }
      } catch (error) {
        console.error('Additional data submission failed:', error)
        // Return the user registration success but note additional data failed
        return {
          ...response,
          message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'
        }
      }
    }

    return response
  }



  // Profile endpoints
  async getProfile() {
    return this.request('/profile')
  }

  async updateProfile(profileData: any) {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    })
  }

  async updateUserPreferences(preferences: any) {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify({ preferences }),
    })
  }

  async uploadAvatar(file: File) {
    const formData = new FormData()
    formData.append('avatar', file)

    // Use the base URL directly if it already contains /api/v1, otherwise add it
    const url = this.baseURL.includes('/api/v1') 
      ? `${this.baseURL}/profile/avatar`
      : `${this.baseURL}/api/v1/profile/avatar`

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
        },
        body: formData,
      })

      const data = await response.json()

      return data
    } catch (error) {
      console.error('Avatar upload failed:', error)
      throw error
    }
  }

  async changePassword(passwordData: {
    current_password: string
    new_password: string
    new_password_confirmation: string
  }) {
    return this.request('/profile/password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    })
  }

  async generateQrCode() {
    return this.request('/profile/generate-qr', {
      method: 'POST',
    })
  }

  async getIdCard() {
    return this.request('/profile/id-card')
  }

  // Dashboard endpoints
  async getDashboardSummary() {
    return this.request('/dashboard/summary')
  }

  // Volunteer endpoints
  async getVolunteerApplication() {
    return this.request('/volunteer/application')
  }

  async getVolunteerHours() {
    return this.request('/volunteer/hours')
  }

  async getVolunteerOpportunities() {
    return this.request('/volunteer/opportunities')
  }

  async logVolunteerHours(hoursData: any) {
    return this.request('/volunteer/hours', {
      method: 'POST',
      body: JSON.stringify(hoursData),
    })
  }

  // Scholarship endpoints
  async getMyScholarshipApplications() {
    return this.request('/scholarships/my-applications')
  }

  async getScholarships() {
    return this.request('/scholarships')
  }

  async applyForScholarship(scholarshipId: number, applicationData: any) {
    return this.request(`/scholarships/${scholarshipId}/apply`, {
      method: 'POST',
      body: JSON.stringify(applicationData),
    })
  }

  // Event endpoints
  async getMyEventRegistrations() {
    return this.request('/events/my-registrations')
  }

  async getUpcomingEvents() {
    return this.request('/events/upcoming')
  }

  // Helper method for building query strings
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(`${key}[]`, item))
        } else {
          searchParams.append(key, value.toString())
        }
      }
    })

    return searchParams.toString()
  }

  // Admin Dashboard endpoints
  async getAdminDashboard() {
    return this.request('/admin/dashboard')
  }

  async getAdminAnalytics(params?: { days?: number }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/dashboard/analytics${queryString}`)
  }

  async getAdminStats(params?: { period?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/dashboard/stats${queryString}`)
  }

  // Admin User Management
  async getAdminUsers(params?: {
    page?: number
    per_page?: number
    search?: string
    role?: string
    status?: string
    date_range?: string
    sort_by?: string
    sort_order?: string
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/users${queryString}`)
  }

  async createAdminUser(userData: any) {
    return this.request('/admin/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  }

  async updateAdminUser(userId: number, userData: any) {
    return this.request(`/admin/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    })
  }

  async deleteAdminUser(userId: number) {
    return this.request(`/admin/users/${userId}`, {
      method: 'DELETE',
    })
  }

  async bulkActionUsers(data: { action: string; user_ids: number[] }) {
    return this.request('/admin/users/bulk-action', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async exportUsers(params?: { format?: string; role?: string; status?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/users/export${queryString}`)
  }

  // Admin Scholarship Management
  async getScholarshipApplications(params?: {
    page?: number
    per_page?: number
    status?: string
    scholarship_id?: number
    search?: string
    date_range?: string
    sort_by?: string
    sort_order?: string
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/scholarship-applications${queryString}`)
  }

  async reviewScholarshipApplication(applicationId: number, reviewData: any) {
    return this.request(`/admin/scholarship-applications/${applicationId}/review`, {
      method: 'PUT',
      body: JSON.stringify(reviewData),
    })
  }

  async bulkActionScholarshipApplications(data: {
    action: string
    application_ids: number[]
    review_notes?: string
    award_amount?: number
  }) {
    return this.request('/admin/scholarship-applications/bulk-action', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async exportScholarshipApplications(params?: {
    format?: string
    status?: string
    scholarship_id?: number
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/scholarship-applications/export${queryString}`)
  }

  async getScholarshipStatistics() {
    return this.request('/admin/scholarships/statistics')
  }

  // Admin Event Management
  async getAdminEvents(params?: {
    page?: number
    per_page?: number
    search?: string
    status?: string
    date_range?: string
    sort_by?: string
    sort_order?: string
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/events${queryString}`)
  }

  async createAdminEvent(eventData: any) {
    return this.request('/admin/events', {
      method: 'POST',
      body: JSON.stringify(eventData),
    })
  }

  async updateAdminEvent(eventId: number, eventData: any) {
    return this.request(`/admin/events/${eventId}`, {
      method: 'PUT',
      body: JSON.stringify(eventData),
    })
  }

  async deleteAdminEvent(eventId: number) {
    return this.request(`/admin/events/${eventId}`, {
      method: 'DELETE',
    })
  }

  async bulkActionEvents(data: { action: string; event_ids: number[] }) {
    return this.request('/admin/events/bulk-action', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async exportEvents(params?: { format?: string; status?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/events/export${queryString}`)
  }

  async getEventStatistics() {
    return this.request('/admin/events/statistics')
  }

  // Admin Program Management
  async getAdminPrograms(params?: {
    page?: number
    per_page?: number
    search?: string
    status?: string
    date_range?: string
    sort_by?: string
    sort_order?: string
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/programs${queryString}`)
  }

  async createAdminProgram(programData: any) {
    return this.request('/admin/programs', {
      method: 'POST',
      body: JSON.stringify(programData),
    })
  }

  async updateAdminProgram(programId: number, programData: any) {
    return this.request(`/admin/programs/${programId}`, {
      method: 'PUT',
      body: JSON.stringify(programData),
    })
  }

  async deleteAdminProgram(programId: number) {
    return this.request(`/admin/programs/${programId}`, {
      method: 'DELETE',
    })
  }

  // Admin Blog Management
  async getAdminBlogPosts(params?: {
    page?: number
    per_page?: number
    search?: string
    status?: string
    date_range?: string
    sort_by?: string
    sort_order?: string
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/blog/posts${queryString}`)
  }

  async createAdminBlogPost(postData: any) {
    return this.request('/admin/blog/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    })
  }

  async updateAdminBlogPost(postId: number, postData: any) {
    return this.request(`/admin/blog/posts/${postId}`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    })
  }

  async deleteAdminBlogPost(postId: number) {
    return this.request(`/admin/blog/posts/${postId}`, {
      method: 'DELETE',
    })
  }

  async exportBlogPosts(params?: { format?: string; status?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/admin/blog/posts/export${queryString}`)
  }

  // Donation endpoints
  async getMyDonations() {
    return this.request('/donations/my-donations')
  }

  async getDonationCampaigns() {
    return this.request('/donations/campaigns')
  }

  // Blog endpoints
  async getBlogPosts(page = 1) {
    return this.request(`/blog/posts?page=${page}`)
  }

  async getBlogPost(slug: string) {
    return this.request(`/blog/posts/${slug}`)
  }

  // Events endpoints
  async getEvents(page = 1) {
    return this.request(`/events?page=${page}`)
  }

  async getEvent(id: string) {
    return this.request(`/events/${id}`)
  }

  async registerForEvent(eventId: string, additionalInfo?: any) {
    return this.request(`/events/${eventId}/register`, {
      method: 'POST',
      body: JSON.stringify({ additional_info: additionalInfo }),
    })
  }

  // Programs endpoints
  async getPrograms(page = 1) {
    return this.request(`/programs?page=${page}`)
  }

  async getProgram(slug: string) {
    return this.request(`/programs/${slug}`)
  }

  // Additional scholarship endpoint
  async getScholarship(id: string) {
    return this.request(`/scholarships/${id}`)
  }

  // Public scholarships endpoints (no authentication required)
  async getPublicScholarships(params?: {
    category?: string
    search?: string
    sort?: string
    featured?: boolean
    page?: number
    per_page?: number
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/public-scholarships${queryString}`)
  }

  async getPublicScholarship(id: string | number) {
    return this.request(`/public-scholarships/${id}`)
  }

  // Project scholarship endpoints for Projects page
  async getProjectScholarshipStatistics() {
    return this.request('/project-scholarships/statistics')
  }

  async getProjectScholarshipPortfolio() {
    return this.request('/project-scholarships/portfolio')
  }

  async getScholarshipsByStatus(status: string = 'active') {
    return this.request(`/project-scholarships/by-status?status=${status}`)
  }

  async submitScholarshipApplication(scholarshipId: string, formData: FormData) {
    // Use the same URL construction logic as the request method
    const url = this.baseURL.includes('/api/v1')
      ? `${this.baseURL}/apply-scholarship/${scholarshipId}`
      : `${this.baseURL}/api/v1/apply-scholarship/${scholarshipId}`

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        body: formData
      })

      const data = await response.json()
      console.log(`API Response for scholarship application:`, data)

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error('Error submitting scholarship application:', error)
      throw error
    }
  }

  async applyAsVolunteer(applicationData: any) {
    return this.request('/volunteer/apply', {
      method: 'POST',
      body: JSON.stringify(applicationData),
    })
  }

  // Contact endpoints
  async submitContactForm(contactData: any) {
    return this.request('/contact', {
      method: 'POST',
      body: JSON.stringify(contactData),
    })
  }

  // Newsletter endpoints
  async subscribeToNewsletter(email: string) {
    return this.request('/newsletter/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email }),
    })
  }

  // Settings endpoint
  async getSettings() {
    return this.request('/settings')
  }

  // Partner Dashboard endpoints
  async getPartnerDashboard() {
    return this.request('/partner-dashboard')
  }

  // Partner Scholarship endpoints
  async getPartnerAvailableScholarships(params?: { category?: string; status?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/partner-scholarships/available${queryString}`)
  }

  async getPartnerScholarshipDetails(scholarshipId: string) {
    return this.request(`/partner-scholarships/${scholarshipId}/details`)
  }

  async applyForStudentScholarship(scholarshipId: string, applicationData: any) {
    return this.request(`/partner-scholarships/${scholarshipId}/apply`, {
      method: 'POST',
      body: applicationData
    })
  }

  async getPartnerApplications(params?: {
    status?: string;
    category?: string;
    academic_year?: string;
    page?: number;
    per_page?: number;
  }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/partner-scholarships/my-applications${queryString}`)
  }

  async getPartnerStudents(params?: { grade_level?: string; academic_year?: string }) {
    const queryString = params ? `?${this.buildQueryString(params)}` : ''
    return this.request(`/partner-students${queryString}`)
  }

  async addPartnerStudent(studentData: any) {
    return this.request('/partner-students', {
      method: 'POST',
      body: studentData
    })
  }
}

// Create and export a default instance
export const apiClient = new ApiClient()

// Export the class for custom instances
export { ApiClient }

// Export the extractArrayData utility function
export { extractArrayData }

// Authentication is now handled by Laravel - helper functions removed