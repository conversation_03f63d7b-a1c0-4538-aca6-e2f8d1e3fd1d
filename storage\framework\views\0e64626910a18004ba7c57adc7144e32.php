

<?php $__env->startSection('title', 'Contact Messages'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Contact Messages</h1>
        <div>
            <a href="<?php echo e(route('admin.contact.faq')); ?>" class="btn btn-info me-2">
                <i class="fas fa-question-circle me-2"></i>Manage FAQ
            </a>
            <button class="btn btn-primary">
                <i class="fas fa-download me-2"></i>Export Messages
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Messages</div>
                            <div class="h4"><?php echo e($messages->total() ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New Messages</div>
                            <div class="h4"><?php echo e($messages->where('status', 'new')->count() ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">In Progress</div>
                            <div class="h4"><?php echo e($messages->where('status', 'in_progress')->count() ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Resolved</div>
                            <div class="h4"><?php echo e($messages->where('status', 'resolved')->count() ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Messages</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.contact.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="new">New</option>
                            <option value="in_progress">In Progress</option>
                            <option value="resolved">Resolved</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="priority" class="form-select">
                            <option value="">All Priorities</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Search messages..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Contact Messages</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $messages ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($message->id ?? rand(1, 100)); ?></td>
                            <td><?php echo e($message->name ?? 'John Doe'); ?></td>
                            <td><?php echo e($message->email ?? '<EMAIL>'); ?></td>
                            <td><?php echo e(Str::limit($message->subject ?? 'Sample Subject', 30)); ?></td>
                            <td>
                                <?php
                                    $status = $message->status ?? 'new';
                                    $statusClass = $status === 'new' ? 'warning' : ($status === 'in_progress' ? 'info' : 'success');
                                ?>
                                <span class="badge bg-<?php echo e($statusClass); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $status))); ?></span>
                            </td>
                            <td>
                                <?php
                                    $priority = $message->priority ?? 'medium';
                                    $priorityClass = $priority === 'high' ? 'danger' : ($priority === 'medium' ? 'warning' : 'secondary');
                                ?>
                                <span class="badge bg-<?php echo e($priorityClass); ?>"><?php echo e(ucfirst($priority)); ?></span>
                            </td>
                            <td><?php echo e($message->created_at ? $message->created_at->format('M j, Y') : now()->format('M j, Y')); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.contact.messages.show', $message->id ?? 1)); ?>" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-success" onclick="markAsResolved(<?php echo e($message->id ?? 1); ?>)" title="Mark as Resolved">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteMessage(<?php echo e($message->id ?? 1); ?>)" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php for($i = 1; $i <= 10; $i++): ?>
                        <tr>
                            <td><?php echo e($i); ?></td>
                            <td>Contact Person <?php echo e($i); ?></td>
                            <td>contact<?php echo e($i); ?>@example.com</td>
                            <td><?php echo e(['Inquiry about programs', 'Volunteer application', 'Donation question', 'General inquiry', 'Partnership proposal'][array_rand(['Inquiry about programs', 'Volunteer application', 'Donation question', 'General inquiry', 'Partnership proposal'])]); ?></td>
                            <td>
                                <?php $status = ['new', 'in_progress', 'resolved'][array_rand(['new', 'in_progress', 'resolved'])]; ?>
                                <span class="badge bg-<?php echo e($status === 'new' ? 'warning' : ($status === 'in_progress' ? 'info' : 'success')); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $status))); ?></span>
                            </td>
                            <td>
                                <?php $priority = ['low', 'medium', 'high'][array_rand(['low', 'medium', 'high'])]; ?>
                                <span class="badge bg-<?php echo e($priority === 'high' ? 'danger' : ($priority === 'medium' ? 'warning' : 'secondary')); ?>"><?php echo e(ucfirst($priority)); ?></span>
                            </td>
                            <td><?php echo e(now()->subDays(rand(1, 30))->format('M j, Y')); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.contact.messages.show', $i)); ?>" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-success" onclick="markAsResolved(<?php echo e($i); ?>)" title="Mark as Resolved">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteMessage(<?php echo e($i); ?>)" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endfor; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if(isset($messages) && method_exists($messages, 'links')): ?>
                <div class="d-flex justify-content-center">
                    <?php echo e($messages->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function markAsResolved(messageId) {
    if (confirm('Mark this message as resolved?')) {
        // In a real implementation, make an AJAX call
        alert('Message marked as resolved (placeholder)');
    }
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        // In a real implementation, submit delete form
        alert('Message deleted (placeholder)');
    }
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/contact/index.blade.php ENDPATH**/ ?>