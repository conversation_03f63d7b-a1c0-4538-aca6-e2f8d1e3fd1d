<?php $__env->startSection('title', 'Scholarship Details - Partner Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-6xl mx-auto">
                    <div class="flex items-center mb-4">
                        <a href="<?php echo e(route('partner.scholarships.index')); ?>" class="mr-4 p-2 hover:bg-white/20 rounded-lg transition-colors duration-300">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div class="flex-1">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2"><?php echo e($scholarship->title); ?></h1>
                            <div class="flex flex-wrap items-center gap-4 text-sm">
                                <span class="inline-flex items-center px-3 py-1 rounded-full bg-white/20">
                                    <i class="fas <?php echo e($scholarship->category == 'primary' ? 'fa-child' : 'fa-user-graduate'); ?> mr-2"></i>
                                    <?php echo e(ucfirst($scholarship->category)); ?> School
                                </span>
                                <span class="inline-flex items-center">
                                    <i class="fas fa-money-bill-wave mr-2"></i>
                                    ₦<?php echo e(number_format($scholarship->amount, 0)); ?>

                                </span>
                                <span class="inline-flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    Deadline: <?php echo e($scholarship->application_deadline->format('M d, Y')); ?>

                                </span>
                            </div>
                        </div>
                        <?php if($scholarship->application_deadline > now()): ?>
                            <a href="<?php echo e(route('partner.scholarships.create', ['scholarship_id' => $scholarship->id])); ?>" 
                               class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-plus mr-2"></i>
                                Apply Now
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 lg:p-8">
                <div class="max-w-6xl mx-auto">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Main Content -->
                        <div class="lg:col-span-2 space-y-8">
                            <!-- Description -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-info-circle text-green-600 mr-2"></i>
                                    Description
                                </h3>
                                <div class="prose prose-sm max-w-none text-gray-600">
                                    <?php echo nl2br(e($scholarship->description)); ?>

                                </div>
                            </div>

                            <!-- Eligibility Criteria -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    Eligibility Criteria
                                </h3>
                                <div class="prose prose-sm max-w-none text-gray-600">
                                    <?php echo nl2br(e($scholarship->eligibility_criteria)); ?>

                                </div>
                            </div>

                            <!-- Requirements -->
                            <?php if(!empty($scholarship->requirements)): ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                        <i class="fas fa-list-ul text-green-600 mr-2"></i>
                                        Requirements
                                    </h3>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $scholarship->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-check text-green-500 mr-3 mt-1 text-sm"></i>
                                                <span class="text-gray-600"><?php echo e($requirement); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Documents Required -->
                            <?php if(!empty($documentsRequired)): ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                        <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                        Required Documents
                                    </h3>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $documentsRequired; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-paperclip text-blue-500 mr-3 mt-1 text-sm"></i>
                                                <span class="text-gray-600"><?php echo e($document); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Additional Information -->
                            <?php if(!empty($customFields)): ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                        <i class="fas fa-clipboard-list text-green-600 mr-2"></i>
                                        Additional Information Required
                                    </h3>
                                    <ul class="space-y-2">
                                        <?php $__currentLoopData = $customFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldKey => $fieldLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="flex items-start">
                                                <i class="fas fa-question-circle text-orange-500 mr-3 mt-1 text-sm"></i>
                                                <span class="text-gray-600"><?php echo e($fieldLabel); ?></span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- My Applications for this Scholarship -->
                            <?php if($applications->count() > 0): ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                        <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                        My Applications (<?php echo e($applications->count()); ?>)
                                    </h3>
                                    <div class="space-y-4">
                                        <?php $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="border border-gray-200 rounded-lg p-4">
                                                <div class="flex items-center justify-between mb-2">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-user-graduate text-blue-500 mr-2"></i>
                                                        <span class="font-medium"><?php echo e($application->student->name ?? 'Student'); ?></span>
                                                        <span class="ml-2 text-sm text-gray-500">
                                                            (Class <?php echo e($application->grade_level ?? 'N/A'); ?>)
                                                        </span>
                                                    </div>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                        <?php echo e($application->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                                                        <?php echo e($application->status == 'under_review' ? 'bg-blue-100 text-blue-800' : ''); ?>

                                                        <?php echo e($application->status == 'approved' ? 'bg-green-100 text-green-800' : ''); ?>

                                                        <?php echo e($application->status == 'rejected' ? 'bg-red-100 text-red-800' : ''); ?>">
                                                        <?php echo e(ucfirst(str_replace('_', ' ', $application->status))); ?>

                                                    </span>
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-calendar mr-2"></i>
                                                        Applied: <?php echo e($application->submitted_at->format('M d, Y g:i A')); ?>

                                                    </div>
                                                    <?php if($application->reviewed_at): ?>
                                                        <div class="flex items-center mt-1">
                                                            <i class="fas fa-eye mr-2"></i>
                                                            Reviewed: <?php echo e($application->reviewed_at->format('M d, Y g:i A')); ?>

                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Sidebar -->
                        <div class="space-y-6">
                            <!-- Quick Stats -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Award Amount</span>
                                        <span class="font-semibold text-green-600">₦<?php echo e(number_format($scholarship->amount, 0)); ?></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Category</span>
                                        <span class="font-semibold"><?php echo e(ucfirst($scholarship->category)); ?></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Applicants</span>
                                        <span class="font-semibold"><?php echo e($scholarship->current_applicants); ?>/<?php echo e($scholarship->max_applicants ?? '∞'); ?></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Status</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            <?php echo e($scholarship->status == 'open' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                            <?php echo e(ucfirst($scholarship->status)); ?>

                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Deadline</span>
                                        <span class="font-semibold <?php echo e($scholarship->application_deadline <= now() ? 'text-red-600' : 'text-gray-900'); ?>">
                                            <?php echo e($scholarship->application_deadline->format('M d, Y')); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <?php if($scholarship->contact_email): ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                                    <div class="space-y-3">
                                        <div class="flex items-center">
                                            <i class="fas fa-envelope text-blue-500 mr-3"></i>
                                            <a href="mailto:<?php echo e($scholarship->contact_email); ?>" class="text-blue-600 hover:text-blue-800">
                                                <?php echo e($scholarship->contact_email); ?>

                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Action Buttons -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <div class="space-y-3">
                                    <?php if($scholarship->application_deadline > now()): ?>
                                        <a href="<?php echo e(route('partner.scholarships.create', ['scholarship_id' => $scholarship->id])); ?>" 
                                           class="w-full inline-flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                            <i class="fas fa-plus mr-2"></i>
                                            Apply for Student
                                        </a>
                                    <?php else: ?>
                                        <div class="w-full inline-flex items-center justify-center px-4 py-3 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed">
                                            <i class="fas fa-clock mr-2"></i>
                                            Application Closed
                                        </div>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo e(route('partner.applications.index')); ?>" 
                                       class="w-full inline-flex items-center justify-center px-4 py-3 border border-green-600 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-300">
                                        <i class="fas fa-file-alt mr-2"></i>
                                        View All Applications
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    if (mobileMenuBtn) mobileMenuBtn.addEventListener('click', openMobileMenu);
    if (mobileSidebarOverlay) mobileSidebarOverlay.addEventListener('click', closeMobileMenu);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/scholarships/show.blade.php ENDPATH**/ ?>