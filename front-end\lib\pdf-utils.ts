import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { BACKEND_BASE_URL } from './constants';

/**
 * Convert image URL to base64 data URL
 */
export const imageUrlToBase64 = async (url: string): Promise<string> => {
  try {
    // Handle relative URLs by making them absolute
    let absoluteUrl = url;
    if (url.startsWith('/')) {
      absoluteUrl = BACKEND_BASE_URL + url;
    }
    
    console.log('Converting image to base64:', absoluteUrl);
    
    const response = await fetch(absoluteUrl, {
      mode: 'cors',
      credentials: 'omit'
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Failed to convert image to base64:', error);
    // Return a fallback base64 image (1x1 transparent pixel)
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  }
};

/**
 * Preload and convert all images in an element to base64
 */
export const preloadImagesInElement = async (element: HTMLElement): Promise<void> => {
  const images = element.querySelectorAll('img');
  
  for (const img of Array.from(images)) {
    try {
      // Skip if already base64
      if (img.src.startsWith('data:')) continue;
      
      // Convert to base64
      const base64 = await imageUrlToBase64(img.src);
      img.src = base64;
      
      // Ensure image is loaded
      await new Promise<void>((resolve) => {
        if (img.complete) {
          resolve();
        } else {
          img.onload = () => resolve();
          img.onerror = () => resolve(); // Continue even if image fails
        }
      });
    } catch (error) {
      console.warn('Failed to preload image:', img.src, error);
    }
  }
};

/**
 * Generate PDF from HTML element with proper image handling
 */
export const generatePDFFromElement = async (
  element: HTMLElement,
  filename: string,
  options: {
    format?: [number, number] | string
    orientation?: 'portrait' | 'landscape'
    margin?: number
    scale?: number
  } = {}
): Promise<void> => {
  const {
    format = 'a4',
    orientation = 'portrait',
    margin = 10,
    scale = 2
  } = options

  try {
    // Convert all images to base64 first
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(async (img) => {
      if (img.src && img.src.startsWith('http')) {
        try {
          const base64 = await imageUrlToBase64(img.src)
          img.src = base64
        } catch (error) {
          console.warn('Failed to convert image to base64:', img.src, error)
        }
      }
    })

    await Promise.all(imagePromises)

    // Generate canvas
    const canvas = await html2canvas(element, {
      scale,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      removeContainer: true,
    })

    // Create PDF
    const imgData = canvas.toDataURL('image/png')
    
    let pdf: jsPDF
    if (Array.isArray(format)) {
      // Custom format in mm
      pdf = new jsPDF({
        orientation,
        unit: 'mm',
        format
      })
    } else {
      pdf = new jsPDF({
        orientation,
        unit: 'mm',
        format
      })
    }

    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = pdf.internal.pageSize.getHeight()
    
    const canvasWidth = canvas.width
    const canvasHeight = canvas.height
    
    // Calculate dimensions to fit PDF
    const ratio = Math.min(
      (pdfWidth - 2 * margin) / (canvasWidth / scale),
      (pdfHeight - 2 * margin) / (canvasHeight / scale)
    )
    
    const imgWidth = (canvasWidth / scale) * ratio
    const imgHeight = (canvasHeight / scale) * ratio
    
    const x = (pdfWidth - imgWidth) / 2
    const y = (pdfHeight - imgHeight) / 2

    pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight)
    pdf.save(filename)

  } catch (error) {
    console.error('Failed to generate PDF:', error)
    throw error
  }
}

/**
 * Download ID card as PDF with dynamic app name
 */
export const downloadIdCardAsPDF = async (
  user: any, 
  appName: string = 'Laravel NGO Foundation',
  settings?: any
): Promise<void> => {
  try {
    // Use settings to get dynamic values
    const organizationName = settings?.app_name || appName
    const organizationAddress = settings?.organization_address || 'foundation.org'
    
    // Create a temporary ID card element
    const idCardElement = document.createElement('div');
    idCardElement.innerHTML = `
      <div style="
        width: 350px;
        height: 220px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 12px;
        padding: 20px;
        color: white;
        font-family: Arial, sans-serif;
        position: relative;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
      ">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
          <div style="
            width: 48px;
            height: 48px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
          ">
            <img 
              src="${user.profile_picture_url || '/img/default-avatar.png'}" 
              alt="Profile"
              style="width: 44px; height: 44px; border-radius: 50%; object-fit: cover;"
              crossorigin="anonymous"
            />
          </div>
          <div style="text-align: right;">
            <p style="margin: 0; font-size: 10px; opacity: 0.8;">Member ID</p>
            <p style="margin: 0; font-family: monospace; font-size: 14px; font-weight: bold;">${user.short_id}</p>
          </div>
        </div>
        
        <div style="text-align: left;">
          <h3 style="margin: 0 0 4px 0; font-size: 18px; font-weight: bold;">${user.first_name} ${user.last_name}</h3>
          <p style="margin: 0 0 8px 0; font-size: 12px; opacity: 0.9;">${user.email}</p>
          <p style="margin: 0; font-size: 10px; opacity: 0.8;">Member since ${new Date().getFullYear()}</p>
        </div>
        
        <div style="position: absolute; bottom: 20px; right: 20px;">
          <div style="background: white; padding: 8px; border-radius: 6px;">
            ${user.qr_code_url ? `
              <img 
                src="${user.qr_code_url}" 
                alt="QR Code"
                style="width: 60px; height: 60px; display: block;"
                crossorigin="anonymous"
              />
            ` : `
              <div style="width: 60px; height: 60px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; font-size: 8px; color: #666;">
                No QR
              </div>
            `}
          </div>
        </div>
        
        <div style="position: absolute; bottom: 8px; left: 20px;">
          <p style="margin: 0; font-size: 8px; opacity: 0.7;">${organizationName}</p>
        </div>
        
        <div style="position: absolute; bottom: 2px; right: 8px;">
          <p style="margin: 0; font-size: 6px; opacity: 0.5;">${organizationAddress}</p>
        </div>
      </div>
    `;

    // Add to DOM temporarily
    idCardElement.style.position = 'absolute';
    idCardElement.style.left = '-9999px';
    idCardElement.style.top = '-9999px';
    document.body.appendChild(idCardElement);

    try {
      await generatePDFFromElement(
        idCardElement.firstElementChild as HTMLElement,
        `${user.first_name}_${user.last_name}_${organizationName.replace(/\s+/g, '_')}_ID_Card.pdf`,
        {
          format: [85.6, 53.98], // Credit card size in mm
          orientation: 'landscape',
          margin: 5
        }
      );
    } finally {
      document.body.removeChild(idCardElement);
    }

  } catch (error) {
    console.error('Failed to download ID card:', error);
    throw error;
  }
}; 