<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'API Documentation') - NGO Platform API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .api-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        .api-card:hover {
            transform: translateY(-2px);
        }
        .method-badge {
            font-size: 0.75rem;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: black; }
        .method-delete { background: #dc3545; color: white; }
        .role-badge {
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin: 1px;
        }
        .role-admin { background: #dc3545; color: white; }
        .role-partner { background: #17a2b8; color: white; }
        .role-volunteer { background: #28a745; color: white; }
        .role-student { background: #6f42c1; color: white; }
        .role-user { background: #6c757d; color: white; }
        .role-public { background: #343a40; color: white; }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
        }
        .endpoint-section {
            border-left: 4px solid #667eea;
            padding-left: 1rem;
            margin: 1rem 0;
        }
        .test-form {
            background: #fff;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="d-flex align-items-center mb-4">
                    <i class="fas fa-code me-2"></i>
                    <h5 class="mb-0">API Docs</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link {{ request()->routeIs('api-docs.index') ? 'active' : '' }}" href="{{ route('api-docs.index') }}">
                        <i class="fas fa-home me-2"></i> Overview
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.authentication') ? 'active' : '' }}" href="{{ route('api-docs.authentication') }}">
                        <i class="fas fa-key me-2"></i> Authentication
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.authorization') ? 'active' : '' }}" href="{{ route('api-docs.authorization') }}">
                        <i class="fas fa-shield-alt me-2"></i> Authorization
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.endpoints') ? 'active' : '' }}" href="{{ route('api-docs.endpoints') }}">
                        <i class="fas fa-list me-2"></i> Endpoints
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.error-codes') ? 'active' : '' }}" href="{{ route('api-docs.error-codes') }}">
                        <i class="fas fa-exclamation-triangle me-2"></i> Error Codes
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.mobile-integration') ? 'active' : '' }}" href="{{ route('api-docs.mobile-integration') }}">
                        <i class="fas fa-mobile-alt me-2"></i> Mobile Integration
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.code-examples') ? 'active' : '' }}" href="{{ route('api-docs.code-examples') }}">
                        <i class="fas fa-code me-2"></i> Code Examples
                    </a>
                    <a class="nav-link {{ request()->routeIs('api-docs.testing') ? 'active' : '' }}" href="{{ route('api-docs.testing') }}">
                        <i class="fas fa-flask me-2"></i> API Testing
                    </a>
                </nav>

                <hr class="my-4" style="border-color: rgba(255, 255, 255, 0.2);">
                
                <div class="mt-auto">
                    <small class="text-light opacity-75">
                        <i class="fas fa-info-circle me-1"></i>
                        API Version: v1<br>
                        Last Updated: {{ date('Y-m-d') }}
                    </small>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="container-fluid">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // Copy code functionality
        function copyCode(button) {
            const codeBlock = button.nextElementSibling.querySelector('code');
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.classList.remove('btn-outline-secondary');
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
            });
        }

        // Method badge helper
        function getMethodBadge(method) {
            const classes = {
                'GET': 'method-get',
                'POST': 'method-post',
                'PUT': 'method-put',
                'DELETE': 'method-delete'
            };
            return `<span class="method-badge ${classes[method] || 'method-get'}">${method}</span>`;
        }

        // Role badge helper
        function getRoleBadge(role) {
            const classes = {
                'admin': 'role-admin',
                'partner_organization': 'role-partner',
                'volunteer': 'role-volunteer',
                'student': 'role-student',
                'user': 'role-user',
                'public': 'role-public'
            };
            return `<span class="role-badge ${classes[role] || 'role-user'}">${role}</span>`;
        }
    </script>
    
    @stack('scripts')
</body>
</html>
