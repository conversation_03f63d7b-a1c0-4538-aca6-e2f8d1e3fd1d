<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('scholarships', function (Blueprint $table) {
            $table->id();
            $table->string('title', 200);
            $table->string('slug', 220)->unique();
            $table->text('description');
            $table->text('eligibility_criteria');
            $table->decimal('amount', 10, 2);
            $table->date('application_deadline');
            $table->json('requirements')->nullable();
            $table->enum('status', ['draft', 'open', 'closed'])->default('draft');
            $table->integer('max_applicants')->nullable();
            $table->integer('current_applicants')->default(0);
            $table->string('contact_email', 100)->nullable();
            $table->json('documents_required')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'application_deadline']);
            $table->index('slug');
            $table->index('application_deadline');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('scholarships');
    }
}; 