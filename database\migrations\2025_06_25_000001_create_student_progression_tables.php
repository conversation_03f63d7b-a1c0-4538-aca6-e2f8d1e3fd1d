<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create students table for tracking individual students (only if it doesn't exist)
        if (!Schema::hasTable('students')) {
            Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('student_id', 20)->unique(); // HLTKKQ-STU-0001
            $table->string('first_name', 100);
            $table->string('last_name', 100);
            $table->string('middle_name', 100)->nullable();
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->string('phone_number', 20)->nullable();
            $table->string('email', 100)->nullable();
            $table->text('address')->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 100)->nullable();
            $table->string('country', 100)->default('Nigeria');
            
            // Academic Information
            $table->enum('current_grade', [
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                'university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6',
                'graduated'
            ]);
            $table->string('academic_year', 10); // 2024/2025
            $table->foreignId('school_id')->nullable()->constrained('partner_organizations')->onDelete('set null');
            $table->string('matriculation_number', 50)->nullable(); // For university students
            $table->enum('student_type', ['primary', 'secondary', 'university'])->default('primary');
            
            // Guardian Information (for primary/secondary students)
            $table->string('guardian_name', 200)->nullable();
            $table->string('guardian_phone', 20)->nullable();
            $table->string('guardian_email', 100)->nullable();
            $table->string('guardian_relationship', 50)->nullable(); // father, mother, guardian
            
            // Status and Tracking
            $table->enum('status', ['active', 'graduated', 'transferred', 'dropped_out', 'inactive'])->default('active');
            $table->date('enrollment_date');
            $table->date('graduation_date')->nullable();
            $table->json('additional_info')->nullable(); // For storing extra student information
            
            $table->timestamps();
            
            // Indexes
            $table->index(['student_id']);
            $table->index(['school_id', 'current_grade']);
            $table->index(['student_type', 'status']);
            $table->index(['academic_year', 'current_grade']);
            });
        }

        // Create student_progressions table for tracking grade advancement (only if it doesn't exist)
        if (!Schema::hasTable('student_progressions')) {
            Schema::create('student_progressions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->enum('from_grade', [
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                'university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6'
            ]);
            $table->enum('to_grade', [
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                'university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6',
                'graduated'
            ]);
            $table->string('from_academic_year', 10); // 2023/2024
            $table->string('to_academic_year', 10); // 2024/2025
            $table->enum('status', ['pending', 'approved', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->date('progression_date');
            $table->decimal('final_grade', 5, 2)->nullable(); // Final grade/percentage for the completed year
            $table->text('notes')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['student_id', 'from_academic_year']);
            $table->index(['status', 'progression_date']);
            $table->unique(['student_id', 'from_grade', 'from_academic_year'], 'student_prog_unique'); // Prevent duplicate progressions
            });
        }

        // Create school_students table for managing school-student relationships (only if it doesn't exist)
        if (!Schema::hasTable('school_students')) {
            Schema::create('school_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained('partner_organizations')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->date('enrollment_date');
            $table->date('graduation_date')->nullable();
            $table->enum('status', ['active', 'graduated', 'transferred', 'dropped_out'])->default('active');
            $table->enum('enrollment_grade', [
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6'
            ]);
            $table->enum('current_grade', [
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                'graduated'
            ]);
            $table->string('academic_year', 10);
            $table->text('notes')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['school_id', 'status']);
            $table->index(['student_id', 'status']);
            $table->index(['academic_year', 'current_grade']);
            $table->unique(['school_id', 'student_id', 'academic_year'], 'school_student_unique'); // Prevent duplicate enrollments per year
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_students');
        Schema::dropIfExists('student_progressions');
        Schema::dropIfExists('students');
    }
};
