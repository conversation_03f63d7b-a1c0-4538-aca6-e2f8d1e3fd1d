<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Scholarship;
use App\Models\ScholarshipField;
use Carbon\Carbon;

class ScholarshipManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Primary School Scholarship
        $primaryScholarship = Scholarship::create([
            'title' => 'Primary School Education Support Scholarship',
            'slug' => 'primary-school-education-support-scholarship',
            'category' => 'primary',
            'description' => 'Financial assistance for primary school students to cover tuition fees, books, and educational materials.',
            'eligibility_criteria' => 'Student must be enrolled in a recognized primary school (Primary 1-6) in Nigeria. Family income must be below N500,000 annually. Student must maintain good academic standing.',
            'amount' => 50000.00,
            'application_deadline' => Carbon::now()->addMonths(3)->format('Y-m-d'),
            'status' => 'open',
            'is_open' => true,
            'max_applicants' => 100,
            'current_applicants' => 0,
            'contact_email' => '<EMAIL>',
            'requirements' => [
                'Valid birth certificate',
                'School admission letter',
                'Parent/Guardian identification',
                'Proof of family income',
                'Academic transcript (if available)'
            ],
            'documents_required' => [
                'Student Photo',
                'Birth Certificate',
                'School Admission Letter',
                'Parent ID Card'
            ],
            'custom_fields' => [
                'guardian_relationship' => 'Parent/Guardian relationship to student',
                'school_distance' => 'Distance from home to school',
                'transportation_method' => 'How student gets to school'
            ]
        ]);

        // Add fields for Primary School Scholarship
        $primaryFields = [
            ['field_name' => 'student_full_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'current_class', 'field_label' => 'Current Class', 'field_type' => 'select', 'is_required' => true, 'field_order' => 3, 'field_options' => ['options' => ['Primary 1', 'Primary 2', 'Primary 3', 'Primary 4', 'Primary 5', 'Primary 6']]],
            ['field_name' => 'father_name', 'field_label' => 'Father\'s Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 4],
            ['field_name' => 'mother_name', 'field_label' => 'Mother\'s Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'parent_phone', 'field_label' => 'Parent/Guardian Phone Number', 'field_type' => 'tel', 'is_required' => true, 'field_order' => 6],
            ['field_name' => 'home_address', 'field_label' => 'Home Address', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'headmaster_name', 'field_label' => 'Headmaster/Principal Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 9],
            ['field_name' => 'school_account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 10],
            ['field_name' => 'student_picture', 'field_label' => 'Student Picture', 'field_type' => 'file', 'is_required' => true, 'field_order' => 11],
            ['field_name' => 'scholarship_reason', 'field_label' => 'Why do you need this scholarship?', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 12],
            ['field_name' => 'school_fee_amount', 'field_label' => 'School Fee Amount (Per Term)', 'field_type' => 'number', 'is_required' => true, 'field_order' => 13],
            ['field_name' => 'supporting_information', 'field_label' => 'Additional Supporting Information', 'field_type' => 'textarea', 'is_required' => false, 'field_order' => 14]
        ];

        foreach ($primaryFields as $field) {
            ScholarshipField::create(array_merge(['scholarship_id' => $primaryScholarship->id], $field));
        }

        // Create Secondary School Scholarship
        $secondaryScholarship = Scholarship::create([
            'title' => 'Secondary School Excellence Scholarship',
            'slug' => 'secondary-school-excellence-scholarship',
            'category' => 'secondary',
            'description' => 'Academic excellence scholarship for secondary school students to pursue their education goals.',
            'eligibility_criteria' => 'Student must be enrolled in JSS1-SS3 in a recognized secondary school. Minimum of 70% academic average required. Must demonstrate financial need.',
            'amount' => 80000.00,
            'application_deadline' => Carbon::now()->addMonths(2)->format('Y-m-d'),
            'status' => 'open',
            'is_open' => true,
            'max_applicants' => 75,
            'current_applicants' => 0,
            'contact_email' => '<EMAIL>',
            'requirements' => [
                'Academic transcript',
                'Letter of recommendation',
                'Proof of enrollment',
                'Family income statement'
            ],
            'documents_required' => [
                'Student Photo',
                'Academic Transcript',
                'Recommendation Letter',
                'Enrollment Certificate'
            ]
        ]);

        // Add fields for Secondary School Scholarship
        $secondaryFields = [
            ['field_name' => 'student_full_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'current_class', 'field_label' => 'Current Class', 'field_type' => 'select', 'is_required' => true, 'field_order' => 3, 'field_options' => ['options' => ['JSS1', 'JSS2', 'JSS3', 'SS1', 'SS2', 'SS3']]],
            ['field_name' => 'father_name', 'field_label' => 'Father\'s Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 4],
            ['field_name' => 'mother_name', 'field_label' => 'Mother\'s Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'student_phone', 'field_label' => 'Student Phone Number', 'field_type' => 'tel', 'is_required' => false, 'field_order' => 6],
            ['field_name' => 'home_address', 'field_label' => 'Home Address', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'principal_name', 'field_label' => 'Principal Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 9],
            ['field_name' => 'school_account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 10],
            ['field_name' => 'student_picture', 'field_label' => 'Student Picture', 'field_type' => 'file', 'is_required' => true, 'field_order' => 11],
            ['field_name' => 'scholarship_reason', 'field_label' => 'Why do you need this scholarship?', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 12],
            ['field_name' => 'school_fee_amount', 'field_label' => 'School Fee Amount (Per Term)', 'field_type' => 'number', 'is_required' => true, 'field_order' => 13],
            ['field_name' => 'academic_goals', 'field_label' => 'Your Academic Goals', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 14]
        ];

        foreach ($secondaryFields as $field) {
            ScholarshipField::create(array_merge(['scholarship_id' => $secondaryScholarship->id], $field));
        }

        // Create University Scholarship
        $universityScholarship = Scholarship::create([
            'title' => 'University Academic Achievement Scholarship',
            'slug' => 'university-academic-achievement-scholarship',
            'category' => 'university',
            'description' => 'Merit-based scholarship for outstanding university students pursuing undergraduate and graduate degrees.',
            'eligibility_criteria' => 'Enrolled in an accredited Nigerian university. Minimum CGPA of 3.0 (or equivalent). Demonstrate academic excellence and leadership potential.',
            'amount' => 150000.00,
            'application_deadline' => Carbon::now()->addMonths(4)->format('Y-m-d'),
            'status' => 'open',
            'is_open' => true,
            'max_applicants' => 50,
            'current_applicants' => 0,
            'contact_email' => '<EMAIL>',
            'requirements' => [
                'University transcript',
                'Letter of recommendation from lecturer',
                'Research proposal (for graduate students)',
                'Leadership experience documentation'
            ],
            'documents_required' => [
                'Student ID Card',
                'Academic Transcript',
                'Payment Evidence',
                'Supporting Documents'
            ]
        ]);

        // Add fields for University Scholarship
        $universityFields = [
            ['field_name' => 'full_name', 'field_label' => 'Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'age', 'field_label' => 'Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'course_of_study', 'field_label' => 'Course of Study', 'field_type' => 'text', 'is_required' => true, 'field_order' => 3],
            ['field_name' => 'current_level', 'field_label' => 'Current Level', 'field_type' => 'select', 'is_required' => true, 'field_order' => 4, 'field_options' => ['options' => ['100L', '200L', '300L', '400L', '500L', '600L', 'Masters', 'PhD']]],
            ['field_name' => 'phone', 'field_label' => 'Phone Number', 'field_type' => 'tel', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'email', 'field_label' => 'Email Address', 'field_type' => 'email', 'is_required' => true, 'field_order' => 6],
            ['field_name' => 'matriculation_number', 'field_label' => 'Matriculation Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'student_id_card', 'field_label' => 'Student ID Card Upload', 'field_type' => 'file', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'payment_evidence', 'field_label' => 'Payment Evidence Upload', 'field_type' => 'file', 'is_required' => true, 'field_order' => 9],
            ['field_name' => 'supporting_documents', 'field_label' => 'Supporting Documents Upload', 'field_type' => 'file', 'is_required' => false, 'field_order' => 10],
            ['field_name' => 'scholarship_reason', 'field_label' => 'Why do you need this scholarship?', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 11],
            ['field_name' => 'academic_achievements', 'field_label' => 'Academic Achievements', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 12],
            ['field_name' => 'future_goals', 'field_label' => 'Future Career Goals', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 13]
        ];

        foreach ($universityFields as $field) {
            ScholarshipField::create(array_merge(['scholarship_id' => $universityScholarship->id], $field));
        }

        // Create a closed scholarship for testing
        $closedScholarship = Scholarship::create([
            'title' => 'Special Needs Education Support (Closed)',
            'slug' => 'special-needs-education-support-closed',
            'category' => 'primary',
            'description' => 'This scholarship program has been closed for this academic year.',
            'eligibility_criteria' => 'Program closed.',
            'amount' => 60000.00,
            'application_deadline' => Carbon::now()->subDays(30)->format('Y-m-d'),
            'status' => 'closed',
            'is_open' => false,
            'max_applicants' => 30,
            'current_applicants' => 25,
            'contact_email' => '<EMAIL>'
        ]);

        $this->command->info('✅ Scholarship Management System seeded successfully!');
        $this->command->info('📚 Created 4 scholarships:');
        $this->command->info('   - Primary School Education Support (Open)');
        $this->command->info('   - Secondary School Excellence (Open)');
        $this->command->info('   - University Academic Achievement (Open)');
        $this->command->info('   - Special Needs Education Support (Closed)');
        $this->command->info('🔧 Added dynamic form fields for each scholarship category');
    }
}
