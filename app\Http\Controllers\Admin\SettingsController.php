<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        // Get all settings as key-value pairs
        $settings = Setting::getAll();
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update application settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'settings.app_name' => 'required|string|max:255',
            'settings.contact_email' => 'required|email|max:255',
            'app_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'settings.*' => 'nullable|string',
        ]);

        try {
            // Handle logo upload
            if ($request->hasFile('app_logo')) {
                $logoFile = $request->file('app_logo');
                
                // Delete old logo if exists
                $oldLogo = Setting::getValue('app_logo');
                if ($oldLogo) {
                    // Extract relative path from full URL
                    $oldLogoPath = str_replace('/storage/', '', parse_url($oldLogo, PHP_URL_PATH));
                    if ($oldLogoPath && Storage::disk('public')->exists($oldLogoPath)) {
                        Storage::disk('public')->delete($oldLogoPath);
                    }
                }
                
                // Store new logo and save just the relative path
                $logoPath = $logoFile->store('logos', 'public');
                
                // Update logo setting with just the relative path and set type as 'image'
                Setting::setValue('app_logo', $logoPath, 'image');
                
                Log::info('Logo uploaded successfully: ' . $logoPath);
            }

            // Update other settings
            if ($request->has('settings')) {
                foreach ($request->input('settings') as $key => $value) {
                    Setting::setValue($key, $value);
                    Log::info("Setting updated: {$key} = {$value}");
                }
            }

            // Clear cache
            Cache::forget('settings');
            Cache::forget('settings.all');
            Cache::forget('settings.public');

            return redirect()->route('admin.settings.index')
                           ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            Log::error('Settings update failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->except(['app_logo']) // Don't log file data
            ]);
            
            return redirect()->back()
                           ->with('error', 'Failed to update settings. Error: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the application logo.
     */
    public function removeLogo()
    {
        try {
            $currentLogo = Setting::getValue('app_logo');
            
            if ($currentLogo) {
                // Extract relative path from URL
                $logoPath = str_replace('/storage/', '', parse_url($currentLogo, PHP_URL_PATH));
                
                // Delete file if exists
                if ($logoPath && Storage::disk('public')->exists($logoPath)) {
                    Storage::disk('public')->delete($logoPath);
                }
                
                // Update setting
                Setting::setValue('app_logo', null);
                
                // Clear cache
                Cache::forget('settings');
                Cache::forget('settings.all');
                Cache::forget('settings.public');
                
                return response()->json([
                    'success' => true,
                    'message' => 'Logo removed successfully'
                ]);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'No logo found to remove'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Logo removal failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove logo'
            ]);
        }
    }

    /**
     * Reset settings to default values.
     */
    public function reset()
    {
        try {
            // Get all current logos to delete
            $appLogo = Setting::getValue('app_logo');
            
            // Delete logo files
            if ($appLogo) {
                $logoPath = str_replace('/storage/', '', parse_url($appLogo, PHP_URL_PATH));
                if ($logoPath && Storage::disk('public')->exists($logoPath)) {
                    Storage::disk('public')->delete($logoPath);
                }
            }
            
            // Truncate settings table and re-seed
            Setting::truncate();
            \Artisan::call('db:seed', ['--class' => 'SettingsSeeder']);
            
            // Clear cache
            Cache::forget('settings');
            Cache::forget('settings.all');
            Cache::forget('settings.public');
            
            return redirect()->route('admin.settings.index')
                           ->with('success', 'Settings reset to default values successfully!');
                           
        } catch (\Exception $e) {
            Log::error('Settings reset failed: ' . $e->getMessage());
            
            return redirect()->back()
                           ->with('error', 'Failed to reset settings. Please try again.');
        }
    }
}
