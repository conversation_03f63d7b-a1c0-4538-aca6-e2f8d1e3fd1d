<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('newsletter_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('subject');
            $table->longText('content');
            $table->string('preview_text')->nullable();
            $table->enum('recipient_type', ['all', 'active', 'volunteers', 'donors'])->default('all');
            $table->integer('estimated_recipients')->default(0);
            $table->integer('actual_recipients')->nullable();
            $table->enum('status', ['draft', 'scheduled', 'sent'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->string('from_name')->default('HLTKKQ Foundation');
            $table->string('from_email')->default('<EMAIL>');
            $table->string('template')->default('default');
            $table->integer('open_count')->default(0);
            $table->integer('click_count')->default(0);
            $table->integer('unsubscribe_count')->default(0);
            $table->integer('bounce_count')->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_campaigns');
    }
};
