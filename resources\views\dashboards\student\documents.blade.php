@extends('layouts.dashboard')

@section('title', 'Documents - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/documents')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/documents')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Documents</h1>
                            <p class="text-green-100 text-sm lg:text-base">Manage your academic documents and certificates</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/applications" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-file-alt mr-2"></i>
                                My Applications
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Content -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <!-- Document Statistics -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Total</p>
                                <p class="text-xl font-bold text-blue-600">{{ $documents->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Academic</p>
                                <p class="text-xl font-bold text-green-600">{{ $documents->where('type', 'academic_transcript')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-id-card text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">ID Docs</p>
                                <p class="text-xl font-bold text-purple-600">{{ $documents->whereIn('type', ['passport_photo', 'id_card'])->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-certificate text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Certificates</p>
                                <p class="text-xl font-bold text-orange-600">{{ $documents->where('type', 'certificate')->count() }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Documents List -->
                    @if($documents->count() > 0)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900">Your Documents</h2>
                                <p class="text-gray-600 text-sm mt-1">Documents uploaded through scholarship applications</p>
                            </div>

                            <div class="divide-y divide-gray-100">
                                @foreach($documents as $document)
                                    <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                            <div class="flex items-start mb-4 lg:mb-0">
                                                <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0
                                                    @if(str_contains($document['type'], 'transcript')) bg-gradient-to-br from-green-500 to-green-600
                                                    @elseif(str_contains($document['type'], 'photo') || str_contains($document['type'], 'id')) bg-gradient-to-br from-purple-500 to-purple-600
                                                    @elseif(str_contains($document['type'], 'certificate')) bg-gradient-to-br from-orange-500 to-orange-600
                                                    @else bg-gradient-to-br from-blue-500 to-blue-600
                                                    @endif">
                                                    <i class="fas 
                                                        @if(str_contains($document['type'], 'transcript')) fa-graduation-cap
                                                        @elseif(str_contains($document['type'], 'photo')) fa-camera
                                                        @elseif(str_contains($document['type'], 'id')) fa-id-card
                                                        @elseif(str_contains($document['type'], 'certificate')) fa-certificate
                                                        @else fa-file-alt
                                                        @endif text-white text-lg"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                                        {{ $document['name'] }}
                                                    </h3>
                                                    <p class="text-gray-600 text-sm mb-2">
                                                        Type: <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $document['type'])) }}</span>
                                                    </p>
                                                    <div class="flex flex-wrap gap-4 text-sm text-gray-500">
                                                        <span class="flex items-center">
                                                            <i class="fas fa-calendar-alt mr-1"></i>
                                                            Uploaded: {{ $document['uploaded_at']->format('M d, Y') }}
                                                        </span>
                                                        <span class="flex items-center">
                                                            <i class="fas fa-file-alt mr-1"></i>
                                                            Size: {{ number_format($document['size'] / 1024, 1) }} KB
                                                        </span>
                                                        <span class="flex items-center">
                                                            <i class="fas fa-graduation-cap mr-1"></i>
                                                            Application: {{ $document['application'] }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex flex-col sm:flex-row gap-2">
                                                <a href="{{ route('student.download.document', $document['id']) }}" 
                                                   class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-download mr-2"></i>
                                                    Download
                                                </a>
                                                <button onclick="previewDocument('{{ $document['name'] }}', '{{ $document['type'] }}')" 
                                                        class="inline-flex items-center justify-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-eye mr-2"></i>
                                                    Preview
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <!-- No Documents -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-folder-open text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Documents Yet</h3>
                            <p class="text-gray-600 mb-6">You haven't uploaded any documents yet. Documents are uploaded when you submit scholarship applications.</p>
                            <a href="/student/scholarships" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>
                    @endif

                    <!-- Document Guidelines -->
                    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-blue-900 mb-2">Document Guidelines</h3>
                                <div class="text-blue-800 text-sm space-y-2">
                                    <p><strong>Accepted Formats:</strong> PDF, JPG, PNG (Max 5MB per file)</p>
                                    <p><strong>Required Documents:</strong> Academic transcripts, passport photograph, ID card/certificate, and any additional certificates</p>
                                    <p><strong>Quality:</strong> Ensure documents are clear, readable, and properly scanned</p>
                                    <p><strong>Note:</strong> Documents are uploaded during scholarship application process</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Preview Modal -->
<div id="documentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Document Preview</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="documentContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function previewDocument(fileName, fileType) {
    // Show loading state
    document.getElementById('documentContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('documentModal').classList.remove('hidden');
    
    // In a real implementation, you would load the document preview
    setTimeout(() => {
        document.getElementById('documentContent').innerHTML = `
            <div class="space-y-4">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">${fileName}</h4>
                    <p class="text-gray-600 text-sm">Document Type: ${fileType.replace('_', ' ')}</p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 text-center">
                    <p class="text-gray-600 text-sm">Document preview would be displayed here.</p>
                    <p class="text-gray-500 text-xs mt-2">For security reasons, document preview is not available in this demo.</p>
                </div>
                <div class="flex justify-center">
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                        Close
                    </button>
                </div>
            </div>
        `;
    }, 500);
}

function closeModal() {
    document.getElementById('documentModal').classList.add('hidden');
}
</script>
@endsection
