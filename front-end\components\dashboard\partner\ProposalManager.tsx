'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Edit, 
  Eye, 
  Trash2, 
  FileText, 
  Calendar, 
  DollarSign, 
  Users, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface Proposal {
  id: number;
  title: string;
  description: string;
  budget: number;
  duration: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  submitted_at?: string;
  reviewed_at?: string;
  partner_organization: string;
  program_type: string;
  target_beneficiaries: number;
  expected_outcomes: string;
  timeline: string;
  admin_notes?: string;
}

interface ProposalFormData {
  title: string;
  description: string;
  budget: string;
  duration: string;
  program_type: string;
  target_beneficiaries: string;
  expected_outcomes: string;
  timeline: string;
}

export default function ProposalManager() {
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [formData, setFormData] = useState<ProposalFormData>({
    title: '',
    description: '',
    budget: '',
    duration: '',
    program_type: '',
    target_beneficiaries: '',
    expected_outcomes: '',
    timeline: ''
  });

  useEffect(() => {
    fetchProposals();
  }, []);

  const fetchProposals = async () => {
    try {
      // This would be the actual API call
      // const response = await apiClient.get('/partner/proposals');
      // For now, using mock data
      const mockProposals: Proposal[] = [
        {
          id: 1,
          title: 'Youth Education Initiative',
          description: 'A comprehensive program to provide educational support to underprivileged youth',
          budget: 500000,
          duration: '12 months',
          status: 'approved',
          submitted_at: '2024-01-15',
          reviewed_at: '2024-01-20',
          partner_organization: 'Education Partners NGO',
          program_type: 'Education',
          target_beneficiaries: 200,
          expected_outcomes: 'Improved literacy rates and school enrollment',
          timeline: 'Q1 2024 - Q4 2024'
        },
        {
          id: 2,
          title: 'Community Health Outreach',
          description: 'Mobile health clinics for rural communities',
          budget: 750000,
          duration: '18 months',
          status: 'under_review',
          submitted_at: '2024-02-01',
          partner_organization: 'Health Access Foundation',
          program_type: 'Healthcare',
          target_beneficiaries: 500,
          expected_outcomes: 'Increased healthcare access and preventive care',
          timeline: 'Q2 2024 - Q3 2025'
        },
        {
          id: 3,
          title: 'Women Empowerment Program',
          description: 'Skills training and microfinance for women entrepreneurs',
          budget: 300000,
          duration: '6 months',
          status: 'draft',
          partner_organization: 'Women Development Center',
          program_type: 'Economic Empowerment',
          target_beneficiaries: 100,
          expected_outcomes: 'Increased women participation in business',
          timeline: 'Q3 2024 - Q4 2024'
        }
      ];
      setProposals(mockProposals);
    } catch (error) {
      console.error('Error fetching proposals:', error);
      toast.error('Failed to load proposals');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProposal = async () => {
    try {
      // This would be the actual API call
      // const response = await apiClient.post('/partner/proposals', formData);
      
      // Mock creation
      const newProposal: Proposal = {
        id: Date.now(),
        ...formData,
        budget: parseFloat(formData.budget),
        target_beneficiaries: parseInt(formData.target_beneficiaries),
        status: 'draft',
        partner_organization: 'Current Organization' // This would come from user context
      };
      
      setProposals(prev => [...prev, newProposal]);
      setIsCreateDialogOpen(false);
      resetForm();
      toast.success('Proposal created successfully');
    } catch (error) {
      console.error('Error creating proposal:', error);
      toast.error('Failed to create proposal');
    }
  };

  const handleUpdateProposal = async () => {
    if (!selectedProposal) return;
    
    try {
      // This would be the actual API call
      // const response = await apiClient.put(`/partner/proposals/${selectedProposal.id}`, formData);
      
      // Mock update
      const updatedProposal = {
        ...selectedProposal,
        ...formData,
        budget: parseFloat(formData.budget),
        target_beneficiaries: parseInt(formData.target_beneficiaries)
      };
      
      setProposals(prev => prev.map(p => p.id === selectedProposal.id ? updatedProposal : p));
      setIsEditDialogOpen(false);
      setSelectedProposal(null);
      resetForm();
      toast.success('Proposal updated successfully');
    } catch (error) {
      console.error('Error updating proposal:', error);
      toast.error('Failed to update proposal');
    }
  };

  const handleSubmitProposal = async (proposalId: number) => {
    try {
      // This would be the actual API call
      // const response = await apiClient.post(`/partner/proposals/${proposalId}/submit`);
      
      // Mock submission
      setProposals(prev => prev.map(p => 
        p.id === proposalId 
          ? { ...p, status: 'submitted', submitted_at: new Date().toISOString().split('T')[0] }
          : p
      ));
      toast.success('Proposal submitted for review');
    } catch (error) {
      console.error('Error submitting proposal:', error);
      toast.error('Failed to submit proposal');
    }
  };

  const handleDeleteProposal = async (proposalId: number) => {
    if (!confirm('Are you sure you want to delete this proposal?')) return;
    
    try {
      // This would be the actual API call
      // const response = await apiClient.delete(`/partner/proposals/${proposalId}`);
      
      // Mock deletion
      setProposals(prev => prev.filter(p => p.id !== proposalId));
      toast.success('Proposal deleted successfully');
    } catch (error) {
      console.error('Error deleting proposal:', error);
      toast.error('Failed to delete proposal');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      budget: '',
      duration: '',
      program_type: '',
      target_beneficiaries: '',
      expected_outcomes: '',
      timeline: ''
    });
  };

  const openEditDialog = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setFormData({
      title: proposal.title,
      description: proposal.description,
      budget: proposal.budget.toString(),
      duration: proposal.duration,
      program_type: proposal.program_type,
      target_beneficiaries: proposal.target_beneficiaries.toString(),
      expected_outcomes: proposal.expected_outcomes,
      timeline: proposal.timeline
    });
    setIsEditDialogOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'submitted':
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'under_review':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'submitted':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Proposal Manager</h2>
          <p className="text-gray-600">Create and manage partnership proposals</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Proposal
        </Button>
      </div>

      {/* Proposals Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Proposals</p>
                <p className="text-2xl font-bold">{proposals.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">
                  {proposals.filter(p => p.status === 'approved').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Under Review</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {proposals.filter(p => p.status === 'under_review' || p.status === 'submitted').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Budget</p>
                <p className="text-2xl font-bold">
                  ₦{proposals.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Proposals Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Proposals</CardTitle>
          <CardDescription>Manage your partnership proposals</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Program Type</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {proposals.map((proposal) => (
                <TableRow key={proposal.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{proposal.title}</p>
                      <p className="text-sm text-gray-500 truncate max-w-xs">
                        {proposal.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>{proposal.program_type}</TableCell>
                  <TableCell>₦{proposal.budget.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(proposal.status)}
                      <Badge className={getStatusColor(proposal.status)}>
                        {proposal.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    {proposal.submitted_at
                      ? new Date(proposal.submitted_at).toLocaleDateString()
                      : '-'
                    }
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedProposal(proposal)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {proposal.status === 'draft' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(proposal)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSubmitProposal(proposal.id)}
                          >
                            Submit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteProposal(proposal.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
