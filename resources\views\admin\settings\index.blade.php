@extends('layouts.admin')

@section('title', 'Website Settings')

@section('styles')
<style>
    .setting-card {
        transition: all 0.3s ease;
    }
    .setting-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .preview-logo {
        max-height: 80px;
        max-width: 80px;
        object-fit: cover;
        border-radius: 50%;
        border: 3px solid #10b981;
        padding: 4px;
        background: white;
    }
</style>
@endsection

@section('content')
<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Website Settings</h1>
                    <p class="text-muted">Manage your website appearance and contact information</p>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <!-- General Settings -->
                <div class="card setting-card mb-4">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>General Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="app_name" class="form-label">Application Name *</label>
                                    <input type="text" class="form-control" id="app_name" name="settings[app_name]" 
                                           value="{{ old('settings.app_name', $settings['app_name'] ?? 'Laravel NGO') }}" required>
                                    <div class="form-text">This name will appear in the header and throughout the website.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="app_logo" class="form-label">Application Logo</label>
                                    <input type="file" class="form-control" id="app_logo" name="app_logo" accept="image/*">
                                    <div class="form-text">Upload a square image (recommended: 200x200px or larger). Will be displayed as circular.</div>
                                    @if(!empty($settings['app_logo']))
                                        <div class="mt-2">
                                            <img src="{{ $settings['app_logo'] }}" alt="Current Logo" class="preview-logo">
                                            <small class="text-muted d-block mt-1">Current logo</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="site_description" class="form-label">Site Description</label>
                            <textarea class="form-control" id="site_description" name="settings[site_description]" rows="3">{{ old('settings.site_description', $settings['site_description'] ?? '') }}</textarea>
                            <div class="form-text">Brief description of your organization for SEO and display purposes.</div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card setting-card mb-4">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Contact Email *</label>
                                    <input type="email" class="form-control" id="contact_email" name="settings[contact_email]" 
                                           value="{{ old('settings.contact_email', $settings['contact_email'] ?? '') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Contact Phone</label>
                                    <input type="text" class="form-control" id="contact_phone" name="settings[contact_phone]" 
                                           value="{{ old('settings.contact_phone', $settings['contact_phone'] ?? '') }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="organization_address" class="form-label">Organization Address</label>
                            <textarea class="form-control" id="organization_address" name="settings[organization_address]" rows="2">{{ old('settings.organization_address', $settings['organization_address'] ?? '') }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="organization_city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="organization_city" name="settings[organization_city]" 
                                           value="{{ old('settings.organization_city', $settings['organization_city'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="organization_state" class="form-label">State/Province</label>
                                    <input type="text" class="form-control" id="organization_state" name="settings[organization_state]" 
                                           value="{{ old('settings.organization_state', $settings['organization_state'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="organization_country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="organization_country" name="settings[organization_country]" 
                                           value="{{ old('settings.organization_country', $settings['organization_country'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="organization_postal_code" class="form-label">Postal Code</label>
                                    <input type="text" class="form-control" id="organization_postal_code" name="settings[organization_postal_code]" 
                                           value="{{ old('settings.organization_postal_code', $settings['organization_postal_code'] ?? '') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Links -->
                <div class="card setting-card mb-4">
                    <div class="card-header bg-gradient-success text-white">
                        <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>Social Media Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_facebook" class="form-label">
                                        <i class="fab fa-facebook text-primary me-2"></i>Facebook Page URL
                                    </label>
                                    <input type="url" class="form-control" id="social_facebook" name="settings[social_facebook]" 
                                           value="{{ old('settings.social_facebook', $settings['social_facebook'] ?? '') }}"
                                           placeholder="https://facebook.com/yourpage">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_twitter" class="form-label">
                                        <i class="fab fa-twitter text-info me-2"></i>Twitter Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="social_twitter" name="settings[social_twitter]" 
                                           value="{{ old('settings.social_twitter', $settings['social_twitter'] ?? '') }}"
                                           placeholder="https://twitter.com/yourprofile">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_instagram" class="form-label">
                                        <i class="fab fa-instagram text-danger me-2"></i>Instagram Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="social_instagram" name="settings[social_instagram]" 
                                           value="{{ old('settings.social_instagram', $settings['social_instagram'] ?? '') }}"
                                           placeholder="https://instagram.com/yourprofile">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_linkedin" class="form-label">
                                        <i class="fab fa-linkedin text-primary me-2"></i>LinkedIn Page URL
                                    </label>
                                    <input type="url" class="form-control" id="social_linkedin" name="settings[social_linkedin]" 
                                           value="{{ old('settings.social_linkedin', $settings['social_linkedin'] ?? '') }}"
                                           placeholder="https://linkedin.com/company/yourcompany">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_youtube" class="form-label">
                                        <i class="fab fa-youtube text-danger me-2"></i>YouTube Channel URL
                                    </label>
                                    <input type="url" class="form-control" id="social_youtube" name="settings[social_youtube]" 
                                           value="{{ old('settings.social_youtube', $settings['social_youtube'] ?? '') }}"
                                           placeholder="https://youtube.com/channel/yourchannel">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_tiktok" class="form-label">
                                        <i class="fab fa-tiktok text-dark me-2"></i>TikTok Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="social_tiktok" name="settings[social_tiktok]" 
                                           value="{{ old('settings.social_tiktok', $settings['social_tiktok'] ?? '') }}"
                                           placeholder="https://tiktok.com/@yourprofile">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_whatsapp" class="form-label">
                                        <i class="fab fa-whatsapp text-success me-2"></i>WhatsApp Number
                                    </label>
                                    <input type="text" class="form-control" id="social_whatsapp" name="settings[social_whatsapp]" 
                                           value="{{ old('settings.social_whatsapp', $settings['social_whatsapp'] ?? '') }}"
                                           placeholder="+234xxxxxxxxxx">
                                    <div class="form-text">Include country code (e.g., +234xxxxxxxxxx)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="social_telegram" class="form-label">
                                        <i class="fab fa-telegram text-info me-2"></i>Telegram Channel URL
                                    </label>
                                    <input type="url" class="form-control" id="social_telegram" name="settings[social_telegram]" 
                                           value="{{ old('settings.social_telegram', $settings['social_telegram'] ?? '') }}"
                                           placeholder="https://t.me/yourchannel">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2 mb-4">
                    <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-undo me-2"></i>Reset Changes
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Preview logo on file select
    document.getElementById('app_logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                let preview = document.querySelector('.preview-logo');
                if (!preview) {
                    const container = document.createElement('div');
                    container.className = 'mt-2';
                    container.innerHTML = '<img class="preview-logo" alt="Logo Preview"><small class="text-muted d-block mt-1">Logo preview</small>';
                    document.getElementById('app_logo').parentNode.appendChild(container);
                    preview = container.querySelector('.preview-logo');
                }
                preview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const appName = document.getElementById('app_name').value.trim();
        const contactEmail = document.getElementById('contact_email').value.trim();
        
        if (!appName) {
            e.preventDefault();
            alert('Application name is required.');
            document.getElementById('app_name').focus();
            return;
        }
        
        if (!contactEmail) {
            e.preventDefault();
            alert('Contact email is required.');
            document.getElementById('contact_email').focus();
            return;
        }
    });
</script>
@endsection 