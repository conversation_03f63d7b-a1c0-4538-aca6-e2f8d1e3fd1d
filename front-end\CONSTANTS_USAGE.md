# Constants Usage Guide

This guide explains how to use the centralized constants file (`lib/constants.ts`) in the HALIMAKQ Foundation Next.js frontend application.

## Overview

The constants file centralizes all configuration values, making it easy to:
- Change API URLs for different environments
- Update app branding and information
- Modify theme colors and styling
- Configure business logic constants
- Manage validation rules

## Quick Start

### 1. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your specific values:

```env
# For Development
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# For Production
NEXT_PUBLIC_API_URL=https://halimakqfoundation.fnskills.ng/api/v1
NEXT_PUBLIC_BACKEND_URL=https://halimakqfoundation.fnskills.ng
```

### 2. Using Constants in Components

Import the constants you need:

```typescript
import { 
  API_BASE_URL, 
  APP_CONFIG, 
  THEME_CONFIG, 
  VALIDATION_RULES 
} from '@/lib/constants'

// Use in your component
export default function MyComponent() {
  return (
    <div>
      <h1>{APP_CONFIG.NAME}</h1>
      <p>{APP_CONFIG.TAGLINE}</p>
    </div>
  )
}
```

## Available Constants

### API Configuration

```typescript
import { API_BASE_URL, BACKEND_BASE_URL, API_ENDPOINTS } from '@/lib/constants'

// API calls
const response = await fetch(`${API_BASE_URL}/scholarships`)

// Authentication redirects
window.location.href = `${BACKEND_BASE_URL}/auth/register`

// Using predefined endpoints
const scholarships = await fetch(`${API_BASE_URL}${API_ENDPOINTS.PUBLIC.SCHOLARSHIPS}`)
```

### App Configuration

```typescript
import { APP_CONFIG, CONTACT_INFO, SOCIAL_LINKS } from '@/lib/constants'

// App branding
<title>{APP_CONFIG.NAME} - {APP_CONFIG.TAGLINE}</title>
<meta name="description" content={APP_CONFIG.DESCRIPTION} />

// Contact information
<a href={`mailto:${CONTACT_INFO.EMAIL}`}>Contact Us</a>
<a href={SOCIAL_LINKS.FACEBOOK}>Follow us on Facebook</a>
```

### Theme and Styling

```typescript
import { THEME_CONFIG, LAYOUT_CONFIG } from '@/lib/constants'

// Using theme colors
<div className={`bg-gradient-to-r ${THEME_CONFIG.GRADIENTS.PRIMARY}`}>
  <button style={{ backgroundColor: THEME_CONFIG.COLORS.PRIMARY }}>
    Donate Now
  </button>
</div>

// Layout classes
<div className={`${LAYOUT_CONFIG.CONTAINER_MAX_WIDTH} mx-auto ${LAYOUT_CONFIG.SECTION_PADDING}`}>
  Content here
</div>
```

### Business Logic Constants

```typescript
import { 
  SCHOLARSHIP_CATEGORIES, 
  USER_ROLES, 
  APPLICATION_STATUS,
  PAYMENT_METHODS 
} from '@/lib/constants'

// Scholarship filtering
const primaryScholarships = scholarships.filter(
  s => s.category === SCHOLARSHIP_CATEGORIES.PRIMARY
)

// Role-based access
if (user.role === USER_ROLES.ADMIN) {
  // Show admin features
}

// Payment method selection
<select>
  <option value={PAYMENT_METHODS.PAYSTACK}>Card Payment</option>
  <option value={PAYMENT_METHODS.BANK_TRANSFER}>Bank Transfer</option>
</select>
```

### Validation

```typescript
import { VALIDATION_RULES, FILE_UPLOAD } from '@/lib/constants'

// Email validation
const isValidEmail = VALIDATION_RULES.EMAIL_REGEX.test(email)

// File upload validation
const isValidFile = file.size <= FILE_UPLOAD.MAX_SIZE && 
                   FILE_UPLOAD.ALLOWED_TYPES.includes(file.type)
```

## Environment-Specific Configuration

### Development
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
NEXT_PUBLIC_DEBUG=true
```

### Production
```env
NEXT_PUBLIC_API_URL=https://halimakqfoundation.fnskills.ng/api/v1
NEXT_PUBLIC_BACKEND_URL=https://halimakqfoundation.fnskills.ng
NEXT_PUBLIC_ENABLE_ANALYTICS=true
```

### Staging
```env
NEXT_PUBLIC_API_URL=https://staging.halimakqfoundation.fnskills.ng/api/v1
NEXT_PUBLIC_BACKEND_URL=https://staging.halimakqfoundation.fnskills.ng
```

## Common Use Cases

### 1. API Client Configuration

```typescript
// lib/api.ts
import { API_BASE_URL } from './constants'

const apiClient = {
  baseURL: API_BASE_URL,
  // ... rest of configuration
}
```

### 2. Form Validation

```typescript
// components/forms/ContactForm.tsx
import { VALIDATION_RULES } from '@/lib/constants'

const validateForm = (data) => {
  const errors = {}
  
  if (!VALIDATION_RULES.EMAIL_REGEX.test(data.email)) {
    errors.email = 'Invalid email format'
  }
  
  if (data.message.length > VALIDATION_RULES.MESSAGE_MAX_LENGTH) {
    errors.message = 'Message too long'
  }
  
  return errors
}
```

### 3. Theme Customization

```typescript
// components/ui/Button.tsx
import { THEME_CONFIG } from '@/lib/constants'

const Button = ({ variant = 'primary', ...props }) => {
  const getButtonColor = () => {
    switch (variant) {
      case 'primary':
        return THEME_CONFIG.COLORS.PRIMARY
      case 'secondary':
        return THEME_CONFIG.COLORS.SECONDARY
      default:
        return THEME_CONFIG.COLORS.PRIMARY
    }
  }
  
  return (
    <button 
      style={{ backgroundColor: getButtonColor() }}
      {...props}
    />
  )
}
```

## Best Practices

1. **Always import specific constants** instead of the entire constants object
2. **Use environment variables** for values that change between environments
3. **Group related constants** together in the constants file
4. **Use TypeScript const assertions** (`as const`) for better type safety
5. **Document any new constants** you add to the file

## Updating Constants

To add new constants:

1. Open `lib/constants.ts`
2. Add your constant to the appropriate section
3. Export it in the default export object
4. Update this documentation if needed

Example:
```typescript
// Add to constants.ts
export const NEW_FEATURE_CONFIG = {
  ENABLED: process.env.NEXT_PUBLIC_NEW_FEATURE === 'true',
  MAX_ITEMS: 10,
} as const

// Add to default export
const CONSTANTS = {
  // ... existing constants
  NEW_FEATURE_CONFIG,
} as const
```

## Troubleshooting

### Environment Variables Not Working
- Ensure variables start with `NEXT_PUBLIC_`
- Restart the development server after adding new variables
- Check that `.env.local` is in the project root

### TypeScript Errors
- Make sure to use `as const` for constant objects
- Import specific constants instead of the entire object
- Check that all constants are properly exported

### Build Issues
- Verify all environment variables are set in production
- Check that constants are not undefined at build time
- Ensure all imports are correct and files exist
