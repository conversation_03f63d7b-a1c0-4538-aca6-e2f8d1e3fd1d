<?php

namespace App\Notifications;

use App\Models\PartnerOrganization;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;

class ScholarshipEligibilityNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $eligibleStudents;
    protected $partnerOrganization;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $eligibleStudents, PartnerOrganization $partnerOrganization)
    {
        $this->eligibleStudents = $eligibleStudents;
        $this->partnerOrganization = $partnerOrganization;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $studentCount = count($this->eligibleStudents);
        $organizationName = $this->partnerOrganization->name;

        $message = (new MailMessage)
            ->subject("New Scholarship Opportunities Available - {$organizationName}")
            ->greeting("Hello {$notifiable->name},")
            ->line("Great news! We have identified {$studentCount} student(s) from {$organizationName} who are now eligible for new scholarship opportunities following their recent grade progression.")
            ->line("Here are the details:");

        // Add student details
        foreach ($this->eligibleStudents as $eligibleStudent) {
            $student = $eligibleStudent['student'];
            $progression = $eligibleStudent['progression'];
            $scholarshipCount = count($eligibleStudent['eligibility']['scholarships']);

            $message->line("• **{$student->full_name}** - Advanced from {$this->formatGrade($progression->from_grade)} to {$this->formatGrade($progression->to_grade)} - {$scholarshipCount} scholarship(s) available");
        }

        $message->action('View Eligible Students', url('/dashboard/partner/progression'))
            ->line('Please log in to your Partner Dashboard to review these opportunities and submit applications on behalf of your students.')
            ->line('Remember that scholarship deadlines may apply, so we recommend reviewing and applying as soon as possible.')
            ->line('Thank you for your continued partnership in supporting student education!');

        return $message;
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        $studentCount = count($this->eligibleStudents);
        
        return [
            'type' => 'scholarship_eligibility',
            'title' => 'New Scholarship Opportunities Available',
            'message' => "{$studentCount} student(s) are now eligible for new scholarships following grade progression",
            'data' => [
                'partner_organization_id' => $this->partnerOrganization->id,
                'partner_organization_name' => $this->partnerOrganization->name,
                'eligible_students_count' => $studentCount,
                'eligible_students' => array_map(function ($eligibleStudent) {
                    return [
                        'student_id' => $eligibleStudent['student']->id,
                        'student_name' => $eligibleStudent['student']->full_name,
                        'from_grade' => $eligibleStudent['progression']->from_grade,
                        'to_grade' => $eligibleStudent['progression']->to_grade,
                        'scholarship_count' => count($eligibleStudent['eligibility']['scholarships']),
                        'category' => $eligibleStudent['eligibility']['category'],
                    ];
                }, $this->eligibleStudents),
            ],
            'action_url' => '/dashboard/partner/progression',
            'action_text' => 'View Eligible Students',
        ];
    }

    /**
     * Format grade level for display
     */
    private function formatGrade(string $grade): string
    {
        $gradeMap = [
            'primary_1' => 'Primary 1',
            'primary_2' => 'Primary 2',
            'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4',
            'primary_5' => 'Primary 5',
            'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1',
            'secondary_2' => 'Secondary 2',
            'secondary_3' => 'Secondary 3',
            'secondary_4' => 'Secondary 4',
            'secondary_5' => 'Secondary 5',
            'secondary_6' => 'Secondary 6',
            'university' => 'University',
        ];

        return $gradeMap[$grade] ?? ucfirst(str_replace('_', ' ', $grade));
    }
}
