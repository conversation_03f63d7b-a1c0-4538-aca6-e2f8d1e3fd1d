{{-- Dashboard Statistics Card Component --}}
@props([
    'title' => '',
    'value' => '',
    'icon' => '',
    'color' => 'blue',
    'trend' => null,
    'trendDirection' => 'up'
])

<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between">
        <div>
            <p class="text-sm font-medium text-gray-600">{{ $title }}</p>
            <p class="text-3xl font-bold text-{{ $color }}-600">{{ $value }}</p>
            @if($trend)
                <div class="flex items-center mt-2">
                    @if($trendDirection === 'up')
                        <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                        <span class="text-sm text-green-600">+{{ $trend }}</span>
                    @else
                        <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                        <span class="text-sm text-red-600">-{{ $trend }}</span>
                    @endif
                    <span class="text-sm text-gray-500 ml-1">from last month</span>
                </div>
            @endif
        </div>
        @if($icon)
            <div class="p-3 bg-{{ $color }}-100 rounded-full">
                <i class="fas fa-{{ $icon }} text-{{ $color }}-600 text-2xl"></i>
            </div>
        @endif
    </div>
</div>
