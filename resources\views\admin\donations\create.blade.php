@extends('layouts.admin')

@section('title', 'Create Donation')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create New Donation</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.donations.index') }}">Donations</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.donations.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="donor_name" class="form-label">Donor Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="donor_name" name="donor_name" required value="{{ old('donor_name') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="donor_email" class="form-label">Donor Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="donor_email" name="donor_email" required value="{{ old('donor_email') }}">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">₦</span>
                                    <input type="number" class="form-control" id="amount" name="amount" required min="1" step="0.01" value="{{ old('amount') }}">
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="currency" class="form-label">Currency</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="NGN" {{ old('currency') == 'NGN' ? 'selected' : '' }}>Nigerian Naira (₦)</option>
                                    <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>US Dollar ($)</option>
                                    <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>Euro (€)</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="donation_type" class="form-label">Donation Type</label>
                                <select class="form-select" id="donation_type" name="donation_type">
                                    <option value="general" {{ old('donation_type') == 'general' ? 'selected' : '' }}>General</option>
                                    <option value="education" {{ old('donation_type') == 'education' ? 'selected' : '' }}>Education</option>
                                    <option value="health" {{ old('donation_type') == 'health' ? 'selected' : '' }}>Health</option>
                                    <option value="emergency" {{ old('donation_type') == 'emergency' ? 'selected' : '' }}>Emergency</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="frequency" class="form-label">Frequency</label>
                                <select class="form-select" id="frequency" name="frequency">
                                    <option value="one-time" {{ old('frequency') == 'one-time' ? 'selected' : '' }}>One-time</option>
                                    <option value="monthly" {{ old('frequency') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                    <option value="quarterly" {{ old('frequency') == 'quarterly' ? 'selected' : '' }}>Quarterly</option>
                                    <option value="annually" {{ old('frequency') == 'annually' ? 'selected' : '' }}>Annually</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="purpose" class="form-label">Purpose/Message</label>
                            <textarea class="form-control" id="purpose" name="purpose" rows="3" placeholder="Optional message or specific purpose for the donation">{{ old('purpose') }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>Credit/Debit Card</option>
                                    <option value="paystack" {{ old('payment_method') == 'paystack' ? 'selected' : '' }}>Paystack</option>
                                    <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_status" class="form-label">Payment Status</label>
                                <select class="form-select" id="payment_status" name="payment_status">
                                    <option value="pending" {{ old('payment_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="completed" {{ old('payment_status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="failed" {{ old('payment_status') == 'failed' ? 'selected' : '' }}>Failed</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="transaction_reference" class="form-label">Transaction Reference</label>
                            <input type="text" class="form-control" id="transaction_reference" name="transaction_reference" value="{{ old('transaction_reference') }}" placeholder="Optional transaction reference">
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.donations.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Donations
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Donation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> This form is for manually recording donations received through other channels.
                    </div>
                    
                    <h6 class="font-weight-bold">Required Information:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Donor name and email</li>
                        <li><i class="fas fa-check text-success me-2"></i>Donation amount</li>
                        <li><i class="fas fa-check text-success me-2"></i>Payment method</li>
                        <li><i class="fas fa-check text-success me-2"></i>Payment status</li>
                    </ul>

                    <h6 class="font-weight-bold mt-3">Payment Status:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-warning me-2">Pending</span>Payment not yet received</li>
                        <li><span class="badge bg-success me-2">Completed</span>Payment confirmed</li>
                        <li><span class="badge bg-danger me-2">Failed</span>Payment failed or cancelled</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
