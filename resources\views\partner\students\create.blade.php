@extends('layouts.dashboard')

@section('title', 'Add New Student')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Add New Student</h1>
                            <p class="text-gray-600">Add a new student to your organization</p>
                        </div>
                        <div class="flex gap-3">
                            <a href="{{ route('partner.students.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Students
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Student Information</h2>
                        <p class="text-gray-600 mt-1">Fill in the details for the new student</p>
                    </div>

                    <form action="{{ route('partner.students.store') }}" method="POST" enctype="multipart/form-data" class="p-6">
                        @csrf

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Student Name -->
                            <div class="lg:col-span-2">
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Student Full Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('name') border-red-500 @enderror"
                                       placeholder="Enter student's full name"
                                       required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Class -->
                            <div>
                                <label for="class" class="block text-sm font-medium text-gray-700 mb-2">
                                    Class/Grade <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="class" 
                                       name="class" 
                                       value="{{ old('class') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('class') border-red-500 @enderror"
                                       placeholder="e.g., Grade 5, JSS 2, SS 1"
                                       required>
                                @error('class')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Age -->
                            <div>
                                <label for="age" class="block text-sm font-medium text-gray-700 mb-2">
                                    Age <span class="text-red-500">*</span>
                                </label>
                                <input type="number" 
                                       id="age" 
                                       name="age" 
                                       value="{{ old('age') }}"
                                       min="5" 
                                       max="25"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('age') border-red-500 @enderror"
                                       placeholder="Enter age"
                                       required>
                                @error('age')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Gender -->
                            <div>
                                <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                                    Gender <span class="text-red-500">*</span>
                                </label>
                                <select id="gender" 
                                        name="gender" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('gender') border-red-500 @enderror"
                                        required>
                                    <option value="">Select Gender</option>
                                    <option value="male" {{ old('gender') === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="female" {{ old('gender') === 'female' ? 'selected' : '' }}>Female</option>
                                </select>
                                @error('gender')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Parent Contact -->
                            <div>
                                <label for="parent_contact" class="block text-sm font-medium text-gray-700 mb-2">
                                    Parent/Guardian Contact <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="parent_contact" 
                                       name="parent_contact" 
                                       value="{{ old('parent_contact') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('parent_contact') border-red-500 @enderror"
                                       placeholder="Phone number or email"
                                       required>
                                @error('parent_contact')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Parent Name -->
                            <div>
                                <label for="parent_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Parent/Guardian Name
                                </label>
                                <input type="text" 
                                       id="parent_name" 
                                       name="parent_name" 
                                       value="{{ old('parent_name') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('parent_name') border-red-500 @enderror"
                                       placeholder="Enter parent/guardian name">
                                @error('parent_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Student Photo -->
                            <div class="lg:col-span-2">
                                <label for="photo" class="block text-sm font-medium text-gray-700 mb-2">
                                    Student Photo
                                </label>
                                <div class="flex items-center space-x-6">
                                    <div class="shrink-0">
                                        <img id="photo-preview" class="h-16 w-16 object-cover rounded-full border-2 border-gray-300 hidden" alt="Photo preview">
                                        <div id="photo-placeholder" class="h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                    </div>
                                    <label class="block">
                                        <span class="sr-only">Choose photo</span>
                                        <input type="file" 
                                               id="photo" 
                                               name="photo" 
                                               accept="image/*"
                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100 @error('photo') border-red-500 @enderror">
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">PNG, JPG up to 2MB</p>
                                @error('photo')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Address -->
                            <div class="lg:col-span-2">
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                    Home Address
                                </label>
                                <textarea id="address" 
                                          name="address" 
                                          rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('address') border-red-500 @enderror"
                                          placeholder="Enter student's home address">{{ old('address') }}</textarea>
                                @error('address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-6 mt-6 border-t border-gray-200">
                            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 flex-1 sm:flex-none">
                                <i class="fas fa-save mr-2"></i>Add Student
                            </button>
                            <a href="{{ route('partner.students.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 text-center flex-1 sm:flex-none">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Photo preview functionality
    document.getElementById('photo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('photo-preview');
        const placeholder = document.getElementById('photo-placeholder');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('hidden');
                placeholder.classList.add('hidden');
            };
            reader.readAsDataURL(file);
        } else {
            preview.classList.add('hidden');
            placeholder.classList.remove('hidden');
        }
    });
</script>
@endpush
@endsection
