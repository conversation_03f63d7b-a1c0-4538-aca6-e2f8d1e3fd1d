<?php

require_once __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\ScholarshipApplication;
use App\Http\Controllers\Api\ImpactStatisticsController;
use Illuminate\Support\Facades\DB;

echo "Testing Impact Statistics API:\n";

try {
    $controller = new ImpactStatisticsController();
    $response = $controller->getImpactStatistics();
    echo "Success! Response:\n";
    echo json_encode($response->getData(), JSON_PRETTY_PRINT) . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
