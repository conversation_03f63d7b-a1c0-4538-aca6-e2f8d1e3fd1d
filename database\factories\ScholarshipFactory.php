<?php

namespace Database\Factories;

use App\Models\Scholarship;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Scholarship>
 */
class ScholarshipFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Scholarship::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['primary', 'secondary', 'university'];
        $category = $this->faker->randomElement($categories);
        $title = $this->faker->sentence(4) . ' Scholarship';

        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title . '-' . $this->faker->unique()->numberBetween(1000, 9999)),
            'description' => $this->faker->paragraph(3),
            'category' => $category,
            'eligibility_criteria' => $this->faker->paragraph(2),
            'amount' => $this->faker->randomElement([25000, 50000, 75000, 100000, 150000, 200000]),
            'application_deadline' => $this->faker->dateTimeBetween('now', '+6 months'),
            'requirements' => json_encode([
                'minimum_gpa' => $this->faker->randomFloat(2, 2.0, 4.0),
                'age_limit' => $this->faker->numberBetween(16, 25),
                'citizenship' => 'Nigerian'
            ]),
            'status' => $this->faker->randomElement(['draft', 'open', 'closed']),
            'max_applicants' => $this->faker->numberBetween(5, 50),
            'current_applicants' => $this->faker->numberBetween(0, 20),
            'contact_email' => $this->faker->safeEmail(),
            'documents_required' => json_encode([
                'Academic transcript',
                'Letter of recommendation',
                'Personal statement',
                'Financial need documentation'
            ]),
            'is_open' => $this->faker->boolean(70),
            'custom_fields' => json_encode([
                'academic_year' => $this->faker->randomElement(['2024-2025', '2025-2026']),
                'renewable' => $this->faker->boolean(60),
                'tags' => $this->faker->randomElements([
                    'merit-based', 'need-based', 'stem', 'arts', 'sports',
                    'leadership', 'community-service', 'minority', 'women'
                ], $this->faker->numberBetween(1, 4))
            ]),
            'application_start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'is_featured' => $this->faker->boolean(20),
            'application_instructions' => $this->faker->paragraph(3),
            'terms_conditions' => $this->faker->paragraph(4),
            'image' => $this->faker->optional()->imageUrl(400, 200, 'education'),
        ];
    }

    /**
     * Indicate that the scholarship is for primary students.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'primary',
            'title' => 'Primary Education ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([25000, 50000, 75000]),
        ]);
    }

    /**
     * Indicate that the scholarship is for secondary students.
     */
    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'secondary',
            'title' => 'Secondary Education ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([50000, 75000, 100000]),
        ]);
    }

    /**
     * Indicate that the scholarship is for university students.
     */
    public function university(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'university',
            'title' => 'University ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([100000, 150000, 200000, 250000]),
        ]);
    }

    /**
     * Indicate that the scholarship is open.
     */
    public function open(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'open',
            'is_open' => true,
            'application_deadline' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
        ]);
    }

    /**
     * Indicate that the scholarship is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'is_open' => false,
            'application_deadline' => $this->faker->dateTimeBetween('-6 months', '-1 month'),
        ]);
    }

    /**
     * Indicate that the scholarship is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the scholarship is renewable.
     */
    public function renewable(): static
    {
        return $this->state(function (array $attributes) {
            $customFields = json_decode($attributes['custom_fields'] ?? '{}', true);
            $customFields['renewable'] = true;
            $customFields['renewal_criteria'] = 'Maintain minimum GPA of 3.0 and demonstrate continued financial need.';

            return [
                'custom_fields' => json_encode($customFields),
            ];
        });
    }
}
