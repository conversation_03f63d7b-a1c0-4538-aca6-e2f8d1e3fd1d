<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('scholarship_applications', 'application_id')) {
                $table->string('application_id', 20)->unique()->after('id');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'form_data')) {
                $table->json('form_data')->after('user_id');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'uploaded_files')) {
                $table->json('uploaded_files')->nullable()->after('form_data');
            }
            
            // Update status enum if needed
            if (Schema::hasColumn('scholarship_applications', 'status')) {
                $table->enum('status', ['pending', 'under_review', 'approved', 'rejected'])->default('pending')->change();
            } else {
                $table->enum('status', ['pending', 'under_review', 'approved', 'rejected'])->default('pending')->after('uploaded_files');
            }
            
            // Add missing columns
            if (!Schema::hasColumn('scholarship_applications', 'admin_notes')) {
                $table->text('admin_notes')->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'submitted_at')) {
                $table->timestamp('submitted_at')->after('admin_notes');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'reviewed_at')) {
                $table->timestamp('reviewed_at')->nullable()->after('submitted_at');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'reviewed_by')) {
                $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null')->after('reviewed_at');
            }
            
            // Add indexes if they don't exist
            try {
                $table->index('application_id');
            } catch (Exception $e) {
                // Index might already exist
            }
        });
        
        // Populate application_id for existing records
        DB::statement("
            UPDATE scholarship_applications 
            SET application_id = CONCAT('HLTKKQ-SCH-', LPAD(id, 4, '0'))
            WHERE application_id IS NULL OR application_id = ''
        ");
        
        // Populate submitted_at for existing records
        DB::statement("
            UPDATE scholarship_applications 
            SET submitted_at = created_at
            WHERE submitted_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            try {
                $table->dropIndex(['application_id']);
            } catch (Exception $e) {
                // Index might not exist
            }
            
            if (Schema::hasColumn('scholarship_applications', 'application_id')) {
                $table->dropColumn('application_id');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'form_data')) {
                $table->dropColumn('form_data');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'uploaded_files')) {
                $table->dropColumn('uploaded_files');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'admin_notes')) {
                $table->dropColumn('admin_notes');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'submitted_at')) {
                $table->dropColumn('submitted_at');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'reviewed_at')) {
                $table->dropColumn('reviewed_at');
            }
            
            if (Schema::hasColumn('scholarship_applications', 'reviewed_by')) {
                $table->dropForeign(['reviewed_by']);
                $table->dropColumn('reviewed_by');
            }
        });
    }
};
