import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export function KofaGetInvolvedBanner() {
  return (
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-green-700 via-green-800 to-green-900 shadow-2xl">
      <div className="absolute inset-0 bg-grid opacity-20"></div>
      <div className="absolute top-0 right-0 w-1/3 h-full bg-amber-500/20 blur-3xl rounded-full transform translate-x-1/2"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/2 bg-blue-300/20 blur-3xl rounded-full transform -translate-x-1/2"></div>
      <div className="absolute top-1/4 left-1/4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-16 h-16 bg-amber-400/20 rounded-full blur-lg"></div>

      <div className="relative z-10 px-8 py-16 lg:py-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              <div className="space-y-4">
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white tracking-tight">
                  Get <span className="text-amber-400">Involved</span>
                </h2>
                <p className="text-xl md:text-2xl text-green-100 leading-relaxed max-w-2xl">
                  Join us in transforming lives through education. Every contribution brings a child closer to their
                  dreams.
                </p>
              </div>

              <div className="flex flex-wrap gap-4">
                <Link href="/donate">
                  <Button
                    size="lg"
                    className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold text-lg px-8 py-4 transition-all duration-300 hover:scale-105 hover:shadow-xl rounded-full"
                  >
                    <Heart className="mr-2 h-5 w-5" />
                    Donate Now
                  </Button>
                </Link>
                <Link href="/volunteer">
                  <Button
                    size="lg"
                    variant="outline"
                    className="text-white border-white hover:bg-white/10 font-semibold text-lg px-8 py-4 transition-all duration-300 hover:scale-105 rounded-full backdrop-blur-sm"
                  >
                    Volunteer
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>

              <div className="grid grid-cols-3 gap-6 pt-6">
                <div className="text-center">
                  <div className="flex justify-center mb-2">
                    <Users className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">1,200+</div>
                  <div className="text-green-200 text-sm">Students Helped</div>
                </div>
                <div className="text-center">
                  <div className="flex justify-center mb-2">
                    <BookOpen className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">5,000+</div>
                  <div className="text-green-200 text-sm">Books Donated</div>
                </div>
                <div className="text-center">
                  <div className="flex justify-center mb-2">
                    <Heart className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">₦12M</div>
                  <div className="text-green-200 text-sm">Scholarships</div>
                </div>
              </div>
            </div>

            {/* Side Card */}
            <div className="lg:col-span-1">
              <div className="bg-white/10 backdrop-blur-lg p-8 rounded-2xl border border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300 hover:scale-105">
                <div className="text-center space-y-4">
                  <div className="text-5xl font-bold text-white">₦5,000</div>
                  <div className="text-amber-300 font-semibold text-lg">Can Provide</div>
                  <ul className="text-green-100 space-y-2 text-sm">
                    <li>• School supplies for 1 child</li>
                    <li>• Uniform and shoes</li>
                    <li>• Textbooks for a term</li>
                    <li>• Nutritious meals for a month</li>
                  </ul>
                  <Link href="/donate">
                    <Button className="w-full bg-amber-500 hover:bg-amber-600 text-green-950 font-medium transition-all duration-300 hover:scale-105 rounded-full">
                      Sponsor a Child
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
