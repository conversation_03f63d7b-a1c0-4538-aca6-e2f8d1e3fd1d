<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Event;
use Illuminate\Auth\Access\HandlesAuthorization;

class EventPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any events.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view events list
    }

    /**
     * Determine whether the user can view the event.
     */
    public function view(User $user, Event $event): bool
    {
        // Public events can be viewed by anyone
        if ($event->is_public) {
            return true;
        }

        // Admins can view all events
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can view their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Users registered for the event can view it
        if ($user->registeredEvents()->where('event_id', $event->id)->exists()) {
            return true;
        }

        // Partner organizations can view events they created or are involved in
        if ($user->role === 'partner_organization' && $event->created_by === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create events.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can update the event.
     */
    public function update(User $user, Event $event): bool
    {
        // Admins can update any event
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can update their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Partner organizations can update events they created
        if ($user->role === 'partner_organization' && $event->created_by === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the event.
     */
    public function delete(User $user, Event $event): bool
    {
        // Admins can delete any event
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can delete their events if no registrations exist
        if ($event->organizer_id === $user->id && $event->registrations()->count() === 0) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can register for the event.
     */
    public function register(User $user, Event $event): bool
    {
        // Check if event is open for registration
        if (!$event->is_active || $event->registration_deadline < now()) {
            return false;
        }

        // Check if event is full
        if ($event->max_participants && $event->registrations()->count() >= $event->max_participants) {
            return false;
        }

        // Check if user is already registered
        if ($user->registeredEvents()->where('event_id', $event->id)->exists()) {
            return false;
        }

        // All active users can register for public events
        if ($event->is_public && $user->isActive()) {
            return true;
        }

        // Check role-specific restrictions
        if ($event->target_audience) {
            $targetRoles = json_decode($event->target_audience, true);
            if (is_array($targetRoles) && !in_array($user->role, $targetRoles)) {
                return false;
            }
        }

        return $user->isActive();
    }

    /**
     * Determine whether the user can unregister from the event.
     */
    public function unregister(User $user, Event $event): bool
    {
        // Users can unregister if they are registered and event hasn't started
        return $user->registeredEvents()->where('event_id', $event->id)->exists() &&
               $event->start_date > now();
    }

    /**
     * Determine whether the user can view event registrations.
     */
    public function viewRegistrations(User $user, Event $event): bool
    {
        // Admins can view all registrations
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can view registrations for their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Partner organizations can view registrations for events they created
        if ($user->role === 'partner_organization' && $event->created_by === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can manage event registrations.
     */
    public function manageRegistrations(User $user, Event $event): bool
    {
        // Admins can manage all registrations
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can manage registrations for their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can check in attendees.
     */
    public function checkInAttendees(User $user, Event $event): bool
    {
        // Admins can check in attendees for any event
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can check in attendees
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Volunteers assigned to the event can check in attendees
        if ($user->role === 'volunteer' && $this->isVolunteerAssignedToEvent($user, $event)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view event analytics.
     */
    public function viewAnalytics(User $user, Event $event): bool
    {
        // Admins can view all analytics
        if ($user->role === 'admin') {
            return true;
        }

        // Event organizers can view analytics for their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Partner organizations can view analytics for events they created
        if ($user->role === 'partner_organization' && $event->created_by === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if volunteer is assigned to the event
     */
    private function isVolunteerAssignedToEvent(User $user, Event $event): bool
    {
        // This would need to be implemented based on your volunteer assignment logic
        // For now, return false as a placeholder
        return false;
    }
}
