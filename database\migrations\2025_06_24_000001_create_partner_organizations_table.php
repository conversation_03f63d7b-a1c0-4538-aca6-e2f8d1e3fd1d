<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partner_organizations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type')->default('school'); // school, university, ngo, government
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('website')->nullable();
            $table->text('description')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('contact_person_title')->nullable();
            $table->string('contact_person_email')->nullable();
            $table->string('contact_person_phone')->nullable();
            $table->year('established_year')->nullable();
            $table->integer('student_capacity')->nullable();
            $table->integer('current_students')->nullable();
            $table->date('partnership_start_date')->nullable();
            $table->string('partnership_status')->default('active'); // active, pending, inactive
            $table->string('logo')->nullable();
            $table->json('documents')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verification_date')->nullable();
            $table->timestamps();
        });

        // Add partner_organization_id to users table
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('partner_organization_id')->nullable()->after('role')
                ->constrained('partner_organizations')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['partner_organization_id']);
            $table->dropColumn('partner_organization_id');
        });
        
        Schema::dropIfExists('partner_organizations');
    }
};
