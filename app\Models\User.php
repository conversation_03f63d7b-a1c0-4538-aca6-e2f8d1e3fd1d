<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spa<PERSON>\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'phone_number',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'state',
        'country',
        'profile_picture',
        'role',
        'role_id',
        'student_id',
        'matriculation_number',
        'university_name',
        'course_of_study',
        'year_of_study',
        'cgpa',
        'organization_name',
        'organization_type',
        'organization_registration_number',
        'organization_address',
        'organization_phone',
        'organization_email',
        'principal_name',
        'principal_phone',
        'principal_email',
        'partner_organization_id',
        'institutional_role',
        'institutional_verification_status',
        'status',
        'last_login_at',
        'preferences',
        'short_id',
        'qr_code_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'date_of_birth' => 'date',
        'last_login_at' => 'datetime',
        'preferences' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = ['full_name', 'profile_picture_url'];

    /**
     * Generate a unique short ID for the user.
     */
    public static function generateShortId(): string
    {
        do {
            $shortId = 'HLTKKQ' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('short_id', $shortId)->exists());

        return $shortId;
    }

    /**
     * Generate QR code URL for the user.
     */
    public function generateQrCodeUrl(): string
    {
        try {
            // Use endroid/qr-code package to generate actual QR codes
            $qrCode = new \Endroid\QrCode\QrCode(
                data: $this->getQrData(),
                size: 300,
                margin: 10
            );

            $writer = new \Endroid\QrCode\Writer\PngWriter();
            $result = $writer->write($qrCode);

            // Save QR code to storage
            $filename = 'qr_code_' . $this->short_id . '.png';
            $path = 'qr-codes/' . $filename;
            
            Storage::disk('public')->put($path, $result->getString());

            return asset('storage/' . $path);
        } catch (\Exception $e) {
            \Log::error('QR Code generation failed: ' . $e->getMessage());
            return $this->generateFallbackQr();
        }
    }

    /**
     * Get QR code data content
     */
    private function getQrData(): string
    {
        return url("/user/{$this->short_id}");
    }

    /**
     * Generate fallback QR code as SVG.
     */
    private function generateFallbackQr(): string
    {
        $data = [
            'id' => $this->short_id,
            'name' => $this->full_name,
            'email' => $this->email,
            'organization' => 'HLTKKQ Foundation',
            'url' => config('app.url') . '/user/' . $this->short_id
        ];
        
        return 'data:image/svg+xml;base64,' . base64_encode($this->generateQrCodeSvg(json_encode($data)));
    }

    /**
     * Generate QR code SVG.
     */
    private function generateQrCodeSvg(string $data): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
    <rect width="200" height="200" fill="white"/>
    <text x="100" y="100" text-anchor="middle" dominant-baseline="middle" font-family="Arial" font-size="10">
        QR Code for ' . $this->short_id . '
    </text>
    <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="8">
        ' . $this->full_name . '
    </text>
</svg>';
    }

    /**
     * Boot method to generate short_id when creating a user.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->short_id)) {
                $user->short_id = self::generateShortId();
            }
        });

        static::created(function ($user) {
            if (empty($user->qr_code_url)) {
                $user->qr_code_url = $user->generateQrCodeUrl();
                $user->save();
            }
        });
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Role-based helper methods
     */
    public function isNormalUser(): bool
    {
        return $this->role === 'normal';
    }

    public function isUniversityStudent(): bool
    {
        return $this->role === 'student';
    }

    public function isPartnerOrganization(): bool
    {
        return $this->role === 'partner';
    }

    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isPartnerOrganization(): bool
    {
        return $this->role === 'partner';
    }

    public function getRoleNameAttribute(): string
    {
        return match($this->role) {
            'normal' => 'Normal User',
            'student' => 'University Student',
            'partner' => 'Partner Organization',
            'admin' => 'Admin',
            default => 'Unknown'
        };
    }

    public function getDashboardRouteAttribute(): string
    {
        return match($this->role) {
            'normal' => '/dashboard/user',
            'student' => '/student-dashboard',
            'partner' => '/partner-dashboard',
            'admin' => '/admin/dashboard',
            default => '/dashboard/user'
        };
    }

    /**
     * Get the user's profile picture URL.
     */
    public function getProfilePictureUrlAttribute(): string
    {
        if ($this->profile_picture) {
            // Use API route for avatars to support CORS
            return url("/api/v1/images/{$this->profile_picture}");
        }
        
        // Return a local default avatar image via API route for CORS
        return url("/api/v1/images/default-avatar.svg");
    }

    /**
     * Get the volunteer record associated with the user.
     */
    public function volunteer()
    {
        return $this->hasOne(Volunteer::class);
    }

    /**
     * Get the students managed by this partner organization.
     */
    public function partnerStudents()
    {
        return $this->hasMany(PartnerStudent::class, 'partner_id');
    }

    /**
     * Get scholarship applications made by this user (for university students).
     */
    public function scholarshipApplications()
    {
        return $this->hasMany(ScholarshipApplication::class, 'user_id');
    }

    /**
     * Get the active scholarship application for this user.
     */
    public function activeScholarshipApplication()
    {
        return $this->scholarshipApplications()
                    ->whereIn('status', ['pending', 'under_review', 'approved'])
                    ->latest()
                    ->first();
    }

    /**
     * Check if user has an active scholarship application.
     */
    public function hasActiveApplication(): bool
    {
        return $this->activeScholarshipApplication() !== null;
    }

    /**
     * Get the blog posts authored by the user.
     */
    public function blogPosts()
    {
        return $this->hasMany(BlogPost::class, 'author_id');
    }

    /**
     * Get the blog comments made by the user.
     */
    public function blogComments()
    {
        return $this->hasMany(BlogComment::class);
    }

    /**
     * Get the programs created by the user.
     */
    public function programs()
    {
        return $this->hasMany(Program::class, 'created_by');
    }

    /**
     * Get the events organized by the user.
     */
    public function organizedEvents()
    {
        return $this->hasMany(Event::class, 'organizer_id');
    }

    /**
     * Get the event registrations for the user.
     */
    public function eventRegistrations()
    {
        return $this->hasMany(EventRegistration::class);
    }

    /**
     * Get the events the user is registered for.
     */
    public function registeredEvents()
    {
        return $this->belongsToMany(Event::class, 'event_registrations')
                    ->withPivot(['status', 'registered_at', 'checked_in_at', 'additional_info'])
                    ->withTimestamps();
    }

    /**
     * Get the partner organization this user belongs to.
     */
    public function partnerOrganization()
    {
        return $this->belongsTo(PartnerOrganization::class);
    }

    /**
     * Get all partner organizations this user is associated with.
     */
    public function partnerOrganizations()
    {
        return $this->belongsToMany(PartnerOrganization::class, 'partner_organization_users')
                    ->withPivot(['role', 'status', 'start_date', 'end_date', 'permissions', 'notes'])
                    ->withTimestamps();
    }

    /**
     * Get active partner organizations for this user.
     */
    public function activePartnerOrganizations()
    {
        return $this->partnerOrganizations()->wherePivot('status', 'active');
    }

    /**
     * Check if user is associated with a partner organization.
     */
    public function hasPartnerOrganization(): bool
    {
        return $this->partner_organization_id !== null || $this->partnerOrganizations()->exists();
    }

    /**
     * Check if user is a partner organization representative.
     */
    public function isPartnerOrganizationUser(): bool
    {
        return $this->role === 'partner_organization' || $this->hasPartnerOrganization();
    }

    /**
     * Check if user is verified for institutional access.
     */
    public function isInstitutionallyVerified(): bool
    {
        return $this->institutional_verification_status === 'verified';
    }

    /**
     * Get the donations made by the user.
     */
    public function donations()
    {
        return $this->hasMany(Donation::class, 'donor_id');
    }

    /**
     * Get the scholarship applications made by the user.
     */
    public function scholarshipApplications()
    {
        return $this->hasMany(ScholarshipApplication::class);
    }

    /**
     * Get the contact messages assigned to the user.
     */
    public function assignedContactMessages()
    {
        return $this->hasMany(ContactMessage::class, 'assigned_to');
    }

    /**
     * Get the donation campaigns created by the user.
     */
    public function donationCampaigns()
    {
        return $this->hasMany(DonationCampaign::class, 'created_by');
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role_id === 4 || $this->role === 'admin';
    }

    /**
     * Check if user is a volunteer.
     */
    public function isVolunteer(): bool
    {
        return $this->role === 'volunteer';
    }

    /**
     * Check if user is a donor.
     */
    public function isDonor(): bool
    {
        return $this->role === 'donor';
    }



    /**
     * Check if user is an individual student.
     */
    public function isIndividualStudent(): bool
    {
        return in_array($this->role, ['user', 'student']) && !$this->partner_organization_id;
    }

    /**
     * Check if user is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include users with a specific role.
     */
    public function scopeWithRole($query, $role)
    {
        return $query->where('role', $role);
    }
}
