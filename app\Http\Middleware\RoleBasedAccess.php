<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleBasedAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$roles  Allowed role IDs
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Convert role IDs to integers for comparison
        $allowedRoles = array_map('intval', $roles);

        // Check if user's role is in the allowed roles
        if (!in_array($user->role_id, $allowedRoles)) {
            // Redirect to appropriate dashboard based on user's role
            return redirect($user->dashboard_route)
                ->with('error', 'You do not have permission to access this area.');
        }

        return $next($request);
    }
}
