<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteerTrainingProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'volunteer_id',
        'training_module_id',
        'progress_percentage',
        'is_completed',
        'completed_at',
        'time_spent_minutes',
        'quiz_score',
        'attempts'
    ];

    protected $casts = [
        'progress_percentage' => 'decimal:2',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'quiz_score' => 'decimal:2'
    ];

    /**
     * Get the volunteer that owns this progress
     */
    public function volunteer()
    {
        return $this->belongsTo(Volunteer::class);
    }

    /**
     * Get the training module for this progress
     */
    public function trainingModule()
    {
        return $this->belongsTo(VolunteerTrainingModule::class, 'training_module_id');
    }

    /**
     * Scope for completed progress
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    /**
     * Scope for in-progress items
     */
    public function scopeInProgress($query)
    {
        return $query->where('is_completed', false)
                    ->where('progress_percentage', '>', 0);
    }

    /**
     * Mark as completed
     */
    public function markCompleted($quizScore = null)
    {
        $this->update([
            'is_completed' => true,
            'progress_percentage' => 100,
            'completed_at' => now(),
            'quiz_score' => $quizScore
        ]);
    }

    /**
     * Update progress
     */
    public function updateProgress($percentage, $timeSpent = null)
    {
        $data = [
            'progress_percentage' => min(100, max(0, $percentage))
        ];

        if ($timeSpent !== null) {
            $data['time_spent_minutes'] = $this->time_spent_minutes + $timeSpent;
        }

        if ($percentage >= 100) {
            $data['is_completed'] = true;
            $data['completed_at'] = now();
        }

        $this->update($data);
    }

    /**
     * Get formatted time spent
     */
    public function getFormattedTimeSpentAttribute()
    {
        if (!$this->time_spent_minutes) {
            return '0 minutes';
        }

        $hours = intval($this->time_spent_minutes / 60);
        $minutes = $this->time_spent_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }

        return $minutes . ' minute' . ($minutes > 1 ? 's' : '');
    }

    /**
     * Get quiz grade
     */
    public function getQuizGradeAttribute()
    {
        if (!$this->quiz_score) {
            return null;
        }

        if ($this->quiz_score >= 90) return 'A';
        if ($this->quiz_score >= 80) return 'B';
        if ($this->quiz_score >= 70) return 'C';
        if ($this->quiz_score >= 60) return 'D';
        return 'F';
    }

    /**
     * Check if passed (assuming 70% is passing)
     */
    public function isPassed()
    {
        return $this->quiz_score >= 70;
    }
}
