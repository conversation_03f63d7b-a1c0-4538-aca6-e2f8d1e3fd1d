import { Card, CardContent } from "@/components/ui/card"
import { Droplets, GraduationCap, Shield } from "lucide-react"

export function DonationImpact() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <Card className="text-center neumorphic rounded-2xl">
        <CardContent className="p-6">
          <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Droplets className="h-8 w-8 text-blue-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Clean Water Access</h3>
          <p className="text-muted-foreground mb-4">
            ₦5,000 provides clean water access for one family for an entire month.
          </p>
          <div className="text-2xl font-bold text-blue-600">2,500+ families served</div>
        </CardContent>
      </Card>

      <Card className="text-center neumorphic rounded-2xl">
        <CardContent className="p-6">
          <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <GraduationCap className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Education Support</h3>
          <p className="text-muted-foreground mb-4">
            ₦15,000 sponsors school supplies and uniforms for 5 children for a full academic year.
          </p>
          <div className="text-2xl font-bold text-green-600">1,200+ children supported</div>
        </CardContent>
      </Card>

      <Card className="text-center neumorphic rounded-2xl">
        <CardContent className="p-6">
          <div className="h-16 w-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Healthcare Access</h3>
          <p className="text-muted-foreground mb-4">
            ₦50,000 provides comprehensive healthcare services for 10 families including medical checkups and
            treatments.
          </p>
          <div className="text-2xl font-bold text-red-600">800+ families treated</div>
        </CardContent>
      </Card>
    </div>
  )
}
