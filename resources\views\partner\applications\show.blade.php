@extends('layouts.dashboard')

@section('title', 'Application Details')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Application Details</h1>
                            <p class="text-gray-600">Application ID: {{ $application->application_id }}</p>
                        </div>
                        <div class="flex gap-3">
                            @if($application->status === 'pending')
                                <a href="{{ route('partner.applications.edit', $application) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                    <i class="fas fa-edit mr-2"></i>Edit Application
                                </a>
                            @endif
                            <a href="{{ route('partner.applications.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Applications
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Status Banner -->
                <div class="mb-8">
                    @php
                        $statusConfig = [
                            'pending' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-800', 'icon' => 'fas fa-clock', 'message' => 'Your application is pending review.'],
                            'under_review' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-800', 'icon' => 'fas fa-search', 'message' => 'Your application is currently under review.'],
                            'approved' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'icon' => 'fas fa-check-circle', 'message' => 'Congratulations! Your application has been approved.'],
                            'rejected' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'icon' => 'fas fa-times-circle', 'message' => 'Your application has been rejected.'],
                        ];
                        $config = $statusConfig[$application->status] ?? $statusConfig['pending'];
                    @endphp
                    
                    <div class="{{ $config['bg'] }} {{ $config['text'] }} rounded-2xl p-6">
                        <div class="flex items-center">
                            <i class="{{ $config['icon'] }} text-2xl mr-4"></i>
                            <div>
                                <h3 class="text-lg font-semibold">{{ ucfirst(str_replace('_', ' ', $application->status)) }}</h3>
                                <p class="mt-1">{{ $config['message'] }}</p>
                                @if($application->reviewed_at)
                                    <p class="text-sm mt-2">Reviewed on {{ $application->reviewed_at->format('M d, Y \a\t g:i A') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Main Application Details -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Scholarship Information -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Scholarship Information</h2>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Scholarship Title</label>
                                        <p class="text-gray-900 font-semibold">{{ $application->scholarship->title ?? 'N/A' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {{ ucfirst($application->scholarship->category ?? 'N/A') }}
                                        </span>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                                        <p class="text-gray-900 font-semibold text-lg">₦{{ number_format($application->scholarship->amount ?? 0) }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Application Deadline</label>
                                        <p class="text-gray-900">{{ $application->scholarship->application_deadline ? $application->scholarship->application_deadline->format('M d, Y') : 'N/A' }}</p>
                                    </div>
                                </div>
                                @if($application->scholarship->description)
                                    <div class="mt-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                        <p class="text-gray-600 leading-relaxed">{{ $application->scholarship->description }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Student Information -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Student Information</h2>
                            </div>
                            <div class="p-6">
                                @if($application->student)
                                    <div class="flex items-start space-x-4 mb-6">
                                        @if($application->student->photo)
                                            <img class="h-16 w-16 rounded-full object-cover" src="{{ Storage::url($application->student->photo) }}" alt="{{ $application->student->name }}">
                                        @else
                                            <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-400 text-xl"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">{{ $application->student->name }}</h3>
                                            <p class="text-gray-600">{{ $application->student->class }} • Age {{ $application->student->age }}</p>
                                            <p class="text-gray-600">{{ ucfirst($application->student->gender) }}</p>
                                        </div>
                                    </div>
                                @endif

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Guardian Name</label>
                                        <p class="text-gray-900">{{ $application->guardian_name ?? 'N/A' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Guardian Phone</label>
                                        <p class="text-gray-900">{{ $application->guardian_phone ?? 'N/A' }}</p>
                                    </div>
                                    @if($application->guardian_email)
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Guardian Email</label>
                                            <p class="text-gray-900">{{ $application->guardian_email }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Application Details -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Application Details</h2>
                            </div>
                            <div class="p-6">
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Application</label>
                                    <div class="bg-gray-50 rounded-xl p-4">
                                        <p class="text-gray-900 leading-relaxed">{{ $application->form_data['reason'] ?? 'N/A' }}</p>
                                    </div>
                                </div>

                                @if($application->uploaded_files && isset($application->uploaded_files['supporting_documents']))
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Supporting Documents</label>
                                        <div class="bg-gray-50 rounded-xl p-4">
                                            <a href="{{ Storage::url($application->uploaded_files['supporting_documents']) }}" 
                                               target="_blank"
                                               class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200">
                                                <i class="fas fa-file-alt mr-2"></i>
                                                View Document
                                                <i class="fas fa-external-link-alt ml-2 text-sm"></i>
                                            </a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        @if($application->reviewer_notes)
                            <!-- Reviewer Notes -->
                            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                                <div class="p-6 border-b border-gray-200">
                                    <h2 class="text-xl font-semibold text-gray-900">Reviewer Notes</h2>
                                </div>
                                <div class="p-6">
                                    <div class="bg-gray-50 rounded-xl p-4">
                                        <p class="text-gray-900 leading-relaxed">{{ $application->reviewer_notes }}</p>
                                    </div>
                                    @if($application->reviewer)
                                        <p class="text-sm text-gray-600 mt-3">
                                            Reviewed by {{ $application->reviewer->name }} on {{ $application->reviewed_at->format('M d, Y \a\t g:i A') }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Sidebar Information -->
                    <div class="space-y-6">
                        <!-- Application Summary -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Application Summary</h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Application ID</label>
                                    <p class="text-gray-900 font-mono">{{ $application->application_id }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Submitted Date</label>
                                    <p class="text-gray-900">{{ $application->submitted_at ? $application->submitted_at->format('M d, Y \a\t g:i A') : 'N/A' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Application Type</label>
                                    <p class="text-gray-900">{{ ucfirst($application->application_type) }}</p>
                                </div>
                                @if($application->reviewed_at)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Review Date</label>
                                        <p class="text-gray-900">{{ $application->reviewed_at->format('M d, Y \a\t g:i A') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Actions -->
                        @if($application->status === 'pending')
                            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                                <div class="p-6 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
                                </div>
                                <div class="p-6 space-y-3">
                                    <a href="{{ route('partner.applications.edit', $application) }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300 text-center block">
                                        <i class="fas fa-edit mr-2"></i>Edit Application
                                    </a>
                                    <form action="{{ route('partner.applications.destroy', $application) }}" method="POST" class="w-full">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300"
                                                onclick="return confirm('Are you sure you want to delete this application? This action cannot be undone.')">
                                            <i class="fas fa-trash mr-2"></i>Delete Application
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }
</script>
@endpush
@endsection
