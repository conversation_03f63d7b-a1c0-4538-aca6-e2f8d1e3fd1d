<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScholarshipApplication extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'application_id',
        'scholarship_id',
        'user_id',
        'student_id',
        'school_id',
        'grade_level',
        'academic_year',
        'application_type',
        'progression_from_grade',
        'is_progression_application',
        'guardian_name',
        'guardian_phone',
        'guardian_email',
        'principal_name',
        'principal_phone',
        'principal_email',
        'matriculation_number',
        'institution_name',
        'course_of_study',
        'year_of_study',
        'expected_graduation_year',
        'previous_scholarship_id',
        'renewal_count',
        'application_data',
        'form_data',
        'uploaded_files',
        'status',
        'priority',
        'score',
        'award_amount',
        'review_notes',
        'internal_notes',
        'admin_notes', // Keep for backward compatibility
        'notification_sent',
        'submitted_at',
        'reviewed_at',
        'reviewed_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'application_data' => 'array',
        'form_data' => 'array',
        'uploaded_files' => 'array',
        'score' => 'decimal:2',
        'award_amount' => 'decimal:2',
        'notification_sent' => 'boolean',
        'is_progression_application' => 'boolean',
        'year_of_study' => 'integer',
        'renewal_count' => 'integer',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    /**
     * Boot method to auto-generate application_id
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->application_id)) {
                $model->application_id = static::generateApplicationId();
            }
            if (empty($model->submitted_at)) {
                $model->submitted_at = now();
            }
        });
    }

    /**
     * Generate unique application ID
     */
    public static function generateApplicationId(): string
    {
        $prefix = 'HLTKKQ-SCH-';
        $lastApplication = static::latest('id')->first();
        $nextNumber = $lastApplication ? $lastApplication->id + 1 : 1;
        return $prefix . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the scholarship this application belongs to.
     */
    public function scholarship()
    {
        return $this->belongsTo(Scholarship::class);
    }

    /**
     * Get the user who submitted this application.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who reviewed this application.
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get all files associated with this application.
     */
    public function files()
    {
        return $this->hasMany(ScholarshipApplicationFile::class, 'application_id');
    }

    /**
     * Get the student this application is for (institutional applications)
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the school/partner organization (for Primary/Secondary applications)
     */
    public function school()
    {
        return $this->belongsTo(PartnerOrganization::class, 'school_id');
    }

    /**
     * Get the previous scholarship application (for renewals/progressions)
     */
    public function previousApplication()
    {
        return $this->belongsTo(ScholarshipApplication::class, 'previous_scholarship_id');
    }

    /**
     * Get renewal applications based on this application
     */
    public function renewalApplications()
    {
        return $this->hasMany(ScholarshipApplication::class, 'previous_scholarship_id');
    }

    /**
     * Get verified files only.
     */
    public function verifiedFiles()
    {
        return $this->hasMany(ScholarshipApplicationFile::class, 'application_id')->verified();
    }

    /**
     * Get unverified files only.
     */
    public function unverifiedFiles()
    {
        return $this->hasMany(ScholarshipApplicationFile::class, 'application_id')->unverified();
    }

    /**
     * Check if application is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if application is under review.
     */
    public function isUnderReview(): bool
    {
        return $this->status === 'under_review';
    }

    /**
     * Check if application is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if application is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Scope a query to only include pending applications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include applications under review.
     */
    public function scopeUnderReview($query)
    {
        return $query->where('status', 'under_review');
    }

    /**
     * Scope a query to only include approved applications.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include rejected applications.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Mark application as under review.
     */
    public function markAsUnderReview(User $reviewer): void
    {
        $this->update([
            'status' => 'under_review',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);
    }

    /**
     * Approve the application.
     */
    public function approve(User $reviewer, string $notes = null): void
    {
        $this->update([
            'status' => 'approved',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'admin_notes' => $notes,
        ]);
    }

    /**
     * Reject the application.
     */
    public function reject(User $reviewer, string $notes = null): void
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'admin_notes' => $notes,
        ]);
    }

    /**
     * Check if this is an institutional application
     */
    public function isInstitutional(): bool
    {
        return $this->application_type === 'institutional';
    }

    /**
     * Check if this is an individual application
     */
    public function isIndividual(): bool
    {
        return $this->application_type === 'individual';
    }

    /**
     * Check if this is a progression application
     */
    public function isProgression(): bool
    {
        return $this->is_progression_application;
    }

    /**
     * Check if this is a renewal application
     */
    public function isRenewal(): bool
    {
        return $this->previous_scholarship_id !== null;
    }

    /**
     * Get the grade display format
     */
    public function getGradeDisplayAttribute(): ?string
    {
        if (!$this->grade_level) return null;

        return match($this->grade_level) {
            'primary_1' => 'Primary 1',
            'primary_2' => 'Primary 2',
            'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4',
            'primary_5' => 'Primary 5',
            'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1 (JSS 1)',
            'secondary_2' => 'Secondary 2 (JSS 2)',
            'secondary_3' => 'Secondary 3 (JSS 3)',
            'secondary_4' => 'Secondary 4 (SSS 1)',
            'secondary_5' => 'Secondary 5 (SSS 2)',
            'secondary_6' => 'Secondary 6 (SSS 3)',
            'university_1' => 'University Year 1',
            'university_2' => 'University Year 2',
            'university_3' => 'University Year 3',
            'university_4' => 'University Year 4',
            'university_5' => 'University Year 5',
            'university_6' => 'University Year 6',
            default => ucfirst(str_replace('_', ' ', $this->grade_level))
        };
    }

    /**
     * Scope for institutional applications
     */
    public function scopeInstitutional($query)
    {
        return $query->where('application_type', 'institutional');
    }

    /**
     * Scope for individual applications
     */
    public function scopeIndividual($query)
    {
        return $query->where('application_type', 'individual');
    }

    /**
     * Scope for progression applications
     */
    public function scopeProgression($query)
    {
        return $query->where('is_progression_application', true);
    }

    /**
     * Scope for renewal applications
     */
    public function scopeRenewal($query)
    {
        return $query->whereNotNull('previous_scholarship_id');
    }

    /**
     * Scope for applications by grade level
     */
    public function scopeByGrade($query, $grade)
    {
        return $query->where('grade_level', $grade);
    }

    /**
     * Scope for applications by academic year
     */
    public function scopeByAcademicYear($query, $year)
    {
        return $query->where('academic_year', $year);
    }

    /**
     * Scope for applications by school
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }
}
