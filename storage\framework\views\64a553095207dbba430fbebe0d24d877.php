

<?php $__env->startSection('title', 'Edit Blog Post'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Edit Blog Post</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.blog.index')); ?>">Blog</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.blog.posts')); ?>">Posts</a></li>
                        <li class="breadcrumb-item active">Edit Post #<?php echo e($id); ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>Edit Blog Post Details
                    </h6>
                </div>
                <div class="card-body">
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('admin.blog.posts.update', $id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    <label for="title" class="form-label">Post Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?php echo e(old('title', $post->title)); ?>" required>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3" placeholder="Brief description of the post..."><?php echo e(old('excerpt', $post->excerpt)); ?></textarea>
                                    <div class="form-text">This will be shown in post listings and search results</div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="content" name="content" rows="15" required><?php echo e(old('content', $post->content)); ?></textarea>
                                    <div class="form-text">Write your blog post content here</div>
                                </div>

                                <!-- SEO Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-info">SEO Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="seo_title" class="form-label">SEO Title</label>
                                            <input type="text" class="form-control" id="seo_title" name="seo_title" value="<?php echo e(old('seo_title', $post->seo_title)); ?>" maxlength="60">
                                            <div class="form-text">Recommended: 50-60 characters</div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="seo_description" class="form-label">SEO Description</label>
                                            <textarea class="form-control" id="seo_description" name="seo_description" rows="3" maxlength="160"><?php echo e(old('seo_description', $post->seo_description)); ?></textarea>
                                            <div class="form-text">Recommended: 150-160 characters</div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="tags" class="form-label">Tags</label>
                                            <input type="text" class="form-control" id="tags" name="tags" value="<?php echo e(old('tags', $post->tags)); ?>" placeholder="tag1, tag2, tag3">
                                            <div class="form-text">Separate tags with commas</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publish Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Publish Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="draft" <?php echo e(old('status', $post->status) == 'draft' ? 'selected' : ''); ?>>Draft</option>
                                                <option value="published" <?php echo e(old('status', $post->status) == 'published' ? 'selected' : ''); ?>>Published</option>
                                                <option value="archived" <?php echo e(old('status', $post->status) == 'archived' ? 'selected' : ''); ?>>Archived</option>
                                            </select>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="category_id" class="form-label">Category</label>
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="">Select Category</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $post->category_id) == $category->id ? 'selected' : ''); ?>>
                                                        <?php echo e($category->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label class="form-label">Published Date</label>
                                            <input type="text" class="form-control" value="<?php echo e($post->published_at ? $post->published_at->format('M d, Y \a\t g:i A') : 'Not published yet'); ?>" readonly>
                                            <div class="form-text">Original publish date</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Current Image -->
                                        <div class="current-image mb-3">
                                            <?php if($post->featured_image): ?>
                                                <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="Current featured image" class="img-fluid rounded" style="max-height: 200px;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                                                    <i class="fas fa-image fa-3x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="mt-2">
                                                <small class="text-muted">Current featured image</small>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="featured_image" class="form-label">Replace Image</label>
                                            <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                                            <div class="form-text">Upload a new featured image (JPG, PNG, GIF - Max: 2MB)</div>
                                        </div>
                                        
                                        <div id="imagePreview" style="display: none;">
                                            <img id="preview" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                            <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removePreview()">Remove</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Post Statistics -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-info">Post Statistics</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h4 class="text-primary">1,234</h4>
                                                <small class="text-muted">Views</small>
                                            </div>
                                            <div class="col-6">
                                                <h4 class="text-success">56</h4>
                                                <small class="text-muted">Likes</small>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h5 class="text-warning">23</h5>
                                                <small class="text-muted">Comments</small>
                                            </div>
                                            <div class="col-6">
                                                <h5 class="text-info">89</h5>
                                                <small class="text-muted">Shares</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary" name="action" value="update">
                                                <i class="fas fa-save me-2"></i>Update Post
                                            </button>
                                            <a href="/blog/<?php echo e($post->slug); ?>" target="_blank" class="btn btn-info">
                                                <i class="fas fa-eye me-2"></i>Preview Post
                                            </a>
                                            <a href="<?php echo e(route('admin.blog.posts')); ?>" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-2"></i>Back to Posts
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('featured_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });
});

function removePreview() {
    document.getElementById('featured_image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/blog/posts/edit.blade.php ENDPATH**/ ?>