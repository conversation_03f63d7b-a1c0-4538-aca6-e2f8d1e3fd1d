<?php

if (!function_exists('formatCurrency')) {
    /**
     * Format currency for display
     */
    function formatCurrency($amount, $currency = 'NGN')
    {
        return '₦' . number_format($amount, 2);
    }
}

if (!function_exists('frontendUrl')) {
    /**
     * Get the frontend URL for public pages
     */
    function frontendUrl($path = '')
    {
        $baseUrl = config('app.frontend_url', env('FRONTEND_URL', 'http://localhost:3000'));
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}

if (!function_exists('backendUrl')) {
    /**
     * Get the backend URL for admin/dashboard pages
     */
    function backendUrl($path = '')
    {
        $baseUrl = config('app.backend_url', env('BACKEND_URL', 'http://localhost:8000'));
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}
