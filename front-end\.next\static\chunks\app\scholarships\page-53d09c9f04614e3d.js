(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[894],{1103:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(5155),a=r(2115),l=r(6874),n=r.n(l),i=r(3194),d=r(8482),o=r(7168),c=r(8145),x=r(5040),m=r(3896),u=r(7949),h=r(5339),g=r(7924),f=r(1482),p=r(2138),b=r(9852),v=r(5784),N=r(1886);let j={primary:x.A,secondary:m.A,university:u.A},y={primary:"bg-blue-100 text-blue-800 border-blue-200",secondary:"bg-green-100 text-green-800 border-green-200",university:"bg-purple-100 text-purple-800 border-purple-200"};function w(){let[e,s]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),[m,u]=(0,a.useState)(null),[w,k]=(0,a.useState)(""),[C,A]=(0,a.useState)("all"),[S,R]=(0,a.useState)("all");(0,a.useEffect)(()=>{E()},[]);let E=async()=>{try{l(!0);let r=await N.uE.get("/public-scholarships");if(r.success){var e;let t=(null===(e=r.data)||void 0===e?void 0:e.data)||r.data||[];s(Array.isArray(t)?t:[])}else u(r.message||"Failed to fetch scholarships")}catch(e){console.error("Error fetching scholarships:",e),u("Failed to load scholarships. Please try again later.")}finally{l(!1)}},D=(Array.isArray(e)?e:[]).filter(e=>{let s=e.title.toLowerCase().includes(w.toLowerCase())||e.description.toLowerCase().includes(w.toLowerCase()),r="all"===C||e.category===C,t="all"===S||e.status===S;return s&&r&&t}),F=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),_=e=>new Date(e)<new Date,$=e=>{let s=new Date(e),r=new Date;return Math.ceil((s.getTime()-r.getTime())/864e5)};return r?(0,t.jsx)("div",{className:"flex min-h-screen flex-col bg-pattern",children:(0,t.jsx)("main",{className:"flex-1",children:(0,t.jsx)("div",{className:"container py-20",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-muted-foreground",children:"Loading scholarships..."})]})})})}):m?(0,t.jsx)("div",{className:"flex min-h-screen flex-col bg-pattern",children:(0,t.jsx)("main",{className:"flex-1",children:(0,t.jsx)("div",{className:"container py-20",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(h.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Error Loading Scholarships"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-6",children:m}),(0,t.jsx)(o.$,{onClick:E,className:"bg-green-600 hover:bg-green-700",children:"Try Again"})]})})})}):(0,t.jsx)("div",{className:"flex min-h-screen flex-col bg-pattern",children:(0,t.jsxs)("main",{className:"flex-1",children:[(0,t.jsxs)("section",{className:"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-grid opacity-20"}),(0,t.jsx)("div",{className:"container relative",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto text-center space-y-6",children:[(0,t.jsx)(c.E,{className:"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2",children:"Scholarships"}),(0,t.jsx)("h1",{className:"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up",children:"Educational Scholarships"}),(0,t.jsx)("p",{className:"text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto",style:{animationDelay:"0.2s"},children:"Empowering students through accessible education funding opportunities across Nigeria"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-12 animate-fade-in-up",style:{animationDelay:"0.4s"},children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-amber-400",children:D.length}),(0,t.jsx)("div",{className:"text-green-200 text-sm",children:"Available Scholarships"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-amber-400",children:"3"}),(0,t.jsx)("div",{className:"text-green-200 text-sm",children:"Categories"})]})]})]})})]}),(0,t.jsx)(i.$,{className:"py-12 bg-green-50 dark:bg-green-950/20",children:(0,t.jsxs)("div",{className:"container",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold tracking-tight mb-4",children:"Find Your Perfect Scholarship"}),(0,t.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Use our filters to discover scholarships that match your educational level and goals"})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-center justify-center max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"flex-1 max-w-md",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(b.p,{placeholder:"Search scholarships...",value:w,onChange:e=>k(e.target.value),className:"pl-10 neumorphic border-green-200 focus:border-green-400"})]})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)(v.l6,{value:C,onValueChange:A,children:[(0,t.jsxs)(v.bq,{className:"w-[180px] neumorphic border-green-200",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)(v.yv,{placeholder:"Category"})]}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"all",children:"All Categories"}),(0,t.jsx)(v.eb,{value:"primary",children:"Primary"}),(0,t.jsx)(v.eb,{value:"secondary",children:"Secondary"}),(0,t.jsx)(v.eb,{value:"university",children:"University"})]})]}),(0,t.jsxs)(v.l6,{value:S,onValueChange:R,children:[(0,t.jsx)(v.bq,{className:"w-[180px] neumorphic border-green-200",children:(0,t.jsx)(v.yv,{placeholder:"Status"})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"all",children:"All Status"}),(0,t.jsx)(v.eb,{value:"active",children:"Active"}),(0,t.jsx)(v.eb,{value:"closed",children:"Closed"})]})]})]})]})]})}),(0,t.jsx)(i.$,{className:"py-20 bg-background",children:(0,t.jsx)("div",{className:"container",children:0===D.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(x.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No Scholarships Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:w||"all"!==C||"all"!==S?"Try adjusting your search criteria or filters.":"No scholarships are currently available."}),(w||"all"!==C||"all"!==S)&&(0,t.jsx)(o.$,{onClick:()=>{k(""),A("all"),R("all")},variant:"outline",className:"border-green-200 text-green-700 hover:bg-green-50",children:"Clear Filters"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold tracking-tight sm:text-4xl mb-4",children:"Available Scholarships"}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg max-w-2xl mx-auto",children:"Discover opportunities that match your educational goals and aspirations"}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)(c.E,{className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full text-lg px-4 py-2",children:[D.length," ",1===D.length?"Scholarship":"Scholarships"," Available"]})})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:D.map(e=>{let s=j[e.category],r=_(e.application_deadline),a=$(e.application_deadline),l=e.max_applicants?e.max_applicants-e.current_applicants:null;return(0,t.jsx)(d.Zp,{className:"overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1",children:(0,t.jsxs)(d.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,t.jsx)("div",{className:"h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:(0,t.jsx)(s,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-green-800 dark:text-green-200 mb-1",children:e.title}),(0,t.jsx)(c.E,{className:"".concat(y[e.category]," text-xs font-medium rounded-full"),children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:F(e.amount)}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Amount"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a>0?a:0}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Days Left"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:null!==l?l>0?l:0:"∞"}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Spots Left"})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,t.jsx)("span",{children:"Application Status"}),(0,t.jsx)("span",{className:r?"text-red-600":"text-green-600",children:r?"Closed":"Open"})]}),(0,t.jsx)("div",{className:"h-2 bg-gray-200 rounded-full overflow-hidden",children:(0,t.jsx)("div",{className:"h-full transition-all duration-300 ".concat(r?"bg-red-500":"bg-green-500"),style:{width:r?"100%":"".concat(Math.max(10,100-a/30*100),"%")}})})]}),a<=7&&a>0&&!r&&(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)("span",{className:"text-sm font-medium text-orange-800",children:["Only ",a," ",1===a?"day":"days"," left to apply!"]})]})}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(n(),{href:"/scholarships/".concat(e.id),className:"flex-1",children:(0,t.jsx)(o.$,{variant:"outline",className:"w-full border-green-200 text-green-700 hover:bg-green-50",children:"View Details"})}),(0,t.jsx)(o.$,{className:"w-full bg-green-600 hover:bg-green-700 flex-1",disabled:r||null!==l&&l<=0,onClick:()=>{window.location.href="".concat("https://halimakqfoundation.fnskills.ng","/login")},children:r?"Expired":null!==l&&l<=0?"Full":"Apply Now"})]})]})},e.id)})})]})})}),(0,t.jsx)(i.$,{className:"py-20 bg-gradient-to-br from-green-600 to-green-800 text-white",children:(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold tracking-tight sm:text-4xl mb-6",children:"Ready to Start Your Educational Journey?"}),(0,t.jsx)("p",{className:"text-xl text-green-100 mb-8",children:"Don't miss out on these life-changing opportunities. Apply today and take the first step towards your dreams."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(o.$,{size:"lg",className:"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold",onClick:()=>{window.location.href="".concat("https://halimakqfoundation.fnskills.ng","/login")},children:["Start Application",(0,t.jsx)(p.A,{className:"ml-2 h-5 w-5"})]}),(0,t.jsx)(n(),{href:"/contact",children:(0,t.jsx)(o.$,{size:"lg",variant:"outline",className:"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold",children:"Need Help?"})})]})]})})})]})})}},3194:(e,s,r)=>{"use strict";r.d(s,{$:()=>n});var t=r(5155),a=r(2115),l=r(3096);function n(e){let{children:s,className:r="",id:n}=e,[i,d]=(0,a.useState)(!1),{ref:o,inView:c}=(0,l.Wx)({threshold:.1,triggerOnce:!0});return(0,a.useEffect)(()=>{c&&!i&&d(!0)},[c,i]),(0,t.jsx)("section",{ref:o,id:n,className:"".concat(r," transition-opacity duration-500 ").concat(i?"opacity-100 animate-fade-in-up":"opacity-0"),children:s})}},3999:(e,s,r)=>{"use strict";r.d(s,{cn:()=>l});var t=r(2596),a=r(9688);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}},5017:(e,s,r)=>{Promise.resolve().then(r.bind(r,1103))},5784:(e,s,r)=>{"use strict";r.d(s,{bq:()=>m,eb:()=>f,gC:()=>g,l6:()=>c,yv:()=>x});var t=r(5155),a=r(2115),l=r(8715),n=r(6474),i=r(7863),d=r(5196),o=r(3999);let c=l.bL;l.YJ;let x=l.WT,m=a.forwardRef((e,s)=>{let{className:r,children:a,...i}=e;return(0,t.jsxs)(l.l9,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[a,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let u=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(l.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(l.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let g=a.forwardRef((e,s)=>{let{className:r,children:a,position:n="popper",...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...i,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(h,{})]})})});g.displayName=l.UC.displayName,a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(l.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=l.JU.displayName;let f=a.forwardRef((e,s)=>{let{className:r,children:a,...n}=e;return(0,t.jsxs)(l.q7,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:a})]})});f.displayName=l.q7.displayName,a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(l.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=l.wv.displayName},7168:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(5155),a=r(2115),l=r(9708),n=r(2085),i=r(3999);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,s)=>{let{className:r,variant:a,size:n,asChild:o=!1,...c}=e,x=o?l.DX:"button";return(0,t.jsx)(x,{className:(0,i.cn)(d({variant:a,size:n,className:r})),ref:s,suppressHydrationWarning:!0,...c})});o.displayName="Button"},8145:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(5155);r(2115);var a=r(2085),l=r(3999);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)(n({variant:r}),s),...a})}},8482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var t=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},9852:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,s)=>{let{className:r,type:a,...n}=e;return(0,t.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:s,suppressHydrationWarning:!0,...n})});n.displayName="Input"}},e=>{var s=s=>e(e.s=s);e.O(0,[598,874,114,461,558,886,441,684,358],()=>s(5017)),_N_E=e.O()}]);