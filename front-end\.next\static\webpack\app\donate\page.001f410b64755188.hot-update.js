"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/bank-transfer-receipt.tsx":
/*!**********************************************!*\
  !*** ./components/bank-transfer-receipt.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BankTransferReceipt: () => (/* binding */ BankTransferReceipt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ BankTransferReceipt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BankTransferReceipt(param) {\n    let { donationId, onSuccess, onError } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sender_name: '',\n        sender_bank: ''\n    });\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadSuccess, setUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadError, setUploadError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleFileSelect = (file)=>{\n        // Validate file type\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/jpg',\n            'application/pdf'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setUploadError('Please select a valid file (JPG, PNG, or PDF)');\n            return;\n        }\n        // Validate file size (5MB max)\n        if (file.size > 5 * 1024 * 1024) {\n            setUploadError('File size must be less than 5MB');\n            return;\n        }\n        setSelectedFile(file);\n        setUploadError(null);\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        var _e_dataTransfer_files;\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        const file = (_e_dataTransfer_files = e.dataTransfer.files) === null || _e_dataTransfer_files === void 0 ? void 0 : _e_dataTransfer_files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedFile) {\n            setUploadError('Please select a receipt file');\n            return;\n        }\n        if (!formData.sender_name || !formData.sender_bank) {\n            setUploadError('Please fill in all required fields');\n            return;\n        }\n        setIsUploading(true);\n        setUploadError(null);\n        try {\n            const uploadFormData = new FormData();\n            uploadFormData.append('receipt', selectedFile);\n            uploadFormData.append('sender_name', formData.sender_name);\n            uploadFormData.append('sender_bank', formData.sender_bank);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.uploadDonationReceipt(donationId, uploadFormData);\n            if (response.success) {\n                setUploadSuccess(true);\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setUploadError(response.message || 'Failed to upload receipt');\n                onError === null || onError === void 0 ? void 0 : onError(response.message || 'Failed to upload receipt');\n            }\n        } catch (error) {\n            console.error('Receipt upload error:', error);\n            const errorMessage = 'Failed to upload receipt. Please try again.';\n            setUploadError(errorMessage);\n            onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    if (uploadSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"border-green-200 bg-green-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-12 w-12 text-green-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-green-700 mb-2\",\n                        children: \"Receipt Uploaded Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-600 text-sm mb-4\",\n                        children: \"Your receipt has been uploaded and is now pending admin verification. You will receive an email confirmation once your donation is approved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Back to Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            \"Upload Bank Transfer Receipt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Please upload your bank transfer receipt and provide the required details for verification.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        uploadError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-red-600\",\n                                    children: uploadError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"sender-name\",\n                                            children: [\n                                                \"Sender Name \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 56\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"sender-name\",\n                                            placeholder: \"Name on the bank account\",\n                                            value: formData.sender_name,\n                                            onChange: (e)=>handleInputChange('sender_name', e.target.value),\n                                            className: \"mt-1\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"sender-bank\",\n                                            children: [\n                                                \"Sender Bank \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 56\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"sender-bank\",\n                                            placeholder: \"Bank used for transfer\",\n                                            value: formData.sender_bank,\n                                            onChange: (e)=>handleInputChange('sender_bank', e.target.value),\n                                            className: \"mt-1\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    children: [\n                                        \"Receipt File \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 border-2 border-dashed rounded-lg p-6 text-center transition-colors \".concat(dragActive ? 'border-green-400 bg-green-50' : selectedFile ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'),\n                                    onDragEnter: handleDrag,\n                                    onDragLeave: handleDrag,\n                                    onDragOver: handleDrag,\n                                    onDrop: handleDrop,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \".jpg,.jpeg,.png,.pdf\",\n                                            onChange: handleFileChange,\n                                            className: \"hidden\",\n                                            id: \"receipt-upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-700\",\n                                                    children: selectedFile.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600\",\n                                                    children: [\n                                                        (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                        \" MB\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('receipt-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                    },\n                                                    children: \"Change File\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8 text-gray-400 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"receipt-upload\",\n                                                                    className: \"cursor-pointer text-green-600 hover:text-green-500\",\n                                                                    children: \"Click to upload\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                ' ',\n                                                                \"or drag and drop\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"JPG, PNG or PDF (max 5MB)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"submit\",\n                            disabled: isUploading || !selectedFile || !formData.sender_name || !formData.sender_bank,\n                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                            children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Uploading Receipt...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Upload Receipt\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground text-center\",\n                            children: \"Your receipt will be reviewed by our admin team. You will receive an email confirmation once verified.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(BankTransferReceipt, \"RHGvAwhjm4rZ5St4aQOduj0IKQM=\");\n_c = BankTransferReceipt;\nvar _c;\n$RefreshReg$(_c, \"BankTransferReceipt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/bank-transfer-receipt.tsx\n"));

/***/ })

});