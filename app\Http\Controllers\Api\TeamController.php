<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

/**
 * @OA\Tag(
 *     name="Team",
 *     description="Team member management endpoints"
 * )
 */
class TeamController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/team",
     *     summary="Get all active team members",
     *     tags={"Team"},
     *     @OA\Parameter(
     *         name="department",
     *         in="query",
     *         description="Filter by department",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="position",
     *         in="query",
     *         description="Filter by position",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Team members retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="team_members", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="first_name", type="string"),
     *                     @OA\Property(property="last_name", type="string"),
     *                     @OA\Property(property="position", type="string"),
     *                     @OA\Property(property="department", type="string"),
     *                     @OA\Property(property="bio", type="string"),
     *                     @OA\Property(property="photo_url", type="string"),
     *                     @OA\Property(property="linkedin_url", type="string"),
     *                     @OA\Property(property="twitter_url", type="string"),
     *                     @OA\Property(property="display_order", type="integer")
     *                 )
     *             ),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = TeamMember::where('is_active', true);

        // Filter by department
        if ($request->has('department') && $request->department) {
            $query->where('department', $request->department);
        }

        // Filter by position
        if ($request->has('position') && $request->position) {
            $query->where('position', 'like', '%' . $request->position . '%');
        }

        $teamMembers = $query->orderBy('sort_order')
                            ->orderBy('name')
                            ->get();

        return response()->json([
            'team_members' => $teamMembers->map(function ($member) {
                return [
                    'id' => $member->id,
                    'name' => $member->name,
                    'full_name' => $member->full_name,
                    'position' => $member->position,
                    'department' => $member->department,
                    'bio' => $member->bio,
                    'photo_url' => $member->photo_url,
                    'linkedin_url' => $member->linkedin_url,
                    'twitter_url' => $member->twitter_url,
                    'facebook_url' => $member->facebook_url,
                    'sort_order' => $member->sort_order,
                    'is_active' => $member->is_active
                ];
            }),
            'success' => true
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/team/{id}",
     *     summary="Get specific team member",
     *     tags={"Team"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Team member ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Team member retrieved successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Team member not found"
     *     )
     * )
     */
    public function show($id)
    {
        $teamMember = TeamMember::where('id', $id)
                               ->where('is_active', true)
                               ->first();

        if (!$teamMember) {
            return response()->json([
                'message' => 'Team member not found',
                'success' => false
            ], 404);
        }

        return response()->json([
            'team_member' => [
                'id' => $teamMember->id,
                'first_name' => $teamMember->first_name,
                'last_name' => $teamMember->last_name,
                'full_name' => $teamMember->full_name,
                'position' => $teamMember->position,
                'department' => $teamMember->department,
                'bio' => $teamMember->bio,
                'photo_url' => $teamMember->photo_url,
                'linkedin_url' => $teamMember->linkedin_url,
                'twitter_url' => $teamMember->twitter_url,
                'facebook_url' => $teamMember->facebook_url,
                'instagram_url' => $teamMember->instagram_url,
                'display_order' => $teamMember->display_order,
                'joined_date' => $teamMember->joined_date?->format('Y-m-d'),
                'achievements' => $teamMember->achievements,
                'education' => $teamMember->education,
                'experience' => $teamMember->experience
            ],
            'success' => true
        ]);
    }

    /**
     * Admin: Get all team members (including inactive)
     */
    public function adminIndex(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $department = $request->get('department');
        $status = $request->get('status'); // active, inactive, all

        $query = TeamMember::query();

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        if ($department) {
            $query->where('department', $department);
        }

        if ($status && $status !== 'all') {
            $query->where('is_active', $status === 'active');
        }

        $teamMembers = $query->orderBy('sort_order')
                            ->orderBy('name')
                            ->paginate($perPage);

        return response()->json([
            'team_members' => $teamMembers,
            'success' => true
        ]);
    }

    /**
     * Admin: Create new team member
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'bio' => 'required|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'joined_date' => 'nullable|date',
            'display_order' => 'nullable|integer|min:0',
            'achievements' => 'nullable|string',
            'education' => 'nullable|string',
            'experience' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $teamMember = TeamMember::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'position' => $request->position,
            'department' => $request->department,
            'bio' => $request->bio,
            'email' => $request->email,
            'phone' => $request->phone,
            'linkedin_url' => $request->linkedin_url,
            'twitter_url' => $request->twitter_url,
            'facebook_url' => $request->facebook_url,
            'instagram_url' => $request->instagram_url,
            'joined_date' => $request->joined_date,
            'display_order' => $request->display_order ?? 0,
            'achievements' => $request->achievements,
            'education' => $request->education,
            'experience' => $request->experience,
            'is_active' => $request->is_active ?? true
        ]);

        return response()->json([
            'message' => 'Team member created successfully',
            'team_member' => $teamMember,
            'success' => true
        ], 201);
    }

    /**
     * Admin: Update team member
     */
    public function update(Request $request, $id)
    {
        $teamMember = TeamMember::find($id);

        if (!$teamMember) {
            return response()->json([
                'message' => 'Team member not found',
                'success' => false
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'position' => 'sometimes|required|string|max:255',
            'department' => 'sometimes|required|string|max:255',
            'bio' => 'sometimes|required|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'joined_date' => 'nullable|date',
            'display_order' => 'nullable|integer|min:0',
            'achievements' => 'nullable|string',
            'education' => 'nullable|string',
            'experience' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $teamMember->update($request->only([
            'first_name', 'last_name', 'position', 'department', 'bio',
            'email', 'phone', 'linkedin_url', 'twitter_url', 'facebook_url',
            'instagram_url', 'joined_date', 'display_order', 'achievements',
            'education', 'experience', 'is_active'
        ]));

        return response()->json([
            'message' => 'Team member updated successfully',
            'team_member' => $teamMember,
            'success' => true
        ]);
    }

    /**
     * Admin: Delete team member
     */
    public function destroy($id)
    {
        $teamMember = TeamMember::find($id);

        if (!$teamMember) {
            return response()->json([
                'message' => 'Team member not found',
                'success' => false
            ], 404);
        }

        // Delete photo if exists
        if ($teamMember->photo_path) {
            Storage::delete($teamMember->photo_path);
        }

        $teamMember->delete();

        return response()->json([
            'message' => 'Team member deleted successfully',
            'success' => true
        ]);
    }

    /**
     * Admin: Upload team member photo
     */
    public function uploadPhoto(Request $request, $id)
    {
        $teamMember = TeamMember::find($id);

        if (!$teamMember) {
            return response()->json([
                'message' => 'Team member not found',
                'success' => false
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        try {
            // Delete old photo if exists
            if ($teamMember->photo_path) {
                Storage::delete($teamMember->photo_path);
            }

            $image = $request->file('photo');
            $filename = 'team_' . $teamMember->id . '_' . time() . '.jpg';
            
            // Resize and optimize image
            $processedImage = Image::make($image)
                                  ->fit(400, 400)
                                  ->encode('jpg', 85);

            $path = 'team/' . $filename;
            Storage::put($path, $processedImage);

            $teamMember->update([
                'photo_path' => $path,
                'photo_url' => Storage::url($path)
            ]);

            return response()->json([
                'message' => 'Photo uploaded successfully',
                'photo_url' => $teamMember->photo_url,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to upload photo: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
} 