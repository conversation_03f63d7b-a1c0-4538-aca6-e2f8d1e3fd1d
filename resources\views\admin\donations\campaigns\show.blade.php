@extends('layouts.admin')

@section('title', 'Campaign Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Campaign Details</h1>
        <div>
            <a href="{{ route('admin.donations.campaigns') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Campaigns
            </a>
            <a href="{{ route('admin.donations.campaigns.edit', $campaign->id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Campaign
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Campaign Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h4 class="text-primary">{{ $campaign->title }}</h4>
                        @if($campaign->category)
                            <span class="badge bg-secondary mb-3">{{ ucfirst($campaign->category) }}</span>
                        @endif
                        @if($campaign->featured)
                            <span class="badge bg-warning mb-3">Featured</span>
                        @endif
                    </div>

                    @if($campaign->image)
                    <div class="mb-4">
                        <img src="{{ asset('storage/' . $campaign->image) }}" alt="{{ $campaign->title }}" class="img-fluid rounded" style="max-height: 300px;">
                    </div>
                    @endif

                    <div class="mb-4">
                        <h6 class="text-muted">Description</h6>
                        <p class="text-justify">{{ $campaign->description }}</p>
                    </div>

                    <!-- Goal Progress -->
                    <div class="mb-4">
                        <h6 class="text-muted">Fundraising Progress</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span>₦{{ number_format($campaign->raised_amount, 2) }} raised</span>
                            <span>₦{{ number_format($campaign->goal_amount, 2) }} goal</span>
                        </div>
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ $campaign->progress_percentage }}%"></div>
                        </div>
                        <small class="text-muted">{{ number_format($campaign->progress_percentage, 1) }}% of goal reached</small>
                    </div>

                    <!-- Timeline -->
                    @if($campaign->start_date || $campaign->end_date)
                    <div class="mb-4">
                        <h6 class="text-muted">Campaign Timeline</h6>
                        <div class="row">
                            @if($campaign->start_date)
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-success me-2"></i>
                                    <div>
                                        <small class="text-muted">Start Date</small>
                                        <p class="mb-0">{{ $campaign->start_date->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @if($campaign->end_date)
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-stop-circle text-danger me-2"></i>
                                    <div>
                                        <small class="text-muted">End Date</small>
                                        <p class="mb-0">{{ $campaign->end_date->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Recent Donations Card -->
            @if($campaign->donations && $campaign->donations->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Donations</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Donor</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($campaign->donations->take(10) as $donation)
                                <tr>
                                    <td>
                                        @if($donation->user)
                                            {{ $donation->user->first_name }} {{ $donation->user->last_name }}
                                        @else
                                            {{ $donation->donor_name ?? 'Anonymous' }}
                                        @endif
                                    </td>
                                    <td>₦{{ number_format($donation->amount, 2) }}</td>
                                    <td>{{ $donation->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $donation->status === 'completed' ? 'success' : ($donation->status === 'failed' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($donation->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @if($campaign->donations->count() > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.donations.index') }}?campaign={{ $campaign->id }}" class="btn btn-outline-primary btn-sm">
                            View All {{ $campaign->donations->count() }} Donations
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Status</h6>
                </div>
                <div class="card-body text-center">
                    <span class="badge bg-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'completed' ? 'primary' : ($campaign->status === 'paused' ? 'warning' : 'secondary')) }} p-3 mb-3" style="font-size: 1.2rem;">
                        <i class="fas fa-{{ $campaign->status === 'active' ? 'play' : ($campaign->status === 'completed' ? 'check' : ($campaign->status === 'paused' ? 'pause' : 'file')) }} me-2"></i>
                        {{ ucfirst($campaign->status) }}
                    </span>
                    <p class="text-muted mb-0">Current campaign status</p>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary">{{ $campaign->donations ? $campaign->donations->count() : 0 }}</h4>
                            <small class="text-muted">Total Donations</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-success">₦{{ number_format($campaign->raised_amount, 0) }}</h5>
                                <small class="text-muted">Raised</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-info">₦{{ number_format($campaign->goal_amount - $campaign->raised_amount, 0) }}</h5>
                            <small class="text-muted">Remaining</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Campaign ID</small>
                        <p class="mb-0">#{{ $campaign->id }}</p>
                    </div>
                    @if($campaign->slug)
                    <div class="mb-3">
                        <small class="text-muted">Slug</small>
                        <p class="mb-0">{{ $campaign->slug }}</p>
                    </div>
                    @endif
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0">{{ $campaign->created_at->format('F j, Y g:i A') }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0">{{ $campaign->updated_at->format('F j, Y g:i A') }}</p>
                    </div>
                    @if($campaign->creator)
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0">{{ $campaign->creator->first_name }} {{ $campaign->creator->last_name }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.donations.campaigns.edit', $campaign->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Campaign
                        </a>
                        @if($campaign->donations && $campaign->donations->count() > 0)
                        <a href="{{ route('admin.donations.index') }}?campaign={{ $campaign->id }}" class="btn btn-outline-info">
                            <i class="fas fa-list me-2"></i>View Donations
                        </a>
                        @endif
                        @if($campaign->status === 'draft')
                        <form method="POST" action="{{ route('admin.donations.campaigns.update', $campaign->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $campaign->title }}">
                            <input type="hidden" name="description" value="{{ $campaign->description }}">
                            <input type="hidden" name="goal_amount" value="{{ $campaign->goal_amount }}">
                            <input type="hidden" name="category" value="{{ $campaign->category }}">
                            <input type="hidden" name="start_date" value="{{ $campaign->start_date ? $campaign->start_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="end_date" value="{{ $campaign->end_date ? $campaign->end_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="status" value="active">
                            @if($campaign->featured)
                            <input type="hidden" name="featured" value="1">
                            @endif
                            <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to publish this campaign?')">
                                <i class="fas fa-rocket me-2"></i>Publish Campaign
                            </button>
                        </form>
                        @endif
                        @if($campaign->status === 'active')
                        <form method="POST" action="{{ route('admin.donations.campaigns.update', $campaign->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $campaign->title }}">
                            <input type="hidden" name="description" value="{{ $campaign->description }}">
                            <input type="hidden" name="goal_amount" value="{{ $campaign->goal_amount }}">
                            <input type="hidden" name="category" value="{{ $campaign->category }}">
                            <input type="hidden" name="start_date" value="{{ $campaign->start_date ? $campaign->start_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="end_date" value="{{ $campaign->end_date ? $campaign->end_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="status" value="paused">
                            @if($campaign->featured)
                            <input type="hidden" name="featured" value="1">
                            @endif
                            <button type="submit" class="btn btn-outline-warning w-100" onclick="return confirm('Are you sure you want to pause this campaign?')">
                                <i class="fas fa-pause me-2"></i>Pause Campaign
                            </button>
                        </form>
                        @endif
                        @if($campaign->status === 'paused')
                        <form method="POST" action="{{ route('admin.donations.campaigns.update', $campaign->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $campaign->title }}">
                            <input type="hidden" name="description" value="{{ $campaign->description }}">
                            <input type="hidden" name="goal_amount" value="{{ $campaign->goal_amount }}">
                            <input type="hidden" name="category" value="{{ $campaign->category }}">
                            <input type="hidden" name="start_date" value="{{ $campaign->start_date ? $campaign->start_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="end_date" value="{{ $campaign->end_date ? $campaign->end_date->format('Y-m-d') : '' }}">
                            <input type="hidden" name="status" value="active">
                            @if($campaign->featured)
                            <input type="hidden" name="featured" value="1">
                            @endif
                            <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to resume this campaign?')">
                                <i class="fas fa-play me-2"></i>Resume Campaign
                            </button>
                        </form>
                        @endif
                        <form method="POST" action="{{ route('admin.donations.campaigns.destroy', $campaign->id) }}" style="display: inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this campaign? This action cannot be undone.')">
                                <i class="fas fa-trash me-2"></i>Delete Campaign
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
