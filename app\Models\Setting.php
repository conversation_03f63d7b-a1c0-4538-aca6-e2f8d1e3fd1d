<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'group',
        'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get image URL that works with CORS for frontend
     */
    protected static function getImageUrl(string $path): string
    {
        // Convert storage path to API route for CORS support
        if (strpos($path, 'logos/') === 0 || strpos($path, 'avatars/') === 0) {
            return url("/api/v1/images/{$path}");
        }
        
        // For default images, use regular asset
        return asset('storage/' . $path);
    }

    /**
     * Get all settings as key-value array
     */
    public static function getAll(): array
    {
        return Cache::remember('settings.all', 3600, function () {
            $settings = static::all();
            $result = [];

            foreach ($settings as $setting) {
                switch ($setting->type) {
                    case 'boolean':
                        $result[$setting->key] = filter_var($setting->value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'json':
                        $result[$setting->key] = json_decode($setting->value, true);
                        break;
                    case 'image':
                        $result[$setting->key] = $setting->value ? static::getImageUrl($setting->value) : null;
                        break;
                    default:
                        $result[$setting->key] = $setting->value;
                }
            }

            return $result;
        });
    }

    /**
     * Get a setting value by key (alias for get method)
     */
    public static function getValue(string $key, $default = null)
    {
        return static::get($key, $default);
    }

    /**
     * Set a setting value (alias for set method)
     */
    public static function setValue(string $key, $value, string $type = null): bool
    {
        // Auto-detect type if not provided
        if ($type === null) {
            if (is_bool($value)) {
                $type = 'boolean';
            } elseif (is_array($value) || is_object($value)) {
                $type = 'json';
            } elseif (filter_var($value, FILTER_VALIDATE_URL) && strpos($value, '/storage/') !== false) {
                $type = 'image';
            } else {
                $type = 'text';
            }
        }

        return static::set($key, $value, $type);
    }

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("setting.{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }

            // Handle different types
            switch ($setting->type) {
                case 'boolean':
                    return filter_var($setting->value, FILTER_VALIDATE_BOOLEAN);
                case 'json':
                    return json_decode($setting->value, true);
                case 'image':
                    return $setting->value ? static::getImageUrl($setting->value) : $default;
                default:
                    return $setting->value ?: $default;
            }
        });
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, string $type = 'text'): bool
    {
        // Handle different types
        if ($type === 'json') {
            $value = json_encode($value);
        } elseif ($type === 'boolean') {
            $value = $value ? '1' : '0';
        }

        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
            ]
        );

        // Clear cache
        Cache::forget("setting.{$key}");
        Cache::forget('settings.all');
        Cache::forget('settings.public');

        return $setting->wasRecentlyCreated || $setting->wasChanged();
    }

    /**
     * Get all public settings (for frontend)
     */
    public static function getPublicSettings(): array
    {
        return Cache::remember('settings.public', 3600, function () {
            $settings = static::where('is_public', true)->get();
            $result = [];

            foreach ($settings as $setting) {
                switch ($setting->type) {
                    case 'boolean':
                        $result[$setting->key] = filter_var($setting->value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'json':
                        $result[$setting->key] = json_decode($setting->value, true);
                        break;
                    case 'image':
                        $result[$setting->key] = $setting->value ? static::getImageUrl($setting->value) : null;
                        break;
                    default:
                        $result[$setting->key] = $setting->value;
                }
            }

            return $result;
        });
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        Cache::forget('settings.all');
        Cache::forget('settings.public');
        // Clear individual setting caches
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("setting.{$key}");
        }
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            // Clear cache when setting is saved
            Cache::forget("setting.{$setting->key}");
            Cache::forget('settings.all');
            Cache::forget('settings.public');
        });

        static::deleted(function ($setting) {
            // Clear cache when setting is deleted
            Cache::forget("setting.{$setting->key}");
            Cache::forget('settings.all');
            Cache::forget('settings.public');
        });
    }
}
