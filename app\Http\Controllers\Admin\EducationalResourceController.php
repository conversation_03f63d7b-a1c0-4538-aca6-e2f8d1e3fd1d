<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EducationalResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class EducationalResourceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $resources = EducationalResource::with('creator')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $stats = [
            'total' => EducationalResource::count(),
            'active' => EducationalResource::where('is_active', true)->count(),
            'featured' => EducationalResource::where('is_featured', true)->count(),
            'study_guides' => EducationalResource::where('type', 'study_guide')->count(),
            'video_courses' => EducationalResource::where('type', 'video_course')->count(),
            'documents' => EducationalResource::where('type', 'document')->count(),
        ];

        return view('admin.educational-resources.index', compact('resources', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.educational-resources.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:study_guide,video_course,document,external_link,interactive',
            'category' => 'required|string|max:100',
            'target_audience' => 'required|in:primary,secondary,university,all',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'duration' => 'nullable|numeric|min:0',
            'learning_objectives' => 'nullable|string',
            'prerequisites' => 'nullable|string',
            'content_url' => 'nullable|url',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,zip|max:10240',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tags' => 'nullable|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $validator->validated();
        $data['created_by'] = Auth::id();

        // Handle file upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $data['file_path'] = $file->storeAs('educational-resources/files', $filename, 'public');
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $image = $request->file('featured_image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $data['featured_image'] = $image->storeAs('educational-resources/images', $imageName, 'public');
        }

        // Process tags
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        }

        EducationalResource::create($data);

        return redirect()->route('admin.educational-resources.index')
            ->with('success', 'Educational resource created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $resource = EducationalResource::with('creator')->findOrFail($id);
        return view('admin.educational-resources.show', compact('resource'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $resource = EducationalResource::findOrFail($id);
        return view('admin.educational-resources.edit', compact('resource'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $resource = EducationalResource::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:study_guide,video_course,document,external_link,interactive',
            'category' => 'required|string|max:100',
            'target_audience' => 'required|in:primary,secondary,university,all',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'duration' => 'nullable|numeric|min:0',
            'learning_objectives' => 'nullable|string',
            'prerequisites' => 'nullable|string',
            'content_url' => 'nullable|url',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,zip|max:10240',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tags' => 'nullable|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $validator->validated();

        // Handle file upload
        if ($request->hasFile('file')) {
            // Delete old file if exists
            if ($resource->file_path && Storage::disk('public')->exists($resource->file_path)) {
                Storage::disk('public')->delete($resource->file_path);
            }

            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $data['file_path'] = $file->storeAs('educational-resources/files', $filename, 'public');
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($resource->featured_image && Storage::disk('public')->exists($resource->featured_image)) {
                Storage::disk('public')->delete($resource->featured_image);
            }

            $image = $request->file('featured_image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $data['featured_image'] = $image->storeAs('educational-resources/images', $imageName, 'public');
        }

        // Process tags
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        }

        $resource->update($data);

        return redirect()->route('admin.educational-resources.index')
            ->with('success', 'Educational resource updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $resource = EducationalResource::findOrFail($id);

        // Delete associated files
        if ($resource->file_path && Storage::disk('public')->exists($resource->file_path)) {
            Storage::disk('public')->delete($resource->file_path);
        }

        if ($resource->featured_image && Storage::disk('public')->exists($resource->featured_image)) {
            Storage::disk('public')->delete($resource->featured_image);
        }

        $resource->delete();

        return redirect()->route('admin.educational-resources.index')
            ->with('success', 'Educational resource deleted successfully!');
    }
}
