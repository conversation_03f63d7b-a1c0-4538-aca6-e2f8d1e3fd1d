"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Youtube, MessageCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useSettings } from "@/hooks/useSettings"

export function Footer() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])
  const { settings, loading: settingsLoading } = useSettings()

  // Get dynamic settings or use defaults
  const appName = settings?.app_name || 'Laravel NGO'
  const appLogo = settings?.app_logo
  const contactEmail = settings?.contact_email || '<EMAIL>'
  const contactPhone = settings?.contact_phone || '+234 ************'
  const siteDescription = settings?.site_description || 'Empowering communities through education, development programs, and sustainable initiatives.'
  
  // Address information
  const organizationAddress = settings?.organization_address
  const organizationCity = settings?.organization_city
  const organizationState = settings?.organization_state
  const organizationCountry = settings?.organization_country
  const organizationPostalCode = settings?.organization_postal_code
  
  // Construct full address
  const fullAddress = [
    organizationAddress,
    organizationCity,
    organizationState,
    organizationCountry,
    organizationPostalCode
  ].filter(Boolean).join(', ')

  // Social media links
  const socialLinks = [
    { key: 'social_facebook', icon: Facebook, label: 'Facebook' },
    { key: 'social_twitter', icon: Twitter, label: 'Twitter' },
    { key: 'social_instagram', icon: Instagram, label: 'Instagram' },
    { key: 'social_linkedin', icon: Linkedin, label: 'LinkedIn' },
    { key: 'social_youtube', icon: Youtube, label: 'YouTube' },
  ]

  return (
    <footer className="bg-green-950 dark:bg-black text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-2">
              {appLogo ? (
                <img 
                  src={appLogo} 
                  alt={`${appName} Logo`}
                  className="h-8 w-auto object-contain"
                />
              ) : (
                <div className="relative h-8 w-8 overflow-hidden rounded-full bg-green-600">
                  <Heart className="absolute inset-0 m-auto h-5 w-5 text-white" />
                </div>
              )}
              <span className="text-xl font-bold">
                <span className="text-amber-400 dark:text-amber-400">{appName}</span>
              </span>
            </Link>
            <p className="text-green-200 text-sm">
              {siteDescription}
            </p>
            
            {/* Social Media Links */}
            <div className="flex flex-wrap gap-2">
              {socialLinks.map(({ key, icon: Icon, label }) => {
                const url = settings?.[key as keyof typeof settings]
                if (!url) return null
                
                return (
                  <Link key={key} href={url} target="_blank" rel="noopener noreferrer">
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200" 
                      aria-label={label}
                    >
                      <Icon className="h-4 w-4" />
                    </Button>
                  </Link>
                )
              })}
              
              {/* WhatsApp Link */}
              {(settings as any)?.social_whatsapp && (
                <Link
                  href={`https://wa.me/${(settings as any).social_whatsapp.replace(/[^0-9]/g, '')}`}
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200" 
                    aria-label="WhatsApp"
                  >
                    <MessageCircle className="h-4 w-4" />
                  </Button>
                </Link>
              )}
              
              {/* Telegram Link */}
              {(settings as any)?.social_telegram && (
                <Link href={(settings as any).social_telegram} target="_blank" rel="noopener noreferrer">
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200" 
                    aria-label="Telegram"
                  >
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                    </svg>
                  </Button>
                </Link>
              )}
              
              {/* TikTok Link */}
              {(settings as any)?.social_tiktok && (
                <Link href={(settings as any).social_tiktok} target="_blank" rel="noopener noreferrer">
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    className="text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200" 
                    aria-label="TikTok"
                  >
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                  </Button>
                </Link>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-green-200 hover:text-amber-400 transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/projects" className="text-green-200 hover:text-amber-400 transition-colors">
                  Our Projects
                </Link>
              </li>
              <li>
                <Link href="/scholarships" className="text-green-200 hover:text-amber-400 transition-colors">
                  Scholarships
                </Link>
              </li>
              <li>
                <Link href="/learn-with-us" className="text-green-200 hover:text-amber-400 transition-colors">
                  Learn With Us
                </Link>
              </li>
              <li>
                <Link href="/impact" className="text-green-200 hover:text-amber-400 transition-colors">
                  Our Impact
                </Link>
              </li>
              <li>
                <Link href="/team" className="text-green-200 hover:text-amber-400 transition-colors">
                  Our Team
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Info</h3>
            <div className="space-y-3">
              {fullAddress && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-green-200 text-sm">{fullAddress}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-green-400" />
                <a 
                  href={`tel:${contactPhone}`}
                  className="text-green-200 text-sm hover:text-amber-400 transition-colors"
                >
                  {contactPhone}
                </a>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-green-400" />
                <a 
                  href={`mailto:${contactEmail}`}
                  className="text-green-200 text-sm hover:text-amber-400 transition-colors"
                >
                  {contactEmail}
                </a>
              </div>
            </div>
          </div>

          {/* Newsletter */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Stay Updated</h3>
            <p className="text-green-200 text-sm">
              Subscribe to our newsletter for updates on our programs and impact.
            </p>
            {isClient && (
              <div className="flex gap-2" suppressHydrationWarning>
                <Input
                  type="email"
                  placeholder="Your email"
                  className="bg-green-900 border-green-700 text-white placeholder:text-green-300 focus:ring-amber-500 focus:border-amber-500 rounded-md"
                  suppressHydrationWarning
                />
                <Button 
                  className="bg-amber-500 hover:bg-amber-600 text-green-950 dark:text-green-950"
                  suppressHydrationWarning
                >
                  Subscribe
                </Button>
              </div>
            )}
            
            {/* Legal Links */}
            <div className="pt-4 space-y-1">
              <Link href="/privacy" className="block text-green-300 text-xs hover:text-amber-400 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="block text-green-300 text-xs hover:text-amber-400 transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-green-800 mt-8 pt-8 text-center">
          <p className="text-green-200 text-sm">
            © {new Date().getFullYear()} {appName}. All rights reserved.
          </p>
          <p className="text-green-300 text-xs mt-2">
            Built with ❤️ to empower communities and create lasting impact.
          </p>
        </div>
      </div>
    </footer>
  )
}
