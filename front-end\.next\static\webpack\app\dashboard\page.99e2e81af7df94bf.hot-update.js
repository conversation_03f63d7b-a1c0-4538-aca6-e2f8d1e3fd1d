"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./components/ui/alert-dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _student_page__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./student/page */ \"(app-pages-browser)/./app/dashboard/student/page.tsx\");\n/* harmony import */ var _volunteer_page__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./volunteer/page */ \"(app-pages-browser)/./app/dashboard/volunteer/page.tsx\");\n/* harmony import */ var _partner_page__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./partner/page */ \"(app-pages-browser)/./app/dashboard/partner/page.tsx\");\n/* harmony import */ var _user_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./user/page */ \"(app-pages-browser)/./app/dashboard/user/page.tsx\");\n/* harmony import */ var _admin_page__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./admin/page */ \"(app-pages-browser)/./app/dashboard/admin/page.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Dashboard components for different user types\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [logoutLoading, setLogoutLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings)();\n    // Get app name from settings or use default\n    const appName = (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO';\n    const appLogo = settings === null || settings === void 0 ? void 0 : settings.app_logo;\n    const handleLogout = async ()=>{\n        try {\n            setLogoutLoading(true);\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.logout)();\n            router.push('/auth/login');\n        } catch (error) {\n            console.error('Logout failed:', error);\n            // Even if API logout fails, clear local storage and redirect\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            router.push('/auth/login');\n        } finally{\n            setLogoutLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Check if user is authenticated\n            if (!(0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                console.log('User not authenticated, redirecting to login');\n                router.push('/auth/login');\n                return;\n            }\n            // Get user data from localStorage first\n            const currentUser = (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                console.log('Current user from localStorage:', currentUser);\n                // Check if partner organization should be redirected to /dashboard/partner\n                if (currentUser.role === 'partner_organization' || currentUser.partner_organization_id) {\n                    console.log('Partner organization detected, redirecting to /dashboard/partner');\n                    router.push('/dashboard/partner');\n                    return;\n                }\n                setUser(currentUser);\n            }\n            // Fetch updated user profile from API\n            const fetchUserProfile = {\n                \"DashboardPage.useEffect.fetchUserProfile\": async ()=>{\n                    try {\n                        console.log('Fetching user profile from API...');\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getProfile();\n                        console.log('Profile API response:', response);\n                        if (response.success && response.user) {\n                            // Handle the correct response structure from Laravel backend\n                            const userData = response.user;\n                            console.log('Extracted user data:', userData);\n                            setUser(userData);\n                            // Update localStorage with fresh data\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else if (response.success && response.data) {\n                            // Fallback for different response structure\n                            const userData = response.data.user || response.data;\n                            console.log('Extracted user data (fallback):', userData);\n                            setUser(userData);\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else {\n                            console.error('Failed to fetch profile - invalid response structure:', response);\n                            // If profile fetch fails but we have cached user, continue with cached data\n                            if (!currentUser) {\n                                router.push('/auth/login');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch user profile:', error);\n                        // If API call fails but we have cached user, continue with cached data\n                        if (!currentUser) {\n                            router.push('/auth/login');\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchUserProfile\"];\n            fetchUserProfile();\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Unable to load user data. Please try logging in again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // Route to appropriate dashboard based on user role and preferences\n    const getUserType = ()=>{\n        var _user_preferences;\n        // Check user preferences first (from registration)\n        if ((_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.user_type) {\n            return user.preferences.user_type;\n        }\n        // Fall back to role-based routing\n        if (user.role === 'partner_organization') {\n            return 'partner';\n        } else if (user.role === 'volunteer') {\n            return 'volunteer';\n        } else if (user.role === 'admin') {\n            return 'admin';\n        } else if (user.role === 'student') {\n            return 'student';\n        } else {\n            // For regular users, check if they have specific preferences\n            // If user has partner_organization_id, they're linked to an institution\n            if (user.partner_organization_id) {\n                return 'partner';\n            }\n            return 'user';\n        }\n    };\n    const userType = getUserType();\n    // Add a header with logout functionality\n    const DashboardHeader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white border-b border-gray-200 px-4 py-3 sm:px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: appLogo,\n                                alt: \"\".concat(appName, \" Logo\"),\n                                className: \"h-8 w-auto object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"absolute inset-0 m-auto h-5 w-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                children: [\n                                    appName,\n                                    \" Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:block text-sm text-gray-600\",\n                                children: [\n                                    \"Welcome, \",\n                                    user.first_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Visit Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                    children: [\n                                                        user.first_name.charAt(0),\n                                                        user.last_name.charAt(0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: user.first_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-56\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-sm font-medium\",\n                                                children: [\n                                                    user.first_name,\n                                                    \" \",\n                                                    user.last_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-xs text-gray-500\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                onClick: ()=>{\n                                                    // Navigate to profile settings (we'll implement this)\n                                                    const element = document.querySelector('[data-value=\"profile\"]');\n                                                    if (element) element.click();\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialog, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                            onSelect: (e)=>e.preventDefault(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Logout\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTitle, {\n                                                                        children: \"Are you sure you want to logout?\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogDescription, {\n                                                                        children: \"You will need to login again to access your dashboard.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogCancel, {\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogAction, {\n                                                                        onClick: handleLogout,\n                                                                        disabled: logoutLoading,\n                                                                        className: \"bg-red-600 hover:bg-red-700\",\n                                                                        children: logoutLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Logging out...\"\n                                                                            ]\n                                                                        }, void 0, true) : 'Logout'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, this);\n    // Render the appropriate dashboard component with header\n    const renderDashboard = ()=>{\n        switch(userType){\n            case 'student':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_student_page__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 16\n                }, this);\n            case 'volunteer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_volunteer_page__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 16\n                }, this);\n            case 'partner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_partner_page__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 16\n                }, this);\n            case 'admin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_admin_page__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_page__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardHeader, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            renderDashboard()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"+k7tylyzCuhPf6WaDKLSmdv9BFI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});