@extends('layouts.dashboard')

@section('title', 'Resources - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/resources')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/resources')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Educational Resources</h1>
                            <p class="text-green-100 text-sm lg:text-base">Access study materials, courses, and educational content</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-user-graduate mr-2"></i>
                                My Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resources Content -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <!-- Resource Categories -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-book text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Study Guides</p>
                                <p class="text-xl font-bold text-blue-600">{{ $resources->where('type', 'study_guide')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-video text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Video Courses</p>
                                <p class="text-xl font-bold text-green-600">{{ $resources->where('type', 'video_course')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-pdf text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Documents</p>
                                <p class="text-xl font-bold text-purple-600">{{ $resources->where('type', 'document')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-link text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">External Links</p>
                                <p class="text-xl font-bold text-orange-600">{{ $resources->where('type', 'external_link')->count() }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Resources Grid -->
                    @if($resources->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            @foreach($resources as $resource)
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
                                    @if($resource->featured_image)
                                        <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                                            <img src="{{ Storage::url($resource->featured_image) }}" alt="{{ $resource->title }}" 
                                                 class="w-full h-full object-cover">
                                            <div class="absolute top-4 right-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800">
                                                    {{ ucfirst(str_replace('_', ' ', $resource->type)) }}
                                                </span>
                                            </div>
                                        </div>
                                    @else
                                        <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center relative">
                                            <i class="fas 
                                                @if($resource->type === 'video_course') fa-video
                                                @elseif($resource->type === 'document') fa-file-pdf
                                                @elseif($resource->type === 'external_link') fa-link
                                                @else fa-book
                                                @endif text-white text-4xl"></i>
                                            <div class="absolute top-4 right-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800">
                                                    {{ ucfirst(str_replace('_', ' ', $resource->type)) }}
                                                </span>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="p-6">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                            {{ $resource->title }}
                                        </h3>

                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                            {{ $resource->description }}
                                        </p>

                                        <div class="space-y-2 mb-4">
                                            @if($resource->duration)
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-clock text-blue-500 mr-2"></i>
                                                    <span>Duration: {{ $resource->duration }}</span>
                                                </div>
                                            @endif
                                            @if($resource->difficulty_level)
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-signal text-green-500 mr-2"></i>
                                                    <span>Level: {{ ucfirst($resource->difficulty_level) }}</span>
                                                </div>
                                            @endif
                                            <div class="flex items-center text-sm text-gray-600">
                                                <i class="fas fa-calendar-alt text-purple-500 mr-2"></i>
                                                <span>Added: {{ $resource->created_at->format('M d, Y') }}</span>
                                            </div>
                                        </div>

                                        <div class="flex flex-col gap-2">
                                            @if($resource->type === 'external_link')
                                                <a href="{{ $resource->content_url }}" target="_blank" rel="noopener noreferrer"
                                                   class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-external-link-alt mr-2"></i>
                                                    Visit Resource
                                                </a>
                                            @elseif($resource->file_path)
                                                <a href="{{ Storage::url($resource->file_path) }}" target="_blank"
                                                   class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-download mr-2"></i>
                                                    Download
                                                </a>
                                            @else
                                                <button onclick="viewResource({{ $resource->id }})"
                                                        class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-eye mr-2"></i>
                                                    View Resource
                                                </button>
                                            @endif
                                            <button onclick="viewDetails({{ $resource->id }})" 
                                                    class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-300">
                                                <i class="fas fa-info-circle mr-2"></i>
                                                Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($resources->hasPages())
                            <div class="flex justify-center">
                                {{ $resources->links() }}
                            </div>
                        @endif
                    @else
                        <!-- No Resources -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book-open text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Resources Available</h3>
                            <p class="text-gray-600 mb-6">There are currently no educational resources available for university students.</p>
                            <a href="/student/support" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-life-ring mr-2"></i>
                                Contact Support
                            </a>
                        </div>
                    @endif

                    <!-- Study Tips -->
                    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-lightbulb text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-blue-900 mb-2">Study Tips</h3>
                                <div class="text-blue-800 text-sm space-y-2">
                                    <p><strong>Create a Schedule:</strong> Set aside dedicated time for studying and stick to it</p>
                                    <p><strong>Use Multiple Resources:</strong> Combine different types of materials for better understanding</p>
                                    <p><strong>Take Notes:</strong> Write down key points while studying to improve retention</p>
                                    <p><strong>Practice Regularly:</strong> Apply what you learn through exercises and practice tests</p>
                                    <p><strong>Join Study Groups:</strong> Collaborate with fellow students for better learning outcomes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resource Details Modal -->
<div id="resourceModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Resource Details</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="resourceContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function viewResource(resourceId) {
    // Show loading state
    document.getElementById('resourceContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('resourceModal').classList.remove('hidden');
    
    // In a real implementation, you would load the resource content
    setTimeout(() => {
        document.getElementById('resourceContent').innerHTML = `
            <div class="space-y-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Resource Content</h4>
                    <p class="text-gray-600">Resource content would be displayed here. This could include embedded videos, interactive content, or document viewers.</p>
                </div>
                <div class="flex gap-3">
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                        Close
                    </button>
                </div>
            </div>
        `;
    }, 500);
}

function viewDetails(resourceId) {
    // Show loading state
    document.getElementById('resourceContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('resourceModal').classList.remove('hidden');
    
    // In a real implementation, you would fetch resource details
    setTimeout(() => {
        document.getElementById('resourceContent').innerHTML = `
            <div class="space-y-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Resource Information</h4>
                    <p class="text-gray-600">Detailed resource information would be loaded here via AJAX call to the backend.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Learning Objectives</h4>
                    <p class="text-gray-600">Learning objectives and outcomes would be displayed here.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Prerequisites</h4>
                    <p class="text-gray-600">Any prerequisites or recommended background knowledge would be listed here.</p>
                </div>
                <div class="flex gap-3">
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                        Close
                    </button>
                </div>
            </div>
        `;
    }, 500);
}

function closeModal() {
    document.getElementById('resourceModal').classList.add('hidden');
}
</script>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
