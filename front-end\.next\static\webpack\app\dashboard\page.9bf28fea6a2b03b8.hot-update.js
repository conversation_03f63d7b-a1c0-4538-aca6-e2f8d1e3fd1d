"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/partner/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/partner/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PartnerDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/partner/ProposalManager */ \"(app-pages-browser)/./components/dashboard/partner/ProposalManager.tsx\");\n/* harmony import */ var _components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/partner/ResourceSharing */ \"(app-pages-browser)/./components/dashboard/partner/ResourceSharing.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PartnerDashboard() {\n    var _user_preferences, _dashboardData_overview, _dashboardData_quick_stats, _dashboardData_overview1, _dashboardData_overview2;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [partnershipApplications, setPartnershipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collaborationOpportunities, setCollaborationOpportunities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [upcomingEvents, setUpcomingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [programs, setPrograms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Scholarship-related state\n    const [availableScholarships, setAvailableScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [myStudents, setMyStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scholarshipApplications, setScholarshipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentStats, setStudentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Dashboard overview state\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PartnerDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"PartnerDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        // Fetch user data first\n                        const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getProfile();\n                        if (userData.success) {\n                            setUser(userData.data);\n                        }\n                        // Fetch comprehensive dashboard data\n                        try {\n                            console.log('Fetching partner dashboard data...');\n                            const dashboardResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-dashboard');\n                            console.log('Dashboard response:', dashboardResponse);\n                            if (dashboardResponse.success) {\n                                setDashboardData(dashboardResponse.data);\n                                console.log('Dashboard data set:', dashboardResponse.data);\n                                // Update student stats from dashboard data\n                                if (dashboardResponse.data.overview) {\n                                    setStudentStats({\n                                        total_students: dashboardResponse.data.overview.total_students,\n                                        active_students: dashboardResponse.data.overview.total_students,\n                                        recent_enrollments: 0,\n                                        scholarship_applications: dashboardResponse.data.overview.total_applications\n                                    });\n                                    console.log('Student stats updated from dashboard data');\n                                }\n                            } else {\n                                console.log('Dashboard response not successful:', dashboardResponse);\n                            }\n                        } catch (error) {\n                            var _error_response;\n                            console.error('Error fetching dashboard data:', error);\n                            console.error('Error details:', ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                        }\n                        // Fetch partner-specific data\n                        const [eventsResponse, programsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getUpcomingEvents(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getPrograms()\n                        ]);\n                        const eventsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(eventsResponse);\n                        const programsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(programsResponse);\n                        setUpcomingEvents(eventsData.slice(0, 5));\n                        setPrograms(programsData.slice(0, 3));\n                        // Fetch scholarship-related data\n                        await fetchScholarshipData();\n                        setLoading(false);\n                    } catch (error) {\n                        console.error('Error fetching dashboard data:', error);\n                        setLoading(false);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchDashboardData\"];\n            const fetchScholarshipData = {\n                \"PartnerDashboard.useEffect.fetchScholarshipData\": async ()=>{\n                    try {\n                        console.log('Starting to fetch scholarship data...');\n                        // Fetch available scholarships for partner organizations (Primary/Secondary only)\n                        try {\n                            console.log('Fetching available scholarships...');\n                            const scholarshipsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/available');\n                            console.log('Scholarships response:', scholarshipsResponse);\n                            if (scholarshipsResponse.success) {\n                                setAvailableScholarships(scholarshipsResponse.data || []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching scholarships:', error);\n                        }\n                        // Fetch partner students\n                        try {\n                            console.log('Fetching partner students...');\n                            const studentsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students');\n                            console.log('Students response:', studentsResponse);\n                            if (studentsResponse.success) {\n                                var _studentsResponse_data;\n                                // Handle both paginated and direct array responses\n                                const studentsData = ((_studentsResponse_data = studentsResponse.data) === null || _studentsResponse_data === void 0 ? void 0 : _studentsResponse_data.data) || studentsResponse.data || [];\n                                setMyStudents(Array.isArray(studentsData) ? studentsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching students:', error);\n                        }\n                        // Fetch scholarship applications submitted by this partner\n                        try {\n                            console.log('Fetching my applications...');\n                            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/my-applications');\n                            console.log('Applications response:', applicationsResponse);\n                            if (applicationsResponse.success) {\n                                var _applicationsResponse_data;\n                                const applicationsData = ((_applicationsResponse_data = applicationsResponse.data) === null || _applicationsResponse_data === void 0 ? void 0 : _applicationsResponse_data.data) || applicationsResponse.data || [];\n                                setScholarshipApplications(Array.isArray(applicationsData) ? applicationsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching applications:', error);\n                        }\n                        // Fetch student statistics\n                        try {\n                            console.log('Fetching student statistics...');\n                            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students/statistics');\n                            console.log('Stats response:', statsResponse);\n                            if (statsResponse.success) {\n                                setStudentStats(statsResponse.data || {});\n                            }\n                        } catch (error) {\n                            console.error('Error fetching stats:', error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching scholarship data:', error);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchScholarshipData\"];\n            fetchDashboardData();\n        }\n    }[\"PartnerDashboard.useEffect\"], []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 199,\n            columnNumber: 12\n        }, this);\n    }\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'approved':\n                return 'bg-green-500';\n            case 'rejected':\n                return 'bg-red-500';\n            case 'pending':\n                return 'bg-yellow-500';\n            case 'under_review':\n                return 'bg-blue-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const partnerInfo = (user === null || user === void 0 ? void 0 : (_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.partner_data) || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    partnerInfo.organization_name || (user === null || user === void 0 ? void 0 : user.first_name),\n                                                    \"!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Partnership Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.organization_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: [\n                                                    partnerInfo.organization_type,\n                                                    \" • \",\n                                                    partnerInfo.sector\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/opportunities\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Opportunities\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/apply\",\n                                            children: \"Apply for Partnership\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"My Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview = dashboardData.overview) === null || _dashboardData_overview === void 0 ? void 0 : _dashboardData_overview.total_students) || (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Enrolled students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Available Scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quick_stats = dashboardData.quick_stats) === null || _dashboardData_quick_stats === void 0 ? void 0 : _dashboardData_quick_stats.active_scholarships) || availableScholarships.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Open for applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview1 = dashboardData.overview) === null || _dashboardData_overview1 === void 0 ? void 0 : _dashboardData_overview1.total_applications) || scholarshipApplications.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Total applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: [\n                                                (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview2 = dashboardData.overview) === null || _dashboardData_overview2 === void 0 ? void 0 : _dashboardData_overview2.success_rate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Application success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    defaultValue: \"scholarships\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"scholarships\",\n                                    children: \"Scholarships\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"students\",\n                                    children: \"My Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"applications\",\n                                    children: \"Applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"progression\",\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"opportunities\",\n                                    children: \"Opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"proposals\",\n                                    children: \"Proposals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"resources\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"scholarships\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Available Scholarships\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                children: [\n                                                                    availableScholarships.length,\n                                                                    \" Available\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        availableScholarships.slice(0, 3).map((scholarship)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: scholarship.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                                                    children: scholarship.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 355,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 355,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 358,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \"Deadline: \",\n                                                                                                new Date(scholarship.application_deadline).toLocaleDateString()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 358,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3 flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: scholarship.category === 'primary' ? 'default' : 'secondary',\n                                                                                            children: scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 363,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium text-green-600\",\n                                                                                            children: [\n                                                                                                \"₦\",\n                                                                                                scholarship.amount ? Number(scholarship.amount).toLocaleString() : 'Amount varies'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 366,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 362,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarship-application?scholarship_id=\".concat(scholarship.id, \"&category=\").concat(scholarship.category),\n                                                                                    children: \"Apply for Student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarships/\".concat(scholarship.id),\n                                                                                    children: \"View Details\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, scholarship.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        availableScholarships.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"No scholarships available at the moment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-2\",\n                                                                    children: \"Check back later for new opportunities\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        availableScholarships.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                \"View All Scholarships (\",\n                                                                availableScholarships.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Quick Actions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Add New Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 416,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Register a new student to your school\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 417,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Manage Students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 428,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View and manage all your students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 429,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Browse Scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Explore all available scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 441,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/analytics\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Analytics & Reports\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 452,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View performance insights and statistics\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 453,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                dashboardData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        dashboardData.recent_activities && dashboardData.recent_activities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Recent Activities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-3\",\n                                                    children: dashboardData.recent_activities.slice(0, 5).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-3 p-2 rounded-lg bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: activity.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(activity.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: activity.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this),\n                                        dashboardData.upcoming_deadlines && dashboardData.upcoming_deadlines.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Upcoming Deadlines\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-3\",\n                                                    children: dashboardData.upcoming_deadlines.slice(0, 5).map((deadline, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-2 rounded-lg bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: deadline.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-600\",\n                                                                            children: deadline.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-yellow-600\",\n                                                                            children: new Date(deadline.deadline).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                deadline.days_remaining,\n                                                                                \" days left\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Recent Scholarship Applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            scholarshipApplications.length,\n                                                            \" Total\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    scholarshipApplications.slice(0, 5).map((application)=>{\n                                                        var _application_student, _application_scholarship, _application_scholarship1, _application_scholarship2, _application_status, _application_status1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'Student Name'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.category) || application.scholarship_category || 'Category'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.title) || application.scholarship_title || 'Scholarship Title'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Applied: \",\n                                                                                        new Date(application.created_at).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Amount: ₦\",\n                                                                                        Number(application.scholarship.amount).toLocaleString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                            children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: \"View\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No scholarship applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Start applying for scholarships on behalf of your students\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: \"Browse Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"students\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All Students\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students/add\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add New Student\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.active_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Active Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.recent_enrollments) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Recent Enrollments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.scholarship_applications) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"My Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    myStudents.slice(0, 10).map((student)=>{\n                                                        var _student_progressions_, _student_progressions;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: student.full_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Age: \",\n                                                                                        student.age || 'N/A'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 640,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Grade: \",\n                                                                                        ((_student_progressions = student.progressions) === null || _student_progressions === void 0 ? void 0 : (_student_progressions_ = _student_progressions[0]) === null || _student_progressions_ === void 0 ? void 0 : _student_progressions_.grade_level) || 'Not set'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Status: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: \"outline\",\n                                                                                            className: \"text-xs ml-1\",\n                                                                                            children: student.status || 'Active'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        student.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                student.student_id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            children: \"View Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                href: \"/scholarship-application?student_id=\".concat(student.id),\n                                                                                children: \"Apply for Scholarship\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, student.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    myStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No students registered yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Add students to start managing their scholarship applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: \"Add Your First Student\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Collaboration Opportunities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        collaborationOpportunities.slice(0, 3).map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: opportunity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: opportunity.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 693,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.location\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.timeline\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: opportunity.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                                children: opportunity.budget_range\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, opportunity.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/partnerships/opportunities\",\n                                                                children: \"View All Opportunities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Available Programs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        programs.map((program)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: program.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: program.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: program.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 722,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: \"Learn More\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, program.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        programs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-center py-4\",\n                                                            children: \"No programs available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Partnership Applications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    partnershipApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: application.program\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"Submitted: \",\n                                                                                new Date(application.submitted_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getStatusColor(application.status),\n                                                                    children: application.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    partnershipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No partnership applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/partnerships/apply\",\n                                                                    children: \"Submit Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"opportunities\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Partnership Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Explore collaboration opportunities that align with your organization's mission\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: collaborationOpportunities.map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: opportunity.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Apply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: opportunity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Location: \",\n                                                                        opportunity.location\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Timeline: \",\n                                                                        opportunity.timeline\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 791,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Type: \",\n                                                                        opportunity.type\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Budget: \",\n                                                                        opportunity.budget_range\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, opportunity.id, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"proposals\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 802,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"resources\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"applications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Scholarship Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track scholarship applications submitted on behalf of your students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                scholarshipApplications.map((application)=>{\n                                                    var _application_scholarship, _application_status, _application_status1, _application_student, _application_scholarship1, _application_scholarship2, _application_scholarship3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4 hover:shadow-sm transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.title) || 'Scholarship Application'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                        children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Student:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Amount:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 829,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" ₦\",\n                                                                            ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.amount) ? Number(application.scholarship.amount).toLocaleString() : 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Category:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.category) || application.category || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Submitted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 831,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            new Date(application.submitted_at || application.created_at).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            ((_application_scholarship3 = application.scholarship) === null || _application_scholarship3 === void 0 ? void 0 : _application_scholarship3.application_deadline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Deadline:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" \",\n                                                                    new Date(application.scholarship.application_deadline).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"View Details\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    application.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"Edit Application\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    application.status === 'approved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓ Approved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, application.id, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No scholarship applications submitted yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-4\",\n                                                            children: \"Start applying for scholarships on behalf of your students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/scholarships\",\n                                                                children: \"Browse Available Scholarships\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"progression\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Academic Progression Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                            href: \"/dashboard/partner/progression\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View All Progressions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track and manage student academic progression through grade levels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-8 w-8 text-yellow-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pending Approval\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"Recent Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/progression/create\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Create Progression\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-2\",\n                                                                children: \"No progressions found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Start tracking student academic progression\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Manage your organization's information and partnership preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_name || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 933,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_type || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Contact Person\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    user === null || user === void 0 ? void 0 : user.first_name,\n                                                                    \" \",\n                                                                    user === null || user === void 0 ? void 0 : user.last_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    partnerInfo.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Website\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.website\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.sector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Sector\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.sector\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.size\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.annual_budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Budget\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.annual_budget\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.partnership_interests && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Partnership Interests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: partnerInfo.partnership_interests\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: \"Edit Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(PartnerDashboard, \"Bk3UO5XMfRUlkQk2ciyYz20OBnE=\");\n_c = PartnerDashboard;\nvar _c;\n$RefreshReg$(_c, \"PartnerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/partner/page.tsx\n"));

/***/ })

});