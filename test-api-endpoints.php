<?php

// Simple script to test API endpoints
$baseUrl = 'http://127.0.0.1:8000/api/v1';

// Test user credentials
$email = '<EMAIL>';
$password = 'password123'; // Default password from seeder

echo "Testing API Endpoints\n";
echo "====================\n\n";

// Step 1: Login to get token
echo "1. Logging in...\n";
$loginData = [
    'email' => $email,
    'password' => $password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Login Response Code: $loginHttpCode\n";
echo "Login Response: $loginResponse\n\n";

$loginData = json_decode($loginResponse, true);

if (!$loginData || !isset($loginData['data']['access_token'])) {
    echo "Login failed. Exiting.\n";
    exit(1);
}

$token = $loginData['data']['access_token'];
echo "Token obtained: " . substr($token, 0, 20) . "...\n\n";

// Step 2: Test partner endpoints
$endpoints = [
    '/user' => 'GET',
    '/partner-scholarships/available' => 'GET',
    '/partner-students' => 'GET',
    '/partner-scholarships/my-applications' => 'GET',
    '/partner-students/statistics' => 'GET'
];

foreach ($endpoints as $endpoint => $method) {
    echo "Testing: $method $endpoint\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . $endpoint);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Response Code: $httpCode\n";
    
    if ($httpCode >= 200 && $httpCode < 300) {
        $data = json_decode($response, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "✓ SUCCESS\n";
            if (isset($data['data']) && is_array($data['data'])) {
                echo "Data count: " . count($data['data']) . "\n";
            }
        } else {
            echo "✗ API returned success=false\n";
            echo "Response: $response\n";
        }
    } else {
        echo "✗ HTTP ERROR\n";
        echo "Response: $response\n";
    }
    
    echo "---\n\n";
}

echo "API testing completed.\n";
