<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Donation;
use Illuminate\Auth\Access\HandlesAuthorization;

class DonationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any donations.
     */
    public function viewAny(User $user): bool
    {
        // Only admins and donors can view donations list
        return in_array($user->role, ['admin', 'donor']);
    }

    /**
     * Determine whether the user can view the donation.
     */
    public function view(User $user, Donation $donation): bool
    {
        // Admins can view all donations
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view their own donations
        if ($donation->user_id === $user->id) {
            return true;
        }

        // Partner organizations can view donations made to their campaigns
        if ($user->role === 'partner_organization' && $donation->campaign) {
            return $donation->campaign->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create donations.
     */
    public function create(User $user): bool
    {
        // All active users can make donations
        return $user->isActive();
    }

    /**
     * Determine whether the user can update the donation.
     */
    public function update(User $user, Donation $donation): bool
    {
        // Only admins can update donations (for corrections)
        if ($user->role === 'admin') {
            return true;
        }

        // Users can update their own pending donations
        return $donation->user_id === $user->id && $donation->status === 'pending';
    }

    /**
     * Determine whether the user can delete the donation.
     */
    public function delete(User $user, Donation $donation): bool
    {
        // Only admins can delete donations
        if ($user->role === 'admin') {
            return true;
        }

        // Users can cancel their own pending donations
        return $donation->user_id === $user->id && $donation->status === 'pending';
    }

    /**
     * Determine whether the user can process the donation.
     */
    public function process(User $user, Donation $donation): bool
    {
        // Only admins can process donations
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can refund the donation.
     */
    public function refund(User $user, Donation $donation): bool
    {
        // Only admins can process refunds
        return $user->role === 'admin' && $donation->status === 'completed';
    }

    /**
     * Determine whether the user can view donation analytics.
     */
    public function viewAnalytics(User $user, Donation $donation = null): bool
    {
        // Admins can view all donation analytics
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view their own donation history analytics
        if ($donation && $donation->user_id === $user->id) {
            return true;
        }

        // Partner organizations can view analytics for their campaigns
        if ($user->role === 'partner_organization' && $donation && $donation->campaign) {
            return $donation->campaign->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can download donation receipts.
     */
    public function downloadReceipt(User $user, Donation $donation): bool
    {
        // Admins can download any receipt
        if ($user->role === 'admin') {
            return true;
        }

        // Users can download their own receipts for completed donations
        return $donation->user_id === $user->id && $donation->status === 'completed';
    }

    /**
     * Determine whether the user can view donation campaigns.
     */
    public function viewCampaigns(User $user): bool
    {
        // All authenticated users can view donation campaigns
        return $user->isActive();
    }

    /**
     * Determine whether the user can create donation campaigns.
     */
    public function createCampaign(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can update donation campaigns.
     */
    public function updateCampaign(User $user, $campaign): bool
    {
        // Admins can update any campaign
        if ($user->role === 'admin') {
            return true;
        }

        // Campaign creators can update their campaigns
        return $campaign->created_by === $user->id;
    }

    /**
     * Determine whether the user can delete donation campaigns.
     */
    public function deleteCampaign(User $user, $campaign): bool
    {
        // Admins can delete any campaign
        if ($user->role === 'admin') {
            return true;
        }

        // Campaign creators can delete their campaigns if no donations received
        return $campaign->created_by === $user->id && $campaign->donations()->count() === 0;
    }

    /**
     * Determine whether the user can view campaign analytics.
     */
    public function viewCampaignAnalytics(User $user, $campaign): bool
    {
        // Admins can view all campaign analytics
        if ($user->role === 'admin') {
            return true;
        }

        // Campaign creators can view their campaign analytics
        return $campaign->created_by === $user->id;
    }

    /**
     * Determine whether the user can export donation data.
     */
    public function exportData(User $user): bool
    {
        // Only admins can export donation data
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can view donor information.
     */
    public function viewDonorInfo(User $user, Donation $donation): bool
    {
        // Admins can view all donor information
        if ($user->role === 'admin') {
            return true;
        }

        // Campaign creators can view donor info for their campaigns (if donor consented)
        if ($user->role === 'partner_organization' && $donation->campaign) {
            return $donation->campaign->created_by === $user->id && !$donation->is_anonymous;
        }

        return false;
    }

    /**
     * Determine whether the user can send thank you messages.
     */
    public function sendThankYou(User $user, Donation $donation): bool
    {
        // Admins can send thank you messages
        if ($user->role === 'admin') {
            return true;
        }

        // Campaign creators can send thank you messages for their campaigns
        if ($user->role === 'partner_organization' && $donation->campaign) {
            return $donation->campaign->created_by === $user->id && 
                   $donation->status === 'completed' && 
                   !$donation->is_anonymous;
        }

        return false;
    }
}
