"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/donation-form.tsx":
/*!**************************************!*\
  !*** ./components/donation-form.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DonationForm: () => (/* binding */ DonationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./bank-transfer-receipt */ \"(app-pages-browser)/./components/bank-transfer-receipt.tsx\");\n/* __next_internal_client_entry_do_not_use__ DonationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DonationForm() {\n    var _campaigns_find;\n    _s();\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings)();\n    const [selectedAmount, setSelectedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customAmount, setCustomAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [donationType, setDonationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"one-time\");\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [campaignsLoading, setCampaignsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        donor_name: '',\n        donor_email: '',\n        donor_phone: '',\n        amount: 0,\n        currency: 'NGN',\n        donation_type: 'one-time',\n        payment_method: 'paystack',\n        is_anonymous: false,\n        notes: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [donationId, setDonationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [termsAccepted, setTermsAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load donation campaigns\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DonationForm.useEffect\": ()=>{\n            const loadCampaigns = {\n                \"DonationForm.useEffect.loadCampaigns\": async ()=>{\n                    try {\n                        setCampaignsLoading(true);\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.getDonationCampaigns();\n                        if (response.success && response.data) {\n                            const campaignsData = Array.isArray(response.data) ? response.data : response.data.data || [];\n                            setCampaigns(campaignsData);\n                        }\n                    } catch (error) {\n                        console.error('Error loading campaigns:', error);\n                    } finally{\n                        setCampaignsLoading(false);\n                    }\n                }\n            }[\"DonationForm.useEffect.loadCampaigns\"];\n            loadCampaigns();\n        }\n    }[\"DonationForm.useEffect\"], []);\n    const handleAmountSelect = (amount)=>{\n        setSelectedAmount(amount);\n        setCustomAmount(\"\");\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleCustomAmountChange = (value)=>{\n        const amount = Number.parseInt(value) || 0;\n        setCustomAmount(value);\n        setSelectedAmount(null);\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleDonationTypeChange = (value)=>{\n        const type = value;\n        setDonationType(type);\n        setFormData((prev)=>({\n                ...prev,\n                donation_type: type,\n                frequency: type === 'recurring' ? 'monthly' : undefined\n            }));\n    };\n    const finalAmount = selectedAmount || Number.parseInt(customAmount) || 0;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const amountValidation = (0,_lib_constants__WEBPACK_IMPORTED_MODULE_2__.validateDonationAmount)(finalAmount);\n        if (!amountValidation.isValid) {\n            setSubmitError(amountValidation.error || 'Invalid donation amount');\n            return;\n        }\n        if (!formData.donor_name || !formData.donor_email) {\n            setSubmitError('Please fill in all required fields');\n            return;\n        }\n        if (!termsAccepted) {\n            setSubmitError('Please accept the terms and conditions');\n            return;\n        }\n        setIsSubmitting(true);\n        setSubmitError(null);\n        try {\n            const donationData = {\n                ...formData,\n                amount: finalAmount\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.createDonation(donationData);\n            if (response.success && response.data) {\n                // Store donation ID for receipt upload\n                setDonationId(response.data.id);\n                if (formData.payment_method === 'paystack' && response.data.payment_url) {\n                    // Redirect to Paystack payment page\n                    window.location.href = response.data.payment_url;\n                } else {\n                    // Bank transfer success - show receipt upload\n                    setSubmitSuccess(true);\n                }\n            } else {\n                setSubmitError(response.message || 'Failed to process donation');\n            }\n        } catch (error) {\n            console.error('Donation submission error:', error);\n            setSubmitError('An error occurred while processing your donation. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (submitSuccess) {\n        // For bank transfers, show receipt upload component\n        if (formData.payment_method === 'bank_transfer' && donationId) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-blue-200 bg-blue-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Bank Transfer Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Number:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_number) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Bank Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_bank_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: [\n                                                            \"₦\",\n                                                            finalAmount.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                                        className: \"mt-4 border-blue-200 bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                                className: \"text-blue-700\",\n                                                children: \"Please complete your bank transfer using the details above, then upload your receipt below for verification.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__.BankTransferReceipt, {\n                        donationId: donationId,\n                        onSuccess: ()=>{\n                            // Receipt uploaded successfully\n                            console.log('Receipt uploaded successfully');\n                        },\n                        onError: (error)=>{\n                            console.error('Receipt upload error:', error);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this);\n        }\n        // For other payment methods or fallback\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-green-600 mb-4\",\n                        children: \"Thank You!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Your donation has been received successfully.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Make Another Donation\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-2xl text-center\",\n                    children: \"Complete Your Donation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-8\",\n                    children: [\n                        submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                            className: \"border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                    className: \"text-red-600\",\n                                    children: submitError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: donationType,\n                            onValueChange: handleDonationTypeChange,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"one-time\",\n                                            className: \"rounded-l-xl\",\n                                            children: \"One-Time Donation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"recurring\",\n                                            className: \"rounded-r-xl\",\n                                            children: \"Monthly Giving\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"one-time\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Select Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        15000,\n                                                        25000,\n                                                        50000,\n                                                        100000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"amount-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"amount-\".concat(amount),\n                                                                    className: \"flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        amount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-amount\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"Or enter custom amount (minimum \",\n                                                        (0,_lib_constants__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-amount\",\n                                                    type: \"number\",\n                                                    min: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT,\n                                                    placeholder: \"Enter amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"recurring\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-amber-50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-amber-800 dark:text-amber-200 mb-2\",\n                                                    children: \"Monthly Giving Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-amber-700 dark:text-amber-300\",\n                                                    children: \"Join our monthly giving program and provide sustained support for our ongoing projects. You can modify or cancel your recurring donation at any time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Monthly Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        15000,\n                                                        25000,\n                                                        50000,\n                                                        100000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"monthly-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"monthly-\".concat(amount),\n                                                                    className: \"flex flex-col items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₦\",\n                                                                                amount.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"/month\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-monthly\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"Or enter custom monthly amount (minimum \",\n                                                        (0,_lib_constants__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-monthly\",\n                                                    type: \"number\",\n                                                    min: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT,\n                                                    placeholder: \"Enter monthly amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Frequency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    value: formData.frequency || 'monthly',\n                                                    onValueChange: (value)=>handleInputChange('frequency', value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"monthly\",\n                                                                    children: \"Monthly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"quarterly\",\n                                                                    children: \"Quarterly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"annually\",\n                                                                    children: \"Annually\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Donor Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-name\",\n                                                            children: [\n                                                                \"Full Name \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 55\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-name\",\n                                                            placeholder: \"Enter your full name\",\n                                                            value: formData.donor_name,\n                                                            onChange: (e)=>handleInputChange('donor_name', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-email\",\n                                                            children: [\n                                                                \"Email Address \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 60\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-email\",\n                                                            type: \"email\",\n                                                            placeholder: \"Enter your email\",\n                                                            value: formData.donor_email,\n                                                            onChange: (e)=>handleInputChange('donor_email', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-phone\",\n                                                            children: \"Phone Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-phone\",\n                                                            placeholder: \"Enter your phone number\",\n                                                            value: formData.donor_phone,\n                                                            onChange: (e)=>handleInputChange('donor_phone', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"purpose\",\n                                                            children: \"Purpose (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"purpose\",\n                                                            placeholder: \"e.g., Education, Healthcare, General\",\n                                                            value: formData.purpose || '',\n                                                            onChange: (e)=>handleInputChange('purpose', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"anonymous\",\n                                                    checked: formData.is_anonymous,\n                                                    onCheckedChange: (checked)=>handleInputChange('is_anonymous', checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"anonymous\",\n                                                    className: \"text-sm\",\n                                                    children: \"Make this donation anonymous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Payment Method\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                            value: formData.payment_method,\n                                            onValueChange: (value)=>handleInputChange('payment_method', value),\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"paystack\",\n                                                            id: \"paystack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"paystack\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Pay with Card (Paystack)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Secure online payment with debit/credit card\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"bank_transfer\",\n                                                            id: \"bank_transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bank_transfer\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Bank Transfer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Transfer directly to our bank account\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 13\n                                        }, this),\n                                        formData.payment_method === 'bank_transfer' && !settingsLoading && settings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-950/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Bank Account Details\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_name || 'HALIMAKQ Foundation'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Number:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_number || '**********'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Bank Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_bank_name || 'First Bank of Nigeria'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-4\",\n                                                    children: \"Please use your email address as the transfer reference for easy identification.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 11\n                                }, this),\n                                finalAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 pt-6 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 dark:bg-green-950/20 p-6 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-800 dark:text-green-200 mb-4\",\n                                                    children: \"Donation Summary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: donationType === \"recurring\" ? \"\".concat(formData.frequency || 'Monthly', \" Recurring\") : \"One-Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        formData.campaign_id && campaigns.find((c)=>c.id === formData.campaign_id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Campaign:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: (_campaigns_find = campaigns.find((c)=>c.id === formData.campaign_id)) === null || _campaigns_find === void 0 ? void 0 : _campaigns_find.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Payment Method:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: formData.payment_method === 'paystack' ? 'Card Payment' : 'Bank Transfer'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-green-600 pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"terms\",\n                                                    checked: termsAccepted,\n                                                    onCheckedChange: (checked)=>setTermsAccepted(checked === true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"terms\",\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        \"I agree to the\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Terms of Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        \"and\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Privacy Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"submit\",\n                                            size: \"lg\",\n                                            disabled: isSubmitting || finalAmount < _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT || !termsAccepted || !formData.donor_name || !formData.donor_email,\n                                            className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-lg py-6 transition-all duration-200 hover:scale-105 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Processing...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    formData.payment_method === 'paystack' ? 'Pay Now' : 'Submit Donation',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground text-center mt-4\",\n                                    children: \"Your donation is secure and encrypted. We never store your payment information.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_s(DonationForm, \"8/3rt3UtNsd7/Wz7WV++9wGRHI8=\", false, function() {\n    return [\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings\n    ];\n});\n_c = DonationForm;\nvar _c;\n$RefreshReg$(_c, \"DonationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/donation-form.tsx\n"));

/***/ })

});