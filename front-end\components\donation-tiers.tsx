"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Droplets, GraduationCap, Shield, Sprout } from "lucide-react"

const donationTiers = [
  {
    amount: 15000,
    title: "Supporter",
    description: "Provides clean water for 3 families for a month",
    icon: <Droplets className="h-8 w-8 text-blue-500" />,
    benefits: ["Monthly newsletter", "Impact updates"],
  },
  {
    amount: 25000,
    title: "Advocate",
    description: "Sponsors school supplies for 8 children",
    icon: <GraduationCap className="h-8 w-8 text-green-500" />,
    benefits: ["Monthly newsletter", "Impact updates", "Quarterly reports"],
    popular: true,
  },
  {
    amount: 75000,
    title: "Champion",
    description: "Funds healthcare for 15 families",
    icon: <Shield className="h-8 w-8 text-red-500" />,
    benefits: ["Monthly newsletter", "Impact updates", "Quarterly reports", "Annual event invitation"],
  },
  {
    amount: 150000,
    title: "Hero",
    description: "Supports agricultural training for 30 farmers",
    icon: <Sprout className="h-8 w-8 text-amber-500" />,
    benefits: [
      "Monthly newsletter",
      "Impact updates",
      "Quarterly reports",
      "Annual event invitation",
      "Direct project updates",
    ],
  },
]

interface DonationTiersProps {
  selectedAmount?: number | null
  onAmountSelect?: (amount: number) => void
}

export function DonationTiers({ selectedAmount = null, onAmountSelect }: DonationTiersProps) {
  const [internalSelectedAmount, setInternalSelectedAmount] = useState<number | null>(null)

  const currentSelectedAmount = selectedAmount !== undefined ? selectedAmount : internalSelectedAmount

  const handleAmountSelect = (amount: number) => {
    if (onAmountSelect) {
      onAmountSelect(amount)
    } else {
      setInternalSelectedAmount(amount)
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
      {donationTiers.map((tier, index) => (
        <Card
          key={index}
          className={`relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer neumorphic rounded-2xl ${
            currentSelectedAmount === tier.amount ? "ring-2 ring-green-500" : ""
          } ${tier.popular ? "border-green-500" : ""}`}
          onClick={() => handleAmountSelect(tier.amount)}
        >
          {tier.popular && (
            <Badge className="absolute top-4 right-4 bg-green-500 hover:bg-green-600 rounded-full">Most Popular</Badge>
          )}
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">{tier.icon}</div>
            <CardTitle className="text-xl">{tier.title}</CardTitle>
            <div className="text-3xl font-bold text-green-600">₦{tier.amount.toLocaleString()}</div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground text-center">{tier.description}</p>
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Benefits:</h4>
              <ul className="space-y-1">
                {tier.benefits.map((benefit, i) => (
                  <li key={i} className="flex items-center gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
