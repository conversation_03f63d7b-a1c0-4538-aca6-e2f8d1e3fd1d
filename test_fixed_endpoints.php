<?php

echo "=== Testing Fixed API Endpoints ===\n\n";

// Test the corrected endpoint paths that frontend will use
$baseUrl = 'http://127.0.0.1:8000/api/v1';

echo "1. Testing GET {$baseUrl}/public-scholarships\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/public-scholarships');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✓ Scholarship listing API working\n";
        $scholarshipsData = isset($data['data']['data']) ? $data['data']['data'] : $data['data'];
        echo "✓ Found " . count($scholarshipsData) . " scholarships\n";
        
        if (!empty($scholarshipsData)) {
            $first = $scholarshipsData[0];
            echo "✓ First scholarship: " . $first['title'] . " (₦" . number_format($first['amount']) . ")\n";
        }
    } else {
        echo "✗ API error: " . ($data['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode\n";
}

echo "\n2. Testing GET {$baseUrl}/public-scholarships/5\n";
$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $baseUrl . '/public-scholarships/5');
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch2, CURLOPT_TIMEOUT, 10);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

if ($httpCode2 === 200) {
    $data2 = json_decode($response2, true);
    if ($data2 && $data2['success']) {
        echo "✓ Individual scholarship API working\n";
        $scholarship = $data2['data'];
        echo "✓ Scholarship: " . $scholarship['title'] . "\n";
        echo "✓ Amount: ₦" . number_format($scholarship['amount']) . "\n";
        echo "✓ Custom fields: " . count($scholarship['custom_fields'] ?? []) . "\n";
    } else {
        echo "✗ API error: " . ($data2['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode2\n";
}

echo "\n=== Frontend URL Test ===\n";
echo "Frontend will call:\n";
echo "- List: http://127.0.0.1:8000/api/v1/public-scholarships\n";
echo "- Detail: http://127.0.0.1:8000/api/v1/public-scholarships/5\n";
echo "Both endpoints are working correctly!\n";
