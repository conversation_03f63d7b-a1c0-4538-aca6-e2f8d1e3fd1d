<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartnershipProposal extends Model
{
    use HasFactory;

    protected $fillable = [
        'partner_id',
        'title',
        'description',
        'collaboration_type',
        'proposed_duration',
        'expected_outcomes',
        'resources_required',
        'budget_estimate',
        'timeline',
        'target_beneficiaries',
        'proposal_documents',
        'status',
        'submitted_at',
        'reviewed_at',
        'reviewed_by',
        'review_notes',
        'approved_at',
        'implementation_start_date',
        'implementation_end_date'
    ];

    protected $casts = [
        'resources_required' => 'array',
        'proposal_documents' => 'array',
        'budget_estimate' => 'decimal:2',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'approved_at' => 'datetime',
        'implementation_start_date' => 'date',
        'implementation_end_date' => 'date'
    ];

    /**
     * Get the partner that owns this proposal
     */
    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    /**
     * Get the user who reviewed this proposal
     */
    public function reviewedBy()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the collaborative program if this proposal was approved
     */
    public function collaborativeProgram()
    {
        return $this->hasOne(CollaborativeProgram::class, 'proposal_id');
    }

    /**
     * Scope for submitted proposals
     */
    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    /**
     * Scope for approved proposals
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected proposals
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for specific collaboration type
     */
    public function scopeCollaborationType($query, $type)
    {
        return $query->where('collaboration_type', $type);
    }

    /**
     * Check if proposal is submitted
     */
    public function isSubmitted()
    {
        return $this->status === 'submitted';
    }

    /**
     * Check if proposal is under review
     */
    public function isUnderReview()
    {
        return $this->status === 'under_review';
    }

    /**
     * Check if proposal is approved
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if proposal is rejected
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Mark proposal as under review
     */
    public function markUnderReview($reviewedBy = null)
    {
        $this->update([
            'status' => 'under_review',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy
        ]);
    }

    /**
     * Approve proposal
     */
    public function approve($reviewedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'reviewed_by' => $reviewedBy,
            'review_notes' => $notes
        ]);
    }

    /**
     * Reject proposal
     */
    public function reject($reviewedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy,
            'review_notes' => $notes
        ]);
    }

    /**
     * Get collaboration type label
     */
    public function getCollaborationTypeLabelAttribute()
    {
        $types = [
            'joint_program' => 'Joint Program',
            'resource_sharing' => 'Resource Sharing',
            'event_hosting' => 'Event Co-hosting',
            'research' => 'Research Collaboration',
            'funding' => 'Funding Partnership',
            'training' => 'Training & Capacity Building'
        ];

        return $types[$this->collaboration_type] ?? $this->collaboration_type;
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'submitted':
                return 'blue';
            case 'under_review':
                return 'yellow';
            case 'approved':
                return 'green';
            case 'rejected':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Get formatted budget estimate
     */
    public function getFormattedBudgetAttribute()
    {
        if (!$this->budget_estimate) {
            return 'Not specified';
        }

        return '₦' . number_format($this->budget_estimate, 2);
    }

    /**
     * Get days since submission
     */
    public function getDaysSinceSubmissionAttribute()
    {
        return $this->submitted_at ? $this->submitted_at->diffInDays(now()) : 0;
    }
}
