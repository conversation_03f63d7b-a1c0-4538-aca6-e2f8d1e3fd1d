<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\BlogController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\DonationController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\ProgramController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VolunteerController;
use App\Http\Controllers\Api\NewsletterController;
use App\Http\Controllers\Api\TeamController;
use App\Http\Controllers\Api\ScholarshipController;
use App\Http\Controllers\Api\ScholarshipFieldController;
use App\Http\Controllers\Api\ScholarshipApplicationController;
use App\Http\Controllers\Api\EducationalResourceController;
use App\Http\Controllers\Api\PartnerController;
use App\Http\Controllers\Api\PartnerAnalyticsController;
use App\Http\Controllers\Api\Admin\AdminUserController;
use App\Http\Controllers\Api\Admin\AdminBlogController;
use App\Http\Controllers\Api\Admin\AdminEventController;
use App\Http\Controllers\Api\Admin\AdminProgramController;
use App\Http\Controllers\Api\Admin\AdminDonationController;
use App\Http\Controllers\Api\Admin\AdminVolunteerController;
use App\Http\Controllers\Api\Admin\AdminContactController;
use App\Http\Controllers\Api\Admin\AdminScholarshipController;
use App\Http\Controllers\Api\Admin\AdminScholarshipFieldController;
use App\Http\Controllers\Api\Admin\AdminScholarshipTemplateController;
use App\Http\Controllers\Api\PublicScholarshipController;
use App\Http\Controllers\Api\ProjectScholarshipController;
use App\Http\Controllers\Api\Admin\AdminDashboardController;
use App\Http\Controllers\Api\PdfController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Status endpoint
Route::get('/v1/status', function () {
    return response()->json([
        'status' => 'online',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Public routes
Route::prefix('v1')->group(function () {
    // Public settings
    Route::get('/settings', [UserController::class, 'getPublicSettings']);
    
    // Authentication routes
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])->name('verification.verify');
    Route::post('/check-email', [AuthController::class, 'checkEmail']);
    
    // Public content routes
    Route::get('/blog/posts', [BlogController::class, 'index']);
    Route::get('/blog/posts/{slug}', [BlogController::class, 'show']);
    Route::get('/blog/categories', [BlogController::class, 'categories']);
    Route::get('/blog/posts/category/{category}', [BlogController::class, 'byCategory']);
    
    Route::get('/programs', [ProgramController::class, 'index']);
    Route::get('/programs/featured', [ProgramController::class, 'featured']);
    Route::get('/programs/stats', [ProgramController::class, 'stats']);
    Route::get('/programs/{slug}', [ProgramController::class, 'show'])->name('public.programs.show');
    
    Route::get('/events', [EventController::class, 'index']);
    Route::get('/events/upcoming', [EventController::class, 'upcoming']);
    Route::get('/events/{id}', [EventController::class, 'show']);
    
    Route::get('/team', [TeamController::class, 'index']);
    Route::get('/team/{id}', [TeamController::class, 'show']);
    
    // Public scholarships (Enhanced)
    Route::get('/public-scholarships', [PublicScholarshipController::class, 'index']);
    Route::get('/public-scholarships/{id}', [PublicScholarshipController::class, 'show']);

    // Project scholarship data for Projects page
    Route::get('/project-scholarships/statistics', [ProjectScholarshipController::class, 'getStatistics']);
    Route::get('/project-scholarships/portfolio', [ProjectScholarshipController::class, 'getPortfolio']);
    Route::get('/project-scholarships/by-status', [ProjectScholarshipController::class, 'getScholarshipsByStatus']);
    
    // Volunteer opportunities (public)
    Route::get('/volunteer/opportunities', [VolunteerController::class, 'opportunities']);
    
    // Contact and newsletter
    Route::post('/contact', [ContactController::class, 'store']);
    Route::get('/contact/faq', [ContactController::class, 'faq']);
    Route::post('/newsletter/subscribe', [NewsletterController::class, 'subscribe']);
    Route::post('/newsletter/unsubscribe', [NewsletterController::class, 'unsubscribe']);
    
    // Public donation endpoints
    Route::post('/donations/payment/callback', [DonationController::class, 'paymentCallback']);
    Route::get('/donations/campaigns', [DonationController::class, 'campaigns']);
    Route::get('/donations/campaigns/{id}', [DonationController::class, 'showCampaign']);
});

// Protected routes (require authentication)
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // Authentication
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/email/verification-notification', [AuthController::class, 'resendVerification']);
    
    // User profile
    Route::get('/profile', [UserController::class, 'profile']);
    Route::put('/profile', [UserController::class, 'updateProfile']);
    Route::put('/profile/password', [UserController::class, 'changePassword']);
    Route::post('/profile/avatar', [UserController::class, 'uploadAvatar']);
    Route::post('/profile/generate-qr', [UserController::class, 'generateQrCode']);
    Route::get('/profile/id-card/pdf', [PdfController::class, 'generateIdCard']);
    
    // Volunteer routes
    Route::post('/volunteer/apply', [VolunteerController::class, 'apply']);
    Route::get('/volunteer/application', [VolunteerController::class, 'getApplication']);
    Route::put('/volunteer/application', [VolunteerController::class, 'updateApplication']);
    Route::post('/volunteer/hours', [VolunteerController::class, 'logHours']);
    Route::get('/volunteer/hours', [VolunteerController::class, 'getHours']);
    
    // Event registration
    Route::post('/events/{id}/register', [EventController::class, 'register']);
    Route::delete('/events/{id}/unregister', [EventController::class, 'unregister']);
    Route::get('/events/my-registrations', [EventController::class, 'myRegistrations']);
    
    // Donations
    Route::post('/donations', [DonationController::class, 'store']);
    Route::get('/donations/my-donations', [DonationController::class, 'myDonations']);
    Route::get('/donations/{id}/receipt', [DonationController::class, 'receipt']);
    
    // Blog interaction
    Route::post('/blog/posts/{id}/comments', [BlogController::class, 'storeComment']);
    Route::put('/blog/comments/{id}', [BlogController::class, 'updateComment']);
    Route::delete('/blog/comments/{id}', [BlogController::class, 'deleteComment']);
    
    // Scholarship applications (Enhanced)
    Route::post('/apply-scholarship/{id}', [PublicScholarshipController::class, 'apply']);
    Route::get('/scholarships/my-applications', [ScholarshipController::class, 'myApplications']);

    // Partner Organization Scholarship Applications (Primary/Secondary)
    Route::prefix('partner-scholarships')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization', 'scholarship.category'])->group(function () {
        Route::get('/available', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'getAvailableScholarships']);
        Route::get('/{id}/details', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'getScholarshipDetails']);
        Route::post('/{id}/apply', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'applyForStudent']);
        Route::get('/my-applications', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'getMyApplications']);
        Route::get('/my-students', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'getMyStudents']);
        Route::get('/statistics', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'getStatistics']);
        Route::post('/bulk-action', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'bulkAction'])->middleware('role.auth:admin');
        Route::get('/export', [App\Http\Controllers\Api\PartnerScholarshipController::class, 'exportApplications']);
    });

    // Partner Organization Progression Management
    Route::prefix('partner-progression')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization'])->group(function () {
        Route::get('/', [App\Http\Controllers\Api\PartnerProgressionController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\PartnerProgressionController::class, 'store']);
        Route::get('/statistics', [App\Http\Controllers\Api\PartnerProgressionController::class, 'getStatistics']);
        Route::get('/eligible-students', [App\Http\Controllers\Api\PartnerProgressionController::class, 'getEligibleStudents']);
        Route::get('/{id}', [App\Http\Controllers\Api\PartnerProgressionController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\PartnerProgressionController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\PartnerProgressionController::class, 'destroy'])->middleware('role.auth:admin');
        Route::post('/{id}/approve', [App\Http\Controllers\Api\PartnerProgressionController::class, 'approve']);
        Route::post('/{id}/complete', [App\Http\Controllers\Api\PartnerProgressionController::class, 'complete']);
        Route::post('/{id}/reject', [App\Http\Controllers\Api\PartnerProgressionController::class, 'reject']);
    });

    // Student Progression API (accessible by both partners and individual students)
    Route::prefix('student-progression')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization,user'])->group(function () {
        Route::get('/student/{studentId}/history', [App\Http\Controllers\Api\StudentProgressionController::class, 'getStudentHistory']);
        Route::get('/analytics', [App\Http\Controllers\Api\StudentProgressionController::class, 'getProgressionAnalytics'])->middleware('role.auth:admin,partner_organization');
        Route::post('/create', [App\Http\Controllers\Api\StudentProgressionController::class, 'store'])->middleware('role.auth:admin,partner_organization');
        Route::put('/{progressionId}/status', [App\Http\Controllers\Api\StudentProgressionController::class, 'updateProgressionStatus'])->middleware('role.auth:admin,partner_organization');
        Route::get('/eligible-students', [App\Http\Controllers\Api\StudentProgressionController::class, 'getEligibleStudents'])->middleware('role.auth:admin,partner_organization');
        Route::post('/bulk-advance', [App\Http\Controllers\Api\StudentProgressionController::class, 'bulkAdvanceStudents'])->middleware('role.auth:admin,partner_organization');
        Route::post('/{progressionId}/approve', [App\Http\Controllers\Api\StudentProgressionController::class, 'approve'])->middleware('role.auth:admin,partner_organization');
        Route::get('/notifications', [App\Http\Controllers\Api\StudentProgressionController::class, 'getProgressionNotifications'])->middleware('role.auth:admin,partner_organization');
        Route::post('/send-notifications', [App\Http\Controllers\Api\StudentProgressionController::class, 'sendProgressionNotifications'])->middleware('role.auth:admin,partner_organization');
    });

    // Multi-Year Application Management
    Route::prefix('multi-year-applications')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization,user'])->group(function () {
        Route::get('/student/{studentId}/history', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'getStudentApplicationHistory']);
        Route::get('/student/{studentId}/eligible-scholarships', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'getEligibleScholarships']);
        Route::post('/create', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'createMultiYearApplication'])->middleware('scholarship.category');
        Route::get('/analytics', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'getMultiYearAnalytics'])->middleware('role.auth:admin,partner_organization');
        Route::put('/{applicationId}/renew', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'renewApplication'])->middleware('role.auth:admin,partner_organization');
        Route::get('/{applicationId}/tracking', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'getApplicationTracking']);
        Route::post('/bulk-renew', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'bulkRenewApplications'])->middleware('role.auth:admin,partner_organization');
        Route::get('/renewal-eligibility', [App\Http\Controllers\Api\MultiYearApplicationController::class, 'getRenewalEligibility'])->middleware('role.auth:admin,partner_organization');
    });

    // Partner Organization Student Management
    Route::prefix('partner-students')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization'])->group(function () {
        Route::get('/', [App\Http\Controllers\Api\PartnerStudentController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\PartnerStudentController::class, 'store']);
        Route::get('/statistics', [App\Http\Controllers\Api\PartnerStudentController::class, 'getStatistics']);
        Route::get('/{id}', [App\Http\Controllers\Api\PartnerStudentController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\PartnerStudentController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\PartnerStudentController::class, 'destroy']);
        Route::post('/{id}/advance-grade', [App\Http\Controllers\Api\PartnerStudentController::class, 'advanceGrade']);
    });

    // Partner Organization Analytics
    Route::prefix('partner-analytics')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization'])->group(function () {
        Route::get('/', [PartnerAnalyticsController::class, 'index']);
        Route::get('/export', [PartnerAnalyticsController::class, 'export']);
    });

    // Student Scholarship Applications (University)
    Route::prefix('student-scholarships')->middleware(['auth:sanctum', 'role.auth:admin,user', 'scholarship.category'])->group(function () {
        Route::get('/available', [App\Http\Controllers\Api\StudentScholarshipController::class, 'getAvailableScholarships']);
        Route::get('/statistics', [App\Http\Controllers\Api\StudentScholarshipController::class, 'getStatistics']);
        Route::get('/{id}/details', [App\Http\Controllers\Api\StudentScholarshipController::class, 'getScholarshipDetails']);
        Route::post('/{id}/apply', [App\Http\Controllers\Api\StudentScholarshipController::class, 'applyForScholarship']);
        Route::get('/my-applications', [App\Http\Controllers\Api\StudentScholarshipController::class, 'getMyApplications']);
        Route::get('/files/{fileId}/download', [App\Http\Controllers\Api\StudentScholarshipController::class, 'downloadFile']);
    });

    // Educational Resources
    Route::get('/educational-resources', [EducationalResourceController::class, 'index']);
    Route::get('/educational-resources/{id}', [EducationalResourceController::class, 'show']);
    Route::post('/educational-resources/{id}/progress', [EducationalResourceController::class, 'updateProgress']);
    Route::post('/educational-resources/{id}/bookmark', [EducationalResourceController::class, 'toggleBookmark']);
    Route::get('/educational-resources/my-bookmarks', [EducationalResourceController::class, 'getBookmarks']);
    Route::get('/educational-resources/my-progress-summary', [EducationalResourceController::class, 'getProgressSummary']);
    Route::get('/educational-resources/{id}/download', [EducationalResourceController::class, 'download']);

    // Partner Organization Routes
    Route::post('/partners/register', [PartnerController::class, 'register']);
    Route::get('/partners/profile', [PartnerController::class, 'getProfile']);
    Route::post('/partners/proposals', [PartnerController::class, 'submitProposal']);
    Route::get('/partners/proposals', [PartnerController::class, 'getProposals']);
    Route::post('/partners/resources/share', [PartnerController::class, 'shareResource']);
    Route::get('/partners/resources', [PartnerController::class, 'getSharedResources']);
    Route::get('/partners/analytics', [PartnerController::class, 'getAnalytics']);

    // Partner Organization Dashboard
    Route::prefix('partner-dashboard')->middleware(['auth:sanctum', 'role.auth:admin,partner_organization'])->group(function () {
        Route::get('/', [App\Http\Controllers\Api\PartnerDashboardController::class, 'index']);
    });

    // Enhanced Volunteer Routes
    Route::get('/volunteer/opportunities/matched', [VolunteerController::class, 'getMatchedOpportunities']);
    Route::post('/volunteer/opportunities/{id}/apply', [VolunteerController::class, 'applyForOpportunity']);
    Route::post('/volunteer/hours/log', [VolunteerController::class, 'logHours']);
    Route::get('/volunteer/hours/logs', [VolunteerController::class, 'getHourLogs']);
    Route::get('/volunteer/training/modules', [VolunteerController::class, 'getTrainingModules']);
    Route::post('/volunteer/training/{id}/progress', [VolunteerController::class, 'updateTrainingProgress']);
    Route::get('/volunteer/analytics', [VolunteerController::class, 'getAnalytics']);
});

// Admin routes (require admin role)
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role.auth:admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index']);
    Route::get('/dashboard/analytics', [AdminDashboardController::class, 'analytics']);
    Route::get('/dashboard/stats', [AdminDashboardController::class, 'stats']);

    // User management
    Route::get('/users/export', [AdminUserController::class, 'exportUsers']);
    Route::resource('users', AdminUserController::class);
    Route::put('/users/{id}/toggle-status', [AdminUserController::class, 'toggleStatus']);
    Route::post('/users/{id}/assign-role', [AdminUserController::class, 'assignRole']);
    Route::delete('/users/{id}/remove-role', [AdminUserController::class, 'removeRole']);
    Route::post('/users/bulk-action', [AdminUserController::class, 'bulkAction']);
    
    // Blog Management
    Route::prefix('blog')->group(function () {
        Route::get('/', [BlogController::class, 'adminIndex']);
        Route::get('/posts', [BlogController::class, 'adminPosts']);
        Route::get('/stats', [BlogController::class, 'adminStats']);
        Route::post('/posts/{id}/toggle-status', [BlogController::class, 'togglePostStatus']);
        Route::delete('/posts/{id}', [BlogController::class, 'deletePost']);
        Route::get('/posts/{id}/comments', [BlogController::class, 'getPostComments']);
        Route::post('/posts/{id}/comments', [BlogController::class, 'storeComment']);
        Route::put('/comments/{id}', [BlogController::class, 'updateComment']);
        Route::delete('/comments/{id}', [BlogController::class, 'deleteComment']);
    });
    
    // Event management
    Route::get('/events/export', [AdminEventController::class, 'exportEvents']);
    Route::get('/events/statistics', [AdminEventController::class, 'statistics']);
    Route::resource('events', AdminEventController::class);
    Route::get('/events/{id}/registrations', [AdminEventController::class, 'registrations']);
    Route::put('/events/{id}/registrations/{registrationId}/check-in', [AdminEventController::class, 'checkIn']);
    Route::post('/events/bulk-action', [AdminEventController::class, 'bulkAction']);

    // Program management
    Route::resource('programs', AdminProgramController::class)->names([
        'index' => 'api.admin.programs.index',
        'create' => 'api.admin.programs.create',
        'store' => 'api.admin.programs.store',
        'show' => 'api.admin.programs.show',
        'edit' => 'api.admin.programs.edit',
        'update' => 'api.admin.programs.update',
        'destroy' => 'api.admin.programs.destroy'
    ]);
    Route::put('/programs/{id}/activate', [AdminProgramController::class, 'activate']);
    Route::put('/programs/{id}/complete', [AdminProgramController::class, 'complete']);

    // Donation management
    Route::resource('donations', AdminDonationController::class);
    Route::get('/donations/{id}/receipt', [AdminDonationController::class, 'receipt']);
    Route::resource('donation-campaigns', AdminDonationController::class);

    // Volunteer management
    Route::resource('volunteers', AdminVolunteerController::class);
    Route::put('/volunteers/{id}/approve', [AdminVolunteerController::class, 'approve']);
    Route::put('/volunteers/{id}/reject', [AdminVolunteerController::class, 'reject']);
    Route::put('/volunteers/{id}/background-check', [AdminVolunteerController::class, 'backgroundCheck']);

    // Contact message management
    Route::resource('contact-messages', AdminContactController::class);
    Route::put('/contact-messages/{id}/resolve', [AdminContactController::class, 'resolve']);
    Route::put('/contact-messages/{id}/assign', [AdminContactController::class, 'assign']);

    // Scholarship Management System
    Route::prefix('scholarships')->group(function () {
        // Main scholarship CRUD
        Route::get('/', [ScholarshipController::class, 'index']);
        Route::post('/', [ScholarshipController::class, 'store']);
        Route::get('/statistics', [AdminScholarshipController::class, 'statistics']);
        Route::get('/{id}', [ScholarshipController::class, 'show']);
        Route::put('/{id}', [ScholarshipController::class, 'update']);
        Route::delete('/{id}', [ScholarshipController::class, 'destroy']);
        Route::post('/{id}/close', [ScholarshipController::class, 'close']);
        Route::get('/{id}/applications', [AdminScholarshipController::class, 'getScholarshipApplications']);
        
        // Enhanced Scholarship Fields Management (Admin)
        Route::get('/{scholarshipId}/fields', [AdminScholarshipFieldController::class, 'index']);
        Route::post('/{scholarshipId}/fields', [AdminScholarshipFieldController::class, 'store']);
        Route::get('/{scholarshipId}/fields/{fieldId}', [AdminScholarshipFieldController::class, 'show']);
        Route::put('/{scholarshipId}/fields/{fieldId}', [AdminScholarshipFieldController::class, 'update']);
        Route::delete('/{scholarshipId}/fields/{fieldId}', [AdminScholarshipFieldController::class, 'destroy']);
        Route::post('/{scholarshipId}/fields/reorder', [AdminScholarshipFieldController::class, 'reorder']);
        Route::post('/{scholarshipId}/fields/{fieldId}/toggle', [AdminScholarshipFieldController::class, 'toggleActive']);
        Route::post('/{scholarshipId}/fields/bulk-create', [AdminScholarshipFieldController::class, 'bulkCreate']);
    });

    // Scholarship Templates Management
    Route::prefix('scholarship-templates')->group(function () {
        Route::get('/', [AdminScholarshipTemplateController::class, 'index']);
        Route::post('/', [AdminScholarshipTemplateController::class, 'store']);
        Route::get('/predefined', [AdminScholarshipTemplateController::class, 'getPredefinedTemplates']);
        Route::post('/create-from-predefined', [AdminScholarshipTemplateController::class, 'createFromPredefined']);
        Route::get('/{id}', [AdminScholarshipTemplateController::class, 'show']);
        Route::put('/{id}', [AdminScholarshipTemplateController::class, 'update']);
        Route::delete('/{id}', [AdminScholarshipTemplateController::class, 'destroy']);
        Route::post('/{id}/toggle', [AdminScholarshipTemplateController::class, 'toggleActive']);
    });

    // Scholarship Applications Management
    Route::prefix('scholarship-applications')->group(function () {
        Route::get('/', [ScholarshipApplicationController::class, 'index']);
        Route::get('/{id}', [ScholarshipApplicationController::class, 'show']);
        Route::put('/{id}/status', [ScholarshipApplicationController::class, 'updateStatus']);
        Route::post('/{id}/approve', [ScholarshipApplicationController::class, 'approve']);
        Route::post('/{id}/reject', [ScholarshipApplicationController::class, 'reject']);
        Route::delete('/{id}', [ScholarshipApplicationController::class, 'destroy']);
        Route::get('/scholarship/{scholarshipId}', [ScholarshipApplicationController::class, 'byScholarship']);
        Route::post('/bulk-approve', [ScholarshipApplicationController::class, 'bulkApprove']);
        Route::post('/bulk-reject', [ScholarshipApplicationController::class, 'bulkReject']);
        Route::get('/scholarship/{scholarshipId}/export', [ScholarshipApplicationController::class, 'export']);

        // Enhanced Admin Application Management
        Route::get('/{applicationId}/files', [AdminScholarshipController::class, 'getApplicationFiles']);
        Route::get('/{applicationId}/files/{fileId}/download', [AdminScholarshipController::class, 'downloadFile'])
            ->name('api.admin.scholarships.applications.files.download');
        Route::put('/{applicationId}/files/{fileId}/verify', [AdminScholarshipController::class, 'verifyFile']);
        Route::put('/{id}/review', [AdminScholarshipController::class, 'reviewApplication']);
        Route::post('/bulk-action', [AdminScholarshipController::class, 'bulkAction']);
        Route::post('/export', [AdminScholarshipController::class, 'exportApplications']);

        // New Enhanced Application Review System Routes
        Route::get('/{id}', [AdminScholarshipController::class, 'getApplication']);
        Route::post('/bulk-review', [AdminScholarshipController::class, 'bulkReview']);
        Route::post('/bulk-status', [AdminScholarshipController::class, 'bulkStatus']);
        Route::post('/compare', [AdminScholarshipController::class, 'compareApplications']);
        Route::get('/files/{fileId}', [AdminScholarshipController::class, 'getFile']);
        Route::put('/files/{fileId}/verify', [AdminScholarshipController::class, 'toggleFileVerification']);
        Route::post('/{applicationId}/notify', [AdminScholarshipController::class, 'sendNotification']);
        Route::get('/{applicationId}/download', [AdminScholarshipController::class, 'downloadApplicationFiles']);
    });
    
    // Team management
    Route::resource('team-members', TeamController::class, ['only' => ['store', 'update', 'destroy']]);
    
    // Newsletter management
    Route::get('/newsletter/subscribers', [NewsletterController::class, 'subscribers']);
    Route::post('/newsletter/send', [NewsletterController::class, 'send']);
    Route::delete('/newsletter/subscribers/{id}', [NewsletterController::class, 'removeSubscriber']);
});

// Team management routes (require admin or team leader role)
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role.auth:admin,team_leader'])->group(function () {
    Route::get('/team', [TeamController::class, 'adminIndex']);
    Route::post('/team', [TeamController::class, 'store']);
    Route::put('/team/{id}', [TeamController::class, 'update']);
    Route::delete('/team/{id}', [TeamController::class, 'destroy']);
    Route::post('/team/{id}/photo', [TeamController::class, 'uploadPhoto']);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Image serving with CORS headers
Route::get('/v1/images/{path}', function ($path) {
    $filePath = storage_path('app/public/' . $path);
    
    if (!file_exists($filePath)) {
        // Try public directory for default images
        $publicPath = public_path('img/' . basename($path));
        if (file_exists($publicPath)) {
            $filePath = $publicPath;
        } else {
            abort(404);
        }
    }
    
    $mimeType = mime_content_type($filePath);
    
    return response()->file($filePath, [
        'Content-Type' => $mimeType,
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET, OPTIONS',
        'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept, Authorization',
    ]);
})->where('path', '.*'); 