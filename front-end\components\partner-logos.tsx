"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"

interface Partner {
  name: string
  logo: string
  website: string
  category: "corporate" | "ngo" | "government" | "academic"
}

const partners: Partner[] = [
  {
    name: "Global Health Initiative",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "ngo",
  },
  {
    name: "EcoBank Nigeria",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "corporate",
  },
  {
    name: "Ministry of Health",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "government",
  },
  {
    name: "University of Lagos",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "academic",
  },
  {
    name: "Tech4Good Foundation",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "ngo",
  },
  {
    name: "Nigerian Breweries",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "corporate",
  },
  {
    name: "Lagos State Government",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "government",
  },
  {
    name: "Pan-Atlantic University",
    logo: "/placeholder.svg?height=100&width=200",
    website: "#",
    category: "academic",
  },
]

export function PartnerLogos() {
  const [activeCategory, setActiveCategory] = useState<string>("all")
  const [imagesLoaded, setImagesLoaded] = useState<Record<string, boolean>>({})

  const handleImageLoad = (name: string) => {
    setImagesLoaded((prev) => ({ ...prev, [name]: true }))
  }

  const filteredPartners =
    activeCategory === "all" ? partners : partners.filter((partner) => partner.category === activeCategory)

  const categories = [
    { id: "all", label: "All Partners" },
    { id: "corporate", label: "Corporate" },
    { id: "ngo", label: "NGOs" },
    { id: "government", label: "Government" },
    { id: "academic", label: "Academic" },
  ]

  return (
    <div>
      <div className="flex flex-wrap justify-center gap-2 mb-8">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeCategory === category.id
                ? "bg-green-600 text-white shadow-md"
                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800/30"
            }`}
          >
            {category.label}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {filteredPartners.map((partner) => (
          <Link key={partner.name} href={partner.website} target="_blank" rel="noopener noreferrer" className="group">
            <Card className="h-full overflow-hidden border border-green-100 dark:border-green-900/50 hover:border-green-300 dark:hover:border-green-700 transition-all duration-300 hover:shadow-md group-hover:-translate-y-1 bg-white dark:bg-gray-900/50 rounded-xl">
              <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                <div className="relative w-full h-20 mb-4">
                  <div
                    className={`absolute inset-0 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-md ${
                      imagesLoaded[partner.name] ? "opacity-0" : "opacity-100"
                    }`}
                  />
                  <Image
                    src={partner.logo || "/placeholder.svg"}
                    alt={partner.name}
                    fill
                    sizes="(max-width: 768px) 50vw, 25vw"
                    className={`object-contain transition-all duration-300 group-hover:scale-105 ${
                      imagesLoaded[partner.name] ? "opacity-100" : "opacity-0"
                    }`}
                    onLoad={() => handleImageLoad(partner.name)}
                  />
                </div>
                <div className="text-center">
                  <h3 className="font-medium text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                    {partner.name}
                  </h3>
                  <p className="text-xs text-muted-foreground mt-1 capitalize">{partner.category}</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}
