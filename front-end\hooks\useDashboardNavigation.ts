import { useMemo } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from './useAuth'
import { useRoleAccess } from './useRoleAccess'

export interface NavigationItem {
  id: string
  label: string
  href: string
  icon: string
  badge?: string | number
  isActive?: boolean
  children?: NavigationItem[]
  permission?: string
}

export interface NavigationSection {
  id: string
  title: string
  items: NavigationItem[]
}

/**
 * Hook for managing dashboard navigation based on user role
 */
export function useDashboardNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const { user } = useAuth()
  const { permissions, roleInfo } = useRoleAccess()

  /**
   * Check if a navigation item should be active
   */
  const isActiveItem = (href: string): boolean => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }

  /**
   * Get navigation sections based on user role
   */
  const navigationSections = useMemo((): NavigationSection[] => {
    if (!user || !roleInfo) return []

    // Admin Navigation
    if (roleInfo.isAdmin) {
      return [
        {
          id: 'main',
          title: 'Main',
          items: [
            {
              id: 'dashboard',
              label: 'Dashboard',
              href: '/admin/dashboard',
              icon: 'Home',
              isActive: isActiveItem('/admin/dashboard')
            },
            {
              id: 'analytics',
              label: 'Analytics',
              href: '/admin/analytics',
              icon: 'BarChart3',
              isActive: isActiveItem('/admin/analytics')
            }
          ]
        },
        {
          id: 'scholarships',
          title: 'Scholarships',
          items: [
            {
              id: 'scholarships',
              label: 'All Scholarships',
              href: '/admin/scholarships',
              icon: 'Award',
              isActive: isActiveItem('/admin/scholarships')
            },
            {
              id: 'applications',
              label: 'Applications',
              href: '/admin/applications',
              icon: 'FileText',
              isActive: isActiveItem('/admin/applications')
            },
            {
              id: 'categories',
              label: 'Categories',
              href: '/admin/scholarship-categories',
              icon: 'BookOpen',
              isActive: isActiveItem('/admin/scholarship-categories')
            }
          ]
        },
        {
          id: 'users',
          title: 'User Management',
          items: [
            {
              id: 'users',
              label: 'All Users',
              href: '/admin/users',
              icon: 'Users',
              isActive: isActiveItem('/admin/users')
            },
            {
              id: 'partners',
              label: 'Partner Organizations',
              href: '/admin/partners',
              icon: 'Building',
              isActive: isActiveItem('/admin/partners')
            },
            {
              id: 'students',
              label: 'Students',
              href: '/admin/students',
              icon: 'GraduationCap',
              isActive: isActiveItem('/admin/students')
            }
          ]
        },
        {
          id: 'system',
          title: 'System',
          items: [
            {
              id: 'settings',
              label: 'Settings',
              href: '/admin/settings',
              icon: 'Settings',
              isActive: isActiveItem('/admin/settings')
            }
          ]
        }
      ]
    }

    // Partner Organization Navigation
    if (roleInfo.isPartnerOrganization) {
      return [
        {
          id: 'main',
          title: 'Main',
          items: [
            {
              id: 'dashboard',
              label: 'Dashboard',
              href: '/dashboard/partner',
              icon: 'Home',
              isActive: isActiveItem('/dashboard/partner') && pathname === '/dashboard/partner'
            }
          ]
        },
        {
          id: 'students',
          title: 'Student Management',
          items: [
            {
              id: 'students',
              label: 'All Students',
              href: '/dashboard/partner/students',
              icon: 'Users',
              isActive: isActiveItem('/dashboard/partner/students')
            },
            {
              id: 'progression',
              label: 'Student Progression',
              href: '/dashboard/partner/progression',
              icon: 'TrendingUp',
              isActive: isActiveItem('/dashboard/partner/progression')
            }
          ]
        },
        {
          id: 'scholarships',
          title: 'Scholarships',
          items: [
            {
              id: 'applications',
              label: 'Applications',
              href: '/dashboard/partner/applications',
              icon: 'FileText',
              isActive: isActiveItem('/dashboard/partner/applications')
            },
            {
              id: 'available',
              label: 'Available Scholarships',
              href: '/scholarships?category=primary,secondary',
              icon: 'Award',
              isActive: pathname.startsWith('/scholarships') && 
                       (pathname.includes('primary') || pathname.includes('secondary'))
            }
          ]
        },
        {
          id: 'reports',
          title: 'Reports',
          items: [
            {
              id: 'reports',
              label: 'Analytics',
              href: '/dashboard/partner/reports',
              icon: 'BarChart3',
              isActive: isActiveItem('/dashboard/partner/reports')
            }
          ]
        }
      ]
    }

    // Individual Student Navigation
    if (roleInfo.isIndividualStudent) {
      return [
        {
          id: 'main',
          title: 'Main',
          items: [
            {
              id: 'dashboard',
              label: 'Dashboard',
              href: '/dashboard/student',
              icon: 'Home',
              isActive: isActiveItem('/dashboard/student') && pathname === '/dashboard/student'
            }
          ]
        },
        {
          id: 'scholarships',
          title: 'Scholarships',
          items: [
            {
              id: 'applications',
              label: 'My Applications',
              href: '/dashboard/student/applications',
              icon: 'FileText',
              isActive: isActiveItem('/dashboard/student/applications')
            },
            {
              id: 'available',
              label: 'Available Scholarships',
              href: '/scholarships?category=university',
              icon: 'Award',
              isActive: pathname.startsWith('/scholarships') && pathname.includes('university')
            }
          ]
        },
        {
          id: 'profile',
          title: 'Account',
          items: [
            {
              id: 'profile',
              label: 'Profile',
              href: '/dashboard/student/profile',
              icon: 'User',
              isActive: isActiveItem('/dashboard/student/profile')
            }
          ]
        }
      ]
    }

    return []
  }, [user, roleInfo, pathname])

  /**
   * Get breadcrumb items for current page
   */
  const breadcrumbs = useMemo(() => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbItems = []

    // Add home/dashboard
    if (roleInfo?.isAdmin) {
      breadcrumbItems.push({ label: 'Admin', href: '/admin/dashboard' })
    } else if (roleInfo?.isPartnerOrganization) {
      breadcrumbItems.push({ label: 'Partner Dashboard', href: '/dashboard/partner' })
    } else if (roleInfo?.isIndividualStudent) {
      breadcrumbItems.push({ label: 'Student Dashboard', href: '/dashboard/student' })
    }

    // Add path segments
    let currentPath = ''
    for (let i = 1; i < pathSegments.length; i++) {
      currentPath += '/' + pathSegments[i]
      const segment = pathSegments[i]
      
      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      breadcrumbItems.push({
        label,
        href: i === pathSegments.length - 1 ? undefined : currentPath
      })
    }

    return breadcrumbItems
  }, [pathname, roleInfo])

  /**
   * Navigate to a specific route
   */
  const navigateTo = (href: string) => {
    router.push(href)
  }

  /**
   * Get appropriate dashboard URL for user
   */
  const getDashboardUrl = () => {
    if (roleInfo?.isAdmin) return '/admin/dashboard'
    if (roleInfo?.isPartnerOrganization) return '/dashboard/partner'
    if (roleInfo?.isIndividualStudent) return '/dashboard/student'
    return '/dashboard'
  }

  /**
   * Check if user can access a specific route
   */
  const canAccessRoute = (href: string): boolean => {
    // Admin routes
    if (href.startsWith('/admin/')) {
      return roleInfo?.isAdmin || false
    }

    // Partner routes
    if (href.startsWith('/dashboard/partner/')) {
      return roleInfo?.isPartnerOrganization || false
    }

    // Student routes
    if (href.startsWith('/dashboard/student/')) {
      return roleInfo?.isIndividualStudent || false
    }

    // Public routes
    return true
  }

  return {
    navigationSections,
    breadcrumbs,
    currentPath: pathname,
    isActiveItem,
    navigateTo,
    getDashboardUrl,
    canAccessRoute,
    user,
    roleInfo,
    permissions
  }
}
