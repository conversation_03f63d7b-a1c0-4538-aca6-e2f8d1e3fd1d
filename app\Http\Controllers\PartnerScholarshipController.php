<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\PartnerStudent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PartnerScholarshipController extends Controller
{
    /**
     * Display available scholarships for partner organizations.
     */
    public function index(Request $request)
    {
        $partner = Auth::user();
        
        // Ensure user is a partner organization
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get only Primary and Secondary scholarships that are open
        $query = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->whereIn('category', ['primary', 'secondary'])
            ->where('application_deadline', '>', now());

        // Filter by category if specified
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $scholarships = $query->orderBy('application_deadline', 'asc')->paginate(12);

        // Get statistics
        $stats = [
            'total_available' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->whereIn('category', ['primary', 'secondary'])
                ->where('application_deadline', '>', now())
                ->count(),
            'primary_scholarships' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'primary')
                ->where('application_deadline', '>', now())
                ->count(),
            'secondary_scholarships' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'secondary')
                ->where('application_deadline', '>', now())
                ->count(),
            'my_applications' => ScholarshipApplication::where('user_id', $partner->id)
                ->orWhere('school_id', $partner->id)
                ->count(),
        ];

        return view('partner.scholarships.index', compact('scholarships', 'stats'));
    }

    /**
     * Show the form for creating a new scholarship application.
     */
    public function create(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get the scholarship
        $scholarshipId = $request->get('scholarship_id');
        if (!$scholarshipId) {
            return redirect()->route('partner.scholarships.index')
                ->with('error', 'Please select a scholarship to apply for.');
        }

        $scholarship = Scholarship::where('id', $scholarshipId)
            ->where('status', 'open')
            ->where('is_open', true)
            ->whereIn('category', ['primary', 'secondary'])
            ->where('application_deadline', '>', now())
            ->firstOrFail();

        // Get partner's students
        $students = PartnerStudent::where('partner_id', $partner->id)
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        if ($students->isEmpty()) {
            return redirect()->route('partner.students.create')
                ->with('info', 'Please add students to your organization before applying for scholarships.');
        }

        // Get scholarship fields/requirements
        $scholarshipFields = $scholarship->activeFields ?? collect();
        $customFields = $scholarship->custom_fields ?? [];
        $documentsRequired = $scholarship->documents_required ?? [];

        return view('partner.scholarships.create', compact(
            'scholarship', 
            'students', 
            'scholarshipFields', 
            'customFields', 
            'documentsRequired'
        ));
    }

    /**
     * Store a newly created scholarship application.
     */
    public function store(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Validate basic requirements
        $validator = Validator::make($request->all(), [
            'scholarship_id' => 'required|exists:scholarships,id',
            'student_id' => 'required|exists:partner_students,id',
            'guardian_name' => 'required|string|max:255',
            'guardian_phone' => 'required|string|max:20',
            'guardian_email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $scholarship = Scholarship::findOrFail($request->scholarship_id);
        $student = PartnerStudent::where('id', $request->student_id)
            ->where('partner_id', $partner->id)
            ->firstOrFail();

        // Check if application deadline has passed
        if ($scholarship->application_deadline <= now()) {
            return redirect()->back()
                ->with('error', 'This scholarship application deadline has passed.')
                ->withInput();
        }

        // Check if student already has an application for this scholarship
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where('student_id', $student->id)
            ->first();

        if ($existingApplication) {
            return redirect()->back()
                ->with('error', 'This student has already applied for this scholarship.')
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Handle file uploads
            $uploadedFiles = [];
            if ($request->hasFile('documents')) {
                foreach ($request->file('documents') as $key => $file) {
                    if ($file && $file->isValid()) {
                        $filename = time() . '_' . $key . '_' . $file->getClientOriginalName();
                        $path = $file->storeAs('scholarship_applications', $filename, 'public');
                        $uploadedFiles[$key] = $path;
                    }
                }
            }

            // Prepare application data
            $applicationData = [
                'student_name' => $student->name,
                'student_class' => $student->class,
                'student_age' => $student->age,
                'student_gender' => $student->gender,
                'parent_name' => $student->parent_name,
                'parent_contact' => $student->parent_contact,
                'guardian_name' => $request->guardian_name,
                'guardian_phone' => $request->guardian_phone,
                'guardian_email' => $request->guardian_email,
                'school_name' => $partner->organization_name ?? $partner->name,
                'school_address' => $partner->organization_address ?? '',
            ];

            // Add custom field data
            foreach ($request->all() as $key => $value) {
                if (strpos($key, 'custom_') === 0) {
                    $applicationData[$key] = $value;
                }
            }

            // Create the application
            $application = ScholarshipApplication::create([
                'scholarship_id' => $scholarship->id,
                'user_id' => $partner->id,
                'student_id' => $student->id,
                'school_id' => $partner->id,
                'application_type' => 'institutional',
                'grade_level' => $student->class,
                'guardian_name' => $request->guardian_name,
                'guardian_phone' => $request->guardian_phone,
                'guardian_email' => $request->guardian_email,
                'application_data' => $applicationData,
                'uploaded_files' => $uploadedFiles,
                'status' => 'pending',
                'submitted_at' => now(),
            ]);

            // Update scholarship application count
            $scholarship->increment('current_applicants');

            // Update student's application history
            $applicationHistory = $student->application_history ?? [];
            $applicationHistory[] = [
                'scholarship_id' => $scholarship->id,
                'scholarship_title' => $scholarship->title,
                'application_id' => $application->id,
                'applied_at' => now()->toDateTimeString(),
                'status' => 'pending'
            ];
            $student->update(['application_history' => $applicationHistory]);

            DB::commit();

            return redirect()->route('partner.applications.index')
                ->with('success', 'Scholarship application submitted successfully for ' . $student->name . '.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->with('error', 'An error occurred while submitting the application. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the specified scholarship details.
     */
    public function show($id)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $scholarship = Scholarship::where('id', $id)
            ->whereIn('category', ['primary', 'secondary'])
            ->firstOrFail();

        // Get scholarship fields/requirements
        $scholarshipFields = $scholarship->activeFields ?? collect();
        $customFields = $scholarship->custom_fields ?? [];
        $documentsRequired = $scholarship->documents_required ?? [];

        // Check if partner has any applications for this scholarship
        $applications = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where(function($query) use ($partner) {
                $query->where('user_id', $partner->id)
                      ->orWhere('school_id', $partner->id);
            })
            ->with('student')
            ->get();

        return view('partner.scholarships.show', compact(
            'scholarship', 
            'scholarshipFields', 
            'customFields', 
            'documentsRequired',
            'applications'
        ));
    }
}
