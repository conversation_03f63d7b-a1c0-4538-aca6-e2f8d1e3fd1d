<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ScholarshipApplicationController extends Controller
{
    /**
     * Display scholarship application form
     */
    public function create(Request $request, $scholarshipId)
    {
        $scholarship = Scholarship::where('id', $scholarshipId)
            ->where('status', 'active')
            ->where('is_open', true)
            ->where('application_deadline', '>', now())
            ->firstOrFail();

        $user = Auth::user();
        
        // Check if user has already applied
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingApplication) {
            return redirect()->route('scholarships.show', $scholarship->id)
                ->with('error', 'You have already applied for this scholarship.');
        }

        // Check role-based access
        $this->checkRoleAccess($user, $scholarship);

        return view('scholarships.apply', compact('scholarship', 'user'));
    }

    /**
     * Store scholarship application
     */
    public function store(Request $request, $scholarshipId)
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $user = Auth::user();

        // Check if scholarship is still open
        if (!$scholarship->isOpen() || $scholarship->application_deadline <= now()) {
            return redirect()->back()
                ->with('error', 'This scholarship is no longer accepting applications.');
        }

        // Check if user has already applied
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingApplication) {
            return redirect()->route('scholarships.show', $scholarship->id)
                ->with('error', 'You have already applied for this scholarship.');
        }

        // Validate based on scholarship category
        $validator = $this->getValidationRules($scholarship->category);
        $validatedData = $request->validate($validator);

        DB::beginTransaction();
        try {
            // Handle file uploads
            $uploadedFiles = $this->handleFileUploads($request, $scholarship);

            // Prepare form data based on category
            $formData = $this->prepareFormData($validatedData, $scholarship->category);

            // Create application
            $application = ScholarshipApplication::create([
                'scholarship_id' => $scholarship->id,
                'user_id' => $user->id,
                'form_data' => $formData,
                'uploaded_files' => $uploadedFiles,
                'status' => 'pending',
                'submitted_at' => now(),
            ]);

            DB::commit();

            return redirect()->route('scholarships.show', $scholarship->id)
                ->with('success', 'Your scholarship application has been submitted successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            
            // Clean up uploaded files if application creation failed
            if (!empty($uploadedFiles)) {
                foreach ($uploadedFiles as $filePath) {
                    Storage::disk('public')->delete($filePath);
                }
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'An error occurred while submitting your application. Please try again.');
        }
    }

    /**
     * Check role-based access to scholarship categories
     */
    private function checkRoleAccess($user, $scholarship)
    {
        switch ($scholarship->category) {
            case 'primary':
            case 'secondary':
                if (!$user->isPartnerOrganization()) {
                    abort(403, 'Only partner organizations can apply for Primary and Secondary scholarships.');
                }
                break;
            case 'university':
                if (!$user->isUniversityStudent()) {
                    abort(403, 'Only university students can apply for University scholarships.');
                }
                break;
            default:
                abort(403, 'Invalid scholarship category.');
        }
    }

    /**
     * Get validation rules based on scholarship category
     */
    private function getValidationRules($category)
    {
        $baseRules = [
            'reason_for_scholarship' => 'required|string|min:50|max:1000',
        ];

        switch ($category) {
            case 'primary':
                return array_merge($baseRules, [
                    // Student Information
                    'student_full_name' => 'required|string|max:255',
                    'student_age' => 'required|integer|min:5|max:15',
                    'student_gender' => 'required|in:male,female',
                    'student_grade' => 'required|string|max:50',
                    'student_date_of_birth' => 'required|date|before:today',
                    
                    // Parent/Guardian Information
                    'parent_name' => 'required|string|max:255',
                    'parent_phone' => 'required|string|max:20',
                    'parent_email' => 'nullable|email|max:255',
                    'parent_relationship' => 'required|string|max:100',
                    'home_address' => 'required|string|max:500',
                    
                    // School Information
                    'school_account_number' => 'required|string|max:100',
                    'headmaster_name' => 'required|string|max:255',
                    'current_school_fee' => 'required|numeric|min:0',
                    
                    // Application Details
                    'financial_need_explanation' => 'required|string|min:50|max:1000',
                    'supporting_information' => 'nullable|string|max:1000',
                    
                    // File uploads
                    'student_photo' => 'required|image|mimes:jpeg,png,jpg|max:2048',
                    'parent_id' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'school_fee_receipt' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
                ]);

            case 'secondary':
                return array_merge($baseRules, [
                    // Student Information
                    'student_full_name' => 'required|string|max:255',
                    'student_age' => 'required|integer|min:10|max:20',
                    'student_gender' => 'required|in:male,female',
                    'student_grade' => 'required|string|max:50',
                    'student_date_of_birth' => 'required|date|before:today',
                    'student_phone' => 'required|string|max:20',
                    'student_email' => 'nullable|email|max:255',
                    
                    // Parent/Guardian Information
                    'parent_name' => 'required|string|max:255',
                    'parent_phone' => 'required|string|max:20',
                    'parent_email' => 'nullable|email|max:255',
                    'home_address' => 'required|string|max:500',
                    
                    // School Information
                    'principal_name' => 'required|string|max:255',
                    'principal_phone' => 'required|string|max:20',
                    'school_account_number' => 'required|string|max:100',
                    'current_school_fee' => 'required|numeric|min:0',
                    
                    // Academic Information
                    'current_subjects' => 'required|string|max:500',
                    'academic_performance' => 'required|string|max:500',
                    'career_aspirations' => 'required|string|max:500',
                    
                    // Application Details
                    'financial_need_explanation' => 'required|string|min:50|max:1000',
                    'extracurricular_activities' => 'nullable|string|max:500',
                    
                    // File uploads
                    'student_photo' => 'required|image|mimes:jpeg,png,jpg|max:2048',
                    'student_id' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'academic_transcript' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'principal_recommendation' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
                ]);

            case 'university':
                return array_merge($baseRules, [
                    // Personal Information
                    'full_name' => 'required|string|max:255',
                    'email' => 'required|email|max:255',
                    'phone' => 'required|string|max:20',
                    'date_of_birth' => 'required|date|before:today',
                    'address' => 'required|string|max:500',
                    
                    // Academic Information
                    'institution' => 'required|string|max:255',
                    'matriculation_number' => 'required|string|max:50',
                    'field_of_study' => 'required|string|max:255',
                    'academic_level' => 'required|string|max:100',
                    'current_year' => 'required|string|max:50',
                    'current_gpa' => 'nullable|numeric|min:0|max:5',
                    'expected_graduation' => 'nullable|date|after:today',
                    
                    // Application Details
                    'career_goals' => 'required|string|min:50|max:1000',
                    'academic_achievements' => 'nullable|string|max:1000',
                    'extracurricular_activities' => 'nullable|string|max:1000',
                    'financial_need_explanation' => 'required|string|min:50|max:1000',
                    'how_scholarship_helps' => 'required|string|min:50|max:1000',
                    
                    // References
                    'reference1_name' => 'required|string|max:255',
                    'reference1_position' => 'required|string|max:255',
                    'reference1_email' => 'required|email|max:255',
                    'reference1_phone' => 'required|string|max:20',
                    'reference2_name' => 'nullable|string|max:255',
                    'reference2_position' => 'nullable|string|max:255',
                    'reference2_email' => 'nullable|email|max:255',
                    'reference2_phone' => 'nullable|string|max:20',
                    
                    // File uploads
                    'student_photo' => 'required|image|mimes:jpeg,png,jpg|max:2048',
                    'student_id_card' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'academic_transcript' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'recommendation_letter' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
                    'additional_documents' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
                ]);

            default:
                return $baseRules;
        }
    }

    /**
     * Handle file uploads
     */
    private function handleFileUploads(Request $request, Scholarship $scholarship)
    {
        $uploadedFiles = [];
        $fileFields = $this->getFileFields($scholarship->category);

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                if ($file && $file->isValid()) {
                    $filename = time() . '_' . $field . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('scholarship_applications', $filename, 'public');
                    $uploadedFiles[$field] = $path;
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Get file fields based on category
     */
    private function getFileFields($category)
    {
        switch ($category) {
            case 'primary':
                return ['student_photo', 'parent_id', 'school_fee_receipt'];
            case 'secondary':
                return ['student_photo', 'student_id', 'academic_transcript', 'principal_recommendation'];
            case 'university':
                return ['student_photo', 'student_id_card', 'academic_transcript', 'recommendation_letter', 'additional_documents'];
            default:
                return [];
        }
    }

    /**
     * Prepare form data based on category
     */
    private function prepareFormData($validatedData, $category)
    {
        // Remove file fields from form data as they're stored separately
        $fileFields = $this->getFileFields($category);
        foreach ($fileFields as $field) {
            unset($validatedData[$field]);
        }

        return $validatedData;
    }
}
