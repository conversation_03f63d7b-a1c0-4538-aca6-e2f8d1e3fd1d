<?php

namespace Database\Factories;

use App\Models\Student;
use App\Models\User;
use App\Models\PartnerOrganization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Student::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'school_id' => PartnerOrganization::factory()->school(),
            'student_id' => $this->faker->unique()->numerify('STU####'),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'middle_name' => $this->faker->optional()->firstName(),
            'date_of_birth' => $this->faker->date('Y-m-d', '-10 years'),
            'gender' => $this->faker->randomElement(['male', 'female', 'other']),
            'phone_number' => $this->faker->optional()->phoneNumber(),
            'email' => $this->faker->optional()->safeEmail(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => 'Nigeria',
            'current_grade' => $this->faker->randomElement([
                'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                'university_1', 'university_2', 'university_3', 'university_4'
            ]),
            'academic_year' => $this->faker->randomElement(['2023/2024', '2024/2025', '2025/2026']),
            'student_type' => $this->faker->randomElement(['primary', 'secondary', 'university']),
            'enrollment_date' => $this->faker->date('Y-m-d', '-2 years'),
            'status' => $this->faker->randomElement(['active', 'inactive', 'graduated', 'transferred']),
            'guardian_name' => $this->faker->name(),
            'guardian_phone' => $this->faker->phoneNumber(),
            'guardian_email' => $this->faker->optional()->safeEmail(),
            'guardian_relationship' => $this->faker->randomElement(['father', 'mother', 'guardian', 'uncle', 'aunt', 'grandparent']),
            'matriculation_number' => $this->faker->optional()->numerify('MAT/####/####'),
            'graduation_date' => $this->faker->optional()->date('Y-m-d', '+2 years'),
            'additional_info' => $this->faker->optional()->randomElement([
                json_encode(['notes' => 'Excellent student', 'interests' => ['science', 'math']]),
                json_encode(['medical_info' => 'No known allergies']),
                null
            ]),
        ];
    }

    /**
     * Indicate that the student is in primary school.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->randomElement(['primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6']),
            'student_type' => 'primary',
            'school_id' => PartnerOrganization::factory()->school(),
        ]);
    }

    /**
     * Indicate that the student is in secondary school.
     */
    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->randomElement(['secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6']),
            'student_type' => 'secondary',
            'school_id' => PartnerOrganization::factory()->school(),
        ]);
    }

    /**
     * Indicate that the student is in university.
     */
    public function university(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->randomElement(['university_1', 'university_2', 'university_3', 'university_4']),
            'student_type' => 'university',
            'school_id' => PartnerOrganization::factory()->university(),
            'matriculation_number' => $this->faker->numerify('MAT/####/####'),
        ]);
    }

    /**
     * Indicate that the student is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }
}
