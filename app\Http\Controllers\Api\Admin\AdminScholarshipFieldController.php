<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\ScholarshipField;
use App\Models\Scholarship;
use App\Models\ScholarshipTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AdminScholarshipFieldController extends Controller
{
    /**
     * Get all fields for a scholarship with admin details
     */
    public function index($scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::with(['fields' => function($query) {
            $query->orderBy('field_order');
        }])->findOrFail($scholarshipId);
        
        $fields = $scholarship->fields;
        
        // Add usage statistics for each field
        $fieldsWithStats = $fields->map(function ($field) {
            $field->usage_count = $this->getFieldUsageCount($field);
            return $field;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'scholarship' => $scholarship->only(['id', 'title', 'category', 'status']),
                'fields' => $fieldsWithStats,
                'total_fields' => $fields->count(),
                'active_fields' => $fields->where('is_active', true)->count(),
                'inactive_fields' => $fields->where('is_active', false)->count(),
            ]
        ]);
    }

    /**
     * Store a new field for a scholarship
     */
    public function store(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'field_name' => 'required|string|max:100|regex:/^[a-zA-Z0-9_]+$/',
            'field_label' => 'required|string|max:200',
            'field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'field_options' => 'nullable|array',
            'field_options.placeholder' => 'nullable|string|max:255',
            'field_options.help_text' => 'nullable|string|max:500',
            'field_options.validation_rules' => 'nullable|array',
            'field_options.options' => 'required_if:field_type,select|array',
            'is_required' => 'boolean',
            'field_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate field names
        $existingField = $scholarship->fields()->where('field_name', $request->field_name)->first();
        if ($existingField) {
            return response()->json([
                'success' => false,
                'message' => 'A field with this name already exists for this scholarship'
            ], 422);
        }

        $data = $validator->validated();
        $data['scholarship_id'] = $scholarship->id;
        
        // Auto-generate field_order if not provided
        if (!isset($data['field_order'])) {
            $maxOrder = $scholarship->fields()->max('field_order') ?? 0;
            $data['field_order'] = $maxOrder + 1;
        }

        $field = ScholarshipField::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Field created successfully',
            'data' => $field
        ], 201);
    }

    /**
     * Show a specific field with detailed information
     */
    public function show($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        // Add usage statistics
        $field->usage_count = $this->getFieldUsageCount($field);
        $field->recent_applications = $this->getRecentApplicationsForField($field);

        return response()->json([
            'success' => true,
            'data' => $field
        ]);
    }

    /**
     * Update a field
     */
    public function update(Request $request, $scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        $validator = Validator::make($request->all(), [
            'field_name' => 'string|max:100|regex:/^[a-zA-Z0-9_]+$/',
            'field_label' => 'string|max:200',
            'field_type' => 'string|in:text,textarea,email,tel,number,date,file,select',
            'field_options' => 'nullable|array',
            'field_options.placeholder' => 'nullable|string|max:255',
            'field_options.help_text' => 'nullable|string|max:500',
            'field_options.validation_rules' => 'nullable|array',
            'field_options.options' => 'required_if:field_type,select|array',
            'is_required' => 'boolean',
            'field_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate field names (excluding current field)
        if ($request->has('field_name') && $request->field_name !== $field->field_name) {
            $existingField = $scholarship->fields()
                ->where('field_name', $request->field_name)
                ->where('id', '!=', $field->id)
                ->first();
            
            if ($existingField) {
                return response()->json([
                    'success' => false,
                    'message' => 'A field with this name already exists for this scholarship'
                ], 422);
            }
        }

        $field->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Field updated successfully',
            'data' => $field->fresh()
        ]);
    }

    /**
     * Delete a field
     */
    public function destroy($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        // Check if field is being used in applications
        $usageCount = $this->getFieldUsageCount($field);
        if ($usageCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete field. It is being used in {$usageCount} application(s). Consider deactivating it instead."
            ], 422);
        }

        $field->delete();

        return response()->json([
            'success' => true,
            'message' => 'Field deleted successfully'
        ]);
    }

    /**
     * Reorder fields
     */
    public function reorder(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'fields' => 'required|array',
            'fields.*.id' => 'required|integer|exists:scholarship_fields,id',
            'fields.*.field_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::transaction(function () use ($request, $scholarship) {
            foreach ($request->fields as $fieldData) {
                $field = $scholarship->fields()->find($fieldData['id']);
                if ($field) {
                    $field->update(['field_order' => $fieldData['field_order']]);
                }
            }
        });

        return response()->json([
            'success' => true,
            'message' => 'Fields reordered successfully'
        ]);
    }

    /**
     * Toggle field active status
     */
    public function toggleActive($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        $field->update(['is_active' => !$field->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Field status updated successfully',
            'data' => [
                'field_id' => $field->id,
                'is_active' => $field->is_active
            ]
        ]);
    }

    /**
     * Bulk create fields from template
     */
    public function bulkCreate(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'template_id' => 'nullable|integer|exists:scholarship_templates,id',
            'fields' => 'required_without:template_id|array|min:1',
            'fields.*.field_name' => 'required|string|max:100|regex:/^[a-zA-Z0-9_]+$/',
            'fields.*.field_label' => 'required|string|max:200',
            'fields.*.field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'fields.*.field_options' => 'nullable|array',
            'fields.*.is_required' => 'boolean',
            'replace_existing' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $fieldsData = [];
        
        if ($request->template_id) {
            $template = ScholarshipTemplate::findOrFail($request->template_id);
            $fieldsData = $template->default_fields;
        } else {
            $fieldsData = $request->fields;
        }

        // Check for duplicate field names
        $existingFieldNames = $scholarship->fields()->pluck('field_name')->toArray();
        $newFieldNames = collect($fieldsData)->pluck('field_name')->toArray();
        $duplicates = array_intersect($existingFieldNames, $newFieldNames);

        if (!empty($duplicates) && !$request->replace_existing) {
            return response()->json([
                'success' => false,
                'message' => 'Duplicate field names found: ' . implode(', ', $duplicates) . '. Set replace_existing to true to replace them.'
            ], 422);
        }

        DB::transaction(function () use ($scholarship, $fieldsData, $request) {
            if ($request->replace_existing) {
                // Delete existing fields with same names
                $newFieldNames = collect($fieldsData)->pluck('field_name')->toArray();
                $scholarship->fields()->whereIn('field_name', $newFieldNames)->delete();
            }

            $maxOrder = $scholarship->fields()->max('field_order') ?? 0;
            $createdFields = [];

            foreach ($fieldsData as $index => $fieldData) {
                $fieldData['scholarship_id'] = $scholarship->id;
                $fieldData['field_order'] = $maxOrder + $index + 1;
                $fieldData['is_active'] = $fieldData['is_active'] ?? true;
                
                $field = ScholarshipField::create($fieldData);
                $createdFields[] = $field;
            }

            return $createdFields;
        });

        return response()->json([
            'success' => true,
            'message' => count($fieldsData) . ' fields created successfully',
            'data' => $scholarship->fields()->ordered()->get()
        ], 201);
    }

    /**
     * Get field usage count in applications
     */
    private function getFieldUsageCount(ScholarshipField $field): int
    {
        return $field->scholarship->applications()
            ->whereJsonContains('form_data', [$field->field_name => null])
            ->orWhereJsonLength('form_data->' . $field->field_name, '>', 0)
            ->count();
    }

    /**
     * Get recent applications that used this field
     */
    private function getRecentApplicationsForField(ScholarshipField $field, int $limit = 5): array
    {
        return $field->scholarship->applications()
            ->whereJsonContains('form_data', [$field->field_name => null])
            ->orWhereJsonLength('form_data->' . $field->field_name, '>', 0)
            ->with('user:id,name,email')
            ->latest()
            ->limit($limit)
            ->get(['id', 'application_id', 'user_id', 'status', 'submitted_at'])
            ->toArray();
    }
}
