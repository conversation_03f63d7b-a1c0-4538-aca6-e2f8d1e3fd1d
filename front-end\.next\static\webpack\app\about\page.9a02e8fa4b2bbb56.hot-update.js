"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   APPLICATION_STATUS: () => (/* binding */ APPLICATION_STATUS),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   BACKEND_BASE_URL: () => (/* binding */ BACKEND_BASE_URL),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   DONATION_TYPES: () => (/* binding */ DONATION_TYPES),\n/* harmony export */   ENV: () => (/* binding */ ENV),\n/* harmony export */   FEATURES: () => (/* binding */ FEATURES),\n/* harmony export */   FILE_UPLOAD: () => (/* binding */ FILE_UPLOAD),\n/* harmony export */   LAYOUT_CONFIG: () => (/* binding */ LAYOUT_CONFIG),\n/* harmony export */   PAGINATION: () => (/* binding */ PAGINATION),\n/* harmony export */   PAYMENT_METHODS: () => (/* binding */ PAYMENT_METHODS),\n/* harmony export */   SCHOLARSHIP_CATEGORIES: () => (/* binding */ SCHOLARSHIP_CATEGORIES),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_CONFIG: () => (/* binding */ THEME_CONFIG),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   VALIDATION_RULES: () => (/* binding */ VALIDATION_RULES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   validateDonationAmount: () => (/* binding */ validateDonationAmount)\n/* harmony export */ });\n/**\n * Application Constants\n * Centralized configuration for the NGO platform frontend\n */ // =============================================================================\n// API CONFIGURATION\n// =============================================================================\n/**\n * Base API URL for the Laravel backend\n * Change this URL to switch between development, staging, and production\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n/**\n * Laravel backend base URL (without /api/v1)\n * Used for authentication redirects and file uploads\n */ const BACKEND_BASE_URL = \"http://localhost:8000\" || 0;\n/**\n * API endpoints configuration\n */ const API_ENDPOINTS = {\n    // Authentication\n    AUTH: {\n        LOGIN: '/login',\n        REGISTER: '/register',\n        LOGOUT: '/logout',\n        PROFILE: '/profile',\n        REFRESH: '/refresh-token'\n    },\n    // Public endpoints\n    PUBLIC: {\n        SCHOLARSHIPS: '/public-scholarships',\n        BLOG_POSTS: '/blog/posts',\n        EVENTS: '/events',\n        PROGRAMS: '/programs',\n        TEAM: '/team',\n        SETTINGS: '/settings',\n        CONTACT: '/contact',\n        NEWSLETTER: '/newsletter/subscribe',\n        DONATIONS: '/donations',\n        DONATION_CAMPAIGNS: '/donations/campaigns',\n        DONATION_RECEIPT_UPLOAD: '/donations/{id}/upload-receipt',\n        DONATION_VERIFY: '/donations/verify/{reference}'\n    },\n    // Protected endpoints\n    PROTECTED: {\n        DASHBOARD: '/dashboard',\n        APPLICATIONS: '/scholarship-applications',\n        MY_DONATIONS: '/donations/my-donations',\n        PROFILE: '/profile'\n    }\n};\n// =============================================================================\n// APPLICATION INFORMATION\n// =============================================================================\n/**\n * Application name and branding\n */ const APP_CONFIG = {\n    NAME: 'HALIMAKQ Foundation',\n    SHORT_NAME: 'HALIMAKQ',\n    TAGLINE: 'Empowering Communities Through Education',\n    DESCRIPTION: 'A non-governmental organization dedicated to providing educational opportunities and community development programs.',\n    VERSION: '1.0.0'\n};\n/**\n * Contact information\n */ const CONTACT_INFO = {\n    EMAIL: '<EMAIL>',\n    PHONE: '+*********** 7890',\n    ADDRESS: 'Lagos, Nigeria',\n    WEBSITE: 'https://halimakqfoundation.org'\n};\n/**\n * Social media links\n */ const SOCIAL_LINKS = {\n    FACEBOOK: 'https://facebook.com/halimakqfoundation',\n    TWITTER: 'https://twitter.com/halimakqfoundation',\n    INSTAGRAM: 'https://instagram.com/halimakqfoundation',\n    LINKEDIN: 'https://linkedin.com/company/halimakqfoundation',\n    YOUTUBE: 'https://youtube.com/@halimakqfoundation'\n};\n// =============================================================================\n// UI CONFIGURATION\n// =============================================================================\n/**\n * Theme and styling constants\n */ const THEME_CONFIG = {\n    COLORS: {\n        PRIMARY: '#059669',\n        PRIMARY_DARK: '#047857',\n        SECONDARY: '#3B82F6',\n        ACCENT: '#8B5CF6',\n        SUCCESS: '#10B981',\n        WARNING: '#F59E0B',\n        ERROR: '#EF4444'\n    },\n    GRADIENTS: {\n        PRIMARY: 'from-blue-50 via-indigo-50 to-purple-50',\n        HEADER: 'from-blue-600 via-indigo-600 to-purple-600',\n        ADMIN: 'from-green-600 to-green-700'\n    }\n};\n/**\n * Layout configuration\n */ const LAYOUT_CONFIG = {\n    CONTAINER_MAX_WIDTH: 'max-w-7xl',\n    SECTION_PADDING: 'py-16',\n    CARD_SHADOW: 'shadow-lg',\n    BORDER_RADIUS: 'rounded-lg'\n};\n// =============================================================================\n// BUSINESS LOGIC CONSTANTS\n// =============================================================================\n/**\n * Scholarship categories\n */ const SCHOLARSHIP_CATEGORIES = {\n    PRIMARY: 'primary',\n    SECONDARY: 'secondary',\n    UNIVERSITY: 'university'\n};\n/**\n * User roles\n */ const USER_ROLES = {\n    ADMIN: 'admin',\n    STUDENT: 'student',\n    PARTNER: 'partner',\n    NORMAL: 'normal'\n};\n/**\n * Application statuses\n */ const APPLICATION_STATUS = {\n    PENDING: 'pending',\n    UNDER_REVIEW: 'under_review',\n    APPROVED: 'approved',\n    REJECTED: 'rejected',\n    COMPLETED: 'completed'\n};\n/**\n * Payment methods\n */ const PAYMENT_METHODS = {\n    PAYSTACK: 'paystack',\n    BANK_TRANSFER: 'bank_transfer'\n};\n/**\n * Donation types\n */ const DONATION_TYPES = {\n    ONE_TIME: 'one-time',\n    RECURRING: 'recurring'\n};\n// =============================================================================\n// VALIDATION CONSTANTS\n// =============================================================================\n/**\n * Form validation rules\n */ const VALIDATION_RULES = {\n    EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    PHONE_REGEX: /^(\\+234|0)[789][01]\\d{8}$/,\n    PASSWORD_MIN_LENGTH: 8,\n    NAME_MIN_LENGTH: 2,\n    NAME_MAX_LENGTH: 50,\n    MESSAGE_MAX_LENGTH: 1000,\n    // Donation validation\n    DONATION_MIN_AMOUNT: 15000,\n    DONATION_MAX_AMOUNT: ********\n};\n/**\n * File upload constraints\n */ const FILE_UPLOAD = {\n    MAX_SIZE: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'image/jpeg',\n        'image/png',\n        'image/webp',\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.jpg',\n        '.jpeg',\n        '.png',\n        '.webp',\n        '.pdf'\n    ]\n};\n// =============================================================================\n// PAGINATION AND LIMITS\n// =============================================================================\n/**\n * Pagination configuration\n */ const PAGINATION = {\n    DEFAULT_PAGE_SIZE: 10,\n    MAX_PAGE_SIZE: 50,\n    BLOG_POSTS_PER_PAGE: 6,\n    SCHOLARSHIPS_PER_PAGE: 9,\n    EVENTS_PER_PAGE: 8\n};\n// =============================================================================\n// CACHE AND PERFORMANCE\n// =============================================================================\n/**\n * Cache configuration\n */ const CACHE_CONFIG = {\n    DEFAULT_STALE_TIME: 5 * 60 * 1000,\n    DEFAULT_CACHE_TIME: 10 * 60 * 1000,\n    STATIC_DATA_CACHE_TIME: 60 * 60 * 1000\n};\n// =============================================================================\n// ENVIRONMENT HELPERS\n// =============================================================================\n/**\n * Environment detection\n */ const ENV = {\n    IS_DEVELOPMENT: \"development\" === 'development',\n    IS_PRODUCTION: \"development\" === 'production',\n    IS_TEST: \"development\" === 'test'\n};\n/**\n * Feature flags\n */ const FEATURES = {\n    ENABLE_ANALYTICS: \"false\" === 'true',\n    ENABLE_CHAT_SUPPORT: \"false\" === 'true',\n    ENABLE_NOTIFICATIONS: \"false\" === 'true'\n};\n// =============================================================================\n// EXPORT ALL CONSTANTS\n// =============================================================================\n/**\n * Default export with all constants grouped\n */ const CONSTANTS = {\n    API_BASE_URL,\n    BACKEND_BASE_URL,\n    API_ENDPOINTS,\n    APP_CONFIG,\n    CONTACT_INFO,\n    SOCIAL_LINKS,\n    THEME_CONFIG,\n    LAYOUT_CONFIG,\n    SCHOLARSHIP_CATEGORIES,\n    USER_ROLES,\n    APPLICATION_STATUS,\n    PAYMENT_METHODS,\n    DONATION_TYPES,\n    VALIDATION_RULES,\n    FILE_UPLOAD,\n    PAGINATION,\n    CACHE_CONFIG,\n    ENV,\n    FEATURES\n};\n// =============================================================================\n// UTILITY FUNCTIONS\n// =============================================================================\n/**\n * Format currency amount in Nigerian Naira\n */ const formatCurrency = (amount)=>{\n    return \"₦\".concat(amount.toLocaleString('en-NG'));\n};\n/**\n * Validate donation amount\n */ const validateDonationAmount = (amount)=>{\n    if (!amount || amount < VALIDATION_RULES.DONATION_MIN_AMOUNT) {\n        return {\n            isValid: false,\n            error: \"Minimum donation amount is \".concat(formatCurrency(VALIDATION_RULES.DONATION_MIN_AMOUNT))\n        };\n    }\n    if (amount > VALIDATION_RULES.DONATION_MAX_AMOUNT) {\n        return {\n            isValid: false,\n            error: \"Maximum donation amount is \".concat(formatCurrency(VALIDATION_RULES.DONATION_MAX_AMOUNT))\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CONSTANTS);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/constants.ts\n"));

/***/ })

});