<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('volunteer_training_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('volunteer_id')->constrained()->onDelete('cascade');
            $table->foreignId('training_module_id')->constrained('volunteer_training_modules')->onDelete('cascade');
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->boolean('is_completed')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->integer('time_spent_minutes')->default(0);
            $table->decimal('quiz_score', 5, 2)->nullable();
            $table->integer('attempts')->default(0);
            $table->timestamps();

            $table->unique(['volunteer_id', 'training_module_id'], 'vol_training_progress_unique');
            $table->index(['volunteer_id', 'is_completed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('volunteer_training_progress');
    }
};
