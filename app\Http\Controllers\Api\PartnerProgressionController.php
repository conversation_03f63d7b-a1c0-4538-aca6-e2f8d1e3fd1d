<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PartnerProgressionController extends Controller
{
    /**
     * Get progression statistics for partner organization
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            // Get progression statistics
            $totalProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->count();

            $pendingProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->where('status', 'pending')->count();

            $approvedProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->where('status', 'approved')->count();

            $completedProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->where('status', 'completed')->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_progressions' => $totalProgressions,
                    'pending_progressions' => $pendingProgressions,
                    'approved_progressions' => $approvedProgressions,
                    'completed_progressions' => $completedProgressions,
                    'current_academic_year' => '2024/2025',
                ],
                'message' => 'Statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get paginated list of progressions for partner organization
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $query = StudentProgression::whereHas('student', function ($q) use ($partnerOrg) {
                $q->where('school_id', $partnerOrg->id);
            })->with(['student']);

            // Apply filters
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->whereHas('student', function ($q) use ($search) {
                    $q->where('full_name', 'like', "%{$search}%")
                      ->orWhere('student_id', 'like', "%{$search}%");
                });
            }

            if ($request->has('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            if ($request->has('grade') && $request->grade !== 'all') {
                $query->where('from_grade', $request->grade)
                      ->orWhere('to_grade', $request->grade);
            }

            if ($request->has('academic_year') && !empty($request->academic_year)) {
                $query->where('academic_year', $request->academic_year);
            }

            // Order by most recent first
            $query->orderBy('created_at', 'desc');

            // Paginate results
            $perPage = $request->get('per_page', 15);
            $progressions = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $progressions,
                'message' => 'Progressions retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve progressions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get students eligible for progression
     */
    public function getEligibleStudents(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            // Get students who haven't been progressed this academic year
            $currentAcademicYear = '2024/2025';
            
            $eligibleStudents = Student::where('school_id', $partnerOrg->id)
                ->whereNotIn('id', function ($query) use ($currentAcademicYear) {
                    $query->select('student_id')
                          ->from('student_progressions')
                          ->where('academic_year', $currentAcademicYear)
                          ->where('status', '!=', 'failed');
                })
                ->where('status', 'active')
                ->get()
                ->map(function ($student) {
                    return [
                        'id' => $student->id,
                        'student_id' => $student->student_id,
                        'full_name' => $student->full_name,
                        'current_grade' => $student->grade_level,
                        'next_grade' => $this->getNextGrade($student->grade_level),
                        'academic_year' => '2024/2025',
                        'eligible_for_progression' => true,
                        'scholarship_eligible' => $this->isScholarshipEligible($student->grade_level),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $eligibleStudents,
                'message' => 'Eligible students retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve eligible students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new progression
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'to_grade' => 'required|string',
                'academic_year' => 'required|string',
                'progression_date' => 'required|date',
                'notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Verify student belongs to this partner organization
            $student = Student::where('id', $request->student_id)
                             ->where('school_id', $partnerOrg->id)
                             ->firstOrFail();

            // Check if progression already exists for this academic year
            $existingProgression = StudentProgression::where('student_id', $student->id)
                                                   ->where('academic_year', $request->academic_year)
                                                   ->first();

            if ($existingProgression) {
                return response()->json([
                    'success' => false,
                    'message' => 'Progression already exists for this student in the specified academic year'
                ], 409);
            }

            // Create progression
            $progression = StudentProgression::create([
                'student_id' => $student->id,
                'from_grade' => $student->grade_level,
                'to_grade' => $request->to_grade,
                'academic_year' => $request->academic_year,
                'progression_date' => $request->progression_date,
                'status' => 'pending',
                'notes' => $request->notes,
            ]);

            $progression->load('student');

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific progression
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->with(['student'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update progression
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            $validator = Validator::make($request->all(), [
                'to_grade' => 'sometimes|required|string',
                'academic_year' => 'sometimes|required|string',
                'progression_date' => 'sometimes|required|date',
                'notes' => 'nullable|string|max:1000',
                'status' => 'sometimes|required|in:pending,approved,completed,failed',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $progression->update($request->only([
                'to_grade', 'academic_year', 'progression_date', 'notes', 'status'
            ]));

            $progression->load('student');

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete progression
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            $progression->delete();

            return response()->json([
                'success' => true,
                'message' => 'Progression deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve progression
     */
    public function approve(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            if ($progression->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending progressions can be approved'
                ], 400);
            }

            $progression->update(['status' => 'approved']);
            $progression->load('student');

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression approved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete progression
     */
    public function complete(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->with('student')->findOrFail($id);

            if ($progression->status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only approved progressions can be completed'
                ], 400);
            }

            DB::transaction(function () use ($progression) {
                // Update progression status
                $progression->update(['status' => 'completed']);

                // Update student's grade level
                $progression->student->update([
                    'grade_level' => $progression->to_grade
                ]);
            });

            $progression->load('student');

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression completed successfully. Student grade level updated.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject progression
     */
    public function reject(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            $validator = Validator::make($request->all(), [
                'rejection_reason' => 'required|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $progression->update([
                'status' => 'failed',
                'notes' => $request->rejection_reason
            ]);

            $progression->load('student');

            return response()->json([
                'success' => true,
                'data' => $progression,
                'message' => 'Progression rejected successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk approve progressions
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $validator = Validator::make($request->all(), [
                'progression_ids' => 'required|array|min:1',
                'progression_ids.*' => 'integer|exists:student_progressions,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $progressionIds = $request->input('progression_ids');

            // Verify all progressions belong to this partner organization
            $progressions = StudentProgression::whereIn('id', $progressionIds)
                ->whereHas('student', function ($query) use ($partnerOrg) {
                    $query->where('school_id', $partnerOrg->id);
                })
                ->where('status', 'pending')
                ->get();

            if ($progressions->count() !== count($progressionIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some progressions do not belong to your organization or are not pending'
                ], 403);
            }

            $updated = StudentProgression::whereIn('id', $progressionIds)
                ->whereHas('student', function ($query) use ($partnerOrg) {
                    $query->where('school_id', $partnerOrg->id);
                })
                ->where('status', 'pending')
                ->update(['status' => 'approved']);

            return response()->json([
                'success' => true,
                'message' => "Successfully approved {$updated} progressions",
                'data' => ['approved_count' => $updated]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve progressions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper function to get next grade level
     */
    private function getNextGrade($currentGrade): string
    {
        $gradeMap = [
            'primary_1' => 'primary_2',
            'primary_2' => 'primary_3',
            'primary_3' => 'primary_4',
            'primary_4' => 'primary_5',
            'primary_5' => 'primary_6',
            'primary_6' => 'secondary_1',
            'secondary_1' => 'secondary_2',
            'secondary_2' => 'secondary_3',
            'secondary_3' => 'secondary_4',
            'secondary_4' => 'secondary_5',
            'secondary_5' => 'secondary_6',
            'secondary_6' => 'graduated',
        ];

        return $gradeMap[$currentGrade] ?? 'graduated';
    }

    /**
     * Helper function to check if student is eligible for scholarship
     */
    private function isScholarshipEligible($gradeLevel): bool
    {
        // Students are eligible for scholarships when progressing to new levels
        $scholarshipEligibleGrades = [
            'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
            'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6'
        ];

        return in_array($gradeLevel, $scholarshipEligibleGrades);
    }
}
