<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserResourceBookmark extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'resource_id'
    ];

    /**
     * Get the user that owns the bookmark
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the resource for this bookmark
     */
    public function resource()
    {
        return $this->belongsTo(EducationalResource::class, 'resource_id');
    }
}
