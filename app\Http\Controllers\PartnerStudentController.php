<?php

namespace App\Http\Controllers;

use App\Models\PartnerStudent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PartnerStudentController extends Controller
{
    /**
     * Display a listing of the partner's students.
     */
    public function index()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            return redirect()->route('login');
        }

        $students = PartnerStudent::where('partner_id', $partner->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $stats = [
            'total_students' => PartnerStudent::where('partner_id', $partner->id)->count(),
            'active_students' => PartnerStudent::where('partner_id', $partner->id)->where('status', 'active')->count(),
            'graduated_students' => PartnerStudent::where('partner_id', $partner->id)->where('status', 'graduated')->count(),
            'applications_submitted' => PartnerStudent::where('partner_id', $partner->id)
                ->whereNotNull('application_history')
                ->count(),
        ];

        return view('partner.students.index', compact('students', 'stats'));
    }

    /**
     * Show the form for creating a new student.
     */
    public function create()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            return redirect()->route('login');
        }

        return view('partner.students.create');
    }

    /**
     * Store a newly created student in storage.
     */
    public function store(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'class' => 'required|string|max:100',
            'gender' => 'required|in:male,female',
            'age' => 'required|integer|min:5|max:25',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'parent_contact' => 'required|string|max:20',
            'parent_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
        ]);

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $photoPath = $request->file('photo')->store('student-photos', 'public');
            $validated['photo'] = $photoPath;
        }

        // Add partner_id
        $validated['partner_id'] = $partner->id;
        $validated['status'] = 'active';

        $student = PartnerStudent::create($validated);

        return redirect()->route('partner.students.index')
            ->with('success', 'Student added successfully!');
    }

    /**
     * Display the specified student.
     */
    public function show(PartnerStudent $student)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization() || $student->partner_id !== $partner->id) {
            return redirect()->route('login');
        }

        $applications = $student->scholarshipApplications()
            ->with('scholarship')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('partner.students.show', compact('student', 'applications'));
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(PartnerStudent $student)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization() || $student->partner_id !== $partner->id) {
            return redirect()->route('login');
        }

        return view('partner.students.edit', compact('student'));
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, PartnerStudent $student)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization() || $student->partner_id !== $partner->id) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'class' => 'required|string|max:100',
            'gender' => 'required|in:male,female',
            'age' => 'required|integer|min:5|max:25',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'parent_contact' => 'required|string|max:20',
            'parent_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'status' => 'required|in:active,graduated,transferred,inactive',
        ]);

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($student->photo) {
                Storage::disk('public')->delete($student->photo);
            }
            
            $photoPath = $request->file('photo')->store('student-photos', 'public');
            $validated['photo'] = $photoPath;
        }

        $student->update($validated);

        return redirect()->route('partner.students.index')
            ->with('success', 'Student updated successfully!');
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy(PartnerStudent $student)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization() || $student->partner_id !== $partner->id) {
            return redirect()->route('login');
        }

        // Delete photo if exists
        if ($student->photo) {
            Storage::disk('public')->delete($student->photo);
        }

        $student->delete();

        return redirect()->route('partner.students.index')
            ->with('success', 'Student deleted successfully!');
    }

    /**
     * Bulk actions for students
     */
    public function bulkAction(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'action' => 'required|in:delete,graduate,activate,deactivate',
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:partner_students,id',
        ]);

        $students = PartnerStudent::whereIn('id', $validated['student_ids'])
            ->where('partner_id', $partner->id)
            ->get();

        $count = 0;
        foreach ($students as $student) {
            switch ($validated['action']) {
                case 'delete':
                    if ($student->photo) {
                        Storage::disk('public')->delete($student->photo);
                    }
                    $student->delete();
                    $count++;
                    break;
                case 'graduate':
                    $student->update(['status' => 'graduated']);
                    $count++;
                    break;
                case 'activate':
                    $student->update(['status' => 'active']);
                    $count++;
                    break;
                case 'deactivate':
                    $student->update(['status' => 'inactive']);
                    $count++;
                    break;
            }
        }

        $actionText = match($validated['action']) {
            'delete' => 'deleted',
            'graduate' => 'graduated',
            'activate' => 'activated',
            'deactivate' => 'deactivated',
        };

        return redirect()->route('partner.students.index')
            ->with('success', "{$count} students {$actionText} successfully!");
    }
}
