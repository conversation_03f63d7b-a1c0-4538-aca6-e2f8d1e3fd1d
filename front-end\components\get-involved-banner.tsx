import { <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"

export function GetInvolvedBanner() {
  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-green-700 to-green-900 shadow-xl">
      <div className="absolute inset-0 bg-grid opacity-20"></div>
      <div className="absolute top-0 right-0 w-1/3 h-full bg-amber-500/20 blur-3xl rounded-full transform translate-x-1/2"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/2 bg-green-300/20 blur-3xl rounded-full transform -translate-x-1/2"></div>

      <div className="relative z-10 px-8 py-12 md:py-16 lg:py-20 flex flex-col md:flex-row items-center justify-between gap-8">
        <div className="max-w-2xl">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 tracking-tight">
            Ready to Make a <span className="text-amber-400">Difference?</span>
          </h2>
          <p className="text-green-100 text-lg md:text-xl mb-6">
            Join our community of changemakers and help build a brighter future for communities across Nigeria. Whether
            through donations, volunteering, or partnerships, your contribution matters.
          </p>
          <div className="flex flex-wrap gap-4">
            <Link href="/donate">
              <Button
                size="lg"
                className="bg-amber-500 hover:bg-amber-600 text-green-950 font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-full"
              >
                Donate Now
              </Button>
            </Link>
            <Link href="#volunteer">
              <Button
                size="lg"
                variant="outline"
                className="text-white border-white hover:bg-white/10 transition-all duration-300 hover:scale-105 rounded-full"
              >
                Volunteer
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>

        <div className="flex flex-col items-center justify-center bg-white/10 backdrop-blur-sm p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white/15 border border-white/20">
          <div className="text-5xl md:text-6xl font-bold text-white mb-2">50K+</div>
          <div className="text-xl text-amber-300 font-medium mb-1">Lives Impacted</div>
          <div className="text-green-100 text-center max-w-[200px]">
            Join us in reaching our goal of 100,000 by 2025
          </div>
        </div>
      </div>
    </div>
  )
}
