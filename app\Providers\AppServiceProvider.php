<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Setting;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share app settings with all views
        View::composer('*', function ($view) {
            $view->with([
                'appName' => Setting::get('app_name', config('app.name', 'Laravel NGO')),
                'appLogo' => Setting::get('app_logo'),
                'siteDescription' => Setting::get('site_description', 'Making a difference in communities worldwide'),
            ]);
        });
    }
}
