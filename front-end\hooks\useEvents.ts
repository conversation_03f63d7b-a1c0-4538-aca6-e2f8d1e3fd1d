'use client'

import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'

interface Event {
  id: number
  title: string
  description: string
  event_type: 'fundraising' | 'volunteer' | 'awareness' | 'training' | 'other'
  start_datetime: string
  end_datetime: string
  venue?: string
  address?: string
  capacity?: number
  registration_deadline?: string
  featured_image?: string
  status: 'draft' | 'published' | 'cancelled'
  registrations_count: number
  organizer?: {
    id: number
    first_name: string
    last_name: string
  }
}

interface UseEventsReturn {
  events: Event[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useEvents(limit?: number): UseEventsReturn {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEvents = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getUpcomingEvents(limit)
      
      if (response.success && response.data) {
        // Handle both direct array and paginated response
        const eventsData = Array.isArray(response.data) 
          ? response.data 
          : response.data.data || []
        
        setEvents(eventsData)
      } else {
        setError('Failed to fetch events')
        setEvents([])
      }
    } catch (err) {
      console.error('Error fetching events:', err)
      setError('Failed to fetch events')
      setEvents([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEvents()
  }, [limit])

  return {
    events,
    loading,
    error,
    refetch: fetchEvents
  }
}

export function useAllEvents(): UseEventsReturn {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEvents = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getEvents()
      
      if (response.success && response.data) {
        // Handle paginated response
        const eventsData = response.data.data || []
        setEvents(eventsData)
      } else {
        setError('Failed to fetch events')
        setEvents([])
      }
    } catch (err) {
      console.error('Error fetching events:', err)
      setError('Failed to fetch events')
      setEvents([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEvents()
  }, [])

  return {
    events,
    loading,
    error,
    refetch: fetchEvents
  }
}
