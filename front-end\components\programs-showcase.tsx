"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { GraduationCap, BookOpen, Users, ArrowRight } from "lucide-react"

interface Program {
  id: number
  title: string
  description: string
  cta: string
  ctaLink: string
  icon: React.ReactNode
  image: string
  impact: string
  featured?: boolean
}

const programs: Program[] = [
  {
    id: 1,
    title: "Primary Education Support",
    description: "Providing uniforms, textbooks, and meals to elementary school children in rural communities.",
    cta: "Learn More",
    ctaLink: "/programs/primary-education",
    icon: <BookOpen className="h-8 w-8 text-blue-600" />,
    image: "/placeholder.svg?height=300&width=400",
    impact: "800+ children supported annually",
  },
  {
    id: 2,
    title: "Tertiary Scholarships",
    description: "Full tuition grants for university students in Kaduna State.",
    cta: "Apply Now",
    ctaLink: "/programs/scholarships",
    icon: <GraduationCap className="h-8 w-8 text-green-600" />,
    image: "/placeholder.svg?height=300&width=400",
    impact: "WAEC/NECO 5+ credits required",
    featured: true,
  },
  {
    id: 3,
    title: "Community Literacy",
    description: "Adult education programs to break the cycle of poverty.",
    cta: "Join a Class",
    ctaLink: "/programs/literacy",
    icon: <Users className="h-8 w-8 text-purple-600" />,
    image: "/placeholder.svg?height=300&width=400",
    impact: "Breaking generational poverty",
  },
]

export function ProgramsShowcase() {
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }))
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {programs.map((program, index) => (
        <Card
          key={program.id}
          className={`overflow-hidden border-green-100 dark:border-green-800 transition-all duration-500 hover:shadow-xl hover:-translate-y-2 hover:border-green-300 dark:hover:border-green-600 neumorphic rounded-2xl group animate-fade-in-up ${
            program.featured ? "ring-2 ring-green-500/20" : ""
          }`}
          style={{ animationDelay: `${index * 0.2}s` }}
        >
          {program.featured && (
            <Badge className="absolute top-4 right-4 z-10 bg-green-500 hover:bg-green-600 text-white rounded-full">
              Featured
            </Badge>
          )}

          <div className="relative h-48 overflow-hidden">
            <div
              className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${
                imagesLoaded[program.id] ? "opacity-0" : "opacity-100"
              }`}
            />
            <Image
              src={program.image || "/placeholder.svg"}
              alt={program.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className={`object-cover transition-all duration-700 group-hover:scale-110 ${
                imagesLoaded[program.id] ? "opacity-100" : "opacity-0"
              }`}
              onLoad={() => handleImageLoad(program.id)}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm p-2 rounded-full group-hover:bg-white transition-colors duration-300">
              {program.icon}
            </div>
          </div>

          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-3 text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
              {program.title}
            </h3>
            <p className="text-muted-foreground mb-4 group-hover:text-foreground transition-colors duration-300">
              {program.description}
            </p>
            <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-4">{program.impact}</div>
          </CardContent>

          <CardFooter className="px-6 pb-6 pt-0">
            <Link href={program.ctaLink} className="w-full">
              <Button className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-full group">
                {program.cta}
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
