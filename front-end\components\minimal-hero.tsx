"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Play, ChevronLeft, ChevronRight } from "lucide-react"

const heroImages = [
  {
    src: "/placeholder.svg?height=800&width=1200&text=Students+Learning&bg=22c55e&color=ffffff",
    alt: "Students in classroom receiving quality education",
    caption: "Empowering minds through education",
  },
  {
    src: "/placeholder.svg?height=800&width=1200&text=Scholarship+Ceremony&bg=f59e0b&color=ffffff",
    alt: "Scholarship award ceremony with students",
    caption: "Celebrating academic achievements",
  },
  {
    src: "/placeholder.svg?height=800&width=1200&text=Community+Outreach&bg=059669&color=ffffff",
    alt: "Community literacy program in session",
    caption: "Building stronger communities",
  },
  {
    src: "/placeholder.svg?height=800&width=1200&text=School+Supplies&bg=7c3aed&color=ffffff",
    alt: "Distribution of educational materials",
    caption: "Providing essential learning resources",
  },
]

export function MinimalHero() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  // Auto-advance images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + heroImages.length) % heroImages.length)
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-white dark:bg-gray-950 overflow-hidden">
      {/* Background Pattern - Subtle */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-950 opacity-50"></div>

      <div className="container relative z-10 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]">
          {/* Content Section */}
          <div className="space-y-8 text-center lg:text-left">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 rounded-full bg-green-100 dark:bg-green-900/30 px-4 py-2 text-sm font-medium text-green-700 dark:text-green-300 animate-fade-in">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Since 2015
            </div>

            {/* Headline */}
      <h1 className="text-4xl font-bold tracking-tighter text-foreground sm:text-5xl md:text-6xl lg:text-7xl">
        Empowering Nigeria Through <span className="text-primary">Education</span>
      </h1>

            {/* Subheading */}
      <p className="text-lg text-muted-foreground md:text-xl max-w-xl">
        Providing scholarships, school supplies, and hope to students, underprivileged people, and those in need across Nigeria since 2015. Together, we're building a brighter future for all.
      </p>

            {/* CTA Buttons */}
            <div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in-up"
              style={{ animationDelay: "0.4s" }}
            >
              <Link href="/donate">
                <Button
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 rounded-full px-8 py-4 text-lg font-semibold group w-full sm:w-auto"
                >
                  Donate Now
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
              <Link href="/students">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 rounded-full px-8 py-4 text-lg font-semibold group w-full sm:w-auto"
                >
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  Meet Our Students
                </Button>
              </Link>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-6 pt-8 animate-fade-in-up" style={{ animationDelay: "0.6s" }}>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-green-600 dark:text-green-400">1,200+</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Students Supported</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl sm:text-3xl font-bold text-green-600 dark:text-green-400">₦12M</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">In Scholarships</div>
              </div>
            </div>
          </div>

          {/* Image Section */}
          <div className="relative">
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl bg-gray-100 dark:bg-gray-800">
              {/* Loading State */}
              {!isLoaded && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}

              {/* Main Image */}
              <Image
                src={heroImages[currentImageIndex].src || "/placeholder.svg"}
                alt={heroImages[currentImageIndex].alt}
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
                className="object-cover transition-opacity duration-500"
                onLoad={() => setIsLoaded(true)}
                priority
              />

              {/* Image Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>

              {/* Image Caption */}
              <div className="absolute bottom-4 left-4 right-4">
                <p className="text-white text-sm font-medium bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2">
                  {heroImages[currentImageIndex].caption}
                </p>
              </div>

              {/* Navigation Arrows */}
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
                aria-label="Previous image"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
                aria-label="Next image"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>

            {/* Image Indicators */}
            <div className="flex justify-center mt-6 gap-2">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImageIndex
                      ? "bg-green-600 scale-125"
                      : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                  }`}
                  aria-label={`View image ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}
