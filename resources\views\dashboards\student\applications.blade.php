@extends('layouts.dashboard')

@section('title', 'My Applications - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                    ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications', 'active' => true],
                    ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/student/scholarships'],
                    ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                    ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/student/resources'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                        ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications', 'active' => true],
                        ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/student/scholarships'],
                        ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                        ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/student/resources'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">My Applications</h1>
                            <p class="text-green-100 text-sm lg:text-base">Track your scholarship applications and their status</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/scholarships" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Total</p>
                                <p class="text-xl font-bold text-blue-600">{{ $stats['total'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-clock text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Pending</p>
                                <p class="text-xl font-bold text-yellow-600">{{ $stats['pending'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-search text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Review</p>
                                <p class="text-xl font-bold text-orange-600">{{ $stats['under_review'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Approved</p>
                                <p class="text-xl font-bold text-green-600">{{ $stats['approved'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-times-circle text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Rejected</p>
                                <p class="text-xl font-bold text-red-600">{{ $stats['rejected'] }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Applications List -->
                    @if($applications->count() > 0)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900">Your Applications</h2>
                                <p class="text-gray-600 text-sm mt-1">Manage and track your scholarship applications</p>
                            </div>

                            <div class="divide-y divide-gray-100">
                                @foreach($applications as $application)
                                    <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                            <div class="flex-1 mb-4 lg:mb-0">
                                                <div class="flex items-start">
                                                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                                            {{ $application->scholarship->title ?? 'University Scholarship' }}
                                                        </h3>
                                                        <p class="text-gray-600 text-sm mb-2">
                                                            Applied on {{ $application->created_at->format('M d, Y') }} at {{ $application->created_at->format('g:i A') }}
                                                        </p>
                                                        @if($application->scholarship)
                                                            <p class="text-gray-500 text-sm">
                                                                Amount: <span class="font-medium text-green-600">₦{{ number_format($application->scholarship->amount) }}</span>
                                                            </p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                    @if($application->status === 'approved') bg-green-100 text-green-800
                                                    @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                                    @elseif($application->status === 'under_review') bg-orange-100 text-orange-800
                                                    @else bg-yellow-100 text-yellow-800
                                                    @endif">
                                                    <i class="fas {{ $application->status === 'approved' ? 'fa-check-circle' : ($application->status === 'rejected' ? 'fa-times-circle' : ($application->status === 'under_review' ? 'fa-search' : 'fa-clock')) }} mr-1"></i>
                                                    {{ ucfirst(str_replace('_', ' ', $application->status)) }}
                                                </span>

                                                <div class="flex gap-2">
                                                    <button onclick="viewApplication({{ $application->id }})" class="inline-flex items-center px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm transition-colors duration-200">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        View
                                                    </button>
                                                    @if($application->files->count() > 0)
                                                        <button onclick="viewDocuments({{ $application->id }})" class="inline-flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors duration-200">
                                                            <i class="fas fa-file-alt mr-1"></i>
                                                            Files ({{ $application->files->count() }})
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        @if($application->status === 'approved')
                                            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                                    <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                                                </div>
                                            </div>
                                        @elseif($application->status === 'rejected' && $application->rejection_reason)
                                            <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                                <div class="flex items-start">
                                                    <i class="fas fa-info-circle text-red-600 mr-2 mt-0.5"></i>
                                                    <div>
                                                        <span class="text-red-800 font-medium block">Application not approved</span>
                                                        <p class="text-red-700 text-sm mt-1">{{ $application->rejection_reason }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Pagination -->
                        @if($applications->hasPages())
                            <div class="mt-6">
                                {{ $applications->links() }}
                            </div>
                        @endif
                    @else
                        <!-- No Applications -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Applications Yet</h3>
                            <p class="text-gray-600 mb-6">You haven't submitted any scholarship applications. Start by browsing available scholarships.</p>
                            <a href="/student/scholarships" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Application Details Modal -->
<div id="applicationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Application Details</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="applicationContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function viewApplication(applicationId) {
    // Show loading state
    document.getElementById('applicationContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('applicationModal').classList.remove('hidden');
    
    // In a real implementation, you would fetch application details via AJAX
    // For now, we'll show a placeholder
    setTimeout(() => {
        document.getElementById('applicationContent').innerHTML = `
            <div class="space-y-4">
                <p class="text-gray-600">Application details would be loaded here via AJAX call to the backend.</p>
                <p class="text-sm text-gray-500">This would include all form data, submitted documents, and application history.</p>
            </div>
        `;
    }, 500);
}

function viewDocuments(applicationId) {
    // Similar to viewApplication but for documents
    document.getElementById('applicationContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('applicationModal').classList.remove('hidden');
    
    setTimeout(() => {
        document.getElementById('applicationContent').innerHTML = `
            <div class="space-y-4">
                <h4 class="font-semibold text-gray-900">Submitted Documents</h4>
                <p class="text-gray-600">Document list and download links would be displayed here.</p>
            </div>
        `;
    }, 500);
}

function closeModal() {
    document.getElementById('applicationModal').classList.add('hidden');
}
</script>
@endsection
