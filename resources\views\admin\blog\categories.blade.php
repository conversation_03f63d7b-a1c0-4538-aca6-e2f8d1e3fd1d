@extends('layouts.admin')

@section('title', 'Blog Categories')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Blog Categories</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">Blog</a></li>
                        <li class="breadcrumb-item active">Categories</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Validation Errors:</strong>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tags me-2"></i>Categories List
                    </h6>
                </div>
                <div class="card-body">
                    @if(isset($categories) && count($categories) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Posts</th>
                                        <th>Color</th>
                                        <th>Status</th>
                                        <th>Sort Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($categories as $category)
                                        <tr>
                                            <td>
                                                <strong>{{ $category['name'] }}</strong>
                                                <br><small class="text-muted">{{ $category['slug'] }}</small>
                                            </td>
                                            <td>{{ $category['description'] ?? 'No description' }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ $category['posts_count'] }} posts</span>
                                            </td>
                                            <td>
                                                <div style="width: 20px; height: 20px; background-color: {{ $category['color'] }}; border-radius: 3px; display: inline-block;"></div>
                                                <small class="text-muted ms-1">{{ $category['color'] }}</small>
                                            </td>
                                            <td>
                                                @if($category['is_active'])
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $category['sort_order'] }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editCategoryModal{{ $category['id'] }}">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                @if($category['posts_count'] == 0)
                                                    <form action="{{ route('admin.blog.categories.destroy.form', $category['id']) }}" method="POST" style="display: inline-block;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-danger" disabled title="Cannot delete category with posts">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>

                                        <!-- Edit Category Modal -->
                                        <div class="modal fade" id="editCategoryModal{{ $category['id'] }}" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <form action="{{ route('admin.blog.categories.update.form', $category['id']) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Edit Category</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="form-group mb-3">
                                                                <label for="edit_name_{{ $category['id'] }}" class="form-label">Name <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="edit_name_{{ $category['id'] }}" name="name" value="{{ $category['name'] }}" required>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_description_{{ $category['id'] }}" class="form-label">Description</label>
                                                                <textarea class="form-control" id="edit_description_{{ $category['id'] }}" name="description" rows="3">{{ $category['description'] }}</textarea>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_color_{{ $category['id'] }}" class="form-label">Color</label>
                                                                <input type="color" class="form-control form-control-color" id="edit_color_{{ $category['id'] }}" name="color" value="{{ $category['color'] }}">
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_sort_order_{{ $category['id'] }}" class="form-label">Sort Order</label>
                                                                <input type="number" class="form-control" id="edit_sort_order_{{ $category['id'] }}" name="sort_order" value="{{ $category['sort_order'] }}" min="0">
                                                            </div>
                                                            <div class="form-check">
                                                                <input type="hidden" name="is_active" value="0">
                                                                <input class="form-check-input" type="checkbox" id="edit_is_active_{{ $category['id'] }}" name="is_active" value="1" {{ $category['is_active'] ? 'checked' : '' }}>
                                                                <label class="form-check-label" for="edit_is_active_{{ $category['id'] }}">
                                                                    Active
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-primary">Update Category</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted">No categories found. Create your first category!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus me-2"></i>Add New Category
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.blog.categories.store.form') }}" method="POST">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label for="color" class="form-label">Color</label>
                            <input type="color" class="form-control form-control-color" id="color" name="color" value="{{ old('color', '#6c757d') }}">
                        </div>
                        <div class="form-group mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                        </div>
                        <div class="form-check mb-3">
                            <input type="hidden" name="is_active" value="0">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection