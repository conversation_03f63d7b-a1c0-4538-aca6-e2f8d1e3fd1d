@extends('layouts.admin')

@section('title', 'Create Blog Post')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create Blog Post</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">Blog</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.blog.posts') }}">Posts</a></li>
                        <li class="breadcrumb-item active">Create Post</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-pen me-2"></i>Blog Post Details
                    </h6>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.blog.posts.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    <label for="title" class="form-label">Post Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}" required>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3" placeholder="Brief description of the post...">{{ old('excerpt') }}</textarea>
                                    <div class="form-text">This will be shown in post listings and search results</div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="content" name="content" rows="15" required>{{ old('content') }}</textarea>
                                    <div class="form-text">Write your blog post content here</div>
                                </div>

                                <!-- SEO Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-info">SEO Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="seo_title" class="form-label">SEO Title</label>
                                            <input type="text" class="form-control" id="seo_title" name="seo_title" value="{{ old('seo_title') }}" maxlength="60">
                                            <div class="form-text">Recommended: 50-60 characters</div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="seo_description" class="form-label">SEO Description</label>
                                            <textarea class="form-control" id="seo_description" name="seo_description" rows="3" maxlength="160">{{ old('seo_description') }}</textarea>
                                            <div class="form-text">Recommended: 150-160 characters</div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="tags" class="form-label">Tags</label>
                                            <input type="text" class="form-control" id="tags" name="tags" value="{{ old('tags') }}" placeholder="tag1, tag2, tag3">
                                            <div class="form-text">Separate tags with commas</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publish Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Publish Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                            </select>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="category_id" class="form-label">Category</label>
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="">Select Category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                                            <div class="form-text">Upload a featured image for your post (JPG, PNG, GIF - Max: 2MB)</div>
                                        </div>
                                        
                                        <div id="imagePreview" style="display: none;">
                                            <img id="preview" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                            <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removePreview()">Remove</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary" name="action" value="publish">
                                                <i class="fas fa-save me-2"></i>Save & Publish
                                            </button>
                                            <button type="submit" class="btn btn-secondary" name="action" value="draft">
                                                <i class="fas fa-file-alt me-2"></i>Save as Draft
                                            </button>
                                            <a href="{{ route('admin.blog.posts') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('featured_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Check file size (2MB limit)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                this.value = '';
                return;
            }
            
            // Check file type
            if (!file.type.match('image.*')) {
                alert('Please select an image file');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });
});

function removePreview() {
    document.getElementById('featured_image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}

// Character counter for SEO fields
document.getElementById('seo_title').addEventListener('input', function() {
    updateCharacterCount(this, 60);
});

document.getElementById('seo_description').addEventListener('input', function() {
    updateCharacterCount(this, 160);
});

function updateCharacterCount(element, maxLength) {
    const current = element.value.length;
    const remaining = maxLength - current;
    let helpText = element.parentNode.querySelector('.form-text');
    
    if (helpText) {
        const color = remaining < 10 ? 'text-danger' : (remaining < 20 ? 'text-warning' : 'text-muted');
        helpText.className = `form-text ${color}`;
        helpText.textContent = `${current}/${maxLength} characters (${remaining} remaining)`;
    }
}
</script>
@endsection 