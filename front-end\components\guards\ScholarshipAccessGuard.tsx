'use client'

import { ReactNode, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/use-toast'
import { ScholarshipRoutingService } from '@/lib/scholarshipRouting'
import { Loader2, Alert<PERSON>riangle, Shield } from 'lucide-react'
import { Button } from '@/components/ui/button'

export interface ScholarshipAccessGuardProps {
  children: ReactNode
  category?: 'primary' | 'secondary' | 'university'
  scholarshipId?: string
  fallbackUrl?: string
  showAccessDenied?: boolean
  requireAuth?: boolean
}

/**
 * Access control guard for scholarship-related pages
 * Protects routes based on scholarship category and user role
 */
export default function ScholarshipAccessGuard({
  children,
  category,
  scholarshipId,
  fallbackUrl = '/scholarships',
  showAccessDenied = true,
  requireAuth = true
}: ScholarshipAccessGuardProps) {
  const { user, isLoading: authLoading } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  
  const [accessState, setAccessState] = useState<{
    isLoading: boolean
    hasAccess: boolean
    errorMessage?: string
    requiredRole?: string
  }>({
    isLoading: true,
    hasAccess: false
  })

  useEffect(() => {
    if (authLoading) {
      setAccessState(prev => ({ ...prev, isLoading: true }))
      return
    }

    // If no category specified, allow access (general protection)
    if (!category) {
      if (requireAuth && !user) {
        setAccessState({
          isLoading: false,
          hasAccess: false,
          errorMessage: 'Authentication required',
          requiredRole: 'user'
        })
        return
      }
      
      setAccessState({
        isLoading: false,
        hasAccess: true
      })
      return
    }

    // Check category-specific access
    const routingResult = ScholarshipRoutingService.determineRouting({
      category,
      scholarshipId,
      redirectUrl: window.location.pathname + window.location.search
    }, user)

    setAccessState({
      isLoading: false,
      hasAccess: !routingResult.shouldRedirect,
      errorMessage: routingResult.errorMessage,
      requiredRole: routingResult.requiredRole
    })

    // Handle automatic redirects for unauthorized access
    if (routingResult.shouldRedirect && routingResult.redirectUrl) {
      if (routingResult.errorMessage) {
        toast({
          title: "Access Required",
          description: routingResult.errorMessage,
          variant: routingResult.requiresAuth ? "default" : "destructive",
        })
      }

      // Redirect after a short delay
      setTimeout(() => {
        router.push(routingResult.redirectUrl!)
      }, 1500)
    }

  }, [authLoading, user, category, scholarshipId, requireAuth, router, toast])

  // Show loading state
  if (accessState.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">
            Verifying access permissions...
          </p>
        </div>
      </div>
    )
  }

  // Show access denied page if configured to do so
  if (!accessState.hasAccess && showAccessDenied) {
    const categoryMessages = category ? ScholarshipRoutingService.getCategoryMessages(category) : {
      title: 'Access Required',
      description: 'You need appropriate permissions to access this page',
      loginPrompt: 'Please log in to continue',
      accessRequirement: 'Valid user account required'
    }

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <section className="relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-red-600 to-red-800 text-white">
          <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-4 sm:space-y-6">
              <Shield className="h-16 w-16 mx-auto mb-4 opacity-80" />
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                Access Restricted
              </h1>
              <p className="text-base sm:text-lg md:text-xl text-red-100 max-w-3xl mx-auto px-2">
                {categoryMessages.description}
              </p>
            </div>
          </div>
        </section>

        {/* Access Denied Content */}
        <section className="py-12 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center">
              <AlertTriangle className="h-16 w-16 text-amber-500 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Access Required
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6 text-lg">
                {accessState.errorMessage || 'You do not have permission to access this page.'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mb-8">
                {categoryMessages.accessRequirement}
              </p>
              
              <div className="flex gap-4 justify-center flex-wrap">
                {!user && (
                  <Button 
                    onClick={() => {
                      // Store current URL for redirect after login
                      localStorage.setItem('redirectAfterLogin', window.location.pathname + window.location.search)
                      
                      let loginUrl = '/auth/login'
                      if (category === 'primary' || category === 'secondary') {
                        loginUrl += '?type=partner'
                      } else if (category === 'university') {
                        loginUrl += '?type=student'
                      }
                      
                      router.push(loginUrl)
                    }}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {accessState.requiredRole === 'partner_organization' ? 'School Login' : 'Student Login'}
                  </Button>
                )}
                
                <Button 
                  onClick={() => router.push(fallbackUrl)} 
                  variant="outline"
                >
                  Go to Scholarships
                </Button>
                
                <Button 
                  onClick={() => window.history.back()} 
                  variant="ghost"
                >
                  Go Back
                </Button>
              </div>

              {/* Additional Help */}
              <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Need Help?
                </h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                  {category === 'primary' || category === 'secondary' ? (
                    <>
                      <p>• Primary and Secondary scholarship applications must be submitted by schools</p>
                      <p>• Contact your school administrator to apply on your behalf</p>
                      <p>• Schools can register as Partner Organizations to access these features</p>
                    </>
                  ) : category === 'university' ? (
                    <>
                      <p>• University scholarships are for individual student applications</p>
                      <p>• You must register as a student (not through a school)</p>
                      <p>• Ensure you're not logged in through a school account</p>
                    </>
                  ) : (
                    <>
                      <p>• Make sure you're logged in with the correct account type</p>
                      <p>• Contact support if you believe this is an error</p>
                      <p>• Check that your account has the necessary permissions</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    )
  }

  // If access denied but not showing access denied page, return null
  if (!accessState.hasAccess) {
    return null
  }

  // User has access, render children
  return <>{children}</>
}

/**
 * Higher-order component for protecting scholarship routes
 */
export function withScholarshipAccess<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<ScholarshipAccessGuardProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <ScholarshipAccessGuard {...guardProps}>
        <Component {...props} />
      </ScholarshipAccessGuard>
    )
  }
}

/**
 * Specific guards for different scholarship categories
 */
export const PrimaryScholarshipGuard = ({ children, ...props }: Omit<ScholarshipAccessGuardProps, 'category'>) => (
  <ScholarshipAccessGuard category="primary" {...props}>
    {children}
  </ScholarshipAccessGuard>
)

export const SecondaryScholarshipGuard = ({ children, ...props }: Omit<ScholarshipAccessGuardProps, 'category'>) => (
  <ScholarshipAccessGuard category="secondary" {...props}>
    {children}
  </ScholarshipAccessGuard>
)

export const UniversityScholarshipGuard = ({ children, ...props }: Omit<ScholarshipAccessGuardProps, 'category'>) => (
  <ScholarshipAccessGuard category="university" {...props}>
    {children}
  </ScholarshipAccessGuard>
)
