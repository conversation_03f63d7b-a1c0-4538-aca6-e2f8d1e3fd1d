"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { AnimatedSection } from "@/components/animated-section"
import { Search, Calendar, User, ArrowRight, Heart, MessageCircle, Clock } from "lucide-react"

// Blog data
const blogPosts = [
  {
    id: 1,
    title: "Transforming Lives Through Clean Water: The Ikeja Community Success Story",
    excerpt:
      "Discover how our clean water initiative has revolutionized daily life for over 500 families in the Ikeja community, reducing waterborne diseases by 80%.",
    author: "Dr. <PERSON><PERSON>",
    date: "2024-01-15",
    category: "Water & Sanitation",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "5 min read",
    likes: 124,
    comments: 18,
    featured: true,
  },
  {
    id: 2,
    title: "Empowering Women Entrepreneurs: Microfinance Success in Kano",
    excerpt:
      "Meet the inspiring women who have built thriving businesses through our microfinance program, creating jobs and strengthening their communities.",
    author: "<PERSON><PERSON><PERSON><PERSON>",
    date: "2024-01-10",
    category: "Women's Empowerment",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "7 min read",
    likes: 89,
    comments: 12,
    featured: false,
  },
  {
    id: 3,
    title: "Digital Skills for the Future: Tech Training Program Graduates First Cohort",
    excerpt:
      "Our technology skills development program has graduated its first cohort of 50 young Nigerians, equipped with coding and digital marketing skills.",
    author: "Emeka Okafor",
    date: "2024-01-05",
    category: "Education & Skills",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "6 min read",
    likes: 156,
    comments: 24,
    featured: false,
  },
  {
    id: 4,
    title: "Sustainable Agriculture: Training Farmers for Climate Resilience",
    excerpt:
      "Learn how our agricultural training program is helping farmers adapt to climate change while increasing crop yields and income.",
    author: "Dr. Yusuf Abdullahi",
    date: "2023-12-28",
    category: "Agriculture",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "8 min read",
    likes: 203,
    comments: 31,
    featured: false,
  },
  {
    id: 5,
    title: "Healthcare Reaches Remote Communities: Mobile Clinic Impact Report",
    excerpt:
      "Our mobile healthcare units have provided medical services to over 10,000 people in remote areas, bringing essential healthcare to underserved communities.",
    author: "Dr. Funmi Adebayo",
    date: "2023-12-20",
    category: "Healthcare",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "9 min read",
    likes: 178,
    comments: 27,
    featured: false,
  },
  {
    id: 6,
    title: "Building Tomorrow's Leaders: Youth Leadership Development Program",
    excerpt:
      "Discover how our youth leadership program is nurturing the next generation of Nigerian leaders through mentorship, training, and real-world experience.",
    author: "Adaora Okafor",
    date: "2023-12-15",
    category: "Youth Development",
    image: "/placeholder.svg?height=400&width=600",
    readTime: "7 min read",
    likes: 142,
    comments: 19,
    featured: false,
  },
]

const categories = [
  "All",
  "Water & Sanitation",
  "Women's Empowerment",
  "Education & Skills",
  "Agriculture",
  "Healthcare",
  "Youth Development",
]

export function BlogList() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [filteredPosts, setFilteredPosts] = useState(blogPosts)
  const [imageLoaded, setImageLoaded] = useState<Record<number, boolean>>({})

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    filterPosts(term, selectedCategory)
  }

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category)
    filterPosts(searchTerm, category)
  }

  const filterPosts = (search: string, category: string) => {
    let filtered = blogPosts

    if (category !== "All") {
      filtered = filtered.filter((post) => post.category === category)
    }

    if (search) {
      filtered = filtered.filter(
        (post) =>
          post.title.toLowerCase().includes(search.toLowerCase()) ||
          post.excerpt.toLowerCase().includes(search.toLowerCase()),
      )
    }

    setFilteredPosts(filtered)
  }

  const handleImageLoad = (id: number) => {
    setImageLoaded((prev) => ({ ...prev, [id]: true }))
  }

  const featuredPost = blogPosts.find((post) => post.featured)
  const regularPosts = filteredPosts.filter((post) => !post.featured)

  return (
    <>
      {/* Search and Filter */}
      <AnimatedSection className="py-12 bg-background">
        <div className="container">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between mb-8">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 rounded-xl"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleCategoryFilter(category)}
                  className={`transition-all duration-200 rounded-full ${
                    selectedCategory === category
                      ? "bg-green-600 hover:bg-green-700"
                      : "hover:bg-green-50 dark:hover:bg-green-950/20"
                  }`}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </AnimatedSection>

      {/* Featured Post */}
      {featuredPost && selectedCategory === "All" && !searchTerm && (
        <AnimatedSection className="py-12 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="mb-8">
              <Badge className="bg-amber-500 hover:bg-amber-600 text-white rounded-full">Featured Story</Badge>
            </div>
            <Card className="overflow-hidden shadow-xl neumorphic rounded-2xl">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative h-64 lg:h-full">
                  <div
                    className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${imageLoaded[featuredPost.id] ? "opacity-0" : "opacity-100"}`}
                  />
                  <Image
                    src={featuredPost.image || "/placeholder.svg"}
                    alt={featuredPost.title}
                    fill
                    sizes="(max-width: 1024px) 100vw, 50vw"
                    className={`object-cover ${imageLoaded[featuredPost.id] ? "opacity-100" : "opacity-0"}`}
                    onLoad={() => handleImageLoad(featuredPost.id)}
                    priority
                  />
                </div>
                <CardContent className="p-8 flex flex-col justify-center">
                  <Badge className="w-fit mb-4 bg-green-100 text-green-800 hover:bg-green-200 rounded-full">
                    {featuredPost.category}
                  </Badge>
                  <h2 className="text-2xl md:text-3xl font-bold mb-4 leading-tight">{featuredPost.title}</h2>
                  <p className="text-muted-foreground mb-6 text-lg">{featuredPost.excerpt}</p>
                  <div className="flex items-center gap-4 mb-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {featuredPost.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(featuredPost.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {featuredPost.readTime}
                    </div>
                  </div>
                  <Link href={`/blog/${featuredPost.id}`}>
                    <Button className="bg-green-600 hover:bg-green-700 transition-all duration-200 hover:scale-105 rounded-full">
                      Read Full Story
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </CardContent>
              </div>
            </Card>
          </div>
        </AnimatedSection>
      )}

      {/* Blog Posts Grid */}
      <AnimatedSection className="py-12 bg-background">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <Card
                key={post.id}
                className="overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 neumorphic animate-fade-in-up rounded-2xl"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative h-48">
                  <div
                    className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${imageLoaded[post.id] ? "opacity-0" : "opacity-100"}`}
                  />
                  <Image
                    src={post.image || "/placeholder.svg"}
                    alt={post.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className={`object-cover transition-transform duration-500 hover:scale-105 ${imageLoaded[post.id] ? "opacity-100" : "opacity-0"}`}
                    onLoad={() => handleImageLoad(post.id)}
                    loading="lazy"
                  />
                </div>
                <CardContent className="p-6">
                  <Badge className="mb-3 bg-green-100 text-green-800 hover:bg-green-200 rounded-full">
                    {post.category}
                  </Badge>
                  <h3 className="text-xl font-semibold mb-3 leading-tight line-clamp-2">{post.title}</h3>
                  <p className="text-muted-foreground mb-4 line-clamp-3">{post.excerpt}</p>
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {post.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {post.readTime}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Heart className="h-4 w-4" />
                        {post.likes}
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="h-4 w-4" />
                        {post.comments}
                      </div>
                    </div>
                    <Link href={`/blog/${post.id}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-green-50 dark:hover:bg-green-950/20 rounded-full"
                      >
                        Read More
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">No articles found</h3>
              <p className="text-muted-foreground">Try adjusting your search terms or filter criteria.</p>
            </div>
          )}
        </div>
      </AnimatedSection>
    </>
  )
}
