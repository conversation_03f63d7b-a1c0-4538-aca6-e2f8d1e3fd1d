<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Admin Blog",
 *     description="Blog management endpoints for administrators"
 * )
 */
class AdminBlogController extends Controller
{
    /**
     * Get blog analytics dashboard data
     */
    public function index()
    {
        $totalPosts = BlogPost::count();
        $publishedPosts = BlogPost::where('status', 'published')->count();
        $draftPosts = BlogPost::where('status', 'draft')->count();
        $totalComments = BlogComment::count();
        $pendingComments = BlogComment::where('status', 'pending')->count();
        $totalCategories = BlogCategory::count();

        // Recent posts
        $recentPosts = BlogPost::with(['author', 'category'])
            ->latest()
            ->limit(5)
            ->get();

        // Popular posts (by views)
        $popularPosts = BlogPost::where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Recent comments
        $recentComments = BlogComment::with(['post', 'user'])
            ->latest()
            ->limit(5)
            ->get();

        // Categories with post counts
        $categories = BlogCategory::withCount('posts')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => [
                    'total_posts' => $totalPosts,
                    'published_posts' => $publishedPosts,
                    'draft_posts' => $draftPosts,
                    'total_comments' => $totalComments,
                    'pending_comments' => $pendingComments,
                    'total_categories' => $totalCategories,
                ],
                'recent_posts' => $recentPosts,
                'popular_posts' => $popularPosts,
                'recent_comments' => $recentComments,
                'categories' => $categories,
            ]
        ]);
    }

    // ======= BLOG POSTS =======

    /**
     * Get all blog posts for admin
     */
    public function getPosts(Request $request)
    {
        $query = BlogPost::with(['author', 'category']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->search($search);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by author
        if ($request->has('author_id')) {
            $query->where('author_id', $request->author_id);
        }

        // Sort
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $posts = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $posts
        ]);
    }

    /**
     * Get single blog post for editing
     */
    public function getPost($id)
    {
        $post = BlogPost::with(['author', 'category', 'comments.user'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $post
        ]);
    }

    /**
     * Create new blog post
     */
    public function createPost(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'required|string|max:500',
            'category_id' => 'required|exists:blog_categories,id',
            'status' => 'required|in:draft,published',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160',
            'seo_keywords' => 'nullable|array',
            'tags' => 'nullable|array',
            'published_at' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $request->all();
            $data['author_id'] = auth()->id();

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                $image = $request->file('featured_image');
                $filename = 'blog_' . time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('blog/images', $filename, 'public');
                $data['featured_image'] = $path;
            }

            // Set published_at if status is published and not set
            if ($data['status'] === 'published' && !isset($data['published_at'])) {
                $data['published_at'] = now();
            }

            $post = BlogPost::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Blog post created successfully',
                'data' => $post->load(['author', 'category'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create blog post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update blog post
     */
    public function updatePost(Request $request, $id)
    {
        $post = BlogPost::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'required|string|max:500',
            'category_id' => 'required|exists:blog_categories,id',
            'status' => 'required|in:draft,published',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160',
            'seo_keywords' => 'nullable|array',
            'tags' => 'nullable|array',
            'published_at' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $request->all();

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                // Delete old image if exists
                if ($post->featured_image && Storage::disk('public')->exists($post->featured_image)) {
                    Storage::disk('public')->delete($post->featured_image);
                }

                $image = $request->file('featured_image');
                $filename = 'blog_' . time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('blog/images', $filename, 'public');
                $data['featured_image'] = $path;
            }

            // Set published_at if status changed to published and not set
            if ($data['status'] === 'published' && $post->status !== 'published' && !isset($data['published_at'])) {
                $data['published_at'] = now();
            }

            $post->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Blog post updated successfully',
                'data' => $post->load(['author', 'category'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update blog post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete blog post
     */
    public function deletePost($id)
    {
        try {
            $post = BlogPost::findOrFail($id);

            // Delete featured image if exists
            if ($post->featured_image && Storage::disk('public')->exists($post->featured_image)) {
                Storage::disk('public')->delete($post->featured_image);
            }

            $post->delete();

            return response()->json([
                'success' => true,
                'message' => 'Blog post deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete blog post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Publish blog post
     */
    public function publishPost($id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            $post->publish();

            return response()->json([
                'success' => true,
                'message' => 'Blog post published successfully',
                'data' => $post
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to publish blog post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unpublish blog post
     */
    public function unpublishPost($id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            $post->unpublish();

            return response()->json([
                'success' => true,
                'message' => 'Blog post unpublished successfully',
                'data' => $post
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unpublish blog post: ' . $e->getMessage()
            ], 500);
        }
    }

    // ======= BLOG CATEGORIES =======

    /**
     * Get all blog categories
     */
    public function getCategories(Request $request)
    {
        $query = BlogCategory::withCount('posts');

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $categories = $query->ordered()->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get single category
     */
    public function getCategory($id)
    {
        $category = BlogCategory::withCount('posts')->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }

    /**
     * Create new blog category
     */
    public function createCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:blog_categories,name',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $request->all();
            $data['is_active'] = $request->boolean('is_active', true);
            $data['sort_order'] = $data['sort_order'] ?? 0;

            $category = BlogCategory::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Blog category created successfully',
                'data' => $category
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create blog category: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update blog category
     */
    public function updateCategory(Request $request, $id)
    {
        $category = BlogCategory::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:blog_categories,name,' . $id,
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $request->all();
            $data['is_active'] = $request->boolean('is_active', $category->is_active);

            $category->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Blog category updated successfully',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update blog category: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete blog category
     */
    public function deleteCategory($id)
    {
        try {
            $category = BlogCategory::findOrFail($id);

            // Check if category has posts
            if ($category->posts()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category that has blog posts. Please move posts to another category first.'
                ], 400);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Blog category deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete blog category: ' . $e->getMessage()
            ], 500);
        }
    }

    // ======= BLOG COMMENTS =======

    /**
     * Get all blog comments
     */
    public function getComments(Request $request)
    {
        $query = BlogComment::with(['post', 'user']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where('content', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by post
        if ($request->has('post_id')) {
            $query->where('post_id', $request->post_id);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $comments = $query->latest()->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    /**
     * Get single comment
     */
    public function getComment($id)
    {
        $comment = BlogComment::with(['post', 'user', 'replies.user'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $comment
        ]);
    }

    /**
     * Approve comment
     */
    public function approveComment($id)
    {
        try {
            $comment = BlogComment::findOrFail($id);
            $comment->approve();

            return response()->json([
                'success' => true,
                'message' => 'Comment approved successfully',
                'data' => $comment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve comment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject comment
     */
    public function rejectComment($id)
    {
        try {
            $comment = BlogComment::findOrFail($id);
            $comment->reject();

            return response()->json([
                'success' => true,
                'message' => 'Comment rejected successfully',
                'data' => $comment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject comment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete comment
     */
    public function deleteComment($id)
    {
        try {
            $comment = BlogComment::findOrFail($id);
            $comment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Comment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete comment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get blog analytics
     */
    public function getAnalytics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        // Posts analytics
        $postsAnalytics = [
            'total_views' => BlogPost::sum('views_count'),
            'total_likes' => BlogPost::sum('likes_count'),
            'posts_published_period' => BlogPost::where('published_at', '>=', $startDate)->count(),
            'avg_views_per_post' => BlogPost::avg('views_count'),
        ];

        // Comments analytics
        $commentsAnalytics = [
            'total_comments' => BlogComment::count(),
            'approved_comments' => BlogComment::where('status', 'approved')->count(),
            'pending_comments' => BlogComment::where('status', 'pending')->count(),
            'comments_in_period' => BlogComment::where('created_at', '>=', $startDate)->count(),
        ];

        // Top performing posts
        $topPosts = BlogPost::select('id', 'title', 'views_count', 'likes_count', 'slug')
            ->where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->limit(10)
            ->get();

        // Popular categories
        $popularCategories = BlogCategory::withCount('posts')
            ->having('posts_count', '>', 0)
            ->orderBy('posts_count', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'posts' => $postsAnalytics,
                'comments' => $commentsAnalytics,
                'top_posts' => $topPosts,
                'popular_categories' => $popularCategories,
            ]
        ]);
    }
} 