<?php

echo "=== SIMPLE INTEGRATION TEST ===\n\n";

// Test 1: Basic API functionality
echo "1. Testing Basic API...\n";
$api_url = 'http://localhost:8000/api/v1/public-scholarships';

$response = file_get_contents($api_url);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "   ✓ API working - Found " . count($data['data']) . " scholarships\n";
        
        // Test individual scholarship
        $first_id = $data['data'][0]['id'];
        $detail_response = file_get_contents($api_url . '/' . $first_id);
        $detail_data = json_decode($detail_response, true);
        
        if ($detail_data && $detail_data['success']) {
            echo "   ✓ Individual scholarship API working\n";
            
            $scholarship = $detail_data['data'];
            echo "   ✓ Scholarship: " . $scholarship['title'] . "\n";
            echo "   ✓ Category: " . $scholarship['category'] . "\n";
            echo "   ✓ Amount: ₦" . number_format($scholarship['amount']) . "\n";
            
            if (isset($scholarship['category_instructions'])) {
                echo "   ✓ Category instructions available\n";
            }
            
            if (isset($scholarship['custom_fields']) && count($scholarship['custom_fields']) > 0) {
                echo "   ✓ Custom fields available (" . count($scholarship['custom_fields']) . " fields)\n";
            }
        }
    }
} else {
    echo "   ✗ API not responding\n";
}

echo "\n";

// Test 2: Frontend accessibility
echo "2. Testing Frontend...\n";

$frontend_urls = [
    'http://localhost:3000/scholarships' => 'Scholarships page',
    'http://localhost:3000/scholarships/1' => 'Scholarship detail page',
    'http://localhost:3000/scholarships/1/apply' => 'Application page'
];

foreach ($frontend_urls as $url => $description) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response !== false) {
        echo "   ✓ {$description} accessible\n";
    } else {
        echo "   ⚠ {$description} not accessible (server may not be running)\n";
    }
}

echo "\n";

// Test 3: Category filtering
echo "3. Testing Category Filtering...\n";

$categories = ['primary', 'secondary', 'university'];
foreach ($categories as $category) {
    $filter_url = $api_url . '?category=' . $category;
    $response = file_get_contents($filter_url);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "   ✓ {$category} filter: " . count($data['data']) . " scholarships\n";
        }
    }
}

echo "\n";

echo "=== MANUAL TESTING CHECKLIST ===\n";
echo "Please verify the following manually:\n\n";

echo "□ Open http://localhost:3000/scholarships\n";
echo "  - Page loads without errors\n";
echo "  - Scholarships are displayed in cards\n";
echo "  - Search and filter work\n";
echo "  - Mobile responsive design\n\n";

echo "□ Click on a scholarship card\n";
echo "  - Detail page loads\n";
echo "  - All scholarship information displayed\n";
echo "  - Category instructions shown\n";
echo "  - Apply button works\n\n";

echo "□ Click 'Apply Now' button\n";
echo "  - Application form loads\n";
echo "  - Dynamic fields based on scholarship\n";
echo "  - Form validation works\n";
echo "  - File upload fields present\n\n";

echo "□ Test mobile responsiveness\n";
echo "  - Use browser dev tools\n";
echo "  - Test different screen sizes\n";
echo "  - Verify cards stack properly\n";
echo "  - Forms are mobile-friendly\n\n";

echo "□ Test error handling\n";
echo "  - Try invalid scholarship ID\n";
echo "  - Test with network issues\n";
echo "  - Verify error messages display\n\n";

echo "Integration test completed!\n";

?>
