<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - HALIMAKQ NGO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-green: #059669;
            --secondary-green: #047857;
        }
        
        .navbar-brand {
            color: var(--primary-green) !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            border: none;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>HALIMAKQ NGO - Student Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo e(Auth::user()->first_name); ?>

                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Student Dashboard</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Welcome to your student dashboard, <?php echo e(Auth::user()->first_name); ?>!
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-graduation-cap fa-3x text-success mb-3"></i>
                                        <h5>Apply for Scholarships</h5>
                                        <p class="text-muted">Browse and apply for university scholarships</p>
                                        <a href="#" class="btn btn-primary">View Scholarships</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                                        <h5>My Applications</h5>
                                        <p class="text-muted">Track your scholarship applications</p>
                                        <a href="#" class="btn btn-outline-primary">View Applications</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-book fa-3x text-warning mb-3"></i>
                                        <h5>Resources</h5>
                                        <p class="text-muted">Access educational resources and guides</p>
                                        <a href="#" class="btn btn-outline-primary">Browse Resources</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Your Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> <?php echo e(Auth::user()->full_name); ?></p>
                                    <p><strong>Email:</strong> <?php echo e(Auth::user()->email); ?></p>
                                    <p><strong>Role:</strong> <?php echo e(Auth::user()->role_name); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <?php if(Auth::user()->university_name): ?>
                                        <p><strong>University:</strong> <?php echo e(Auth::user()->university_name); ?></p>
                                    <?php endif; ?>
                                    <?php if(Auth::user()->course_of_study): ?>
                                        <p><strong>Course:</strong> <?php echo e(Auth::user()->course_of_study); ?></p>
                                    <?php endif; ?>
                                    <?php if(Auth::user()->year_of_study): ?>
                                        <p><strong>Year:</strong> <?php echo e(Auth::user()->year_of_study); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/dashboards/student/index.blade.php ENDPATH**/ ?>