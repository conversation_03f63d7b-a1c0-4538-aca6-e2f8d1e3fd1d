<?php $__env->startSection('title', 'Student Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'student','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                    ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                    ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                    ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'student','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                    ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                    ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                    ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'student','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                        ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                        ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                        ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'student','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
                        ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
                        ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
                        ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Welcome, <?php echo e(Auth::user()->first_name); ?>!</h1>
                            <p class="text-green-100 text-sm lg:text-base">Manage your scholarship applications and track your progress</p>
                            <?php if(Auth::user()->university_name): ?>
                                <p class="text-green-200 text-xs lg:text-sm mt-1"><?php echo e(Auth::user()->university_name); ?> - <?php echo e(Auth::user()->course_of_study); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/applications" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-file-alt mr-2"></i>
                                My Applications
                            </a>
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-user-graduate mr-2"></i>
                                Academic Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Available</p>
                                <p class="text-xl font-bold text-green-600"><?php echo e(\App\Models\Scholarship::where('status', 'active')->where('category', 'university')->count()); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">My Apps</p>
                                <p class="text-xl font-bold text-blue-600"><?php echo e(Auth::user()->scholarshipApplications()->count()); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-chart-line text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Status</p>
                                <p class="text-sm font-bold text-purple-600"><?php echo e(ucfirst(str_replace('_', ' ', Auth::user()->scholarshipApplications()->latest()->first()->status ?? 'None'))); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-check text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Profile</p>
                                <p class="text-xl font-bold text-indigo-600"><?php echo e(Auth::user()->getProfileCompletionPercentage()); ?>%</p>
                            </div>
                        </div>
                    </div>

                    <!-- Application Status -->
                    <?php
                        $activeApplication = Auth::user()->activeScholarshipApplication();
                    ?>

                    <?php if($activeApplication): ?>
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-alt text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Your Scholarship Application</h2>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-700"><?php echo e($activeApplication->scholarship->title ?? 'University Scholarship'); ?></h3>
                                    <p class="text-gray-600 text-sm">Applied on: <?php echo e($activeApplication->created_at->format('M d, Y')); ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                        <?php if($activeApplication->status === 'approved'): ?> bg-green-100 text-green-800
                                        <?php elseif($activeApplication->status === 'rejected'): ?> bg-red-100 text-red-800
                                        <?php elseif($activeApplication->status === 'under_review'): ?> bg-yellow-100 text-yellow-800
                                        <?php else: ?> bg-blue-100 text-blue-800
                                        <?php endif; ?>">
                                        <i class="fas <?php echo e($activeApplication->status === 'approved' ? 'fa-check-circle' : ($activeApplication->status === 'rejected' ? 'fa-times-circle' : 'fa-clock')); ?> mr-1"></i>
                                        <?php echo e(ucfirst(str_replace('_', ' ', $activeApplication->status))); ?>

                                    </span>
                                </div>
                            </div>

                            <?php if($activeApplication->status === 'approved'): ?>
                                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                        <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                                    </div>
                                    <div class="mt-2">
                                        <a href="#" class="text-green-600 hover:text-green-800 font-medium text-sm">Download Approval Letter (PDF) →</a>
                                    </div>
                                </div>
                            <?php elseif($activeApplication->status === 'rejected'): ?>
                                <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-times-circle text-red-600 mr-2"></i>
                                        <span class="text-red-800 font-medium">Your application was not approved this time.</span>
                                    </div>
                                    <?php if($activeApplication->rejection_reason): ?>
                                        <p class="text-red-700 mt-2 text-sm"><?php echo e($activeApplication->rejection_reason); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <!-- No Active Application -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center mb-8">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Active Application</h3>
                            <p class="text-gray-600 mb-6">You haven't applied for any scholarships yet. University students can apply for one scholarship.</p>
                            <a href="<?php echo e(route('scholarships')); ?>" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- University Scholarships Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-graduation-cap text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">University Scholarships</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Browse available scholarships for university students.</p>
                            <a href="<?php echo e(route('scholarships')); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-search mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>

                        <!-- Academic Profile Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-graduate text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">Academic Profile</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Update your academic information and credentials.</p>
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-edit mr-2"></i>
                                Update Profile
                            </a>
                        </div>

                        <!-- Support Center Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-life-ring text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">Support Center</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Get help and support for your applications.</p>
                            <a href="<?php echo e(route('contact')); ?>" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-headset mr-2"></i>
                                Get Support
                            </a>
                        </div>
                    </div>



                    <!-- Student Information and Resources -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Academic Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-university text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Academic Information</h2>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">University:</span>
                                    <span class="font-medium text-gray-900 text-sm"><?php echo e(Auth::user()->university_name ?? 'Not specified'); ?></span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Course of Study:</span>
                                    <span class="font-medium text-gray-900 text-sm"><?php echo e(Auth::user()->course_of_study ?? 'Not specified'); ?></span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Year of Study:</span>
                                    <span class="font-medium text-gray-900 text-sm"><?php echo e(Auth::user()->year_of_study ?? 'Not specified'); ?></span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Student ID:</span>
                                    <span class="font-medium text-gray-900 text-sm"><?php echo e(Auth::user()->student_id ?? 'Not specified'); ?></span>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <a href="/student/profile" class="text-green-600 hover:text-green-700 font-medium text-sm">Update Information →</a>
                            </div>
                        </div>

                        <!-- Application Guidelines -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-info-circle text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Application Guidelines</h2>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">1</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">University students can apply for one scholarship at a time.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">2</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">Ensure all required documents are uploaded before submission.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">3</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">You can track your application status from this dashboard.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">4</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">Approved applications will generate a PDF approval letter.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Applications -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">Recent Applications</h2>
                            </div>
                            <a href="/student/applications" class="text-green-600 hover:text-green-700 font-medium text-sm">View All →</a>
                        </div>
                        <?php
                            $recentApplications = Auth::user()->scholarshipApplications()->with('scholarship')->latest()->limit(5)->get();
                        ?>

                        <?php if($recentApplications->count() > 0): ?>
                            <div class="space-y-3">
                                <?php $__currentLoopData = $recentApplications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow duration-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <h3 class="font-medium text-gray-900 text-sm"><?php echo e($application->scholarship->title ?? 'N/A'); ?></h3>
                                                <p class="text-xs text-gray-600 mt-1"><?php echo e(ucfirst($application->scholarship->category ?? 'N/A')); ?> • Applied <?php echo e($application->submitted_at ? $application->submitted_at->format('M j, Y') : 'N/A'); ?></p>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <?php if($application->status === 'approved'): ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        Approved
                                                    </span>
                                                <?php elseif($application->status === 'rejected'): ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-times-circle mr-1"></i>
                                                        Rejected
                                                    </span>
                                                <?php elseif($application->status === 'pending'): ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Pending
                                                    </span>
                                                <?php else: ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <i class="fas fa-question-circle mr-1"></i>
                                                        <?php echo e(ucfirst($application->status)); ?>

                                                    </span>
                                                <?php endif; ?>
                                                <?php if($application->status === 'approved'): ?>
                                                    <a href="/api/v1/scholarship-applications/<?php echo e($application->id); ?>/pdf"
                                                       class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
                                                       target="_blank">
                                                        <i class="fas fa-download mr-1"></i> PDF
                                                    </a>
                                                <?php endif; ?>
                                                <a href="/student/applications/<?php echo e($application->id); ?>" class="text-gray-400 hover:text-gray-600">
                                                    <i class="fas fa-chevron-right text-xs"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-700 mb-2">No Applications Yet</h3>
                                <p class="text-gray-600 mb-6">You haven't submitted any applications yet.</p>
                                <a href="/scholarships" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-graduation-cap mr-2"></i>
                                    Browse Scholarships
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Document Management -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-folder text-white text-sm"></i>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">Document Management</h2>
                            </div>
                            <a href="/student/documents" class="text-green-600 hover:text-green-700 font-medium text-sm">Manage All →</a>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-pdf text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Academic Transcripts</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Upload your official transcripts</p>
                                <a href="/student/documents" class="text-green-600 hover:text-green-700 text-xs font-medium">Manage Documents →</a>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-id-card text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Identification</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Student ID and personal documents</p>
                                <a href="/student/documents" class="text-green-600 hover:text-green-700 text-xs font-medium">Manage Documents →</a>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-certificate text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Certificates</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Awards and achievement certificates</p>
                                <a href="/student/documents" class="text-green-600 hover:text-green-700 text-xs font-medium">Manage Documents →</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                const mobileSidebar = document.getElementById('mobile-sidebar');
                const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
                const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

                function openMobileMenu() {
                    mobileSidebar.classList.remove('hidden');
                    setTimeout(() => {
                        mobileSidebarContent.classList.remove('-translate-x-full');
                    }, 10);
                }

                function closeMobileMenu() {
                    mobileSidebarContent.classList.add('-translate-x-full');
                    setTimeout(() => {
                        mobileSidebar.classList.add('hidden');
                    }, 300);
                }

                mobileMenuBtn?.addEventListener('click', openMobileMenu);
                mobileSidebarOverlay?.addEventListener('click', closeMobileMenu);
            });
        </script>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/dashboards/student/index.blade.php ENDPATH**/ ?>