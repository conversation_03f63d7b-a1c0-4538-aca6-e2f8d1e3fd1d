"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Heart,
  Mail,
  Lock,
  ArrowLeft,
  Eye,
  EyeOff,
  User,
  Phone,
  MapPin,
  GraduationCap,
  Briefcase,
  Users,

} from "lucide-react"
import { apiClient } from "@/lib/api"
import { useSettings } from "@/hooks/useSettings"


type UserType = "student" | "partner" | "volunteer" | null

export default function RegisterPage() {
  const router = useRouter()
  const { settings } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [userType, setUserType] = useState<UserType>(null)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    address: "",

    city: "",
    state: "",
    country: "",
    // Student specific
    institution: "",
    course: "",
    yearOfStudy: "",
    studentId: "",
    matriculationNumber: "",
    currentGPA: "",
    expectedGraduation: "",
    fieldOfStudy: "",
    academicLevel: "",
    // Partner specific
    organizationName: "",
    organizationType: "",
    position: "",
    website: "",
    registrationNumber: "",
    facilities: "",
    programsOffered: "",
    // Volunteer specific
    skills: "",
    availability: "",
    experience: "",
    motivation: "",
  })
  const [successMessage, setSuccessMessage] = useState("")

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // Function to get additional data based on user type
  const getAdditionalData = () => {
    switch (userType) {
      case "student":
        return {
          institution: formData.institution,
          course: formData.course,
          year_of_study: formData.yearOfStudy,
          student_id: formData.studentId,
          matriculation_number: formData.matriculationNumber,
          current_gpa: formData.currentGPA,
          expected_graduation: formData.expectedGraduation,
          field_of_study: formData.fieldOfStudy,
          academic_level: formData.academicLevel,
        }
      case "partner":
        return {
          organization_name: formData.organizationName,
          organization_type: formData.organizationType,
          position: formData.position,
          website: formData.website,
          registration_number: formData.registrationNumber,
          facilities: formData.facilities,
          programs_offered: formData.programsOffered,
        }
      case "volunteer":
        return {
          skills: formData.skills,
          availability: formData.availability,
          experience: formData.experience,
          motivation: formData.motivation,
        }
      default:
        return null
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Basic validation (can be expanded)
    if (formData.password !== formData.confirmPassword) {
      alert("Passwords do not match")
      setIsLoading(false)
      return
    }

    // Ensure user type is selected
    if (!userType) {
      alert("Please select a user type")
      setIsLoading(false)
      return
    }

    try {
      // First, check if the email is already registered
      const emailCheckResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/check-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ email: formData.email })
      })

      if (emailCheckResponse.ok) {
        const emailCheck = await emailCheckResponse.json()
        if (emailCheck.exists) {
          alert("An account with this email already exists. Please use a different email or login to your existing account.")
          setIsLoading(false)
          return
        }
      }

      // Create user data object based on current form structure
      const userData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        password: formData.password,
        password_confirmation: formData.confirmPassword,
        phone_number: formData.phone,
        address: formData.address,

        city: formData.city,
        state: formData.state,
        country: formData.country,
        user_type: userType,
        additional_data: userType ? getAdditionalData() : null,
      }

      const data = await apiClient.register(userData)

      if (data.success) {
        setSuccessMessage(data.message || "Registration successful!")
        alert(data.message || "Registration successful! Please login to access your dashboard.")

        // Check if backend indicates redirect to login
        if (data.data?.redirect_to_login) {
          console.log('Registration successful, redirecting to login page')
          router.push("/auth/login")
        } else {
          // Fallback to login page
          console.log('Registration successful, redirecting to login page (fallback)')
          router.push("/auth/login")
        }
      } else {
        // Handle errors
        alert(data.message || "Registration failed. Please try again.")
        if (data.errors) {
          console.error("Validation errors:", data.errors)
          // You could display these errors to the user
        }
      }
    } catch (error) {
      console.error("Registration error:", error)
      alert("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const renderUserTypeSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
          How would you like to join us?
        </h3>
        <p className="text-green-600 dark:text-green-400 text-sm">
          Select your role to customize your registration experience
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
            userType === "student"
              ? "border-green-500 bg-green-50 dark:bg-green-900/20"
              : "border-green-200 dark:border-green-700 hover:border-green-300"
          }`}
          onClick={() => setUserType("student")}
        >
          <CardContent className="flex items-center gap-4 p-4">
            <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <GraduationCap className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-green-800 dark:text-green-200">Student</h4>
              <p className="text-sm text-green-600 dark:text-green-400">
                Apply for scholarships and access educational resources
              </p>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
            userType === "partner"
              ? "border-green-500 bg-green-50 dark:bg-green-900/20"
              : "border-green-200 dark:border-green-700 hover:border-green-300"
          }`}
          onClick={() => setUserType("partner")}
        >
          <CardContent className="flex items-center gap-4 p-4">
            <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <Briefcase className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-green-800 dark:text-green-200">Partner Organization</h4>
              <p className="text-sm text-green-600 dark:text-green-400">
                Collaborate with us to expand educational opportunities
              </p>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
            userType === "volunteer"
              ? "border-green-500 bg-green-50 dark:bg-green-900/20"
              : "border-green-200 dark:border-green-700 hover:border-green-300"
          }`}
          onClick={() => setUserType("volunteer")}
        >
          <CardContent className="flex items-center gap-4 p-4">
            <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-green-800 dark:text-green-200">Volunteer</h4>
              <p className="text-sm text-green-600 dark:text-green-400">
                Contribute your time and skills to support our mission
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {userType && (
        <Button onClick={() => {}} className="w-full bg-green-600 hover:bg-green-700 text-white">
          Continue as {userType.charAt(0).toUpperCase() + userType.slice(1)}
        </Button>
      )}
    </div>
  )

  const renderBasicFields = () => (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-green-800 dark:text-green-200">
            First Name *
          </Label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
            <Input
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              placeholder="John"
              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
              required
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-green-800 dark:text-green-200">
            Last Name *
          </Label>
          <Input
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleInputChange}
            placeholder="Doe"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-green-800 dark:text-green-200">
          Email Address *
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="<EMAIL>"
            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone" className="text-green-800 dark:text-green-200">
          Phone Number *
        </Label>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          <Input
            id="phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="+234 ************"
            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="address" className="text-green-800 dark:text-green-200">
          Address
        </Label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          <Input
            id="address"
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="Street address"
            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city" className="text-green-800 dark:text-green-200">
            City
          </Label>
          <Input
            id="city"
            name="city"
            value={formData.city}
            onChange={handleInputChange}
            placeholder="Lagos"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="state" className="text-green-800 dark:text-green-200">
            State
          </Label>
          <Select onValueChange={(value) => handleSelectChange("state", value)}>
            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
              <SelectValue placeholder="Select state" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Lagos">Lagos</SelectItem>
              <SelectItem value="Abuja">Abuja (FCT)</SelectItem>
              <SelectItem value="Kano">Kano</SelectItem>
              <SelectItem value="Rivers">Rivers</SelectItem>
              <SelectItem value="Ogun">Ogun</SelectItem>
              <SelectItem value="Kaduna">Kaduna</SelectItem>
              <SelectItem value="Oyo">Oyo</SelectItem>
              <SelectItem value="Delta">Delta</SelectItem>
              <SelectItem value="Imo">Imo</SelectItem>
              <SelectItem value="Anambra">Anambra</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="country" className="text-green-800 dark:text-green-200">
            Country
          </Label>
          <Select onValueChange={(value) => handleSelectChange("country", value)}>
            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
              <SelectValue placeholder="Select country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Nigeria">Nigeria</SelectItem>
              <SelectItem value="Ghana">Ghana</SelectItem>
              <SelectItem value="Kenya">Kenya</SelectItem>
              <SelectItem value="South Africa">South Africa</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>



      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="password" className="text-green-800 dark:text-green-200">
            Password *
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange}
              placeholder="••••••••"
              className="pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-green-800 dark:text-green-200">
            Confirm Password *
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="••••••••"
              className="pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
              required
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600"
            >
              {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>
    </>
  )

  const renderStudentFields = () => (
    <>
      <div className="space-y-2">
        <Label htmlFor="institution" className="text-green-800 dark:text-green-200">
          Educational Institution *
        </Label>
        <Input
          id="institution"
          name="institution"
          value={formData.institution}
          onChange={handleInputChange}
          placeholder="University of Lagos"
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="course" className="text-green-800 dark:text-green-200">
            Course of Study *
          </Label>
          <Input
            id="course"
            name="course"
            value={formData.course}
            onChange={handleInputChange}
            placeholder="Computer Science"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="yearOfStudy" className="text-green-800 dark:text-green-200">
            Year of Study *
          </Label>
          <Select onValueChange={(value) => handleSelectChange("yearOfStudy", value)}>
            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1st Year</SelectItem>
              <SelectItem value="2">2nd Year</SelectItem>
              <SelectItem value="3">3rd Year</SelectItem>
              <SelectItem value="4">4th Year</SelectItem>
              <SelectItem value="5">5th Year</SelectItem>
              <SelectItem value="graduate">Graduate</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="studentId" className="text-green-800 dark:text-green-200">
            Student ID Number
          </Label>
          <Input
            id="studentId"
            name="studentId"
            value={formData.studentId}
            onChange={handleInputChange}
            placeholder="STU/2024/001"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="matriculationNumber" className="text-green-800 dark:text-green-200">
            Matriculation Number *
          </Label>
          <Input
            id="matriculationNumber"
            name="matriculationNumber"
            value={formData.matriculationNumber}
            onChange={handleInputChange}
            placeholder="MAT/2024/12345"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="fieldOfStudy" className="text-green-800 dark:text-green-200">
          Field of Study *
        </Label>
        <Select onValueChange={(value) => handleSelectChange("fieldOfStudy", value)}>
          <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
            <SelectValue placeholder="Select field of study" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="medicine">Medicine & Health Sciences</SelectItem>
            <SelectItem value="sciences">Natural Sciences</SelectItem>
            <SelectItem value="social_sciences">Social Sciences</SelectItem>
            <SelectItem value="arts_humanities">Arts & Humanities</SelectItem>
            <SelectItem value="business">Business & Management</SelectItem>
            <SelectItem value="law">Law</SelectItem>
            <SelectItem value="education">Education</SelectItem>
            <SelectItem value="agriculture">Agriculture</SelectItem>
            <SelectItem value="technology">Information Technology</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="academicLevel" className="text-green-800 dark:text-green-200">
            Academic Level *
          </Label>
          <Select onValueChange={(value) => handleSelectChange("academicLevel", value)}>
            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="undergraduate">Undergraduate</SelectItem>
              <SelectItem value="graduate">Graduate/Masters</SelectItem>
              <SelectItem value="phd">PhD/Doctorate</SelectItem>
              <SelectItem value="postdoc">Post-Doctorate</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="currentGPA" className="text-green-800 dark:text-green-200">
            Current GPA/CGPA
          </Label>
          <Input
            id="currentGPA"
            name="currentGPA"
            value={formData.currentGPA}
            onChange={handleInputChange}
            placeholder="3.75 (out of 4.0 or 5.0)"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="expectedGraduation" className="text-green-800 dark:text-green-200">
          Expected Graduation Date
        </Label>
        <Input
          id="expectedGraduation"
          name="expectedGraduation"
          type="date"
          value={formData.expectedGraduation}
          onChange={handleInputChange}
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
        />
      </div>
    </>
  )

  const renderPartnerFields = () => (
    <>
      <div className="space-y-2">
        <Label htmlFor="organizationName" className="text-green-800 dark:text-green-200">
          Organization Name *
        </Label>
        <Input
          id="organizationName"
          name="organizationName"
          value={formData.organizationName}
          onChange={handleInputChange}
          placeholder="ABC Foundation"
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="organizationType" className="text-green-800 dark:text-green-200">
            Organization Type *
          </Label>
          <Select onValueChange={(value) => handleSelectChange("organizationType", value)}>
            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="primary_school">Primary School</SelectItem>
              <SelectItem value="secondary_school">Secondary School</SelectItem>
              <SelectItem value="university">University</SelectItem>
              <SelectItem value="ngo">NGO/Non-Profit</SelectItem>
              <SelectItem value="government">Government Agency</SelectItem>
              <SelectItem value="corporate">Corporate</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="position" className="text-green-800 dark:text-green-200">
            Your Position *
          </Label>
          <Input
            id="position"
            name="position"
            value={formData.position}
            onChange={handleInputChange}
            placeholder="Program Director"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="website" className="text-green-800 dark:text-green-200">
            Organization Website
          </Label>
          <Input
            id="website"
            name="website"
            type="url"
            value={formData.website}
            onChange={handleInputChange}
            placeholder="https://www.organization.com"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="registrationNumber" className="text-green-800 dark:text-green-200">
            Registration Number
          </Label>
          <Input
            id="registrationNumber"
            name="registrationNumber"
            value={formData.registrationNumber}
            onChange={handleInputChange}
            placeholder="RC123456 or CAC/IT/NO/123456"
            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="facilities" className="text-green-800 dark:text-green-200">
          Facilities & Infrastructure
        </Label>
        <Textarea
          id="facilities"
          name="facilities"
          value={formData.facilities}
          onChange={handleInputChange}
          placeholder="Describe your organization's facilities, classrooms, laboratories, etc."
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="programsOffered" className="text-green-800 dark:text-green-200">
          Programs Offered
        </Label>
        <Textarea
          id="programsOffered"
          name="programsOffered"
          value={formData.programsOffered}
          onChange={handleInputChange}
          placeholder="Describe the educational programs, courses, or services you offer"
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          rows={3}
        />
      </div>
    </>
  )

  const renderVolunteerFields = () => (
    <>
      <div className="space-y-2">
        <Label htmlFor="skills" className="text-green-800 dark:text-green-200">
          Skills & Expertise *
        </Label>
        <Textarea
          id="skills"
          name="skills"
          value={formData.skills}
          onChange={handleInputChange}
          placeholder="Teaching, IT support, project management, etc."
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          rows={3}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="availability" className="text-green-800 dark:text-green-200">
          Availability *
        </Label>
        <Select onValueChange={(value) => handleSelectChange("availability", value)}>
          <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
            <SelectValue placeholder="Select availability" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="weekends">Weekends only</SelectItem>
            <SelectItem value="evenings">Evenings (weekdays)</SelectItem>
            <SelectItem value="flexible">Flexible schedule</SelectItem>
            <SelectItem value="full-time">Full-time availability</SelectItem>
            <SelectItem value="seasonal">Seasonal/Project-based</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="experience" className="text-green-800 dark:text-green-200">
          Previous Volunteer Experience
        </Label>
        <Textarea
          id="experience"
          name="experience"
          value={formData.experience}
          onChange={handleInputChange}
          placeholder="Describe any previous volunteer work or community involvement..."
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="motivation" className="text-green-800 dark:text-green-200">
          Why do you want to volunteer with us? *
        </Label>
        <Textarea
          id="motivation"
          name="motivation"
          value={formData.motivation}
          onChange={handleInputChange}
          placeholder="Share your motivation for joining our mission..."
          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
          rows={4}
          required
        />
      </div>
    </>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
      {/* Header */}
      <header className="border-b border-green-200 dark:border-green-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
              {settings?.app_logo ? (
                <div className="relative h-10 w-10 overflow-hidden rounded-full">
                  <img 
                    src={settings.app_logo} 
                    alt={`${settings?.app_name || 'Laravel NGO Foundation'} Logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="h-10 w-10 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg">
                  <Heart className="h-6 w-6 text-white" />
                </div>
              )}
              <span className="text-xl font-bold text-green-800 dark:text-green-200">
                {settings?.app_name || 'Laravel NGO Foundation'}
              </span>
            </Link>
            <Link href="/auth/login">
              <Button variant="outline" className="border-green-300 text-green-600 hover:bg-green-50 dark:border-green-700 dark:text-green-400 dark:hover:bg-green-900/50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Login
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95">
            <CardHeader className="text-center pb-6 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-800/50">
              <div className="flex justify-center mb-4">
                {settings?.app_logo ? (
                  <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float">
                    <img 
                      src={settings.app_logo} 
                      alt={`${settings?.app_name || 'Laravel NGO Foundation'} Logo`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float">
                    <Heart className="absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md" />
                  </div>
                )}
              </div>
              <CardTitle className="text-3xl font-bold text-green-800 dark:text-green-200">
                Join {settings?.app_name || 'Laravel NGO Foundation'}
              </CardTitle>
              <CardDescription className="text-green-600 dark:text-green-400">
                Start your journey towards making a positive impact in communities
              </CardDescription>
            </CardHeader>

            <CardContent>
              {!userType ? (
                renderUserTypeSelection()
              ) : (
                <form onSubmit={handleRegister} className="space-y-6">
                  {renderBasicFields()}

                  <div className="space-y-2">
                    <Label htmlFor="address" className="text-green-800 dark:text-green-200">
                      Address *
                    </Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                      <Textarea
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        placeholder="Your full address"
                        className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                        rows={2}
                        required
                      />
                    </div>
                  </div>



                  {userType === "student" && renderStudentFields()}
                  {userType === "partner" && renderPartnerFields()}
                  {userType === "volunteer" && renderVolunteerFields()}

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      required
                      className="border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label htmlFor="terms" className="text-sm font-normal text-green-700 dark:text-green-300">
                      I agree to the{" "}
                      <Link
                        href="/terms"
                        className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline"
                      >
                        Terms of Service
                      </Link>{" "}
                      and{" "}
                      <Link
                        href="/privacy"
                        className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline"
                      >
                        Privacy Policy
                      </Link>
                    </Label>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setUserType(null)}
                      className="flex-1 border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400"
                    >
                      Back
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 font-semibold"
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Creating Account...
                        </div>
                      ) : (
                        "Create Account"
                      )}
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>

            <div className="px-6 pb-6">
              <div className="text-center">
                <p className="text-sm text-green-600 dark:text-green-400">
                  Already have an account?{" "}
                  <Link
                    href="/auth/login"
                    className="text-green-700 hover:text-green-800 dark:text-green-300 dark:hover:text-green-200 font-medium underline"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>

              <div className="flex justify-center mt-4">
                <Link
                  href="/"
                  className="flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group"
                >
                  <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
                  Back to home
                </Link>
              </div>
            </div>
          </Card>
        </div>
      </main>
    </div>
  )
}
