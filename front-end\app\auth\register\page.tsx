"use client"

import Link from "next/link"
import { ArrowLeft, Heart, GraduationCap, Building2, Users } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export default function RegisterPage() {
  const laravelBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

  const handleUserTypeSelection = (userType: string) => {
    // Redirect to Laravel registration with user type pre-selected
    window.location.href = `${laravelBaseUrl}/register?type=${userType}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-amber-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
              <Heart className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Join HLTKKQ Foundation</h1>
          <p className="text-green-600 text-lg mb-4">Start your journey towards making a positive impact in communities</p>

          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-gray-800">How would you like to join us?</h2>
            <p className="text-green-600">Select your role to customize your registration experience</p>
          </div>
        </div>

        {/* User Type Selection Cards */}
        <div className="space-y-4 mb-8">
          {/* Student Card */}
          <Card
            className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 hover:border-green-500 bg-white/80 backdrop-blur-sm"
            onClick={() => handleUserTypeSelection('student')}
          >
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <GraduationCap className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Student</h3>
                  <p className="text-gray-600">Apply for scholarships and access educational resources</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Partner Organization Card */}
          <Card
            className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 hover:border-green-500 bg-white/80 backdrop-blur-sm"
            onClick={() => handleUserTypeSelection('partner_organization')}
          >
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Building2 className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Partner Organization</h3>
                  <p className="text-gray-600">Collaborate with us to expand educational opportunities</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Volunteer Card */}
          <Card
            className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 hover:border-green-500 bg-white/80 backdrop-blur-sm"
            onClick={() => handleUserTypeSelection('volunteer')}
          >
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Volunteer</h3>
                  <p className="text-gray-600">Contribute your time and skills to support our mission</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Already have account */}
        <div className="text-center mb-6">
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link
              href="/auth/login"
              className="text-green-600 hover:text-green-700 font-medium underline"
            >
              Sign in here
            </Link>
          </p>
        </div>

        {/* Back to Home */}
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to home
          </Link>
        </div>
      </div>
    </div>
  )
}