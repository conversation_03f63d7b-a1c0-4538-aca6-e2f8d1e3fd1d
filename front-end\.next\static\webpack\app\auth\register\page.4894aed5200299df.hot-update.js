"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/register/page",{

/***/ "(app-pages-browser)/./app/auth/register/page.tsx":
/*!************************************!*\
  !*** ./app/auth/register/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Eye,EyeOff,GraduationCap,Heart,Lock,Mail,MapPin,Phone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { settings } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userType, setUserType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        phone: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        address: \"\",\n        city: \"\",\n        state: \"\",\n        country: \"\",\n        // Student specific\n        institution: \"\",\n        course: \"\",\n        yearOfStudy: \"\",\n        studentId: \"\",\n        matriculationNumber: \"\",\n        currentGPA: \"\",\n        expectedGraduation: \"\",\n        fieldOfStudy: \"\",\n        academicLevel: \"\",\n        // Partner specific\n        organizationName: \"\",\n        organizationType: \"\",\n        position: \"\",\n        website: \"\",\n        registrationNumber: \"\",\n        facilities: \"\",\n        programsOffered: \"\",\n        // Volunteer specific\n        skills: \"\",\n        availability: \"\",\n        experience: \"\",\n        motivation: \"\"\n    });\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Function to get additional data based on user type\n    const getAdditionalData = ()=>{\n        switch(userType){\n            case \"student\":\n                return {\n                    institution: formData.institution,\n                    course: formData.course,\n                    year_of_study: formData.yearOfStudy,\n                    student_id: formData.studentId,\n                    matriculation_number: formData.matriculationNumber,\n                    current_gpa: formData.currentGPA,\n                    expected_graduation: formData.expectedGraduation,\n                    field_of_study: formData.fieldOfStudy,\n                    academic_level: formData.academicLevel\n                };\n            case \"partner\":\n                return {\n                    organization_name: formData.organizationName,\n                    organization_type: formData.organizationType,\n                    position: formData.position,\n                    website: formData.website,\n                    registration_number: formData.registrationNumber,\n                    facilities: formData.facilities,\n                    programs_offered: formData.programsOffered\n                };\n            case \"volunteer\":\n                return {\n                    skills: formData.skills,\n                    availability: formData.availability,\n                    experience: formData.experience,\n                    motivation: formData.motivation\n                };\n            default:\n                return null;\n        }\n    };\n    const handleRegister = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        // Basic validation (can be expanded)\n        if (formData.password !== formData.confirmPassword) {\n            alert(\"Passwords do not match\");\n            setIsLoading(false);\n            return;\n        }\n        // Ensure user type is selected\n        if (!userType) {\n            alert(\"Please select a user type\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // First, check if the email is already registered\n            const emailCheckResponse = await fetch(\"\".concat(\"http://localhost:8000\", \"/api/v1/check-email\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify({\n                    email: formData.email\n                })\n            });\n            if (emailCheckResponse.ok) {\n                const emailCheck = await emailCheckResponse.json();\n                if (emailCheck.exists) {\n                    alert(\"An account with this email already exists. Please use a different email or login to your existing account.\");\n                    setIsLoading(false);\n                    return;\n                }\n            }\n            // Create user data object based on current form structure\n            const userData = {\n                first_name: formData.firstName,\n                last_name: formData.lastName,\n                email: formData.email,\n                password: formData.password,\n                password_confirmation: formData.confirmPassword,\n                phone_number: formData.phone,\n                address: formData.address,\n                city: formData.city,\n                state: formData.state,\n                country: formData.country,\n                user_type: userType,\n                additional_data: userType ? getAdditionalData() : null\n            };\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.apiClient.register(userData);\n            if (data.success) {\n                var _data_data, _data_data1, _data_data2;\n                setSuccessMessage(data.message || \"Registration successful!\");\n                alert(data.message || \"Registration successful! Welcome to \".concat((settings === null || settings === void 0 ? void 0 : settings.app_name) || 'our platform', \"!\"));\n                // Store the token and user data for immediate login\n                if ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.token) {\n                    localStorage.setItem('authToken', data.data.token);\n                }\n                if ((_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.user) {\n                    localStorage.setItem('user', JSON.stringify(data.data.user));\n                }\n                // Role-based dashboard routing after registration\n                const userData = (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.user;\n                if (userData) {\n                    const userRole = userData.role;\n                    console.log('Registration redirect logic - User type selected:', userType);\n                    console.log('Registration redirect logic - User role:', userRole);\n                    console.log('Registration redirect logic - Partner org ID:', userData.partner_organization_id);\n                    console.log('Registration redirect logic - Full user data:', userData);\n                    if (userRole === 'partner_organization' || userData.partner_organization_id) {\n                        console.log('Redirecting to partner dashboard after registration');\n                        router.push(\"/dashboard/partner\") // Partner Organization Dashboard\n                        ;\n                    } else if (userRole === 'admin') {\n                        console.log('Redirecting to admin dashboard after registration');\n                        router.push(\"/dashboard\") // Admin Dashboard\n                        ;\n                    } else if (userRole === 'volunteer') {\n                        console.log('Redirecting to volunteer dashboard after registration');\n                        router.push(\"/dashboard/volunteer\") // Volunteer Dashboard\n                        ;\n                    } else if (userType === 'student' || userRole === 'student') {\n                        console.log('Redirecting to student dashboard after registration');\n                        router.push(\"/dashboard/student\") // Student Dashboard\n                        ;\n                    } else {\n                        console.log('Redirecting to default dashboard after registration');\n                        router.push(\"/dashboard\") // Default User Dashboard\n                        ;\n                    }\n                } else {\n                    console.log('No user data received, redirecting to default dashboard');\n                    router.push(\"/dashboard\") // Fallback to default dashboard\n                    ;\n                }\n            } else {\n                // Handle errors\n                alert(data.message || \"Registration failed. Please try again.\");\n                if (data.errors) {\n                    console.error(\"Validation errors:\", data.errors);\n                // You could display these errors to the user\n                }\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            alert(\"An unexpected error occurred. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderUserTypeSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-green-800 dark:text-green-200 mb-2\",\n                            children: \"How would you like to join us?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-600 dark:text-green-400 text-sm\",\n                            children: \"Select your role to customize your registration experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"cursor-pointer transition-all duration-200 hover:shadow-lg \".concat(userType === \"student\" ? \"border-green-500 bg-green-50 dark:bg-green-900/20\" : \"border-green-200 dark:border-green-700 hover:border-green-300\"),\n                            onClick: ()=>setUserType(\"student\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"flex items-center gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                children: \"Student\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                children: \"Apply for scholarships and access educational resources\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"cursor-pointer transition-all duration-200 hover:shadow-lg \".concat(userType === \"partner\" ? \"border-green-500 bg-green-50 dark:bg-green-900/20\" : \"border-green-200 dark:border-green-700 hover:border-green-300\"),\n                            onClick: ()=>setUserType(\"partner\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"flex items-center gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                children: \"Partner Organization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                children: \"Collaborate with us to expand educational opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"cursor-pointer transition-all duration-200 hover:shadow-lg \".concat(userType === \"volunteer\" ? \"border-green-500 bg-green-50 dark:bg-green-900/20\" : \"border-green-200 dark:border-green-700 hover:border-green-300\"),\n                            onClick: ()=>setUserType(\"volunteer\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"flex items-center gap-4 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                children: \"Volunteer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                children: \"Contribute your time and skills to support our mission\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 7\n                }, this),\n                userType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>{},\n                    className: \"w-full bg-green-600 hover:bg-green-700 text-white\",\n                    children: [\n                        \"Continue as \",\n                        userType.charAt(0).toUpperCase() + userType.slice(1)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n            lineNumber: 243,\n            columnNumber: 5\n        }, this);\n    const renderBasicFields = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"firstName\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"First Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"firstName\",\n                                            name: \"firstName\",\n                                            value: formData.firstName,\n                                            onChange: handleInputChange,\n                                            placeholder: \"John\",\n                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"lastName\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Last Name *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"lastName\",\n                                    name: \"lastName\",\n                                    value: formData.lastName,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Doe\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"email\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Email Address *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"email\",\n                                    name: \"email\",\n                                    type: \"email\",\n                                    value: formData.email,\n                                    onChange: handleInputChange,\n                                    placeholder: \"<EMAIL>\",\n                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"phone\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Phone Number *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"phone\",\n                                    name: \"phone\",\n                                    type: \"tel\",\n                                    value: formData.phone,\n                                    onChange: handleInputChange,\n                                    placeholder: \"+234 ************\",\n                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"address\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Address\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"address\",\n                                    name: \"address\",\n                                    value: formData.address,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Street address\",\n                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"city\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"City\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"city\",\n                                    name: \"city\",\n                                    value: formData.city,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Lagos\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"state\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"State\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    onValueChange: (value)=>handleSelectChange(\"state\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"Select state\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Lagos\",\n                                                    children: \"Lagos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Abuja\",\n                                                    children: \"Abuja (FCT)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Kano\",\n                                                    children: \"Kano\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Rivers\",\n                                                    children: \"Rivers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Ogun\",\n                                                    children: \"Ogun\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Kaduna\",\n                                                    children: \"Kaduna\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Oyo\",\n                                                    children: \"Oyo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Delta\",\n                                                    children: \"Delta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Imo\",\n                                                    children: \"Imo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Anambra\",\n                                                    children: \"Anambra\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"country\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Country\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    onValueChange: (value)=>handleSelectChange(\"country\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"Select country\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Nigeria\",\n                                                    children: \"Nigeria\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Ghana\",\n                                                    children: \"Ghana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Kenya\",\n                                                    children: \"Kenya\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"South Africa\",\n                                                    children: \"South Africa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"Other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"password\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: formData.password,\n                                            onChange: handleInputChange,\n                                            placeholder: \"••••••••\",\n                                            className: \"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 31\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 64\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Confirm Password *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange,\n                                            placeholder: \"••••••••\",\n                                            className: \"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 38\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 71\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true);\n    const renderStudentFields = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"institution\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Educational Institution *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            id: \"institution\",\n                            name: \"institution\",\n                            value: formData.institution,\n                            onChange: handleInputChange,\n                            placeholder: \"University of Lagos\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"course\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Course of Study *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"course\",\n                                    name: \"course\",\n                                    value: formData.course,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Computer Science\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"yearOfStudy\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Year of Study *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    onValueChange: (value)=>handleSelectChange(\"yearOfStudy\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"Select year\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"1\",\n                                                    children: \"1st Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"2\",\n                                                    children: \"2nd Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"3\",\n                                                    children: \"3rd Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"4\",\n                                                    children: \"4th Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"5\",\n                                                    children: \"5th Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"graduate\",\n                                                    children: \"Graduate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"studentId\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Student ID Number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"studentId\",\n                                    name: \"studentId\",\n                                    value: formData.studentId,\n                                    onChange: handleInputChange,\n                                    placeholder: \"STU/2024/001\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"matriculationNumber\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Matriculation Number *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"matriculationNumber\",\n                                    name: \"matriculationNumber\",\n                                    value: formData.matriculationNumber,\n                                    onChange: handleInputChange,\n                                    placeholder: \"MAT/2024/12345\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"fieldOfStudy\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Field of Study *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                            onValueChange: (value)=>handleSelectChange(\"fieldOfStudy\", value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                        placeholder: \"Select field of study\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"engineering\",\n                                            children: \"Engineering\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"medicine\",\n                                            children: \"Medicine & Health Sciences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"sciences\",\n                                            children: \"Natural Sciences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"social_sciences\",\n                                            children: \"Social Sciences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"arts_humanities\",\n                                            children: \"Arts & Humanities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"business\",\n                                            children: \"Business & Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"law\",\n                                            children: \"Law\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"education\",\n                                            children: \"Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"agriculture\",\n                                            children: \"Agriculture\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"technology\",\n                                            children: \"Information Technology\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"other\",\n                                            children: \"Other\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 611,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"academicLevel\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Academic Level *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    onValueChange: (value)=>handleSelectChange(\"academicLevel\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"Select level\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"undergraduate\",\n                                                    children: \"Undergraduate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"graduate\",\n                                                    children: \"Graduate/Masters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"phd\",\n                                                    children: \"PhD/Doctorate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"postdoc\",\n                                                    children: \"Post-Doctorate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"currentGPA\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Current GPA/CGPA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"currentGPA\",\n                                    name: \"currentGPA\",\n                                    value: formData.currentGPA,\n                                    onChange: handleInputChange,\n                                    placeholder: \"3.75 (out of 4.0 or 5.0)\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"expectedGraduation\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Expected Graduation Date\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            id: \"expectedGraduation\",\n                            name: \"expectedGraduation\",\n                            type: \"date\",\n                            value: formData.expectedGraduation,\n                            onChange: handleInputChange,\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true);\n    const renderPartnerFields = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"organizationName\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Organization Name *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            id: \"organizationName\",\n                            name: \"organizationName\",\n                            value: formData.organizationName,\n                            onChange: handleInputChange,\n                            placeholder: \"ABC Foundation\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"organizationType\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Organization Type *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                    onValueChange: (value)=>handleSelectChange(\"organizationType\", value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                placeholder: \"Select type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"primary_school\",\n                                                    children: \"Primary School\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"secondary_school\",\n                                                    children: \"Secondary School\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"university\",\n                                                    children: \"University\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"ngo\",\n                                                    children: \"NGO/Non-Profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"government\",\n                                                    children: \"Government Agency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                    value: \"corporate\",\n                                                    children: \"Corporate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"position\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Your Position *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"position\",\n                                    name: \"position\",\n                                    value: formData.position,\n                                    onChange: handleInputChange,\n                                    placeholder: \"Program Director\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 700,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"website\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Organization Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"website\",\n                                    name: \"website\",\n                                    type: \"url\",\n                                    value: formData.website,\n                                    onChange: handleInputChange,\n                                    placeholder: \"https://www.organization.com\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"registrationNumber\",\n                                    className: \"text-green-800 dark:text-green-200\",\n                                    children: \"Registration Number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"registrationNumber\",\n                                    name: \"registrationNumber\",\n                                    value: formData.registrationNumber,\n                                    onChange: handleInputChange,\n                                    placeholder: \"RC123456 or CAC/IT/NO/123456\",\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 735,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"facilities\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Facilities & Infrastructure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                            id: \"facilities\",\n                            name: \"facilities\",\n                            value: formData.facilities,\n                            onChange: handleInputChange,\n                            placeholder: \"Describe your organization's facilities, classrooms, laboratories, etc.\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 769,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 765,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"programsOffered\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Programs Offered\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                            id: \"programsOffered\",\n                            name: \"programsOffered\",\n                            value: formData.programsOffered,\n                            onChange: handleInputChange,\n                            placeholder: \"Describe the educational programs, courses, or services you offer\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 784,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 780,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true);\n    const renderVolunteerFields = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"skills\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Skills & Expertise *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                            id: \"skills\",\n                            name: \"skills\",\n                            value: formData.skills,\n                            onChange: handleInputChange,\n                            placeholder: \"Teaching, IT support, project management, etc.\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            rows: 3,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 803,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"availability\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Availability *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                            onValueChange: (value)=>handleSelectChange(\"availability\", value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                        placeholder: \"Select availability\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"weekends\",\n                                            children: \"Weekends only\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"evenings\",\n                                            children: \"Evenings (weekdays)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"flexible\",\n                                            children: \"Flexible schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"full-time\",\n                                            children: \"Full-time availability\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                            value: \"seasonal\",\n                                            children: \"Seasonal/Project-based\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"experience\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Previous Volunteer Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 834,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                            id: \"experience\",\n                            name: \"experience\",\n                            value: formData.experience,\n                            onChange: handleInputChange,\n                            placeholder: \"Describe any previous volunteer work or community involvement...\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 833,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                            htmlFor: \"motivation\",\n                            className: \"text-green-800 dark:text-green-200\",\n                            children: \"Why do you want to volunteer with us? *\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                            id: \"motivation\",\n                            name: \"motivation\",\n                            value: formData.motivation,\n                            onChange: handleInputChange,\n                            placeholder: \"Share your motivation for joining our mission...\",\n                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                            rows: 4,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b border-green-200 dark:border-green-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center gap-3 hover:opacity-80 transition-opacity\",\n                                children: [\n                                    (settings === null || settings === void 0 ? void 0 : settings.app_logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-10 w-10 overflow-hidden rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: settings.app_logo,\n                                            alt: \"\".concat((settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO Foundation', \" Logo\"),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-green-800 dark:text-green-200\",\n                                        children: (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO Foundation'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"border-green-300 text-green-600 hover:bg-green-50 dark:border-green-700 dark:text-green-400 dark:hover:bg-green-900/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back to Login\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 870,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                lineNumber: 869,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"text-center pb-6 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-4\",\n                                        children: (settings === null || settings === void 0 ? void 0 : settings.app_logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: settings.app_logo,\n                                                alt: \"\".concat((settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO Foundation', \" Logo\"),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-3xl font-bold text-green-800 dark:text-green-200\",\n                                        children: [\n                                            \"Join \",\n                                            (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO Foundation'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                        className: \"text-green-600 dark:text-green-400\",\n                                        children: \"Start your journey towards making a positive impact in communities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                children: !userType ? renderUserTypeSelection() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleRegister,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        renderBasicFields(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"address\",\n                                                    className: \"text-green-800 dark:text-green-200\",\n                                                    children: \"Address *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"address\",\n                                                            name: \"address\",\n                                                            value: formData.address,\n                                                            onChange: handleInputChange,\n                                                            placeholder: \"Your full address\",\n                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                            rows: 2,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 19\n                                        }, this),\n                                        userType === \"student\" && renderStudentFields(),\n                                        userType === \"partner\" && renderPartnerFields(),\n                                        userType === \"volunteer\" && renderVolunteerFields(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                    id: \"terms\",\n                                                    required: true,\n                                                    className: \"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"terms\",\n                                                    className: \"text-sm font-normal text-green-700 dark:text-green-300\",\n                                                    children: [\n                                                        \"I agree to the\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/terms\",\n                                                            className: \"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline\",\n                                                            children: \"Terms of Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        \"and\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/privacy\",\n                                                            className: \"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline\",\n                                                            children: \"Privacy Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setUserType(null),\n                                                    className: \"flex-1 border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400\",\n                                                    children: \"Back\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    disabled: isLoading,\n                                                    className: \"flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 font-semibold\",\n                                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Creating Account...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 25\n                                                    }, this) : \"Create Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 pb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 dark:text-green-400\",\n                                            children: [\n                                                \"Already have an account?\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/login\",\n                                                    className: \"text-green-700 hover:text-green-800 dark:text-green-300 dark:hover:text-green-200 font-medium underline\",\n                                                    children: \"Sign in here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Eye_EyeOff_GraduationCap_Heart_Lock_Mail_MapPin_Phone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to home\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 1024,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                lineNumber: 1011,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                lineNumber: 900,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\register\\\\page.tsx\",\n        lineNumber: 867,\n        columnNumber: 5\n    }, this);\n}\n_s(RegisterPage, \"xrcBBYErSGL9YHDM5KkazUZQr2M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/register/page.tsx\n"));

/***/ })

});