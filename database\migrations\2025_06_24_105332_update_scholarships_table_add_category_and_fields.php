<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scholarships', function (Blueprint $table) {
            $table->enum('category', ['primary', 'secondary', 'university'])->after('title');
            $table->boolean('is_open')->default(true)->after('status');
            $table->json('custom_fields')->nullable()->after('documents_required');
            
            // Add index for category
            $table->index(['category', 'is_open']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scholarships', function (Blueprint $table) {
            $table->dropIndex(['category', 'is_open']);
            $table->dropColumn(['category', 'is_open', 'custom_fields']);
        });
    }
};
