 

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Edit Newsletter Campaign</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.newsletter.index') }}">Newsletter</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.newsletter.campaigns') }}">Campaigns</a></li>
                        <li class="breadcrumb-item active">Edit Campaign #{{ $id }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>Edit Newsletter Campaign
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.newsletter.campaigns.update', $id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="title" class="form-label">Campaign Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" value="December Newsletter" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subject" name="subject" value="Year-End Impact Report" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="content" class="form-label">Campaign Content <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="8" required>Thank you for your support in 2024!</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="draft">Draft</option>
                                        <option value="scheduled">Scheduled</option>
                                        <option value="sent" selected>Sent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="recipient_type" class="form-label">Send To</label>
                                    <select class="form-control" id="recipient_type" name="recipient_type">
                                        <option value="all" selected>All Subscribers</option>
                                        <option value="active">Active Subscribers Only</option>
                                        <option value="volunteers">Volunteers Only</option>
                                        <option value="donors">Donors Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Campaign
                            </button>
                            <a href="{{ route('admin.newsletter.campaigns') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Campaigns
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 
@extends('layouts.admin')
 