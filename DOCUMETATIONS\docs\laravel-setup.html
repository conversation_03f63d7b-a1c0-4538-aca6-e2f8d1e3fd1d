<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Setup Guide - HLTKKQ Foundation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <div class="sidebar-header">
                        <h3><PERSON>vel Setup</h3>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#prerequisites">
                                <i class="fas fa-check-circle"></i> Prerequisites
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#installation">
                                <i class="fas fa-download"></i> Installation
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#configuration">
                                <i class="fas fa-cog"></i> Configuration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#authentication">
                                <i class="fas fa-lock"></i> Authentication
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#cors">
                                <i class="fas fa-exchange-alt"></i> CORS Setup
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1>Laravel Backend Setup Guide</h1>
                </div>

                <!-- Prerequisites Section -->
                <section id="prerequisites" class="mb-5">
                    <h2>Prerequisites</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Required Software</h5>
                            <ul>
                                <li>PHP 8.2 or higher</li>
                                <li>Composer</li>
                                <li>MySQL 8.0 or higher</li>
                                <li>Node.js and NPM</li>
                                <li>Laragon (for Windows)</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Installation Section -->
                <section id="installation" class="mb-5">
                    <h2>Installation</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Step 1: Clone the Repository</h5>
                            <pre><code>git clone [repository-url]
cd hltkkq-foundation</code></pre>

                            <h5>Step 2: Install Dependencies</h5>
                            <pre><code>composer install
npm install</code></pre>

                            <h5>Step 3: Environment Setup</h5>
                            <pre><code>cp .env.example .env
php artisan key:generate</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Configuration Section -->
                <section id="configuration" class="mb-5">
                    <h2>Configuration</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Database Configuration</h5>
                            <p>Update your .env file with database credentials:</p>
                            <pre><code>DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=</code></pre>

                            <h5>Run Migrations</h5>
                            <pre><code>php artisan migrate
php artisan db:seed</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Authentication Section -->
                <section id="authentication" class="mb-5">
                    <h2>Authentication Setup</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Sanctum Configuration</h5>
                            <p>Update config/sanctum.php:</p>
                            <pre><code>return [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', 'localhost,localhost:3000')),
    'guard' => ['web'],
    'expiration' => null,
    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],
];</code></pre>

                            <h5>Update .env</h5>
                            <pre><code>SESSION_DOMAIN=.test
SANCTUM_STATEFUL_DOMAINS=localhost:3000
FRONTEND_URL=http://localhost:3000</code></pre>
                        </div>
                    </div>
                </section>

                <!-- CORS Section -->
                <section id="cors" class="mb-5">
                    <h2>CORS Configuration</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Update config/cors.php</h5>
                            <pre><code>return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['*'],
    'allowed_origins' => [env('FRONTEND_URL', 'http://localhost:3000')],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,
];</code></pre>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html> 