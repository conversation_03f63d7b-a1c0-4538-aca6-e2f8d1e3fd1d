<?php $__env->startSection('title', 'Scholarships - HALIMAKQ Foundation'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 text-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto text-center space-y-6">
                <div class="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-sm font-medium">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Scholarships
                </div>
                <h1 class="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                    Educational Scholarships
                </h1>
                <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                    Discover scholarship opportunities designed to support students at every educational level - from primary school through university.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('login')); ?>" class="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Apply Now
                    </a>
                    <a href="#scholarships" class="inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Browse Scholarships
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scholarship Categories -->
    <section class="py-16" id="scholarships">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Scholarship Categories</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    We offer scholarships across different educational levels to support students throughout their academic journey.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <!-- Primary Education -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-child text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Primary Education</h3>
                    <p class="text-gray-600 mb-6">Supporting young learners in grades 1-6 with essential educational resources and opportunities.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Grades 1-6</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>School fees coverage</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Learning materials</li>
                    </ul>
                </div>

                <!-- Secondary Education -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-school text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Secondary Education</h3>
                    <p class="text-gray-600 mb-6">Empowering teenagers to complete their secondary education and prepare for higher learning.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Junior & Senior Secondary</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Exam preparation</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Career guidance</li>
                    </ul>
                </div>

                <!-- University Education -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-graduation-cap text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">University Education</h3>
                    <p class="text-gray-600 mb-6">Supporting undergraduate and graduate students in pursuing higher education goals.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Undergraduate programs</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Tuition assistance</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Research support</li>
                    </ul>
                </div>
            </div>

            <!-- Available Scholarships -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <div class="flex items-center justify-between mb-8">
                    <h3 class="text-2xl font-bold text-gray-900">Available Scholarships</h3>
                    <div class="flex items-center space-x-4">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Categories</option>
                            <option value="primary">Primary</option>
                            <option value="secondary">Secondary</option>
                            <option value="university">University</option>
                        </select>
                        <div class="relative">
                            <input type="text" placeholder="Search scholarships..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <?php if(isset($scholarships) && $scholarships->count() > 0): ?>
                    <div class="grid gap-6">
                        <?php $__currentLoopData = $scholarships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scholarship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($scholarship->title); ?></h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo e(ucfirst($scholarship->category)); ?>

                                        </span>
                                        <span><i class="fas fa-calendar mr-1"></i>Deadline: <?php echo e($scholarship->application_deadline->format('M d, Y')); ?></span>
                                        <span><i class="fas fa-money-bill-wave mr-1"></i>₦<?php echo e(number_format($scholarship->amount)); ?></span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <?php if($scholarship->status === 'open'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-circle text-green-400 mr-1" style="font-size: 6px;"></i>
                                            Open
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-circle text-gray-400 mr-1" style="font-size: 6px;"></i>
                                            Closed
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <p class="text-gray-600 mb-4 line-clamp-2"><?php echo e($scholarship->description); ?></p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fas fa-users mr-1"></i>
                                    <?php echo e($scholarship->current_applicants ?? 0); ?> applicants
                                </div>
                                <div class="flex items-center space-x-3">
                                    <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">
                                        View Details
                                    </a>
                                    <?php if($scholarship->status === 'open'): ?>
                                        <a href="<?php echo e(route('login')); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-edit mr-2"></i>
                                            Apply Now
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Scholarships Available</h3>
                        <p class="text-gray-500 mb-6">There are currently no open scholarships. Please check back later.</p>
                        <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-home mr-2"></i>
                            Return Home
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- How to Apply Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">How to Apply</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Follow these simple steps to submit your scholarship application.
                </p>
            </div>

            <div class="grid md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">1</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Choose Scholarship</h3>
                    <p class="text-gray-600">Browse available scholarships and select the one that matches your educational level.</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">2</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Complete Application</h3>
                    <p class="text-gray-600">Fill out the application form with accurate information and required documents.</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">3</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Submit & Review</h3>
                    <p class="text-gray-600">Submit your application and wait for our team to review your submission.</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">4</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Get Results</h3>
                    <p class="text-gray-600">Receive notification about your application status and next steps.</p>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.querySelector('input[placeholder="Search scholarships..."]');
    const categorySelect = document.querySelector('select');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            // Add search functionality here
            console.log('Searching for:', this.value);
        });
    }
    
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            // Add filter functionality here
            console.log('Filtering by category:', this.value);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/scholarships.blade.php ENDPATH**/ ?>