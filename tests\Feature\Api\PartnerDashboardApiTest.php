<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\ScholarshipApplication;
use App\Models\StudentProgression;
use App\Models\Scholarship;
use Laravel\Sanctum\Sanctum;

class PartnerDashboardApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $partnerUser;
    protected $partnerOrganization;
    protected $adminUser;
    protected $studentUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create partner organization
        $this->partnerOrganization = PartnerOrganization::factory()->create([
            'name' => 'Test School',
            'type' => 'school',
            'status' => 'active'
        ]);

        // Create partner user
        $this->partnerUser = User::factory()->create([
            'role' => 'partner_organization',
            'partner_organization_id' => $this->partnerOrganization->id
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create student user
        $this->studentUser = User::factory()->create([
            'role' => 'user'
        ]);
    }

    /** @test */
    public function partner_can_access_dashboard_data()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create test data
        $students = Student::factory()->count(5)->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'is_open' => true
        ]);

        ScholarshipApplication::factory()->count(3)->create([
            'student_id' => $students->first()->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'approved'
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview' => [
                            'total_students',
                            'total_applications',
                            'approved_applications',
                            'success_rate'
                        ],
                        'recent_activities',
                        'upcoming_deadlines',
                        'progression_alerts',
                        'quick_stats',
                        'organization'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals(5, $response->json('data.overview.total_students'));
    }

    /** @test */
    public function non_partner_cannot_access_partner_dashboard()
    {
        Sanctum::actingAs($this->studentUser);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_dashboard()
    {
        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(401);
    }

    /** @test */
    public function dashboard_returns_correct_statistics()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create students
        $students = Student::factory()->count(10)->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        // Create scholarship
        $scholarship = Scholarship::factory()->create([
            'category' => 'secondary',
            'status' => 'active',
            'is_open' => true
        ]);

        // Create applications with different statuses
        ScholarshipApplication::factory()->count(5)->create([
            'student_id' => $students->random()->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'approved'
        ]);

        ScholarshipApplication::factory()->count(3)->create([
            'student_id' => $students->random()->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'pending'
        ]);

        ScholarshipApplication::factory()->count(2)->create([
            'student_id' => $students->random()->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'rejected'
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200);

        $overview = $response->json('data.overview');
        $this->assertEquals(10, $overview['total_students']);
        $this->assertEquals(10, $overview['total_applications']);
        $this->assertEquals(5, $overview['approved_applications']);
        $this->assertEquals(3, $overview['pending_applications']);
        $this->assertEquals(50.0, $overview['success_rate']); // 5/10 * 100
    }

    /** @test */
    public function dashboard_includes_recent_activities()
    {
        Sanctum::actingAs($this->partnerUser);

        $student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active'
        ]);

        // Create recent application
        ScholarshipApplication::factory()->create([
            'student_id' => $student->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'pending',
            'created_at' => now()->subHours(2)
        ]);

        // Create recent progression
        StudentProgression::factory()->create([
            'student_id' => $student->id,
            'from_grade' => 5,
            'to_grade' => 6,
            'status' => 'completed',
            'created_at' => now()->subHours(1)
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200);

        $activities = $response->json('data.recent_activities');
        $this->assertIsArray($activities);
        $this->assertGreaterThan(0, count($activities));

        // Check activity structure
        $this->assertArrayHasKey('type', $activities[0]);
        $this->assertArrayHasKey('title', $activities[0]);
        $this->assertArrayHasKey('description', $activities[0]);
        $this->assertArrayHasKey('timestamp', $activities[0]);
    }

    /** @test */
    public function dashboard_shows_upcoming_deadlines()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create scholarship with upcoming deadline
        Scholarship::factory()->create([
            'category' => 'secondary',
            'status' => 'active',
            'is_open' => true,
            'application_deadline' => now()->addDays(15)
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200);

        $deadlines = $response->json('data.upcoming_deadlines');
        $this->assertIsArray($deadlines);
    }

    /** @test */
    public function dashboard_shows_progression_alerts()
    {
        Sanctum::actingAs($this->partnerUser);

        $student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        // Create completed progression from last year (eligible for new progression)
        StudentProgression::factory()->create([
            'student_id' => $student->id,
            'grade_level' => 5,
            'academic_year' => now()->year - 1,
            'status' => 'completed',
            'is_current' => true
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200);

        $alerts = $response->json('data.progression_alerts');
        $this->assertIsArray($alerts);
    }

    /** @test */
    public function dashboard_includes_quick_stats()
    {
        Sanctum::actingAs($this->partnerUser);

        $student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'is_open' => true,
            'application_deadline' => now()->addDays(30)
        ]);

        // Create application this month
        ScholarshipApplication::factory()->create([
            'student_id' => $student->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'pending',
            'created_at' => now()->startOfMonth()->addDays(5)
        ]);

        $response = $this->getJson('/api/v1/partner-dashboard');

        $response->assertStatus(200);

        $quickStats = $response->json('data.quick_stats');
        $this->assertArrayHasKey('applications_this_month', $quickStats);
        $this->assertArrayHasKey('application_change', $quickStats);
        $this->assertArrayHasKey('active_scholarships', $quickStats);
        $this->assertArrayHasKey('pending_reviews', $quickStats);

        $this->assertEquals(1, $quickStats['applications_this_month']);
        $this->assertEquals(1, $quickStats['active_scholarships']);
        $this->assertEquals(1, $quickStats['pending_reviews']);
    }

    /** @test */
    public function admin_can_access_any_partner_dashboard()
    {
        Sanctum::actingAs($this->adminUser);

        // Admin should be able to access dashboard data
        // Note: This might require additional implementation for admin access
        $response = $this->getJson('/api/v1/partner-dashboard');

        // This test might need adjustment based on actual admin access requirements
        $response->assertStatus(403); // Currently expects partner auth
    }
}
