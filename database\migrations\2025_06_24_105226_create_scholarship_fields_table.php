<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scholarship_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('scholarship_id')->constrained()->onDelete('cascade');
            $table->string('field_name', 100);
            $table->string('field_label', 200);
            $table->enum('field_type', ['text', 'textarea', 'email', 'tel', 'number', 'date', 'file', 'select']);
            $table->json('field_options')->nullable(); // For select options, validation rules, etc.
            $table->boolean('is_required')->default(false);
            $table->integer('field_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['scholarship_id', 'field_order']);
            $table->index(['scholarship_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scholarship_fields');
    }
};
