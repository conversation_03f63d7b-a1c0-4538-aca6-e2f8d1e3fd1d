<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\ScholarshipApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class PdfController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/profile/id-card/pdf",
     *     summary="Generate user ID card PDF",
     *     tags={"Profile"},
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="PDF generated successfully",
     *         @OA\MediaType(
     *             mediaType="application/pdf"
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function generateIdCard(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Get app settings
            $appName = Setting::get('app_name', config('app.name', 'Laravel NGO Foundation'));
            $appLogo = Setting::get('app_logo');
            
            // Convert images to base64 for PDF embedding
            $profileImageBase64 = null;
            $qrCodeBase64 = null;
            
            // Handle profile picture
            if ($user->profile_picture) {
                $profileImagePath = storage_path('app/public/' . $user->profile_picture);
                if (file_exists($profileImagePath)) {
                    $profileImageBase64 = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($profileImagePath));
                }
            }
            
            // Handle QR code
            if ($user->qr_code_url) {
                // Extract path from URL
                $qrPath = str_replace(Storage::disk('public')->url(''), '', $user->qr_code_url);
                $qrCodePath = storage_path('app/public/' . $qrPath);
                if (file_exists($qrCodePath)) {
                    $qrCodeBase64 = 'data:image/png;base64,' . base64_encode(file_get_contents($qrCodePath));
                }
            }
            
            // Generate fallback profile image if none exists
            if (!$profileImageBase64) {
                $profileImageBase64 = $this->generateDefaultAvatar($user->first_name, $user->last_name);
            }
            
            // Generate fallback QR code if none exists
            if (!$qrCodeBase64) {
                $qrCodeBase64 = $this->generateFallbackQrCode($user->short_id);
            }
            
            $data = [
                'user' => $user,
                'app_name' => $appName,
                'profile_image' => $profileImageBase64,
                'qr_code' => $qrCodeBase64,
                'current_year' => date('Y')
            ];
            
            // Generate PDF
            $pdf = Pdf::loadView('pdf.id-card', $data)
                      ->setPaper([0, 0, 323.15, 204.09], 'landscape') // Credit card size in points
                      ->setOptions([
                          'dpi' => 150,
                          'defaultFont' => 'sans-serif',
                          'isHtml5ParserEnabled' => true,
                          'isPhpEnabled' => true
                      ]);
            
            $filename = "{$user->first_name}_{$user->last_name}_{$appName}_ID_Card.pdf";
            
            return $pdf->download($filename);
            
        } catch (\Exception $e) {
            \Log::error('ID Card PDF generation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate ID card PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate scholarship application approval PDF
     */
    public function generateScholarshipApprovalPDF(Request $request, $applicationId)
    {
        try {
            $application = ScholarshipApplication::with(['scholarship', 'user'])
                ->where('id', $applicationId)
                ->where('status', 'approved')
                ->firstOrFail();

            // Verify user owns this application or is admin
            $user = auth()->user();
            if ($application->user_id !== $user->id && !$user->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this application'
                ], 403);
            }

            // Get app settings
            $appName = Setting::get('app_name', config('app.name', 'HALIMAKQ'));
            $appLogo = Setting::get('app_logo');

            $data = [
                'application' => $application,
                'scholarship' => $application->scholarship,
                'user' => $application->user,
                'app_name' => $appName,
                'app_logo' => $appLogo,
                'generated_date' => now()->format('F j, Y'),
                'current_year' => date('Y')
            ];

            // Generate PDF
            $pdf = Pdf::loadView('pdf.scholarship-approval', $data)
                      ->setPaper('a4', 'portrait')
                      ->setOptions([
                          'dpi' => 150,
                          'defaultFont' => 'sans-serif',
                          'isHtml5ParserEnabled' => true,
                          'isPhpEnabled' => true
                      ]);

            $filename = "Scholarship_Approval_{$application->application_id}_{$application->user->first_name}_{$application->user->last_name}.pdf";

            return $pdf->download($filename);

        } catch (\Exception $e) {
            \Log::error('Scholarship approval PDF generation failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate scholarship approval PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a default avatar with initials
     */
    private function generateDefaultAvatar($firstName, $lastName)
    {
        $initials = strtoupper(substr($firstName, 0, 1) . substr($lastName, 0, 1));
        
        // Create SVG avatar
        $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <rect width="100" height="100" fill="#10b981" rx="50"/>
    <text x="50" y="50" font-family="Arial, sans-serif" font-size="36" font-weight="bold" 
          text-anchor="middle" dominant-baseline="middle" fill="white">' . $initials . '</text>
</svg>';
        
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
    
    /**
     * Generate a fallback QR code as SVG
     */
    private function generateFallbackQrCode($shortId)
    {
        $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <rect width="100" height="100" fill="white" stroke="#e5e7eb" stroke-width="1"/>
    <text x="50" y="45" font-family="Arial, sans-serif" font-size="8" 
          text-anchor="middle" dominant-baseline="middle" fill="#6b7280">QR Code</text>
    <text x="50" y="55" font-family="Arial, sans-serif" font-size="6" font-weight="bold"
          text-anchor="middle" dominant-baseline="middle" fill="#374151">' . $shortId . '</text>
</svg>';
        
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
}
