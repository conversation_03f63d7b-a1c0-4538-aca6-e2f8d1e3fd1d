"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Quote } from "lucide-react"

interface Testimonial {
  id: number
  name: string
  role: string
  location: string
  quote: string
  image: string
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Community Member",
    location: "Lagos State",
    quote:
      "The clean water initiative has transformed our community. My children no longer fall ill from waterborne diseases, and I can focus on my small business instead of walking miles to fetch water.",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Farmer",
    location: "Kaduna State",
    quote:
      "The sustainable agriculture training changed everything for me. My crop yields have increased by 40% even during difficult weather conditions, and I've been able to send all my children to school.",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Entrepreneur",
    location: "Kano State",
    quote:
      "With the microfinance loan and business training, I've grown my tailoring business from just me to employing eight women. Now I can provide for my family and help other women do the same.",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    id: 4,
    name: "Dr. Grace Adebayo",
    role: "Healthcare Provider",
    location: "Rivers State",
    quote:
      "Working with the mobile healthcare program has been the most rewarding experience of my career. We're not just treating diseases; we're preventing them through education and early intervention.",
    image: "/placeholder.svg?height=200&width=200",
  },
]

export function TestimonialCarousel() {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [direction, setDirection] = useState<"left" | "right">("right")
  const [autoplay, setAutoplay] = useState(true)
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }))
  }

  const goToPrevious = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setDirection("left")
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1))
    setTimeout(() => setIsAnimating(false), 500)
  }, [isAnimating])

  const goToNext = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setDirection("right")
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1))
    setTimeout(() => setIsAnimating(false), 500)
  }, [isAnimating])

  useEffect(() => {
    if (!autoplay) return

    const interval = setInterval(goToNext, 6000)
    return () => clearInterval(interval)
  }, [autoplay, goToNext])

  return (
    <div className="relative">
      <div className="absolute top-1/2 -left-4 md:-left-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/80 backdrop-blur-sm hover:bg-white shadow-md hover:shadow-lg transition-all duration-300"
          onClick={goToPrevious}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronLeft className="h-5 w-5" />
          <span className="sr-only">Previous testimonial</span>
        </Button>
      </div>

      <div className="overflow-hidden rounded-2xl">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${activeIndex * 100}%)`,
          }}
        >
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="w-full flex-shrink-0">
              <Card className="border-0 shadow-none overflow-hidden bg-transparent">
                <CardContent className="p-0">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6 items-center">
                    <div className="md:col-span-2 relative">
                      <div className="relative aspect-square overflow-hidden rounded-2xl shadow-lg">
                        <div
                          className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${
                            imagesLoaded[testimonial.id] ? "opacity-0" : "opacity-100"
                          }`}
                        />
                        <Image
                          src={testimonial.image || "/placeholder.svg"}
                          alt={testimonial.name}
                          fill
                          sizes="(max-width: 768px) 100vw, 40vw"
                          className={`object-cover transition-opacity duration-300 ${
                            imagesLoaded[testimonial.id] ? "opacity-100" : "opacity-0"
                          }`}
                          onLoad={() => handleImageLoad(testimonial.id)}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-60"></div>
                        <div className="absolute bottom-4 left-4 right-4 text-white">
                          <div className="font-bold text-lg">{testimonial.name}</div>
                          <div className="text-sm opacity-90">{testimonial.role}</div>
                          <div className="text-xs opacity-80">{testimonial.location}</div>
                        </div>
                      </div>
                    </div>
                    <div className="md:col-span-3 flex flex-col justify-center">
                      <Quote className="h-10 w-10 text-green-600/30 mb-4" />
                      <p className="text-lg md:text-xl text-muted-foreground italic mb-6">{testimonial.quote}</p>
                      <div className="flex items-center gap-1">
                        {testimonials.map((_, index) => (
                          <button
                            key={index}
                            className={`h-2 rounded-full transition-all duration-300 ${
                              index === activeIndex ? "w-8 bg-green-600" : "w-2 bg-green-200 dark:bg-green-800"
                            }`}
                            onClick={() => {
                              setDirection(index < activeIndex ? "left" : "right")
                              setActiveIndex(index)
                            }}
                            aria-label={`Go to testimonial ${index + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      <div className="absolute top-1/2 -right-4 md:-right-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/80 backdrop-blur-sm hover:bg-white shadow-md hover:shadow-lg transition-all duration-300"
          onClick={goToNext}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronRight className="h-5 w-5" />
          <span className="sr-only">Next testimonial</span>
        </Button>
      </div>
    </div>
  )
}
