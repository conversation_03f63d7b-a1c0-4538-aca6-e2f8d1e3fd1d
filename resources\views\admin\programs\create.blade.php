@extends('layouts.admin')

@section('title', 'Create New Program')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Program</h1>
        <a href="{{ route('admin.programs.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Programs
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.programs.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Program Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Program Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required placeholder="Detailed description of the program, its purpose, and expected outcomes"></textarea>
                        </div>

                        <!-- Program Category and Status -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Program Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="education">Education</option>
                                        <option value="healthcare">Healthcare</option>
                                        <option value="environment">Environment</option>
                                        <option value="poverty_alleviation">Poverty Alleviation</option>
                                        <option value="youth_development">Youth Development</option>
                                        <option value="women_empowerment">Women Empowerment</option>
                                        <option value="community_development">Community Development</option>
                                        <option value="emergency_relief">Emergency Relief</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="planning" selected>Planning</option>
                                        <option value="active">Active</option>
                                        <option value="completed">Completed</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Target Beneficiaries -->
                        <div class="mb-3">
                            <label for="target_beneficiaries" class="form-label">Target Beneficiaries</label>
                            <textarea class="form-control" id="target_beneficiaries" name="target_beneficiaries" rows="3" placeholder="Who will benefit from this program? (e.g., underprivileged children, rural communities, etc.)"></textarea>
                        </div>

                        <!-- Program Objectives -->
                        <div class="mb-3">
                            <label for="objectives" class="form-label">Program Objectives</label>
                            <textarea class="form-control" id="objectives" name="objectives" rows="4" placeholder="List the main objectives and goals of this program"></textarea>
                        </div>

                        <!-- Date Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>

                        <!-- Budget Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="budget" class="form-label">Total Budget (₦)</label>
                                    <input type="number" class="form-control" id="budget" name="budget" min="0" step="0.01" placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="funding_source" class="form-label">Primary Funding Source</label>
                                    <select class="form-select" id="funding_source" name="funding_source">
                                        <option value="">Select Funding Source</option>
                                        <option value="donations">Donations</option>
                                        <option value="grants">Grants</option>
                                        <option value="government">Government</option>
                                        <option value="corporate_sponsorship">Corporate Sponsorship</option>
                                        <option value="international_aid">International Aid</option>
                                        <option value="mixed">Mixed Sources</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="mb-3">
                            <label for="location" class="form-label">Program Location</label>
                            <textarea class="form-control" id="location" name="location" rows="2" placeholder="Where will this program be implemented? (Cities, states, communities, etc.)"></textarea>
                        </div>

                        <!-- Impact Metrics -->
                        <div class="mb-3">
                            <label for="impact_metrics" class="form-label">Expected Impact Metrics</label>
                            <textarea class="form-control" id="impact_metrics" name="impact_metrics" rows="3" placeholder="How will you measure the success of this program? (e.g., number of beneficiaries, improvement metrics, etc.)"></textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Featured Image Upload -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="imagePreview" src="{{ asset('img/default-program.svg') }}" alt="Program Preview" class="img-fluid rounded" style="width: 100%; height: 200px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*" onchange="previewImage(this)">
                    </div>
                    <small class="text-muted">Recommended size: 800x400px</small>
                </div>
            </div>

            <!-- Program Guidelines -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Define clear, measurable objectives
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Identify specific target beneficiaries
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Set realistic budget and timeline
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Plan for impact measurement
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Include sustainability considerations
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Related Programs -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Categories</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-primary me-1">Education</span>
                        <span class="badge bg-success me-1">Healthcare</span>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info me-1">Environment</span>
                        <span class="badge bg-warning me-1">Youth Dev.</span>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary me-1">Community</span>
                        <span class="badge bg-danger me-1">Emergency</span>
                    </div>
                    <small class="text-muted">Choose appropriate category for better organization</small>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Foundation Stats</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-primary">25</h4>
                                <p class="text-muted mb-0 small">Active Programs</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">₦2.5M</h4>
                                <p class="text-muted mb-0 small">Total Budget</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-info">15K</h4>
                                <p class="text-muted mb-0 small">Beneficiaries</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">8</h4>
                                <p class="text-muted mb-0 small">Locations</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Auto-generate slug from title
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    
    titleInput.addEventListener('input', function() {
        // This would typically generate a slug for the program
        // For now, we'll just update the preview if needed
    });
    
    // Set minimum start date to today
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    const today = new Date().toISOString().split('T')[0];
    startDateInput.min = today;
    
    // Set end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
    });
});
</script>
@endsection 