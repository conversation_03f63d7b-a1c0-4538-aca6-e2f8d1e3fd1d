<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartnerStudent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'partner_id',
        'name',
        'class',
        'gender',
        'age',
        'photo',
        'parent_contact',
        'parent_name',
        'address',
        'application_history',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'application_history' => 'array',
    ];

    /**
     * Get the partner organization that owns this student.
     */
    public function partner()
    {
        return $this->belongsTo(User::class, 'partner_id');
    }

    /**
     * Get all scholarship applications for this student.
     */
    public function scholarshipApplications()
    {
        return $this->hasMany(ScholarshipApplication::class, 'student_id', 'id')
                    ->where('applicant_type', 'partner_student');
    }

    /**
     * Get the active scholarship application for this student.
     */
    public function activeScholarshipApplication()
    {
        return $this->scholarshipApplications()
                    ->whereIn('status', ['pending', 'under_review', 'approved'])
                    ->latest()
                    ->first();
    }

    /**
     * Check if student has an active scholarship application.
     */
    public function hasActiveApplication(): bool
    {
        return $this->activeScholarshipApplication() !== null;
    }

    /**
     * Get the student's current grade level for display.
     */
    public function getGradeDisplayAttribute(): string
    {
        return "Class {$this->class}";
    }

    /**
     * Get the next grade level for progression tracking.
     */
    public function getNextGradeAttribute(): string
    {
        $currentClass = (int) $this->class;
        return $currentClass < 12 ? (string) ($currentClass + 1) : 'Graduated';
    }

    /**
     * Scope to get students by class.
     */
    public function scopeByClass($query, $class)
    {
        return $query->where('class', $class);
    }

    /**
     * Scope to get active students.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Add a scholarship application to the student's history.
     */
    public function addApplicationToHistory($applicationData)
    {
        $history = $this->application_history ?? [];
        $history[] = array_merge($applicationData, [
            'applied_at' => now()->toDateTimeString()
        ]);
        
        $this->update(['application_history' => $history]);
    }
}
