"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/bank-transfer-receipt.tsx":
/*!**********************************************!*\
  !*** ./components/bank-transfer-receipt.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BankTransferReceipt: () => (/* binding */ BankTransferReceipt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,FileText,Loader2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ BankTransferReceipt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BankTransferReceipt(param) {\n    let { donationId, onSuccess, onError } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sender_name: '',\n        sender_bank: ''\n    });\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadSuccess, setUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadError, setUploadError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleFileSelect = (file)=>{\n        // Validate file type\n        if (!_lib_constants__WEBPACK_IMPORTED_MODULE_8__.FILE_UPLOAD.ALLOWED_TYPES.includes(file.type)) {\n            setUploadError('Please select a valid file (JPG, PNG, or PDF)');\n            return;\n        }\n        // Validate file size\n        if (file.size > _lib_constants__WEBPACK_IMPORTED_MODULE_8__.FILE_UPLOAD.MAX_SIZE) {\n            setUploadError(\"File size must be less than \".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_8__.FILE_UPLOAD.MAX_SIZE / (1024 * 1024), \"MB\"));\n            return;\n        }\n        setSelectedFile(file);\n        setUploadError(null);\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (e.type === \"dragenter\" || e.type === \"dragover\") {\n            setDragActive(true);\n        } else if (e.type === \"dragleave\") {\n            setDragActive(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        var _e_dataTransfer_files;\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        const file = (_e_dataTransfer_files = e.dataTransfer.files) === null || _e_dataTransfer_files === void 0 ? void 0 : _e_dataTransfer_files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedFile) {\n            setUploadError('Please select a receipt file');\n            return;\n        }\n        if (!formData.sender_name || !formData.sender_bank) {\n            setUploadError('Please fill in all required fields');\n            return;\n        }\n        setIsUploading(true);\n        setUploadError(null);\n        try {\n            const uploadFormData = new FormData();\n            uploadFormData.append('receipt', selectedFile);\n            uploadFormData.append('sender_name', formData.sender_name);\n            uploadFormData.append('sender_bank', formData.sender_bank);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.uploadDonationReceipt(donationId, uploadFormData);\n            if (response.success) {\n                setUploadSuccess(true);\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                setUploadError(response.message || 'Failed to upload receipt');\n                onError === null || onError === void 0 ? void 0 : onError(response.message || 'Failed to upload receipt');\n            }\n        } catch (error) {\n            console.error('Receipt upload error:', error);\n            const errorMessage = 'Failed to upload receipt. Please try again.';\n            setUploadError(errorMessage);\n            onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    if (uploadSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"border-green-200 bg-green-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-12 w-12 text-green-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-green-700 mb-2\",\n                        children: \"Receipt Uploaded Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-600 text-sm mb-4\",\n                        children: \"Your receipt has been uploaded and is now pending admin verification. You will receive an email confirmation once your donation is approved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Back to Home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            \"Upload Bank Transfer Receipt\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Please upload your bank transfer receipt and provide the required details for verification.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        uploadError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-red-600\",\n                                    children: uploadError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"sender-name\",\n                                            children: [\n                                                \"Sender Name \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 56\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"sender-name\",\n                                            placeholder: \"Name on the bank account\",\n                                            value: formData.sender_name,\n                                            onChange: (e)=>handleInputChange('sender_name', e.target.value),\n                                            className: \"mt-1\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"sender-bank\",\n                                            children: [\n                                                \"Sender Bank \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 56\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"sender-bank\",\n                                            placeholder: \"Bank used for transfer\",\n                                            value: formData.sender_bank,\n                                            onChange: (e)=>handleInputChange('sender_bank', e.target.value),\n                                            className: \"mt-1\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    children: [\n                                        \"Receipt File \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 border-2 border-dashed rounded-lg p-6 text-center transition-colors \".concat(dragActive ? 'border-green-400 bg-green-50' : selectedFile ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'),\n                                    onDragEnter: handleDrag,\n                                    onDragLeave: handleDrag,\n                                    onDragOver: handleDrag,\n                                    onDrop: handleDrop,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \".jpg,.jpeg,.png,.pdf\",\n                                            onChange: handleFileChange,\n                                            className: \"hidden\",\n                                            id: \"receipt-upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-8 w-8 text-green-500 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-700\",\n                                                    children: selectedFile.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600\",\n                                                    children: [\n                                                        (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                        \" MB\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('receipt-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                    },\n                                                    children: \"Change File\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8 text-gray-400 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"receipt-upload\",\n                                                                    className: \"cursor-pointer text-green-600 hover:text-green-500\",\n                                                                    children: \"Click to upload\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                ' ',\n                                                                \"or drag and drop\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"JPG, PNG or PDF (max 5MB)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"submit\",\n                            disabled: isUploading || !selectedFile || !formData.sender_name || !formData.sender_bank,\n                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                            children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Uploading Receipt...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_FileText_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Upload Receipt\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground text-center\",\n                            children: \"Your receipt will be reviewed by our admin team. You will receive an email confirmation once verified.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\bank-transfer-receipt.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(BankTransferReceipt, \"RHGvAwhjm4rZ5St4aQOduj0IKQM=\");\n_c = BankTransferReceipt;\nvar _c;\n$RefreshReg$(_c, \"BankTransferReceipt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/bank-transfer-receipt.tsx\n"));

/***/ })

});