exports.id=702,exports.ids=[702],exports.modules={2586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687);r(43210);var a=r(24934),n=r(43649),i=r(67141),o=r(32192),l=r(85814),d=r.n(l);function c({error:e,reset:t}){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(n.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Something went wrong!"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-8",children:"We're sorry, but something unexpected happened. Please try again or go back to the home page."}),e.message&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400 font-mono break-all",children:e.message})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(a.$,{onClick:t,className:"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),"Try Again"]}),(0,s.jsx)(d(),{href:"/",children:(0,s.jsxs)(a.$,{variant:"outline",className:"border-green-600 text-green-600 hover:bg-green-600 hover:text-white flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"Go Home"]})})]})]})})}},13368:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},21376:(e,t,r)=>{Promise.resolve().then(r.bind(r,58392)),Promise.resolve().then(r.bind(r,21979)),Promise.resolve().then(r.bind(r,83066))},21979:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\navigation.tsx","Navigation")},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687),a=r(43210),n=r(8730),i=r(24224),o=r(96241);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,suppressHydrationWarning:!0,...i})});d.displayName="Button"},27265:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},37964:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>z});var s=r(60687),a=r(43210),n=r(16189),i=r(85814),o=r.n(i),l=r(24934),d=r(26134),c=r(24224),h=r(11860),u=r(96241);let m=d.bL,g=d.l9;d.bm;let p=d.ZL,x=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(d.hJ,{className:(0,u.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));x.displayName=d.hJ.displayName;let f=(0,c.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),b=a.forwardRef(({side:e="right",className:t,children:r,...a},n)=>(0,s.jsxs)(p,{children:[(0,s.jsx)(x,{}),(0,s.jsxs)(d.UC,{ref:n,className:(0,u.cn)(f({side:e}),t),...a,children:[r,(0,s.jsxs)(d.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));b.displayName=d.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(d.hE,{ref:r,className:(0,u.cn)("text-lg font-semibold text-foreground",e),...t})).displayName=d.hE.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(d.VY,{ref:r,className:(0,u.cn)("text-sm text-muted-foreground",e),...t})).displayName=d.VY.displayName;var v=r(26312),y=r(14952),j=r(13964),N=r(65822);let w=v.bL,k=v.l9;v.YJ,v.ZL,v.Pb,v.z6,a.forwardRef(({className:e,inset:t,children:r,...a},n)=>(0,s.jsxs)(v.ZP,{ref:n,className:(0,u.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...a,children:[r,(0,s.jsx)(y.A,{className:"ml-auto"})]})).displayName=v.ZP.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(v.G5,{ref:r,className:(0,u.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=v.G5.displayName;let S=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>(0,s.jsx)(v.ZL,{children:(0,s.jsx)(v.UC,{ref:a,sideOffset:t,className:(0,u.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));S.displayName=v.UC.displayName;let P=a.forwardRef(({className:e,inset:t,...r},a)=>(0,s.jsx)(v.q7,{ref:a,className:(0,u.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r}));P.displayName=v.q7.displayName,a.forwardRef(({className:e,children:t,checked:r,...a},n)=>(0,s.jsxs)(v.H_,{ref:n,className:(0,u.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(v.VF,{children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})}),t]})).displayName=v.H_.displayName,a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(v.hN,{ref:a,className:(0,u.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(v.VF,{children:(0,s.jsx)(N.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=v.hN.displayName,a.forwardRef(({className:e,inset:t,...r},a)=>(0,s.jsx)(v.JU,{ref:a,className:(0,u.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r})).displayName=v.JU.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(v.wv,{ref:r,className:(0,u.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=v.wv.displayName;var $=r(67760),A=r(41312),q=r(28947),_=r(27351),O=r(82080),T=r(58887),C=r(78272),E=r(12941),L=r(25541),U=r(40093);function z(){let[e,t]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!1),[c,u]=(0,a.useState)(!1),[p,x]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),y=(0,n.usePathname)(),{settings:j,loading:N}=(0,U.t)(),z=(0,a.useRef)(null),R=e=>{z.current&&clearTimeout(z.current),x(e)},J=()=>{z.current=setTimeout(()=>{x(null)},150)},F=[{href:"/about",label:"About Us",icon:$.A,description:"Learn about our mission"},{href:"/team",label:"Our Team",icon:A.A,description:"Meet our dedicated team"}],H=[{href:"/projects",label:"Projects",icon:q.A,description:"Our community projects"},{href:"/scholarships",label:"Scholarships",icon:_.A,description:"Educational support"},{href:"/learn-with-us",label:"Learn With Us",icon:O.A,description:"Educational programs"}],I=[{href:"/contact",label:"Contact",icon:T.A,description:"Get in touch"},{href:"/blog",label:"Blog",icon:O.A,description:"Latest updates"}],Q=e=>e.some(e=>y===e.href),D=j?.app_name||"Laravel NGO",W=j?.app_logo;return console.log("Navigation - Settings:",j),console.log("Navigation - App Logo:",W),(0,s.jsx)("header",{className:`fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ${c?"bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5":"bg-white/95 backdrop-blur-md border-b border-gray-200/30"} dark:bg-gray-950/95 dark:supports-[backdrop-filter]:bg-gray-950/80`,children:(0,s.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(o(),{href:"/",className:"group flex items-center gap-3 hover:scale-105 transition-all duration-300 ease-out",children:[W?(0,s.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,s.jsx)("img",{src:W,alt:`${D} Logo`,className:"h-9 w-9 object-cover transition-transform duration-300 group-hover:scale-110"})}):(0,s.jsxs)("div",{className:"relative h-9 w-9 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg group-hover:shadow-xl transition-all duration-300",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"}),(0,s.jsx)($.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110"})]}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white transition-colors duration-300",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:D})})]}),(0,s.jsxs)("nav",{className:"hidden lg:flex items-center space-x-1",children:[(0,s.jsxs)(o(),{href:"/",className:`group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${"/"===y?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"}`,children:[(0,s.jsx)("span",{className:"relative z-10",children:"Home"}),"/"===y&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,s.jsx)("div",{className:"relative",onMouseEnter:()=>R("about"),onMouseLeave:J,children:(0,s.jsxs)(w,{open:"about"===p,onOpenChange:e=>x(e?"about":null),children:[(0,s.jsx)(k,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",className:`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${Q(F)||"about"===p?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"}`,children:[(0,s.jsx)("span",{className:"relative z-10",children:"About"}),(0,s.jsx)(C.A,{className:`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${"about"===p?"rotate-180":""}`}),(Q(F)||"about"===p)&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,s.jsx)(S,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:F.map(e=>{let t=e.icon;return(0,s.jsx)(P,{asChild:!0,className:"rounded-xl p-0",children:(0,s.jsxs)(o(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,s.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,s.jsx)("div",{className:"relative",onMouseEnter:()=>R("programs"),onMouseLeave:J,children:(0,s.jsxs)(w,{open:"programs"===p,onOpenChange:e=>x(e?"programs":null),children:[(0,s.jsx)(k,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",className:`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${Q(H)||"programs"===p?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"}`,children:[(0,s.jsx)("span",{className:"relative z-10",children:"Programs"}),(0,s.jsx)(C.A,{className:`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${"programs"===p?"rotate-180":""}`}),(Q(H)||"programs"===p)&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,s.jsx)(S,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:H.map(e=>{let t=e.icon;return(0,s.jsx)(P,{asChild:!0,className:"rounded-xl p-0",children:(0,s.jsxs)(o(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,s.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,s.jsxs)(o(),{href:"/impact",className:`group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${"/impact"===y?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"}`,children:[(0,s.jsx)("span",{className:"relative z-10",children:"Impact"}),"/impact"===y&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,s.jsx)("div",{className:"relative",onMouseEnter:()=>R("involved"),onMouseLeave:J,children:(0,s.jsxs)(w,{open:"involved"===p,onOpenChange:e=>x(e?"involved":null),children:[(0,s.jsx)(k,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",className:`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${Q(I)||"involved"===p?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"}`,children:[(0,s.jsx)("span",{className:"relative z-10",children:"Get Involved"}),(0,s.jsx)(C.A,{className:`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${"involved"===p?"rotate-180":""}`}),(Q(I)||"involved"===p)&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,s.jsx)(S,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:I.map(e=>{let t=e.icon;return(0,s.jsx)(P,{asChild:!0,className:"rounded-xl p-0",children:(0,s.jsxs)(o(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,s.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})})]}),f&&(0,s.jsx)("div",{className:"hidden lg:flex items-center gap-3",suppressHydrationWarning:!0,children:r?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o(),{href:"/donate",children:(0,s.jsxs)(l.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,s.jsx)("span",{className:"relative z-10 font-medium",children:"Donate Now"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o(),{href:"/auth/login",children:(0,s.jsxs)(l.$,{variant:"outline",className:"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20",suppressHydrationWarning:!0,children:[(0,s.jsx)("span",{className:"relative z-10 font-medium",children:"Login"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300"})]})}),(0,s.jsx)(o(),{href:"/auth/register",children:(0,s.jsxs)(l.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,s.jsx)("span",{className:"relative z-10 font-medium",children:"Register"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})]})}),(0,s.jsxs)(m,{open:e,onOpenChange:t,children:[(0,s.jsx)(g,{asChild:!0,className:"lg:hidden",children:(0,s.jsxs)(l.$,{variant:"ghost",size:"icon",className:"group relative overflow-hidden transition-all duration-300 hover:scale-110 focus:scale-110 rounded-xl hover:bg-green-50",children:[(0,s.jsx)("div",{className:`absolute inset-0 transition-all duration-300 ${e?"rotate-180 scale-75":""}`,children:e?(0,s.jsx)(h.A,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"}):(0,s.jsx)(E.A,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"})}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,s.jsxs)(b,{side:"right",className:"w-[320px] sm:w-[400px] z-[100] p-0 bg-white/95 backdrop-blur-xl border-l border-gray-200/50",children:[(0,s.jsx)(d.hE,{className:"sr-only",children:"Mobile Navigation Menu"}),(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200/50",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[W?(0,s.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,s.jsx)("img",{src:W,alt:`${D} Logo`,className:"h-8 w-auto object-contain"})}):(0,s.jsx)("div",{className:"h-8 w-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg",children:(0,s.jsx)($.A,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-900",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:D})})]})}),(0,s.jsxs)("div",{className:"flex-1 p-6 space-y-6 overflow-y-auto",children:[(0,s.jsx)(o(),{href:"/",className:`block text-lg font-medium transition-all duration-200 p-3 rounded-xl ${"/"===y?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"}`,onClick:()=>t(!1),children:"Home"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"About"}),F.map(e=>{let r=e.icon;return(0,s.jsxs)(o(),{href:e.href,className:`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${y===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"}`,onClick:()=>t(!1),children:[(0,s.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Programs"}),H.map(e=>{let r=e.icon;return(0,s.jsxs)(o(),{href:e.href,className:`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${y===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"}`,onClick:()=>t(!1),children:[(0,s.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,s.jsxs)(o(),{href:"/impact",className:`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${"/impact"===y?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"}`,onClick:()=>t(!1),children:[(0,s.jsx)(L.A,{className:"h-5 w-5"}),"Impact"]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Get Involved"}),I.map(e=>{let r=e.icon;return(0,s.jsxs)(o(),{href:e.href,className:`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${y===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"}`,onClick:()=>t(!1),children:[(0,s.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),!r&&(0,s.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,s.jsx)(o(),{href:"/auth/login",onClick:()=>t(!1),children:(0,s.jsx)(l.$,{variant:"outline",className:"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3",children:"Login"})}),(0,s.jsx)(o(),{href:"/auth/register",onClick:()=>t(!1),children:(0,s.jsx)(l.$,{className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg",children:"Register"})})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200/50",children:(0,s.jsx)(o(),{href:"/donate",onClick:()=>t(!1),children:(0,s.jsx)(l.$,{className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg",children:"Donate Now"})})})]})]})]})]})})}},40093:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var s=r(43210),a=r(59556);let n=()=>{let[e,t]=(0,s.useState)(null),[r,n]=(0,s.useState)(!0),[i,o]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{try{let e=await a.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),t(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),o(e instanceof Error?e.message:"Unknown error"),t({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{n(!1)}})()},[]),{settings:e,loading:r,error:i}}},41778:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>y});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(19526),l=r(72575),d=r(66232),c=r(98876),h=r(84113),u=r(67760),m=r(33872),g=r(97992),p=r(48340),x=r(41550),f=r(24934),b=r(68988),v=r(40093);function y(){let[e,t]=(0,a.useState)(!1),{settings:r,loading:n}=(0,v.t)(),y=r?.app_name||"Laravel NGO",j=r?.app_logo,N=r?.contact_email||"<EMAIL>",w=r?.contact_phone||"+234 ************",k=r?.site_description||"Empowering communities through education, development programs, and sustainable initiatives.",S=r?.organization_address,P=r?.organization_city,$=r?.organization_state,A=[S,P,$,r?.organization_country,r?.organization_postal_code].filter(Boolean).join(", "),q=[{key:"social_facebook",icon:o.A,label:"Facebook"},{key:"social_twitter",icon:l.A,label:"Twitter"},{key:"social_instagram",icon:d.A,label:"Instagram"},{key:"social_linkedin",icon:c.A,label:"LinkedIn"},{key:"social_youtube",icon:h.A,label:"YouTube"}];return(0,s.jsx)("footer",{className:"bg-green-950 dark:bg-black text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(i(),{href:"/",className:"flex items-center gap-2",children:[j?(0,s.jsx)("img",{src:j,alt:`${y} Logo`,className:"h-8 w-auto object-contain"}):(0,s.jsx)("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-green-600",children:(0,s.jsx)(u.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold",children:(0,s.jsx)("span",{className:"text-amber-400 dark:text-amber-400",children:y})})]}),(0,s.jsx)("p",{className:"text-green-200 text-sm",children:k}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[q.map(({key:e,icon:t,label:a})=>{let n=r?.[e];return n?(0,s.jsx)(i(),{href:n,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":a,children:(0,s.jsx)(t,{className:"h-4 w-4"})})},e):null}),r?.social_whatsapp&&(0,s.jsx)(i(),{href:`https://wa.me/${r.social_whatsapp.replace(/[^0-9]/g,"")}`,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"WhatsApp",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})}),r?.social_telegram&&(0,s.jsx)(i(),{href:r.social_telegram,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"Telegram",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"})})})}),r?.social_tiktok&&(0,s.jsx)(i(),{href:r.social_tiktok,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"TikTok",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"})})})})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/about",className:"text-green-200 hover:text-amber-400 transition-colors",children:"About Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/projects",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Projects"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/scholarships",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Scholarships"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/learn-with-us",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Learn With Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/impact",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Impact"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/team",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Team"})})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Info"}),(0,s.jsxs)("div",{className:"space-y-3",children:[A&&(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-green-200 text-sm",children:A})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-green-400"}),(0,s.jsx)("a",{href:`tel:${w}`,className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:w})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-green-400"}),(0,s.jsx)("a",{href:`mailto:${N}`,className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:N})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-green-200 text-sm",children:"Subscribe to our newsletter for updates on our programs and impact."}),e&&(0,s.jsxs)("div",{className:"flex gap-2",suppressHydrationWarning:!0,children:[(0,s.jsx)(b.p,{type:"email",placeholder:"Your email",className:"bg-green-900 border-green-700 text-white placeholder:text-green-300 focus:ring-amber-500 focus:border-amber-500 rounded-md",suppressHydrationWarning:!0}),(0,s.jsx)(f.$,{className:"bg-amber-500 hover:bg-amber-600 text-green-950 dark:text-green-950",suppressHydrationWarning:!0,children:"Subscribe"})]}),(0,s.jsxs)("div",{className:"pt-4 space-y-1",children:[(0,s.jsx)(i(),{href:"/privacy",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Privacy Policy"}),(0,s.jsx)(i(),{href:"/terms",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Terms of Service"})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-green-800 mt-8 pt-8 text-center",children:[(0,s.jsxs)("p",{className:"text-green-200 text-sm",children:["\xa9 ",new Date().getFullYear()," ",y,". All rights reserved."]}),(0,s.jsx)("p",{className:"text-green-300 text-xs mt-2",children:"Built with ❤️ to empower communities and create lasting impact."})]})]})})}},49107:(e,t,r)=>{Promise.resolve().then(r.bind(r,52608))},50752:(e,t,r)=>{Promise.resolve().then(r.bind(r,41778)),Promise.resolve().then(r.bind(r,37964)),Promise.resolve().then(r.bind(r,92892))},52608:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx","default")},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>d,viewport:()=>c});var s=r(37413),a=r(13346),n=r.n(a);r(82704);var i=r(83066),o=r(21979),l=r(58392);let d={title:"NGO Foundation | Empowering Communities",description:"We provide scholarships, school supplies, and community development programs to support students, underprivileged individuals, and those in need.",other:{"Content-Security-Policy":"script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none';"},generator:"v0.dev"},c={width:"device-width",initialScale:1,maximumScale:5};function h({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""})]}),(0,s.jsx)("body",{className:`${n().className} flex flex-col min-h-screen`,suppressHydrationWarning:!0,children:(0,s.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!1,children:[(0,s.jsx)(o.Navigation,{}),(0,s.jsx)("main",{className:"flex-grow",children:e}),(0,s.jsx)(l.Footer,{})]})})]})}},58392:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\footer.tsx","Footer")},59556:(e,t,r)=>{"use strict";r.d(t,{uE:()=>a});class s{constructor(e="https://halimakqfoundation.fnskills.ng/api/v1"){this.baseURL=e}async request(e,t={}){let r=this.baseURL.includes("/api/v1")?`${this.baseURL}${e}`:`${this.baseURL}/api/v1${e}`,s={headers:{"Content-Type":"application/json",Accept:"application/json",...t.headers},...t};try{console.log(`API Request: ${t.method||"GET"} ${r}`);let a=await fetch(r,s),n=await a.json();return console.log(`API Response for ${e}:`,n),n}catch(e){throw console.error("API request failed:",e),e}}async get(e,t={}){return this.request(e,{...t,method:"GET"})}async post(e,t,r={}){let s={...r,method:"POST"};return t&&(s.body=JSON.stringify(t)),this.request(e,s)}async put(e,t,r={}){let s={...r,method:"PUT"};return t&&(s.body=JSON.stringify(t)),this.request(e,s)}async patch(e,t,r={}){let s={...r,method:"PATCH"};return t&&(s.body=JSON.stringify(t)),this.request(e,s)}async delete(e,t={}){return this.request(e,{...t,method:"DELETE"})}async register(e){let t=await this.request("/register",{method:"POST",body:JSON.stringify({first_name:e.first_name,last_name:e.last_name,email:e.email,password:e.password,password_confirmation:e.password_confirmation,phone_number:e.phone_number,address:e.address,date_of_birth:e.date_of_birth,city:e.city,state:e.state,country:e.country})});if(t.success&&t.data?.token,t.success&&e.user_type&&e.additional_data)try{if("volunteer"===e.user_type)await this.applyAsVolunteer(e.additional_data);else if("student"===e.user_type||"partner"===e.user_type){let t={user_type:e.user_type,profile_data:e.additional_data,profile_completed:!0};await this.updateUserPreferences(t)}}catch(e){return console.error("Additional data submission failed:",e),{...t,message:t.message+" However, additional profile information could not be saved. You can complete your profile later."}}return t}async getProfile(){return this.request("/profile")}async updateProfile(e){return this.request("/profile",{method:"PUT",body:JSON.stringify(e)})}async updateUserPreferences(e){return this.request("/profile",{method:"PUT",body:JSON.stringify({preferences:e})})}async uploadAvatar(e){let t=new FormData;t.append("avatar",e);let r=this.baseURL.includes("/api/v1")?`${this.baseURL}/profile/avatar`:`${this.baseURL}/api/v1/profile/avatar`;try{let e=await fetch(r,{method:"POST",headers:{Accept:"application/json"},body:t});return await e.json()}catch(e){throw console.error("Avatar upload failed:",e),e}}async changePassword(e){return this.request("/profile/password",{method:"PUT",body:JSON.stringify(e)})}async generateQrCode(){return this.request("/profile/generate-qr",{method:"POST"})}async getIdCard(){return this.request("/profile/id-card")}async getDashboardSummary(){return this.request("/dashboard/summary")}async getVolunteerApplication(){return this.request("/volunteer/application")}async getVolunteerHours(){return this.request("/volunteer/hours")}async getVolunteerOpportunities(){return this.request("/volunteer/opportunities")}async logVolunteerHours(e){return this.request("/volunteer/hours",{method:"POST",body:JSON.stringify(e)})}async getMyScholarshipApplications(){return this.request("/scholarships/my-applications")}async getScholarships(){return this.request("/scholarships")}async applyForScholarship(e,t){return this.request(`/scholarships/${e}/apply`,{method:"POST",body:JSON.stringify(t)})}async getMyEventRegistrations(){return this.request("/events/my-registrations")}async getUpcomingEvents(){return this.request("/events/upcoming")}buildQueryString(e){let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&(Array.isArray(r)?r.forEach(r=>t.append(`${e}[]`,r)):t.append(e,r.toString()))}),t.toString()}async getAdminDashboard(){return this.request("/admin/dashboard")}async getAdminAnalytics(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/dashboard/analytics${t}`)}async getAdminStats(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/dashboard/stats${t}`)}async getAdminUsers(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/users${t}`)}async createAdminUser(e){return this.request("/admin/users",{method:"POST",body:JSON.stringify(e)})}async updateAdminUser(e,t){return this.request(`/admin/users/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteAdminUser(e){return this.request(`/admin/users/${e}`,{method:"DELETE"})}async bulkActionUsers(e){return this.request("/admin/users/bulk-action",{method:"POST",body:JSON.stringify(e)})}async exportUsers(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/users/export${t}`)}async getScholarshipApplications(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/scholarship-applications${t}`)}async reviewScholarshipApplication(e,t){return this.request(`/admin/scholarship-applications/${e}/review`,{method:"PUT",body:JSON.stringify(t)})}async bulkActionScholarshipApplications(e){return this.request("/admin/scholarship-applications/bulk-action",{method:"POST",body:JSON.stringify(e)})}async exportScholarshipApplications(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/scholarship-applications/export${t}`)}async getScholarshipStatistics(){return this.request("/admin/scholarships/statistics")}async getAdminEvents(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/events${t}`)}async createAdminEvent(e){return this.request("/admin/events",{method:"POST",body:JSON.stringify(e)})}async updateAdminEvent(e,t){return this.request(`/admin/events/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteAdminEvent(e){return this.request(`/admin/events/${e}`,{method:"DELETE"})}async bulkActionEvents(e){return this.request("/admin/events/bulk-action",{method:"POST",body:JSON.stringify(e)})}async exportEvents(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/events/export${t}`)}async getEventStatistics(){return this.request("/admin/events/statistics")}async getAdminPrograms(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/programs${t}`)}async createAdminProgram(e){return this.request("/admin/programs",{method:"POST",body:JSON.stringify(e)})}async updateAdminProgram(e,t){return this.request(`/admin/programs/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteAdminProgram(e){return this.request(`/admin/programs/${e}`,{method:"DELETE"})}async getAdminBlogPosts(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/blog/posts${t}`)}async createAdminBlogPost(e){return this.request("/admin/blog/posts",{method:"POST",body:JSON.stringify(e)})}async updateAdminBlogPost(e,t){return this.request(`/admin/blog/posts/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteAdminBlogPost(e){return this.request(`/admin/blog/posts/${e}`,{method:"DELETE"})}async exportBlogPosts(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/admin/blog/posts/export${t}`)}async getMyDonations(){return this.request("/donations/my-donations")}async getDonationCampaigns(){return this.request("/donations/campaigns")}async getBlogPosts(e=1){return this.request(`/blog/posts?page=${e}`)}async getBlogPost(e){return this.request(`/blog/posts/${e}`)}async getEvents(e=1){return this.request(`/events?page=${e}`)}async getEvent(e){return this.request(`/events/${e}`)}async registerForEvent(e,t){return this.request(`/events/${e}/register`,{method:"POST",body:JSON.stringify({additional_info:t})})}async getPrograms(e=1){return this.request(`/programs?page=${e}`)}async getProgram(e){return this.request(`/programs/${e}`)}async getScholarship(e){return this.request(`/scholarships/${e}`)}async getPublicScholarships(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/public-scholarships${t}`)}async getPublicScholarship(e){return this.request(`/public-scholarships/${e}`)}async getProjectScholarshipStatistics(){return this.request("/project-scholarships/statistics")}async getProjectScholarshipPortfolio(){return this.request("/project-scholarships/portfolio")}async getScholarshipsByStatus(e="active"){return this.request(`/project-scholarships/by-status?status=${e}`)}async getImpactStatistics(){return this.request("/impact/statistics")}async getScholarshipImpactStatistics(){return this.request("/impact/scholarship-statistics")}async submitScholarshipApplication(e,t){let r=this.baseURL.includes("/api/v1")?`${this.baseURL}/apply-scholarship/${e}`:`${this.baseURL}/api/v1/apply-scholarship/${e}`;try{let e=await fetch(r,{method:"POST",headers:{Accept:"application/json"},body:t}),s=await e.json();if(console.log("API Response for scholarship application:",s),!e.ok)throw Error(s.message||`HTTP error! status: ${e.status}`);return s}catch(e){throw console.error("Error submitting scholarship application:",e),e}}async applyAsVolunteer(e){return this.request("/volunteer/apply",{method:"POST",body:JSON.stringify(e)})}async submitContactForm(e){return this.request("/contact",{method:"POST",body:JSON.stringify(e)})}async subscribeToNewsletter(e){return this.request("/newsletter/subscribe",{method:"POST",body:JSON.stringify({email:e})})}async getSettings(){return this.request("/settings")}async getPartnerDashboard(){return this.request("/partner-dashboard")}async getPartnerAvailableScholarships(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/partner-scholarships/available${t}`)}async getPartnerScholarshipDetails(e){return this.request(`/partner-scholarships/${e}/details`)}async applyForStudentScholarship(e,t){return this.request(`/partner-scholarships/${e}/apply`,{method:"POST",body:t})}async getPartnerApplications(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/partner-scholarships/my-applications${t}`)}async getPartnerStudents(e){let t=e?`?${this.buildQueryString(e)}`:"";return this.request(`/partner-students${t}`)}async addPartnerStudent(e){return this.request("/partner-students",{method:"POST",body:e})}}let a=new s},63713:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687),a=r(43210),n=r(96241);let i=a.forwardRef(({className:e,type:t,...r},a)=>(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,suppressHydrationWarning:!0,...r}));i.displayName="Input"},78335:()=>{},78520:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},82366:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(4536),n=r.n(a);function i(){return(0,s.jsxs)("div",{style:{textAlign:"center",marginTop:"50px"},children:[(0,s.jsx)("h1",{children:"404 - Page Not Found"}),(0,s.jsx)("p",{children:"Sorry, the page you are looking for does not exist."}),(0,s.jsx)(n(),{href:"/",children:"Go back home"})]})}},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\theme-provider.tsx","ThemeProvider")},86059:(e,t,r)=>{Promise.resolve().then(r.bind(r,2586))},92892:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(60687),a=r(10218);function n({children:e,...t}){return(0,s.jsx)(a.N,{...t,children:e})}},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}},96487:()=>{},99766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413);function a(){return(0,s.jsx)("div",{children:(0,s.jsx)("p",{children:"Loading..."})})}}};