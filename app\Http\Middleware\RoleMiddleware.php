<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $roles): Response
    {
        if (!auth()->check()) {
            return response()->json([
                'message' => 'Unauthenticated',
                'success' => false
            ], 401);
        }

        $user = auth()->user();
        $allowedRoles = explode('|', $roles);

        // Check if user has any of the required roles
        $hasRequiredRole = false;
        foreach ($allowedRoles as $role) {
            if ($user->hasRole($role) || $user->role === $role) {
                $hasRequiredRole = true;
                break;
            }
        }

        if (!$hasRequiredRole) {
            return response()->json([
                'message' => 'Access denied. Required role: ' . $roles,
                'success' => false
            ], 403);
        }

        return $next($request);
    }
}
