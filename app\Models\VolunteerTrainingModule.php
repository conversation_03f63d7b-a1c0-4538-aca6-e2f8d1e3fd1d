<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteerTrainingModule extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category',
        'content',
        'video_url',
        'duration_minutes',
        'order',
        'is_required',
        'is_active',
        'prerequisites',
        'learning_objectives',
        'resources'
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'prerequisites' => 'array',
        'learning_objectives' => 'array',
        'resources' => 'array'
    ];

    /**
     * Get training progress for this module
     */
    public function progress()
    {
        return $this->hasMany(VolunteerTrainingProgress::class, 'training_module_id');
    }

    /**
     * Scope for active modules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for required modules
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for specific category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get category badge color
     */
    public function getCategoryColorAttribute()
    {
        $colors = [
            'orientation' => 'blue',
            'safety' => 'red',
            'skills' => 'green',
            'leadership' => 'purple',
            'communication' => 'orange'
        ];

        return $colors[$this->category] ?? 'gray';
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration_minutes) {
            return 'Self-paced';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }

        return $minutes . ' minutes';
    }

    /**
     * Check if module has prerequisites
     */
    public function hasPrerequisites()
    {
        return !empty($this->prerequisites);
    }

    /**
     * Check if user meets prerequisites
     */
    public function meetsPrerequisites($volunteerId)
    {
        if (!$this->hasPrerequisites()) {
            return true;
        }

        foreach ($this->prerequisites as $prerequisiteId) {
            $completed = VolunteerTrainingProgress::where('volunteer_id', $volunteerId)
                ->where('training_module_id', $prerequisiteId)
                ->where('is_completed', true)
                ->exists();

            if (!$completed) {
                return false;
            }
        }

        return true;
    }
}
