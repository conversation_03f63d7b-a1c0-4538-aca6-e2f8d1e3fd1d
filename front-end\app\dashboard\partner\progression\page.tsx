'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  TrendingUp, 
  Users, 
  GraduationCap, 
  Calendar,
  Search,
  Filter,
  MoreHorizontal,
  ArrowUp,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Download,
  Eye,
  Edit
} from 'lucide-react'
import { toast } from 'react-hot-toast'

// TypeScript interfaces
interface StudentProgression {
  id: number
  student: {
    id: number
    student_id: string
    full_name: string
    age: number
    gender: string
  }
  from_grade: string
  to_grade: string
  academic_year: string
  status: 'pending' | 'approved' | 'completed' | 'failed'
  progression_date: string
  notes?: string
  created_at: string
  updated_at: string
}

interface ProgressionStats {
  total_progressions: number
  pending_progressions: number
  approved_progressions: number
  completed_progressions: number
  current_academic_year: string
}

interface EligibleStudent {
  id: number
  student_id: string
  full_name: string
  current_grade: string
  next_grade: string
  academic_year: string
  eligible_for_progression: boolean
  scholarship_eligible: boolean
}

// Helper function for status colors
const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'approved':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'failed':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

// Helper function for grade level display
const formatGradeLevel = (grade: string) => {
  return grade.replace('_', ' ').toUpperCase()
}

export default function AcademicProgressionTracking() {
  const { user, token } = useAuth()
  const [progressions, setProgressions] = useState<StudentProgression[]>([])
  const [stats, setStats] = useState<ProgressionStats | null>(null)
  const [eligibleStudents, setEligibleStudents] = useState<EligibleStudent[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [gradeFilter, setGradeFilter] = useState('all')
  const [academicYearFilter, setAcademicYearFilter] = useState('2024/2025')
  const [selectedProgressions, setSelectedProgressions] = useState<number[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // API base URL
  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

  // Fetch progression statistics
  const fetchStatistics = async () => {
    try {
      const response = await fetch(`${API_BASE}/partner-progression/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch statistics')
      }

      const data = await response.json()
      if (data.success) {
        setStats(data.data)
      }
    } catch (error) {
      console.error('Error fetching statistics:', error)
      toast.error('Failed to load statistics')
    }
  }

  // Fetch progressions
  const fetchProgressions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '15',
        search: searchTerm,
        status: statusFilter,
        grade: gradeFilter,
        academic_year: academicYearFilter,
      })

      const response = await fetch(`${API_BASE}/partner-progression?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch progressions')
      }

      const data = await response.json()
      if (data.success) {
        setProgressions(data.data.data || [])
        setTotalPages(data.data.last_page || 1)
      }
    } catch (error) {
      console.error('Error fetching progressions:', error)
      toast.error('Failed to load progressions')
    } finally {
      setLoading(false)
    }
  }

  // Fetch eligible students
  const fetchEligibleStudents = async () => {
    try {
      const response = await fetch(`${API_BASE}/partner-progression/eligible-students`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch eligible students')
      }

      const data = await response.json()
      if (data.success) {
        setEligibleStudents(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching eligible students:', error)
      toast.error('Failed to load eligible students')
    }
  }

  // Handle progression approval
  const handleProgressionAction = async (progressionId: number, action: 'approve' | 'complete' | 'reject') => {
    try {
      const response = await fetch(`${API_BASE}/partner-progression/${progressionId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} progression`)
      }

      const data = await response.json()
      if (data.success) {
        toast.success(`Progression ${action}d successfully`)
        fetchProgressions()
        fetchStatistics()
      }
    } catch (error) {
      console.error(`Error ${action}ing progression:`, error)
      toast.error(`Failed to ${action} progression`)
    }
  }

  // Handle bulk progression
  const handleBulkProgression = async () => {
    if (selectedProgressions.length === 0) {
      toast.error('Please select progressions to process')
      return
    }

    try {
      const response = await fetch(`${API_BASE}/partner-progression/bulk-approve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          progression_ids: selectedProgressions
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to process bulk progressions')
      }

      const data = await response.json()
      if (data.success) {
        toast.success(`Successfully processed ${selectedProgressions.length} progressions`)
        setSelectedProgressions([])
        fetchProgressions()
        fetchStatistics()
      }
    } catch (error) {
      console.error('Error processing bulk progressions:', error)
      toast.error('Failed to process bulk progressions')
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProgressions(progressions.map(p => p.id))
    } else {
      setSelectedProgressions([])
    }
  }

  // Handle select progression
  const handleSelectProgression = (progressionId: number) => {
    setSelectedProgressions(prev => 
      prev.includes(progressionId)
        ? prev.filter(id => id !== progressionId)
        : [...prev, progressionId]
    )
  }

  // Load data on component mount and when filters change
  useEffect(() => {
    if (token) {
      fetchStatistics()
      fetchProgressions()
      fetchEligibleStudents()
    }
  }, [token, currentPage, searchTerm, statusFilter, gradeFilter, academicYearFilter])

  if (!user || !token) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">Please log in to access this page.</p>
          <Button asChild>
            <Link href="/auth/login">Login</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Academic Progression Tracking
            </h1>
            <p className="text-gray-600 mt-2">
              Track and manage student academic progression through grade levels
            </p>
          </div>
          <div className="flex space-x-3 mt-4 md:mt-0">
            <Button asChild>
              <Link href="/dashboard/partner/progression/create">
                <Plus className="h-4 w-4 mr-2" />
                Create Progression
              </Link>
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Progressions</p>
                  <p className="text-2xl font-bold text-blue-600">{stats?.total_progressions || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats?.pending_progressions || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{stats?.completed_progressions || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Academic Year</p>
                  <p className="text-lg font-bold text-purple-600">{stats?.current_academic_year || '2024/2025'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Eligible Students Alert */}
        {eligibleStudents.length > 0 && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertCircle className="h-6 w-6 text-green-600 mr-3" />
                  <div>
                    <h3 className="font-semibold text-green-800">Students Eligible for Progression</h3>
                    <p className="text-sm text-green-700">
                      {eligibleStudents.length} students are eligible for grade advancement
                    </p>
                  </div>
                </div>
                <Button asChild size="sm">
                  <Link href="/dashboard/partner/progression/eligible">
                    View Eligible Students
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={gradeFilter} onValueChange={setGradeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filter by grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  <SelectItem value="primary_1">Primary 1</SelectItem>
                  <SelectItem value="primary_2">Primary 2</SelectItem>
                  <SelectItem value="primary_3">Primary 3</SelectItem>
                  <SelectItem value="primary_4">Primary 4</SelectItem>
                  <SelectItem value="primary_5">Primary 5</SelectItem>
                  <SelectItem value="primary_6">Primary 6</SelectItem>
                  <SelectItem value="secondary_1">Secondary 1</SelectItem>
                  <SelectItem value="secondary_2">Secondary 2</SelectItem>
                  <SelectItem value="secondary_3">Secondary 3</SelectItem>
                  <SelectItem value="secondary_4">Secondary 4</SelectItem>
                  <SelectItem value="secondary_5">Secondary 5</SelectItem>
                  <SelectItem value="secondary_6">Secondary 6</SelectItem>
                </SelectContent>
              </Select>
              <Select value={academicYearFilter} onValueChange={setAcademicYearFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Academic year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2024/2025">2024/2025</SelectItem>
                  <SelectItem value="2023/2024">2023/2024</SelectItem>
                  <SelectItem value="2022/2023">2022/2023</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions Bar */}
        {selectedProgressions.length > 0 && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-blue-800">
                    {selectedProgressions.length} progression(s) selected
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    onClick={handleBulkProgression}
                  >
                    <ArrowUp className="h-4 w-4 mr-2" />
                    Approve Selected
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Progressions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Progressions</CardTitle>
            <CardDescription>
              Track and manage student grade level progressions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading progressions...</span>
              </div>
            ) : progressions.length === 0 ? (
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No progressions found</p>
                <Button asChild>
                  <Link href="/dashboard/partner/progression/create">
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Progression
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedProgressions.length === progressions.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Student</TableHead>
                      <TableHead>From Grade</TableHead>
                      <TableHead>To Grade</TableHead>
                      <TableHead>Academic Year</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Progression Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {progressions.map((progression) => (
                      <TableRow key={progression.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedProgressions.includes(progression.id)}
                            onCheckedChange={() => handleSelectProgression(progression.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{progression.student?.full_name}</p>
                            <p className="text-sm text-gray-500">{progression.student?.student_id}</p>
                            <p className="text-xs text-gray-400">
                              {progression.student?.age} years old, {progression.student?.gender}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {formatGradeLevel(progression.from_grade)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {formatGradeLevel(progression.to_grade)}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">
                          {progression.academic_year}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(progression.status)}>
                            {progression.status.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(progression.progression_date).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/partner/progression/${progression.id}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              {progression.status === 'pending' && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => handleProgressionAction(progression.id, 'approve')}
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem asChild>
                                    <Link href={`/dashboard/partner/progression/${progression.id}/edit`}>
                                      <Edit className="h-4 w-4 mr-2" />
                                      Edit
                                    </Link>
                                  </DropdownMenuItem>
                                </>
                              )}
                              {progression.status === 'approved' && (
                                <DropdownMenuItem
                                  onClick={() => handleProgressionAction(progression.id, 'complete')}
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Mark Complete
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between pt-4">
                    <div className="text-sm text-gray-700">
                      Showing page {currentPage} of {totalPages}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
