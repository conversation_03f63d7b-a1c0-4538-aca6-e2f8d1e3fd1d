<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

use App\Models\Scholarship;

echo "Checking scholarships in database:\n";
echo "==================================\n\n";

$scholarships = Scholarship::all();

echo "Total scholarships: " . $scholarships->count() . "\n\n";

foreach ($scholarships as $scholarship) {
    echo "ID: " . $scholarship->id . "\n";
    echo "Title: " . $scholarship->title . "\n";
    echo "Status: " . $scholarship->status . "\n";
    echo "Category: " . $scholarship->category . "\n";
    echo "Deadline: " . $scholarship->application_deadline . "\n";
    echo "Created: " . $scholarship->created_at . "\n";
    echo "---\n";
}

// Check active scholarships
echo "\nActive scholarships with future deadlines:\n";
$activeScholarships = Scholarship::where(function($query) {
    $query->where('status', 'active')->orWhere('status', 'open');
})->where('application_deadline', '>', now())->get();

echo "Count: " . $activeScholarships->count() . "\n";

foreach ($activeScholarships as $scholarship) {
    echo "- " . $scholarship->title . " (Status: " . $scholarship->status . ", Deadline: " . $scholarship->application_deadline . ")\n";
}

// Let's update one scholarship to be active for testing
if ($scholarships->count() > 0) {
    echo "\nUpdating first scholarship for testing...\n";
    $firstScholarship = $scholarships->first();
    $firstScholarship->status = 'active';
    $firstScholarship->application_deadline = now()->addDays(30);
    $firstScholarship->save();
    
    echo "Updated scholarship: " . $firstScholarship->title . "\n";
    echo "New status: " . $firstScholarship->status . "\n";
    echo "New deadline: " . $firstScholarship->application_deadline . "\n";
}
