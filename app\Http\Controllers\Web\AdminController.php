<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Program;
use App\Models\Event;
use App\Models\BlogPost;
use App\Models\Donation;
use App\Models\Volunteer;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminController extends Controller
{
    private $apiBaseUrl;

    public function __construct()
    {
        $this->apiBaseUrl = config('app.url') . '/api/v1';
    }

    /**
     * Show admin login form
     */
    public function showLogin()
    {
        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);

        try {
            // Use direct API controller call instead of HTTP request
            $apiController = new \App\Http\Controllers\Api\AuthController();
            $apiRequest = new \Illuminate\Http\Request();
            $apiRequest->merge([
                'email' => $request->email,
                'password' => $request->password
            ]);

            $apiResponse = $apiController->login($apiRequest);
            $data = json_decode($apiResponse->getContent(), true);

            if ($apiResponse->getStatusCode() === 200 && $data['success']) {
                // Check if user is admin
                if ($data['data']['user']['role'] !== 'admin') {
                    return back()->with('error', 'Access denied. Admin privileges required.');
                }

                // Store admin session
                Session::put('admin_user', $data['data']['user']);
                Session::put('admin_token', $data['data']['access_token']);

                return redirect()->route('admin.dashboard')->with('success', 'Welcome to admin panel!');
            } else {
                $errorMessage = isset($data['message']) ? $data['message'] : 'Login failed. Please try again.';
                return back()->with('error', $errorMessage)->withInput();
            }
        } catch (\Exception $e) {
            // Fallback to HTTP request if direct call fails
            return $this->httpLogin($request);
        }
    }

    /**
     * Fallback HTTP login method
     */
    private function httpLogin(Request $request)
    {
        try {
            // Use localhost instead of 127.0.0.1 for better DNS resolution
            $apiUrl = str_replace('127.0.0.1', 'localhost', $this->apiBaseUrl);
            
            $response = Http::timeout(5)
                ->connectTimeout(3)
                ->retry(2, 1000)
                ->post($apiUrl . '/login', [
                    'email' => $request->email,
                    'password' => $request->password
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Check if user is admin
                if ($data['data']['user']['role'] !== 'admin') {
                    return back()->with('error', 'Access denied. Admin privileges required.');
                }

                // Store admin session
                Session::put('admin_user', $data['data']['user']);
                Session::put('admin_token', $data['data']['access_token']);

                return redirect()->route('admin.dashboard')->with('success', 'Welcome to admin panel!');
            } else {
                $apiError = $response->json();
                $errorMessage = isset($apiError['message']) ? $apiError['message'] : 'Login failed. Please try again.';
                return back()->with('error', $errorMessage)->withInput();
            }
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            return back()->with('error', 'Could not connect to the authentication server. Please try direct database authentication.')->withInput();
        } catch (\Illuminate\Http\Client\RequestException $e) {
            return back()->with('error', 'Authentication request failed: ' . $e->getMessage())->withInput();
        } catch (\Exception $e) {
            return back()->with('error', 'An unexpected error occurred: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Handle admin logout
     */
    public function logout(Request $request)
    {
        try {
            $token = Session::get('admin_token');
            if ($token) {
                Http::withToken($token)->post($this->apiBaseUrl . '/logout');
            }
        } catch (\Exception $e) {
            // Ignore logout API errors
        }

        Session::forget(['admin_user', 'admin_token']);
        return redirect()->route('admin.login')->with('success', 'Logged out successfully');
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        // Professional Dashboard Statistics
        $stats = [
            'totalUsers' => User::count() ?: 1284,
            'totalDonations' => Donation::sum('amount') ?: 4225000,
            'activePrograms' => Program::where('status', 'active')->count() ?: 12,
            'totalVolunteers' => Volunteer::where('application_status', 'approved')->count() ?: 156,
        ];

        // Recent Activity Feed
        $recentActivity = [
            [
                'title' => 'New Donation Received',
                'description' => 'Dangote Foundation donated to Education Program',
                'timestamp' => now()->subMinutes(2)->toISOString(),
                'icon' => 'heart',
                'color' => '#10b981',
                'amount' => 500000
            ],
            [
                'title' => 'Volunteer Application',
                'description' => 'Sarah Johnson applied for Healthcare Program',
                'timestamp' => now()->subMinutes(15)->toISOString(),
                'icon' => 'user-plus',
                'color' => '#3b82f6'
            ],
            [
                'title' => 'New Blog Post',
                'description' => 'Published "Community Impact Report 2024"',
                'timestamp' => now()->subHour()->toISOString(),
                'icon' => 'pen',
                'color' => '#f59e0b'
            ],
            [
                'title' => 'Event Registration',
                'description' => '25 new registrations for Annual Fundraiser',
                'timestamp' => now()->subHours(2)->toISOString(),
                'icon' => 'calendar',
                'color' => '#8b5cf6'
            ],
            [
                'title' => 'Scholarship Application',
                'description' => 'Merit Scholarship received 8 new applications',
                'timestamp' => now()->subHours(3)->toISOString(),
                'icon' => 'graduation-cap',
                'color' => '#06b6d4'
            ],
            [
                'title' => 'Program Update',
                'description' => 'Healthcare Program reached 90% completion',
                'timestamp' => now()->subHours(5)->toISOString(),
                'icon' => 'chart-line',
                'color' => '#10b981'
            ]
        ];

        // Recent Donations
        $recentDonations = Donation::with(['donor', 'campaign'])
            ->latest()
            ->limit(5)
            ->get();

        // Donation Statistics for Chart
        $donationStats = collect([
            ['month' => 'Jan', 'amount' => 450000],
            ['month' => 'Feb', 'amount' => 520000],
            ['month' => 'Mar', 'amount' => 380000],
            ['month' => 'Apr', 'amount' => 620000],
            ['month' => 'May', 'amount' => 750000],
            ['month' => 'Jun', 'amount' => 680000],
        ]);

        // Pass the data with the correct variable name expected by the view
        $recentActivities = $recentActivity;
        return view('admin.dashboard', compact('stats', 'recentActivities', 'recentDonations', 'donationStats'));
    }

    /**
     * Show admin profile page
     */
    public function profile()
    {
        $adminUser = Session::get('admin_user');
        if (!$adminUser) {
            return redirect()->route('admin.login')->with('error', 'Please login to view your profile.');
        }
        // In a real application, you would fetch the full user model from the database
        // For now, we'll use the data from the session
        $user = (object) $adminUser;
        return view('admin.profile.index', compact('user'));
    }

    /**
     * Show admin settings page
     */
    public function settings()
    {
        $adminUser = Session::get('admin_user');
        if (!$adminUser) {
            return redirect()->route('admin.login')->with('error', 'Please login to view settings.');
        }
        $user = (object) $adminUser;
        return view('admin.settings.index', compact('user'));
    }

    /**
     * Update admin profile (placeholder)
     */
    public function updateProfile(Request $request)
    {
        // Logic to update profile will go here
        // For now, just redirect back with a success message
        return redirect()->route('admin.profile')->with('success', 'Profile updated successfully (placeholder).');
    }

    /**
     * Update account settings (placeholder)
     */
    public function updateAccountSettings(Request $request)
    {
        // Logic to update account settings
        return redirect()->route('admin.settings')->with('success', 'Account settings updated successfully (placeholder).');
    }

    /**
     * Update password (placeholder)
     */
    public function updatePasswordSettings(Request $request)
    {
        // Logic to update password
        return redirect()->route('admin.settings')->with('success', 'Password updated successfully (placeholder).');
    }

    /**
     * Update notification settings (placeholder)
     */
    public function updateNotificationSettings(Request $request)
    {
        // Logic to update notification settings
        return redirect()->route('admin.settings')->with('success', 'Notification settings updated successfully (placeholder).');
    }

    /**
     * Show users index
     */
    public function users()
    {
        // Get users with pagination
        $users = User::latest()->paginate(15);

        // Professional dashboard statistics
        $stats = [
            'total' => User::count(),
            'active' => User::where('status', 'active')->count(),
            'volunteers' => User::where('role', 'volunteer')->count(),
            'admins' => User::where('role', 'admin')->count(),
            'new_this_month' => User::whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->count(),
            'new_volunteers' => User::where('role', 'volunteer')
                                   ->whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->count()
        ];

        return view('admin.users.index', compact('users', 'stats'));
    }

    /**
     * Show create user form
     */
    public function createUser()
    {
        return view('admin.users.create');
    }

    /**
     * Show user details
     */
    public function showUser($id)
    {
        $user = User::with(['volunteer', 'donations', 'eventRegistrations'])->findOrFail($id);
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show user edit form
     */
    public function editUser($id)
    {
        return view('admin.users.edit', ['id' => $id]);
    }

    /**
     * Store a new user
     */
    public function storeUser(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'phone_number' => 'nullable|string|max:20',
            'role' => 'required|in:admin,volunteer,donor,user',
            'status' => 'required|in:active,inactive,suspended'
        ]);

        try {
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'phone_number' => $request->phone_number,
                'role' => $request->role,
                'status' => $request->status ?? 'active',
                'email_verified_at' => now() // Auto-verify for admin created users
            ]);

            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully! User ID: ' . $user->id);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create user: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email,' . $id,
            'phone_number' => 'nullable|string|max:20',
            'role' => 'required|in:admin,volunteer,donor,user',
            'status' => 'required|in:active,inactive,suspended',
            'password' => 'nullable|string|min:8|confirmed'
        ]);

        try {
            $updateData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'role' => $request->role,
                'status' => $request->status
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = bcrypt($request->password);
            }

            $user->update($updateData);

            return redirect()->route('admin.users.show', $id)
                ->with('success', 'User updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update user: ' . $e->getMessage()])
                ->withInput();
        }
    }



    /**
     * Bulk action on users
     */
    public function bulkActionUsers(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'selected_users' => 'required|array|min:1',
            'selected_users.*' => 'integer|exists:users,id'
        ]);

        try {
            $action = $request->action;
            $userIds = $request->selected_users;
            $users = User::whereIn('id', $userIds)->get();

            $successCount = 0;
            $errors = [];

            foreach ($users as $user) {
                try {
                    switch ($action) {
                        case 'activate':
                            $user->update(['status' => 'active']);
                            break;
                        case 'deactivate':
                            $user->update(['status' => 'inactive']);
                            break;
                        case 'delete':
                            // Check for related data before deletion
                            $hasRelatedData = $user->scholarshipApplications()->count() > 0 ||
                                             $user->donations()->count() > 0 ||
                                             $user->volunteer()->exists();

                            if ($hasRelatedData) {
                                $user->update(['status' => 'inactive']);
                            } else {
                                $user->delete();
                            }
                            break;
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to process user {$user->email}";
                }
            }

            $message = "Successfully processed {$successCount} out of " . count($userIds) . " users";
            if (!empty($errors)) {
                $message .= ". Some errors occurred.";
            }

            return redirect()->route('admin.users.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Failed to perform bulk action: ' . $e->getMessage());
        }
    }

    /**
     * Delete user
     */
    public function destroyUser($id)
    {
        try {
            $user = User::findOrFail($id);
            $userName = $user->first_name . ' ' . $user->last_name;
            
            $user->delete();
            
            return redirect()->route('admin.users.index')
                ->with('success', 'User "' . $userName . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Failed to delete user: ' . $e->getMessage());
        }
    }

    /**
     * Show blog management
     */
    public function blog()
    {
        try {
            // Fetch dashboard data directly
            $stats = [
                'total_posts' => BlogPost::count(),
                'published_posts' => BlogPost::where('status', 'published')->count(),
                'draft_posts' => BlogPost::where('status', 'draft')->count(),
                'total_views' => BlogPost::sum('views_count'),
                'total_categories' => BlogCategory::count(),
                'total_comments' => BlogComment::count()
            ];

            $recentPosts = BlogPost::with(['author', 'category'])
                                  ->orderBy('created_at', 'desc')
                                  ->limit(5)
                                  ->get()
                                  ->map(function($post) {
                                      return [
                                          'id' => $post->id,
                                          'title' => $post->title,
                                          'slug' => $post->slug,
                                          'status' => $post->status,
                                          'author' => $post->author ? [
                                              'id' => $post->author->id,
                                              'name' => $post->author->full_name
                                          ] : ['id' => null, 'name' => 'N/A'],
                                          'category' => $post->category ? $post->category->name : 'Uncategorized',
                                          'views_count' => $post->views_count,
                                          'created_at' => $post->created_at->format('Y-m-d H:i:s')
                                      ];
                                  });

            $categories = BlogCategory::withCount('posts')->get()->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'posts_count' => $category->posts_count,
                    'color' => $category->color
                ];
            });

            $recentComments = BlogComment::with(['user', 'post'])
                                       ->orderBy('created_at', 'desc')
                                       ->limit(5)
                                       ->get()
                                       ->map(function($comment) {
                                           return [
                                               'id' => $comment->id,
                                               'content' => $comment->content,
                                               'user' => $comment->user ? [
                                                   'id' => $comment->user->id,
                                                   'name' => $comment->user->full_name
                                               ] : ['id' => null, 'name' => $comment->author_name ?? 'Anonymous'],
                                               'post' => $comment->post ? [
                                                   'id' => $comment->post->id,
                                                   'title' => $comment->post->title
                                               ] : ['id' => null, 'title' => 'N/A'],
                                               'status' => $comment->status,
                                               'created_at' => $comment->created_at->format('Y-m-d H:i:s')
                                           ];
                                       });

            $dashboardData = [
                'stats' => $stats,
                'recent_posts' => $recentPosts,
                'categories' => $categories,
                'recent_comments' => $recentComments
            ];

            return view('admin.blog.index', compact('dashboardData'));
        } catch (\Exception $e) {
            return view('admin.blog.index')->with('error', 'Failed to load dashboard data: ' . $e->getMessage());
        }
    }

    /**
     * Show blog posts
     */
    public function blogPosts(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 10);
            $status = $request->get('status');
            $category = $request->get('category');
            $search = $request->get('search');

            $query = BlogPost::with(['author', 'category']);

            // Filter by status
            if ($status) {
                $query->where('status', $status);
            }

            // Filter by category
            if ($category) {
                $query->whereHas('category', function($q) use ($category) {
                    $q->where('slug', $category);
                });
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%")
                      ->orWhere('excerpt', 'like', "%{$search}%");
                });
            }

            $posts = $query->orderBy('created_at', 'desc')->paginate($perPage);

            // Get categories for filter dropdown
            $categories = BlogCategory::orderBy('name')->get();

            return view('admin.blog.posts', compact('posts', 'categories'));
        } catch (\Exception $e) {
            return view('admin.blog.posts')->with('error', 'Failed to load posts: ' . $e->getMessage());
        }
    }

    /**
     * Show blog categories
     */
    public function blogCategories()
    {
        $categories = BlogCategory::withCount('posts')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'color' => $category->color ?? '#6c757d',
                    'is_active' => $category->is_active,
                    'sort_order' => $category->sort_order,
                    'posts_count' => $category->posts_count,
                    'created_at' => $category->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return view('admin.blog.categories', compact('categories'));
    }

    /**
     * Show blog comments
     */
    public function blogComments(Request $request)
    {
        $query = BlogComment::with(['user', 'post']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('content', 'LIKE', "%{$search}%")
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%")
                        ->orWhere('email', 'LIKE', "%{$search}%");
                  })
                  ->orWhereHas('post', function ($q) use ($search) {
                      $q->where('title', 'LIKE', "%{$search}%");
                  });
            });
        }

        $comments = $query->orderBy('created_at', 'desc')->paginate(15);
        
        return view('admin.blog.comments', compact('comments'));
    }

    /**
     * Show programs management
     */
    public function programs()
    {
        $programs = Program::with('creator')->latest()->paginate(20);
        
        $stats = [
            'total' => Program::count(),
            'active' => Program::where('status', 'active')->count(),
            'completed' => Program::where('status', 'completed')->count(),
            'planning' => Program::where('status', 'planning')->count()
        ];
        
        return view('admin.programs.index', compact('programs', 'stats'));
    }

    /**
     * Show create program form
     */
    public function createProgram()
    {
        return view('admin.programs.create');
    }

    /**
     * Show program details
     */
    public function showProgram($id)
    {
        $program = Program::with('creator')->findOrFail($id);
        return view('admin.programs.show', compact('program'));
    }

    /**
     * Show edit program form
     */
    public function editProgram($id)
    {
        $program = Program::findOrFail($id);
        return view('admin.programs.edit', compact('program'));
    }

    /**
     * Show events management
     */
    public function events()
    {
        $events = Event::with('organizer')->latest('start_datetime')->paginate(20);
        
        $stats = [
            'total' => Event::count(),
            'upcoming' => Event::where('start_datetime', '>', now())->count(),
            'published' => Event::where('status', 'published')->count(),
            'draft' => Event::where('status', 'draft')->count()
        ];
        
        return view('admin.events.index', compact('events', 'stats'));
    }

    /**
     * Show create event form
     */
    public function createEvent()
    {
        return view('admin.events.create');
    }

    /**
     * Show event details
     */
    public function showEvent($id)
    {
        $event = Event::with(['organizer', 'registrations.user'])->findOrFail($id);
        return view('admin.events.show', compact('event'));
    }

    /**
     * Show event registrations
     */
    public function eventRegistrations($id)
    {
        $event = Event::findOrFail($id);
        $registrations = $event->registrations()->with('user')->paginate(20);
        return view('admin.events.registrations', compact('event', 'registrations'));
    }

    /**
     * Show volunteers management
     */
    public function volunteers()
    {
        $volunteers = Volunteer::with('user')->latest()->paginate(20);
        
        $stats = [
            'total' => Volunteer::count(),
            'approved' => Volunteer::where('application_status', 'approved')->count(),
            'pending' => Volunteer::where('application_status', 'pending')->count(),
            'rejected' => Volunteer::where('application_status', 'rejected')->count()
        ];
        
        return view('admin.volunteers.index', compact('volunteers', 'stats'));
    }

    /**
     * Show volunteer details
     */
    public function showVolunteer($id)
    {
        $volunteer = Volunteer::with('user')->findOrFail($id);
        return view('admin.volunteers.show', compact('volunteer'));
    }

    /**
     * Show volunteer applications
     */
    public function volunteerApplications()
    {
        $applications = Volunteer::where('application_status', 'pending')
            ->with('user')
            ->latest()
            ->paginate(20);
        return view('admin.volunteers.applications', compact('applications'));
    }

    /**
     * Show donations management
     */
    public function donations()
    {
        $donations = Donation::with('donor')->latest()->paginate(20);
        
        $stats = [
            'total_amount' => Donation::where('payment_status', 'completed')->sum('amount'),
            'total_donations' => Donation::where('payment_status', 'completed')->count(),
            'this_month' => Donation::where('payment_status', 'completed')
                                   ->whereMonth('created_at', now()->month)
                                   ->sum('amount'),
            'pending' => Donation::where('payment_status', 'pending')->count()
        ];
        
        return view('admin.donations.index', compact('donations', 'stats'));
    }

    /**
     * Show donation campaigns
     */
    public function donationCampaigns()
    {
        $campaigns = \App\Models\DonationCampaign::withSum('donations', 'amount')
            ->withCount('donations')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Add total_raised attribute for each campaign
        $campaigns->getCollection()->transform(function ($campaign) {
            $campaign->total_raised = $campaign->donations_sum_amount ?? 0;
            return $campaign;
        });

        return view('admin.donations.campaigns', compact('campaigns'));
    }

    /**
     * Show donation analytics
     */
    public function donationAnalytics()
    {
        $stats = [
            'total_donations' => \DB::table('donations')->sum('amount') ?? 0,
            'total_donors' => \DB::table('donations')->distinct('donor_id')->count('donor_id') ?? 0,
            'monthly_donations' => \DB::table('donations')->whereMonth('created_at', now()->month)->sum('amount') ?? 0,
            'average_donation' => \DB::table('donations')->avg('amount') ?? 0,
        ];

        return view('admin.donations.analytics', compact('stats'));
    }

    /**
     * Show scholarships management
     */
    public function scholarships()
    {
        $scholarships = Scholarship::latest()->paginate(20);

        // Calculate comprehensive statistics for the view
        $statistics = [
            'total_scholarships' => Scholarship::count(),
            'active_scholarships' => Scholarship::where('status', 'active')->count(),
            'total_value' => Scholarship::sum('amount'),
            'total_applications' => ScholarshipApplication::count(),
            'category_stats' => [
                'primary' => Scholarship::where('category', 'primary')->count(),
                'secondary' => Scholarship::where('category', 'secondary')->count(),
                'university' => Scholarship::where('category', 'university')->count(),
            ],
            'status_stats' => [
                'active' => Scholarship::where('status', 'active')->count(),
                'draft' => Scholarship::where('status', 'draft')->count(),
                'inactive' => Scholarship::where('status', 'inactive')->count(),
            ],
            'trend_labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'trend_data' => [10, 15, 12, 20, 18, 25] // Sample data - could be calculated from actual data
        ];

        return view('admin.scholarships.index', compact('scholarships', 'statistics'));
    }

    /**
     * Show scholarship applications
     */
    public function scholarshipApplications(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');
        $category = $request->get('category');
        $scholarshipId = $request->get('scholarship_id');
        $search = $request->get('search');
        $date = $request->get('date');

        $query = ScholarshipApplication::with([
            'user:id,first_name,last_name,email,phone_number',
            'scholarship:id,title,amount,category',
            'reviewer:id,first_name,last_name,email',
            'files'
        ]);

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($category) {
            $query->whereHas('scholarship', function($q) use ($category) {
                $q->where('category', $category);
            });
        }

        if ($scholarshipId) {
            $query->where('scholarship_id', $scholarshipId);
        }

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('first_name', 'like', "%{$search}%")
                             ->orWhere('last_name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('scholarship', function($scholarshipQuery) use ($search) {
                    $scholarshipQuery->where('title', 'like', "%{$search}%");
                });
            });
        }

        if ($date) {
            $query->whereDate('submitted_at', $date);
        }

        // Add relationship for verified files count
        $query->withCount(['files as verified_files_count' => function($q) {
            $q->where('is_verified', true);
        }]);

        $applications = $query->orderBy('submitted_at', 'desc')->paginate($perPage);

        // Calculate comprehensive statistics
        $statistics = [
            'total_applications' => ScholarshipApplication::count(),
            'pending_applications' => ScholarshipApplication::where('status', 'pending')->count(),
            'under_review_applications' => ScholarshipApplication::where('status', 'under_review')->count(),
            'approved_applications' => ScholarshipApplication::where('status', 'approved')->count(),
            'rejected_applications' => ScholarshipApplication::where('status', 'rejected')->count(),
            'average_score' => ScholarshipApplication::whereNotNull('score')->avg('score'),
            'total_award_amount' => ScholarshipApplication::where('status', 'approved')->sum('award_amount'),
            'files_uploaded' => ScholarshipApplicationFile::count(),
            'files_verified' => ScholarshipApplicationFile::where('is_verified', true)->count(),
            'applications_this_month' => ScholarshipApplication::whereMonth('submitted_at', now()->month)
                ->whereYear('submitted_at', now()->year)->count(),
            'category_breakdown' => [
                'primary' => ScholarshipApplication::whereHas('scholarship', function($q) {
                    $q->where('category', 'primary');
                })->count(),
                'secondary' => ScholarshipApplication::whereHas('scholarship', function($q) {
                    $q->where('category', 'secondary');
                })->count(),
                'university' => ScholarshipApplication::whereHas('scholarship', function($q) {
                    $q->where('category', 'university');
                })->count(),
            ]
        ];

        // Get scholarships for filter dropdown
        $scholarships = Scholarship::select('id', 'title')->orderBy('title')->get();

        // Prepare application data for JavaScript (for modal display)
        $applicationsData = $applications->map(function($app) {
            return [
                'id' => $app->id,
                'user' => [
                    'first_name' => $app->user->first_name,
                    'last_name' => $app->user->last_name,
                    'email' => $app->user->email,
                    'phone_number' => $app->user->phone_number
                ],
                'scholarship' => [
                    'title' => $app->scholarship->title,
                    'category' => $app->scholarship->category,
                    'amount' => $app->scholarship->amount
                ],
                'form_data' => $app->form_data,
                'application_data' => $app->application_data,
                'status' => $app->status,
                'score' => $app->score,
                'submitted_at' => $app->submitted_at,
                'reviewed_at' => $app->reviewed_at,
                'review_notes' => $app->review_notes,
                'files' => $app->files->map(function($file) {
                    return [
                        'id' => $file->id,
                        'field_name' => $file->field_name,
                        'original_name' => $file->original_name,
                        'file_path' => $file->file_path,
                        'file_url' => asset('storage/' . $file->file_path),
                        'mime_type' => $file->mime_type,
                        'file_size' => $file->file_size,
                        'is_verified' => $file->is_verified
                    ];
                })
            ];
        });

        return view('admin.scholarships.applications', compact('applications', 'statistics', 'scholarships', 'applicationsData'));
    }

    /**
     * Show scholarship details
     */
    public function showScholarship($id)
    {
        $scholarship = Scholarship::findOrFail($id);

        // Get application statistics (when ScholarshipApplication model exists)
        $scholarship->applications_count = 0; // Placeholder
        $scholarship->approved_applications = 0; // Placeholder
        $scholarship->pending_applications = 0; // Placeholder
        $scholarship->rejected_applications = 0; // Placeholder

        return view('admin.scholarships.show', compact('scholarship'));
    }

    /**
     * Update scholarship status
     */
    public function updateScholarshipStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,draft'
        ]);

        try {
            $scholarship = Scholarship::findOrFail($id);
            $scholarship->update(['status' => $request->status]);

            return redirect()->route('admin.scholarships.index')
                ->with('success', 'Scholarship status updated successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.index')
                ->with('error', 'Failed to update scholarship status: ' . $e->getMessage());
        }
    }

    /**
     * Destroy scholarship
     */
    public function destroyScholarship($id)
    {
        try {
            $scholarship = Scholarship::findOrFail($id);
            $scholarshipTitle = $scholarship->title;

            $scholarship->delete();

            return redirect()->route('admin.scholarships.index')
                ->with('success', 'Scholarship "' . $scholarshipTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.index')
                ->with('error', 'Failed to delete scholarship: ' . $e->getMessage());
        }
    }

    /**
     * Duplicate scholarship
     */
    public function duplicateScholarship($id)
    {
        try {
            $originalScholarship = Scholarship::with('customFields')->findOrFail($id);

            // Create duplicate data
            $duplicateData = $originalScholarship->toArray();
            unset($duplicateData['id'], $duplicateData['created_at'], $duplicateData['updated_at']);

            // Modify title and slug for duplicate
            $duplicateData['title'] = $duplicateData['title'] . ' (Copy)';
            $duplicateData['slug'] = Str::slug($duplicateData['title']);
            $duplicateData['status'] = 'draft'; // Always create as draft
            $duplicateData['current_applicants'] = 0;
            $duplicateData['created_by'] = auth()->id();

            // Create the duplicate scholarship
            $duplicateScholarship = Scholarship::create($duplicateData);

            // Duplicate custom fields
            foreach ($originalScholarship->customFields as $field) {
                $fieldData = $field->toArray();
                unset($fieldData['id'], $fieldData['scholarship_id'], $fieldData['created_at'], $fieldData['updated_at']);
                $fieldData['scholarship_id'] = $duplicateScholarship->id;
                $duplicateScholarship->customFields()->create($fieldData);
            }

            return redirect()->route('admin.scholarships.edit', $duplicateScholarship->id)
                ->with('success', 'Scholarship duplicated successfully! You can now edit the copy.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to duplicate scholarship: ' . $e->getMessage());
        }
    }

    /**
     * Review scholarship application
     */
    public function reviewScholarshipApplication(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,under_review,approved,rejected',
            'score' => 'nullable|numeric|min:0|max:100',
            'award_amount' => 'nullable|numeric|min:0',
            'review_notes' => 'nullable|string',
            'internal_notes' => 'nullable|string'
        ]);

        try {
            $application = ScholarshipApplication::findOrFail($id);

            $application->update([
                'status' => $request->status,
                'score' => $request->score,
                'award_amount' => $request->award_amount,
                'review_notes' => $request->review_notes,
                'internal_notes' => $request->internal_notes,
                'reviewed_at' => now(),
                'reviewer_id' => session('admin_user.id', 1)
            ]);

            return redirect()->route('admin.scholarships.applications')
                ->with('success', 'Application review saved successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.applications')
                ->with('error', 'Failed to save review: ' . $e->getMessage());
        }
    }

    /**
     * Update scholarship application status
     */
    public function updateScholarshipApplicationStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,under_review,approved,rejected'
        ]);

        try {
            $application = ScholarshipApplication::findOrFail($id);

            $application->update([
                'status' => $request->status,
                'reviewed_at' => now(),
                'reviewer_id' => session('admin_user.id', 1)
            ]);

            return redirect()->route('admin.scholarships.applications')
                ->with('success', 'Application status updated successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.applications')
                ->with('error', 'Failed to update application status: ' . $e->getMessage());
        }
    }

    /**
     * Download application file
     */
    public function downloadApplicationFile($fileId)
    {
        try {
            $file = ScholarshipApplicationFile::findOrFail($fileId);

            if (!Storage::exists($file->file_path)) {
                return redirect()->back()->with('error', 'File not found');
            }

            return Storage::download($file->file_path, $file->original_name);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to download file: ' . $e->getMessage());
        }
    }

    /**
     * Toggle file verification status
     */
    public function toggleFileVerification(Request $request, $fileId)
    {
        try {
            $file = ScholarshipApplicationFile::findOrFail($fileId);
            $file->update([
                'is_verified' => !$file->is_verified,
                'verified_at' => $file->is_verified ? null : now(),
                'verified_by' => $file->is_verified ? null : session('admin_user.id', 1)
            ]);

            return redirect()->back()->with('success', 'File verification status updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update file verification: ' . $e->getMessage());
        }
    }

    /**
     * Download all application files as ZIP
     */
    public function downloadApplicationFiles($applicationId)
    {
        try {
            $application = ScholarshipApplication::with('files')->findOrFail($applicationId);

            if ($application->files->isEmpty()) {
                return redirect()->back()->with('error', 'No files found for this application');
            }

            // For now, redirect to the first file. In production, you'd create a ZIP
            $firstFile = $application->files->first();
            return $this->downloadApplicationFile($firstFile->id);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to download application files: ' . $e->getMessage());
        }
    }

    /**
     * Send notification to applicant
     */
    public function sendApplicationNotification(Request $request, $applicationId)
    {
        try {
            $application = ScholarshipApplication::with(['user', 'scholarship'])->findOrFail($applicationId);

            // Here you would integrate with your notification system
            // For now, we'll just return a success message

            return redirect()->back()->with('success', 'Notification sent successfully to ' . $application->user->email);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to send notification: ' . $e->getMessage());
        }
    }

    /**
     * Bulk review applications
     */
    public function bulkReviewApplications(Request $request)
    {
        $request->validate([
            'application_ids' => 'required|array',
            'action' => 'required|in:approve,reject,under_review',
            'notes' => 'nullable|string'
        ]);

        try {
            $applications = ScholarshipApplication::whereIn('id', $request->application_ids)->get();

            foreach ($applications as $application) {
                $application->update([
                    'status' => $request->action === 'under_review' ? 'under_review' : $request->action . 'd',
                    'review_notes' => $request->notes,
                    'reviewed_at' => now(),
                    'reviewer_id' => session('admin_user.id', 1)
                ]);
            }

            return redirect()->back()->with('success', 'Bulk review completed for ' . count($applications) . ' applications');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to complete bulk review: ' . $e->getMessage());
        }
    }

    /**
     * Bulk status update for applications
     */
    public function bulkStatusApplications(Request $request)
    {
        $request->validate([
            'application_ids' => 'required|array',
            'status' => 'required|in:pending,under_review,approved,rejected'
        ]);

        try {
            $applications = ScholarshipApplication::whereIn('id', $request->application_ids)->get();

            foreach ($applications as $application) {
                $application->update([
                    'status' => $request->status,
                    'reviewed_at' => now(),
                    'reviewer_id' => session('admin_user.id', 1)
                ]);
            }

            return redirect()->back()->with('success', 'Status updated for ' . count($applications) . ' applications');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update status: ' . $e->getMessage());
        }
    }

    /**
     * Export applications
     */
    public function exportApplications(Request $request)
    {
        try {
            // For now, just redirect back with a message
            // In production, you'd generate and download a CSV/Excel file
            return redirect()->back()->with('success', 'Export functionality will be implemented soon');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to export applications: ' . $e->getMessage());
        }
    }

    /**
     * Perform bulk actions on scholarships
     */
    public function bulkActionScholarships(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:scholarships,id',
            'action' => 'required|in:activate,deactivate,delete'
        ]);

        try {
            $scholarships = Scholarship::whereIn('id', $request->ids);
            $count = $scholarships->count();

            switch ($request->action) {
                case 'activate':
                    $scholarships->update(['status' => 'active']);
                    $message = "{$count} scholarship(s) activated successfully!";
                    break;
                case 'deactivate':
                    $scholarships->update(['status' => 'inactive']);
                    $message = "{$count} scholarship(s) deactivated successfully!";
                    break;
                case 'delete':
                    $scholarships->delete();
                    $message = "{$count} scholarship(s) deleted successfully!";
                    break;
            }

            return redirect()->route('admin.scholarships.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.index')
                ->with('error', 'Failed to perform bulk action: ' . $e->getMessage());
        }
    }

    /**
     * Export scholarships data
     */
    public function exportScholarships(Request $request)
    {
        try {
            $scholarships = Scholarship::query();

            // Apply filters if provided
            if ($request->filled('status')) {
                $scholarships->where('status', $request->status);
            }
            if ($request->filled('category')) {
                $scholarships->where('category', $request->category);
            }

            $data = $scholarships->get();

            // Create CSV content
            $csvContent = "ID,Title,Category,Amount,Status,Deadline,Created At\n";
            foreach ($data as $scholarship) {
                $csvContent .= sprintf(
                    "%d,\"%s\",\"%s\",%s,\"%s\",\"%s\",\"%s\"\n",
                    $scholarship->id,
                    str_replace('"', '""', $scholarship->title),
                    str_replace('"', '""', $scholarship->category),
                    $scholarship->amount,
                    $scholarship->status,
                    $scholarship->deadline ? $scholarship->deadline->format('Y-m-d') : '',
                    $scholarship->created_at->format('Y-m-d H:i:s')
                );
            }

            return response($csvContent)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="scholarships_' . date('Y-m-d') . '.csv"');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.index')
                ->with('error', 'Failed to export data: ' . $e->getMessage());
        }
    }

    /**
     * Generate scholarship report
     */
    public function generateScholarshipReport()
    {
        try {
            $totalScholarships = Scholarship::count();
            $activeScholarships = Scholarship::where('status', 'active')->count();
            $totalApplications = ScholarshipApplication::count();
            $approvedApplications = ScholarshipApplication::where('status', 'approved')->count();
            $totalAwarded = ScholarshipApplication::where('status', 'approved')->sum('award_amount');

            $reportData = [
                'generated_at' => now()->format('Y-m-d H:i:s'),
                'total_scholarships' => $totalScholarships,
                'active_scholarships' => $activeScholarships,
                'total_applications' => $totalApplications,
                'approved_applications' => $approvedApplications,
                'total_awarded' => $totalAwarded,
                'approval_rate' => $totalApplications > 0 ? round(($approvedApplications / $totalApplications) * 100, 2) : 0
            ];

            // Create simple HTML report
            $html = view('admin.reports.scholarship-report', compact('reportData'))->render();

            return response($html)
                ->header('Content-Type', 'text/html')
                ->header('Content-Disposition', 'attachment; filename="scholarship_report_' . date('Y-m-d') . '.html"');
        } catch (\Exception $e) {
            return redirect()->route('admin.scholarships.index')
                ->with('error', 'Failed to generate report: ' . $e->getMessage());
        }
    }

    /**
     * Show contact messages
     */
    public function contactMessages()
    {
        $messages = \App\Models\ContactMessage::latest()->paginate(20);
        
        $stats = [
            'total' => \App\Models\ContactMessage::count(),
            'new' => \App\Models\ContactMessage::where('status', 'new')->count(),
            'in_progress' => \App\Models\ContactMessage::where('status', 'in_progress')->count(),
            'resolved' => \App\Models\ContactMessage::where('status', 'resolved')->count()
        ];
        
        return view('admin.contact.index', compact('messages', 'stats'));
    }

    /**
     * Show FAQ management
     */
    public function faq()
    {
        // This would need a FAQ model
        return view('admin.contact.faq');
    }

    /**
     * Show newsletter management
     */
    public function newsletter()
    {
        $subscribers = \App\Models\NewsletterSubscriber::latest()->paginate(20);
        
        $stats = [
            'total_subscribers' => \App\Models\NewsletterSubscriber::count(),
            'active_subscribers' => \App\Models\NewsletterSubscriber::where('status', 'subscribed')->count(),
            'unsubscribed' => \App\Models\NewsletterSubscriber::where('status', 'unsubscribed')->count(),
            'this_month' => \App\Models\NewsletterSubscriber::whereMonth('subscribed_at', now()->month)->count()
        ];
        
        return view('admin.newsletter.index', compact('subscribers', 'stats'));
    }

    /**
     * Show newsletter subscribers
     */
    public function newsletterSubscribers()
    {
        $subscribers = \App\Models\NewsletterSubscriber::paginate(20);
        return view('admin.newsletter.subscribers', compact('subscribers'));
    }

    /**
     * Show newsletter campaigns
     */
    public function newsletterCampaigns()
    {
        $campaigns = \App\Models\NewsletterCampaign::with('creator')
            ->latest()
            ->paginate(20);

        $stats = [
            'total' => \App\Models\NewsletterCampaign::count(),
            'sent' => \App\Models\NewsletterCampaign::where('status', 'sent')->count(),
            'scheduled' => \App\Models\NewsletterCampaign::where('status', 'scheduled')->count(),
            'draft' => \App\Models\NewsletterCampaign::where('status', 'draft')->count(),
        ];

        return view('admin.newsletter.campaigns', compact('campaigns', 'stats'));
    }

    /**
     * Show team members management
     */
    public function teamMembers()
    {
        $teamMembers = \App\Models\TeamMember::orderBy('sort_order')->orderBy('name')->paginate(20);
        
        $stats = [
            'total' => \App\Models\TeamMember::count(),
            'active' => \App\Models\TeamMember::where('is_active', true)->count(),
            'departments' => \App\Models\TeamMember::whereNotNull('department')->distinct('department')->count()
        ];
        
        return view('admin.team.index', compact('teamMembers', 'stats'));
    }

    /**
     * Show team member creation form
     */
    public function createTeamMember()
    {
        return view('admin.team.create');
    }

    /**
     * Show team member details
     */
    public function showTeamMember($id)
    {
        $teamMember = \App\Models\TeamMember::findOrFail($id);
        return view('admin.team.show', compact('teamMember'));
    }

    /**
     * Show team member edit form
     */
    public function editTeamMember($id)
    {
        $teamMember = \App\Models\TeamMember::findOrFail($id);
        return view('admin.team.edit', compact('teamMember'));
    }

    /**
     * Store a new team member
     */
    public function storeTeamMember(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'position' => 'required|string|max:100',
            'department' => 'nullable|string|max:100',
            'bio' => 'nullable|string',
            'email' => 'nullable|email|max:100',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'nullable|in:active,inactive',
            'join_date' => 'nullable|date'
        ]);

        try {
            $teamMemberData = [
                'name' => $request->name,
                'slug' => \Illuminate\Support\Str::slug($request->name),
                'position' => $request->position,
                'department' => $request->department,
                'bio' => $request->bio,
                'email' => $request->email,
                'phone' => $request->phone,
                'linkedin_url' => $request->linkedin_url,
                'twitter_url' => $request->twitter_url,
                'sort_order' => $request->sort_order ?? 0,
                'is_active' => $request->status === 'active' || $request->status === null, // default to active
                'join_date' => $request->join_date
            ];

            // Handle photo upload
            if ($request->hasFile('photo')) {
                $path = $request->file('photo')->store('team', 'public');
                $teamMemberData['photo'] = $path;
            }

            $teamMember = \App\Models\TeamMember::create($teamMemberData);

            return redirect()->route('admin.team.index')
                ->with('success', 'Team member "' . $teamMember->name . '" created successfully!');
        } catch (\Exception $e) {
            \Log::error('Team member creation failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create team member: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Update team member
     */
    public function updateTeamMember(Request $request, $id)
    {
        $teamMember = \App\Models\TeamMember::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:100',
            'position' => 'required|string|max:100',
            'department' => 'nullable|string|max:100',
            'bio' => 'nullable|string',
            'email' => 'nullable|email|max:100',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'nullable|in:active,inactive',
            'join_date' => 'nullable|date'
        ]);

        try {
            $updateData = [
                'name' => $request->name,
                'slug' => \Illuminate\Support\Str::slug($request->name),
                'position' => $request->position,
                'department' => $request->department,
                'bio' => $request->bio,
                'email' => $request->email,
                'phone' => $request->phone,
                'linkedin_url' => $request->linkedin_url,
                'twitter_url' => $request->twitter_url,
                'sort_order' => $request->sort_order ?? 0,
                'is_active' => $request->status === 'active' || $request->status === null,
                'join_date' => $request->join_date
            ];

            // Handle photo upload
            if ($request->hasFile('photo')) {
                // Delete old photo if exists
                if ($teamMember->photo && \Storage::disk('public')->exists($teamMember->photo)) {
                    \Storage::disk('public')->delete($teamMember->photo);
                }
                
                $path = $request->file('photo')->store('team', 'public');
                $updateData['photo'] = $path;
            }

            $teamMember->update($updateData);

            return redirect()->route('admin.team.show', $id)
                ->with('success', 'Team member updated successfully!');
        } catch (\Exception $e) {
            \Log::error('Team member update failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update team member: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete team member
     */
    public function destroyTeamMember($id)
    {
        try {
            $teamMember = \App\Models\TeamMember::findOrFail($id);
            $memberName = $teamMember->name;
            
            // Delete associated photo
            if ($teamMember->photo && \Storage::disk('public')->exists($teamMember->photo)) {
                \Storage::disk('public')->delete($teamMember->photo);
            }
            
            $teamMember->delete();
            
            return redirect()->route('admin.team.index')
                ->with('success', 'Team member "' . $memberName . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.team.index')
                ->with('error', 'Failed to delete team member: ' . $e->getMessage());
        }
    }

    /**
     * Show analytics
     */
    public function analytics()
    {
        return view('admin.analytics.index');
    }

    /**
     * Show reports
     */
    public function reports()
    {
        return view('admin.analytics.reports');
    }

    /**
     * Show API documentation
     */
    public function apiDocs()
    {
        $endpoints = [
            'Authentication' => [
                'POST /api/v1/login' => 'User login',
                'POST /api/v1/register' => 'User registration',
                'POST /api/v1/logout' => 'User logout',
                'GET /api/v1/user' => 'Get authenticated user',
            ],
            'Users' => [
                'GET /api/v1/admin/users' => 'List all users',
                'GET /api/v1/admin/users/{id}' => 'Get user details',
                'PUT /api/v1/admin/users/{id}' => 'Update user',
                'DELETE /api/v1/admin/users/{id}' => 'Delete user',
            ],
            'Programs' => [
                'GET /api/v1/programs' => 'List programs',
                'GET /api/v1/programs/featured' => 'Get featured programs',
                'GET /api/v1/programs/stats' => 'Get program statistics',
                'GET /api/v1/programs/{slug}' => 'Get program details',
            ],
            'Events' => [
                'GET /api/v1/events' => 'List events',
                'GET /api/v1/events/{id}' => 'Get event details',
                'POST /api/v1/events/{id}/register' => 'Register for event',
                'DELETE /api/v1/events/{id}/unregister' => 'Cancel registration',
            ],
            'Blog' => [
                'GET /api/v1/blog/posts' => 'List blog posts',
                'GET /api/v1/blog/posts/{slug}' => 'Get blog post',
                'GET /api/v1/blog/categories' => 'Get categories',
                'POST /api/v1/blog/posts/{id}/comments' => 'Add comment',
            ],
            'Donations' => [
                'POST /api/v1/donations' => 'Create donation',
                'GET /api/v1/donations/campaigns' => 'List campaigns',
                'POST /api/v1/donations/payment/callback' => 'Payment callback',
            ],
            'Volunteers' => [
                'POST /api/v1/volunteer/apply' => 'Submit application',
                'GET /api/v1/volunteer/opportunities' => 'List opportunities',
                'POST /api/v1/volunteer/hours' => 'Log hours',
            ],
            'Scholarships' => [
                'GET /api/v1/scholarships' => 'List scholarships',
                'GET /api/v1/scholarships/{id}' => 'Get scholarship details',
                'POST /api/v1/scholarships/{id}/apply' => 'Apply for scholarship',
            ],
            'Contact' => [
                'POST /api/v1/contact' => 'Submit contact form',
                'GET /api/v1/contact/faq' => 'Get FAQ items',
            ],
        ];

        return view('admin.api-docs', compact('endpoints'));
    }

    /**
     * Make authenticated API call
     */
    private function apiCall($method, $endpoint, $data = [])
    {
        $token = Session::get('admin_token');
        
        // Use localhost instead of 127.0.0.1 for better DNS resolution
        $apiUrl = str_replace('127.0.0.1', 'localhost', $this->apiBaseUrl);
        
        $response = Http::timeout(5)
            ->connectTimeout(3)
            ->retry(2, 1000)
            ->withToken($token)
            ->{$method}($apiUrl . $endpoint, $data);
        
        if ($response->status() === 401) {
            Session::forget(['admin_user', 'admin_token']);
            return redirect()->route('admin.login')->with('error', 'Session expired. Please login again.');
        }
        
        return $response;
    }

    // ========================================
    // BLOG MANAGEMENT METHODS
    // ========================================

    /**
     * Show create blog post form
     */
    public function createBlogPost()
    {
        $categories = BlogCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
            
        return view('admin.blog.posts.create', compact('categories'));
    }

    /**
     * Store blog post
     */
    public function storeBlogPost(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category_id' => 'nullable|exists:blog_categories,id',
            'status' => 'required|in:draft,published,archived',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tags' => 'nullable|string',
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160'
        ]);

        try {
            $postData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'content' => $request->content,
                'excerpt' => $request->excerpt,
                'category_id' => $request->category_id,
                'status' => $request->status,
                'author_id' => session('admin_user.id', 1),
                'seo_title' => $request->seo_title,
                'seo_description' => $request->seo_description,
                'seo_keywords' => $request->tags ? json_encode(explode(',', $request->tags)) : null,
                'published_at' => $request->status === 'published' ? now() : null
            ];

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                $path = $request->file('featured_image')->store('blog', 'public');
                $postData['featured_image'] = $path;
            }

            $post = BlogPost::create($postData);

            return redirect()->route('admin.blog.index')
                ->with('success', 'Blog post "' . $post->title . '" created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create blog post: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show edit blog post form
     */
    public function editBlogPost($id)
    {
        $post = BlogPost::with(['author', 'category'])->findOrFail($id);
        $categories = BlogCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
            
        return view('admin.blog.posts.edit', compact('post', 'categories', 'id'));
    }

    /**
     * Update blog post
     */
    public function updateBlogPost(Request $request, $id)
    {
        $post = BlogPost::findOrFail($id);
        
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category_id' => 'nullable|exists:blog_categories,id',
            'status' => 'required|in:draft,published,archived',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tags' => 'nullable|string',
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160'
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'content' => $request->content,
                'excerpt' => $request->excerpt,
                'category_id' => $request->category_id,
                'status' => $request->status,
                'seo_title' => $request->seo_title,
                'seo_description' => $request->seo_description,
                'seo_keywords' => $request->tags ? json_encode(explode(',', $request->tags)) : null
            ];

            // Update published_at if changing to published
            if ($request->status === 'published' && $post->status !== 'published') {
                $updateData['published_at'] = now();
            }

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                // Delete old image if exists
                if ($post->featured_image && \Storage::disk('public')->exists($post->featured_image)) {
                    \Storage::disk('public')->delete($post->featured_image);
                }
                
                $path = $request->file('featured_image')->store('blog', 'public');
                $updateData['featured_image'] = $path;
            }

            $post->update($updateData);

            return redirect()->route('admin.blog.index')
                ->with('success', 'Blog post updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update blog post: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete blog post
     */
    public function destroyBlogPost($id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            $postTitle = $post->title;
            
            // Delete associated image
            if ($post->featured_image && \Storage::disk('public')->exists($post->featured_image)) {
                \Storage::disk('public')->delete($post->featured_image);
            }
            
            $post->delete();
            
            return redirect()->route('admin.blog.index')
                ->with('success', 'Blog post "' . $postTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.blog.index')
                ->with('error', 'Failed to delete blog post: ' . $e->getMessage());
        }
    }

    // ========================================
    // PROGRAM MANAGEMENT METHODS
    // ========================================

    /**
     * Store program
     */
    public function storeProgram(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'objectives' => 'nullable|string',
            'target_beneficiaries' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,active,completed,suspended',
            'location_address' => 'nullable|string|max:255',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $programData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'objectives' => $request->objectives ? json_encode(explode("\n", $request->objectives)) : null,
                'target_beneficiaries' => $request->target_beneficiaries,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'budget' => $request->budget,
                'status' => $request->status,
                'location' => $request->location_address ? json_encode(['address' => $request->location_address]) : null,
                'created_by' => session('admin_user.id', 1)
            ];

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                $path = $request->file('featured_image')->store('programs', 'public');
                $programData['featured_image'] = $path;
            }

            $program = Program::create($programData);

            return redirect()->route('admin.programs.index')
                ->with('success', 'Program "' . $program->title . '" created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create program: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Update program
     */
    public function updateProgram(Request $request, $id)
    {
        $program = Program::findOrFail($id);
        
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'objectives' => 'nullable|string',
            'target_beneficiaries' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,active,completed,suspended',
            'location_address' => 'nullable|string|max:255',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'objectives' => $request->objectives ? json_encode(explode("\n", $request->objectives)) : null,
                'target_beneficiaries' => $request->target_beneficiaries,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'budget' => $request->budget,
                'status' => $request->status,
                'location' => $request->location_address ? json_encode(['address' => $request->location_address]) : null
            ];

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                // Delete old image if exists
                if ($program->featured_image && \Storage::disk('public')->exists($program->featured_image)) {
                    \Storage::disk('public')->delete($program->featured_image);
                }
                
                $path = $request->file('featured_image')->store('programs', 'public');
                $updateData['featured_image'] = $path;
            }

            $program->update($updateData);

            return redirect()->route('admin.programs.show', $id)
                ->with('success', 'Program updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update program: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete program
     */
    public function destroyProgram($id)
    {
        try {
            $program = Program::findOrFail($id);
            $programTitle = $program->title;
            
            // Delete associated image
            if ($program->featured_image && \Storage::disk('public')->exists($program->featured_image)) {
                \Storage::disk('public')->delete($program->featured_image);
            }
            
            $program->delete();
            
            return redirect()->route('admin.programs.index')
                ->with('success', 'Program "' . $programTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.programs.index')
                ->with('error', 'Failed to delete program: ' . $e->getMessage());
        }
    }

    // ========================================
    // EVENT MANAGEMENT METHODS
    // ========================================

    /**
     * Store event
     */
    public function storeEvent(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'event_type' => 'required|in:fundraising,volunteer,awareness,training,other',
            'start_datetime' => 'required|date|after:now',
            'end_datetime' => 'required|date|after:start_datetime',
            'venue' => 'nullable|string|max:200',
            'address' => 'nullable|string',
            'capacity' => 'nullable|integer|min:1',
            'registration_deadline' => 'nullable|date|before:start_datetime',
            'requirements' => 'nullable|string',
            'agenda' => 'nullable|string',
            'status' => 'required|in:draft,published,cancelled',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $eventData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'event_type' => $request->event_type,
                'start_datetime' => $request->start_datetime,
                'end_datetime' => $request->end_datetime,
                'venue' => $request->venue,
                'address' => $request->address,
                'capacity' => $request->capacity,
                'registration_deadline' => $request->registration_deadline,
                'requirements' => $request->requirements ? json_encode(explode("\n", $request->requirements)) : null,
                'agenda' => $request->agenda ? json_encode(explode("\n", $request->agenda)) : null,
                'status' => $request->status,
                'organizer_id' => session('admin_user.id', 1)
            ];

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                $path = $request->file('featured_image')->store('events', 'public');
                $eventData['featured_image'] = $path;
            }

            $event = Event::create($eventData);

            return redirect()->route('admin.events.index')
                ->with('success', 'Event "' . $event->title . '" created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create event: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show edit event form
     */
    public function editEvent($id)
    {
        $event = Event::with(['organizer', 'registrations.user'])->findOrFail($id);
        return view('admin.events.edit', compact('event'));
    }

    /**
     * Update event
     */
    public function updateEvent(Request $request, $id)
    {
        $event = Event::findOrFail($id);
        
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'event_type' => 'required|in:fundraising,volunteer,awareness,training,other',
            'start_datetime' => 'required|date',
            'end_datetime' => 'required|date|after:start_datetime',
            'venue' => 'nullable|string|max:200',
            'address' => 'nullable|string',
            'capacity' => 'nullable|integer|min:1',
            'registration_deadline' => 'nullable|date|before:start_datetime',
            'requirements' => 'nullable|string',
            'agenda' => 'nullable|string',
            'status' => 'required|in:draft,published,cancelled',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'event_type' => $request->event_type,
                'start_datetime' => $request->start_datetime,
                'end_datetime' => $request->end_datetime,
                'venue' => $request->venue,
                'address' => $request->address,
                'capacity' => $request->capacity,
                'registration_deadline' => $request->registration_deadline,
                'requirements' => $request->requirements ? json_encode(explode("\n", $request->requirements)) : null,
                'agenda' => $request->agenda ? json_encode(explode("\n", $request->agenda)) : null,
                'status' => $request->status
            ];

            // Handle image upload
            if ($request->hasFile('featured_image')) {
                // Delete old image if exists
                if ($event->featured_image && \Storage::disk('public')->exists($event->featured_image)) {
                    \Storage::disk('public')->delete($event->featured_image);
                }
                
                $path = $request->file('featured_image')->store('events', 'public');
                $updateData['featured_image'] = $path;
            }

            $event->update($updateData);

            return redirect()->route('admin.events.show', $id)
                ->with('success', 'Event updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update event: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete event
     */
    public function destroyEvent($id)
    {
        try {
            $event = Event::findOrFail($id);
            $eventTitle = $event->title;
            
            // Delete associated image
            if ($event->featured_image && \Storage::disk('public')->exists($event->featured_image)) {
                \Storage::disk('public')->delete($event->featured_image);
            }
            
            $event->delete();
            
            return redirect()->route('admin.events.index')
                ->with('success', 'Event "' . $eventTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.events.index')
                ->with('error', 'Failed to delete event: ' . $e->getMessage());
        }
    }

    // ========================================
    // VOLUNTEER MANAGEMENT METHODS
    // ========================================

    /**
     * Show create volunteer form
     */
    public function createVolunteer()
    {
        return view('admin.volunteers.create');
    }

    /**
     * Store volunteer
     */
    public function storeVolunteer(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'skills' => 'nullable|string',
            'interests' => 'nullable|string',
            'experience' => 'nullable|string',
            'motivation' => 'nullable|string',
            'availability_weekdays' => 'nullable|array',
            'availability_weekends' => 'nullable|boolean',
            'emergency_contact_name' => 'nullable|string|max:100',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relationship' => 'nullable|string|max:50'
        ]);

        try {
            $volunteerData = [
                'user_id' => $request->user_id,
                'application_status' => 'approved', // Auto-approve admin created volunteers
                'skills' => $request->skills ? json_encode(explode(',', $request->skills)) : null,
                'interests' => $request->interests ? json_encode(explode(',', $request->interests)) : null,
                'experience' => $request->experience,
                'motivation' => $request->motivation,
                'availability' => json_encode([
                    'weekdays' => $request->availability_weekdays ?? [],
                    'weekends' => $request->availability_weekends ?? false
                ]),
                'emergency_contact' => json_encode([
                    'name' => $request->emergency_contact_name,
                    'phone' => $request->emergency_contact_phone,
                    'relationship' => $request->emergency_contact_relationship
                ]),
                'approved_at' => now()
            ];

            $volunteer = Volunteer::create($volunteerData);

            return redirect()->route('admin.volunteers.index')
                ->with('success', 'Volunteer created successfully! ID: ' . $volunteer->id);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create volunteer: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show volunteer opportunities
     */
    public function volunteerOpportunities()
    {
        $opportunities = \App\Models\VolunteerOpportunity::with(['creator', 'applications'])
            ->latest()
            ->paginate(20);

        $stats = [
            'total' => \App\Models\VolunteerOpportunity::count(),
            'active' => \App\Models\VolunteerOpportunity::where('is_active', true)->count(),
            'featured' => \App\Models\VolunteerOpportunity::where('is_featured', true)->count(),
            'open_applications' => \App\Models\VolunteerOpportunity::where('application_deadline', '>', now())->count(),
        ];

        return view('admin.volunteers.opportunities', compact('opportunities', 'stats'));
    }

    /**
     * Show create volunteer opportunity form
     */
    public function createVolunteerOpportunity()
    {
        return view('admin.volunteers.opportunities.create');
    }

    /**
     * Store volunteer opportunity
     */
    public function storeVolunteerOpportunity(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|in:education,health,environment,community,events,administration,fundraising',
            'required_skills' => 'nullable|array',
            'time_commitment' => 'required|string|in:one-time,weekly,monthly,flexible,ongoing',
            'location' => 'nullable|string|max:255',
            'application_deadline' => 'required|date|after:today',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'max_volunteers' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ]);

        try {
            $opportunityData = $request->all();
            $opportunityData['created_by'] = auth()->id();
            $opportunityData['is_active'] = $request->has('is_active');
            $opportunityData['is_featured'] = $request->has('is_featured');

            $opportunity = \App\Models\VolunteerOpportunity::create($opportunityData);

            return redirect()->route('admin.volunteers.opportunities.index')
                ->with('success', 'Volunteer opportunity "' . $opportunity->title . '" created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create volunteer opportunity: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show volunteer opportunity details
     */
    public function showVolunteerOpportunity($id)
    {
        $opportunity = \App\Models\VolunteerOpportunity::with(['creator', 'applications.user'])->findOrFail($id);
        return view('admin.volunteers.opportunities.show', compact('opportunity'));
    }

    /**
     * Show edit volunteer opportunity form
     */
    public function editVolunteerOpportunity($id)
    {
        $opportunity = \App\Models\VolunteerOpportunity::findOrFail($id);
        return view('admin.volunteers.opportunities.edit', compact('opportunity'));
    }

    /**
     * Update volunteer opportunity
     */
    public function updateVolunteerOpportunity(Request $request, $id)
    {
        $opportunity = \App\Models\VolunteerOpportunity::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|in:education,health,environment,community,events,administration,fundraising',
            'required_skills' => 'nullable|array',
            'time_commitment' => 'required|string|in:one-time,weekly,monthly,flexible,ongoing',
            'location' => 'nullable|string|max:255',
            'application_deadline' => 'required|date',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'max_volunteers' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ]);

        try {
            $updateData = $request->all();
            $updateData['is_active'] = $request->has('is_active');
            $updateData['is_featured'] = $request->has('is_featured');

            $opportunity->update($updateData);

            return redirect()->route('admin.volunteers.opportunities.show', $id)
                ->with('success', 'Volunteer opportunity updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update volunteer opportunity: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete volunteer opportunity
     */
    public function destroyVolunteerOpportunity($id)
    {
        try {
            $opportunity = \App\Models\VolunteerOpportunity::findOrFail($id);
            $opportunityTitle = $opportunity->title;

            $opportunity->delete();

            return redirect()->route('admin.volunteers.opportunities.index')
                ->with('success', 'Volunteer opportunity "' . $opportunityTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.volunteers.opportunities.index')
                ->with('error', 'Failed to delete volunteer opportunity: ' . $e->getMessage());
        }
    }

    /**
     * Show edit volunteer form
     */
    public function editVolunteer($id)
    {
        return view('admin.volunteers.edit', ['id' => $id]);
    }

    /**
     * Update volunteer
     */
    public function updateVolunteer(Request $request, $id)
    {
        $volunteer = Volunteer::findOrFail($id);
        
        $request->validate([
            'application_status' => 'required|in:pending,approved,rejected',
            'skills' => 'nullable|string',
            'interests' => 'nullable|string',
            'experience' => 'nullable|string',
            'motivation' => 'nullable|string',
            'hours_logged' => 'nullable|numeric|min:0',
            'emergency_contact_name' => 'nullable|string|max:100',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relationship' => 'nullable|string|max:50'
        ]);

        try {
            $updateData = [
                'application_status' => $request->application_status,
                'skills' => $request->skills ? json_encode(explode(',', $request->skills)) : null,
                'interests' => $request->interests ? json_encode(explode(',', $request->interests)) : null,
                'experience' => $request->experience,
                'motivation' => $request->motivation,
                'hours_logged' => $request->hours_logged ?? $volunteer->hours_logged,
                'emergency_contact' => json_encode([
                    'name' => $request->emergency_contact_name,
                    'phone' => $request->emergency_contact_phone,
                    'relationship' => $request->emergency_contact_relationship
                ])
            ];

            // Set approved_at timestamp if changing to approved
            if ($request->application_status === 'approved' && $volunteer->application_status !== 'approved') {
                $updateData['approved_at'] = now();
            }

            $volunteer->update($updateData);

            return redirect()->route('admin.volunteers.show', $id)
                ->with('success', 'Volunteer updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update volunteer: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete volunteer
     */
    public function destroyVolunteer($id)
    {
        try {
            $volunteer = Volunteer::findOrFail($id);
            $volunteer->delete();
            
            return redirect()->route('admin.volunteers.index')
                ->with('success', 'Volunteer record deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.volunteers.index')
                ->with('error', 'Failed to delete volunteer: ' . $e->getMessage());
        }
    }

    // ========================================
    // DONATION MANAGEMENT METHODS
    // ========================================

    /**
     * Show create donation form
     */
    public function createDonation()
    {
        return view('admin.donations.create');
    }

    /**
     * Store donation
     */
    public function storeDonation(Request $request)
    {
        return redirect()->route('admin.donations.index')->with('success', 'Donation created successfully (placeholder).');
    }

    /**
     * Show create donation campaign form
     */
    public function createDonationCampaign()
    {
        return view('admin.donations.campaigns.create');
    }

    /**
     * Store donation campaign
     */
    public function storeDonationCampaign(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'goal_amount' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:100',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'status' => 'nullable|in:draft,active,paused,completed',
            'featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'action' => 'nullable|in:draft,publish'
        ]);

        try {
            // Determine status based on action or explicit status
            $status = 'draft'; // default
            if ($request->action === 'publish') {
                $status = 'active';
            } elseif ($request->action === 'draft') {
                $status = 'draft';
            } elseif ($request->status) {
                $status = $request->status;
            }

            $campaignData = [
                'title' => $request->title,
                'slug' => \Illuminate\Support\Str::slug($request->title),
                'description' => $request->description,
                'goal_amount' => $request->goal_amount,
                'raised_amount' => 0.00,
                'category' => $request->category,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => $status,
                'featured' => $request->has('featured'),
                'created_by' => auth()->id()
            ];

            // Handle image upload
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('campaigns', 'public');
                $campaignData['image'] = $path;
            }

            $campaign = \App\Models\DonationCampaign::create($campaignData);

            $message = $status === 'active'
                ? 'Donation campaign "' . $campaign->title . '" created and published successfully!'
                : 'Donation campaign "' . $campaign->title . '" saved as draft successfully!';

            return redirect()->route('admin.donations.campaigns')
                ->with('success', $message);
        } catch (\Exception $e) {
            \Log::error('Donation campaign creation failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to create donation campaign: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show donation campaign details
     */
    public function showDonationCampaign($id)
    {
        $campaign = \App\Models\DonationCampaign::with(['creator', 'donations.user'])->findOrFail($id);
        return view('admin.donations.campaigns.show', compact('campaign'));
    }

    /**
     * Show edit donation campaign form
     */
    public function editDonationCampaign($id)
    {
        $campaign = \App\Models\DonationCampaign::findOrFail($id);
        return view('admin.donations.campaigns.edit', compact('campaign'));
    }

    /**
     * Update donation campaign
     */
    public function updateDonationCampaign(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'goal_amount' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:100',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'status' => 'required|in:draft,active,paused,completed',
            'featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            $campaign = \App\Models\DonationCampaign::findOrFail($id);

            $campaignData = [
                'title' => $request->title,
                'slug' => \Illuminate\Support\Str::slug($request->title),
                'description' => $request->description,
                'goal_amount' => $request->goal_amount,
                'category' => $request->category,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => $request->status,
                'featured' => $request->has('featured')
            ];

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($campaign->image && \Storage::disk('public')->exists($campaign->image)) {
                    \Storage::disk('public')->delete($campaign->image);
                }
                $path = $request->file('image')->store('campaigns', 'public');
                $campaignData['image'] = $path;
            }

            $campaign->update($campaignData);

            return redirect()->route('admin.donations.campaigns.show', $campaign->id)
                ->with('success', 'Donation campaign "' . $campaign->title . '" updated successfully!');
        } catch (\Exception $e) {
            \Log::error('Donation campaign update failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to update donation campaign: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete donation campaign
     */
    public function destroyDonationCampaign($id)
    {
        try {
            $campaign = \App\Models\DonationCampaign::findOrFail($id);
            $campaignTitle = $campaign->title;

            // Delete associated image
            if ($campaign->image && \Storage::disk('public')->exists($campaign->image)) {
                \Storage::disk('public')->delete($campaign->image);
            }

            $campaign->delete();

            return redirect()->route('admin.donations.campaigns')
                ->with('success', 'Donation campaign "' . $campaignTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.donations.campaigns')
                ->with('error', 'Failed to delete donation campaign: ' . $e->getMessage());
        }
    }

    // ========================================
    // SCHOLARSHIP MANAGEMENT METHODS
    // ========================================

    /**
     * Show create scholarship form
     */
    public function createScholarship()
    {
        return view('admin.scholarships.create');
    }

    /**
     * Store scholarship
     */
    public function storeScholarship(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'eligibility_criteria' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'category' => 'required|in:primary,secondary,university',
            'application_deadline' => 'required|date|after:today',
            'application_start_date' => 'nullable|date|after_or_equal:today',
            'contact_email' => 'nullable|email',
            'required_documents' => 'nullable|string',
            'max_applicants' => 'nullable|integer|min:1',
            'action' => 'required|in:draft,publish',
            'is_featured' => 'nullable|boolean',
            'is_open' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'custom_fields' => 'nullable|array',
            'custom_fields.*.field_name' => 'required_with:custom_fields|string|max:100',
            'custom_fields.*.field_label' => 'required_with:custom_fields|string|max:200',
            'custom_fields.*.field_type' => 'required_with:custom_fields|in:text,textarea,email,tel,number,date,file,select',
            'custom_fields.*.is_required' => 'nullable|boolean',
            'custom_fields.*.display_order' => 'nullable|integer',
            'custom_fields.*.help_text' => 'nullable|string|max:500'
        ]);

        try {
            // Handle image upload
            $imagePath = null;
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('scholarships', 'public');
            }

            // Determine final status based on action
            $finalStatus = $request->input('action') === 'publish' ? 'active' : 'draft';
            if ($request->status === 'inactive') {
                $finalStatus = 'inactive';
            }

            $scholarshipData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'eligibility_criteria' => $request->eligibility_criteria,
                'amount' => $request->amount,
                'category' => $request->category,
                'application_deadline' => $request->application_deadline,
                'application_start_date' => $request->application_start_date,
                'contact_email' => $request->contact_email,
                'documents_required' => $request->required_documents ? explode("\n", $request->required_documents) : null,
                'max_applicants' => $request->max_applicants,
                'status' => $finalStatus,
                'is_featured' => $request->has('is_featured'),
                'is_open' => $request->has('is_open'),
                'image' => $imagePath,
                'created_by' => auth()->id()
            ];

            $scholarship = Scholarship::create($scholarshipData);

            // Handle custom fields
            if ($request->has('custom_fields') && is_array($request->custom_fields)) {
                foreach ($request->custom_fields as $fieldData) {
                    if (!empty($fieldData['field_name']) && !empty($fieldData['field_label'])) {
                        $scholarship->customFields()->create([
                            'field_name' => $fieldData['field_name'],
                            'field_label' => $fieldData['field_label'],
                            'field_type' => $fieldData['field_type'] ?? 'text',
                            'is_required' => isset($fieldData['is_required']),
                            'display_order' => $fieldData['display_order'] ?? 0,
                            'help_text' => $fieldData['help_text'] ?? null,
                            'field_options' => null // Can be extended for select fields
                        ]);
                    }
                }
            }

            $message = $finalStatus === 'active' ?
                'Scholarship "' . $scholarship->title . '" created and published successfully!' :
                'Scholarship "' . $scholarship->title . '" saved as draft successfully!';

            return redirect()->route('admin.scholarships.index')->with('success', $message);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create scholarship: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show edit scholarship form
     */
    public function editScholarship($id)
    {
        $scholarship = Scholarship::findOrFail($id);
        return view('admin.scholarships.edit', compact('scholarship'));
    }

    /**
     * Update scholarship
     */
    public function updateScholarship(Request $request, $id)
    {
        $scholarship = Scholarship::findOrFail($id);
        
        $request->validate([
            'title' => 'required|string|max:200',
            'description' => 'required|string',
            'eligibility_criteria' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'application_deadline' => 'required|date',
            'requirements' => 'nullable|string',
            'max_applicants' => 'nullable|integer|min:1',
            'status' => 'required|in:draft,open,closed'
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'eligibility_criteria' => $request->eligibility_criteria,
                'amount' => $request->amount,
                'application_deadline' => $request->application_deadline,
                'requirements' => $request->requirements ? json_encode(explode("\n", $request->requirements)) : null,
                'max_applicants' => $request->max_applicants,
                'status' => $request->status
            ];

            $scholarship->update($updateData);

            return redirect()->route('admin.scholarships.show', $id)
                ->with('success', 'Scholarship updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update scholarship: ' . $e->getMessage()])
                ->withInput();
        }
    }



    // ========================================
    // CONTACT MANAGEMENT METHODS
    // ========================================

    /**
     * Show contact message details
     */
    public function showContactMessage($id)
    {
        $message = \App\Models\ContactMessage::findOrFail($id);
        return view('admin.contact.show', compact('message'));
    }

    /**
     * Update contact message
     */
    public function updateContactMessage(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:new,in_progress,resolved',
            'priority' => 'required|in:low,medium,high',
            'assigned_to' => 'nullable|exists:users,id',
            'admin_notes' => 'nullable|string'
        ]);

        try {
            $message = \App\Models\ContactMessage::findOrFail($id);
            
            $message->update([
                'status' => $request->status,
                'priority' => $request->priority,
                'assigned_to' => $request->assigned_to,
                'admin_notes' => $request->admin_notes
            ]);

            return redirect()->route('admin.contact.show', $id)
                ->with('success', 'Contact message updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update message: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete contact message
     */
    public function destroyContactMessage($id)
    {
        try {
            $message = \App\Models\ContactMessage::findOrFail($id);
            $message->delete();
            
            return redirect()->route('admin.contact.index')
                ->with('success', 'Contact message deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.contact.index')
                ->with('error', 'Failed to delete message: ' . $e->getMessage());
        }
    }

    // ========================================
    // NEWSLETTER MANAGEMENT METHODS
    // ========================================

    /**
     * Show create newsletter campaign form
     */
    public function createNewsletterCampaign()
    {
        return view('admin.newsletter.campaigns.create');
    }

    /**
     * Store newsletter campaign
     */
    public function storeNewsletterCampaign(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'preview_text' => 'nullable|string|max:150',
            'recipient_type' => 'required|in:all,active,volunteers,donors',
            'estimated_recipients' => 'required|integer|min:0',
            'status' => 'required|in:draft,scheduled,sent',
            'scheduled_at' => 'nullable|date|after:now',
            'from_name' => 'required|string|max:100',
            'from_email' => 'required|email|max:100',
            'template' => 'required|string|in:default,newsletter,announcement'
        ]);

        try {
            $campaignData = [
                'title' => $request->title,
                'subject' => $request->subject,
                'content' => $request->content,
                'preview_text' => $request->preview_text,
                'recipient_type' => $request->recipient_type,
                'estimated_recipients' => $request->estimated_recipients,
                'status' => $request->status,
                'scheduled_at' => $request->scheduled_at,
                'from_name' => $request->from_name,
                'from_email' => $request->from_email,
                'template' => $request->template,
                'created_by' => auth()->id()
            ];

            // If sending immediately, set sent_at
            if ($request->status === 'sent') {
                $campaignData['sent_at'] = now();
                $campaignData['actual_recipients'] = $request->estimated_recipients;
            }

            $campaign = \App\Models\NewsletterCampaign::create($campaignData);

            $message = $request->status === 'sent'
                ? 'Newsletter campaign sent successfully!'
                : 'Newsletter campaign "' . $campaign->title . '" created successfully!';

            return redirect()->route('admin.newsletter.campaigns')
                ->with('success', $message);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create newsletter campaign: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show edit newsletter campaign form
     */
    public function editNewsletterCampaign($id)
    {
        $campaign = \App\Models\NewsletterCampaign::findOrFail($id);
        return view('admin.newsletter.campaigns.edit', compact('campaign'));
    }

    /**
     * Update newsletter campaign
     */
    public function updateNewsletterCampaign(Request $request, $id)
    {
        $campaign = \App\Models\NewsletterCampaign::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'preview_text' => 'nullable|string|max:150',
            'recipient_type' => 'required|in:all,active,volunteers,donors',
            'estimated_recipients' => 'required|integer|min:0',
            'status' => 'required|in:draft,scheduled,sent',
            'scheduled_at' => 'nullable|date|after:now',
            'from_name' => 'required|string|max:100',
            'from_email' => 'required|email|max:100',
            'template' => 'required|string|in:default,newsletter,announcement'
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'subject' => $request->subject,
                'content' => $request->content,
                'preview_text' => $request->preview_text,
                'recipient_type' => $request->recipient_type,
                'estimated_recipients' => $request->estimated_recipients,
                'status' => $request->status,
                'scheduled_at' => $request->scheduled_at,
                'from_name' => $request->from_name,
                'from_email' => $request->from_email,
                'template' => $request->template
            ];

            // If sending immediately, set sent_at
            if ($request->status === 'sent' && $campaign->status !== 'sent') {
                $updateData['sent_at'] = now();
                $updateData['actual_recipients'] = $request->estimated_recipients;
            }

            $campaign->update($updateData);

            $message = $request->status === 'sent'
                ? 'Newsletter campaign sent successfully!'
                : 'Newsletter campaign updated successfully!';

            return redirect()->route('admin.newsletter.campaigns')
                ->with('success', $message);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update newsletter campaign: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Delete newsletter campaign
     */
    public function destroyNewsletterCampaign($id)
    {
        try {
            $campaign = \App\Models\NewsletterCampaign::findOrFail($id);
            $campaignTitle = $campaign->title;

            $campaign->delete();

            return redirect()->route('admin.newsletter.campaigns')
                ->with('success', 'Newsletter campaign "' . $campaignTitle . '" deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.newsletter.campaigns')
                ->with('error', 'Failed to delete newsletter campaign: ' . $e->getMessage());
        }
    }



    /**
     * Import newsletter subscribers
     */
    public function importSubscribers(Request $request)
    {
        // In a real app, handle CSV/Excel import of subscribers
        return redirect()->route('admin.newsletter.subscribers')->with('success', 'Subscribers imported successfully (placeholder).');
    }

    /**
     * Export newsletter subscribers
     */
    public function exportSubscribers()
    {
        // In a real app, export subscribers to CSV/Excel
        return redirect()->route('admin.newsletter.subscribers')->with('success', 'Subscribers exported successfully (placeholder).');
    }


    // ========================================
    // WEB-BASED ADMIN API METHODS
    // ========================================

    /**
     * Get blog dashboard data for admin panel
     */
    public function getBlogDashboard()
    {
        try {
            $stats = [
                'total_posts' => BlogPost::count(),
                'published_posts' => BlogPost::where('status', 'published')->count(),
                'draft_posts' => BlogPost::where('status', 'draft')->count(),
                'total_views' => BlogPost::sum('views_count'),
                'total_categories' => BlogCategory::count(),
                'total_comments' => BlogComment::count()
            ];

            $recentPosts = BlogPost::with(['author', 'category'])
                                  ->orderBy('created_at', 'desc')
                                  ->limit(5)
                                  ->get()
                                  ->map(function($post) {
                                      return [
                                          'id' => $post->id,
                                          'title' => $post->title,
                                          'slug' => $post->slug,
                                          'status' => $post->status,
                                          'author' => $post->author ? [
                                              'id' => $post->author->id,
                                              'first_name' => $post->author->first_name,
                                              'last_name' => $post->author->last_name,
                                              'name' => $post->author->full_name
                                          ] : ['id' => null, 'first_name' => 'N/A', 'last_name' => '', 'name' => 'N/A'],
                                          'category' => $post->category ? $post->category->name : 'Uncategorized',
                                          'views_count' => $post->views_count,
                                          'created_at' => $post->created_at->format('Y-m-d H:i:s')
                                      ];
                                  });

            $categories = BlogCategory::withCount('posts')->get()->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'posts_count' => $category->posts_count,
                    'color' => $category->color
                ];
            });

            $recentComments = BlogComment::with(['user', 'post'])
                                       ->orderBy('created_at', 'desc')
                                       ->limit(5)
                                       ->get()
                                       ->map(function($comment) {
                                           return [
                                               'id' => $comment->id,
                                               'content' => $comment->content,
                                               'user' => $comment->user ? [
                                                   'id' => $comment->user->id,
                                                   'first_name' => $comment->user->first_name,
                                                   'last_name' => $comment->user->last_name,
                                                   'name' => $comment->user->full_name
                                               ] : ['id' => null, 'first_name' => $comment->author_name ?? 'Anonymous', 'last_name' => '', 'name' => $comment->author_name ?? 'Anonymous'],
                                               'post' => $comment->post ? [
                                                   'id' => $comment->post->id,
                                                   'title' => $comment->post->title
                                               ] : ['id' => null, 'title' => 'N/A'],
                                               'status' => $comment->status,
                                               'created_at' => $comment->created_at->format('Y-m-d H:i:s')
                                           ];
                                       });

            return response()->json([
                'data' => [
                    'stats' => $stats,
                    'recent_posts' => $recentPosts,
                    'categories' => $categories,
                    'recent_comments' => $recentComments
                ],
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load blog dashboard: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get blog posts for admin panel
     */
    public function getBlogPosts(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 10);
            $status = $request->get('status');
            $category = $request->get('category');
            $search = $request->get('search');

            $query = BlogPost::with(['author', 'category']);

            // Filter by status
            if ($status) {
                $query->where('status', $status);
            }

            // Filter by category
            if ($category) {
                $query->whereHas('category', function($q) use ($category) {
                    $q->where('slug', $category);
                });
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%")
                      ->orWhere('excerpt', 'like', "%{$search}%");
                });
            }

            $posts = $query->orderBy('created_at', 'desc')->paginate($perPage);

            // Calculate stats
            $stats = [
                'total_posts' => BlogPost::count(),
                'published_posts' => BlogPost::where('status', 'published')->count(),
                'draft_posts' => BlogPost::where('status', 'draft')->count(),
                'total_views' => BlogPost::sum('views_count')
            ];

            return response()->json([
                'data' => [
                    'posts' => $posts->map(function($post) {
                        return [
                            'id' => $post->id,
                            'title' => $post->title,
                            'slug' => $post->slug,
                            'excerpt' => $post->excerpt,
                            'featured_image_url' => $post->featured_image_url,
                            'status' => $post->status,
                            'views' => $post->views_count,
                            'author' => $post->author ? [
                                'id' => $post->author->id,
                                'name' => $post->author->full_name,
                                'avatar' => $post->author->profile_picture_url
                            ] : ['id' => null, 'name' => 'N/A', 'avatar' => 'https://ui-avatars.com/api/?name=N/A&color=fff&background=16a34a'],
                            'category' => $post->category ? [
                                'id' => $post->category->id,
                                'name' => $post->category->name,
                                'slug' => $post->category->slug
                            ] : ['id' => null, 'name' => 'Uncategorized', 'slug' => 'uncategorized'],
                            'published_at' => $post->published_at ? $post->published_at->format('Y-m-d H:i:s') : null,
                            'created_at' => $post->created_at->format('Y-m-d H:i:s')
                        ];
                    }),
                    'pagination' => [
                        'current_page' => $posts->currentPage(),
                        'per_page' => $posts->perPage(),
                        'total' => $posts->total(),
                        'last_page' => $posts->lastPage()
                    ],
                    'stats' => $stats
                ],
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load posts: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get blog categories for admin panel
     */
    public function getBlogCategories()
    {
        try {
            $categories = BlogCategory::withCount('posts')
                                    ->orderBy('sort_order')
                                    ->orderBy('name')
                                    ->get()
                                    ->map(function($category) {
                                        return [
                                            'id' => $category->id,
                                            'name' => $category->name,
                                            'slug' => $category->slug,
                                            'description' => $category->description,
                                            'color' => $category->color,
                                            'posts_count' => $category->posts_count,
                                            'is_active' => $category->is_active,
                                            'sort_order' => $category->sort_order
                                        ];
                                    });

            return response()->json([
                'data' => $categories,
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load categories: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get blog comments for admin panel
     */
    public function getBlogComments(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $search = $request->get('search');

            $query = BlogComment::with(['user', 'post']);

            // Filter by status
            if ($status) {
                $query->where('status', $status);
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('content', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('first_name', 'like', "%{$search}%")
                                    ->orWhere('last_name', 'like', "%{$search}%")
                                    ->orWhere('email', 'like', "%{$search}%");
                      })
                      ->orWhereHas('post', function($postQuery) use ($search) {
                          $postQuery->where('title', 'like', "%{$search}%");
                      });
                });
            }

            $comments = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'data' => [
                    'comments' => $comments->map(function($comment) {
                        return [
                            'id' => $comment->id,
                            'content' => $comment->content,
                            'status' => $comment->status,
                            'user' => $comment->user ? [
                                'id' => $comment->user->id,
                                'name' => $comment->user->full_name,
                                'email' => $comment->user->email,
                                'avatar' => $comment->user->profile_picture_url
                            ] : [
                                'id' => null,
                                'name' => $comment->author_name ?? 'Anonymous',
                                'email' => $comment->author_email ?? 'N/A',
                                'avatar' => 'https://ui-avatars.com/api/?name=' . urlencode($comment->author_name ?? 'A') . '&color=fff&background=16a34a'
                            ],
                            'post' => $comment->post ? [
                                'id' => $comment->post->id,
                                'title' => $comment->post->title,
                                'slug' => $comment->post->slug
                            ] : ['id' => null, 'title' => 'N/A', 'slug' => 'n-a'],
                            'created_at' => $comment->created_at->format('Y-m-d H:i:s'),
                            'updated_at' => $comment->updated_at->format('Y-m-d H:i:s')
                        ];
                    }),
                    'pagination' => [
                        'current_page' => $comments->currentPage(),
                        'per_page' => $comments->perPage(),
                        'total' => $comments->total(),
                        'last_page' => $comments->lastPage()
                    ]
                ],
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load comments: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete blog post
     */
    public function deleteBlogPost($id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            $title = $post->title;

            // Delete associated image
            if ($post->featured_image && \Storage::disk('public')->exists($post->featured_image)) {
                \Storage::disk('public')->delete($post->featured_image);
            }

            // Delete associated comments
            $post->comments()->delete();

            $post->delete();

            return response()->json([
                'message' => "Post '{$title}' deleted successfully",
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete post: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Toggle blog post status
     */
    public function toggleBlogPostStatus(Request $request, $id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            $newStatus = $request->input('status');

            // Validate newStatus if necessary (e.g., ensure it's one of the allowed statuses)
            $allowedStatuses = ['published', 'draft', 'pending', 'archived'];
            if (!in_array($newStatus, $allowedStatuses)) {
                return response()->json(['message' => 'Invalid status provided.', 'success' => false], 400);
            }
            
            $oldStatus = $post->status;
            $post->status = $newStatus;
            if ($newStatus === 'published' && $oldStatus !== 'published') {
                $post->published_at = now();
            } elseif ($newStatus !== 'published') {
                // Optionally clear published_at if moving away from published, or keep as is
                // $post->published_at = null; 
            }
            $post->save();

            return response()->json([
                'data' => [
                    'id' => $post->id,
                    'status' => $post->status,
                    'published_at' => $post->published_at ? $post->published_at->format('Y-m-d H:i:s') : null
                ],
                'message' => "Post status updated to {$newStatus} successfully",
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to toggle post status: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    // Methods for Blog Comments will be added here later

    // Methods for Blog Categories
    public function storeBlogCategory(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:blog_categories,name',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7', // Hex color
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer',
        ]);

        try {
            $category = BlogCategory::create([
                'name' => $validatedData['name'],
                'slug' => Str::slug($validatedData['name']),
                'description' => $validatedData['description'] ?? null,
                'color' => $validatedData['color'] ?? '#6c757d',
                'is_active' => $request->input('is_active', true), // Ensure boolean from request
                'sort_order' => $validatedData['sort_order'] ?? 0,
            ]);

            return response()->json([
                'data' => $category->loadCount('posts'), // Return with posts_count
                'message' => 'Category created successfully',
                'success' => true
            ], 201);
        } catch (\Exception $e) {
            Log::error('Category creation failed: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json([
                'message' => 'Failed to create category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    public function updateBlogCategory(Request $request, $id)
    {
        $category = BlogCategory::findOrFail($id);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:blog_categories,name,' . $id,
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer',
        ]);

        try {
            $category->update([
                'name' => $validatedData['name'],
                'slug' => Str::slug($validatedData['name']),
                'description' => $validatedData['description'] ?? $category->description,
                'color' => $validatedData['color'] ?? $category->color,
                'is_active' => $request->input('is_active', $category->is_active), // Ensure boolean
                'sort_order' => $validatedData['sort_order'] ?? $category->sort_order,
            ]);

            return response()->json([
                'data' => $category->loadCount('posts'), // Return with posts_count
                'message' => 'Category updated successfully',
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error("Category update failed for id {$id}: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json([
                'message' => 'Failed to update category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    public function deleteBlogCategory($id)
    {
        try {
            $category = BlogCategory::findOrFail($id);
            
            if ($category->posts()->count() > 0) {
                 return response()->json([
                    'message' => 'Cannot delete category: It has ' . $category->posts()->count() . ' associated posts. Please reassign posts first.',
                    'success' => false
                ], 400); // Bad Request
            }
            
            $categoryName = $category->name;
            $category->delete();

            return response()->json([
                'message' => "Category '{$categoryName}' deleted successfully",
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error("Category delete failed for id {$id}: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json([
                'message' => 'Failed to delete category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    // Methods for Blog Comments
    public function updateBlogCommentStatus(Request $request, $id)
    {
        $comment = BlogComment::findOrFail($id);
        $validatedData = $request->validate([
            'status' => 'required|string|in:pending,approved,rejected,spam',
        ]);

        try {
            $comment->status = $validatedData['status'];
            $comment->save();

            return response()->json([
                'data' => $comment,
                'message' => 'Comment status updated successfully to ' . $validatedData['status'] . '.',
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error("Comment status update failed for id {$id}: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json([
                'message' => 'Failed to update comment status: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    public function deleteBlogComment($id)
    {
        try {
            $comment = BlogComment::findOrFail($id);
            $commentContent = Str::limit($comment->content, 30);
            $comment->delete();

            return response()->json([
                'message' => "Comment '{$commentContent}...' deleted successfully",
                'success' => true
            ]);
        } catch (\Exception $e) {
            Log::error("Comment delete failed for id {$id}: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json([
                'message' => 'Failed to delete comment: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    // Server-side form handling methods for CRUD operations

    /**
     * Handle category creation form submission
     */
    public function storeBlogCategoryForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|max:7',
            'is_active' => 'nullable|boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please fix the validation errors.');
        }

        try {
            $slug = Str::slug($request->name);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (BlogCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            BlogCategory::create([
                'name' => $request->name,
                'slug' => $slug,
                'description' => $request->description,
                'color' => $request->color ?? '#6c757d',
                'is_active' => $request->boolean('is_active'),
                'sort_order' => $request->sort_order ?? 0
            ]);

            return redirect()->route('admin.blog.categories')
                ->with('success', 'Category created successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create category: ' . $e->getMessage());
        }
    }

    /**
     * Handle category update form submission
     */
    public function updateBlogCategoryForm(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|max:7',
            'is_active' => 'nullable|boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please fix the validation errors.');
        }

        try {
            $category = BlogCategory::findOrFail($id);

            $slug = Str::slug($request->name);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug (excluding current category)
            while (BlogCategory::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $category->update([
                'name' => $request->name,
                'slug' => $slug,
                'description' => $request->description,
                'color' => $request->color ?? '#6c757d',
                'is_active' => $request->boolean('is_active'),
                'sort_order' => $request->sort_order ?? 0
            ]);

            return redirect()->route('admin.blog.categories')
                ->with('success', 'Category updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update category: ' . $e->getMessage());
        }
    }

    /**
     * Handle category deletion form submission
     */
    public function destroyBlogCategoryForm($id)
    {
        try {
            $category = BlogCategory::findOrFail($id);
            
            // Check if category has posts
            if ($category->posts()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Cannot delete category with existing posts. Please move posts to another category first.');
            }

            $category->delete();

            return redirect()->route('admin.blog.categories')
                ->with('success', 'Category deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete category: ' . $e->getMessage());
        }
    }

    /**
     * Handle comment status update form submission
     */
    public function updateBlogCommentStatusForm(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected,spam'
        ]);

        try {
            $comment = BlogComment::findOrFail($id);
            $comment->update(['status' => $request->status]);

            return redirect()->back()
                ->with('success', 'Comment status updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update comment status: ' . $e->getMessage());
        }
    }

    /**
     * Handle comment deletion form submission
     */
    public function destroyBlogCommentForm($id)
    {
        try {
            $comment = BlogComment::findOrFail($id);
            $comment->delete();

            return redirect()->back()
                ->with('success', 'Comment deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete comment: ' . $e->getMessage());
        }
    }

    /**
     * Handle blog post deletion form submission
     */
    public function destroyBlogPostForm($id)
    {
        try {
            $post = BlogPost::findOrFail($id);
            
            // Delete featured image if exists
            if ($post->featured_image && Storage::exists('public/' . $post->featured_image)) {
                Storage::delete('public/' . $post->featured_image);
            }

            $post->delete();

            return redirect()->route('admin.blog.posts')
                ->with('success', 'Blog post deleted successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete blog post: ' . $e->getMessage());
        }
    }

    /**
     * Handle blog post status toggle form submission
     */
    public function toggleBlogPostStatusForm(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:draft,published,archived'
        ]);

        try {
            $post = BlogPost::findOrFail($id);
            $post->update([
                'status' => $request->status,
                'published_at' => $request->status === 'published' ? now() : null
            ]);

            return redirect()->back()
                ->with('success', 'Post status updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update post status: ' . $e->getMessage());
        }
    }
} 
