/**
 * Application Constants
 * Centralized configuration for the NGO platform frontend
 */

// =============================================================================
// API CONFIGURATION
// =============================================================================

/**
 * Base API URL for the Laravel backend
 * Change this URL to switch between development, staging, and production
 */
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

/**
 * Laravel backend base URL (without /api/v1)
 * Used for authentication redirects and file uploads
 */
export const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';

/**
 * API endpoints configuration
 */
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/login',
    REGISTER: '/register',
    LOGOUT: '/logout',
    PROFILE: '/profile',
    REFRESH: '/refresh-token',
  },
  
  // Public endpoints
  PUBLIC: {
    SCHOLARSHIPS: '/public-scholarships',
    BLOG_POSTS: '/blog/posts',
    EVENTS: '/events',
    PROGRAMS: '/programs',
    TEAM: '/team',
    SETTINGS: '/settings',
    CONTACT: '/contact',
    NEWSLETTER: '/newsletter/subscribe',
    DONATIONS: '/donations',
    DONATION_CAMPAIGNS: '/donations/campaigns',
    DONATION_RECEIPT_UPLOAD: '/donations/{id}/upload-receipt',
    DONATION_VERIFY: '/donations/verify/{reference}',
  },
  
  // Protected endpoints
  PROTECTED: {
    DASHBOARD: '/dashboard',
    APPLICATIONS: '/scholarship-applications',
    MY_DONATIONS: '/donations/my-donations',
    PROFILE: '/profile',
  }
} as const;

// =============================================================================
// APPLICATION INFORMATION
// =============================================================================

/**
 * Application name and branding
 */
export const APP_CONFIG = {
  NAME: 'HALIMAKQ Foundation',
  SHORT_NAME: 'HALIMAKQ',
  TAGLINE: 'Empowering Communities Through Education',
  DESCRIPTION: 'A non-governmental organization dedicated to providing educational opportunities and community development programs.',
  VERSION: '1.0.0',
} as const;

/**
 * Contact information
 */
export const CONTACT_INFO = {
  EMAIL: '<EMAIL>',
  PHONE: '+234 ************',
  ADDRESS: 'Lagos, Nigeria',
  WEBSITE: 'https://halimakqfoundation.org',
} as const;

/**
 * Social media links
 */
export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/halimakqfoundation',
  TWITTER: 'https://twitter.com/halimakqfoundation',
  INSTAGRAM: 'https://instagram.com/halimakqfoundation',
  LINKEDIN: 'https://linkedin.com/company/halimakqfoundation',
  YOUTUBE: 'https://youtube.com/@halimakqfoundation',
} as const;

// =============================================================================
// UI CONFIGURATION
// =============================================================================

/**
 * Theme and styling constants
 */
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: '#059669', // Green-600
    PRIMARY_DARK: '#047857', // Green-700
    SECONDARY: '#3B82F6', // Blue-500
    ACCENT: '#8B5CF6', // Purple-500
    SUCCESS: '#10B981', // Emerald-500
    WARNING: '#F59E0B', // Amber-500
    ERROR: '#EF4444', // Red-500
  },
  GRADIENTS: {
    PRIMARY: 'from-blue-50 via-indigo-50 to-purple-50',
    HEADER: 'from-blue-600 via-indigo-600 to-purple-600',
    ADMIN: 'from-green-600 to-green-700',
  }
} as const;

/**
 * Layout configuration
 */
export const LAYOUT_CONFIG = {
  CONTAINER_MAX_WIDTH: 'max-w-7xl',
  SECTION_PADDING: 'py-16',
  CARD_SHADOW: 'shadow-lg',
  BORDER_RADIUS: 'rounded-lg',
} as const;

// =============================================================================
// BUSINESS LOGIC CONSTANTS
// =============================================================================

/**
 * Scholarship categories
 */
export const SCHOLARSHIP_CATEGORIES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  UNIVERSITY: 'university',
} as const;

/**
 * User roles
 */
export const USER_ROLES = {
  ADMIN: 'admin',
  STUDENT: 'student',
  PARTNER: 'partner',
  NORMAL: 'normal',
} as const;

/**
 * Application statuses
 */
export const APPLICATION_STATUS = {
  PENDING: 'pending',
  UNDER_REVIEW: 'under_review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  COMPLETED: 'completed',
} as const;

/**
 * Payment methods
 */
export const PAYMENT_METHODS = {
  PAYSTACK: 'paystack',
  BANK_TRANSFER: 'bank_transfer',
} as const;

/**
 * Donation types
 */
export const DONATION_TYPES = {
  ONE_TIME: 'one-time',
  RECURRING: 'recurring',
} as const;

// =============================================================================
// VALIDATION CONSTANTS
// =============================================================================

/**
 * Form validation rules
 */
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^(\+234|0)[789][01]\d{8}$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  MESSAGE_MAX_LENGTH: 1000,
  // Donation validation
  DONATION_MIN_AMOUNT: 15000,
  DONATION_MAX_AMOUNT: ********, // 10 million
} as const;

/**
 * File upload constraints
 */
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp', '.pdf'],
} as const;

// =============================================================================
// PAGINATION AND LIMITS
// =============================================================================

/**
 * Pagination configuration
 */
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50,
  BLOG_POSTS_PER_PAGE: 6,
  SCHOLARSHIPS_PER_PAGE: 9,
  EVENTS_PER_PAGE: 8,
} as const;

// =============================================================================
// CACHE AND PERFORMANCE
// =============================================================================

/**
 * Cache configuration
 */
export const CACHE_CONFIG = {
  DEFAULT_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_CACHE_TIME: 10 * 60 * 1000, // 10 minutes
  STATIC_DATA_CACHE_TIME: 60 * 60 * 1000, // 1 hour
} as const;

// =============================================================================
// ENVIRONMENT HELPERS
// =============================================================================

/**
 * Environment detection
 */
export const ENV = {
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_TEST: process.env.NODE_ENV === 'test',
} as const;

/**
 * Feature flags
 */
export const FEATURES = {
  ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  ENABLE_CHAT_SUPPORT: process.env.NEXT_PUBLIC_ENABLE_CHAT === 'true',
  ENABLE_NOTIFICATIONS: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === 'true',
} as const;

// =============================================================================
// EXPORT ALL CONSTANTS
// =============================================================================

/**
 * Default export with all constants grouped
 */
const CONSTANTS = {
  API_BASE_URL,
  BACKEND_BASE_URL,
  API_ENDPOINTS,
  APP_CONFIG,
  CONTACT_INFO,
  SOCIAL_LINKS,
  THEME_CONFIG,
  LAYOUT_CONFIG,
  SCHOLARSHIP_CATEGORIES,
  USER_ROLES,
  APPLICATION_STATUS,
  PAYMENT_METHODS,
  DONATION_TYPES,
  VALIDATION_RULES,
  FILE_UPLOAD,
  PAGINATION,
  CACHE_CONFIG,
  ENV,
  FEATURES,
} as const;

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Format currency amount in Nigerian Naira
 */
export const formatCurrency = (amount: number): string => {
  return `₦${amount.toLocaleString('en-NG')}`
}

/**
 * Validate donation amount
 */
export const validateDonationAmount = (amount: number): { isValid: boolean; error?: string } => {
  if (!amount || amount < VALIDATION_RULES.DONATION_MIN_AMOUNT) {
    return {
      isValid: false,
      error: `Minimum donation amount is ${formatCurrency(VALIDATION_RULES.DONATION_MIN_AMOUNT)}`
    }
  }

  if (amount > VALIDATION_RULES.DONATION_MAX_AMOUNT) {
    return {
      isValid: false,
      error: `Maximum donation amount is ${formatCurrency(VALIDATION_RULES.DONATION_MAX_AMOUNT)}`
    }
  }

  return { isValid: true }
}

export default CONSTANTS;
