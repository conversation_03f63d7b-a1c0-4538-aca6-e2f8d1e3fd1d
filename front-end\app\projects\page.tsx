"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Users, Heart, Target, TrendingUp, Award, CheckCircle, Calendar, DollarSign, Loader2 } from "lucide-react"

// TypeScript interfaces for API data
interface ScholarshipStatistics {
  approved_by_category: {
    primary: number
    secondary: number
    university: number
  }
  totals: {
    total_approved: number
    total_scholarships: number
    active_scholarships: number
    total_amount_awarded: string
  }
  impact_metrics: {
    primary_students_helped: number
    secondary_students_helped: number
    university_students_helped: number
    total_students_helped: number
  }
}

interface PortfolioCategory {
  category: string
  title: string
  description: string
  approved_count: number
  active_count: number
  total_amount: string
}

interface ScholarshipProject {
  id: number
  title: string
  description: string
  category: string
  amount: string
  application_deadline: string
  status: string
  approved_applications: number
  days_remaining?: number
}

interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

// API functions
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api/v1'

const fetchStatistics = async (): Promise<ScholarshipStatistics> => {
  const response = await fetch(`${API_BASE_URL}/project-scholarships/statistics`)
  const result: ApiResponse<ScholarshipStatistics> = await response.json()
  if (!result.success) throw new Error(result.message)
  return result.data
}

const fetchPortfolio = async (): Promise<PortfolioCategory[]> => {
  const response = await fetch(`${API_BASE_URL}/project-scholarships/portfolio`)
  const result: ApiResponse<PortfolioCategory[]> = await response.json()
  if (!result.success) throw new Error(result.message)
  return result.data
}

const fetchScholarshipsByStatus = async (status: string): Promise<ScholarshipProject[]> => {
  const response = await fetch(`${API_BASE_URL}/project-scholarships/by-status?status=${status}`)
  const result: ApiResponse<ScholarshipProject[]> = await response.json()
  if (!result.success) throw new Error(result.message)
  return result.data
}

export default function ProjectsPage() {
  const [statistics, setStatistics] = useState<ScholarshipStatistics | null>(null)
  const [portfolio, setPortfolio] = useState<PortfolioCategory[]>([])
  const [activeProjects, setActiveProjects] = useState<ScholarshipProject[]>([])
  const [completedProjects, setCompletedProjects] = useState<ScholarshipProject[]>([])
  const [plannedProjects, setPlannedProjects] = useState<ScholarshipProject[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [categoryScholarships, setCategoryScholarships] = useState<ScholarshipProject[]>([])
  const [showCategoryResults, setShowCategoryResults] = useState(false)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const [statsData, portfolioData, activeData, completedData, plannedData] = await Promise.all([
          fetchStatistics(),
          fetchPortfolio(),
          fetchScholarshipsByStatus('active'),
          fetchScholarshipsByStatus('completed'),
          fetchScholarshipsByStatus('planned')
        ])

        setStatistics(statsData)
        setPortfolio(portfolioData)
        setActiveProjects(activeData)
        setCompletedProjects(completedData)
        setPlannedProjects(plannedData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Handle category click to show scholarships for that category
  const handleCategoryClick = async (category: string) => {
    try {
      setLoading(true)
      setSelectedCategory(category)

      // Fetch scholarships for the selected category
      const response = await fetch(`${API_BASE_URL}/public-scholarships?category=${category}`)
      const result: ApiResponse<ScholarshipProject[]> = await response.json()

      if (result.success) {
        setCategoryScholarships(result.data || [])
        setShowCategoryResults(true)
      } else {
        setError(`Failed to load ${category} scholarships`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to load ${category} scholarships`)
    } finally {
      setLoading(false)
    }
  }

  // Handle back to main view
  const handleBackToMain = () => {
    setShowCategoryResults(false)
    setSelectedCategory(null)
    setCategoryScholarships([])
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-500"
      case "completed":
        return "bg-blue-500"
      case "planned":
        return "bg-amber-500"
      default:
        return "bg-gray-500"
    }
  }

  const getAllProjects = () => [...activeProjects, ...completedProjects, ...plannedProjects]

  const getProjectsByStatus = (status: string) => {
    switch (status) {
      case 'active': return activeProjects
      case 'completed': return completedProjects
      case 'planned': return plannedProjects
      default: return getAllProjects()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">Loading projects...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <Button onClick={() => window.location.reload()} className="bg-green-600 hover:bg-green-700">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">

      <main>
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Our Projects</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                Transforming Lives Through Education
              </h1>
              <p className="text-xl text-green-100 max-w-3xl mx-auto">
                Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{statistics?.totals.active_scholarships || 0}</div>
                  <div className="text-green-200 text-sm">Active Scholarships</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{statistics?.totals.total_approved || 0}</div>
                  <div className="text-green-200 text-sm">Students Supported</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{statistics?.totals.total_amount_awarded || '₦0'}</div>
                  <div className="text-green-200 text-sm">Total Investment</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{portfolio.length}</div>
                  <div className="text-green-200 text-sm">Program Categories</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Portfolio Categories */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Scholarship Categories</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                Our comprehensive scholarship programs support students at every educational level
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {portfolio.map((category) => (
                <Card key={category.category} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="rounded-full capitalize">
                        {category.category}
                      </Badge>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">{category.approved_count} approved</div>
                        <div className="text-sm text-green-600">{category.active_count} active</div>
                      </div>
                    </div>
                    <CardTitle className="text-xl text-green-800 dark:text-green-200">{category.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600 dark:text-gray-400 text-sm">{category.description}</p>
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        Total: {category.total_amount}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-green-600 border-green-600 hover:bg-green-50"
                        onClick={() => handleCategoryClick(category.category)}
                      >
                        View Available Scholarships
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Category Results or Main View */}
        {showCategoryResults ? (
          <section className="py-20 bg-white dark:bg-gray-900">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between mb-8">
                <div>
                  <Button
                    variant="outline"
                    onClick={handleBackToMain}
                    className="mb-4"
                  >
                    ← Back to Categories
                  </Button>
                  <h2 className="text-3xl font-bold tracking-tight mb-2">
                    {selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : ''} Scholarships
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Available scholarships for {selectedCategory || 'selected'} education level
                  </p>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                </div>
              ) : categoryScholarships.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryScholarships.map((scholarship: any) => (
                    <Card key={scholarship.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-green-600 text-white rounded-full">
                            Active
                          </Badge>
                          <Badge variant="outline" className="rounded-full">
                            {scholarship.category?.charAt(0).toUpperCase() + scholarship.category?.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{scholarship.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{scholarship.description}</p>

                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2 text-green-600">
                            <DollarSign className="h-4 w-4" />
                            ₦{scholarship.amount?.toLocaleString() || 'N/A'}
                          </div>
                          <div className="flex items-center gap-2 text-gray-500">
                            <Calendar className="h-4 w-4" />
                            {scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'}
                          </div>
                        </div>

                        <Button
                          className="w-full bg-green-600 hover:bg-green-700"
                          onClick={() => {
                            const laravelBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
                            window.location.href = `${laravelBaseUrl}/login`
                          }}
                        >
                          Apply Now
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 dark:text-gray-400">No scholarships available for {selectedCategory} category at the moment.</p>
                </div>
              )}
            </div>
          </section>
        ) : (
          <section className="py-20 bg-white dark:bg-gray-900">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <Tabs defaultValue="all" className="space-y-8">
                <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                  <div>
                    <h2 className="text-3xl font-bold tracking-tight mb-2">Our Scholarship Portfolio</h2>
                    <p className="text-gray-600 dark:text-gray-400">
                      Comprehensive educational scholarships supporting students from primary to university level
                    </p>
                  </div>
                  <TabsList className="grid grid-cols-4 w-full md:w-auto">
                    <TabsTrigger value="all">All Projects</TabsTrigger>
                    <TabsTrigger value="active">Active</TabsTrigger>
                    <TabsTrigger value="completed">Completed</TabsTrigger>
                    <TabsTrigger value="planned">Planned</TabsTrigger>
                  </TabsList>
                </div>

              <TabsContent value="all" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getAllProjects().map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className={`${getStatusColor(project.status)} text-white rounded-full`}>
                            {project.status === 'active' ? 'Active' : project.status === 'completed' ? 'Completed' : 'Planned'}
                          </Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{project.description}</p>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Users className="h-4 w-4 text-green-600" />
                            {project.approved_applications} approved applications
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            Amount: {project.amount}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Calendar className="h-4 w-4 text-green-600" />
                            Deadline: {new Date(project.application_deadline).toLocaleDateString()}
                          </div>
                          {project.days_remaining && project.days_remaining > 0 && (
                            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                              <Target className="h-4 w-4 text-green-600" />
                              {Math.ceil(project.days_remaining)} days remaining
                            </div>
                          )}
                        </div>

                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Details</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="active" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getProjectsByStatus("active").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-green-500 text-white rounded-full">Active</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{project.description}</p>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Users className="h-4 w-4 text-green-600" />
                            {project.approved_applications} approved applications
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            Amount: {project.amount}
                          </div>
                          {project.days_remaining && project.days_remaining > 0 && (
                            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                              <Target className="h-4 w-4 text-green-600" />
                              {Math.ceil(project.days_remaining)} days remaining
                            </div>
                          )}
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Details</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="completed" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getProjectsByStatus("completed").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-blue-500 text-white rounded-full">Completed</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{project.description}</p>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Users className="h-4 w-4 text-green-600" />
                            {project.approved_applications} approved applications
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            Amount: {project.amount}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            Completed
                          </div>
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Results</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="planned" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getProjectsByStatus("planned").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-amber-500 text-white rounded-full">Planned</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{project.description}</p>
                        <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-green-600" />
                            Deadline: {new Date(project.application_deadline).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            Amount: {project.amount}
                          </div>
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">Learn More</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>
        )}

        {/* Project Impact */}
        <section className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Measuring Our Impact</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                Every project we undertake is designed to create measurable, lasting change in the communities we serve.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-emerald-100 dark:bg-emerald-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Total Students Helped</h3>
                  <p className="text-3xl font-bold text-emerald-600 mb-2">{statistics?.impact_metrics.total_students_helped || 0}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Across all education levels</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-indigo-100 dark:bg-indigo-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <DollarSign className="h-8 w-8 text-indigo-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Total Amount Awarded</h3>
                  <p className="text-3xl font-bold text-indigo-600 mb-2">{statistics?.totals.total_amount_awarded || '₦0'}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">In scholarship funding</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Primary Students</h3>
                  <p className="text-3xl font-bold text-green-600 mb-2">{statistics?.approved_by_category.primary || 0}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Approved scholarships</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Secondary Students</h3>
                  <p className="text-3xl font-bold text-blue-600 mb-2">{statistics?.approved_by_category.secondary || 0}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Approved scholarships</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="h-8 w-8 text-amber-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">University Students</h3>
                  <p className="text-3xl font-bold text-amber-600 mb-2">{statistics?.approved_by_category.university || 0}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Approved scholarships</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Active Programs</h3>
                  <p className="text-3xl font-bold text-purple-600 mb-2">{statistics?.totals.active_scholarships || 0}</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Currently accepting applications</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-green-900 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Support Our Projects</h2>
              <p className="text-green-100 text-lg">
                Your contribution can help us expand our reach and create even more educational opportunities for
                underprivileged students across Northern Nigeria.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold">
                  <Heart className="mr-2 h-5 w-5" />
                  Donate to Projects
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                  Become a Partner
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-green-950 dark:bg-black text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Building Tomorrow's Leaders</h3>
            <p className="text-green-200 max-w-2xl mx-auto">
              Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this
              transformative journey.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
