"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Heart, Calendar, DollarSign, Loader2 } from "lucide-react"
import { apiClient } from '@/lib/api'
import { BACKEND_BASE_URL, API_BASE_URL } from '@/lib/constants'

// TypeScript interfaces for API data

interface PortfolioCategory {
  category: string
  title: string
  description: string
  approved_count: number
  active_count: number
  total_amount: string
}

interface ScholarshipProject {
  id: number
  title: string
  description: string
  category: string
  amount: string
  application_deadline: string
  status: string
  approved_applications: number
  days_remaining?: number
}

interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

// API functions



const fetchPortfolio = async (): Promise<PortfolioCategory[]> => {
  const response = await fetch(`${API_BASE_URL}/project-scholarships/portfolio`)
  const result: ApiResponse<PortfolioCategory[]> = await response.json()
  if (!result.success) throw new Error(result.message)
  return result.data
}



export default function ProjectsPage() {
  const [portfolio, setPortfolio] = useState<PortfolioCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [categoryScholarships, setCategoryScholarships] = useState<ScholarshipProject[]>([])
  const [showCategoryResults, setShowCategoryResults] = useState(false)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const portfolioData = await fetchPortfolio()
        setPortfolio(portfolioData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Handle category click to show scholarships for that category
  const handleCategoryClick = async (category: string) => {
    try {
      setLoading(true)
      setSelectedCategory(category)

      // Fetch scholarships for the selected category using apiClient
      const response = await apiClient.getPublicScholarships({ category })

      if (response.success) {
        setCategoryScholarships(response.data?.data || [])
        setShowCategoryResults(true)
      } else {
        setError(`Failed to load ${category} scholarships`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to load ${category} scholarships`)
    } finally {
      setLoading(false)
    }
  }

  // Handle back to main view
  const handleBackToMain = () => {
    setShowCategoryResults(false)
    setSelectedCategory(null)
    setCategoryScholarships([])
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">Loading projects...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <Button onClick={() => window.location.reload()} className="bg-green-600 hover:bg-green-700">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">

      <main>
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Our Projects</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                Transforming Lives Through Education
              </h1>
              <p className="text-xl text-green-100 max-w-3xl mx-auto">
                Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.
              </p>

            </div>
          </div>
        </section>

        {/* Portfolio Categories */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Scholarship Categories</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                Our comprehensive scholarship programs support students at every educational level
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {portfolio.map((category) => (
                <Card key={category.category} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="rounded-full capitalize">
                        {category.category}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl text-green-800 dark:text-green-200">{category.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600 dark:text-gray-400 text-sm">{category.description}</p>
                    <div className="flex items-center justify-end pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-green-600 border-green-600 hover:bg-green-50"
                        onClick={() => handleCategoryClick(category.category)}
                      >
                        View Available Scholarships
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Category Results or Main View */}
        {showCategoryResults ? (
          <section className="py-20 bg-white dark:bg-gray-900">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between mb-8">
                <div>
                  <Button
                    variant="outline"
                    onClick={handleBackToMain}
                    className="mb-4"
                  >
                    ← Back to Categories
                  </Button>
                  <h2 className="text-3xl font-bold tracking-tight mb-2">
                    {selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : ''} Scholarships
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Available scholarships for {selectedCategory || 'selected'} education level
                  </p>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                </div>
              ) : categoryScholarships.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryScholarships.map((scholarship: any) => (
                    <Card key={scholarship.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-green-600 text-white rounded-full">
                            Active
                          </Badge>
                          <Badge variant="outline" className="rounded-full">
                            {scholarship.category?.charAt(0).toUpperCase() + scholarship.category?.slice(1)}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{scholarship.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{scholarship.description}</p>

                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2 text-green-600">
                            <DollarSign className="h-4 w-4" />
                            ₦{scholarship.amount?.toLocaleString() || 'N/A'}
                          </div>
                          <div className="flex items-center gap-2 text-gray-500">
                            <Calendar className="h-4 w-4" />
                            {scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'}
                          </div>
                        </div>

                        <Button
                          className="w-full bg-green-600 hover:bg-green-700"
                          onClick={() => {
                            window.location.href = `${BACKEND_BASE_URL}/login`
                          }}
                        >
                          Apply Now
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 dark:text-gray-400">No scholarships available for {selectedCategory} category at the moment.</p>
                </div>
              )}
            </div>
          </section>
        ) : null}



        {/* Call to Action */}
        <section className="py-20 bg-green-900 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Support Our Projects</h2>
              <p className="text-green-100 text-lg">
                Your contribution can help us expand our reach and create even more educational opportunities for
                underprivileged students across Northern Nigeria.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold">
                  <Heart className="mr-2 h-5 w-5" />
                  Donate to Projects
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                  Become a Partner
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-green-950 dark:bg-black text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Building Tomorrow's Leaders</h3>
            <p className="text-green-200 max-w-2xl mx-auto">
              Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this
              transformative journey.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
