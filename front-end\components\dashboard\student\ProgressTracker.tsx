'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  Clock, 
  Award, 
  TrendingUp, 
  Target, 
  Calendar,
  CheckCircle,
  Star,
  BarChart3
} from 'lucide-react';

interface DashboardStats {
  total_resources: number;
  accessed_resources: number;
  completed_resources: number;
  completion_rate: number;
  total_time_spent: number;
  average_progress: number;
  bookmarked_resources: number;
}

interface ProgressTrackerProps {
  stats: DashboardStats | null;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  earned: boolean;
  progress?: number;
  target?: number;
}

export default function ProgressTracker({ stats }: ProgressTrackerProps) {
  const [achievements, setAchievements] = useState<Achievement[]>([]);

  useEffect(() => {
    if (stats) {
      generateAchievements();
    }
  }, [stats]);

  const generateAchievements = () => {
    if (!stats) return;

    const achievementsList: Achievement[] = [
      {
        id: 'first_resource',
        title: 'Getting Started',
        description: 'Access your first educational resource',
        icon: <BookOpen className="h-6 w-6" />,
        earned: stats.accessed_resources > 0
      },
      {
        id: 'first_completion',
        title: 'First Completion',
        description: 'Complete your first resource',
        icon: <CheckCircle className="h-6 w-6" />,
        earned: stats.completed_resources > 0
      },
      {
        id: 'five_completions',
        title: 'Learning Enthusiast',
        description: 'Complete 5 resources',
        icon: <Star className="h-6 w-6" />,
        earned: stats.completed_resources >= 5,
        progress: stats.completed_resources,
        target: 5
      },
      {
        id: 'ten_completions',
        title: 'Knowledge Seeker',
        description: 'Complete 10 resources',
        icon: <Award className="h-6 w-6" />,
        earned: stats.completed_resources >= 10,
        progress: stats.completed_resources,
        target: 10
      },
      {
        id: 'twenty_hours',
        title: 'Dedicated Learner',
        description: 'Spend 20 hours learning',
        icon: <Clock className="h-6 w-6" />,
        earned: (stats.total_time_spent / 60) >= 20,
        progress: Math.floor(stats.total_time_spent / 60),
        target: 20
      },
      {
        id: 'high_completion_rate',
        title: 'Perfectionist',
        description: 'Achieve 80% completion rate',
        icon: <Target className="h-6 w-6" />,
        earned: stats.completion_rate >= 80,
        progress: stats.completion_rate,
        target: 80
      }
    ];

    setAchievements(achievementsList);
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!stats) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading progress data...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Progress Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Resources Accessed</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.accessed_resources}</div>
                <p className="text-xs text-muted-foreground">
                  of {stats.total_resources} available
                </p>
                <Progress 
                  value={(stats.accessed_resources / stats.total_resources) * 100} 
                  className="mt-2" 
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.completed_resources}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.completion_rate}% completion rate
                </p>
                <Progress value={stats.completion_rate} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Time Spent</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatTime(stats.total_time_spent)}</div>
                <p className="text-xs text-muted-foreground">
                  Total learning time
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Progress</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getProgressColor(stats.average_progress)}`}>
                  {stats.average_progress}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all resources
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Learning Goals */}
          <Card>
            <CardHeader>
              <CardTitle>Learning Goals</CardTitle>
              <CardDescription>
                Track your progress towards learning milestones
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Complete 10 Resources</span>
                    <span className="text-sm text-gray-500">
                      {stats.completed_resources}/10
                    </span>
                  </div>
                  <Progress value={(stats.completed_resources / 10) * 100} />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Achieve 90% Completion Rate</span>
                    <span className="text-sm text-gray-500">
                      {stats.completion_rate}/90%
                    </span>
                  </div>
                  <Progress value={(stats.completion_rate / 90) * 100} />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Spend 50 Hours Learning</span>
                    <span className="text-sm text-gray-500">
                      {Math.floor(stats.total_time_spent / 60)}/50h
                    </span>
                  </div>
                  <Progress value={(stats.total_time_spent / 60 / 50) * 100} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest learning activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-3 border rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">Completed "Introduction to Programming"</p>
                    <p className="text-sm text-gray-500">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-3 border rounded-lg">
                  <BookOpen className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">Started "Advanced Mathematics"</p>
                    <p className="text-sm text-gray-500">1 day ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-3 border rounded-lg">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="font-medium">Bookmarked "Physics Fundamentals"</p>
                    <p className="text-sm text-gray-500">3 days ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="achievements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Achievements</CardTitle>
              <CardDescription>
                Unlock badges and milestones as you progress in your learning journey
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {achievements.map((achievement) => (
                  <div
                    key={achievement.id}
                    className={`p-4 border rounded-lg ${
                      achievement.earned 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className={`p-2 rounded-full ${
                        achievement.earned 
                          ? 'bg-green-100 text-green-600' 
                          : 'bg-gray-100 text-gray-400'
                      }`}>
                        {achievement.icon}
                      </div>
                      <div>
                        <h4 className="font-medium">{achievement.title}</h4>
                        <p className="text-sm text-gray-600">{achievement.description}</p>
                      </div>
                    </div>
                    
                    {achievement.target && !achievement.earned && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{achievement.progress}/{achievement.target}</span>
                        </div>
                        <Progress 
                          value={((achievement.progress || 0) / achievement.target) * 100} 
                        />
                      </div>
                    )}
                    
                    {achievement.earned && (
                      <Badge className="bg-green-100 text-green-800">
                        Earned
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Learning Analytics</CardTitle>
              <CardDescription>
                Detailed insights into your learning patterns and progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Learning Statistics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Total Resources Available:</span>
                      <span className="font-medium">{stats.total_resources}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Resources Accessed:</span>
                      <span className="font-medium">{stats.accessed_resources}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Resources Completed:</span>
                      <span className="font-medium text-green-600">{stats.completed_resources}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Bookmarked Resources:</span>
                      <span className="font-medium">{stats.bookmarked_resources}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Progress:</span>
                      <span className={`font-medium ${getProgressColor(stats.average_progress)}`}>
                        {stats.average_progress}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Time Analytics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Total Time Spent:</span>
                      <span className="font-medium">{formatTime(stats.total_time_spent)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average per Resource:</span>
                      <span className="font-medium">
                        {stats.accessed_resources > 0 
                          ? formatTime(Math.floor(stats.total_time_spent / stats.accessed_resources))
                          : '0m'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Completion Rate:</span>
                      <span className={`font-medium ${getProgressColor(stats.completion_rate)}`}>
                        {stats.completion_rate}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
