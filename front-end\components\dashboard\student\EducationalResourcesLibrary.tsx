'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BookOpen, 
  Play, 
  FileText, 
  Download, 
  Bookmark, 
  BookmarkCheck,
  Search,
  Filter,
  Clock,
  CheckCircle,
  Star,
  Eye
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface EducationalResource {
  id: number;
  title: string;
  description: string;
  type: string;
  category: string;
  file_path?: string;
  video_url?: string;
  duration?: number;
  difficulty_level: string;
  tags: string[];
  is_active: boolean;
  is_featured: boolean;
  view_count: number;
  download_count: number;
  user_progress: number;
  is_completed: boolean;
  is_bookmarked: boolean;
  file_url?: string;
  thumbnail_url?: string;
}

export default function EducationalResourcesLibrary() {
  const [resources, setResources] = useState<EducationalResource[]>([]);
  const [bookmarks, setBookmarks] = useState<EducationalResource[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [activeTab, setActiveTab] = useState('all');

  const categories = ['Technology', 'Mathematics', 'Science', 'Literature', 'History', 'Arts'];
  const types = ['video', 'pdf', 'document', 'interactive'];

  useEffect(() => {
    fetchResources();
    if (activeTab === 'bookmarks') {
      fetchBookmarks();
    }
  }, [activeTab, selectedCategory, selectedType, searchTerm]);

  const fetchResources = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedType !== 'all') params.append('type', selectedType);
      if (searchTerm) params.append('search', searchTerm);

      const response = await apiClient.get(`/educational-resources?${params.toString()}`);
      if (response.success) {
        setResources(response.data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch resources:', error);
      toast.error('Failed to load resources');
    } finally {
      setLoading(false);
    }
  };

  const fetchBookmarks = async () => {
    try {
      const response = await apiClient.get('/educational-resources/my-bookmarks');
      if (response.success) {
        setBookmarks(response.data.data?.map((item: any) => item.resource) || []);
      }
    } catch (error) {
      console.error('Failed to fetch bookmarks:', error);
    }
  };

  const handleBookmarkToggle = async (resourceId: number) => {
    try {
      const response = await apiClient.post(`/educational-resources/${resourceId}/bookmark`);
      if (response.success) {
        toast.success(response.data.is_bookmarked ? 'Added to bookmarks' : 'Removed from bookmarks');
        
        // Update the resource in the current list
        setResources(prev => prev.map(resource => 
          resource.id === resourceId 
            ? { ...resource, is_bookmarked: response.data.is_bookmarked }
            : resource
        ));

        // Refresh bookmarks if on bookmarks tab
        if (activeTab === 'bookmarks') {
          fetchBookmarks();
        }
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      toast.error('Failed to update bookmark');
    }
  };

  const handleProgressUpdate = async (resourceId: number, progress: number) => {
    try {
      const response = await apiClient.post(`/educational-resources/${resourceId}/progress`, {
        progress_percentage: progress,
        time_spent: 5 // Assume 5 minutes spent
      });
      
      if (response.success) {
        setResources(prev => prev.map(resource => 
          resource.id === resourceId 
            ? { 
                ...resource, 
                user_progress: progress,
                is_completed: progress >= 100
              }
            : resource
        ));
      }
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  };

  const handleDownload = async (resourceId: number) => {
    try {
      const response = await apiClient.get(`/educational-resources/${resourceId}/download`);
      if (response.success && response.data.download_url) {
        // Open download URL in new tab
        window.open(response.data.download_url, '_blank');
        toast.success('Download started');
      }
    } catch (error) {
      console.error('Failed to download resource:', error);
      toast.error('Failed to download resource');
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Play className="h-5 w-5" />;
      case 'pdf':
      case 'document':
        return <FileText className="h-5 w-5" />;
      default:
        return <BookOpen className="h-5 w-5" />;
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'Self-paced';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const currentResources = activeTab === 'bookmarks' ? bookmarks : resources;

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Educational Resources</CardTitle>
          <CardDescription>
            Access videos, documents, and interactive learning materials
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {types.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Resources</TabsTrigger>
          <TabsTrigger value="videos">Videos</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="bookmarks">Bookmarked</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading resources...</p>
            </div>
          ) : currentResources.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentResources.map((resource) => (
                <Card key={resource.id} className="group hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getResourceIcon(resource.type)}
                        <Badge className={getDifficultyColor(resource.difficulty_level)}>
                          {resource.difficulty_level}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleBookmarkToggle(resource.id)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        {resource.is_bookmarked ? (
                          <BookmarkCheck className="h-4 w-4 text-yellow-500" />
                        ) : (
                          <Bookmark className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{resource.title}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {resource.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{resource.category}</span>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-3 w-3" />
                        <span>{formatDuration(resource.duration)}</span>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Progress</span>
                        <span className="text-sm text-gray-500">{resource.user_progress}%</span>
                      </div>
                      <Progress value={resource.user_progress} className="h-2" />
                    </div>

                    {/* Tags */}
                    {resource.tags && resource.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {resource.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {resource.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{resource.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-3 w-3" />
                        <span>{resource.view_count} views</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Download className="h-3 w-3" />
                        <span>{resource.download_count} downloads</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <Button 
                        className="flex-1" 
                        size="sm"
                        onClick={() => {
                          // Handle resource access
                          if (resource.type === 'video' && resource.video_url) {
                            window.open(resource.video_url, '_blank');
                          } else if (resource.file_url) {
                            window.open(resource.file_url, '_blank');
                          }
                          // Update progress
                          const newProgress = Math.min(100, resource.user_progress + 25);
                          handleProgressUpdate(resource.id, newProgress);
                        }}
                      >
                        {resource.type === 'video' ? 'Watch' : 'View'}
                      </Button>
                      {resource.file_path && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDownload(resource.id)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    {/* Completion Badge */}
                    {resource.is_completed && (
                      <div className="flex items-center justify-center space-x-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">Completed</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {activeTab === 'bookmarks' 
                    ? 'No bookmarked resources yet' 
                    : 'No resources found'
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
