(()=>{var e={};e.id=384,e.ids=[384],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8045:(e,r,s)=>{Promise.resolve().then(s.bind(s,95395))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32085:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(60687),a=s(43210);s(59556);var o=s(40093);function l(){let[e,r]=(0,a.useState)(null),[s,l]=(0,a.useState)(!0),{settings:n,loading:i}=(0,o.t)();return s||i?(0,t.jsx)("div",{className:"p-8",children:"Loading..."}):(0,t.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-8",children:"Image Loading Test"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"border p-6 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Settings API Response"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(n,null,2)}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Logo Display:"}),n?.app_logo?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["URL: ",n.app_logo]}),(0,t.jsx)("img",{src:n.app_logo,alt:"Logo",className:"w-16 h-16 object-cover border",onLoad:()=>console.log("Logo loaded successfully"),onError:e=>{console.error("Logo failed to load:",n.app_logo),console.log("Error event:",e)}})]}):(0,t.jsx)("p",{className:"text-red-500",children:"No logo URL found"})]})]}),(0,t.jsxs)("div",{className:"border p-6 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"User API Response"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Avatar Display:"}),e?.profile_picture_url?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["URL: ",e.profile_picture_url]}),(0,t.jsx)("img",{src:e.profile_picture_url,alt:"Avatar",className:"w-16 h-16 object-cover border rounded-full",onLoad:()=>console.log("Avatar loaded successfully"),onError:r=>{console.error("Avatar failed to load:",e.profile_picture_url),console.log("Error event:",r)}})]}):(0,t.jsx)("p",{className:"text-red-500",children:"No avatar URL found"})]})]})]}),(0,t.jsxs)("div",{className:"border p-6 rounded-lg mt-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Direct API Tests"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Default Avatar (SVG):"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"URL: http://localhost:8000/api/v1/images/default-avatar.svg"}),(0,t.jsx)("img",{src:"http://localhost:8000/api/v1/images/default-avatar.svg",alt:"Default Avatar",className:"w-16 h-16 object-cover border rounded-full",onLoad:()=>console.log("Default avatar loaded successfully"),onError:e=>{console.error("Default avatar failed to load"),console.log("Error event:",e)}})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Test CORS:"}),(0,t.jsx)("button",{onClick:async()=>{try{let e=await fetch("http://localhost:8000/api/v1/settings"),r=await e.json();console.log("CORS test successful:",r),alert("CORS test successful - check console")}catch(e){console.error("CORS test failed:",e),alert("CORS test failed - check console")}},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Test CORS API Call"})]})]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},48073:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(65239),a=s(48088),o=s(88170),l=s.n(o),n=s(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(r,i);let d={children:["",{children:["test-images",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,95395)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-images\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-images\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-images/page",pathname:"/test-images",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71661:(e,r,s)=>{Promise.resolve().then(s.bind(s,32085))},95395:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-images\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-images\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[555,702],()=>s(48073));module.exports=t})();