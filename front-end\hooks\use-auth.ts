import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/lib/api';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  role: string;
  status: string;
  preferences?: any;
  profile_picture_url?: string;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  token: string | null;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setToken: (token: string | null) => void;
  login: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<any>;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      loading: true,
      token: null,
      setUser: (user) => set({ user }),
      setLoading: (loading) => set({ loading }),
      setToken: (token) => set({ token }),

      login: async (email: string, password: string) => {
        try {
          set({ loading: true });
          const response = await apiClient.login(email, password);

          if (response.success) {
            set({
              user: response.data.user,
              token: response.data.access_token,
              loading: false
            });
          }

          return response;
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          await apiClient.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({ user: null, token: null });
          if (typeof window !== 'undefined') {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
          }
        }
      },

      checkAuth: async () => {
        try {
          set({ loading: true });
          const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

          if (!token) {
            set({ user: null, token: null, loading: false });
            return;
          }

          const response = await apiClient.getUser();

          if (response.success) {
            set({
              user: response.data,
              token,
              loading: false
            });
          } else {
            set({ user: null, token: null, loading: false });
            if (typeof window !== 'undefined') {
              localStorage.removeItem('authToken');
              localStorage.removeItem('user');
            }
          }

          return response;
        } catch (error) {
          set({ user: null, token: null, loading: false });
          if (typeof window !== 'undefined') {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
          }
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token
      }),
    }
  )
);