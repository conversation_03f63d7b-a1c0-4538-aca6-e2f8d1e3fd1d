<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use Illuminate\Support\Facades\Auth;

class ScholarshipApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return false;
        }

        // Get scholarship from route parameter
        $scholarshipId = $this->route('id');
        $scholarship = Scholarship::find($scholarshipId);

        if (!$scholarship) {
            return false;
        }

        // Check if scholarship is active and open
        if ($scholarship->status !== 'active' || !$scholarship->is_open) {
            return false;
        }

        // Check if application deadline has passed
        if ($scholarship->application_deadline <= now()) {
            return false;
        }

        // Check if user already applied
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarshipId)
            ->where('user_id', Auth::id())
            ->exists();

        if ($existingApplication) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $scholarshipId = $this->route('id');
        $scholarship = Scholarship::with(['fields' => function($query) {
            $query->where('is_active', true);
        }])->find($scholarshipId);

        if (!$scholarship) {
            return [];
        }

        $rules = [];

        // Get category-specific base rules
        $categoryRules = $this->getCategorySpecificRules($scholarship->category);
        $rules = array_merge($rules, $categoryRules);

        // Add custom field validation rules
        foreach ($scholarship->fields as $field) {
            $fieldRules = $this->getFieldValidationRules($field);
            if (!empty($fieldRules)) {
                $rules[$field->field_name] = $fieldRules;
            }
        }

        return $rules;
    }

    /**
     * Get category-specific validation rules
     */
    private function getCategorySpecificRules(string $category): array
    {
        return match($category) {
            'primary' => [
                // Primary school specific rules (filled by parent/guardian)
                'student_name' => ['required', 'string', 'max:255'],
                'student_age' => ['required', 'integer', 'min:5', 'max:13'],
                'student_class' => ['required', 'string', 'max:50'],
                'parent_name' => ['required', 'string', 'max:255'],
                'parent_phone' => ['required', 'string', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'],
                'headmaster_name' => ['required', 'string', 'max:255'],
                'reason' => ['required', 'string', 'min:50', 'max:1000'],
                'student_photo' => ['required', 'file', 'mimes:jpeg,png,jpg', 'max:2048'],
                'school_fee' => ['required', 'numeric', 'min:0'],
                'school_name' => ['required', 'string', 'max:255'],
                'account_number' => ['required', 'string', 'max:20'],
                'bank_name' => ['required', 'string', 'max:100'],
                'account_name' => ['required', 'string', 'max:255'],
            ],
            'secondary' => [
                // Secondary school specific rules (filled by student)
                'student_name' => ['required', 'string', 'max:255'],
                'student_age' => ['required', 'integer', 'min:12', 'max:20'],
                'student_class' => ['required', 'string', 'max:50'],
                'parent_name' => ['required', 'string', 'max:255'],
                'parent_phone' => ['required', 'string', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'],
                'principal_name' => ['required', 'string', 'max:255'], // Principal instead of headmaster
                'financial_officer_name' => ['nullable', 'string', 'max:255'],
                'reason' => ['required', 'string', 'min:50', 'max:1000'],
                'student_photo' => ['required', 'file', 'mimes:jpeg,png,jpg', 'max:2048'],
                'school_fee' => ['required', 'numeric', 'min:0'],
                'school_name' => ['required', 'string', 'max:255'],
                'account_number' => ['required', 'string', 'max:20'],
                'bank_name' => ['required', 'string', 'max:100'],
                'account_name' => ['required', 'string', 'max:255'],
            ],
            'university' => [
                // University specific rules (filled by student, no account number)
                'student_name' => ['required', 'string', 'max:255'],
                'student_age' => ['required', 'integer', 'min:16', 'max:35'],
                'matric_number' => ['required', 'string', 'max:50'],
                'student_email' => ['required', 'email', 'max:255'],
                'student_phone' => ['required', 'string', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'],
                'student_id_card' => ['required', 'file', 'mimes:jpeg,png,jpg,pdf', 'max:2048'],
                'remita_payment' => ['required', 'file', 'mimes:jpeg,png,jpg,pdf', 'max:2048'],
                'reason' => ['required', 'string', 'min:50', 'max:1000'],
                'supporting_documents' => ['nullable', 'file', 'mimes:jpeg,png,jpg,pdf', 'max:5120'], // 5MB for supporting docs
                // Note: No account number for university
            ],
            default => []
        };
    }

    /**
     * Get validation rules for a custom field
     */
    private function getFieldValidationRules($field): array
    {
        $rules = [];

        // Required validation
        if ($field->is_required) {
            $rules[] = 'required';
        } else {
            $rules[] = 'nullable';
        }

        // Type-specific validation
        switch ($field->field_type) {
            case 'text':
            case 'textarea':
                $rules[] = 'string';
                if ($field->field_options && isset($field->field_options['max_length'])) {
                    $rules[] = 'max:' . $field->field_options['max_length'];
                } else {
                    $rules[] = 'max:1000';
                }
                if ($field->field_options && isset($field->field_options['min_length'])) {
                    $rules[] = 'min:' . $field->field_options['min_length'];
                }
                break;

            case 'email':
                $rules[] = 'email';
                $rules[] = 'max:255';
                break;

            case 'tel':
                $rules[] = 'string';
                $rules[] = 'regex:/^[\+]?[0-9\s\-\(\)]+$/';
                $rules[] = 'max:20';
                break;

            case 'number':
                $rules[] = 'numeric';
                if ($field->field_options && isset($field->field_options['min'])) {
                    $rules[] = 'min:' . $field->field_options['min'];
                }
                if ($field->field_options && isset($field->field_options['max'])) {
                    $rules[] = 'max:' . $field->field_options['max'];
                }
                break;

            case 'date':
                $rules[] = 'date';
                if ($field->field_options && isset($field->field_options['min_date'])) {
                    $rules[] = 'after_or_equal:' . $field->field_options['min_date'];
                }
                if ($field->field_options && isset($field->field_options['max_date'])) {
                    $rules[] = 'before_or_equal:' . $field->field_options['max_date'];
                }
                break;

            case 'file':
                $rules[] = 'file';
                
                // File size validation
                $maxSize = 2048; // Default 2MB
                if ($field->field_options && isset($field->field_options['max_size'])) {
                    $maxSize = $field->field_options['max_size'];
                }
                $rules[] = 'max:' . $maxSize;

                // File type validation
                $allowedTypes = ['jpeg', 'png', 'jpg', 'pdf'];
                if ($field->field_options && isset($field->field_options['allowed_types'])) {
                    $allowedTypes = $field->field_options['allowed_types'];
                }
                $rules[] = 'mimes:' . implode(',', $allowedTypes);
                break;

            case 'select':
                if ($field->field_options && isset($field->field_options['options'])) {
                    $options = array_keys($field->field_options['options']);
                    $rules[] = 'in:' . implode(',', $options);
                }
                break;

            default:
                $rules[] = 'string';
                $rules[] = 'max:255';
                break;
        }

        // Additional custom validation rules from field options
        if ($field->field_options && isset($field->field_options['validation_rules'])) {
            $customRules = $field->field_options['validation_rules'];
            if (is_array($customRules)) {
                $rules = array_merge($rules, $customRules);
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validation errors
     */
    public function messages(): array
    {
        return [
            'student_name.required' => 'Student name is required.',
            'student_age.required' => 'Student age is required.',
            'student_age.integer' => 'Student age must be a valid number.',
            'student_age.min' => 'Student age is too young for this scholarship category.',
            'student_age.max' => 'Student age is too old for this scholarship category.',
            'parent_phone.regex' => 'Please enter a valid phone number.',
            'student_phone.regex' => 'Please enter a valid phone number.',
            'student_photo.required' => 'Student photo is required.',
            'student_photo.mimes' => 'Student photo must be a JPEG, PNG, or JPG file.',
            'student_photo.max' => 'Student photo must not exceed 2MB.',
            'student_id_card.required' => 'Student ID card is required.',
            'student_id_card.mimes' => 'Student ID card must be a JPEG, PNG, JPG, or PDF file.',
            'remita_payment.required' => 'Remita payment receipt is required.',
            'remita_payment.mimes' => 'Remita payment receipt must be a JPEG, PNG, JPG, or PDF file.',
            'reason.required' => 'Please provide a reason for applying.',
            'reason.min' => 'Please provide a more detailed reason (at least 50 characters).',
            'reason.max' => 'Reason is too long (maximum 1000 characters).',
            'school_fee.required' => 'School fee amount is required.',
            'school_fee.numeric' => 'School fee must be a valid amount.',
            'account_number.required' => 'Bank account number is required.',
            'matric_number.required' => 'Matriculation number is required.',
            'student_email.required' => 'Student email is required.',
            'student_email.email' => 'Please enter a valid email address.',
        ];
    }

    /**
     * Get custom attributes for validation errors
     */
    public function attributes(): array
    {
        return [
            'student_name' => 'student name',
            'student_age' => 'student age',
            'student_class' => 'student class',
            'parent_name' => 'parent/guardian name',
            'parent_phone' => 'parent/guardian phone',
            'headmaster_name' => 'headmaster name',
            'principal_name' => 'principal name',
            'financial_officer_name' => 'financial officer name',
            'student_photo' => 'student photo',
            'student_id_card' => 'student ID card',
            'remita_payment' => 'remita payment receipt',
            'supporting_documents' => 'supporting documents',
            'school_fee' => 'school fee',
            'school_name' => 'school name',
            'account_number' => 'account number',
            'bank_name' => 'bank name',
            'account_name' => 'account name',
            'matric_number' => 'matriculation number',
            'student_email' => 'student email',
            'student_phone' => 'student phone',
        ];
    }
}
