<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'organization_name',
        'organization_type',
        'registration_number',
        'contact_person',
        'contact_email',
        'contact_phone',
        'address',
        'city',
        'state',
        'country',
        'website',
        'description',
        'areas_of_focus',
        'partnership_interests',
        'resources_to_share',
        'expected_collaboration',
        'verification_documents',
        'status',
        'applied_at',
        'approved_at',
        'approved_by',
        'rejection_reason'
    ];

    protected $casts = [
        'areas_of_focus' => 'array',
        'partnership_interests' => 'array',
        'resources_to_share' => 'array',
        'verification_documents' => 'array',
        'applied_at' => 'datetime',
        'approved_at' => 'datetime'
    ];

    /**
     * Get the user that owns the partner profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who approved this partner
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get partnership proposals for this partner
     */
    public function proposals()
    {
        return $this->hasMany(PartnershipProposal::class);
    }

    /**
     * Get shared resources by this partner
     */
    public function sharedResources()
    {
        return $this->hasMany(SharedResource::class);
    }

    /**
     * Get collaborative programs for this partner
     */
    public function collaborativePrograms()
    {
        return $this->hasMany(CollaborativeProgram::class);
    }

    /**
     * Scope for approved partners
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending partners
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for specific organization type
     */
    public function scopeType($query, $type)
    {
        return $query->where('organization_type', $type);
    }

    /**
     * Check if partner is approved
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if partner is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if partner is rejected
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Approve partner
     */
    public function approve($approvedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy
        ]);
    }

    /**
     * Reject partner
     */
    public function reject($reason = null)
    {
        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason
        ]);
    }

    /**
     * Get organization type label
     */
    public function getOrganizationTypeLabelAttribute()
    {
        $types = [
            'school' => 'Educational Institution',
            'ngo' => 'Non-Governmental Organization',
            'government' => 'Government Agency',
            'corporate' => 'Corporate Organization',
            'international' => 'International Organization'
        ];

        return $types[$this->organization_type] ?? $this->organization_type;
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'approved':
                return 'green';
            case 'pending':
                return 'yellow';
            case 'rejected':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Get partnership duration in days
     */
    public function getPartnershipDurationAttribute()
    {
        if (!$this->approved_at) {
            return 0;
        }

        return $this->approved_at->diffInDays(now());
    }
}
