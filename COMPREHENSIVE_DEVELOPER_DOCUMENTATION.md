# 🌟 Halimakq Foundation - Comprehensive Developer Documentation

**Transforming Lives Through Technology** - Complete development guide for the HLTKKQ Foundation NGO platform

---

## 📊 Project Overview

The Halimakq Foundation platform is a comprehensive NGO management system built with modern web technologies to support educational empowerment in Nigeria. The platform manages donations, scholarships, volunteer programs, events, and educational content through a responsive web interface.

### Technology Stack

**Backend (Laravel 12)**
- PHP 8.2+ with Laravel Framework
- MySQL database with Eloquent ORM
- Laravel Sanctum for API authentication
- RESTful API architecture
- Comprehensive admin panel

**Frontend (Next.js 15)**
- React 19 with TypeScript
- Next.js App Router for SSR/SSG
- Tailwind CSS for styling
- shadcn/ui component library
- Zustand for state management

---

## 🚀 Quick Start Guide

### Prerequisites
```bash
# Required software
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- npm/yarn
```

### Installation

**1. Backend Setup**
```bash
# Clone repository
git clone [repository-url]
cd laravel-api-ngo

# Install dependencies
composer install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate:fresh --seed

# Start server
php artisan serve
```

**2. Frontend Setup**
```bash
# Navigate to frontend
cd front-end

# Install dependencies
npm install

# Environment setup
cp .env.example .env.local

# Start development server
npm run dev
```

**3. Access Points**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000/api/v1
- API Documentation: http://localhost:8000/api/documentation

---

## 🏗️ Architecture Overview

### System Design

```
┌─────────────────────────────────────────────────────────────┐
│                    HLTKKQ Foundation Platform               │
├─────────────────────────────────────────────────────────────┤
│  Frontend Layer (Next.js 15)                               │
│  ├── App Router Pages          ├── Components              │
│  ├── Custom Hooks              ├── State Management        │
│  ├── API Integration           └── UI Library (shadcn/ui)  │
├─────────────────────────────────────────────────────────────┤
│  Backend Layer (Laravel 12)                                │
│  ├── API Controllers           ├── Authentication          │
│  ├── Eloquent Models           ├── Middleware              │
│  ├── Database Migrations       └── Business Logic          │
├─────────────────────────────────────────────────────────────┤
│  Database Layer (MySQL)                                    │
│  ├── User Management           ├── Content Management      │
│  ├── Donation System           ├── Event System            │
│  └── Volunteer Management      └── Analytics & Reports     │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow

```
User Request → Next.js Frontend → API Client → Laravel Backend → Database
     ↓              ↓                ↓            ↓              ↓
UI Interaction → State Update → HTTP Request → Controller → Model/Query
     ↓              ↓                ↓            ↓              ↓
User Response ← UI Update ← API Response ← JSON Response ← Database Result
```

---

## 🔧 Backend Documentation

### File Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Api/
│   │   │   ├── AuthController.php          # Authentication
│   │   │   ├── UserController.php          # User management
│   │   │   ├── BlogController.php          # Blog system
│   │   │   ├── DonationController.php      # Donations
│   │   │   ├── EventController.php         # Events
│   │   │   ├── VolunteerController.php     # Volunteer system
│   │   │   ├── ScholarshipController.php   # Scholarships
│   │   │   └── Admin/                      # Admin controllers
│   │   └── Middleware/
│   └── Requests/                           # Form validation
├── Models/                                 # Eloquent models
├── Services/                               # Business logic
└── Providers/                              # Service providers
```

### API Endpoints Structure

**Authentication Endpoints**
```http
POST   /api/v1/register              # User registration
POST   /api/v1/login                 # User login
POST   /api/v1/logout                # User logout
POST   /api/v1/forgot-password       # Password reset request
POST   /api/v1/reset-password        # Password reset
GET    /api/v1/user                  # Get authenticated user
```

**Public Content Endpoints**
```http
GET    /api/v1/blog/posts            # Get blog posts
GET    /api/v1/blog/posts/{slug}     # Get specific post
GET    /api/v1/events                # Get events
GET    /api/v1/programs              # Get programs
GET    /api/v1/scholarships          # Get scholarships
POST   /api/v1/contact               # Contact form
POST   /api/v1/newsletter/subscribe  # Newsletter subscription
```

**User Protected Endpoints**
```http
GET    /api/v1/profile               # User profile
PUT    /api/v1/profile               # Update profile
POST   /api/v1/volunteer/apply       # Volunteer application
POST   /api/v1/donations             # Make donation
POST   /api/v1/events/{id}/register  # Register for event
GET    /api/v1/scholarships/my-applications  # User's applications
```

**Admin Endpoints**
```http
GET    /api/v1/admin/dashboard       # Admin dashboard
GET    /api/v1/admin/users           # User management
POST   /api/v1/admin/blog/posts      # Create blog post
PUT    /api/v1/admin/events/{id}     # Update event
GET    /api/v1/admin/analytics       # Platform analytics
```

### Database Schema

**Core Tables**

```sql
-- Users table
users: id, first_name, last_name, email, password, role, status, preferences

-- Blog system
blog_posts: id, title, slug, content, author_id, category_id, status
blog_categories: id, name, slug, description
blog_comments: id, post_id, user_id, content, status

-- Donation system
donations: id, user_id, campaign_id, amount, payment_method, status
donation_campaigns: id, title, description, goal_amount, current_amount

-- Event system
events: id, title, description, start_date, end_date, location, capacity
event_registrations: id, event_id, user_id, registration_date, status

-- Volunteer system
volunteers: id, user_id, skills, availability, experience, status
volunteer_hours: id, volunteer_id, hours, date, description, approved

-- Scholarship system
scholarships: id, title, description, requirements, deadline, amount
scholarship_applications: id, scholarship_id, user_id, application_data, status
```

**User Types and Roles**

```php
// User types
const USER_TYPES = [
    'student' => 'Student',
    'partner' => 'Partner Organization', 
    'volunteer' => 'Volunteer',
    'admin' => 'Administrator'
];

// User statuses
const USER_STATUSES = [
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended'
];
```

### Authentication Implementation

**Laravel Sanctum Setup**

```php
// config/sanctum.php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),

// API Authentication Middleware
Route::middleware(['auth:sanctum'])->group(function () {
    // Protected routes
});
```

**Controller Authentication Pattern**

```php
// AuthController.php
public function login(Request $request)
{
    $credentials = $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    if (Auth::attempt($credentials)) {
        $user = Auth::user();
        $token = $user->createToken('auth-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'access_token' => $token,
            ]
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Invalid credentials'
    ], 401);
}
```

---

## 🎨 Frontend Documentation

### File Structure

```
front-end/
├── app/                            # Next.js App Router
│   ├── auth/
│   │   ├── login/page.tsx          # Login page
│   │   └── register/page.tsx       # Registration page
│   ├── dashboard/
│   │   ├── admin/page.tsx          # Admin dashboard
│   │   ├── student/page.tsx        # Student dashboard
│   │   ├── volunteer/page.tsx      # Volunteer dashboard
│   │   └── partner/page.tsx        # Partner dashboard
│   ├── blog/
│   │   ├── page.tsx                # Blog listing
│   │   └── [slug]/page.tsx         # Individual post
│   ├── scholarships/page.tsx       # Scholarships page
│   ├── donate/page.tsx             # Donation page
│   └── page.tsx                    # Homepage
├── components/
│   ├── ui/                         # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── card.tsx
│   │   └── ...
│   ├── dashboard/                  # Dashboard components
│   ├── navigation.tsx              # Main navigation
│   └── footer.tsx                  # Site footer
├── hooks/                          # Custom React hooks
│   ├── use-auth.ts                 # Authentication
│   ├── use-settings.ts             # App settings
│   └── use-auth-guard.ts           # Route protection
├── lib/
│   ├── api.ts                      # API client
│   ├── utils.ts                    # Utility functions
│   └── types.ts                    # TypeScript types
└── styles/                         # Global styles
```

### Component Architecture

**Component Design Patterns**

```typescript
// Standard component structure
interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
}

export function Component({ 
  className, 
  children, 
  variant = "default",
  size = "md",
  ...props 
}: ComponentProps) {
  return (
    <div 
      className={cn(
        "base-classes",
        variantClasses[variant],
        sizeClasses[size],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
}
```

**Page Component Structure**

```typescript
// app/example/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { apiClient } from '@/lib/api'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function ExamplePage() {
  const { user } = useAuth()
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiClient.getData()
        if (response.success) {
          setData(response.data)
        }
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) return <LoadingSpinner />
  if (error) return <div>Error: {error}</div>

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page content */}
    </div>
  )
}
```

### State Management

**Authentication State (Zustand)**

```typescript
// hooks/use-auth.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { apiClient } from '@/lib/api'

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  role: string
  status: string
}

interface AuthState {
  user: User | null
  token: string | null
  loading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      loading: false,

      login: async (credentials) => {
        set({ loading: true })
        try {
          const response = await apiClient.login(credentials)
          if (response.success) {
            set({
              user: response.data.user,
              token: response.data.access_token,
              loading: false
            })
            localStorage.setItem('authToken', response.data.access_token)
          }
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      logout: () => {
        set({ user: null, token: null })
        localStorage.removeItem('authToken')
      },

      checkAuth: async () => {
        const token = localStorage.getItem('authToken')
        if (!token) {
          set({ user: null, token: null })
          return
        }

        try {
          const response = await apiClient.getUser()
          if (response.success) {
            set({ user: response.data, token })
          }
        } catch (error) {
          set({ user: null, token: null })
          localStorage.removeItem('authToken')
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user, token: state.token })
    }
  )
)
```

### API Integration

**API Client Implementation**

```typescript
// lib/api.ts
interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
}

class ApiClient {
  private baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  async request<T = any>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}/api/v1${endpoint}`
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      // Handle authentication errors
      if (response.status === 401) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem('authToken')
          window.location.href = '/auth/login'
        }
      }

      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Authentication methods
  async login(credentials: { email: string; password: string }) {
    return this.request('/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    })
  }

  async register(userData: any) {
    return this.request('/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    })
  }

  async getUser() {
    return this.request('/user')
  }

  async logout() {
    return this.request('/logout', { method: 'POST' })
  }

  // Content methods
  async getBlogPosts(params?: any) {
    const query = params ? '?' + new URLSearchParams(params).toString() : ''
    return this.request(`/blog/posts${query}`)
  }

  async getEvents() {
    return this.request('/events')
  }

  async getDonationCampaigns() {
    return this.request('/donations/campaigns')
  }

  // User profile methods
  async getProfile() {
    return this.request('/profile')
  }

  async updateProfile(data: any) {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }
}

export const apiClient = new ApiClient()

// Utility functions
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false
  return !!localStorage.getItem('authToken')
}

export const getCurrentUser = (): any => {
  if (typeof window === 'undefined') return null
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
}
```

### Responsive Design

**Tailwind CSS Responsive System**

```typescript
// Responsive component example
export function ResponsiveCard({ children }: { children: React.ReactNode }) {
  return (
    <div className={cn(
      // Base styles
      "bg-white rounded-lg shadow-sm border p-4",
      // Mobile (default)
      "w-full",
      // Tablet and up
      "md:p-6 md:shadow-md", 
      // Desktop and up
      "lg:p-8 lg:shadow-lg",
      // Large desktop
      "xl:max-w-4xl xl:mx-auto"
    )}>
      {children}
    </div>
  )
}

// Grid layouts
export function ResponsiveGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className={cn(
      "grid gap-4",
      // Mobile: 1 column
      "grid-cols-1",
      // Tablet: 2 columns
      "md:grid-cols-2",
      // Desktop: 3 columns
      "lg:grid-cols-3",
      // Large desktop: 4 columns
      "xl:grid-cols-4"
    )}>
      {children}
    </div>
  )
}
```

**Responsive Navigation**

```typescript
// components/navigation.tsx
'use client'

import { useState } from 'react'
import { Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-green-600">
              HLTKKQ Foundation
            </h1>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-8">
            <NavLink href="/">Home</NavLink>
            <NavLink href="/about">About</NavLink>
            <NavLink href="/programs">Programs</NavLink>
            <NavLink href="/blog">Blog</NavLink>
            <NavLink href="/contact">Contact</NavLink>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right">
                <div className="flex flex-col space-y-4 mt-8">
                  <NavLink href="/" onClick={() => setIsOpen(false)}>
                    Home
                  </NavLink>
                  <NavLink href="/about" onClick={() => setIsOpen(false)}>
                    About
                  </NavLink>
                  {/* More links */}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  )
}
```

---

## 🔗 Development Workflow

### Environment Setup

**Backend Environment (.env)**

```env
# Application Configuration
APP_NAME="HLTKKQ Foundation"
APP_ENV=local
APP_KEY=base64:generated-key
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=

# Authentication
SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000

# File Storage
FILESYSTEM_DISK=public

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Payment Configuration (if applicable)
PAYSTACK_PUBLIC_KEY=your-paystack-public-key
PAYSTACK_SECRET_KEY=your-paystack-secret-key
```

**Frontend Environment (.env.local)**

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"

# Optional: Analytics
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

### Daily Development Commands

```bash
# Backend Development
php artisan serve                     # Start Laravel development server
php artisan migrate:fresh --seed      # Reset database with sample data
php artisan queue:work                # Process background jobs
php artisan storage:link              # Link storage directory
php artisan optimize:clear            # Clear all caches

# Frontend Development  
npm run dev                           # Start Next.js development server
npm run build                         # Build for production
npm run start                         # Start production server
npm run lint                          # Run ESLint
npm run type-check                    # Run TypeScript check

# Database Operations
php artisan migrate                   # Run pending migrations
php artisan migrate:rollback          # Rollback last migration
php artisan db:seed                   # Run database seeders
php artisan tinker                    # Laravel REPL for testing

# Code Generation
php artisan make:controller Api/ExampleController  # Create API controller
php artisan make:model Example -m               # Create model with migration
php artisan make:seeder ExampleSeeder           # Create database seeder
php artisan make:request ExampleRequest         # Create form request
```

### Common Development Tasks

**Creating New API Endpoint**

```bash
# 1. Create controller
php artisan make:controller Api/ExampleController

# 2. Define routes in routes/api.php
Route::get('/examples', [ExampleController::class, 'index']);
Route::post('/examples', [ExampleController::class, 'store']);

# 3. Implement controller methods
```

```php
// app/Http/Controllers/Api/ExampleController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExampleController extends Controller
{
    public function index(): JsonResponse
    {
        $data = []; // Fetch data
        
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
    
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);
        
        // Create record
        
        return response()->json([
            'success' => true,
            'message' => 'Created successfully',
            'data' => $record
        ], 201);
    }
}
```

**Creating New Frontend Page**

```bash
# Create new page
mkdir -p front-end/app/example
touch front-end/app/example/page.tsx
```

```typescript
// front-end/app/example/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { apiClient } from '@/lib/api'

export default function ExamplePage() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiClient.request('/examples')
        if (response.success) {
          setData(response.data)
        }
      } catch (error) {
        console.error('Failed to fetch data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div className="flex justify-center p-8">Loading...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>Example Page</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Page content */}
        </CardContent>
      </Card>
    </div>
  )
}
```

---

## 🧪 Testing

### Backend Testing

**PHPUnit Configuration**

```php
// tests/Feature/ExampleTest.php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_examples()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user, 'sanctum')
                         ->getJson('/api/v1/examples');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data'
                 ]);
    }

    public function test_can_create_example()
    {
        $user = User::factory()->create();
        $data = [
            'title' => 'Test Example',
            'description' => 'Test description'
        ];

        $response = $this->actingAs($user, 'sanctum')
                         ->postJson('/api/v1/examples', $data);

        $response->assertStatus(201)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Created successfully'
                 ]);
    }
}
```

**Running Tests**

```bash
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/ExampleTest.php

# Run tests with coverage
php artisan test --coverage

# Run tests in parallel
php artisan test --parallel
```

### Frontend Testing

**Jest and React Testing Library Setup**

```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies correct classes', () => {
    render(<Button className="custom-class">Button</Button>)
    expect(screen.getByText('Button')).toHaveClass('custom-class')
  })
})
```

```bash
# Run frontend tests
cd front-end
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage
```

---

## 🚀 Deployment

### Production Deployment

**Backend Deployment**

```bash
# 1. Prepare production environment
composer install --no-dev --optimize-autoloader

# 2. Environment configuration
cp .env.example .env.production
# Edit .env.production with production values

# 3. Generate application key
php artisan key:generate

# 4. Run migrations
php artisan migrate --force

# 5. Seed initial data (if needed)
php artisan db:seed --force

# 6. Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan storage:link

# 7. Set proper permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

**Frontend Deployment**

```bash
# 1. Install dependencies
npm ci

# 2. Build for production
npm run build

# 3. Start production server
npm start

# Or for static export
npm run export
```

**Production Environment Variables**

```env
# Backend (.env.production)
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.hltkkqfoundation.org

DB_CONNECTION=mysql
DB_HOST=production-db-host
DB_PORT=3306
DB_DATABASE=production_db
DB_USERNAME=production_user
DB_PASSWORD=secure_password

SANCTUM_STATEFUL_DOMAINS=hltkkqfoundation.org,www.hltkkqfoundation.org
SESSION_DOMAIN=.hltkkqfoundation.org
```

```env
# Frontend (.env.production)
NEXT_PUBLIC_API_URL=https://api.hltkkqfoundation.org
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

---

## 📚 Additional Resources

### Code Style Guidelines

**PHP/Laravel Standards**
- Follow PSR-12 coding standards
- Use meaningful variable and method names
- Write comprehensive comments for complex logic
- Implement proper error handling
- Use Laravel's built-in features and conventions

**TypeScript/React Standards**
- Use TypeScript for all components and functions
- Follow React best practices and hooks patterns
- Implement proper error boundaries
- Use semantic HTML elements
- Follow accessibility guidelines

### Useful Links

- [Laravel Documentation](https://laravel.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Components](https://ui.shadcn.com)
- [Laravel Sanctum](https://laravel.com/docs/sanctum)
- [Zustand State Management](https://github.com/pmndrs/zustand)

### Support and Maintenance

**Getting Help**
- Check existing documentation first
- Search through issues and discussions
- Contact the development team for complex issues
- Create detailed bug reports with reproduction steps

**Contributing**
1. Fork the repository
2. Create a feature branch
3. Follow coding standards
4. Write tests for new features
5. Submit a pull request with clear description

---

## 🎯 NGO-Specific Features

### User Management System

**User Types and Capabilities**

1. **Students**
   - Apply for scholarships
   - Access educational resources
   - Register for events
   - Track application status

2. **Partners** 
   - Collaborate on projects
   - Share resources
   - View partnership analytics
   - Submit project proposals

3. **Volunteers**
   - Apply for volunteer opportunities
   - Log volunteer hours
   - Access training materials
   - Track contributions

4. **Administrators**
   - Manage all users and content
   - View comprehensive analytics
   - Configure system settings
   - Generate reports

### Core NGO Modules

**Donation Management**
- Multiple donation campaigns
- Payment processing integration
- Donor receipt generation
- Campaign progress tracking
- Donor analytics and reporting

**Event Management**
- Public event listings
- Online registration system
- Capacity management
- Attendance tracking
- Event feedback collection

**Scholarship Program**
- Application management
- Document upload system
- Review and approval workflow
- Recipient tracking
- Impact measurement

**Blog and Content System**
- Multi-category blog
- Comment moderation
- SEO optimization
- Content scheduling
- Analytics tracking

**Volunteer Coordination**
- Opportunity matching
- Hour logging system
- Training module tracking
- Volunteer recognition
- Impact reporting

---

> **🌟 Mission Statement**: This platform serves the noble mission of educational empowerment in Nigeria. Every feature developed should align with the goal of transforming lives through technology and education.

**Happy Coding! 🚀** 