<?php $__env->startSection('title', 'Support Ticket #' . $ticket->ticket_id); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <a href="<?php echo e(route('partner.support.index')); ?>" class="text-green-600 hover:text-green-700 mr-4">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div class="flex-1">
                            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Support Ticket #<?php echo e($ticket->ticket_id); ?></h1>
                                    <p class="text-gray-600"><?php echo e($ticket->subject); ?></p>
                                </div>
                                <div class="flex items-center space-x-3 mt-4 lg:mt-0">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($ticket->status_color); ?>">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $ticket->status))); ?>

                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($ticket->priority_color); ?>">
                                        <?php echo e(ucfirst($ticket->priority)); ?> Priority
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="max-w-4xl mx-auto">
                    <!-- Ticket Details -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-900">Ticket Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Ticket ID</h4>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ticket->ticket_id); ?></p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Category</h4>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ticket->category_display); ?></p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Created</h4>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ticket->formatted_created_date); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($ticket->time_since_created); ?></p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Last Updated</h4>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo e($ticket->updated_at->format('M d, Y')); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($ticket->updated_at->diffForHumans()); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ticket Description -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-900">Description</h3>
                        </div>
                        <div class="p-6">
                            <div class="prose max-w-none">
                                <p class="text-gray-700 leading-relaxed whitespace-pre-wrap"><?php echo e($ticket->description); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Attachments -->
                    <?php if($ticket->hasAttachments()): ?>
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-xl font-semibold text-gray-900">
                                    Attachments (<?php echo e($ticket->attachment_count); ?>)
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <?php $__currentLoopData = $ticket->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border border-gray-200 rounded-xl p-4 hover:bg-gray-50 transition-colors duration-300">
                                            <div class="flex items-center">
                                                <div class="p-2 rounded-lg bg-blue-100 text-blue-600 mr-3">
                                                    <?php
                                                        $extension = pathinfo($attachment, PATHINFO_EXTENSION);
                                                        $icon = match(strtolower($extension)) {
                                                            'pdf' => 'file-pdf',
                                                            'doc', 'docx' => 'file-word',
                                                            'jpg', 'jpeg', 'png' => 'file-image',
                                                            default => 'file'
                                                        };
                                                    ?>
                                                    <i class="fas fa-<?php echo e($icon); ?> text-xl"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">
                                                        <?php echo e(basename($attachment)); ?>

                                                    </p>
                                                    <p class="text-xs text-gray-500"><?php echo e(strtoupper($extension)); ?> File</p>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <a href="<?php echo e(Storage::url($attachment)); ?>" target="_blank" 
                                                   class="inline-flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors duration-300">
                                                    <i class="fas fa-download mr-1"></i>Download
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Admin Response -->
                    <?php if($ticket->admin_response): ?>
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="p-2 rounded-lg bg-green-100 text-green-600 mr-3">
                                        <i class="fas fa-user-tie text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-semibold text-gray-900">Support Team Response</h3>
                                        <?php if($ticket->resolved_at): ?>
                                            <p class="text-sm text-gray-500">Resolved on <?php echo e($ticket->resolved_at->format('M d, Y \a\t g:i A')); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="prose max-w-none">
                                    <p class="text-gray-700 leading-relaxed whitespace-pre-wrap"><?php echo e($ticket->admin_response); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Status Timeline -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-900">Status Timeline</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-4"></div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Ticket Created</p>
                                        <p class="text-xs text-gray-500"><?php echo e($ticket->formatted_created_date); ?></p>
                                    </div>
                                </div>
                                
                                <?php if($ticket->status === 'in_progress' || $ticket->status === 'resolved'): ?>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-4"></div>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">Under Review</p>
                                            <p class="text-xs text-gray-500">Support team is working on your request</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($ticket->status === 'resolved'): ?>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-4"></div>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">Resolved</p>
                                            <p class="text-xs text-gray-500"><?php echo e($ticket->resolved_at->format('M d, Y \a\t g:i A')); ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-900">Actions</h3>
                        </div>
                        <div class="p-6">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <?php if($ticket->status !== 'resolved'): ?>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                        <i class="fas fa-reply mr-2"></i>Add Response
                                    </button>
                                <?php endif; ?>
                                
                                <a href="<?php echo e(route('partner.support.create')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300 text-center">
                                    <i class="fas fa-plus mr-2"></i>Create New Ticket
                                </a>
                                
                                <a href="<?php echo e(route('partner.support.index')); ?>" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-xl font-semibold transition-colors duration-300 text-center">
                                    <i class="fas fa-list mr-2"></i>All Tickets
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <?php if($ticket->status === 'resolved'): ?>
                        <div class="mt-8 bg-green-50 border border-green-200 rounded-2xl p-6">
                            <div class="flex items-center mb-4">
                                <div class="p-2 rounded-lg bg-green-100 text-green-600 mr-3">
                                    <i class="fas fa-check-circle text-xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-green-900">Issue Resolved</h4>
                            </div>
                            <p class="text-green-800 mb-4">
                                We're glad we could help resolve your issue! If you have any follow-up questions or need additional assistance, please don't hesitate to create a new support ticket.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-3">
                                <a href="<?php echo e(route('partner.support.create')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300 text-center">
                                    Create New Ticket
                                </a>
                                <a href="<?php echo e(route('partner.support.help')); ?>" class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg font-medium transition-colors duration-300 text-center">
                                    Browse Help Docs
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-2xl p-6">
                            <div class="flex items-center mb-4">
                                <div class="p-2 rounded-lg bg-blue-100 text-blue-600 mr-3">
                                    <i class="fas fa-info-circle text-xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-blue-900">What's Next?</h4>
                            </div>
                            <p class="text-blue-800 mb-4">
                                Our support team will review your ticket and respond within 24 hours. You'll receive an email notification when there's an update.
                            </p>
                            <div class="text-sm text-blue-700">
                                <p><strong>Expected Response Time:</strong></p>
                                <ul class="list-disc list-inside mt-1 space-y-1">
                                    <li>Low Priority: 24-48 hours</li>
                                    <li>Medium Priority: 12-24 hours</li>
                                    <li>High Priority: 4-12 hours</li>
                                    <li>Urgent Priority: 1-4 hours</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/support/show.blade.php ENDPATH**/ ?>