<?php $__env->startSection('title', 'Submit New Application'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Submit New Application</h1>
                            <p class="text-gray-600">Apply for a scholarship on behalf of your student</p>
                        </div>
                        <div class="flex gap-3">
                            <a href="<?php echo e(route('partner.applications.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Applications
                            </a>
                        </div>
                    </div>
                </div>

                <?php if($students->isEmpty()): ?>
                    <!-- No Students Message -->
                    <div class="bg-white rounded-2xl shadow-lg p-12 text-center">
                        <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Students</h3>
                        <p class="text-gray-500 mb-6">You need to add students before you can submit scholarship applications.</p>
                        <a href="<?php echo e(route('partner.students.create')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                            <i class="fas fa-plus mr-2"></i>Add Student
                        </a>
                    </div>
                <?php elseif($scholarships->isEmpty()): ?>
                    <!-- No Scholarships Message -->
                    <div class="bg-white rounded-2xl shadow-lg p-12 text-center">
                        <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Scholarships</h3>
                        <p class="text-gray-500 mb-6">There are currently no open scholarships available for application.</p>
                        <a href="<?php echo e(route('scholarships')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                            <i class="fas fa-search mr-2"></i>Browse Scholarships
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Application Form -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Application Details</h2>
                            <p class="text-gray-600 mt-1">Fill out the application form for your student</p>
                        </div>

                        <form action="<?php echo e(route('partner.applications.store')); ?>" method="POST" enctype="multipart/form-data" class="p-6">
                            <?php echo csrf_field(); ?>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Scholarship Selection -->
                                <div class="lg:col-span-2">
                                    <label for="scholarship_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Select Scholarship <span class="text-red-500">*</span>
                                    </label>
                                    <select id="scholarship_id" 
                                            name="scholarship_id" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['scholarship_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            required>
                                        <option value="">Choose a scholarship</option>
                                        <?php $__currentLoopData = $scholarships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scholarship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($scholarship->id); ?>" 
                                                    <?php echo e(old('scholarship_id', $selectedScholarship?->id) == $scholarship->id ? 'selected' : ''); ?>

                                                    data-category="<?php echo e($scholarship->category); ?>"
                                                    data-amount="<?php echo e($scholarship->amount); ?>"
                                                    data-deadline="<?php echo e($scholarship->application_deadline); ?>">
                                                <?php echo e($scholarship->title); ?> (<?php echo e(ucfirst($scholarship->category)); ?>) - ₦<?php echo e(number_format($scholarship->amount)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['scholarship_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Student Selection -->
                                <div class="lg:col-span-2">
                                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Select Student <span class="text-red-500">*</span>
                                    </label>
                                    <select id="student_id" 
                                            name="student_id" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['student_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            required>
                                        <option value="">Choose a student</option>
                                        <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($student->id); ?>" <?php echo e(old('student_id') == $student->id ? 'selected' : ''); ?>>
                                                <?php echo e($student->name); ?> (<?php echo e($student->class); ?>) - Age <?php echo e($student->age); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['student_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Guardian Information -->
                                <div class="lg:col-span-2">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">Guardian Information</h3>
                                </div>

                                <!-- Guardian Name -->
                                <div>
                                    <label for="guardian_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian/Parent Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           id="guardian_name" 
                                           name="guardian_name" 
                                           value="<?php echo e(old('guardian_name')); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['guardian_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           placeholder="Enter guardian's full name"
                                           required>
                                    <?php $__errorArgs = ['guardian_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Guardian Phone -->
                                <div>
                                    <label for="guardian_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Phone Number <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" 
                                           id="guardian_phone" 
                                           name="guardian_phone" 
                                           value="<?php echo e(old('guardian_phone')); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['guardian_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           placeholder="e.g., +234 ************"
                                           required>
                                    <?php $__errorArgs = ['guardian_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Guardian Email -->
                                <div class="lg:col-span-2">
                                    <label for="guardian_email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Email Address (Optional)
                                    </label>
                                    <input type="email" 
                                           id="guardian_email" 
                                           name="guardian_email" 
                                           value="<?php echo e(old('guardian_email')); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['guardian_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           placeholder="<EMAIL>">
                                    <?php $__errorArgs = ['guardian_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Reason for Application -->
                                <div class="lg:col-span-2">
                                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                                        Reason for Application <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="reason" 
                                              name="reason" 
                                              rows="4"
                                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              placeholder="Explain why this student deserves the scholarship (minimum 50 characters)"
                                              required><?php echo e(old('reason')); ?></textarea>
                                    <p class="mt-1 text-sm text-gray-500">Minimum 50 characters required</p>
                                    <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <!-- Supporting Documents -->
                                <div class="lg:col-span-2">
                                    <label for="supporting_documents" class="block text-sm font-medium text-gray-700 mb-2">
                                        Supporting Documents (Optional)
                                    </label>
                                    <input type="file" 
                                           id="supporting_documents" 
                                           name="supporting_documents" 
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 <?php $__errorArgs = ['supporting_documents'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                    <p class="mt-1 text-sm text-gray-500">Accepted formats: PDF, DOC, DOCX, JPG, PNG. Maximum size: 5MB</p>
                                    <?php $__errorArgs = ['supporting_documents'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6 mt-6 border-t border-gray-200">
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 flex-1 sm:flex-none">
                                    <i class="fas fa-paper-plane mr-2"></i>Submit Application
                                </button>
                                <a href="<?php echo e(route('partner.applications.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 text-center flex-1 sm:flex-none">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }

    // Character counter for reason textarea
    const reasonTextarea = document.getElementById('reason');
    if (reasonTextarea) {
        reasonTextarea.addEventListener('input', function() {
            const length = this.value.length;
            const minLength = 50;
            const parent = this.parentElement;
            let counter = parent.querySelector('.char-counter');
            
            if (!counter) {
                counter = document.createElement('p');
                counter.className = 'char-counter mt-1 text-sm';
                parent.appendChild(counter);
            }
            
            if (length < minLength) {
                counter.textContent = `${length}/${minLength} characters (${minLength - length} more needed)`;
                counter.className = 'char-counter mt-1 text-sm text-red-600';
            } else {
                counter.textContent = `${length} characters`;
                counter.className = 'char-counter mt-1 text-sm text-green-600';
            }
        });
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/applications/create.blade.php ENDPATH**/ ?>