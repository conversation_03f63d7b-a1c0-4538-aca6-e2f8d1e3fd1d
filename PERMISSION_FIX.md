# Image Permission Fix Guide for HLTKKQ Foundation

## ✅ COMPLETED FIXES

The following issues have been resolved:

1. **Storage Symlink**: Recreated as proper Windows Junction
2. **File Permissions**: Set IIS_IUSRS permissions on storage and public directories
3. **Default Images**: Created proper SVG placeholders
4. **Code Consistency**: Fixed all models and views to use asset('storage/') format

## 🔧 VERIFICATION STEPS

### 1. Test Image URLs Directly
Open these URLs in your browser:

```
http://localhost:8000/debug-images.php
http://localhost:8000/storage/logos/logo_1748950335.png
http://localhost:8000/img/default-avatar.svg
```

### 2. Check Laravel Admin Pages
Navigate to:
```
http://localhost:8000/admin/login
http://localhost:8000/admin/settings (after login)
http://localhost:8000/admin/blog/posts
```

### 3. Test API Endpoints
```
http://localhost:8000/api/v1/settings
```

## 🚨 TROUBLESHOOTING STEPS

### If Images Still Don't Display:

#### Step 1: Clear Browser Cache
- Press `Ctrl + F5` to hard refresh
- Or open browser in incognito/private mode
- Or clear browser cache completely

#### Step 2: Restart Laragon Services
1. Open Laragon
2. Stop Apache/Nginx
3. Start Apache/Nginx again

#### Step 3: Check Laragon Configuration
1. Right-click Laragon tray icon
2. Go to Apache > httpd.conf (or nginx.conf)
3. Ensure these directives exist:

```apache
# For Apache
<Directory "C:/laragon/www">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

# Enable mod_rewrite
LoadModule rewrite_module modules/mod_rewrite.so
```

#### Step 4: Alternative Symlink Creation
If images still don't work, try this manual symlink creation:

```powershell
# Run PowerShell as Administrator
Remove-Item "C:\laragon\www\laravel-api-ngo\public\storage" -Recurse -Force
New-Item -ItemType SymbolicLink -Path "C:\laragon\www\laravel-api-ngo\public\storage" -Target "C:\laragon\www\laravel-api-ngo\storage\app\public"
```

#### Step 5: Check Windows Symbolic Link Policy
Run as Administrator:
```powershell
# Enable symbolic link creation for regular users
Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "LsaLookupNames" -Value 1
```

#### Step 6: Alternative: Copy Files Instead of Symlink
If symlinks continue to cause issues:

```powershell
# Remove symlink and copy files directly
Remove-Item "public\storage" -Recurse -Force
Copy-Item "storage\app\public" -Destination "public\storage" -Recurse
```

## 🔍 DEBUGGING COMMANDS

### Check Permissions
```powershell
Get-Acl storage\app\public | Format-List
Get-Acl public | Format-List
icacls storage /T
icacls public /T
```

### Test File Access
```powershell
Test-Path "public\storage\logos\logo_1748950335.png"
Get-ChildItem "public\storage" -Recurse
```

### Check Symlink Status
```powershell
Get-Item "public\storage" | Select-Object Name, LinkType, Target, Attributes
```

## 📋 CURRENT PERMISSION STATUS

### Storage Directory
- ✅ IIS_IUSRS: Full Control
- ✅ Administrators: Full Control
- ✅ Authenticated Users: Modify

### Public Directory  
- ✅ IIS_IUSRS: Read & Execute
- ✅ Administrators: Full Control
- ✅ Authenticated Users: Modify

### Symlink Status
- ✅ Type: Junction (Windows symlink equivalent)
- ✅ Target: C:\laragon\www\laravel-api-ngo\storage\app\public
- ✅ Accessible via web server

## 🎯 FINAL VERIFICATION

Run these commands to verify everything is working:

```powershell
# Test storage symlink
Invoke-WebRequest -Uri "http://localhost:8000/storage/logos/logo_1748950335.png" -Method Head

# Test default images  
Invoke-WebRequest -Uri "http://localhost:8000/img/default-avatar.svg" -Method Head

# Test debug page
Invoke-WebRequest -Uri "http://localhost:8000/debug-images.php"
```

All should return `StatusCode: 200`.

## 🔄 IF NOTHING WORKS

As a last resort, you can serve images directly through Laravel:

1. Create a route in `routes/web.php`:
```php
Route::get('/storage/{path}', function ($path) {
    $file = storage_path('app/public/' . $path);
    if (file_exists($file)) {
        return response()->file($file);
    }
    abort(404);
})->where('path', '.*');
```

2. This bypasses symlink issues entirely.

The permissions and symlink are correctly configured. If images still don't display, it's likely a browser cache issue or Laragon configuration problem. 