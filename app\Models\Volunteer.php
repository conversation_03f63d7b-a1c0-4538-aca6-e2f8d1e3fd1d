<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Volunteer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'application_status',
        'status',
        'skills',
        'interests',
        'availability',
        'experience',
        'motivation',
        'background_check_status',
        'background_check_date',
        'background_check_notes',
        'emergency_contact',
        'hours_logged',
        'certifications',
        'applied_at',
        'approved_at',
        'rejected_at',
        'rejection_reason',
        'notes'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'skills' => 'array',
        'interests' => 'array',
        'availability' => 'array',
        'emergency_contact' => 'array',
        'certifications' => 'array',
        'applied_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'background_check_date' => 'datetime',
        'hours_logged' => 'decimal:2',
    ];

    /**
     * Get the user that owns the volunteer record.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if volunteer application is pending.
     */
    public function isPending(): bool
    {
        return $this->application_status === 'pending';
    }

    /**
     * Check if volunteer is approved.
     */
    public function isApproved(): bool
    {
        return $this->application_status === 'approved';
    }

    /**
     * Check if volunteer application is rejected.
     */
    public function isRejected(): bool
    {
        return $this->application_status === 'rejected';
    }

    /**
     * Check if background check is passed.
     */
    public function hasPassedBackgroundCheck(): bool
    {
        return $this->background_check_status === 'passed';
    }

    /**
     * Scope a query to only include approved volunteers.
     */
    public function scopeApproved($query)
    {
        return $query->where('application_status', 'approved');
    }

    /**
     * Scope a query to only include pending volunteers.
     */
    public function scopePending($query)
    {
        return $query->where('application_status', 'pending');
    }

    /**
     * Scope a query to include volunteers with specific skills.
     */
    public function scopeWithSkills($query, array $skills)
    {
        return $query->whereJsonContains('skills', $skills);
    }

    /**
     * Add volunteer hours.
     */
    public function addHours(float $hours): void
    {
        $this->increment('hours_logged', $hours);
    }

    /**
     * Approve volunteer application.
     */
    public function approve(): void
    {
        $this->update([
            'application_status' => 'approved',
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject volunteer application.
     */
    public function reject(): void
    {
        $this->update([
            'application_status' => 'rejected',
        ]);
    }
}
