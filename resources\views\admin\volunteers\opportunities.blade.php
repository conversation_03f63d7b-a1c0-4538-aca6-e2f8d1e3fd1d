@extends('layouts.admin')

@section('title', 'Volunteer Opportunities')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Volunteer Opportunities</h1>
        <a href="{{ route('admin.volunteers.opportunities.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create Opportunity
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Active Opportunities</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hands-helping fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Opportunities</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Featured</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['featured'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Open Applications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['open_applications'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Opportunities Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Current Opportunities</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Duration</th>
                            <th>Applications</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($opportunities as $opportunity)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $opportunity->title }}</strong>
                                    @if($opportunity->is_featured)
                                        <i class="fas fa-star text-warning ms-1" title="Featured"></i>
                                    @endif
                                </div>
                                <small class="text-muted">{{ Str::limit($opportunity->description, 60) }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $opportunity->category === 'education' ? 'primary' :
                                    ($opportunity->category === 'health' ? 'success' :
                                    ($opportunity->category === 'environment' ? 'info' :
                                    ($opportunity->category === 'community' ? 'warning' : 'secondary')))
                                }}">
                                    {{ ucfirst($opportunity->category) }}
                                </span>
                            </td>
                            <td>{{ $opportunity->location ?? 'Remote' }}</td>
                            <td>{{ $opportunity->time_commitment_badge }}</td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {{ $opportunity->applications->count() }}
                                    @if($opportunity->max_volunteers)
                                        / {{ $opportunity->max_volunteers }}
                                    @endif
                                </span>
                            </td>
                            <td>
                                @if($opportunity->is_active)
                                    @if($opportunity->isOpenForApplications())
                                        <span class="badge bg-success">Open</span>
                                    @else
                                        <span class="badge bg-warning">Closed</span>
                                    @endif
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.volunteers.opportunities.show', $opportunity->id) }}" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.volunteers.opportunities.edit', $opportunity->id) }}" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteOpportunity({{ $opportunity->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-hands-helping fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No volunteer opportunities found</p>
                                <a href="{{ route('admin.volunteers.opportunities.create') }}" class="btn btn-primary">Create Your First Opportunity</a>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteOpportunityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this volunteer opportunity? This action cannot be undone and will remove all associated applications.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteOpportunity">Delete Opportunity</button>
            </div>
        </div>
    </div>
</div>

<script>
function deleteOpportunity(opportunityId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteOpportunityModal'));
    modal.show();

    document.getElementById('confirmDeleteOpportunity').onclick = function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/volunteers/opportunities/${opportunityId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();

        modal.hide();
    };
}
</script>
@endsection