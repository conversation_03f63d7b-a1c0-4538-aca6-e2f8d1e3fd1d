<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('donations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('donor_id')->nullable()->constrained('users');
            $table->string('donor_email', 100)->nullable();
            $table->string('donor_name', 100)->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('NGN');
            $table->enum('donation_type', ['one-time', 'recurring'])->default('one-time');
            $table->enum('frequency', ['monthly', 'quarterly', 'annually'])->nullable();
            $table->string('purpose', 200)->nullable();
            $table->foreignId('campaign_id')->nullable();
            $table->string('payment_method', 50)->nullable();
            $table->string('payment_provider', 50)->default('paystack');
            $table->string('transaction_reference', 100)->unique();
            $table->enum('payment_status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            $table->boolean('is_anonymous')->default(false);
            $table->string('receipt_number', 50)->unique()->nullable();
            $table->timestamp('receipt_issued_at')->nullable();
            $table->text('notes')->nullable();
            $table->json('payment_metadata')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['donor_id', 'payment_status']);
            $table->index(['payment_status', 'created_at']);
            $table->index('transaction_reference');
            $table->index('receipt_number');
            $table->index(['donation_type', 'payment_status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('donations');
    }
}; 