@extends('layouts.admin')

@section('title', 'Blog Posts')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Blog Posts</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">Blog</a></li>
                        <li class="breadcrumb-item active">Posts</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="card shadow">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-newspaper me-2"></i>All Blog Posts
                    </h6>
                </div>
                <div class="col-auto">
                    <a href="{{ route('admin.blog.posts.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>New Post
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="card-body border-bottom">
            <form method="GET" action="{{ route('admin.blog.posts') }}" class="row g-3 align-items-center">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control form-control-sm" placeholder="Search posts..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select form-select-sm">
                        <option value="">All Status</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="archived" {{ request('status') == 'archived' ? 'selected' : '' }}>Archived</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-select form-select-sm">
                        <option value="">All Categories</option>
                        @if(isset($categories))
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-1">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        @if(request('search') || request('status') || request('category'))
                            <a href="{{ route('admin.blog.posts') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i>
                            </a>
                        @endif
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body">
            @if(isset($posts) && $posts->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%"></th>
                                <th width="40%">Title</th>
                                <th width="15%">Author</th>
                                <th width="15%">Category</th>
                                <th width="10%">Status</th>
                                <th width="10%">Date</th>
                                <th width="5%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($posts as $post)
                                <tr>
                                    <td>
                                        @if($post->featured_image)
                                            <img src="{{ asset('storage/' . $post->featured_image) }}" class="img-thumbnail" width="50" height="50" alt="Featured Image">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $post->title }}</strong>
                                            @if($post->excerpt)
                                                <br><small class="text-muted">{{ Str::limit($post->excerpt, 80) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($post->author)
                                            <div class="d-flex align-items-center">
                                                @if($post->author->profile_picture)
                                                    <img src="{{ asset('storage/' . $post->author->profile_picture) }}" class="rounded-circle me-2" width="24" height="24" alt="Author">
                                                @else
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px;">
                                                        <i class="fas fa-user text-white" style="font-size: 10px;"></i>
                                                    </div>
                                                @endif
                                                <small>{{ $post->author->first_name }} {{ $post->author->last_name }}</small>
                                            </div>
                                        @else
                                            <small class="text-muted">Unknown Author</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($post->category)
                                            <span class="badge rounded-pill" style="background-color: {{ $post->category->color ?? '#6c757d' }}; color: white;">
                                                {{ $post->category->name }}
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">Uncategorized</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($post->status == 'published')
                                            <span class="badge bg-success">Published</span>
                                        @elseif($post->status == 'draft')
                                            <span class="badge bg-warning">Draft</span>
                                        @else
                                            <span class="badge bg-secondary">Archived</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($post->published_at)
                                            <small>{{ $post->published_at->format('M d, Y') }}</small>
                                        @else
                                            <small class="text-muted">{{ $post->created_at->format('M d, Y') }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.blog.posts.edit', $post->id) }}">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a>
                                                </li>
                                                @if($post->status == 'published')
                                                    <li>
                                                        <a class="dropdown-item" href="/blog/{{ $post->slug }}" target="_blank">
                                                            <i class="fas fa-eye me-2"></i>View Post
                                                        </a>
                                                    </li>
                                                @endif
                                                <li><hr class="dropdown-divider"></li>
                                                @if($post->status != 'published')
                                                    <li>
                                                        <form action="{{ route('admin.blog.posts.status.form', $post->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="published">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-check text-success me-2"></i>Publish
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                @if($post->status != 'draft')
                                                    <li>
                                                        <form action="{{ route('admin.blog.posts.status.form', $post->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="draft">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-edit text-warning me-2"></i>Move to Draft
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                @if($post->status != 'archived')
                                                    <li>
                                                        <form action="{{ route('admin.blog.posts.status.form', $post->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="archived">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-archive text-info me-2"></i>Archive
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form action="{{ route('admin.blog.posts.destroy.form', $post->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="small text-muted">
                            Showing {{ $posts->firstItem() }} to {{ $posts->lastItem() }} of {{ $posts->total() }} posts
                        </p>
                    </div>
                    <div>
                        {{ $posts->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No blog posts found</h5>
                    @if(request('search') || request('status') || request('category'))
                        <p class="text-muted">Try adjusting your search criteria or filters.</p>
                        <a href="{{ route('admin.blog.posts') }}" class="btn btn-secondary">Clear Filters</a>
                    @else
                        <p class="text-muted">Create your first blog post to get started!</p>
                        <a href="{{ route('admin.blog.posts.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create First Post
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection 