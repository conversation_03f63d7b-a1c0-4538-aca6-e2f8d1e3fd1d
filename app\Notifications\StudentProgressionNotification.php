<?php

namespace App\Notifications;

use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;

class StudentProgressionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $student;
    protected $progression;
    protected $type;
    protected $additionalData;

    /**
     * Create a new notification instance.
     */
    public function __construct(Student $student, StudentProgression $progression = null, string $type = 'progression_eligible', array $additionalData = [])
    {
        $this->student = $student;
        $this->progression = $progression;
        $this->type = $type;
        $this->additionalData = $additionalData;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $mailMessage = new MailMessage();

        switch ($this->type) {
            case 'progression_eligible':
                return $this->progressionEligibleMail($mailMessage, $notifiable);
            
            case 'progression_completed':
                return $this->progressionCompletedMail($mailMessage, $notifiable);
            
            case 'progression_pending':
                return $this->progressionPendingMail($mailMessage, $notifiable);
            
            case 'scholarship_eligible':
                return $this->scholarshipEligibleMail($mailMessage, $notifiable);
            
            case 'bulk_progression_summary':
                return $this->bulkProgressionSummaryMail($mailMessage, $notifiable);
            
            default:
                return $mailMessage->subject('Student Progression Update');
        }
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        $baseData = [
            'type' => $this->type,
            'student_id' => $this->student->id,
            'student_name' => $this->student->full_name,
            'student_grade' => $this->student->grade_level,
            'created_at' => now(),
        ];

        if ($this->progression) {
            $baseData['progression_id'] = $this->progression->id;
            $baseData['from_grade'] = $this->progression->from_grade;
            $baseData['to_grade'] = $this->progression->to_grade;
            $baseData['academic_year'] = $this->progression->academic_year;
        }

        return array_merge($baseData, $this->additionalData);
    }

    /**
     * Mail for progression eligibility
     */
    private function progressionEligibleMail(MailMessage $mailMessage, $notifiable): MailMessage
    {
        $nextGrade = $this->getNextGrade($this->student->grade_level);
        $gradeDisplay = $this->formatGrade($this->student->grade_level);
        $nextGradeDisplay = $this->formatGrade($nextGrade);

        return $mailMessage
            ->subject("Student Ready for Grade Advancement - {$this->student->full_name}")
            ->greeting("Hello {$notifiable->name},")
            ->line("We're pleased to inform you that one of your students is ready for grade advancement.")
            ->line("**Student Details:**")
            ->line("• Name: {$this->student->full_name}")
            ->line("• Current Grade: {$gradeDisplay}")
            ->line("• Ready to Advance to: {$nextGradeDisplay}")
            ->line("• Academic Year: {$this->additionalData['academic_year'] ?? date('Y') . '/' . (date('Y') + 1)}")
            ->line("This student has met all requirements for grade progression and may also be eligible for next-level scholarship opportunities.")
            ->action('Review Student Progression', url('/dashboard/partner/students/' . $this->student->id))
            ->line('Please log in to your Partner Dashboard to review and process this progression.')
            ->line('Thank you for your continued partnership in supporting student education.');
    }

    /**
     * Mail for completed progression
     */
    private function progressionCompletedMail(MailMessage $mailMessage, $notifiable): MailMessage
    {
        $fromGradeDisplay = $this->formatGrade($this->progression->from_grade);
        $toGradeDisplay = $this->formatGrade($this->progression->to_grade);

        return $mailMessage
            ->subject("Grade Progression Completed - {$this->student->full_name}")
            ->greeting("Hello {$notifiable->name},")
            ->line("Congratulations! A student progression has been successfully completed.")
            ->line("**Progression Details:**")
            ->line("• Student: {$this->student->full_name}")
            ->line("• Advanced From: {$fromGradeDisplay}")
            ->line("• Advanced To: {$toGradeDisplay}")
            ->line("• Academic Year: {$this->progression->academic_year}")
            ->line("• Completion Date: {$this->progression->completed_at->format('F j, Y')}")
            ->line("The student's records have been updated and they may now be eligible for new scholarship opportunities at their current grade level.")
            ->action('View Student Profile', url('/dashboard/partner/students/' . $this->student->id))
            ->line('Thank you for supporting student academic advancement.');
    }

    /**
     * Mail for pending progression
     */
    private function progressionPendingMail(MailMessage $mailMessage, $notifiable): MailMessage
    {
        $fromGradeDisplay = $this->formatGrade($this->progression->from_grade);
        $toGradeDisplay = $this->formatGrade($this->progression->to_grade);

        return $mailMessage
            ->subject("Grade Progression Pending Review - {$this->student->full_name}")
            ->greeting("Hello {$notifiable->name},")
            ->line("A grade progression request has been submitted and is pending your review.")
            ->line("**Progression Details:**")
            ->line("• Student: {$this->student->full_name}")
            ->line("• Advancing From: {$fromGradeDisplay}")
            ->line("• Advancing To: {$toGradeDisplay}")
            ->line("• Academic Year: {$this->progression->academic_year}")
            ->line("• Submitted Date: {$this->progression->created_at->format('F j, Y')}")
            ->line("Please review the progression details and approve or reject as appropriate.")
            ->action('Review Progression', url('/dashboard/partner/progressions/' . $this->progression->id))
            ->line('Your prompt attention to this matter is appreciated.');
    }

    /**
     * Mail for scholarship eligibility
     */
    private function scholarshipEligibleMail(MailMessage $mailMessage, $notifiable): MailMessage
    {
        $gradeDisplay = $this->formatGrade($this->student->grade_level);
        $scholarshipCount = $this->additionalData['scholarship_count'] ?? 0;

        return $mailMessage
            ->subject("New Scholarship Opportunities Available - {$this->student->full_name}")
            ->greeting("Hello {$notifiable->name},")
            ->line("Great news! One of your students is now eligible for new scholarship opportunities.")
            ->line("**Student Details:**")
            ->line("• Name: {$this->student->full_name}")
            ->line("• Current Grade: {$gradeDisplay}")
            ->line("• Available Scholarships: {$scholarshipCount}")
            ->line("• Academic Year: {$this->additionalData['academic_year'] ?? date('Y') . '/' . (date('Y') + 1)}")
            ->line("These scholarship opportunities have become available due to the student's recent grade progression or updated eligibility criteria.")
            ->action('View Available Scholarships', url('/dashboard/partner/scholarships?student_id=' . $this->student->id))
            ->line('We encourage you to review and apply for relevant scholarships as soon as possible.')
            ->line('Thank you for your dedication to student success.');
    }

    /**
     * Mail for bulk progression summary
     */
    private function bulkProgressionSummaryMail(MailMessage $mailMessage, $notifiable): MailMessage
    {
        $totalStudents = $this->additionalData['total_students'] ?? 0;
        $eligibleStudents = $this->additionalData['eligible_students'] ?? 0;
        $completedProgressions = $this->additionalData['completed_progressions'] ?? 0;
        $academicYear = $this->additionalData['academic_year'] ?? date('Y') . '/' . (date('Y') + 1);

        return $mailMessage
            ->subject("Student Progression Summary - {$academicYear}")
            ->greeting("Hello {$notifiable->name},")
            ->line("Here's your student progression summary for the current academic year.")
            ->line("**Summary Statistics:**")
            ->line("• Total Students: {$totalStudents}")
            ->line("• Students Eligible for Progression: {$eligibleStudents}")
            ->line("• Completed Progressions: {$completedProgressions}")
            ->line("• Academic Year: {$academicYear}")
            ->line("This summary includes all progression activities for your organization during the specified period.")
            ->action('View Detailed Report', url('/dashboard/partner/analytics'))
            ->line('For detailed information about individual students, please visit your Partner Dashboard.')
            ->line('Thank you for your continued commitment to student education.');
    }

    /**
     * Helper methods
     */
    private function getNextGrade(string $currentGrade): string
    {
        $gradeProgression = [
            'primary_1' => 'primary_2', 'primary_2' => 'primary_3', 'primary_3' => 'primary_4',
            'primary_4' => 'primary_5', 'primary_5' => 'primary_6', 'primary_6' => 'secondary_1',
            'secondary_1' => 'secondary_2', 'secondary_2' => 'secondary_3', 'secondary_3' => 'secondary_4',
            'secondary_4' => 'secondary_5', 'secondary_5' => 'secondary_6', 'secondary_6' => 'university',
        ];

        return $gradeProgression[$currentGrade] ?? $currentGrade;
    }

    private function formatGrade(string $grade): string
    {
        $gradeMap = [
            'primary_1' => 'Primary 1', 'primary_2' => 'Primary 2', 'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4', 'primary_5' => 'Primary 5', 'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1', 'secondary_2' => 'Secondary 2', 'secondary_3' => 'Secondary 3',
            'secondary_4' => 'Secondary 4', 'secondary_5' => 'Secondary 5', 'secondary_6' => 'Secondary 6',
            'university' => 'University',
        ];

        return $gradeMap[$grade] ?? ucfirst(str_replace('_', ' ', $grade));
    }
}
