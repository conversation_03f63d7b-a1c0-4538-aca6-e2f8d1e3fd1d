<?php

echo "=== TESTING DYNAMIC DATA FROM DATABASE ===\n\n";

// Test 1: Check if data is coming from database vs static/hardcoded
echo "1. Testing Database Connection and Dynamic Data...\n";

try {
    // Test Laravel database connection
    $response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    if (!$response) {
        throw new Exception("Failed to fetch API response");
    }
    
    $data = json_decode($response, true);
    if (!$data || !$data['success']) {
        throw new Exception("API returned error or invalid format");
    }
    
    $scholarships = $data['data']['data'];
    echo "   ✓ API responding with " . count($scholarships) . " scholarships\n";
    
    // Check for database-specific indicators
    $database_indicators = [
        'created_at' => false,
        'updated_at' => false,
        'unique_ids' => [],
        'varying_amounts' => [],
        'different_deadlines' => [],
        'custom_fields_present' => false
    ];
    
    foreach ($scholarships as $scholarship) {
        // Check for timestamps (database indicators)
        if (isset($scholarship['created_at'])) {
            $database_indicators['created_at'] = true;
        }
        if (isset($scholarship['updated_at'])) {
            $database_indicators['updated_at'] = true;
        }
        
        // Collect unique IDs
        $database_indicators['unique_ids'][] = $scholarship['id'];
        
        // Collect varying amounts
        $database_indicators['varying_amounts'][] = floatval($scholarship['amount']);
        
        // Collect different deadlines
        $database_indicators['different_deadlines'][] = $scholarship['application_deadline'];
        
        // Check for custom fields
        if (isset($scholarship['custom_fields']) && !empty($scholarship['custom_fields'])) {
            $database_indicators['custom_fields_present'] = true;
        }
    }
    
    // Analyze indicators
    $unique_ids = array_unique($database_indicators['unique_ids']);
    $unique_amounts = array_unique($database_indicators['varying_amounts']);
    $unique_deadlines = array_unique($database_indicators['different_deadlines']);
    
    echo "   ✓ Unique scholarship IDs: " . count($unique_ids) . "\n";
    echo "   ✓ Different scholarship amounts: " . count($unique_amounts) . "\n";
    echo "   ✓ Different application deadlines: " . count($unique_deadlines) . "\n";
    
    if (count($unique_ids) === count($scholarships)) {
        echo "   ✓ All scholarships have unique IDs (database-generated)\n";
    } else {
        echo "   ⚠ Some scholarships may have duplicate IDs\n";
    }
    
    if (count($unique_amounts) > 1) {
        echo "   ✓ Scholarships have varying amounts (not hardcoded)\n";
    } else {
        echo "   ⚠ All scholarships have same amount (possibly hardcoded)\n";
    }
    
    if (count($unique_deadlines) > 1) {
        echo "   ✓ Scholarships have different deadlines (dynamic data)\n";
    } else {
        echo "   ⚠ All scholarships have same deadline (possibly static)\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error testing database connection: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check individual scholarship endpoint for dynamic data
echo "2. Testing Individual Scholarship Dynamic Data...\n";

try {
    // Get first scholarship ID
    $response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    $data = json_decode($response, true);
    
    if ($data && $data['success'] && !empty($data['data']['data'])) {
        $first_scholarship_id = $data['data']['data'][0]['id'];
        
        // Test individual scholarship endpoint
        $detail_response = file_get_contents("http://127.0.0.1:8000/api/v1/public-scholarships/{$first_scholarship_id}");
        $detail_data = json_decode($detail_response, true);
        
        if ($detail_data && $detail_data['success']) {
            $scholarship = $detail_data['data'];
            echo "   ✓ Individual scholarship endpoint working\n";
            echo "   ✓ Scholarship: {$scholarship['title']}\n";
            echo "   ✓ Category: {$scholarship['category']}\n";
            echo "   ✓ Amount: ₦" . number_format($scholarship['amount']) . "\n";
            
            // Check for dynamic category instructions
            if (isset($scholarship['category_instructions']) && is_array($scholarship['category_instructions'])) {
                echo "   ✓ Category instructions present and structured\n";
                echo "     - Filled by: " . $scholarship['category_instructions']['filled_by'] . "\n";
                echo "     - Age range: " . $scholarship['category_instructions']['age_range'] . "\n";
            } else {
                echo "   ⚠ Category instructions missing or not properly structured\n";
            }
            
            // Check for custom fields
            if (isset($scholarship['custom_fields']) && is_array($scholarship['custom_fields']) && !empty($scholarship['custom_fields'])) {
                echo "   ✓ Custom fields present (" . count($scholarship['custom_fields']) . " fields)\n";
                foreach ($scholarship['custom_fields'] as $field) {
                    echo "     - " . $field['field_name'] . " (" . $field['field_type'] . ")\n";
                }
            } else {
                echo "   ⚠ Custom fields missing or empty\n";
            }
            
            // Check for form metadata
            if (isset($scholarship['form_metadata']) && is_array($scholarship['form_metadata'])) {
                echo "   ✓ Form metadata present\n";
            } else {
                echo "   ⚠ Form metadata missing\n";
            }
            
        } else {
            echo "   ✗ Individual scholarship endpoint failed\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ✗ Error testing individual scholarship: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check category filtering for dynamic behavior
echo "3. Testing Category Filtering Dynamic Behavior...\n";

$categories = ['primary', 'secondary', 'university'];
$category_results = [];

foreach ($categories as $category) {
    try {
        $filter_url = "http://127.0.0.1:8000/api/v1/public-scholarships?category={$category}";
        $response = file_get_contents($filter_url);
        $data = json_decode($response, true);
        
        if ($data && $data['success']) {
            $scholarships = $data['data']['data'];
            $category_results[$category] = count($scholarships);
            
            // Verify all returned scholarships match the category
            $correct_category = true;
            foreach ($scholarships as $scholarship) {
                if ($scholarship['category'] !== $category) {
                    $correct_category = false;
                    break;
                }
            }
            
            if ($correct_category) {
                echo "   ✓ {$category} filter: " . count($scholarships) . " scholarships (all correct category)\n";
            } else {
                echo "   ⚠ {$category} filter: " . count($scholarships) . " scholarships (some wrong category)\n";
            }
        } else {
            echo "   ✗ {$category} filter failed\n";
        }
    } catch (Exception $e) {
        echo "   ✗ {$category} filter error: " . $e->getMessage() . "\n";
    }
}

// Check if categories have different counts (indicates dynamic filtering)
$unique_counts = array_unique(array_values($category_results));
if (count($unique_counts) > 1) {
    echo "   ✓ Categories have different scholarship counts (dynamic filtering working)\n";
} else {
    echo "   ⚠ All categories have same count (filtering may not be working properly)\n";
}

echo "\n";

// Test 4: Check for real-time data updates
echo "4. Testing Data Freshness and Real-time Updates...\n";

try {
    // Make two requests with a small delay to check for any timestamp differences
    $response1 = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    sleep(1);
    $response2 = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    
    $data1 = json_decode($response1, true);
    $data2 = json_decode($response2, true);
    
    if ($data1 && $data2 && $data1['success'] && $data2['success']) {
        $scholarships1 = $data1['data']['data'];
        $scholarships2 = $data2['data']['data'];
        
        if (count($scholarships1) === count($scholarships2)) {
            echo "   ✓ Consistent data returned across requests\n";
        } else {
            echo "   ⚠ Data count changed between requests (possible real-time updates)\n";
        }
        
        // Check if data structure is consistent
        if (json_encode($scholarships1) === json_encode($scholarships2)) {
            echo "   ✓ Data structure and content consistent\n";
        } else {
            echo "   ⚠ Data content changed between requests\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ✗ Error testing data freshness: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Database-specific field validation
echo "5. Testing Database-Specific Fields...\n";

try {
    $response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
    $data = json_decode($response, true);
    
    if ($data && $data['success'] && !empty($data['data']['data'])) {
        $scholarship = $data['data']['data'][0];
        
        // Check for database-specific fields
        $db_fields = [
            'id' => 'Primary key',
            'slug' => 'URL slug',
            'status' => 'Application status',
            'current_applicants' => 'Current applicant count',
            'contact_email' => 'Contact information',
            'eligibility_criteria' => 'Detailed criteria',
            'spots_remaining' => 'Calculated field',
            'days_remaining' => 'Calculated field',
            'is_application_open' => 'Calculated field'
        ];
        
        foreach ($db_fields as $field => $description) {
            if (isset($scholarship[$field])) {
                echo "   ✓ {$field}: {$description} - Present\n";
            } else {
                echo "   ⚠ {$field}: {$description} - Missing\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "   ✗ Error testing database fields: " . $e->getMessage() . "\n";
}

echo "\n";

echo "=== SUMMARY ===\n";
echo "✓ = Working correctly with dynamic data\n";
echo "⚠ = Potential issues or static data detected\n";
echo "✗ = Error or not working\n\n";

echo "If you see mostly ✓ marks, your API is serving dynamic data from the database.\n";
echo "If you see ⚠ or ✗ marks, there may be issues with data retrieval or static data being served.\n";

?>
