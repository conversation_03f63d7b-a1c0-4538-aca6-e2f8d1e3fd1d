

<?php $__env->startSection('title', 'Events Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Events Management</h1>
        <a href="<?php echo e(route('admin.events.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Event
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Events</div>
                            <div class="h4"><?php echo e($stats['total'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Upcoming Events</div>
                            <div class="h4"><?php echo e($stats['upcoming'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Published Events</div>
                            <div class="h4"><?php echo e($stats['published'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Draft Events</div>
                            <div class="h4"><?php echo e($stats['draft'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Events</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search events..." id="searchEvents">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterType">
                        <option value="">All Types</option>
                        <option value="fundraising">Fundraising</option>
                        <option value="volunteer">Volunteer</option>
                        <option value="awareness">Awareness</option>
                        <option value="training">Training</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="ongoing">Ongoing</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="month" class="form-control" id="filterMonth" value="<?php echo e(now()->format('Y-m')); ?>">
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Event</th>
                            <th>Type</th>
                            <th>Date & Time</th>
                            <th>Location</th>
                            <th>Capacity</th>
                            <th>Registrations</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($event->featured_image): ?>
                                        <img src="<?php echo e($event->featured_image); ?>" alt="<?php echo e($event->title); ?>" class="rounded me-3" width="50" height="50" style="object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-info rounded d-flex align-items-center justify-content-center me-3 text-white" style="width: 50px; height: 50px;">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-bold"><?php echo e($event->title); ?></div>
                                        <small class="text-muted"><?php echo e(Str::limit($event->description, 60)); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($event->event_type === 'fundraising' ? 'success' : 
                                    ($event->event_type === 'volunteer' ? 'primary' : 
                                    ($event->event_type === 'awareness' ? 'info' : 
                                    ($event->event_type === 'training' ? 'warning' : 'secondary')))); ?>">
                                    <?php echo e(ucfirst($event->event_type)); ?>

                                </span>
                            </td>
                            <td>
                                <div class="small">
                                    <strong><?php echo e($event->start_datetime->format('M d, Y')); ?></strong><br>
                                    <?php echo e($event->start_datetime->format('g:i A')); ?>

                                    <?php if($event->end_datetime): ?>
                                        - <?php echo e($event->end_datetime->format('g:i A')); ?>

                                    <?php endif; ?>
                                </div>
                                <?php if($event->start_datetime->isToday()): ?>
                                    <span class="badge bg-danger">Today</span>
                                <?php elseif($event->start_datetime->isTomorrow()): ?>
                                    <span class="badge bg-warning">Tomorrow</span>
                                <?php elseif($event->start_datetime->isFuture()): ?>
                                    <span class="badge bg-success"><?php echo e($event->start_datetime->diffForHumans()); ?></span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Past</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($event->venue): ?>
                                    <div class="small">
                                        <strong><?php echo e($event->venue); ?></strong>
                                        <?php if($event->address): ?>
                                            <br><span class="text-muted"><?php echo e(Str::limit($event->address, 40)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Online</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($event->capacity): ?>
                                    <div class="text-center">
                                        <strong><?php echo e(number_format($event->capacity)); ?></strong>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Unlimited</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                    $registrations = $event->registrations ? $event->registrations->count() : 0;
                                    $percentage = $event->capacity ? ($registrations / $event->capacity) * 100 : 0;
                                ?>
                                <div class="text-center">
                                    <strong class="text-<?php echo e($percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success')); ?>">
                                        <?php echo e($registrations); ?>

                                    </strong>
                                    <?php if($event->capacity): ?>
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-<?php echo e($percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success')); ?>" 
                                                 style="width: <?php echo e(min(100, $percentage)); ?>%"></div>
                                        </div>
                                        <small class="text-muted"><?php echo e(round($percentage)); ?>%</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if($event->start_datetime->isFuture()): ?>
                                    <span class="badge bg-success">Upcoming</span>
                                <?php elseif($event->start_datetime->isToday() || ($event->end_datetime && $event->end_datetime->isFuture())): ?>
                                    <span class="badge bg-warning">Ongoing</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Completed</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('event.show', $event->id)); ?>" target="_blank" class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.events.show', $event->id)); ?>" class="btn btn-sm btn-outline-primary" title="Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.events.registrations', $event->id)); ?>" class="btn btn-sm btn-outline-success" title="Registrations">
                                        <i class="fas fa-users"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.events.edit', $event->id)); ?>" class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="<?php echo e(route('admin.events.destroy', $event->id)); ?>" style="display: inline;">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No events found</p>
                                <a href="<?php echo e(route('admin.events.create')); ?>" class="btn btn-primary">Create Your First Event</a>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing <?php echo e($events->firstItem()); ?> to <?php echo e($events->lastItem()); ?> of <?php echo e($events->total()); ?> results
                </div>
                <?php echo e($events->links()); ?>

            </div>
        </div>
    </div>
</div>



<script>


// Search functionality
document.getElementById('searchEvents').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterType').addEventListener('change', function() {
    console.log('Filter type:', this.value);
});

document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterMonth').addEventListener('change', function() {
    console.log('Filter month:', this.value);
});
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/events/index.blade.php ENDPATH**/ ?>