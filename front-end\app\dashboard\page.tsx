"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { apiClient, getCurrentUser, isAuthent<PERSON>ted, logout } from "@/lib/api"
import { Loader2, LogOut, Settings, ExternalLink, Heart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import { useSettings } from "@/hooks/useSettings"

// Dashboard components for different user types
import StudentDashboard from "./student/page"
import VolunteerDashboard from "./volunteer/page"
import PartnerDashboard from "./partner/page"
import UserDashboard from "./user/page"
import AdminDashboard from "./admin/page"

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  role: string
  status: string
  preferences?: any
  short_id?: string
  qr_code_url?: string
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [logoutLoading, setLogoutLoading] = useState(false)
  const router = useRouter()
  const { settings } = useSettings()

  // Get app name from settings or use default
  const appName = settings?.app_name || 'Laravel NGO'
  const appLogo = settings?.app_logo

  const handleLogout = async () => {
    try {
      setLogoutLoading(true)
      await logout()
      router.push('/auth/login')
    } catch (error) {
      console.error('Logout failed:', error)
      // Even if API logout fails, clear local storage and redirect
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      router.push('/auth/login')
    } finally {
      setLogoutLoading(false)
    }
  }

  useEffect(() => {
    // Check if user is authenticated
    if (!isAuthenticated()) {
      console.log('User not authenticated, redirecting to login')
      router.push('/auth/login')
      return
    }

    // Get user data from localStorage first
    const currentUser = getCurrentUser()
    if (currentUser) {
      console.log('Current user from localStorage:', currentUser)

      // Check if partner organization should be redirected to /dashboard/partner
      if (currentUser.role === 'partner_organization' || currentUser.partner_organization_id) {
        console.log('Partner organization detected, redirecting to /dashboard/partner')
        router.push('/dashboard/partner')
        return
      }

      setUser(currentUser)
    }

    // Fetch updated user profile from API
    const fetchUserProfile = async () => {
      try {
        console.log('Fetching user profile from API...')
        const response = await apiClient.getProfile()
        console.log('Profile API response:', response)
        
        if (response.success && (response as any).user) {
          // Handle the correct response structure from Laravel backend
          const userData = (response as any).user
          console.log('Extracted user data:', userData)

          // Check if partner organization should be redirected to /dashboard/partner
          if (userData.role === 'partner_organization' || userData.partner_organization_id) {
            console.log('Partner organization detected from API, redirecting to /dashboard/partner')
            router.push('/dashboard/partner')
            return
          }

          setUser(userData)
          // Update localStorage with fresh data
          localStorage.setItem('user', JSON.stringify(userData))
        } else if (response.success && response.data) {
          // Fallback for different response structure
          const userData = response.data.user || response.data
          console.log('Extracted user data (fallback):', userData)

          // Check if partner organization should be redirected to /dashboard/partner
          if (userData.role === 'partner_organization' || userData.partner_organization_id) {
            console.log('Partner organization detected from API (fallback), redirecting to /dashboard/partner')
            router.push('/dashboard/partner')
            return
          }

          setUser(userData)
          localStorage.setItem('user', JSON.stringify(userData))
        } else {
          console.error('Failed to fetch profile - invalid response structure:', response)
          
          // If profile fetch fails but we have cached user, continue with cached data
          if (!currentUser) {
            router.push('/auth/login')
          }
        }
      } catch (error) {
        console.error('Failed to fetch user profile:', error)
        // If API call fails but we have cached user, continue with cached data
        if (!currentUser) {
          router.push('/auth/login')
        }
      } finally {
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-600">Unable to load user data. Please try logging in again.</p>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role and preferences
  const getUserType = () => {
    // Check user preferences first (from registration)
    if (user.preferences?.user_type) {
      return user.preferences.user_type
    }

    // Fall back to role-based routing
    if (user.role === 'partner_organization') {
      return 'partner'
    } else if (user.role === 'volunteer') {
      return 'volunteer'
    } else if (user.role === 'admin') {
      return 'admin'
    } else if (user.role === 'student') {
      return 'student'
    } else {
      // For regular users, check if they have specific preferences
      // If user has partner_organization_id, they're linked to an institution
      if (user.partner_organization_id) {
        return 'partner'
      }
      return 'user'
    }
  }

  const userType = getUserType()

  // Add a header with logout functionality
  const DashboardHeader = () => (
    <div className="bg-white border-b border-gray-200 px-4 py-3 sm:px-6">
      <div className="flex justify-between items-center max-w-7xl mx-auto">
        <div className="flex items-center space-x-4">
          {appLogo ? (
            <img 
              src={appLogo} 
              alt={`${appName} Logo`}
              className="h-8 w-auto object-contain"
            />
          ) : (
            <div className="relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg">
              <Heart className="absolute inset-0 m-auto h-5 w-5 text-white" />
            </div>
          )}
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            {appName} Dashboard
          </h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="hidden sm:block text-sm text-gray-600">
            Welcome, {user.first_name}
          </div>
          
          {/* Visit Website Button */}
          <Link href="/">
            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <ExternalLink className="h-4 w-4" />
              <span className="hidden sm:inline">Visit Website</span>
              <span className="sm:hidden">Website</span>
            </Button>
          </Link>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                </div>
                <span className="hidden sm:inline">{user.first_name}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="px-2 py-1.5 text-sm font-medium">
                {user.first_name} {user.last_name}
              </div>
              <div className="px-2 py-1.5 text-xs text-gray-500">
                {user.email}
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                // Navigate to profile settings (we'll implement this)
                const element = document.querySelector('[data-value="profile"]') as HTMLElement
                if (element) element.click()
              }}>
                <Settings className="mr-2 h-4 w-4" />
                Profile Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
                    <AlertDialogDescription>
                      You will need to login again to access your dashboard.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleLogout}
                      disabled={logoutLoading}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {logoutLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Logging out...
                        </>
                      ) : (
                        'Logout'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )

  // Render the appropriate dashboard component with header
  const renderDashboard = () => {
    switch (userType) {
      case 'student':
        return <StudentDashboard />
      case 'volunteer':
        return <VolunteerDashboard />
      case 'partner':
        return <PartnerDashboard />
      case 'admin':
        return <AdminDashboard />
      default:
        return <UserDashboard />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />
      {renderDashboard()}
    </div>
  )
} 