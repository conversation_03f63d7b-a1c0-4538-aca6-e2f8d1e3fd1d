@extends('layouts.app')

@section('title', 'User Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen">
            <x-dashboard.sidebar
                userType="user"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                ]"
            />
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Welcome Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">Welcome back, {{ Auth::user()->first_name }}!</h1>
                        <p class="text-gray-600 mt-2">Explore our programs and stay updated with our latest initiatives.</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Member since</p>
                        <p class="text-lg font-semibold text-indigo-600">{{ Auth::user()->created_at->format('M Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Access Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <x-dashboard.card
                    title="Scholarships"
                    subtitle="View available scholarships"
                    icon="graduation-cap"
                    iconColor="blue"
                    :href="route('scholarships')"
                />

                <x-dashboard.card
                    title="Programs"
                    subtitle="Explore our programs"
                    icon="users"
                    iconColor="green"
                    :href="route('programs')"
                />

                <x-dashboard.card
                    title="Events"
                    subtitle="Upcoming events"
                    icon="calendar"
                    iconColor="purple"
                    :href="route('events')"
                />

                <x-dashboard.card
                    title="Resources"
                    subtitle="Educational resources"
                    icon="book"
                    iconColor="orange"
                    href="/resources"
                />
            </div>

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <x-dashboard.stat-card
                    title="Available Scholarships"
                    value="{{ \App\Models\Scholarship::where('status', 'active')->count() }}"
                    icon="graduation-cap"
                    color="blue"
                />

                <x-dashboard.stat-card
                    title="Active Programs"
                    value="{{ \App\Models\Program::where('status', 'active')->count() }}"
                    icon="users"
                    color="green"
                />

                <x-dashboard.stat-card
                    title="Upcoming Events"
                    value="{{ \App\Models\Event::where('start_datetime', '>', now())->count() }}"
                    icon="calendar"
                    color="purple"
                />

                <x-dashboard.stat-card
                    title="Your Activities"
                    value="0"
                    icon="chart-line"
                    color="orange"
                />
            </div>

            <!-- Recent Updates and Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Latest News -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Latest News & Updates</h2>
                    <div class="space-y-4">
                        @php
                            $latestBlogs = \App\Models\BlogPost::latest()->limit(3)->get();
                        @endphp
                        @forelse($latestBlogs as $blog)
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800">{{ $blog->title }}</h3>
                                <p class="text-sm text-gray-600 mt-1">{{ Str::limit($blog->content, 100) }}</p>
                                <p class="text-xs text-gray-500 mt-2">{{ $blog->created_at->diffForHumans() }}</p>
                            </div>
                        @empty
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800">Welcome to HALIMAKQ!</h3>
                                <p class="text-sm text-gray-600 mt-1">Stay tuned for the latest updates and news from our organization.</p>
                                <p class="text-xs text-gray-500 mt-2">Today</p>
                            </div>
                        @endforelse
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('blog') }}" class="text-blue-600 hover:text-blue-800 font-medium">View All News →</a>
                    </div>
                </div>

                <!-- Your Activity -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Your Activity</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-user-circle text-blue-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Profile Created</p>
                                    <p class="text-sm text-gray-600">{{ Auth::user()->created_at->format('M d, Y') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-sign-in-alt text-green-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Last Login</p>
                                    <p class="text-sm text-gray-600">{{ Auth::user()->updated_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center py-4">
                            <p class="text-gray-500 text-sm">More activities will appear here as you interact with our platform</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Educational Resources -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Educational Resources</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-book text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Study Guides</h3>
                        <p class="text-sm text-gray-600 mt-1">Access comprehensive study materials and guides</p>
                        <a href="/resources/study-guides" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">View Guides →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-video text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Video Tutorials</h3>
                        <p class="text-sm text-gray-600 mt-1">Watch educational videos and tutorials</p>
                        <a href="/resources/videos" class="text-green-600 hover:text-green-800 text-sm font-medium mt-2 inline-block">Watch Videos →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-download text-purple-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Downloads</h3>
                        <p class="text-sm text-gray-600 mt-1">Download useful documents and forms</p>
                        <a href="/resources/downloads" class="text-purple-600 hover:text-purple-800 text-sm font-medium mt-2 inline-block">View Downloads →</a>
                    </div>
                </div>
            </div>

            <!-- Note about Application -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-800">Want to Apply for Scholarships?</h3>
                        <p class="text-blue-700 mt-1">To apply for scholarships, you need to register as a University Student or have a Partner Organization apply on your behalf.</p>
                        <div class="mt-3 space-x-4">
                            <a href="{{ route('contact') }}" class="text-blue-600 hover:text-blue-800 font-medium">Contact us for more information →</a>
                            <a href="{{ route('scholarships') }}" class="text-blue-600 hover:text-blue-800 font-medium">View Available Scholarships →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
