@extends('layouts.dashboard')

@section('title', 'User Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="user"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => frontendUrl('scholarships'), 'external' => true],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => frontendUrl('programs'), 'external' => true],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => frontendUrl('events'), 'external' => true],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => frontendUrl('resources'), 'external' => true],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => frontendUrl('contact'), 'external' => true]
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 text-white p-3 rounded-xl shadow-lg hover:bg-green-700 transition-all duration-200">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="user"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                        ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                        ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                        ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 pt-20 lg:pt-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Welcome back, {{ Auth::user()->first_name }}!</h1>
                            <p class="text-green-100 text-sm lg:text-base">Discover opportunities and stay connected with our community</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="{{ route('scholarships') }}" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                View Scholarships
                            </a>
                            <a href="{{ route('programs') }}" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-users mr-2"></i>
                                Our Programs
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Scholarships</p>
                                <p class="text-xl font-bold text-green-600">{{ \App\Models\Scholarship::where('status', 'active')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Programs</p>
                                <p class="text-xl font-bold text-blue-600">{{ \App\Models\Program::where('status', 'active')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-calendar text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Events</p>
                                <p class="text-xl font-bold text-purple-600">{{ \App\Models\Event::where('start_datetime', '>', now())->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-check text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Member</p>
                                <p class="text-xl font-bold text-indigo-600">{{ Auth::user()->created_at->diffInDays(now()) }}d</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                        <!-- Scholarships Card -->
                        <a href="{{ route('scholarships') }}" class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300 block">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-graduation-cap text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 text-sm">Scholarships</h3>
                                        <p class="text-xs text-gray-600">Explore opportunities</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </div>
                        </a>

                        <!-- Programs Card -->
                        <a href="{{ route('programs') }}" class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300 block">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-users text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 text-sm">Programs</h3>
                                        <p class="text-xs text-gray-600">Join our initiatives</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </div>
                        </a>

                        <!-- Events Card -->
                        <a href="{{ route('events') }}" class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300 block">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-calendar text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 text-sm">Events</h3>
                                        <p class="text-xs text-gray-600">Upcoming activities</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </div>
                        </a>

                        <!-- Resources Card -->
                        <a href="/resources" class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300 block">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-book text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 text-sm">Resources</h3>
                                        <p class="text-xs text-gray-600">Educational materials</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </div>
                        </a>
                    </div>

                    <!-- Recent Updates and Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Latest News -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-newspaper text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Latest News & Updates</h2>
                                </div>
                                <a href="{{ route('blog.index') }}" class="text-green-600 hover:text-green-700 font-medium text-sm">View All →</a>
                            </div>
                            <div class="space-y-4">
                                @php
                                    $latestBlogs = \App\Models\BlogPost::latest()->limit(3)->get();
                                @endphp
                                @forelse($latestBlogs as $blog)
                                    <div class="border-l-4 border-green-500 pl-4 py-2">
                                        <h3 class="font-medium text-gray-900 text-sm">{{ $blog->title }}</h3>
                                        <p class="text-xs text-gray-600 mt-1">{{ Str::limit($blog->content, 80) }}</p>
                                        <p class="text-xs text-gray-500 mt-2">{{ $blog->created_at->diffForHumans() }}</p>
                                    </div>
                                @empty
                                    <div class="border-l-4 border-green-500 pl-4 py-2">
                                        <h3 class="font-medium text-gray-900 text-sm">Welcome to HALIMAKQ!</h3>
                                        <p class="text-xs text-gray-600 mt-1">Stay tuned for the latest updates and news from our organization.</p>
                                        <p class="text-xs text-gray-500 mt-2">Today</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>

                        <!-- Your Activity -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-chart-line text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Your Activity</h2>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-circle text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">Profile Created</p>
                                        <p class="text-xs text-gray-600">{{ Auth::user()->created_at->format('M d, Y') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-sign-in-alt text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">Last Login</p>
                                        <p class="text-xs text-gray-600">{{ Auth::user()->updated_at->diffForHumans() }}</p>
                                    </div>
                                </div>

                                <div class="text-center py-4">
                                    <p class="text-gray-500 text-xs">More activities will appear here as you interact with our platform</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Educational Resources -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-book text-white text-sm"></i>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">Educational Resources</h2>
                            </div>
                            <a href="{{ frontendUrl('resources') }}" target="_blank" class="text-green-600 hover:text-green-700 font-medium text-sm">View All →</a>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-book text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Study Guides</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Access comprehensive study materials and guides</p>
                                <a href="/resources/study-guides" class="text-green-600 hover:text-green-700 text-xs font-medium">View Guides →</a>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-video text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Video Tutorials</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Watch educational videos and tutorials</p>
                                <a href="/resources/videos" class="text-green-600 hover:text-green-700 text-xs font-medium">Watch Videos →</a>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-download text-white text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-800 text-sm">Downloads</h3>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">Download useful documents and forms</p>
                                <a href="/resources/downloads" class="text-green-600 hover:text-green-700 text-xs font-medium">View Downloads →</a>
                            </div>
                        </div>
                    </div>

                    <!-- Note about Application -->
                    <div class="bg-green-50 border border-green-200 rounded-xl p-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-info-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-green-800">Want to Apply for Scholarships?</h3>
                                <p class="text-green-700 mt-1 text-sm">To apply for scholarships, you need to register as a University Student or have a Partner Organization apply on your behalf.</p>
                                <div class="mt-4 flex flex-col sm:flex-row gap-3">
                                    <a href="{{ route('contact') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                        <i class="fas fa-envelope mr-2"></i>
                                        Contact Us
                                    </a>
                                    <a href="{{ route('scholarships') }}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-green-600 border border-green-600 rounded-lg transition-colors duration-300">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        View Scholarships
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                const mobileSidebar = document.getElementById('mobile-sidebar');
                const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
                const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

                function openMobileMenu() {
                    mobileSidebar.classList.remove('hidden');
                    setTimeout(() => {
                        mobileSidebarContent.classList.remove('-translate-x-full');
                    }, 10);
                }

                function closeMobileMenu() {
                    mobileSidebarContent.classList.add('-translate-x-full');
                    setTimeout(() => {
                        mobileSidebar.classList.add('hidden');
                    }, 300);
                }

                mobileMenuBtn?.addEventListener('click', openMobileMenu);
                mobileSidebarOverlay?.addEventListener('click', closeMobileMenu);
            });
        </script>
    </div>
</div>
@endsection
