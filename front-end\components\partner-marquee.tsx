"use client"

import { useRef, useEffect, useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface Partner {
  name: string
  logo: string
}

interface PartnerMarqueeProps {
  partners: Partner[]
  speed?: number
  pauseOnHover?: boolean
  className?: string
}

const kofaPartners: Partner[] = [
  { name: "Ahmadu Bello University", logo: "/images/partners/abu.png" },
  { name: "Kaduna State Ministry of Education", logo: "/images/partners/kadmoe.png" },
  { name: "UNICEF Nigeria", logo: "/images/partners/unicef.png" },
  { name: "Save the Children", logo: "/images/partners/savechildren.png" },
  { name: "Education Trust Fund", logo: "/images/partners/etf.png" },
  { name: "Dangote Foundation", logo: "/images/partners/dangote.png" },
  { name: "Ahmadu Bello University", logo: "/images/partners/abu.png" },
  { name: "Kaduna State Ministry of Education", logo: "/images/partners/kadmoe.png" },
  { name: "UNICEF Nigeria", logo: "/images/partners/unicef.png" },
  { name: "Save the Children", logo: "/images/partners/savechildren.png" },
  { name: "Education Trust Fund", logo: "/images/partners/etf.png" },
  { name: "Dangote Foundation", logo: "/images/partners/dangote.png" },
]

export function PartnerMarquee({
  partners = kofaPartners,
  speed = 30,
  pauseOnHover = true,
  className,
}: PartnerMarqueeProps) {
  const [isHovering, setIsHovering] = useState(false)
  const [imagesLoaded, setImagesLoaded] = useState(0)
  const [allLoaded, setAllLoaded] = useState(false)
  const marqueeRef = useRef<HTMLDivElement>(null)

  const handleImageLoad = () => {
    setImagesLoaded((prev) => {
      const newCount = prev + 1
      if (newCount === partners.length * 2) {
        setAllLoaded(true)
      }
      return newCount
    })
  }

  useEffect(() => {
    if (!marqueeRef.current || !allLoaded) return

    const marqueeAnimation = () => {
      if (isHovering && pauseOnHover) return
      if (!marqueeRef.current) return

      const marqueeContent = marqueeRef.current
      if (marqueeContent.scrollLeft >= marqueeContent.scrollWidth / 2) {
        marqueeContent.scrollLeft = 0
      } else {
        marqueeContent.scrollLeft += 1
      }
    }

    const animationInterval = setInterval(marqueeAnimation, speed)
    return () => clearInterval(animationInterval)
  }, [isHovering, pauseOnHover, speed, allLoaded])

  // Double the partners array to create a seamless loop
  const displayPartners = [...partners, ...partners]

  return (
    <div
      className={cn(
        "relative w-full overflow-hidden bg-kofa-light-secondary/5 backdrop-blur-sm rounded-xl shadow-md",
        className,
      )}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div
        ref={marqueeRef}
        className={cn(
          "flex items-center gap-12 py-6 px-4 overflow-x-auto scrollbar-hide transition-opacity duration-500",
          allLoaded ? "opacity-100" : "opacity-0",
        )}
        style={{ scrollBehavior: "auto" }}
      >
        {displayPartners.map((partner, index) => (
          <div
            key={`${partner.name}-${index}`}
            className="flex-shrink-0 flex items-center justify-center bg-white/10 backdrop-blur-sm p-4 rounded-lg hover:bg-white/20 transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md min-w-[180px] h-20"
          >
            <div className="relative w-full h-full">
              <Image
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                fill
                sizes="180px"
                className="object-contain filter drop-shadow-sm"
                onLoad={handleImageLoad}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Gradient overlays for fade effect */}
      <div className="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-kofa-light-secondary to-transparent z-10"></div>
      <div className="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-kofa-light-secondary to-transparent z-10"></div>

      {/* Loading state */}
      {!allLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background">
          <div className="flex gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500 animate-bounce"></div>
            <div className="w-3 h-3 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: "0.2s" }}></div>
            <div className="w-3 h-3 rounded-full bg-green-500 animate-bounce" style={{ animationDelay: "0.4s" }}></div>
          </div>
        </div>
      )}
    </div>
  )
}
