<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\HomeController;
use App\Http\Controllers\Web\AdminController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Landing Page
Route::get('/', [HomeController::class, 'index'])->name('home');

// API Documentation
Route::get('/api-docs', function () {
    return view('api-documentation');
})->name('api.documentation');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/programs', [HomeController::class, 'programs'])->name('programs');
Route::get('/programs/{slug}', [HomeController::class, 'program'])->name('programs.show');
Route::get('/events', [HomeController::class, 'events'])->name('events');
Route::get('/events/{id}', [HomeController::class, 'event'])->name('event.show');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog.index');
Route::get('/blog/{slug}', [HomeController::class, 'blogPost'])->name('blog.show');
Route::get('/team', [HomeController::class, 'team'])->name('team');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/donate', [HomeController::class, 'donate'])->name('donate');
Route::get('/volunteer', [HomeController::class, 'volunteer'])->name('volunteer');
Route::get('/scholarships', [HomeController::class, 'scholarships'])->name('scholarships');
Route::get('/scholarships/{slug}', [HomeController::class, 'scholarship'])->name('scholarships.show');

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect /admin to /admin/dashboard
    Route::get('/', function () {
        return redirect()->route('admin.dashboard');
    });

    // Guest routes
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login'])->name('login.submit');
        Route::get('forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
        Route::post('forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
        Route::get('reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
    });

    // Temporary test route for dashboard (remove after testing)
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Protected routes
    Route::middleware(['auth', 'admin.auth'])->group(function () {
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
        Route::put('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::match(['POST', 'PUT'], '/settings', [SettingsController::class, 'update'])->name('settings.update');
        Route::delete('/settings/logo', [SettingsController::class, 'removeLogo'])->name('settings.logo.remove');
        Route::delete('/settings/reset', [SettingsController::class, 'reset'])->name('settings.reset');
        
        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [AdminController::class, 'users'])->name('index');
            Route::get('/create', [AdminController::class, 'createUser'])->name('create');
            Route::post('/', [AdminController::class, 'storeUser'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showUser'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editUser'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateUser'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyUser'])->name('destroy');
        });
        
        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', [AdminController::class, 'blog'])->name('index');
            Route::get('/posts', [AdminController::class, 'blogPosts'])->name('posts');
            Route::get('/posts/create', [AdminController::class, 'createBlogPost'])->name('posts.create');
            Route::post('/posts', [AdminController::class, 'storeBlogPost'])->name('posts.store');
            Route::get('/posts/{id}/edit', [AdminController::class, 'editBlogPost'])->name('posts.edit');
            Route::put('/posts/{id}', [AdminController::class, 'updateBlogPost'])->name('posts.update');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPost'])->name('posts.destroy');
            Route::get('/categories', [AdminController::class, 'blogCategories'])->name('categories');
            Route::get('/comments', [AdminController::class, 'blogComments'])->name('comments');
        });
        
        // Program Management
        Route::prefix('programs')->name('programs.')->group(function () {
            Route::get('/', [AdminController::class, 'programs'])->name('index');
            Route::get('/create', [AdminController::class, 'createProgram'])->name('create');
            Route::post('/', [AdminController::class, 'storeProgram'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showProgram'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editProgram'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateProgram'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyProgram'])->name('destroy');
        });
        
        // Event Management
        Route::prefix('events')->name('events.')->group(function () {
            Route::get('/', [AdminController::class, 'events'])->name('index');
            Route::get('/create', [AdminController::class, 'createEvent'])->name('create');
            Route::post('/', [AdminController::class, 'storeEvent'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showEvent'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editEvent'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateEvent'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyEvent'])->name('destroy');
            Route::get('/{id}/registrations', [AdminController::class, 'eventRegistrations'])->name('registrations');
        });
        
        // Volunteer Management
        Route::prefix('volunteers')->name('volunteers.')->group(function () {
            Route::get('/', [AdminController::class, 'volunteers'])->name('index');
            Route::get('/create', [AdminController::class, 'createVolunteer'])->name('create');
            Route::post('/', [AdminController::class, 'storeVolunteer'])->name('store');
            // Volunteer Opportunities
            Route::prefix('opportunities')->name('opportunities.')->group(function () {
                Route::get('/', [AdminController::class, 'volunteerOpportunities'])->name('index');
                Route::get('/create', [AdminController::class, 'createVolunteerOpportunity'])->name('create');
                Route::post('/', [AdminController::class, 'storeVolunteerOpportunity'])->name('store');
                Route::get('/{id}', [AdminController::class, 'showVolunteerOpportunity'])->name('show');
                Route::get('/{id}/edit', [AdminController::class, 'editVolunteerOpportunity'])->name('edit');
                Route::put('/{id}', [AdminController::class, 'updateVolunteerOpportunity'])->name('update');
                Route::delete('/{id}', [AdminController::class, 'destroyVolunteerOpportunity'])->name('destroy');
            });
            Route::get('/{id}', [AdminController::class, 'showVolunteer'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editVolunteer'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateVolunteer'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyVolunteer'])->name('destroy');
            Route::get('/applications', [AdminController::class, 'volunteerApplications'])->name('applications');
        });
        
        // Donation Management
        Route::prefix('donations')->name('donations.')->group(function () {
            Route::get('/', [AdminController::class, 'donations'])->name('index');
            Route::get('/create', [AdminController::class, 'createDonation'])->name('create');
            Route::post('/', [AdminController::class, 'storeDonation'])->name('store');
            Route::get('/campaigns', [AdminController::class, 'donationCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createDonationCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeDonationCampaign'])->name('campaigns.store');
            Route::get('/analytics', [AdminController::class, 'donationAnalytics'])->name('analytics');
        });
        
        // Scholarship Management
        Route::prefix('scholarships')->name('scholarships.')->group(function () {
            Route::get('/', [AdminController::class, 'scholarships'])->name('index');
            Route::get('/create', [AdminController::class, 'createScholarship'])->name('create');
            Route::post('/', [AdminController::class, 'storeScholarship'])->name('store');
            Route::get('/applications', [AdminController::class, 'scholarshipApplications'])->name('applications');
            Route::get('/{id}', [AdminController::class, 'showScholarship'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editScholarship'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateScholarship'])->name('update');
            Route::post('/{id}/duplicate', [AdminController::class, 'duplicateScholarship'])->name('duplicate');
            Route::delete('/{id}', [AdminController::class, 'destroyScholarship'])->name('destroy');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipStatus'])->name('update-status');
            Route::put('/bulk-action', [AdminController::class, 'bulkActionScholarships'])->name('bulk-action');
            Route::get('/export', [AdminController::class, 'exportScholarships'])->name('export');
            Route::get('/report', [AdminController::class, 'generateScholarshipReport'])->name('report');
        });

        // Scholarship Application Management
        Route::prefix('scholarship-applications')->name('scholarship-applications.')->group(function () {
            Route::put('/{id}/review', [AdminController::class, 'reviewScholarshipApplication'])->name('review');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipApplicationStatus'])->name('update-status');
        });

        // Additional scholarship application routes for admin panel actions
        Route::prefix('scholarships/applications')->name('scholarships.applications.')->group(function () {
            Route::get('/files/{fileId}/download', [AdminController::class, 'downloadApplicationFile'])->name('files.download');
            Route::post('/files/{fileId}/verify', [AdminController::class, 'toggleFileVerification'])->name('files.verify');
            Route::get('/{applicationId}/download', [AdminController::class, 'downloadApplicationFiles'])->name('download');
            Route::post('/{applicationId}/notify', [AdminController::class, 'sendApplicationNotification'])->name('notify');
            Route::post('/bulk-review', [AdminController::class, 'bulkReviewApplications'])->name('bulk-review');
            Route::post('/bulk-status', [AdminController::class, 'bulkStatusApplications'])->name('bulk-status');
            Route::get('/export', [AdminController::class, 'exportApplications'])->name('export');
        });
        
        // Contact Management
        Route::prefix('contact')->name('contact.')->group(function () {
            Route::get('/', [AdminController::class, 'contactMessages'])->name('index');
            Route::get('/messages', [AdminController::class, 'contactMessages'])->name('messages');
            Route::get('/messages/{id}', [AdminController::class, 'showContactMessage'])->name('messages.show');
            Route::put('/messages/{id}', [AdminController::class, 'updateContactMessage'])->name('messages.update');
            Route::delete('/messages/{id}', [AdminController::class, 'destroyContactMessage'])->name('messages.destroy');
            Route::get('/faq', [AdminController::class, 'faq'])->name('faq');
        });
        
        // Newsletter Management
        Route::prefix('newsletter')->name('newsletter.')->group(function () {
            Route::get('/', [AdminController::class, 'newsletter'])->name('index');
            Route::get('/subscribers', [AdminController::class, 'newsletterSubscribers'])->name('subscribers');
            Route::post('/subscribers/import', [AdminController::class, 'importSubscribers'])->name('subscribers.import');
            Route::get('/subscribers/export', [AdminController::class, 'exportSubscribers'])->name('subscribers.export');
            Route::get('/campaigns', [AdminController::class, 'newsletterCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createNewsletterCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeNewsletterCampaign'])->name('campaigns.store');
            Route::get('/campaigns/{id}/edit', [AdminController::class, 'editNewsletterCampaign'])->name('campaigns.edit');
            Route::put('/campaigns/{id}', [AdminController::class, 'updateNewsletterCampaign'])->name('campaigns.update');
            Route::delete('/campaigns/{id}', [AdminController::class, 'destroyNewsletterCampaign'])->name('campaigns.destroy');
        });
        
        // Team Management
        Route::prefix('team')->name('team.')->group(function () {
            Route::get('/', [AdminController::class, 'teamMembers'])->name('index');
            Route::get('/create', [AdminController::class, 'createTeamMember'])->name('create');
            Route::post('/', [AdminController::class, 'storeTeamMember'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showTeamMember'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editTeamMember'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateTeamMember'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyTeamMember'])->name('destroy');
        });
        
        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [AdminController::class, 'analytics'])->name('index');
            Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
        });
        
        // API Documentation
        Route::get('/api-docs', [AdminController::class, 'apiDocs'])->name('api.docs');
        Route::get('/api-authorization-docs', function () {
            return view('api-authorization-docs');
        })->name('api.authorization.docs');
        
        // Admin API endpoints for web interface
        Route::prefix('api')->group(function () {
            Route::get('/blog/dashboard', [AdminController::class, 'getBlogDashboard'])->name('api.blog.dashboard');
            Route::get('/blog/posts', [AdminController::class, 'getBlogPosts'])->name('api.blog.posts');
            Route::get('/blog/categories', [AdminController::class, 'getBlogCategories'])->name('api.blog.categories');
            Route::get('/blog/comments', [AdminController::class, 'getBlogComments'])->name('api.blog.comments');
            Route::delete('/blog/posts/{id}', [AdminController::class, 'deleteBlogPost'])->name('api.blog.posts.delete');
            Route::post('/blog/posts/{id}/toggle-status', [AdminController::class, 'toggleBlogPostStatus'])->name('api.blog.posts.toggle');
            Route::post('/blog/categories', [AdminController::class, 'storeBlogCategory'])->name('api.admin.blog.categories.store');
            Route::put('/blog/categories/{id}', [AdminController::class, 'updateBlogCategory'])->name('api.admin.blog.categories.update');
            Route::delete('/blog/categories/{id}', [AdminController::class, 'deleteBlogCategory'])->name('api.admin.blog.categories.delete');
            Route::put('/blog/comments/{id}/status', [AdminController::class, 'updateBlogCommentStatus'])->name('api.admin.blog.comments.status');
            Route::delete('/blog/comments/{id}', [AdminController::class, 'deleteBlogComment'])->name('api.admin.blog.comments.delete');
        });

        // Server-side form handling routes
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::post('/categories', [AdminController::class, 'storeBlogCategoryForm'])->name('categories.store.form');
            Route::put('/categories/{id}', [AdminController::class, 'updateBlogCategoryForm'])->name('categories.update.form');
            Route::delete('/categories/{id}', [AdminController::class, 'destroyBlogCategoryForm'])->name('categories.destroy.form');
            Route::put('/comments/{id}/status', [AdminController::class, 'updateBlogCommentStatusForm'])->name('comments.status.form');
            Route::delete('/comments/{id}', [AdminController::class, 'destroyBlogCommentForm'])->name('comments.destroy.form');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPostForm'])->name('posts.destroy.form');
            Route::put('/posts/{id}/status', [AdminController::class, 'toggleBlogPostStatusForm'])->name('posts.status.form');
        });
    });
});
