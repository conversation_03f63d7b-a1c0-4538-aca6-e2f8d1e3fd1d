<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\Web\HomeController;
use App\Http\Controllers\Web\AdminController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Auth\AuthController as PublicAuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\PartnerReportController;
use App\Http\Controllers\PartnerSupportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Laravel landing page should be login page
Route::get('/', function () {
    return redirect()->route('login');
})->name('home');

// Test route for debugging authentication
Route::get('/test-auth', function () {
    return response()->json([
        'authenticated' => Auth::check(),
        'user' => Auth::user(),
        'session' => session()->all()
    ]);
});

// FIX USER ROLES - Update incorrect user roles in database
Route::get('/fix-user-roles', function () {
    $fixes = [];

    // Fix <EMAIL> - should be student (role_id: 2, role: 'student')
    $janeUser = App\Models\User::where('email', '<EMAIL>')->first();
    if ($janeUser) {
        $oldRole = $janeUser->role;
        $oldRoleId = $janeUser->role_id;

        $janeUser->update([
            'role_id' => 2,
            'role' => 'student'
        ]);

        $fixes[] = [
            'email' => '<EMAIL>',
            'action' => 'FIXED',
            'old_role' => $oldRole,
            'old_role_id' => $oldRoleId,
            'new_role' => $janeUser->fresh()->role,
            'new_role_id' => $janeUser->fresh()->role_id,
            'new_dashboard_route' => $janeUser->fresh()->dashboard_route
        ];
    }

    // Verify all users now have correct roles
    $allUsers = App\Models\User::whereIn('email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ])->get();

    $verification = [];
    foreach ($allUsers as $user) {
        $verification[] = [
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'status' => 'VERIFIED'
        ];
    }

    return response()->json([
        'message' => 'User Roles Fixed Successfully',
        'fixes_applied' => $fixes,
        'all_users_verified' => $verification,
        'next_step' => 'Now test login at: ' . route('login')
    ]);
});

// QUICK LOGIN TEST - Test login redirects for each user type
Route::get('/quick-login-test', function () {
    $testUsers = [
        ['email' => '<EMAIL>', 'password' => 'password123', 'expected_redirect' => '/dashboard/user'],
        ['email' => '<EMAIL>', 'password' => 'password123', 'expected_redirect' => '/student-dashboard'],
        ['email' => '<EMAIL>', 'password' => 'password123', 'expected_redirect' => '/partner-dashboard']
    ];

    $results = [];

    foreach ($testUsers as $userData) {
        // Logout any current user
        Auth::logout();

        // Attempt login
        $credentials = [
            'email' => $userData['email'],
            'password' => $userData['password']
        ];

        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            $results[] = [
                'email' => $userData['email'],
                'login_successful' => true,
                'user_role' => $user->role,
                'user_role_id' => $user->role_id,
                'dashboard_route' => $user->dashboard_route,
                'expected_redirect' => $userData['expected_redirect'],
                'redirect_correct' => $user->dashboard_route === $userData['expected_redirect'],
                'status' => $user->dashboard_route === $userData['expected_redirect'] ? 'SUCCESS' : 'FAILED'
            ];

            // Logout after test
            Auth::logout();
        } else {
            $results[] = [
                'email' => $userData['email'],
                'login_successful' => false,
                'error' => 'Login failed - check credentials'
            ];
        }
    }

    return response()->json([
        'message' => 'Quick Login Test Results',
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'successful_logins' => count(array_filter($results, fn($r) => $r['login_successful'] ?? false)),
            'correct_redirects' => count(array_filter($results, fn($r) => ($r['redirect_correct'] ?? false))),
            'all_working' => count(array_filter($results, fn($r) => ($r['status'] ?? '') === 'SUCCESS')) === count($results)
        ],
        'next_step' => 'If all working, test actual login at: ' . route('login')
    ]);
});

// Test actual registration process
Route::get('/test-registration-process', function () {
    // Test data for each role type
    $testData = [
        [
            'role_id' => 1,
            'first_name' => 'Test',
            'last_name' => 'Normal',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567890',
            'expected_role' => 'normal'
        ],
        [
            'role_id' => 2,
            'first_name' => 'Test',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567891',
            'university_name' => 'Test University',
            'course_of_study' => 'Computer Science',
            'expected_role' => 'student'
        ],
        [
            'role_id' => 3,
            'first_name' => 'Test',
            'last_name' => 'Partner',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567892',
            'organization_name' => 'Test School',
            'organization_type' => 'secondary_school',
            'organization_address' => 'Test Address',
            'expected_role' => 'partner'
        ]
    ];

    $results = [];

    foreach ($testData as $data) {
        try {
            // Clean up existing user
            App\Models\User::where('email', $data['email'])->delete();

            // Create request object
            $request = new Illuminate\Http\Request();
            $request->merge($data);

            // Get the controller
            $controller = new App\Http\Controllers\Auth\AuthController();

            // Call the createUser method directly
            $user = $controller->createUser($data);

            $results[] = [
                'email' => $data['email'],
                'role_id_sent' => $data['role_id'],
                'expected_role' => $data['expected_role'],
                'user_created' => true,
                'user_id' => $user->id,
                'actual_role' => $user->role,
                'actual_role_id' => $user->role_id,
                'role_correct' => $user->role === $data['expected_role'],
                'dashboard_route' => $user->dashboard_route,
                'role_methods' => [
                    'isNormalUser' => $user->isNormalUser(),
                    'isUniversityStudent' => $user->isUniversityStudent(),
                    'isPartnerOrganization' => $user->isPartnerOrganization(),
                    'isAdmin' => $user->isAdmin(),
                ]
            ];

        } catch (Exception $e) {
            $results[] = [
                'email' => $data['email'],
                'role_id_sent' => $data['role_id'],
                'expected_role' => $data['expected_role'],
                'user_created' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    return response()->json([
        'message' => 'Registration Process Test Results',
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'successful_creations' => count(array_filter($results, fn($r) => $r['user_created'] ?? false)),
            'correct_roles' => count(array_filter($results, fn($r) => ($r['role_correct'] ?? false))),
            'issues' => array_filter($results, fn($r) => !($r['role_correct'] ?? true))
        ]
    ]);
});

// Check database directly for existing users
Route::get('/check-existing-users', function () {
    $users = App\Models\User::whereIn('email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ])->get(['id', 'email', 'role', 'role_id', 'first_name', 'last_name', 'created_at']);

    $results = [];
    foreach ($users as $user) {
        $results[] = [
            'id' => $user->id,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'created_at' => $user->created_at->format('Y-m-d H:i:s'),
            'dashboard_route' => $user->dashboard_route,
            'issue' => ($user->email === '<EMAIL>' && $user->role !== 'student') ? 'WRONG ROLE - Should be student' : 'OK'
        ];
    }

    return response()->json([
        'message' => 'Existing Users Database Check',
        'users' => $results,
        'jane_student_issue' => '<EMAIL> should have role=student and role_id=2, but has role=' .
            ($users->where('email', '<EMAIL>')->first()->role ?? 'NOT_FOUND') .
            ' and role_id=' .
            ($users->where('email', '<EMAIL>')->first()->role_id ?? 'NOT_FOUND')
    ]);
});

// FINAL TEST - Simulate actual login form submission
Route::post('/test-actual-login', function (Request $request) {
    // Use the same validation as the real login controller
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    $remember = $request->boolean('remember');

    if (Auth::attempt($credentials, $remember)) {
        $request->session()->regenerate();

        $user = Auth::user();

        // Update last login (same as real controller)
        $user->update(['last_login_at' => now()]);

        // This is exactly what the real login controller does
        return redirect($user->dashboard_route)
            ->with('success', 'Welcome back, ' . $user->first_name . '!');
    }

    return back()->withErrors([
        'email' => 'The provided credentials do not match our records.',
    ])->withInput($request->only('email'));
});

// Test form for actual login
Route::get('/test-login-form', function () {
    return view('test-login-form');
});



// DEBUG SPECIFIC USER ROLE ISSUE
Route::get('/debug-user-roles', function () {
    // Get the most recent users
    $recentUsers = App\Models\User::latest()->limit(5)->get(['id', 'email', 'role', 'role_id', 'first_name', 'last_name']);

    $results = [];
    foreach ($recentUsers as $user) {
        $results[] = [
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'name' => $user->first_name . ' ' . $user->last_name,
            'dashboard_route' => $user->dashboard_route,
            'expected_dashboard' => match($user->role) {
                'normal' => '/dashboard/user',
                'student' => '/student-dashboard',
                'partner' => '/partner-dashboard',
                'admin' => '/admin/dashboard',
                default => '/dashboard/user'
            },
            'role_methods' => [
                'isNormalUser' => $user->isNormalUser(),
                'isUniversityStudent' => $user->isUniversityStudent(),
                'isPartnerOrganization' => $user->isPartnerOrganization(),
                'isAdmin' => $user->isAdmin()
            ]
        ];
    }

    return response()->json([
        'message' => 'Recent Users Role Debug',
        'users' => $results,
        'role_mapping' => [
            '1' => 'normal',
            '2' => 'student',
            '3' => 'partner',
            '4' => 'admin'
        ]
    ]);
});

// Test PDF generation
Route::get('/test-pdf/{applicationId}', function ($applicationId) {
    $application = \App\Models\ScholarshipApplication::with(['scholarship', 'user'])
        ->where('id', $applicationId)
        ->first();

    if (!$application) {
        return response()->json(['error' => 'Application not found'], 404);
    }

    return response()->json([
        'application_id' => $application->id,
        'status' => $application->status,
        'user' => $application->user->first_name . ' ' . $application->user->last_name,
        'scholarship' => $application->scholarship->title ?? 'N/A',
        'pdf_available' => $application->status === 'approved',
        'pdf_url' => $application->status === 'approved' ? "/api/v1/scholarship-applications/{$application->id}/pdf" : null
    ]);
});

// COMPREHENSIVE SYSTEM CHECK
Route::get('/system-check', function () {
    $results = [];

    // 1. Check if users exist with correct roles
    $users = App\Models\User::whereIn('email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ])->get(['email', 'role', 'role_id']);

    $results['users'] = $users->map(function($user) {
        return [
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'expected_dashboard' => match($user->role) {
                'normal' => '/dashboard/user',
                'student' => '/student-dashboard',
                'partner' => '/partner-dashboard',
                'admin' => '/admin/dashboard',
                default => '/dashboard/user'
            },
            'dashboard_correct' => $user->dashboard_route === match($user->role) {
                'normal' => '/dashboard/user',
                'student' => '/student-dashboard',
                'partner' => '/partner-dashboard',
                'admin' => '/admin/dashboard',
                default => '/dashboard/user'
            }
        ];
    });

    // 2. Check if routes exist
    $routes = [
        'login' => route('login'),
        'user.dashboard' => route('user.dashboard'),
        'student.dashboard' => route('student.dashboard'),
        'partner.dashboard' => route('partner.dashboard')
    ];

    // 3. Check if views exist
    $views = [
        'auth.login' => view()->exists('auth.login'),
        'dashboards.user.index' => view()->exists('dashboards.user.index'),
        'dashboards.student.index' => view()->exists('dashboards.student.index'),
        'dashboards.partner.index' => view()->exists('dashboards.partner.index')
    ];

    return response()->json([
        'message' => 'Comprehensive System Check',
        'users' => $results['users'],
        'routes' => $routes,
        'views_exist' => $views,
        'summary' => [
            'users_with_correct_roles' => $results['users']->where('dashboard_correct', true)->count(),
            'total_users' => $results['users']->count(),
            'all_views_exist' => !in_array(false, $views),
            'system_ready' => ($results['users']->where('dashboard_correct', true)->count() === $results['users']->count()) && !in_array(false, $views)
        ]
    ]);
});

// SIMPLE LOGIN TEST - Check what happens after login
Route::post('/test-simple-login', function (Request $request) {
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    if (Auth::attempt($credentials)) {
        $user = Auth::user();

        return response()->json([
            'login_success' => true,
            'user_id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'role_checks' => [
                'isNormalUser' => $user->isNormalUser(),
                'isUniversityStudent' => $user->isUniversityStudent(),
                'isPartnerOrganization' => $user->isPartnerOrganization(),
                'isAdmin' => $user->isAdmin()
            ],
            'expected_redirect' => url($user->dashboard_route),
            'message' => 'Login successful - check role and dashboard route'
        ]);
    }

    return response()->json([
        'login_success' => false,
        'message' => 'Invalid credentials'
    ]);
});

// TEST DASHBOARD ACCESS WITHOUT PROTECTION
Route::get('/test-dashboard-access', function () {
    if (!Auth::check()) {
        return response()->json(['error' => 'Not logged in']);
    }

    $user = Auth::user();

    return response()->json([
        'user_info' => [
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route
        ],
        'role_checks' => [
            'isNormalUser' => $user->isNormalUser(),
            'isUniversityStudent' => $user->isUniversityStudent(),
            'isPartnerOrganization' => $user->isPartnerOrganization(),
            'isAdmin' => $user->isAdmin()
        ],
        'dashboard_tests' => [
            'should_access_user_dashboard' => $user->isNormalUser(),
            'should_access_student_dashboard' => $user->isUniversityStudent(),
            'should_access_partner_dashboard' => $user->isPartnerOrganization(),
            'current_dashboard_route' => $user->dashboard_route
        ],
        'route_urls' => [
            'user_dashboard' => url('/dashboard/user'),
            'student_dashboard' => url('/student-dashboard'),
            'partner_dashboard' => url('/partner-dashboard')
        ]
    ]);
});

// COMPREHENSIVE LOGIN DEBUG - Shows step by step what happens
Route::get('/debug-login-process/{email}', function ($email) {
    $user = App\Models\User::where('email', $email)->first();

    if (!$user) {
        return response()->json(['error' => 'User not found']);
    }

    // Simulate what happens during login
    return response()->json([
        'step_1_user_data' => [
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name
        ],
        'step_2_dashboard_route' => [
            'dashboard_route_attribute' => $user->dashboard_route,
            'expected_based_on_role' => match($user->role) {
                'normal' => '/dashboard/user',
                'student' => '/student-dashboard',
                'partner' => '/partner-dashboard',
                'admin' => '/admin/dashboard',
                default => '/dashboard/user'
            }
        ],
        'step_3_role_checks' => [
            'isNormalUser' => $user->isNormalUser(),
            'isUniversityStudent' => $user->isUniversityStudent(),
            'isPartnerOrganization' => $user->isPartnerOrganization(),
            'isAdmin' => $user->isAdmin()
        ],
        'step_4_dashboard_access_logic' => [
            'user_dashboard_access' => $user->isNormalUser() ? 'ALLOWED' : 'REDIRECT_TO_' . $user->dashboard_route,
            'student_dashboard_access' => $user->isUniversityStudent() ? 'ALLOWED' : 'REDIRECT_TO_' . $user->dashboard_route,
            'partner_dashboard_access' => $user->isPartnerOrganization() ? 'ALLOWED' : 'REDIRECT_TO_' . $user->dashboard_route
        ],
        'step_5_potential_issue' => [
            'role_string_matches' => [
                'role_equals_normal' => $user->role === 'normal',
                'role_equals_student' => $user->role === 'student',
                'role_equals_partner' => $user->role === 'partner'
            ],
            'role_comparison_debug' => [
                'role_value' => "'" . $user->role . "'",
                'role_length' => strlen($user->role),
                'role_type' => gettype($user->role)
            ]
        ]
    ]);
});

// TEST DASHBOARD ROUTES DIRECTLY
Route::get('/test-dashboard-routes', function () {
    if (!Auth::check()) {
        return 'Please login first: <a href="/login">Login</a>';
    }

    $user = Auth::user();

    return '
    <h2>Dashboard Route Testing</h2>
    <p><strong>Current User:</strong> ' . $user->email . ' (Role: ' . $user->role . ', Role ID: ' . $user->role_id . ')</p>
    <p><strong>Expected Dashboard:</strong> ' . $user->dashboard_route . '</p>

    <h3>Role Checks:</h3>
    <ul>
        <li>isNormalUser(): ' . ($user->isNormalUser() ? 'TRUE' : 'FALSE') . '</li>
        <li>isUniversityStudent(): ' . ($user->isUniversityStudent() ? 'TRUE' : 'FALSE') . '</li>
        <li>isPartnerOrganization(): ' . ($user->isPartnerOrganization() ? 'TRUE' : 'FALSE') . '</li>
        <li>isAdmin(): ' . ($user->isAdmin() ? 'TRUE' : 'FALSE') . '</li>
    </ul>

    <h3>Test Dashboard Access:</h3>
    <ul>
        <li><a href="/dashboard/user" target="_blank">User Dashboard (/dashboard/user)</a> - Should work for Normal Users</li>
        <li><a href="/student-dashboard" target="_blank">Student Dashboard (/student-dashboard)</a> - Should work for Students</li>
        <li><a href="/partner-dashboard" target="_blank">Partner Dashboard (/partner-dashboard)</a> - Should work for Partners</li>
    </ul>

    <h3>Direct Dashboard Views (Bypass Route Protection):</h3>
    <ul>
        <li><a href="/test-user-dashboard-view" target="_blank">Test User Dashboard View</a></li>
        <li><a href="/test-student-dashboard-view" target="_blank">Test Student Dashboard View</a></li>
        <li><a href="/test-partner-dashboard-view" target="_blank">Test Partner Dashboard View</a></li>
    </ul>
    ';
});

// Test dashboard views directly without route protection
Route::get('/test-user-dashboard-view', function () {
    return view('dashboards.user.index');
})->middleware('auth');

Route::get('/test-student-dashboard-view', function () {
    return view('dashboards.student.index');
})->middleware('auth');

Route::get('/test-partner-dashboard-view', function () {
    return view('dashboards.partner.index');
})->middleware('auth');

// FIX ROLE INCONSISTENCIES IN DATABASE
Route::get('/fix-role-inconsistencies', function () {
    $users = App\Models\User::all();
    $fixed = [];
    $issues = [];

    foreach ($users as $user) {
        $expectedRole = match($user->role_id) {
            1 => 'normal',
            2 => 'student',
            3 => 'partner',
            4 => 'admin',
            default => 'normal'
        };

        if ($user->role !== $expectedRole) {
            $issues[] = [
                'id' => $user->id,
                'email' => $user->email,
                'current_role' => $user->role,
                'current_role_id' => $user->role_id,
                'expected_role' => $expectedRole,
                'action' => 'FIXED'
            ];

            // Fix the role
            $user->update(['role' => $expectedRole]);
            $fixed[] = $user->email;
        }
    }

    return response()->json([
        'message' => 'Role Inconsistency Fix Complete',
        'total_users_checked' => $users->count(),
        'issues_found' => count($issues),
        'users_fixed' => $fixed,
        'detailed_issues' => $issues,
        'role_mapping_reference' => [
            '1' => 'normal',
            '2' => 'student',
            '3' => 'partner',
            '4' => 'admin'
        ]
    ]);
});

// TEST NEW REGISTRATION WITH CORRECT ROLE ASSIGNMENT
Route::get('/test-new-registration', function () {
    return '
    <h2>Test New Registration with Fixed Role Assignment</h2>
    <p>This will test if the role assignment fix is working correctly.</p>

    <div style="display: flex; gap: 20px;">
        <div>
            <h3>Register New Student</h3>
            <form action="/register" method="POST" style="border: 1px solid #ccc; padding: 10px;">
                ' . csrf_field() . '
                <input type="hidden" name="role_id" value="2">
                <p>First Name: <input type="text" name="first_name" value="Fixed" required></p>
                <p>Last Name: <input type="text" name="last_name" value="Student" required></p>
                <p>Email: <input type="email" name="email" value="fixed.student' . time() . '@example.com" required></p>
                <p>Phone: <input type="text" name="phone_number" value="1234567890" required></p>
                <p>Password: <input type="password" name="password" value="password123" required></p>
                <p>Confirm: <input type="password" name="password_confirmation" value="password123" required></p>
                <p>University: <input type="text" name="university_name" value="Fixed University" required></p>
                <p>Course: <input type="text" name="course_of_study" value="Computer Science" required></p>
                <p>Year: <input type="text" name="year_of_study" value="2" required></p>
                <p>Student ID: <input type="text" name="student_id" value="ST2024002" required></p>
                <p>Country: <input type="text" name="country" value="Nigeria" required></p>
                <button type="submit">Register as Student (role_id=2, should be role=student)</button>
            </form>
        </div>

        <div>
            <h3>Register New Partner</h3>
            <form action="/register" method="POST" style="border: 1px solid #ccc; padding: 10px;">
                ' . csrf_field() . '
                <input type="hidden" name="role_id" value="3">
                <p>First Name: <input type="text" name="first_name" value="Fixed" required></p>
                <p>Last Name: <input type="text" name="last_name" value="Partner" required></p>
                <p>Email: <input type="email" name="email" value="fixed.partner' . time() . '@example.com" required></p>
                <p>Phone: <input type="text" name="phone_number" value="1234567891" required></p>
                <p>Password: <input type="password" name="password" value="password123" required></p>
                <p>Confirm: <input type="password" name="password_confirmation" value="password123" required></p>
                <p>Organization: <input type="text" name="organization_name" value="Fixed School" required></p>
                <p>Type: <select name="organization_type" required>
                    <option value="primary_school">Primary School</option>
                    <option value="secondary_school">Secondary School</option>
                </select></p>
                <p>Address: <input type="text" name="organization_address" value="123 Fixed St" required></p>
                <p>Principal: <input type="text" name="principal_name" value="Dr. Fixed" required></p>
                <p>Principal Email: <input type="email" name="principal_email" value="<EMAIL>" required></p>
                <p>Country: <input type="text" name="country" value="Nigeria" required></p>
                <button type="submit">Register as Partner (role_id=3, should be role=partner)</button>
            </form>
        </div>
    </div>

    <h3>After Registration:</h3>
    <p>1. Check the Laravel logs to see the role assignment debug info</p>
    <p>2. Go to <a href="/debug-user-roles">/debug-user-roles</a> to verify the new user has correct role/role_id</p>
    <p>3. Try logging in with the new user to test dashboard redirect</p>
    ';
});

// VERIFY LOGIN AND DASHBOARD REDIRECT AFTER FIXES
Route::get('/verify-login-redirect', function () {
    return '
    <h2>Verify Login and Dashboard Redirect</h2>
    <p>Test login with users that have been fixed to ensure proper dashboard redirects.</p>

    <div style="display: flex; gap: 20px;">
        <div>
            <h3>Test Student Login</h3>
            <form action="/login" method="POST" style="border: 1px solid #ccc; padding: 10px;">
                ' . csrf_field() . '
                <p>Email: <input type="email" name="email" value="<EMAIL>" required style="width: 100%;"></p>
                <p>Password: <input type="password" name="password" value="password123" required style="width: 100%;"></p>
                <button type="submit">Login as Student (should redirect to /student-dashboard)</button>
            </form>
        </div>

        <div>
            <h3>Test Partner Login</h3>
            <form action="/login" method="POST" style="border: 1px solid #ccc; padding: 10px;">
                ' . csrf_field() . '
                <p>Email: <input type="email" name="email" value="<EMAIL>" required style="width: 100%;"></p>
                <p>Password: <input type="password" name="password" value="password123" required style="width: 100%;"></p>
                <button type="submit">Login as Partner (should redirect to /partner-dashboard)</button>
            </form>
        </div>

        <div>
            <h3>Test Normal User Login</h3>
            <form action="/login" method="POST" style="border: 1px solid #ccc; padding: 10px;">
                ' . csrf_field() . '
                <p>Email: <input type="email" name="email" value="<EMAIL>" required style="width: 100%;"></p>
                <p>Password: <input type="password" name="password" value="password123" required style="width: 100%;"></p>
                <button type="submit">Login as Normal User (should redirect to /dashboard/user)</button>
            </form>
        </div>
    </div>

    <h3>Expected Results:</h3>
    <ul>
        <li><strong>Student Login:</strong> Should redirect to /student-dashboard</li>
        <li><strong>Partner Login:</strong> Should redirect to /partner-dashboard</li>
        <li><strong>Normal User Login:</strong> Should redirect to /dashboard/user</li>
    </ul>

    <h3>Debug Links:</h3>
    <ul>
        <li><a href="/debug-user-roles">Check User Roles</a></li>
        <li><a href="/simple-login-test">Simple Login Test (JSON Response)</a></li>
    </ul>
    ';
});

// DEBUG ROUTES - Remove these after testing is complete
// Test registration and login flow
Route::get('/test-registration-flow', function () {
    // Test data for each role
    $testData = [
        1 => [
            'role_id' => 1,
            'first_name' => 'Test',
            'last_name' => 'Normal',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567890',
        ],
        2 => [
            'role_id' => 2,
            'first_name' => 'Test',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567891',
            'university_name' => 'Test University',
            'course_of_study' => 'Computer Science',
        ],
        3 => [
            'role_id' => 3,
            'first_name' => 'Test',
            'last_name' => 'Partner',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567892',
            'organization_name' => 'Test School',
            'organization_type' => 'secondary_school',
            'organization_address' => 'Test Address',
        ]
    ];

    $results = [];

    foreach ($testData as $roleId => $data) {
        try {
            // Delete existing user
            App\Models\User::where('email', $data['email'])->delete();

            // Test registration
            $request = new Illuminate\Http\Request();
            $request->merge($data);

            $controller = new App\Http\Controllers\Auth\AuthController();
            $registrationResponse = $controller->register($request);

            // Check if user was created
            $user = App\Models\User::where('email', $data['email'])->first();

            $results[] = [
                'role_id' => $roleId,
                'email' => $data['email'],
                'registration_successful' => $user ? true : false,
                'user_role' => $user ? $user->role : null,
                'user_role_id' => $user ? $user->role_id : null,
                'dashboard_route' => $user ? $user->dashboard_route : null,
                'registration_response_type' => get_class($registrationResponse),
            ];

        } catch (Exception $e) {
            $results[] = [
                'role_id' => $roleId,
                'email' => $data['email'],
                'error' => $e->getMessage(),
            ];
        }
    }

    return response()->json([
        'message' => 'Registration Flow Test Results',
        'results' => $results
    ]);
});

// Test login flow
Route::get('/test-login-flow', function () {
    $testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    $results = [];

    foreach ($testEmails as $email) {
        try {
            // Logout first
            Auth::logout();

            // Find user
            $user = App\Models\User::where('email', $email)->first();
            if (!$user) {
                $results[] = [
                    'email' => $email,
                    'error' => 'User not found'
                ];
                continue;
            }

            // Test login
            $credentials = ['email' => $email, 'password' => 'password123'];
            if (Auth::attempt($credentials)) {
                $loggedInUser = Auth::user();
                $results[] = [
                    'email' => $email,
                    'login_successful' => true,
                    'user_role' => $loggedInUser->role,
                    'user_role_id' => $loggedInUser->role_id,
                    'dashboard_route' => $loggedInUser->dashboard_route,
                    'role_methods' => [
                        'isNormalUser' => $loggedInUser->isNormalUser(),
                        'isUniversityStudent' => $loggedInUser->isUniversityStudent(),
                        'isPartnerOrganization' => $loggedInUser->isPartnerOrganization(),
                        'isAdmin' => $loggedInUser->isAdmin(),
                    ]
                ];
            } else {
                $results[] = [
                    'email' => $email,
                    'login_successful' => false,
                    'user_role' => $user->role,
                    'user_role_id' => $user->role_id,
                ];
            }

        } catch (Exception $e) {
            $results[] = [
                'email' => $email,
                'error' => $e->getMessage()
            ];
        }
    }

    return response()->json([
        'message' => 'Login Flow Test Results',
        'results' => $results
    ]);
});

// Test actual login redirect
Route::get('/test-login-redirect', function () {
    // Test with a specific user
    $user = App\Models\User::where('email', '<EMAIL>')->first();
    if (!$user) {
        return response()->json(['error' => 'Test user not found']);
    }

    // Logout first
    Auth::logout();

    // Login the user
    Auth::login($user);

    return response()->json([
        'message' => 'User logged in successfully',
        'user' => [
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'role_methods' => [
                'isNormalUser' => $user->isNormalUser(),
                'isUniversityStudent' => $user->isUniversityStudent(),
                'isPartnerOrganization' => $user->isPartnerOrganization(),
                'isAdmin' => $user->isAdmin(),
            ]
        ],
        'next_step' => 'Visit: ' . url($user->dashboard_route),
        'dashboard_url' => url($user->dashboard_route)
    ]);
});

// Check what users exist
Route::get('/check-users', function () {
    $users = App\Models\User::whereIn('email', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ])->get(['id', 'email', 'role', 'role_id', 'first_name', 'last_name']);

    return response()->json([
        'message' => 'Test Users',
        'users' => $users->toArray(),
        'count' => $users->count()
    ]);
});

// Debug login issue - test with actual users from logs
Route::get('/debug-login-issue', function () {
    $testEmails = [
        '<EMAIL>',  // Normal User (role_id: 1)
        '<EMAIL>', // Student (role_id: 2)
        '<EMAIL>'  // Partner (role_id: 3)
    ];

    $results = [];

    foreach ($testEmails as $email) {
        $user = App\Models\User::where('email', $email)->first();
        if ($user) {
            $results[] = [
                'email' => $email,
                'user_id' => $user->id,
                'role' => $user->role,
                'role_id' => $user->role_id,
                'dashboard_route' => $user->dashboard_route,
                'role_methods' => [
                    'isNormalUser' => $user->isNormalUser(),
                    'isUniversityStudent' => $user->isUniversityStudent(),
                    'isPartnerOrganization' => $user->isPartnerOrganization(),
                    'isAdmin' => $user->isAdmin(),
                ],
                'expected_dashboard' => match($user->role) {
                    'normal' => '/dashboard/user',
                    'student' => '/student-dashboard',
                    'partner' => '/partner-dashboard',
                    'admin' => '/admin/dashboard',
                    default => '/dashboard/user'
                },
                'dashboard_matches' => $user->dashboard_route === match($user->role) {
                    'normal' => '/dashboard/user',
                    'student' => '/student-dashboard',
                    'partner' => '/partner-dashboard',
                    'admin' => '/admin/dashboard',
                    default => '/dashboard/user'
                }
            ];
        } else {
            $results[] = [
                'email' => $email,
                'error' => 'User not found'
            ];
        }
    }

    return response()->json([
        'message' => 'Debug Login Issue - User Analysis',
        'results' => $results
    ]);
});

// Manual login test - simulate exact login process
Route::get('/manual-login-test/{email}', function ($email) {
    // Find user
    $user = App\Models\User::where('email', $email)->first();
    if (!$user) {
        return response()->json(['error' => 'User not found: ' . $email]);
    }

    // Logout first
    Auth::logout();

    // Simulate login attempt
    $credentials = ['email' => $email, 'password' => 'password123'];

    if (Auth::attempt($credentials)) {
        $loggedInUser = Auth::user();

        // This is what the login controller does
        $dashboardRoute = $loggedInUser->dashboard_route;

        return response()->json([
            'success' => true,
            'message' => 'Login successful - would redirect to: ' . $dashboardRoute,
            'user' => [
                'id' => $loggedInUser->id,
                'email' => $loggedInUser->email,
                'role' => $loggedInUser->role,
                'role_id' => $loggedInUser->role_id,
                'dashboard_route' => $dashboardRoute,
                'role_checks' => [
                    'isNormalUser' => $loggedInUser->isNormalUser(),
                    'isUniversityStudent' => $loggedInUser->isUniversityStudent(),
                    'isPartnerOrganization' => $loggedInUser->isPartnerOrganization(),
                    'isAdmin' => $loggedInUser->isAdmin(),
                ]
            ],
            'redirect_info' => [
                'dashboard_route' => $dashboardRoute,
                'full_url' => url($dashboardRoute),
                'route_exists' => Route::has(match($loggedInUser->role) {
                    'normal' => 'user.dashboard',
                    'student' => 'student.dashboard',
                    'partner' => 'partner.dashboard',
                    'admin' => 'admin.dashboard',
                    default => 'user.dashboard'
                })
            ],
            'next_step' => 'Visit: ' . url($dashboardRoute) . ' to see the actual dashboard'
        ]);
    } else {
        return response()->json([
            'success' => false,
            'message' => 'Login failed - invalid credentials',
            'user_exists' => true,
            'email' => $email
        ]);
    }
});

// Complete system test - register, login, and verify dashboard
Route::get('/complete-system-test', function () {
    $testData = [
        [
            'role_id' => 1,
            'role_name' => 'normal',
            'first_name' => 'Test',
            'last_name' => 'Normal',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567890',
            'expected_dashboard' => '/dashboard/user'
        ],
        [
            'role_id' => 2,
            'role_name' => 'student',
            'first_name' => 'Test',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567891',
            'university_name' => 'Test University',
            'course_of_study' => 'Computer Science',
            'expected_dashboard' => '/student-dashboard'
        ],
        [
            'role_id' => 3,
            'role_name' => 'partner',
            'first_name' => 'Test',
            'last_name' => 'Partner',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567892',
            'organization_name' => 'Test School',
            'organization_type' => 'secondary_school',
            'organization_address' => 'Test Address',
            'expected_dashboard' => '/partner-dashboard'
        ]
    ];

    $results = [];

    foreach ($testData as $data) {
        try {
            // Clean up existing user
            App\Models\User::where('email', $data['email'])->delete();

            // Test registration
            $request = new Illuminate\Http\Request();
            $request->merge($data);

            $controller = new App\Http\Controllers\Auth\AuthController();
            $registrationResponse = $controller->register($request);

            // Check if user was created
            $user = App\Models\User::where('email', $data['email'])->first();

            if ($user) {
                // Test login
                Auth::logout();
                $credentials = ['email' => $data['email'], 'password' => 'password123'];

                if (Auth::attempt($credentials)) {
                    $loggedInUser = Auth::user();

                    $results[] = [
                        'email' => $data['email'],
                        'role_id' => $data['role_id'],
                        'expected_role' => $data['role_name'],
                        'actual_role' => $user->role,
                        'role_correct' => $user->role === $data['role_name'],
                        'expected_dashboard' => $data['expected_dashboard'],
                        'actual_dashboard' => $user->dashboard_route,
                        'dashboard_correct' => $user->dashboard_route === $data['expected_dashboard'],
                        'registration_success' => true,
                        'login_success' => true,
                        'overall_status' => ($user->role === $data['role_name'] && $user->dashboard_route === $data['expected_dashboard']) ? 'SUCCESS' : 'FAILED',
                        'dashboard_url' => url($user->dashboard_route)
                    ];
                } else {
                    $results[] = [
                        'email' => $data['email'],
                        'registration_success' => true,
                        'login_success' => false,
                        'overall_status' => 'LOGIN_FAILED'
                    ];
                }
            } else {
                $results[] = [
                    'email' => $data['email'],
                    'registration_success' => false,
                    'overall_status' => 'REGISTRATION_FAILED'
                ];
            }

        } catch (Exception $e) {
            $results[] = [
                'email' => $data['email'],
                'error' => $e->getMessage(),
                'overall_status' => 'ERROR'
            ];
        }
    }

    return response()->json([
        'message' => 'Complete System Test Results',
        'summary' => [
            'total_tests' => count($results),
            'successful' => count(array_filter($results, fn($r) => ($r['overall_status'] ?? '') === 'SUCCESS')),
            'failed' => count(array_filter($results, fn($r) => ($r['overall_status'] ?? '') !== 'SUCCESS'))
        ],
        'results' => $results,
        'next_steps' => [
            'If all tests show SUCCESS, the system is working correctly',
            'If tests fail, check the specific error messages',
            'Clear browser cache and try logging in manually',
            'Check Laravel logs for any errors'
        ]
    ]);
});

// Test login redirect functionality
Route::get('/test-login-flow', function () {
    if (!Auth::check()) {
        return response()->json([
            'status' => 'not_logged_in',
            'message' => 'Please login first'
        ]);
    }

    $user = Auth::user();
    return response()->json([
        'status' => 'logged_in',
        'user_id' => $user->id,
        'role_id' => $user->role_id,
        'dashboard_route' => $user->dashboard_route,
        'expected_redirect' => url($user->dashboard_route),
        'can_access_student' => $user->role_id === 2,
        'can_access_partner' => $user->role_id === 3,
        'can_access_admin' => $user->role_id === 4
    ]);
});

// Test login for each user type
Route::get('/test-login/{role_id}', function ($role_id) {
    $testEmails = [
        1 => '<EMAIL>',
        2 => '<EMAIL>',
        3 => '<EMAIL>'
    ];

    if (!isset($testEmails[$role_id])) {
        return response()->json(['error' => 'Invalid role_id']);
    }

    $credentials = [
        'email' => $testEmails[$role_id],
        'password' => 'password123'
    ];

    // Logout current user first
    Auth::logout();

    if (Auth::attempt($credentials)) {
        $user = Auth::user();
        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'role_id' => $user->role_id,
            'role_name' => $user->role_name,
            'dashboard_route' => $user->dashboard_route,
            'redirect_url' => url($user->dashboard_route),
            'message' => 'Login successful - should redirect to: ' . $user->dashboard_route
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Login failed for role ' . $role_id
    ]);
});

// TESTING ROUTES - Remove after verification (can be removed once testing is complete)
// Test admin protection
Route::get('/test-admin-protection', function () {
    $results = [];

    // Test 1: Access admin dashboard without login
    try {
        $response = app('router')->dispatch(request()->create('/admin/dashboard', 'GET'));
        $results['no_auth'] = [
            'status' => 'Should redirect to login',
            'response_status' => $response->getStatusCode()
        ];
    } catch (Exception $e) {
        $results['no_auth'] = [
            'status' => 'Exception caught',
            'message' => $e->getMessage()
        ];
    }

    // Test 2: Login as different user types and try admin access
    $testUsers = [
        ['role_id' => 1, 'email' => '<EMAIL>'],
        ['role_id' => 2, 'email' => '<EMAIL>'],
        ['role_id' => 3, 'email' => '<EMAIL>']
    ];

    foreach ($testUsers as $userData) {
        // Find or create test user
        $user = App\Models\User::where('email', $userData['email'])->first();
        if (!$user) {
            $user = new App\Models\User();
            $user->first_name = 'Test';
            $user->last_name = 'User';
            $user->email = $userData['email'];
            $user->password = Hash::make('password123');
            $user->role_id = $userData['role_id'];
            $user->role = match($userData['role_id']) {
                1 => 'user',
                2 => 'user',
                3 => 'user',
                4 => 'admin',
                default => 'user'
            };
            $user->phone_number = '1234567890';
            $user->country = 'Nigeria';
            $user->status = 'active';
            $user->save();
        }

        // Login as this user
        Auth::login($user);

        // Check if they can access admin dashboard
        $canAccess = $user->isAdmin();
        $expectedAccess = $userData['role_id'] === 4;

        $results['role_' . $userData['role_id']] = [
            'email' => $userData['email'],
            'role_id' => $userData['role_id'],
            'can_access_admin' => $canAccess,
            'expected_access' => $expectedAccess,
            'protection_working' => $canAccess === $expectedAccess,
            'dashboard_route' => $user->dashboard_route
        ];

        Auth::logout();
    }

    return response()->json([
        'message' => 'Admin Protection Test Results',
        'timestamp' => now(),
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'protection_working' => count(array_filter($results, function($r) {
                return isset($r['protection_working']) && $r['protection_working'] === true;
            }))
        ]
    ]);
});

// Test login redirect flow
Route::get('/test-login-redirect', function () {
    // Create test users if they don't exist
    $testUsers = [
        [
            'role_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test1',
            'expected_route' => '/'
        ],
        [
            'role_id' => 2,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test2',
            'expected_route' => '/dashboard/student'
        ],
        [
            'role_id' => 3,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test3',
            'expected_route' => '/dashboard/partner'
        ]
    ];

    $results = [];

    foreach ($testUsers as $userData) {
        // Clean up existing user
        App\Models\User::where('email', $userData['email'])->delete();

        // Create user
        $user = new App\Models\User();
        $user->first_name = $userData['first_name'];
        $user->last_name = $userData['last_name'];
        $user->email = $userData['email'];
        $user->password = Hash::make('password123');
        $user->role_id = $userData['role_id'];
        $user->role = match($userData['role_id']) {
            1 => 'user',
            2 => 'user',
            3 => 'user',
            4 => 'admin',
            default => 'user'
        };
        $user->phone_number = '1234567890';
        $user->country = 'Nigeria';
        $user->status = 'active';
        $user->save();

        // Test login
        Auth::logout();
        $loginSuccess = Auth::attempt([
            'email' => $userData['email'],
            'password' => 'password123'
        ]);

        $loggedInUser = Auth::user();

        $results[] = [
            'role_id' => $userData['role_id'],
            'email' => $userData['email'],
            'login_successful' => $loginSuccess,
            'user_dashboard_route' => $loggedInUser ? $loggedInUser->dashboard_route : null,
            'expected_route' => $userData['expected_route'],
            'routes_match' => $loggedInUser ? ($loggedInUser->dashboard_route === $userData['expected_route']) : false,
            'login_route_url' => route('login'),
            'dashboard_url' => $loggedInUser ? url($loggedInUser->dashboard_route) : null
        ];

        Auth::logout();
    }

    return response()->json([
        'message' => 'Login Redirect Test Results',
        'timestamp' => now(),
        'results' => $results,
        'routes' => [
            'login_route' => route('login'),
            'admin_login_route' => route('admin.login')
        ]
    ]);
});

// Quick test user creation
Route::get('/create-test-user', function () {
    // Clean up existing user
    App\Models\User::where('email', '<EMAIL>')->delete();

    // Create user
    $user = new App\Models\User();
    $user->first_name = 'Quick';
    $user->last_name = 'Test';
    $user->email = '<EMAIL>';
    $user->password = Hash::make('password123');
    $user->role_id = 2; // University Student
    $user->role = 'user';
    $user->phone_number = '1234567890';
    $user->country = 'Nigeria';
    $user->status = 'active';
    $user->save();

    return response()->json([
        'message' => 'Test user created successfully',
        'user' => [
            'id' => $user->id,
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'login_url' => route('login')
        ]
    ]);
});

// Debug login process
Route::post('/debug-login', function (Request $request) {
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    \Log::info('Debug login attempt', [
        'email' => $request->email,
        'ip' => $request->ip()
    ]);

    if (Auth::attempt($credentials)) {
        $user = Auth::user();

        \Log::info('Debug login successful', [
            'user_id' => $user->id,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'redirect_url' => url($user->dashboard_route)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $user->id,
                'email' => $user->email,
                'role_id' => $user->role_id,
                'dashboard_route' => $user->dashboard_route,
                'redirect_url' => url($user->dashboard_route)
            ],
            'next_step' => 'Should redirect to: ' . $user->dashboard_route
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Login failed',
        'credentials_provided' => [
            'email' => $request->email,
            'password_length' => strlen($request->password)
        ]
    ]);
});

// Test registration for all user types
Route::get('/test-registration', function () {
    $results = [];

    // Test data for each role
    $testUsers = [
        [
            'role_id' => 1,
            'role_name' => 'Normal User',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'User1',
            'password' => 'password123',
            'phone_number' => '1234567890',
            'country' => 'Nigeria',
            'expected_route' => '/'
        ],
        [
            'role_id' => 2,
            'role_name' => 'University Student',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'Student',
            'password' => 'password123',
            'phone_number' => '1234567891',
            'country' => 'Nigeria',
            'expected_route' => '/dashboard/student'
        ],
        [
            'role_id' => 3,
            'role_name' => 'Partner Organization',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'Partner',
            'password' => 'password123',
            'phone_number' => '1234567892',
            'country' => 'Nigeria',
            'organization_name' => 'Test School',
            'organization_type' => 'Primary School',
            'expected_route' => '/dashboard/partner'
        ]
    ];

    foreach ($testUsers as $userData) {
        try {
            // Create user
            $user = new App\Models\User();
            $user->first_name = $userData['first_name'];
            $user->last_name = $userData['last_name'];
            $user->email = $userData['email'];
            $user->password = Hash::make($userData['password']);
            $user->role_id = $userData['role_id'];
            $user->role = strtolower(str_replace(' ', '_', $userData['role_name']));
            $user->phone_number = $userData['phone_number'];
            $user->country = $userData['country'];
            $user->status = 'active';

            if (isset($userData['organization_name'])) {
                $user->organization_name = $userData['organization_name'];
                $user->organization_type = $userData['organization_type'];
            }

            $user->save();

            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'user_id' => $user->id,
                'dashboard_route' => $user->dashboard_route,
                'expected_route' => $userData['expected_route'],
                'route_matches' => $user->dashboard_route === $userData['expected_route'],
                'status' => 'success'
            ];

        } catch (Exception $e) {
            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'error' => $e->getMessage(),
                'status' => 'failed'
            ];
        }
    }

    return response()->json([
        'message' => 'Registration test completed',
        'results' => $results
    ]);
});

// Test actual registration form submission
Route::get('/test-form-registration/{role_id}', function ($role_id) {
    // Clear any existing session
    session()->flush();
    Auth::logout();

    $testData = [
        1 => [
            'role_id' => 1,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567890',
            'country' => 'Nigeria'
        ],
        2 => [
            'role_id' => 2,
            'first_name' => 'Jane',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567891',
            'country' => 'Nigeria',
            'university_name' => 'University of Lagos',
            'course_of_study' => 'Computer Science',
            'year_of_study' => '2',
            'student_id' => 'UL2023001'
        ],
        3 => [
            'role_id' => 3,
            'first_name' => 'School',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567892',
            'country' => 'Nigeria',
            'organization_name' => 'Green Valley School',
            'organization_type' => 'primary_school',
            'organization_address' => '123 School Street, Lagos',
            'principal_name' => 'Dr. Smith',
            'principal_email' => '<EMAIL>'
        ]
    ];

    if (!isset($testData[$role_id])) {
        return response()->json(['error' => 'Invalid role_id']);
    }

    try {
        // Delete existing user with same email
        App\Models\User::where('email', $testData[$role_id]['email'])->delete();

        // Create a request instance
        $request = new Illuminate\Http\Request();
        $request->merge($testData[$role_id]);

        // Call the registration controller
        $controller = new App\Http\Controllers\Auth\AuthController();
        $response = $controller->register($request);

        // Check if user was created and logged in
        $user = Auth::user();

        return response()->json([
            'success' => true,
            'role_id' => $role_id,
            'user_created' => $user ? true : false,
            'user_id' => $user ? $user->id : null,
            'dashboard_route' => $user ? $user->dashboard_route : null,
            'response_type' => get_class($response),
            'is_redirect' => $response instanceof Illuminate\Http\RedirectResponse,
            'redirect_url' => $response instanceof Illuminate\Http\RedirectResponse ? $response->getTargetUrl() : null,
            'session_messages' => session()->all()
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Final comprehensive test - Registration and Dashboard Redirect
Route::get('/test-complete-flow', function () {
    $results = [];

    // Test data for each role
    $testUsers = [
        [
            'role_id' => 1,
            'role_name' => 'Normal User',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'User1',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1111111111',
            'country' => 'Nigeria',
            'expected_route' => '/',
            'expected_dashboard' => 'Home Page'
        ],
        [
            'role_id' => 2,
            'role_name' => 'University Student',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'Student',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '2222222222',
            'country' => 'Nigeria',
            'university_name' => 'University of Lagos',
            'course_of_study' => 'Computer Science',
            'year_of_study' => '3',
            'student_id' => 'UL2024001',
            'expected_route' => '/dashboard/student',
            'expected_dashboard' => 'Student Dashboard'
        ],
        [
            'role_id' => 3,
            'role_name' => 'Partner Organization',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'Partner',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '3333333333',
            'country' => 'Nigeria',
            'organization_name' => 'Complete Test School',
            'organization_type' => 'primary_school',
            'organization_address' => '123 Complete Street, Lagos',
            'principal_name' => 'Dr. Complete',
            'principal_email' => '<EMAIL>',
            'expected_route' => '/dashboard/partner',
            'expected_dashboard' => 'Partner Dashboard'
        ]
    ];

    foreach ($testUsers as $userData) {
        try {
            // Clean up existing user
            App\Models\User::where('email', $userData['email'])->delete();

            // Test 1: Create user programmatically
            $user = new App\Models\User();
            $user->first_name = $userData['first_name'];
            $user->last_name = $userData['last_name'];
            $user->email = $userData['email'];
            $user->password = Hash::make($userData['password']);
            $user->role_id = $userData['role_id'];
            $user->role = match($userData['role_id']) {
                1 => 'user',
                2 => 'user',
                3 => 'user',
                4 => 'admin',
                default => 'user'
            };
            $user->phone_number = $userData['phone_number'];
            $user->country = $userData['country'];
            $user->status = 'active';

            // Add role-specific fields
            if (isset($userData['university_name'])) {
                $user->university_name = $userData['university_name'];
                $user->course_of_study = $userData['course_of_study'];
                $user->year_of_study = $userData['year_of_study'];
                $user->student_id = $userData['student_id'];
            }

            if (isset($userData['organization_name'])) {
                $user->organization_name = $userData['organization_name'];
                $user->organization_type = $userData['organization_type'];
                $user->organization_address = $userData['organization_address'];
                $user->principal_name = $userData['principal_name'];
                $user->principal_email = $userData['principal_email'];
            }

            $user->save();

            // Test 2: Login and check redirect
            Auth::logout();
            $loginSuccess = Auth::attempt([
                'email' => $userData['email'],
                'password' => $userData['password']
            ]);

            $loggedInUser = Auth::user();

            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'user_created' => true,
                'user_id' => $user->id,
                'login_successful' => $loginSuccess,
                'dashboard_route' => $loggedInUser ? $loggedInUser->dashboard_route : null,
                'expected_route' => $userData['expected_route'],
                'route_matches' => $loggedInUser ? ($loggedInUser->dashboard_route === $userData['expected_route']) : false,
                'expected_dashboard' => $userData['expected_dashboard'],
                'redirect_url' => $loggedInUser ? url($loggedInUser->dashboard_route) : null,
                'status' => 'success'
            ];

        } catch (Exception $e) {
            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'error' => $e->getMessage(),
                'status' => 'failed'
            ];
        }
    }

    return response()->json([
        'message' => 'Complete Registration and Redirect Flow Test',
        'timestamp' => now(),
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'successful' => count(array_filter($results, fn($r) => $r['status'] === 'success')),
            'failed' => count(array_filter($results, fn($r) => $r['status'] === 'failed')),
            'correct_redirects' => count(array_filter($results, fn($r) => isset($r['route_matches']) && $r['route_matches'] === true))
        ]
    ]);
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Registration
    Route::get('/register', [PublicAuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [PublicAuthController::class, 'register']);

    // Login
    Route::get('/login', [PublicAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [PublicAuthController::class, 'login']);

    // Password Reset
    Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
});

// Logout (for authenticated users)
Route::post('/logout', [PublicAuthController::class, 'logout'])->name('logout')->middleware('auth');

// Role-based Dashboard Routes
Route::middleware('auth')->group(function () {
    // Normal User Dashboard
    Route::get('/dashboard/user', function () {
        if (!Auth::user()->isNormalUser()) {
            return redirect(Auth::user()->dashboard_route);
        }
        return view('dashboards.user.index');
    })->name('user.dashboard');

    // University Student Dashboard
    Route::get('/student-dashboard', [App\Http\Controllers\StudentDashboardController::class, 'index'])->name('student.dashboard');

    // University Student Dashboard Sections
    Route::prefix('student')->name('student.')->middleware('auth')->group(function () {
        Route::get('/applications', [App\Http\Controllers\StudentDashboardController::class, 'applications'])->name('applications');
        Route::get('/scholarships', [App\Http\Controllers\StudentDashboardController::class, 'scholarships'])->name('scholarships');
        Route::get('/profile', [App\Http\Controllers\StudentDashboardController::class, 'profile'])->name('profile');
        Route::post('/profile', [App\Http\Controllers\StudentDashboardController::class, 'updateProfile'])->name('profile.update');
        Route::get('/documents', [App\Http\Controllers\StudentDashboardController::class, 'documents'])->name('documents');
        Route::get('/resources', [App\Http\Controllers\StudentDashboardController::class, 'resources'])->name('resources');
        Route::get('/support', [App\Http\Controllers\StudentDashboardController::class, 'support'])->name('support');
        Route::get('/download-document/{fileId}', [App\Http\Controllers\StudentDashboardController::class, 'downloadDocument'])->name('download.document');
    });

    // Partner Organization Dashboard
    Route::get('/partner-dashboard', [App\Http\Controllers\PartnerDashboardController::class, 'index'])->name('partner.dashboard');

    // Partner Organization Routes
    Route::middleware(['auth'])->prefix('partner')->name('partner.')->group(function () {
        // Students Management
        Route::resource('students', App\Http\Controllers\PartnerStudentController::class);
        Route::post('students/bulk-action', [App\Http\Controllers\PartnerStudentController::class, 'bulkAction'])->name('students.bulk-action');

        // Applications Management
        Route::resource('applications', App\Http\Controllers\PartnerApplicationController::class);

        // Scholarships Management
        Route::get('scholarships', [App\Http\Controllers\PartnerScholarshipController::class, 'index'])->name('scholarships.index');
        Route::get('scholarships/create', [App\Http\Controllers\PartnerScholarshipController::class, 'create'])->name('scholarships.create');
        Route::post('scholarships', [App\Http\Controllers\PartnerScholarshipController::class, 'store'])->name('scholarships.store');
        Route::get('scholarships/{id}', [App\Http\Controllers\PartnerScholarshipController::class, 'show'])->name('scholarships.show');

        // Organization Profile
        Route::get('profile', [App\Http\Controllers\PartnerProfileController::class, 'show'])->name('profile.show');
        Route::get('profile/edit', [App\Http\Controllers\PartnerProfileController::class, 'edit'])->name('profile.edit');
        Route::put('profile', [App\Http\Controllers\PartnerProfileController::class, 'update'])->name('profile.update');
        Route::get('profile/settings', [App\Http\Controllers\PartnerProfileController::class, 'settings'])->name('profile.settings');
        Route::put('profile/settings', [App\Http\Controllers\PartnerProfileController::class, 'updateSettings'])->name('profile.settings.update');
        Route::delete('profile/photo', [App\Http\Controllers\PartnerProfileController::class, 'deletePhoto'])->name('profile.photo.delete');
        Route::get('profile/statistics', [App\Http\Controllers\PartnerProfileController::class, 'getStatistics'])->name('profile.statistics');

        // Reports
        Route::get('reports', [App\Http\Controllers\PartnerReportController::class, 'index'])->name('reports.index');
        Route::get('reports/export', [App\Http\Controllers\PartnerReportController::class, 'export'])->name('reports.export');

        // Support routes
        Route::get('support', [App\Http\Controllers\PartnerSupportController::class, 'index'])->name('support.index');
        Route::get('support/create', [App\Http\Controllers\PartnerSupportController::class, 'create'])->name('support.create');
        Route::post('support', [App\Http\Controllers\PartnerSupportController::class, 'store'])->name('support.store');
        Route::get('support/{id}', [App\Http\Controllers\PartnerSupportController::class, 'show'])->name('support.show');
        Route::get('support-help', [App\Http\Controllers\PartnerSupportController::class, 'help'])->name('support.help');
        Route::get('support-contact', [App\Http\Controllers\PartnerSupportController::class, 'contact'])->name('support.contact');
    });

    // Profile Routes
    Route::get('/profile', function () {
        return view('profile.edit');
    })->name('profile.edit');

    Route::put('/profile', function (Request $request) {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
        ]);

        $user->update($validated);

        return redirect()->route('profile.edit')->with('success', 'Profile updated successfully!');
    })->name('profile.update');
});

// Test route to verify profile.edit route exists
Route::get('/test-profile-route', function () {
    return response()->json([
        'message' => 'Profile route test',
        'profile_edit_route_exists' => Route::has('profile.edit'),
        'profile_edit_url' => route('profile.edit'),
        'user_authenticated' => Auth::check(),
        'user_dashboard_route' => Auth::check() ? Auth::user()->dashboard_route : null
    ]);
});

// Test route to verify events query works
Route::get('/test-events-query', function () {
    try {
        $upcomingEventsCount = \App\Models\Event::where('start_datetime', '>', now())->count();
        $totalEventsCount = \App\Models\Event::count();

        return response()->json([
            'message' => 'Events query test successful',
            'upcoming_events_count' => $upcomingEventsCount,
            'total_events_count' => $totalEventsCount,
            'query_works' => true
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'message' => 'Events query test failed',
            'error' => $e->getMessage(),
            'query_works' => false
        ], 500);
    }
});

// API Documentation Routes
Route::prefix('api-docs')->name('api-docs.')->group(function () {
    Route::get('/', [App\Http\Controllers\ApiDocumentationController::class, 'index'])->name('index');
    Route::get('/authentication', [App\Http\Controllers\ApiDocumentationController::class, 'authentication'])->name('authentication');
    Route::get('/authorization', [App\Http\Controllers\ApiDocumentationController::class, 'authorization'])->name('authorization');
    Route::get('/endpoints', [App\Http\Controllers\ApiDocumentationController::class, 'endpoints'])->name('endpoints');
    Route::get('/endpoints/{category}', [App\Http\Controllers\ApiDocumentationController::class, 'endpoint'])->name('endpoint');
    Route::get('/error-codes', [App\Http\Controllers\ApiDocumentationController::class, 'errorCodes'])->name('error-codes');
    Route::get('/mobile-integration', [App\Http\Controllers\ApiDocumentationController::class, 'mobileIntegration'])->name('mobile-integration');
    Route::get('/code-examples', [App\Http\Controllers\ApiDocumentationController::class, 'codeExamples'])->name('code-examples');
    Route::get('/testing', [App\Http\Controllers\ApiDocumentationController::class, 'testing'])->name('testing');
    Route::post('/test-endpoint', [App\Http\Controllers\ApiDocumentationController::class, 'testEndpoint'])->name('test-endpoint');
});
// ============================================================================
// PUBLIC PAGES REMOVED - Now handled by Next.js frontend
// ============================================================================
// The following routes are now handled by Next.js:
// - /about, /programs, /events, /blog, /team, /contact, /donate, /volunteer, /scholarships
// Laravel only handles dashboard, authentication, and API routes

// Donation callback route
Route::get('/donations/callback', [HomeController::class, 'donationCallback'])->name('donations.callback');

// Test route for debugging
Route::get('/test-donate', function() {
    return 'Test route works';
});

// Scholarship Application Routes (Authenticated Users Only)
Route::middleware(['auth'])->group(function () {
    Route::get('/scholarships/{id}/apply', [App\Http\Controllers\ScholarshipApplicationController::class, 'create'])
        ->name('scholarships.apply');
    Route::post('/scholarships/{id}/apply', [App\Http\Controllers\ScholarshipApplicationController::class, 'store'])
        ->name('scholarships.apply.store');
});

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect /admin to /admin/dashboard
    Route::get('/', function () {
        return redirect()->route('admin.dashboard');
    });

    // Guest routes
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login'])->name('login.submit');
        Route::get('forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
        Route::post('forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
        Route::get('reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
    });

    // Protected routes
    Route::middleware(['auth', 'admin.auth'])->group(function () {
        // Admin Dashboard
        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
        Route::put('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::match(['POST', 'PUT'], '/settings', [SettingsController::class, 'update'])->name('settings.update');
        Route::delete('/settings/logo', [SettingsController::class, 'removeLogo'])->name('settings.logo.remove');
        Route::delete('/settings/reset', [SettingsController::class, 'reset'])->name('settings.reset');
        
        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [AdminController::class, 'users'])->name('index');
            Route::get('/create', [AdminController::class, 'createUser'])->name('create');
            Route::post('/', [AdminController::class, 'storeUser'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showUser'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editUser'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateUser'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyUser'])->name('destroy');
            Route::post('/bulk-action', [AdminController::class, 'bulkActionUsers'])->name('bulk-action');
        });
        
        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', [AdminController::class, 'blog'])->name('index');
            Route::get('/posts', [AdminController::class, 'blogPosts'])->name('posts');
            Route::get('/posts/create', [AdminController::class, 'createBlogPost'])->name('posts.create');
            Route::post('/posts', [AdminController::class, 'storeBlogPost'])->name('posts.store');
            Route::get('/posts/{id}/edit', [AdminController::class, 'editBlogPost'])->name('posts.edit');
            Route::put('/posts/{id}', [AdminController::class, 'updateBlogPost'])->name('posts.update');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPost'])->name('posts.destroy');
            Route::get('/categories', [AdminController::class, 'blogCategories'])->name('categories');
            Route::get('/comments', [AdminController::class, 'blogComments'])->name('comments');
        });
        
        // Program Management
        Route::prefix('programs')->name('programs.')->group(function () {
            Route::get('/', [AdminController::class, 'programs'])->name('index');
            Route::get('/create', [AdminController::class, 'createProgram'])->name('create');
            Route::post('/', [AdminController::class, 'storeProgram'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showProgram'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editProgram'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateProgram'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyProgram'])->name('destroy');
        });
        
        // Event Management
        Route::prefix('events')->name('events.')->group(function () {
            Route::get('/', [AdminController::class, 'events'])->name('index');
            Route::get('/create', [AdminController::class, 'createEvent'])->name('create');
            Route::post('/', [AdminController::class, 'storeEvent'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showEvent'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editEvent'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateEvent'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyEvent'])->name('destroy');
            Route::get('/{id}/registrations', [AdminController::class, 'eventRegistrations'])->name('registrations');
        });
        
        // Volunteer Management
        Route::prefix('volunteers')->name('volunteers.')->group(function () {
            Route::get('/', [AdminController::class, 'volunteers'])->name('index');
            Route::get('/create', [AdminController::class, 'createVolunteer'])->name('create');
            Route::post('/', [AdminController::class, 'storeVolunteer'])->name('store');
            Route::get('/applications', [AdminController::class, 'volunteerApplications'])->name('applications');
            // Volunteer Opportunities
            Route::prefix('opportunities')->name('opportunities.')->group(function () {
                Route::get('/', [AdminController::class, 'volunteerOpportunities'])->name('index');
                Route::get('/create', [AdminController::class, 'createVolunteerOpportunity'])->name('create');
                Route::post('/', [AdminController::class, 'storeVolunteerOpportunity'])->name('store');
                Route::get('/{id}', [AdminController::class, 'showVolunteerOpportunity'])->name('show');
                Route::get('/{id}/edit', [AdminController::class, 'editVolunteerOpportunity'])->name('edit');
                Route::put('/{id}', [AdminController::class, 'updateVolunteerOpportunity'])->name('update');
                Route::delete('/{id}', [AdminController::class, 'destroyVolunteerOpportunity'])->name('destroy');
            });
            Route::get('/{id}', [AdminController::class, 'showVolunteer'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editVolunteer'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateVolunteer'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyVolunteer'])->name('destroy');
        });
        
        // Donation Management
        Route::prefix('donations')->name('donations.')->group(function () {
            Route::get('/', [AdminController::class, 'donations'])->name('index');
            Route::get('/create', [AdminController::class, 'createDonation'])->name('create');
            Route::post('/', [AdminController::class, 'storeDonation'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showDonation'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editDonation'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateDonation'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyDonation'])->name('destroy');
            Route::get('/{id}/receipt', [AdminController::class, 'downloadDonationReceipt'])->name('receipt');
            Route::post('/{id}/generate-receipt', [AdminController::class, 'generateDonationReceipt'])->name('generate-receipt');
            Route::post('/{id}/send-thank-you', [AdminController::class, 'sendThankYouEmail'])->name('send-thank-you');
            Route::post('/{id}/approve', [AdminController::class, 'approveDonation'])->name('approve');
            Route::post('/{id}/reject', [AdminController::class, 'rejectDonation'])->name('reject');
            Route::get('/campaigns', [AdminController::class, 'donationCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createDonationCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeDonationCampaign'])->name('campaigns.store');
            Route::get('/campaigns/{id}', [AdminController::class, 'showDonationCampaign'])->name('campaigns.show');
            Route::get('/campaigns/{id}/edit', [AdminController::class, 'editDonationCampaign'])->name('campaigns.edit');
            Route::put('/campaigns/{id}', [AdminController::class, 'updateDonationCampaign'])->name('campaigns.update');
            Route::delete('/campaigns/{id}', [AdminController::class, 'destroyDonationCampaign'])->name('campaigns.destroy');
            Route::get('/analytics', [AdminController::class, 'donationAnalytics'])->name('analytics');
        });
        
        // Scholarship Management
        Route::prefix('scholarships')->name('scholarships.')->group(function () {
            Route::get('/', [AdminController::class, 'scholarships'])->name('index');
            Route::get('/create', [AdminController::class, 'createScholarship'])->name('create');
            Route::post('/', [AdminController::class, 'storeScholarship'])->name('store');
            Route::get('/applications', [AdminController::class, 'scholarshipApplications'])->name('applications');
            Route::get('/{id}', [AdminController::class, 'showScholarship'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editScholarship'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateScholarship'])->name('update');
            Route::post('/{id}/duplicate', [AdminController::class, 'duplicateScholarship'])->name('duplicate');
            Route::delete('/{id}', [AdminController::class, 'destroyScholarship'])->name('destroy');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipStatus'])->name('update-status');
            Route::put('/bulk-action', [AdminController::class, 'bulkActionScholarships'])->name('bulk-action');
            Route::get('/export', [AdminController::class, 'exportScholarships'])->name('export');
            Route::get('/report', [AdminController::class, 'generateScholarshipReport'])->name('report');
        });

        // Scholarship Application Management
        Route::prefix('scholarship-applications')->name('scholarship-applications.')->group(function () {
            Route::put('/{id}/review', [AdminController::class, 'reviewScholarshipApplication'])->name('review');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipApplicationStatus'])->name('update-status');
        });

        // Additional scholarship application routes for admin panel actions
        Route::prefix('scholarships/applications')->name('scholarships.applications.')->group(function () {
            Route::get('/files/{fileId}/download', [AdminController::class, 'downloadApplicationFile'])->name('files.download');
            Route::post('/files/{fileId}/verify', [AdminController::class, 'toggleFileVerification'])->name('files.verify');
            Route::get('/{applicationId}/download', [AdminController::class, 'downloadApplicationFiles'])->name('download');
            Route::post('/{applicationId}/notify', [AdminController::class, 'sendApplicationNotification'])->name('notify');
            Route::post('/bulk-review', [AdminController::class, 'bulkReviewApplications'])->name('bulk-review');
            Route::post('/bulk-status', [AdminController::class, 'bulkStatusApplications'])->name('bulk-status');
            Route::get('/export', [AdminController::class, 'exportApplications'])->name('export');
        });
        
        // Contact Management
        Route::prefix('contact')->name('contact.')->group(function () {
            Route::get('/', [AdminController::class, 'contactMessages'])->name('index');
            Route::get('/messages', [AdminController::class, 'contactMessages'])->name('messages');
            Route::get('/messages/{id}', [AdminController::class, 'showContactMessage'])->name('messages.show');
            Route::put('/messages/{id}', [AdminController::class, 'updateContactMessage'])->name('messages.update');
            Route::delete('/messages/{id}', [AdminController::class, 'destroyContactMessage'])->name('messages.destroy');
            Route::get('/faq', [AdminController::class, 'faq'])->name('faq');
        });
        
        // Newsletter Management
        Route::prefix('newsletter')->name('newsletter.')->group(function () {
            Route::get('/', [AdminController::class, 'newsletter'])->name('index');
            Route::get('/subscribers', [AdminController::class, 'newsletterSubscribers'])->name('subscribers');
            Route::post('/subscribers/import', [AdminController::class, 'importSubscribers'])->name('subscribers.import');
            Route::get('/subscribers/export', [AdminController::class, 'exportSubscribers'])->name('subscribers.export');
            Route::get('/campaigns', [AdminController::class, 'newsletterCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createNewsletterCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeNewsletterCampaign'])->name('campaigns.store');
            Route::get('/campaigns/{id}/edit', [AdminController::class, 'editNewsletterCampaign'])->name('campaigns.edit');
            Route::put('/campaigns/{id}', [AdminController::class, 'updateNewsletterCampaign'])->name('campaigns.update');
            Route::delete('/campaigns/{id}', [AdminController::class, 'destroyNewsletterCampaign'])->name('campaigns.destroy');
        });
        
        // Team Management
        Route::prefix('team')->name('team.')->group(function () {
            Route::get('/', [AdminController::class, 'teamMembers'])->name('index');
            Route::get('/create', [AdminController::class, 'createTeamMember'])->name('create');
            Route::post('/', [AdminController::class, 'storeTeamMember'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showTeamMember'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editTeamMember'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateTeamMember'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyTeamMember'])->name('destroy');
        });

        // Educational Resources Management
        Route::prefix('educational-resources')->name('educational-resources.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\EducationalResourceController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\EducationalResourceController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\EducationalResourceController::class, 'store'])->name('store');
            Route::get('/{id}', [App\Http\Controllers\Admin\EducationalResourceController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [App\Http\Controllers\Admin\EducationalResourceController::class, 'edit'])->name('edit');
            Route::put('/{id}', [App\Http\Controllers\Admin\EducationalResourceController::class, 'update'])->name('update');
            Route::delete('/{id}', [App\Http\Controllers\Admin\EducationalResourceController::class, 'destroy'])->name('destroy');
        });

        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [AdminController::class, 'analytics'])->name('index');
            Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
        });
        
        // API Documentation
        Route::get('/api-docs', [AdminController::class, 'apiDocs'])->name('api.docs');
        Route::get('/api-authorization-docs', function () {
            return view('api-authorization-docs');
        })->name('api.authorization.docs');
        


        // Server-side form handling routes
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::post('/categories', [AdminController::class, 'storeBlogCategoryForm'])->name('categories.store.form');
            Route::put('/categories/{id}', [AdminController::class, 'updateBlogCategoryForm'])->name('categories.update.form');
            Route::delete('/categories/{id}', [AdminController::class, 'destroyBlogCategoryForm'])->name('categories.destroy.form');
            Route::put('/comments/{id}/status', [AdminController::class, 'updateBlogCommentStatusForm'])->name('comments.status.form');
            Route::delete('/comments/{id}', [AdminController::class, 'destroyBlogCommentForm'])->name('comments.destroy.form');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPostForm'])->name('posts.destroy.form');
            Route::put('/posts/{id}/status', [AdminController::class, 'toggleBlogPostStatusForm'])->name('posts.status.form');
        });
    });
});

// Test database fixes
Route::get('/test-db-fixes', function () {
    try {
        $results = [];

        // Test SupportTicket model
        $ticketCount = \App\Models\SupportTicket::count();
        $results['support_tickets'] = "✅ SupportTicket count: " . $ticketCount;

        // Test partner students query
        $partner = \App\Models\User::where('role', 'partner')->first();
        if ($partner) {
            $studentsByGrade = $partner->partnerStudents()
                ->selectRaw('class, COUNT(*) as count')
                ->groupBy('class')
                ->orderBy('class')
                ->get()
                ->pluck('count', 'class')
                ->toArray();
            $results['students_by_grade'] = "✅ Students by grade: " . json_encode($studentsByGrade);
        } else {
            $results['students_by_grade'] = "⚠️ No partner user found";
        }

        // Test scholarship applications query
        if ($partner) {
            $applicationCount = \App\Models\ScholarshipApplication::where('user_id', $partner->id)
                ->orWhere('school_id', $partner->id)
                ->count();
            $results['applications'] = "✅ Applications count: " . $applicationCount;
        }

        // Test scholarships view exists
        $results['scholarships_view'] = "✅ Scholarships view created";

        return response()->json($results);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
});

// Test Partner Dashboard Access
Route::get('/test-partner-dashboard', function () {
    // Find a partner user
    $partner = \App\Models\User::where('role', 'partner')->first();

    if (!$partner) {
        return response()->json(['error' => 'No partner user found']);
    }

    // Login as partner
    Auth::login($partner);

    try {
        // Test dashboard view directly
        $view = view('dashboards.partner.index');
        $rendered = $view->render();

        return response()->json([
            'success' => true,
            'partner_user' => [
                'id' => $partner->id,
                'email' => $partner->email,
                'role' => $partner->role
            ],
            'dashboard_accessible' => true,
            'view_exists' => true,
            'view_renders' => strlen($rendered) > 0,
            'message' => 'Partner dashboard is working correctly',
            'dashboard_url' => url('/partner-dashboard'),
            'view_size' => strlen($rendered) . ' characters'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'partner_user' => [
                'id' => $partner->id,
                'email' => $partner->email,
                'role' => $partner->role
            ]
        ], 500);
    }
});

// Test route for partner reports
Route::get('/test-partner-reports', function () {
    // Create a test partner user
    $partner = \App\Models\User::where('role', 'partner')->first();

    if (!$partner) {
        return response()->json(['error' => 'No partner user found']);
    }

    // Login as the partner
    Auth::login($partner);

    return response()->json([
        'message' => 'Logged in as partner',
        'partner' => $partner->organization_name,
        'redirect_url' => '/partner/reports'
    ]);
});

// Test route for partner scholarship integration
Route::get('/test-partner-scholarships', function () {
    $user = \App\Models\User::where('role', 'partner')->first();
    if (!$user) {
        return response()->json(['error' => 'No partner user found']);
    }

    // Get scholarships available to partners
    $scholarships = \App\Models\Scholarship::where('status', 'open')
        ->where('is_open', true)
        ->whereIn('category', ['primary', 'secondary'])
        ->where('application_deadline', '>', now())
        ->get();

    // Get partner students
    $students = \App\Models\PartnerStudent::where('partner_id', $user->id)->get();

    return response()->json([
        'partner_user' => [
            'id' => $user->id,
            'name' => $user->name,
            'role' => $user->role,
            'organization_name' => $user->organization_name
        ],
        'available_scholarships' => $scholarships->count(),
        'scholarship_details' => $scholarships->map(function($s) {
            return [
                'id' => $s->id,
                'title' => $s->title,
                'category' => $s->category,
                'amount' => $s->amount,
                'deadline' => $s->application_deadline->format('Y-m-d H:i:s')
            ];
        }),
        'partner_students' => $students->count(),
        'student_details' => $students->map(function($s) {
            return [
                'id' => $s->id,
                'name' => $s->name,
                'class' => $s->class,
                'status' => $s->status
            ];
        }),
        'routes_available' => [
            'scholarships_index' => route('partner.scholarships.index'),
            'scholarships_create' => route('partner.scholarships.create'),
            'scholarships_store' => route('partner.scholarships.store')
        ]
    ]);
});

// Test route for partner student creation
Route::get('/test-partner-student-creation', function () {
    $partner = \App\Models\User::where('role', 'partner')->first();
    if (!$partner) {
        return response()->json(['error' => 'No partner user found']);
    }

    // Test database table structure
    $tableExists = Schema::hasTable('partner_students');
    $columns = [];

    if ($tableExists) {
        $columns = Schema::getColumnListing('partner_students');
    }

    // Test model creation
    try {
        $testData = [
            'partner_id' => $partner->id,
            'name' => 'Test Student',
            'class' => 'Grade 5',
            'gender' => 'male',
            'age' => 12,
            'parent_contact' => '08012345678',
            'parent_name' => 'Test Parent',
            'address' => 'Test Address',
            'status' => 'active'
        ];

        // Check if we can create a student
        $student = new \App\Models\PartnerStudent($testData);
        $canCreate = true;
        $validationErrors = [];

        // Try to save
        try {
            $student->save();
            $studentCreated = true;
            $createdStudent = $student->toArray();
            // Clean up test data
            $student->delete();
        } catch (\Exception $e) {
            $studentCreated = false;
            $creationError = $e->getMessage();
        }

    } catch (\Exception $e) {
        $canCreate = false;
        $modelError = $e->getMessage();
    }

    return response()->json([
        'partner_user' => [
            'id' => $partner->id,
            'name' => $partner->name,
            'role' => $partner->role
        ],
        'database_check' => [
            'table_exists' => $tableExists,
            'columns' => $columns
        ],
        'model_check' => [
            'can_create_instance' => $canCreate ?? false,
            'model_error' => $modelError ?? null,
            'fillable_fields' => \App\Models\PartnerStudent::make()->getFillable()
        ],
        'creation_test' => [
            'test_data' => $testData ?? null,
            'student_created' => $studentCreated ?? false,
            'created_student' => $createdStudent ?? null,
            'creation_error' => $creationError ?? null
        ]
    ]);
});

// Test route for direct student creation form submission
Route::post('/test-student-creation-form', function (Request $request) {
    try {
        // Get a partner user
        $partner = \App\Models\User::where('role', 'partner')->first();
        if (!$partner) {
            return response()->json(['error' => 'No partner user found']);
        }

        // Simulate form data
        $formData = [
            'name' => 'Test Student Form',
            'class' => 'Grade 6',
            'gender' => 'female',
            'age' => 13,
            'parent_contact' => '08098765432',
            'parent_name' => 'Test Parent Form',
            'address' => 'Test Address Form'
        ];

        // Validate the data using the same validation as the controller
        $validator = \Illuminate\Support\Facades\Validator::make($formData, [
            'name' => 'required|string|max:255',
            'class' => 'required|string|max:100',
            'gender' => 'required|in:male,female',
            'age' => 'required|integer|min:5|max:25',
            'parent_contact' => 'required|string|max:20',
            'parent_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'validation_failed' => true,
                'errors' => $validator->errors()
            ]);
        }

        // Add partner_id and status
        $formData['partner_id'] = $partner->id;
        $formData['status'] = 'active';

        // Try to create the student
        $student = \App\Models\PartnerStudent::create($formData);

        return response()->json([
            'success' => true,
            'student_created' => $student->toArray(),
            'partner_info' => [
                'id' => $partner->id,
                'name' => $partner->name,
                'role' => $partner->role
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => true,
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Test route to check partner student form access
Route::get('/test-partner-form-access', function () {
    // Get a partner user
    $partner = \App\Models\User::where('role', 'partner')->first();
    if (!$partner) {
        return response()->json(['error' => 'No partner user found']);
    }

    // Login as the partner user
    Auth::login($partner);

    return response()->json([
        'partner_logged_in' => true,
        'partner_info' => [
            'id' => $partner->id,
            'name' => $partner->name,
            'role' => $partner->role,
            'is_partner_organization' => $partner->isPartnerOrganization()
        ],
        'auth_check' => Auth::check(),
        'auth_user' => Auth::user() ? Auth::user()->toArray() : null,
        'create_form_url' => route('partner.students.create'),
        'store_url' => route('partner.students.store')
    ]);
});

// Comprehensive debug route for partner student system
Route::get('/debug-partner-student-system', function () {
    $results = [];

    try {
        // 1. Check if partner_students table exists
        $tableExists = Schema::hasTable('partner_students');
        $results['table_exists'] = $tableExists;

        if ($tableExists) {
            $results['table_columns'] = Schema::getColumnListing('partner_students');
        }

        // 2. Check if PartnerStudent model can be instantiated
        try {
            $model = new \App\Models\PartnerStudent();
            $results['model_instantiation'] = 'success';
            $results['model_fillable'] = $model->getFillable();
            $results['model_table'] = $model->getTable();
        } catch (\Exception $e) {
            $results['model_instantiation'] = 'failed';
            $results['model_error'] = $e->getMessage();
        }

        // 3. Check if partner users exist
        $partnerUsers = \App\Models\User::where('role', 'partner')->get();
        $results['partner_users_count'] = $partnerUsers->count();
        $results['partner_users'] = $partnerUsers->map(function($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'is_partner_organization' => $user->isPartnerOrganization()
            ];
        });

        // 4. Test student creation with first partner
        if ($partnerUsers->count() > 0) {
            $partner = $partnerUsers->first();

            try {
                $testData = [
                    'partner_id' => $partner->id,
                    'name' => 'Debug Test Student',
                    'class' => 'Grade 7',
                    'gender' => 'male',
                    'age' => 14,
                    'parent_contact' => '08011223344',
                    'parent_name' => 'Debug Parent',
                    'address' => 'Debug Address',
                    'status' => 'active'
                ];

                // Try to create student
                $student = \App\Models\PartnerStudent::create($testData);
                $results['student_creation'] = 'success';
                $results['created_student'] = $student->toArray();

                // Clean up test data
                $student->delete();
                $results['cleanup'] = 'success';

            } catch (\Exception $e) {
                $results['student_creation'] = 'failed';
                $results['creation_error'] = $e->getMessage();
                $results['creation_trace'] = $e->getTraceAsString();
            }
        }

        // 5. Check routes
        $results['routes'] = [
            'create_route_exists' => Route::has('partner.students.create'),
            'store_route_exists' => Route::has('partner.students.store'),
            'index_route_exists' => Route::has('partner.students.index')
        ];

        // 6. Check controller
        try {
            $controller = new \App\Http\Controllers\PartnerStudentController();
            $results['controller_instantiation'] = 'success';
        } catch (\Exception $e) {
            $results['controller_instantiation'] = 'failed';
            $results['controller_error'] = $e->getMessage();
        }

    } catch (\Exception $e) {
        $results['debug_error'] = $e->getMessage();
        $results['debug_trace'] = $e->getTraceAsString();
    }

    return response()->json($results, 200, [], JSON_PRETTY_PRINT);
});

// Comprehensive scholarship application system debug route
Route::get('/debug-scholarship-application-system', function () {
    $results = [];

    try {
        // 1. Check scholarship_applications table structure
        $tableExists = Schema::hasTable('scholarship_applications');
        $results['table_exists'] = $tableExists;

        if ($tableExists) {
            $results['table_columns'] = Schema::getColumnListing('scholarship_applications');
        }

        // 2. Check ScholarshipApplication model
        try {
            $model = new \App\Models\ScholarshipApplication();
            $results['model_instantiation'] = 'success';
            $results['model_fillable'] = $model->getFillable();
            $results['model_table'] = $model->getTable();
            $results['model_casts'] = $model->getCasts();
        } catch (\Exception $e) {
            $results['model_instantiation'] = 'failed';
            $results['model_error'] = $e->getMessage();
        }

        // 3. Check available scholarships
        $scholarships = \App\Models\Scholarship::where('status', 'active')
            ->where('is_open', true)
            ->get(['id', 'title', 'category', 'application_deadline', 'custom_fields']);
        $results['available_scholarships'] = $scholarships->count();
        $results['scholarship_details'] = $scholarships->map(function($scholarship) {
            return [
                'id' => $scholarship->id,
                'title' => $scholarship->title,
                'category' => $scholarship->category,
                'deadline' => $scholarship->application_deadline,
                'has_custom_fields' => !empty($scholarship->custom_fields)
            ];
        });

        // 4. Check existing applications
        $applications = \App\Models\ScholarshipApplication::with(['scholarship', 'user'])
            ->latest()
            ->limit(5)
            ->get();
        $results['existing_applications'] = $applications->count();
        $results['application_samples'] = $applications->map(function($app) {
            return [
                'id' => $app->id,
                'scholarship_title' => $app->scholarship->title ?? 'N/A',
                'user_name' => $app->user->name ?? 'N/A',
                'status' => $app->status,
                'has_form_data' => !empty($app->form_data),
                'has_application_data' => !empty($app->application_data),
                'submitted_at' => $app->submitted_at
            ];
        });

        // 5. Check routes
        $results['routes'] = [
            'scholarships_index' => Route::has('scholarships.index'),
            'scholarships_show' => Route::has('scholarships.show'),
            'scholarship_application_exists' => Route::has('scholarship-application'),
            'api_apply_exists' => Route::has('api.apply-scholarship')
        ];

        // 6. Check controllers
        $controllerTests = [];
        try {
            $apiController = new \App\Http\Controllers\Api\ScholarshipController();
            $controllerTests['api_scholarship_controller'] = 'success';
        } catch (\Exception $e) {
            $controllerTests['api_scholarship_controller'] = 'failed: ' . $e->getMessage();
        }

        try {
            $studentController = new \App\Http\Controllers\Api\StudentScholarshipController();
            $controllerTests['student_scholarship_controller'] = 'success';
        } catch (\Exception $e) {
            $controllerTests['student_scholarship_controller'] = 'failed: ' . $e->getMessage();
        }

        $results['controller_tests'] = $controllerTests;

        // 7. Test form data structure compatibility
        $sampleFormData = [
            'student_full_name' => 'Test Student',
            'age' => '20',
            'current_class' => 'Grade 12',
            'parent_phone' => '08012345678',
            'reason_for_scholarship' => 'Financial need for education'
        ];

        try {
            // Test if we can create an application with sample data
            $testScholarship = $scholarships->first();
            if ($testScholarship) {
                $testUser = \App\Models\User::where('role', 'student')->first();
                if ($testUser) {
                    $testApplication = new \App\Models\ScholarshipApplication([
                        'scholarship_id' => $testScholarship->id,
                        'user_id' => $testUser->id,
                        'form_data' => $sampleFormData,
                        'status' => 'pending'
                    ]);

                    $results['form_data_compatibility'] = 'success';
                    $results['test_application_data'] = [
                        'scholarship_id' => $testScholarship->id,
                        'user_id' => $testUser->id,
                        'form_data_keys' => array_keys($sampleFormData)
                    ];
                } else {
                    $results['form_data_compatibility'] = 'no_student_user';
                }
            } else {
                $results['form_data_compatibility'] = 'no_scholarships';
            }
        } catch (\Exception $e) {
            $results['form_data_compatibility'] = 'failed: ' . $e->getMessage();
        }

    } catch (\Exception $e) {
        $results['debug_error'] = $e->getMessage();
        $results['debug_trace'] = $e->getTraceAsString();
    }

    return response()->json($results, 200, [], JSON_PRETTY_PRINT);
});

// ============================================================================
// FALLBACK ROUTE - Redirect unknown routes to Next.js frontend
// ============================================================================
Route::fallback(function () {
    // If it's an API request, return JSON error
    if (request()->is('api/*')) {
        return response()->json([
            'message' => 'API endpoint not found',
            'error' => 'The requested API endpoint does not exist'
        ], 404);
    }

    // For web requests, redirect to frontend with the requested path
    return redirect()->away(frontendUrl(request()->getPathInfo()));
});
