<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\HomeController;
use App\Http\Controllers\Web\AdminController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Auth\AuthController as PublicAuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Landing Page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Test route for debugging authentication
Route::get('/test-auth', function () {
    return response()->json([
        'authenticated' => Auth::check(),
        'user' => Auth::user(),
        'session' => session()->all()
    ]);
});

// Test login for each user type
Route::get('/test-login/{role_id}', function ($role_id) {
    $testEmails = [
        1 => '<EMAIL>',
        2 => '<EMAIL>',
        3 => '<EMAIL>'
    ];

    if (!isset($testEmails[$role_id])) {
        return response()->json(['error' => 'Invalid role_id']);
    }

    $credentials = [
        'email' => $testEmails[$role_id],
        'password' => 'password123'
    ];

    // Logout current user first
    Auth::logout();

    if (Auth::attempt($credentials)) {
        $user = Auth::user();
        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'role_id' => $user->role_id,
            'role_name' => $user->role_name,
            'dashboard_route' => $user->dashboard_route,
            'redirect_url' => url($user->dashboard_route),
            'message' => 'Login successful - should redirect to: ' . $user->dashboard_route
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Login failed for role ' . $role_id
    ]);
});

// TESTING ROUTES - Remove after verification (can be removed once testing is complete)
// Test admin protection
Route::get('/test-admin-protection', function () {
    $results = [];

    // Test 1: Access admin dashboard without login
    try {
        $response = app('router')->dispatch(request()->create('/admin/dashboard', 'GET'));
        $results['no_auth'] = [
            'status' => 'Should redirect to login',
            'response_status' => $response->getStatusCode()
        ];
    } catch (Exception $e) {
        $results['no_auth'] = [
            'status' => 'Exception caught',
            'message' => $e->getMessage()
        ];
    }

    // Test 2: Login as different user types and try admin access
    $testUsers = [
        ['role_id' => 1, 'email' => '<EMAIL>'],
        ['role_id' => 2, 'email' => '<EMAIL>'],
        ['role_id' => 3, 'email' => '<EMAIL>']
    ];

    foreach ($testUsers as $userData) {
        // Find or create test user
        $user = App\Models\User::where('email', $userData['email'])->first();
        if (!$user) {
            $user = new App\Models\User();
            $user->first_name = 'Test';
            $user->last_name = 'User';
            $user->email = $userData['email'];
            $user->password = Hash::make('password123');
            $user->role_id = $userData['role_id'];
            $user->role = match($userData['role_id']) {
                1 => 'user',
                2 => 'user',
                3 => 'user',
                4 => 'admin',
                default => 'user'
            };
            $user->phone_number = '1234567890';
            $user->country = 'Nigeria';
            $user->status = 'active';
            $user->save();
        }

        // Login as this user
        Auth::login($user);

        // Check if they can access admin dashboard
        $canAccess = $user->isAdmin();
        $expectedAccess = $userData['role_id'] === 4;

        $results['role_' . $userData['role_id']] = [
            'email' => $userData['email'],
            'role_id' => $userData['role_id'],
            'can_access_admin' => $canAccess,
            'expected_access' => $expectedAccess,
            'protection_working' => $canAccess === $expectedAccess,
            'dashboard_route' => $user->dashboard_route
        ];

        Auth::logout();
    }

    return response()->json([
        'message' => 'Admin Protection Test Results',
        'timestamp' => now(),
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'protection_working' => count(array_filter($results, function($r) {
                return isset($r['protection_working']) && $r['protection_working'] === true;
            }))
        ]
    ]);
});

// Test login redirect flow
Route::get('/test-login-redirect', function () {
    // Create test users if they don't exist
    $testUsers = [
        [
            'role_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test1',
            'expected_route' => '/'
        ],
        [
            'role_id' => 2,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test2',
            'expected_route' => '/dashboard/student'
        ],
        [
            'role_id' => 3,
            'email' => '<EMAIL>',
            'first_name' => 'Login',
            'last_name' => 'Test3',
            'expected_route' => '/dashboard/partner'
        ]
    ];

    $results = [];

    foreach ($testUsers as $userData) {
        // Clean up existing user
        App\Models\User::where('email', $userData['email'])->delete();

        // Create user
        $user = new App\Models\User();
        $user->first_name = $userData['first_name'];
        $user->last_name = $userData['last_name'];
        $user->email = $userData['email'];
        $user->password = Hash::make('password123');
        $user->role_id = $userData['role_id'];
        $user->role = match($userData['role_id']) {
            1 => 'user',
            2 => 'user',
            3 => 'user',
            4 => 'admin',
            default => 'user'
        };
        $user->phone_number = '1234567890';
        $user->country = 'Nigeria';
        $user->status = 'active';
        $user->save();

        // Test login
        Auth::logout();
        $loginSuccess = Auth::attempt([
            'email' => $userData['email'],
            'password' => 'password123'
        ]);

        $loggedInUser = Auth::user();

        $results[] = [
            'role_id' => $userData['role_id'],
            'email' => $userData['email'],
            'login_successful' => $loginSuccess,
            'user_dashboard_route' => $loggedInUser ? $loggedInUser->dashboard_route : null,
            'expected_route' => $userData['expected_route'],
            'routes_match' => $loggedInUser ? ($loggedInUser->dashboard_route === $userData['expected_route']) : false,
            'login_route_url' => route('login'),
            'dashboard_url' => $loggedInUser ? url($loggedInUser->dashboard_route) : null
        ];

        Auth::logout();
    }

    return response()->json([
        'message' => 'Login Redirect Test Results',
        'timestamp' => now(),
        'results' => $results,
        'routes' => [
            'login_route' => route('login'),
            'admin_login_route' => route('admin.login')
        ]
    ]);
});

// Quick test user creation
Route::get('/create-test-user', function () {
    // Clean up existing user
    App\Models\User::where('email', '<EMAIL>')->delete();

    // Create user
    $user = new App\Models\User();
    $user->first_name = 'Quick';
    $user->last_name = 'Test';
    $user->email = '<EMAIL>';
    $user->password = Hash::make('password123');
    $user->role_id = 2; // University Student
    $user->role = 'user';
    $user->phone_number = '1234567890';
    $user->country = 'Nigeria';
    $user->status = 'active';
    $user->save();

    return response()->json([
        'message' => 'Test user created successfully',
        'user' => [
            'id' => $user->id,
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'login_url' => route('login')
        ]
    ]);
});

// Debug login process
Route::post('/debug-login', function (Request $request) {
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    \Log::info('Debug login attempt', [
        'email' => $request->email,
        'ip' => $request->ip()
    ]);

    if (Auth::attempt($credentials)) {
        $user = Auth::user();

        \Log::info('Debug login successful', [
            'user_id' => $user->id,
            'role_id' => $user->role_id,
            'dashboard_route' => $user->dashboard_route,
            'redirect_url' => url($user->dashboard_route)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $user->id,
                'email' => $user->email,
                'role_id' => $user->role_id,
                'dashboard_route' => $user->dashboard_route,
                'redirect_url' => url($user->dashboard_route)
            ],
            'next_step' => 'Should redirect to: ' . $user->dashboard_route
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Login failed',
        'credentials_provided' => [
            'email' => $request->email,
            'password_length' => strlen($request->password)
        ]
    ]);
});

// Test registration for all user types
Route::get('/test-registration', function () {
    $results = [];

    // Test data for each role
    $testUsers = [
        [
            'role_id' => 1,
            'role_name' => 'Normal User',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'User1',
            'password' => 'password123',
            'phone_number' => '1234567890',
            'country' => 'Nigeria',
            'expected_route' => '/'
        ],
        [
            'role_id' => 2,
            'role_name' => 'University Student',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'Student',
            'password' => 'password123',
            'phone_number' => '1234567891',
            'country' => 'Nigeria',
            'expected_route' => '/dashboard/student'
        ],
        [
            'role_id' => 3,
            'role_name' => 'Partner Organization',
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'Partner',
            'password' => 'password123',
            'phone_number' => '1234567892',
            'country' => 'Nigeria',
            'organization_name' => 'Test School',
            'organization_type' => 'Primary School',
            'expected_route' => '/dashboard/partner'
        ]
    ];

    foreach ($testUsers as $userData) {
        try {
            // Create user
            $user = new App\Models\User();
            $user->first_name = $userData['first_name'];
            $user->last_name = $userData['last_name'];
            $user->email = $userData['email'];
            $user->password = Hash::make($userData['password']);
            $user->role_id = $userData['role_id'];
            $user->role = strtolower(str_replace(' ', '_', $userData['role_name']));
            $user->phone_number = $userData['phone_number'];
            $user->country = $userData['country'];
            $user->status = 'active';

            if (isset($userData['organization_name'])) {
                $user->organization_name = $userData['organization_name'];
                $user->organization_type = $userData['organization_type'];
            }

            $user->save();

            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'user_id' => $user->id,
                'dashboard_route' => $user->dashboard_route,
                'expected_route' => $userData['expected_route'],
                'route_matches' => $user->dashboard_route === $userData['expected_route'],
                'status' => 'success'
            ];

        } catch (Exception $e) {
            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'error' => $e->getMessage(),
                'status' => 'failed'
            ];
        }
    }

    return response()->json([
        'message' => 'Registration test completed',
        'results' => $results
    ]);
});

// Test actual registration form submission
Route::get('/test-form-registration/{role_id}', function ($role_id) {
    // Clear any existing session
    session()->flush();
    Auth::logout();

    $testData = [
        1 => [
            'role_id' => 1,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567890',
            'country' => 'Nigeria'
        ],
        2 => [
            'role_id' => 2,
            'first_name' => 'Jane',
            'last_name' => 'Student',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567891',
            'country' => 'Nigeria',
            'university_name' => 'University of Lagos',
            'course_of_study' => 'Computer Science',
            'year_of_study' => '2',
            'student_id' => 'UL2023001'
        ],
        3 => [
            'role_id' => 3,
            'first_name' => 'School',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1234567892',
            'country' => 'Nigeria',
            'organization_name' => 'Green Valley School',
            'organization_type' => 'primary_school',
            'organization_address' => '123 School Street, Lagos',
            'principal_name' => 'Dr. Smith',
            'principal_email' => '<EMAIL>'
        ]
    ];

    if (!isset($testData[$role_id])) {
        return response()->json(['error' => 'Invalid role_id']);
    }

    try {
        // Delete existing user with same email
        App\Models\User::where('email', $testData[$role_id]['email'])->delete();

        // Create a request instance
        $request = new Illuminate\Http\Request();
        $request->merge($testData[$role_id]);

        // Call the registration controller
        $controller = new App\Http\Controllers\Auth\AuthController();
        $response = $controller->register($request);

        // Check if user was created and logged in
        $user = Auth::user();

        return response()->json([
            'success' => true,
            'role_id' => $role_id,
            'user_created' => $user ? true : false,
            'user_id' => $user ? $user->id : null,
            'dashboard_route' => $user ? $user->dashboard_route : null,
            'response_type' => get_class($response),
            'is_redirect' => $response instanceof Illuminate\Http\RedirectResponse,
            'redirect_url' => $response instanceof Illuminate\Http\RedirectResponse ? $response->getTargetUrl() : null,
            'session_messages' => session()->all()
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Final comprehensive test - Registration and Dashboard Redirect
Route::get('/test-complete-flow', function () {
    $results = [];

    // Test data for each role
    $testUsers = [
        [
            'role_id' => 1,
            'role_name' => 'Normal User',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'User1',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '1111111111',
            'country' => 'Nigeria',
            'expected_route' => '/',
            'expected_dashboard' => 'Home Page'
        ],
        [
            'role_id' => 2,
            'role_name' => 'University Student',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'Student',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '2222222222',
            'country' => 'Nigeria',
            'university_name' => 'University of Lagos',
            'course_of_study' => 'Computer Science',
            'year_of_study' => '3',
            'student_id' => 'UL2024001',
            'expected_route' => '/dashboard/student',
            'expected_dashboard' => 'Student Dashboard'
        ],
        [
            'role_id' => 3,
            'role_name' => 'Partner Organization',
            'email' => '<EMAIL>',
            'first_name' => 'Complete',
            'last_name' => 'Partner',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '3333333333',
            'country' => 'Nigeria',
            'organization_name' => 'Complete Test School',
            'organization_type' => 'primary_school',
            'organization_address' => '123 Complete Street, Lagos',
            'principal_name' => 'Dr. Complete',
            'principal_email' => '<EMAIL>',
            'expected_route' => '/dashboard/partner',
            'expected_dashboard' => 'Partner Dashboard'
        ]
    ];

    foreach ($testUsers as $userData) {
        try {
            // Clean up existing user
            App\Models\User::where('email', $userData['email'])->delete();

            // Test 1: Create user programmatically
            $user = new App\Models\User();
            $user->first_name = $userData['first_name'];
            $user->last_name = $userData['last_name'];
            $user->email = $userData['email'];
            $user->password = Hash::make($userData['password']);
            $user->role_id = $userData['role_id'];
            $user->role = match($userData['role_id']) {
                1 => 'user',
                2 => 'user',
                3 => 'user',
                4 => 'admin',
                default => 'user'
            };
            $user->phone_number = $userData['phone_number'];
            $user->country = $userData['country'];
            $user->status = 'active';

            // Add role-specific fields
            if (isset($userData['university_name'])) {
                $user->university_name = $userData['university_name'];
                $user->course_of_study = $userData['course_of_study'];
                $user->year_of_study = $userData['year_of_study'];
                $user->student_id = $userData['student_id'];
            }

            if (isset($userData['organization_name'])) {
                $user->organization_name = $userData['organization_name'];
                $user->organization_type = $userData['organization_type'];
                $user->organization_address = $userData['organization_address'];
                $user->principal_name = $userData['principal_name'];
                $user->principal_email = $userData['principal_email'];
            }

            $user->save();

            // Test 2: Login and check redirect
            Auth::logout();
            $loginSuccess = Auth::attempt([
                'email' => $userData['email'],
                'password' => $userData['password']
            ]);

            $loggedInUser = Auth::user();

            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'user_created' => true,
                'user_id' => $user->id,
                'login_successful' => $loginSuccess,
                'dashboard_route' => $loggedInUser ? $loggedInUser->dashboard_route : null,
                'expected_route' => $userData['expected_route'],
                'route_matches' => $loggedInUser ? ($loggedInUser->dashboard_route === $userData['expected_route']) : false,
                'expected_dashboard' => $userData['expected_dashboard'],
                'redirect_url' => $loggedInUser ? url($loggedInUser->dashboard_route) : null,
                'status' => 'success'
            ];

        } catch (Exception $e) {
            $results[] = [
                'role_id' => $userData['role_id'],
                'role_name' => $userData['role_name'],
                'email' => $userData['email'],
                'error' => $e->getMessage(),
                'status' => 'failed'
            ];
        }
    }

    return response()->json([
        'message' => 'Complete Registration and Redirect Flow Test',
        'timestamp' => now(),
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'successful' => count(array_filter($results, fn($r) => $r['status'] === 'success')),
            'failed' => count(array_filter($results, fn($r) => $r['status'] === 'failed')),
            'correct_redirects' => count(array_filter($results, fn($r) => isset($r['route_matches']) && $r['route_matches'] === true))
        ]
    ]);
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Registration
    Route::get('/register', [PublicAuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [PublicAuthController::class, 'register']);

    // Login
    Route::get('/login', [PublicAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [PublicAuthController::class, 'login']);

    // Password Reset
    Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
});

// Logout (for authenticated users)
Route::post('/logout', [PublicAuthController::class, 'logout'])->name('logout')->middleware('auth');

// Role-based Dashboard Routes
Route::middleware('auth')->group(function () {
    // Student Dashboard (Role ID: 2)
    Route::middleware('role:2')->prefix('dashboard/student')->name('student.')->group(function () {
        Route::get('/', function () {
            return view('dashboards.student.index');
        })->name('dashboard');
        // Add more student routes here
    });

    // Partner Organization Dashboard (Role ID: 3)
    Route::middleware('role:3')->prefix('dashboard/partner')->name('partner.')->group(function () {
        Route::get('/', function () {
            return view('dashboards.partner.index');
        })->name('dashboard');
        // Add more partner routes here
    });
});

// API Documentation Routes
Route::prefix('api-docs')->name('api-docs.')->group(function () {
    Route::get('/', [App\Http\Controllers\ApiDocumentationController::class, 'index'])->name('index');
    Route::get('/authentication', [App\Http\Controllers\ApiDocumentationController::class, 'authentication'])->name('authentication');
    Route::get('/authorization', [App\Http\Controllers\ApiDocumentationController::class, 'authorization'])->name('authorization');
    Route::get('/endpoints', [App\Http\Controllers\ApiDocumentationController::class, 'endpoints'])->name('endpoints');
    Route::get('/endpoints/{category}', [App\Http\Controllers\ApiDocumentationController::class, 'endpoint'])->name('endpoint');
    Route::get('/error-codes', [App\Http\Controllers\ApiDocumentationController::class, 'errorCodes'])->name('error-codes');
    Route::get('/mobile-integration', [App\Http\Controllers\ApiDocumentationController::class, 'mobileIntegration'])->name('mobile-integration');
    Route::get('/code-examples', [App\Http\Controllers\ApiDocumentationController::class, 'codeExamples'])->name('code-examples');
    Route::get('/testing', [App\Http\Controllers\ApiDocumentationController::class, 'testing'])->name('testing');
    Route::post('/test-endpoint', [App\Http\Controllers\ApiDocumentationController::class, 'testEndpoint'])->name('test-endpoint');
});
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/programs', [HomeController::class, 'programs'])->name('programs');
Route::get('/programs/{slug}', [HomeController::class, 'program'])->name('programs.show');
Route::get('/events', [HomeController::class, 'events'])->name('events');
Route::get('/events/{id}', [HomeController::class, 'event'])->name('event.show');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog.index');
Route::get('/blog/{slug}', [HomeController::class, 'blogPost'])->name('blog.show');
Route::get('/team', [HomeController::class, 'team'])->name('team');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/donate', [HomeController::class, 'donate'])->name('donate');
Route::get('/volunteer', [HomeController::class, 'volunteer'])->name('volunteer');
Route::get('/scholarships', [HomeController::class, 'scholarships'])->name('scholarships');
Route::get('/scholarships/{slug}', [HomeController::class, 'scholarship'])->name('scholarships.show');

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect /admin to /admin/dashboard
    Route::get('/', function () {
        return redirect()->route('admin.dashboard');
    });

    // Guest routes
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login'])->name('login');
        Route::get('forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
        Route::post('forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
        Route::get('reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
    });

    // Protected routes
    Route::middleware(['auth', 'admin.auth'])->group(function () {
        // Admin Dashboard
        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
        Route::put('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::match(['POST', 'PUT'], '/settings', [SettingsController::class, 'update'])->name('settings.update');
        Route::delete('/settings/logo', [SettingsController::class, 'removeLogo'])->name('settings.logo.remove');
        Route::delete('/settings/reset', [SettingsController::class, 'reset'])->name('settings.reset');
        
        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [AdminController::class, 'users'])->name('index');
            Route::get('/create', [AdminController::class, 'createUser'])->name('create');
            Route::post('/', [AdminController::class, 'storeUser'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showUser'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editUser'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateUser'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyUser'])->name('destroy');
            Route::post('/bulk-action', [AdminController::class, 'bulkActionUsers'])->name('bulk-action');
        });
        
        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', [AdminController::class, 'blog'])->name('index');
            Route::get('/posts', [AdminController::class, 'blogPosts'])->name('posts');
            Route::get('/posts/create', [AdminController::class, 'createBlogPost'])->name('posts.create');
            Route::post('/posts', [AdminController::class, 'storeBlogPost'])->name('posts.store');
            Route::get('/posts/{id}/edit', [AdminController::class, 'editBlogPost'])->name('posts.edit');
            Route::put('/posts/{id}', [AdminController::class, 'updateBlogPost'])->name('posts.update');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPost'])->name('posts.destroy');
            Route::get('/categories', [AdminController::class, 'blogCategories'])->name('categories');
            Route::get('/comments', [AdminController::class, 'blogComments'])->name('comments');
        });
        
        // Program Management
        Route::prefix('programs')->name('programs.')->group(function () {
            Route::get('/', [AdminController::class, 'programs'])->name('index');
            Route::get('/create', [AdminController::class, 'createProgram'])->name('create');
            Route::post('/', [AdminController::class, 'storeProgram'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showProgram'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editProgram'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateProgram'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyProgram'])->name('destroy');
        });
        
        // Event Management
        Route::prefix('events')->name('events.')->group(function () {
            Route::get('/', [AdminController::class, 'events'])->name('index');
            Route::get('/create', [AdminController::class, 'createEvent'])->name('create');
            Route::post('/', [AdminController::class, 'storeEvent'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showEvent'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editEvent'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateEvent'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyEvent'])->name('destroy');
            Route::get('/{id}/registrations', [AdminController::class, 'eventRegistrations'])->name('registrations');
        });
        
        // Volunteer Management
        Route::prefix('volunteers')->name('volunteers.')->group(function () {
            Route::get('/', [AdminController::class, 'volunteers'])->name('index');
            Route::get('/create', [AdminController::class, 'createVolunteer'])->name('create');
            Route::post('/', [AdminController::class, 'storeVolunteer'])->name('store');
            Route::get('/applications', [AdminController::class, 'volunteerApplications'])->name('applications');
            // Volunteer Opportunities
            Route::prefix('opportunities')->name('opportunities.')->group(function () {
                Route::get('/', [AdminController::class, 'volunteerOpportunities'])->name('index');
                Route::get('/create', [AdminController::class, 'createVolunteerOpportunity'])->name('create');
                Route::post('/', [AdminController::class, 'storeVolunteerOpportunity'])->name('store');
                Route::get('/{id}', [AdminController::class, 'showVolunteerOpportunity'])->name('show');
                Route::get('/{id}/edit', [AdminController::class, 'editVolunteerOpportunity'])->name('edit');
                Route::put('/{id}', [AdminController::class, 'updateVolunteerOpportunity'])->name('update');
                Route::delete('/{id}', [AdminController::class, 'destroyVolunteerOpportunity'])->name('destroy');
            });
            Route::get('/{id}', [AdminController::class, 'showVolunteer'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editVolunteer'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateVolunteer'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyVolunteer'])->name('destroy');
        });
        
        // Donation Management
        Route::prefix('donations')->name('donations.')->group(function () {
            Route::get('/', [AdminController::class, 'donations'])->name('index');
            Route::get('/create', [AdminController::class, 'createDonation'])->name('create');
            Route::post('/', [AdminController::class, 'storeDonation'])->name('store');
            Route::get('/campaigns', [AdminController::class, 'donationCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createDonationCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeDonationCampaign'])->name('campaigns.store');
            Route::get('/campaigns/{id}', [AdminController::class, 'showDonationCampaign'])->name('campaigns.show');
            Route::get('/campaigns/{id}/edit', [AdminController::class, 'editDonationCampaign'])->name('campaigns.edit');
            Route::put('/campaigns/{id}', [AdminController::class, 'updateDonationCampaign'])->name('campaigns.update');
            Route::delete('/campaigns/{id}', [AdminController::class, 'destroyDonationCampaign'])->name('campaigns.destroy');
            Route::get('/analytics', [AdminController::class, 'donationAnalytics'])->name('analytics');
        });
        
        // Scholarship Management
        Route::prefix('scholarships')->name('scholarships.')->group(function () {
            Route::get('/', [AdminController::class, 'scholarships'])->name('index');
            Route::get('/create', [AdminController::class, 'createScholarship'])->name('create');
            Route::post('/', [AdminController::class, 'storeScholarship'])->name('store');
            Route::get('/applications', [AdminController::class, 'scholarshipApplications'])->name('applications');
            Route::get('/{id}', [AdminController::class, 'showScholarship'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editScholarship'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateScholarship'])->name('update');
            Route::post('/{id}/duplicate', [AdminController::class, 'duplicateScholarship'])->name('duplicate');
            Route::delete('/{id}', [AdminController::class, 'destroyScholarship'])->name('destroy');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipStatus'])->name('update-status');
            Route::put('/bulk-action', [AdminController::class, 'bulkActionScholarships'])->name('bulk-action');
            Route::get('/export', [AdminController::class, 'exportScholarships'])->name('export');
            Route::get('/report', [AdminController::class, 'generateScholarshipReport'])->name('report');
        });

        // Scholarship Application Management
        Route::prefix('scholarship-applications')->name('scholarship-applications.')->group(function () {
            Route::put('/{id}/review', [AdminController::class, 'reviewScholarshipApplication'])->name('review');
            Route::put('/{id}/update-status', [AdminController::class, 'updateScholarshipApplicationStatus'])->name('update-status');
        });

        // Additional scholarship application routes for admin panel actions
        Route::prefix('scholarships/applications')->name('scholarships.applications.')->group(function () {
            Route::get('/files/{fileId}/download', [AdminController::class, 'downloadApplicationFile'])->name('files.download');
            Route::post('/files/{fileId}/verify', [AdminController::class, 'toggleFileVerification'])->name('files.verify');
            Route::get('/{applicationId}/download', [AdminController::class, 'downloadApplicationFiles'])->name('download');
            Route::post('/{applicationId}/notify', [AdminController::class, 'sendApplicationNotification'])->name('notify');
            Route::post('/bulk-review', [AdminController::class, 'bulkReviewApplications'])->name('bulk-review');
            Route::post('/bulk-status', [AdminController::class, 'bulkStatusApplications'])->name('bulk-status');
            Route::get('/export', [AdminController::class, 'exportApplications'])->name('export');
        });
        
        // Contact Management
        Route::prefix('contact')->name('contact.')->group(function () {
            Route::get('/', [AdminController::class, 'contactMessages'])->name('index');
            Route::get('/messages', [AdminController::class, 'contactMessages'])->name('messages');
            Route::get('/messages/{id}', [AdminController::class, 'showContactMessage'])->name('messages.show');
            Route::put('/messages/{id}', [AdminController::class, 'updateContactMessage'])->name('messages.update');
            Route::delete('/messages/{id}', [AdminController::class, 'destroyContactMessage'])->name('messages.destroy');
            Route::get('/faq', [AdminController::class, 'faq'])->name('faq');
        });
        
        // Newsletter Management
        Route::prefix('newsletter')->name('newsletter.')->group(function () {
            Route::get('/', [AdminController::class, 'newsletter'])->name('index');
            Route::get('/subscribers', [AdminController::class, 'newsletterSubscribers'])->name('subscribers');
            Route::post('/subscribers/import', [AdminController::class, 'importSubscribers'])->name('subscribers.import');
            Route::get('/subscribers/export', [AdminController::class, 'exportSubscribers'])->name('subscribers.export');
            Route::get('/campaigns', [AdminController::class, 'newsletterCampaigns'])->name('campaigns');
            Route::get('/campaigns/create', [AdminController::class, 'createNewsletterCampaign'])->name('campaigns.create');
            Route::post('/campaigns', [AdminController::class, 'storeNewsletterCampaign'])->name('campaigns.store');
            Route::get('/campaigns/{id}/edit', [AdminController::class, 'editNewsletterCampaign'])->name('campaigns.edit');
            Route::put('/campaigns/{id}', [AdminController::class, 'updateNewsletterCampaign'])->name('campaigns.update');
            Route::delete('/campaigns/{id}', [AdminController::class, 'destroyNewsletterCampaign'])->name('campaigns.destroy');
        });
        
        // Team Management
        Route::prefix('team')->name('team.')->group(function () {
            Route::get('/', [AdminController::class, 'teamMembers'])->name('index');
            Route::get('/create', [AdminController::class, 'createTeamMember'])->name('create');
            Route::post('/', [AdminController::class, 'storeTeamMember'])->name('store');
            Route::get('/{id}', [AdminController::class, 'showTeamMember'])->name('show');
            Route::get('/{id}/edit', [AdminController::class, 'editTeamMember'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'updateTeamMember'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'destroyTeamMember'])->name('destroy');
        });
        
        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [AdminController::class, 'analytics'])->name('index');
            Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
        });
        
        // API Documentation
        Route::get('/api-docs', [AdminController::class, 'apiDocs'])->name('api.docs');
        Route::get('/api-authorization-docs', function () {
            return view('api-authorization-docs');
        })->name('api.authorization.docs');
        


        // Server-side form handling routes
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::post('/categories', [AdminController::class, 'storeBlogCategoryForm'])->name('categories.store.form');
            Route::put('/categories/{id}', [AdminController::class, 'updateBlogCategoryForm'])->name('categories.update.form');
            Route::delete('/categories/{id}', [AdminController::class, 'destroyBlogCategoryForm'])->name('categories.destroy.form');
            Route::put('/comments/{id}/status', [AdminController::class, 'updateBlogCommentStatusForm'])->name('comments.status.form');
            Route::delete('/comments/{id}', [AdminController::class, 'destroyBlogCommentForm'])->name('comments.destroy.form');
            Route::delete('/posts/{id}', [AdminController::class, 'destroyBlogPostForm'])->name('posts.destroy.form');
            Route::put('/posts/{id}/status', [AdminController::class, 'toggleBlogPostStatusForm'])->name('posts.status.form');
        });
    });
});
