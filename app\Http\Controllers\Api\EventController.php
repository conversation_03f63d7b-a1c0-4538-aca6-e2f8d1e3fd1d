<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Events",
 *     description="Event management and registration endpoints"
 * )
 */
class EventController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/events",
     *     summary="Get list of published events",
     *     tags={"Events"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by event type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"fundraising","volunteer","awareness","training","other"})
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Events retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $type = $request->get('type');
        $search = $request->get('search');

        $query = Event::published()
            ->with(['organizer:id,first_name,last_name'])
            ->withCount('registrations');

        if ($type) {
            $query->byType($type);
        }

        if ($search) {
            $query->search($search);
        }

        $events = $query->orderBy('start_datetime', 'asc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/events/upcoming",
     *     summary="Get upcoming events",
     *     tags={"Events"},
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Number of events to return",
     *         required=false,
     *         @OA\Schema(type="integer", example=5)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Upcoming events retrieved successfully"
     *     )
     * )
     */
    public function upcoming(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 5);

        $events = Event::published()
            ->upcoming()
            ->with(['organizer:id,first_name,last_name'])
            ->withCount('registrations')
            ->orderBy('start_datetime', 'asc')
            ->take($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/events/{id}",
     *     summary="Get event details",
     *     tags={"Events"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Event ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Event details retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Event not found"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $event = Event::published()
            ->with(['organizer:id,first_name,last_name,email'])
            ->withCount('registrations')
            ->findOrFail($id);

        $data = [
            'event' => $event,
            'is_registration_open' => $event->isRegistrationOpen(),
            'spots_remaining' => $event->capacity ? $event->capacity - $event->registrations_count : null,
        ];

        // If user is authenticated, check if they're registered
        if (Auth::check()) {
            $registration = EventRegistration::where('event_id', $event->id)
                ->where('user_id', Auth::id())
                ->first();
            
            $data['user_registration'] = $registration;
            $data['is_registered'] = (bool) $registration;
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/events/{id}/register",
     *     summary="Register for an event",
     *     tags={"Events"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Event ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\JsonContent(
     *             @OA\Property(property="additional_info", type="object", example={"dietary_requirements": "Vegetarian", "emergency_contact": "+234800000000"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Successfully registered for event",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Successfully registered for event"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Registration not allowed"
     *     )
     * )
     */
    public function register(Request $request, $id): JsonResponse
    {
        $event = Event::published()->findOrFail($id);
        $user = Auth::user();

        // Check if registration is open
        if (!$event->isRegistrationOpen()) {
            return response()->json([
                'success' => false,
                'message' => 'Registration is not open for this event'
            ], 400);
        }

        // Check if user is already registered
        $existingRegistration = EventRegistration::where('event_id', $event->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingRegistration) {
            return response()->json([
                'success' => false,
                'message' => 'You are already registered for this event'
            ], 400);
        }

        // Create registration
        $registration = EventRegistration::create([
            'event_id' => $event->id,
            'user_id' => $user->id,
            'status' => 'registered',
            'additional_info' => $request->additional_info ?? null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Successfully registered for event',
            'data' => $registration
        ], 201);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/events/{id}/unregister",
     *     summary="Unregister from an event",
     *     tags={"Events"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Event ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successfully unregistered from event"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Registration not found"
     *     )
     * )
     */
    public function unregister($id): JsonResponse
    {
        $user = Auth::user();
        $registration = EventRegistration::where('event_id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $registration->cancel();

        return response()->json([
            'success' => true,
            'message' => 'Successfully unregistered from event'
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/events/my-registrations",
     *     summary="Get user's event registrations",
     *     tags={"Events"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by registration status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"registered","attended","cancelled"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User registrations retrieved successfully"
     *     )
     * )
     */
    public function myRegistrations(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');

        $query = $user->eventRegistrations()
            ->with(['event:id,title,slug,start_datetime,end_datetime,venue,status']);

        if ($status) {
            $query->where('status', $status);
        }

        $registrations = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $registrations
        ]);
    }
}
