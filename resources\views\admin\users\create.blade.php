@extends('layouts.admin')

@section('title', 'Create New User')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New User</h1>
        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.users.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number">
                                </div>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                    <small class="text-muted">Minimum 8 characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required minlength="8">
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" value="Nigeria">
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select" id="role_id" name="role_id" required onchange="toggleRoleFields()">
                                        <option value="">Select Role</option>
                                        <option value="1">Normal User</option>
                                        <option value="2">University Student</option>
                                        <option value="3">Partner Organization</option>
                                        <option value="4">Administrator</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- University Student Fields -->
                        <div id="student-fields" style="display: none;">
                            <hr>
                            <h6 class="text-primary mb-3">University Student Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="university_name" class="form-label">University Name</label>
                                        <input type="text" class="form-control" id="university_name" name="university_name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="course_of_study" class="form-label">Course of Study</label>
                                        <input type="text" class="form-control" id="course_of_study" name="course_of_study">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="year_of_study" class="form-label">Year of Study</label>
                                        <select class="form-select" id="year_of_study" name="year_of_study">
                                            <option value="">Select Year</option>
                                            <option value="1">1st Year</option>
                                            <option value="2">2nd Year</option>
                                            <option value="3">3rd Year</option>
                                            <option value="4">4th Year</option>
                                            <option value="5">5th Year</option>
                                            <option value="6">6th Year</option>
                                            <option value="7">7th Year</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="matriculation_number" class="form-label">Matriculation Number</label>
                                        <input type="text" class="form-control" id="matriculation_number" name="matriculation_number">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="cgpa" class="form-label">CGPA</label>
                                        <input type="number" class="form-control" id="cgpa" name="cgpa" step="0.01" min="0" max="5">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Partner Organization Fields -->
                        <div id="partner-fields" style="display: none;">
                            <hr>
                            <h6 class="text-primary mb-3">Partner Organization Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_name" class="form-label">Organization Name</label>
                                        <input type="text" class="form-control" id="organization_name" name="organization_name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_type" class="form-label">Organization Type</label>
                                        <select class="form-select" id="organization_type" name="organization_type">
                                            <option value="">Select Type</option>
                                            <option value="primary_school">Primary School</option>
                                            <option value="secondary_school">Secondary School</option>
                                            <option value="university">University</option>
                                            <option value="ngo">NGO</option>
                                            <option value="government">Government</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="organization_address" class="form-label">Organization Address</label>
                                <textarea class="form-control" id="organization_address" name="organization_address" rows="2"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_phone" class="form-label">Organization Phone</label>
                                        <input type="tel" class="form-control" id="organization_phone" name="organization_phone">
                                    </div>
                                </div>
                            </div>
                            <h6 class="text-secondary mb-3">Principal/Contact Person Information</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_name" class="form-label">Principal Name</label>
                                        <input type="text" class="form-control" id="principal_name" name="principal_name">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_email" class="form-label">Principal Email</label>
                                        <input type="email" class="form-control" id="principal_email" name="principal_email">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_phone" class="form-label">Principal Phone</label>
                                        <input type="tel" class="form-control" id="principal_phone" name="principal_phone">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email Verification -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="email_verified" name="email_verified" value="1" checked>
                                <label class="form-check-label" for="email_verified">
                                    Mark email as verified (Admin created users are auto-verified)
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Profile Picture Upload -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Picture</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="profilePreview" src="{{ asset('img/default-avatar.png') }}" alt="Profile Preview" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*" onchange="previewImage(this)">
                    </div>
                    <small class="text-muted">Upload a square image (recommended: 300x300px or larger)</small>
                </div>
            </div>

            <!-- User Guidelines -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Creation Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Use strong passwords (minimum 8 characters)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Verify email addresses when possible
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Assign appropriate roles and permissions
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Complete profile information for better user experience
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Role Descriptions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Role Descriptions</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Normal User:</strong> Basic access to public features and resources
                    </div>
                    <div class="mb-2">
                        <strong>University Student:</strong> Can apply for university scholarships and access educational resources
                    </div>
                    <div class="mb-2">
                        <strong>Partner Organization:</strong> Schools and organizations that can manage students and apply for scholarships on their behalf
                    </div>
                    <div class="mb-2">
                        <strong>Administrator:</strong> Full access to admin panel and all management features
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('profilePreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Toggle role-specific fields
function toggleRoleFields() {
    const roleId = document.getElementById('role_id').value;
    const studentFields = document.getElementById('student-fields');
    const partnerFields = document.getElementById('partner-fields');

    // Hide all role-specific fields first
    studentFields.style.display = 'none';
    partnerFields.style.display = 'none';

    // Show relevant fields based on role
    if (roleId === '2') { // University Student
        studentFields.style.display = 'block';
    } else if (roleId === '3') { // Partner Organization
        partnerFields.style.display = 'block';
    }
}

// Password confirmation validation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
@endsection 