<?php $__env->startSection('title', 'User Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'user','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'user','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Welcome Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">Welcome back, <?php echo e(Auth::user()->first_name); ?>!</h1>
                        <p class="text-gray-600 mt-2">Explore our programs and stay updated with our latest initiatives.</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Member since</p>
                        <p class="text-lg font-semibold text-indigo-600"><?php echo e(Auth::user()->created_at->format('M Y')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Quick Access Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php if (isset($component)) { $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.card','data' => ['title' => 'Scholarships','subtitle' => 'View available scholarships','icon' => 'graduation-cap','iconColor' => 'blue','href' => route('scholarships')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Scholarships','subtitle' => 'View available scholarships','icon' => 'graduation-cap','iconColor' => 'blue','href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('scholarships'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $attributes = $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $component = $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.card','data' => ['title' => 'Programs','subtitle' => 'Explore our programs','icon' => 'users','iconColor' => 'green','href' => route('programs')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Programs','subtitle' => 'Explore our programs','icon' => 'users','iconColor' => 'green','href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('programs'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $attributes = $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $component = $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.card','data' => ['title' => 'Events','subtitle' => 'Upcoming events','icon' => 'calendar','iconColor' => 'purple','href' => route('events')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Events','subtitle' => 'Upcoming events','icon' => 'calendar','iconColor' => 'purple','href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('events'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $attributes = $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $component = $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.card','data' => ['title' => 'Resources','subtitle' => 'Educational resources','icon' => 'book','iconColor' => 'orange','href' => '/resources']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Resources','subtitle' => 'Educational resources','icon' => 'book','iconColor' => 'orange','href' => '/resources']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $attributes = $__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__attributesOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2)): ?>
<?php $component = $__componentOriginalb63f174754b9376d0cbcfda0c77a69e2; ?>
<?php unset($__componentOriginalb63f174754b9376d0cbcfda0c77a69e2); ?>
<?php endif; ?>
            </div>

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php if (isset($component)) { $__componentOriginal457ade557f73eaa008f851091260abe1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal457ade557f73eaa008f851091260abe1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.stat-card','data' => ['title' => 'Available Scholarships','value' => ''.e(\App\Models\Scholarship::where('status', 'active')->count()).'','icon' => 'graduation-cap','color' => 'blue']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.stat-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Available Scholarships','value' => ''.e(\App\Models\Scholarship::where('status', 'active')->count()).'','icon' => 'graduation-cap','color' => 'blue']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $attributes = $__attributesOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__attributesOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $component = $__componentOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__componentOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal457ade557f73eaa008f851091260abe1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal457ade557f73eaa008f851091260abe1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.stat-card','data' => ['title' => 'Active Programs','value' => ''.e(\App\Models\Program::where('status', 'active')->count()).'','icon' => 'users','color' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.stat-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Active Programs','value' => ''.e(\App\Models\Program::where('status', 'active')->count()).'','icon' => 'users','color' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $attributes = $__attributesOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__attributesOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $component = $__componentOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__componentOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal457ade557f73eaa008f851091260abe1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal457ade557f73eaa008f851091260abe1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.stat-card','data' => ['title' => 'Upcoming Events','value' => ''.e(\App\Models\Event::where('event_date', '>', now())->count()).'','icon' => 'calendar','color' => 'purple']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.stat-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Upcoming Events','value' => ''.e(\App\Models\Event::where('event_date', '>', now())->count()).'','icon' => 'calendar','color' => 'purple']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $attributes = $__attributesOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__attributesOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $component = $__componentOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__componentOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal457ade557f73eaa008f851091260abe1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal457ade557f73eaa008f851091260abe1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.stat-card','data' => ['title' => 'Your Activities','value' => '0','icon' => 'chart-line','color' => 'orange']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.stat-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Your Activities','value' => '0','icon' => 'chart-line','color' => 'orange']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $attributes = $__attributesOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__attributesOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal457ade557f73eaa008f851091260abe1)): ?>
<?php $component = $__componentOriginal457ade557f73eaa008f851091260abe1; ?>
<?php unset($__componentOriginal457ade557f73eaa008f851091260abe1); ?>
<?php endif; ?>
            </div>

            <!-- Recent Updates and Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Latest News -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Latest News & Updates</h2>
                    <div class="space-y-4">
                        <?php
                            $latestBlogs = \App\Models\Blog::latest()->limit(3)->get();
                        ?>
                        <?php $__empty_1 = true; $__currentLoopData = $latestBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800"><?php echo e($blog->title); ?></h3>
                                <p class="text-sm text-gray-600 mt-1"><?php echo e(Str::limit($blog->content, 100)); ?></p>
                                <p class="text-xs text-gray-500 mt-2"><?php echo e($blog->created_at->diffForHumans()); ?></p>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800">Welcome to HALIMAKQ!</h3>
                                <p class="text-sm text-gray-600 mt-1">Stay tuned for the latest updates and news from our organization.</p>
                                <p class="text-xs text-gray-500 mt-2">Today</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4">
                        <a href="<?php echo e(route('blog')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">View All News →</a>
                    </div>
                </div>

                <!-- Your Activity -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Your Activity</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-user-circle text-blue-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Profile Created</p>
                                    <p class="text-sm text-gray-600"><?php echo e(Auth::user()->created_at->format('M d, Y')); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-sign-in-alt text-green-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Last Login</p>
                                    <p class="text-sm text-gray-600"><?php echo e(Auth::user()->updated_at->diffForHumans()); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center py-4">
                            <p class="text-gray-500 text-sm">More activities will appear here as you interact with our platform</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Educational Resources -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Educational Resources</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-book text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Study Guides</h3>
                        <p class="text-sm text-gray-600 mt-1">Access comprehensive study materials and guides</p>
                        <a href="/resources/study-guides" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">View Guides →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-video text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Video Tutorials</h3>
                        <p class="text-sm text-gray-600 mt-1">Watch educational videos and tutorials</p>
                        <a href="/resources/videos" class="text-green-600 hover:text-green-800 text-sm font-medium mt-2 inline-block">Watch Videos →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-download text-purple-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Downloads</h3>
                        <p class="text-sm text-gray-600 mt-1">Download useful documents and forms</p>
                        <a href="/resources/downloads" class="text-purple-600 hover:text-purple-800 text-sm font-medium mt-2 inline-block">View Downloads →</a>
                    </div>
                </div>
            </div>

            <!-- Note about Application -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-800">Want to Apply for Scholarships?</h3>
                        <p class="text-blue-700 mt-1">To apply for scholarships, you need to register as a University Student or have a Partner Organization apply on your behalf.</p>
                        <div class="mt-3 space-x-4">
                            <a href="<?php echo e(route('contact')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">Contact us for more information →</a>
                            <a href="<?php echo e(route('scholarships')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">View Available Scholarships →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/dashboards/user/index.blade.php ENDPATH**/ ?>