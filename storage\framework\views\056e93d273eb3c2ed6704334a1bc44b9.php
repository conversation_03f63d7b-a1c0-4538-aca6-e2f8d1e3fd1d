<?php $__env->startSection('title', 'User Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'user','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'user','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                    ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                    ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                    ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                    ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 text-white p-3 rounded-xl shadow-lg hover:bg-green-700 transition-all duration-200">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'user','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                        ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                        ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                        ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'user','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
                        ['label' => 'Profile', 'icon' => 'user', 'url' => '/profile'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Programs', 'icon' => 'users', 'url' => '/programs'],
                        ['label' => 'Events', 'icon' => 'calendar', 'url' => '/events'],
                        ['label' => 'Resources', 'icon' => 'book', 'url' => '/resources'],
                        ['label' => 'Contact', 'icon' => 'envelope', 'url' => '/contact']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:p-8 p-4 pt-20 lg:pt-8">
            <!-- Welcome Header -->
            <div class="relative bg-gradient-to-br from-green-600 to-green-800 text-white rounded-2xl shadow-xl p-8 mb-8 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
                    <div class="absolute top-1/2 left-1/2 w-40 h-40 bg-white/20 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
                </div>

                <div class="relative z-10 flex flex-col lg:flex-row items-start lg:items-center justify-between">
                    <div class="flex-1 mb-6 lg:mb-0">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mr-4 backdrop-blur-sm">
                                <i class="fas fa-user text-white text-2xl"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl lg:text-4xl font-bold text-white mb-1">
                                    Welcome back, <?php echo e(Auth::user()->first_name); ?>!
                                </h1>
                                <p class="text-green-100 text-lg">Discover opportunities and stay connected</p>
                            </div>
                        </div>
                        <p class="text-green-50 max-w-2xl leading-relaxed">
                            Explore our programs, stay updated with our latest initiatives, and be part of our growing community.
                        </p>
                    </div>

                    <div class="lg:ml-8">
                        <div class="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-xl min-w-[200px]">
                            <div class="text-center">
                                <div class="text-green-100 text-sm mb-2">Member since</div>
                                <div class="text-2xl font-bold text-white mb-2"><?php echo e(Auth::user()->created_at->format('M Y')); ?></div>
                                <div class="text-green-200 text-xs">
                                    <i class="fas fa-calendar mr-1"></i><?php echo e(Auth::user()->created_at->diffForHumans()); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Access Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
                <!-- Scholarships Card -->
                <a href="<?php echo e(route('scholarships')); ?>" class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:-translate-y-1 block">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 mr-4">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-200">Scholarships</h3>
                            <p class="text-sm text-gray-600">View available scholarships</p>
                        </div>
                        <div class="text-blue-500 group-hover:translate-x-1 transition-transform duration-200">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500">
                        <i class="fas fa-search mr-1"></i>Discover funding opportunities
                    </div>
                </a>

                <!-- Programs Card -->
                <a href="<?php echo e(route('programs')); ?>" class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:-translate-y-1 block">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 mr-4">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-800 group-hover:text-green-600 transition-colors duration-200">Programs</h3>
                            <p class="text-sm text-gray-600">Explore our programs</p>
                        </div>
                        <div class="text-green-500 group-hover:translate-x-1 transition-transform duration-200">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500">
                        <i class="fas fa-heart mr-1"></i>Join our community initiatives
                    </div>
                </a>

                <!-- Events Card -->
                <a href="<?php echo e(route('events')); ?>" class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:-translate-y-1 block">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 mr-4">
                            <i class="fas fa-calendar text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-800 group-hover:text-purple-600 transition-colors duration-200">Events</h3>
                            <p class="text-sm text-gray-600">Upcoming events</p>
                        </div>
                        <div class="text-purple-500 group-hover:translate-x-1 transition-transform duration-200">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500">
                        <i class="fas fa-calendar-check mr-1"></i>Stay connected with activities
                    </div>
                </a>

                <!-- Resources Card -->
                <a href="/resources" class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:-translate-y-1 block">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 mr-4">
                            <i class="fas fa-book text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-800 group-hover:text-orange-600 transition-colors duration-200">Resources</h3>
                            <p class="text-sm text-gray-600">Educational resources</p>
                        </div>
                        <div class="text-orange-500 group-hover:translate-x-1 transition-transform duration-200">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500">
                        <i class="fas fa-lightbulb mr-1"></i>Access learning materials
                    </div>
                </a>
            </div>

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Available Scholarships Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-graduation-cap text-green-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-green-600 mb-2"><?php echo e(\App\Models\Scholarship::where('status', 'active')->count()); ?></div>
                    <div class="text-sm text-gray-600">Available Scholarships</div>
                </div>

                <!-- Active Programs Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-amber-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-amber-600 mb-2"><?php echo e(\App\Models\Program::where('status', 'active')->count()); ?></div>
                    <div class="text-sm text-gray-600">Active Programs</div>
                </div>

                <!-- Upcoming Events Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-calendar text-blue-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?php echo e(\App\Models\Event::where('start_datetime', '>', now())->count()); ?></div>
                    <div class="text-sm text-gray-600">Upcoming Events</div>
                </div>

                <!-- Your Activities Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-purple-600 mb-2"><?php echo e(Auth::user()->created_at->diffInDays(now())); ?></div>
                    <div class="text-sm text-gray-600">Days as Member</div>
                </div>
            </div>

            <!-- Recent Updates and Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Latest News -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Latest News & Updates</h2>
                    <div class="space-y-4">
                        <?php
                            $latestBlogs = \App\Models\BlogPost::latest()->limit(3)->get();
                        ?>
                        <?php $__empty_1 = true; $__currentLoopData = $latestBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800"><?php echo e($blog->title); ?></h3>
                                <p class="text-sm text-gray-600 mt-1"><?php echo e(Str::limit($blog->content, 100)); ?></p>
                                <p class="text-xs text-gray-500 mt-2"><?php echo e($blog->created_at->diffForHumans()); ?></p>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="border-l-4 border-blue-500 pl-4">
                                <h3 class="font-semibold text-gray-800">Welcome to HALIMAKQ!</h3>
                                <p class="text-sm text-gray-600 mt-1">Stay tuned for the latest updates and news from our organization.</p>
                                <p class="text-xs text-gray-500 mt-2">Today</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4">
                        <a href="<?php echo e(route('blog.index')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">View All News →</a>
                    </div>
                </div>

                <!-- Your Activity -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Your Activity</h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-user-circle text-blue-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Profile Created</p>
                                    <p class="text-sm text-gray-600"><?php echo e(Auth::user()->created_at->format('M d, Y')); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-sign-in-alt text-green-500 text-xl mr-3"></i>
                                <div>
                                    <p class="font-medium text-gray-800">Last Login</p>
                                    <p class="text-sm text-gray-600"><?php echo e(Auth::user()->updated_at->diffForHumans()); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center py-4">
                            <p class="text-gray-500 text-sm">More activities will appear here as you interact with our platform</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Educational Resources -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Educational Resources</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-book text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Study Guides</h3>
                        <p class="text-sm text-gray-600 mt-1">Access comprehensive study materials and guides</p>
                        <a href="/resources/study-guides" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">View Guides →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-video text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Video Tutorials</h3>
                        <p class="text-sm text-gray-600 mt-1">Watch educational videos and tutorials</p>
                        <a href="/resources/videos" class="text-green-600 hover:text-green-800 text-sm font-medium mt-2 inline-block">Watch Videos →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-download text-purple-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Downloads</h3>
                        <p class="text-sm text-gray-600 mt-1">Download useful documents and forms</p>
                        <a href="/resources/downloads" class="text-purple-600 hover:text-purple-800 text-sm font-medium mt-2 inline-block">View Downloads →</a>
                    </div>
                </div>
            </div>

            <!-- Note about Application -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-800">Want to Apply for Scholarships?</h3>
                        <p class="text-blue-700 mt-1">To apply for scholarships, you need to register as a University Student or have a Partner Organization apply on your behalf.</p>
                        <div class="mt-3 space-x-4">
                            <a href="<?php echo e(route('contact')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">Contact us for more information →</a>
                            <a href="<?php echo e(route('scholarships')); ?>" class="text-blue-600 hover:text-blue-800 font-medium">View Available Scholarships →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/dashboards/user/index.blade.php ENDPATH**/ ?>