# HLTKKQ Foundation - Frontend

A modern, responsive frontend for the Halimatu Abdullahi Kofa Knowledge Quest Foundation platform built with Next.js 14+, TypeScript, and Tailwind CSS.

## 🚀 Technology Stack

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Icons**: Lucide React
- **State Management**: React Hooks (useState, useEffect, useContext)
- **API Client**: Custom fetch-based client with authentication
- **Form Handling**: Native React forms with validation

## 🌟 Features

### Public Pages
- **Homepage**: Hero section, featured programs, testimonials
- **About**: Foundation mission, vision, and team
- **Programs**: Educational initiatives and community projects
- **Scholarships**: Available scholarships and application process
- **Impact**: Statistics and success stories
- **Blog**: Latest news and updates
- **Contact**: Contact form and foundation information
- **Donate**: Donation campaigns and payment integration

### User Dashboard
- **Profile Management**: Update personal information and avatar
- **Donation History**: Track past donations and receipts
- **Event Registration**: Register for upcoming events
- **Scholarship Applications**: Apply and track application status
- **Volunteer Opportunities**: Browse and apply for volunteer positions
- **QR Code & ID Card**: Digital identification system

### Authentication
- **Registration**: Multi-step registration for different user types
- **Login/Logout**: Secure authentication with Laravel Sanctum
- **Password Reset**: Email-based password recovery
- **Profile Settings**: Update passwords and preferences

## 📁 Project Structure

```
front-end/
├── app/                     # Next.js App Router pages
│   ├── auth/               # Authentication pages
│   ├── dashboard/          # User dashboard pages
│   ├── (public)/          # Public pages
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── navigation.tsx    # Main navigation
│   └── footer.tsx        # Site footer
├── hooks/                # Custom React hooks
│   └── useSettings.ts    # Dynamic settings hook
├── lib/                  # Utilities and configurations
│   ├── api.ts           # API client
│   └── utils.ts         # Helper functions
├── public/              # Static assets
├── styles/             # Global styles
└── package.json        # Dependencies and scripts
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Running Laravel backend on port 8000

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/halimakq_foundation.git
   cd halimakq_foundation/front-end
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Update `.env.local` with your configuration:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
   NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

## 🎨 UI Components

This project uses [shadcn/ui](https://ui.shadcn.com/) components built on top of:
- **Radix UI**: Accessible component primitives
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful icons

### Adding New Components
```bash
npx shadcn-ui@latest add [component-name]
```

## 🔐 Authentication Flow

1. User registers/logs in via `/auth/login` or `/auth/register`
2. Backend returns access token
3. Token stored in localStorage
4. API client automatically includes token in requests
5. Protected routes redirect to login if not authenticated

## 📱 Responsive Design

The application is built mobile-first with breakpoints:
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🌐 API Integration

The frontend communicates with the Laravel backend through:
- **Settings API**: Dynamic configuration and branding
- **Authentication**: Login, register, logout, password reset
- **User Profile**: Profile management and avatar upload
- **Donations**: Campaign listing and donation processing
- **Events**: Event registration and management
- **Blog**: Content display and commenting
- **Scholarships**: Application submission and tracking

## 🔍 Performance Optimizations

- **Next.js Image Optimization**: Automatic image optimization
- **Code Splitting**: Automatic route-based code splitting
- **Lazy Loading**: Components loaded on demand
- **API Response Caching**: Efficient data fetching
- **Static Generation**: Pre-rendered pages where possible

## 🧪 Testing

```bash
# Run tests (when implemented)
npm run test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
npm run build
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## 📄 License

This project is open source and available under the [MIT License](../LICENSE).

## 🆘 Support

For support, please contact:
- **Email**: <EMAIL>
- **GitHub Issues**: [Create an issue](https://github.com/YOUR_USERNAME/halimakq_foundation/issues)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful component library
- [Lucide](https://lucide.dev/) - Icon library 