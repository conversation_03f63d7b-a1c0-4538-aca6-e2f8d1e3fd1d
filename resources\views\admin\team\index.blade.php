@extends('layouts.admin')

@section('title', 'Team Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Team Management</h1>
        <a href="{{ route('admin.team.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Team Member
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Team Members</div>
                            <div class="h4">{{ method_exists($teamMembers, 'total') ? $teamMembers->total() : $teamMembers->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Members</div>
                            <div class="h4">{{ $teamMembers->where('is_active', true)->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Departments</div>
                            <div class="h4">{{ $teamMembers->pluck('department')->unique()->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Leadership</div>
                            <div class="h4">{{ $teamMembers->whereIn('position', ['Director', 'Manager', 'Coordinator'])->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Members Grid/Table Toggle -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Team Members</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary active" id="gridView">
                        <i class="fas fa-th"></i> Grid
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="listView">
                        <i class="fas fa-list"></i> List
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search team members..." id="searchTeam">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterDepartment">
                        <option value="">All Departments</option>
                        <option value="leadership">Leadership</option>
                        <option value="programs">Programs</option>
                        <option value="outreach">Outreach</option>
                        <option value="operations">Operations</option>
                        <option value="finance">Finance</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>

            <!-- Grid View -->
            <div id="gridContainer">
                <div class="row">
                    @forelse($teamMembers as $member)
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 team-member-card">
                            <div class="position-relative">
                                @if($member->photo)
                                    <img src="{{ $member->photo }}" class="card-img-top" alt="{{ $member->name }}" style="height: 250px; object-fit: cover;">
                                @else
                                    <div class="bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                        <i class="fas fa-user fa-4x text-muted"></i>
                                    </div>
                                @endif
                                @if(!$member->is_active)
                                    <span class="position-absolute top-0 end-0 badge bg-secondary m-2">Inactive</span>
                                @endif
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ $member->name }}</h5>
                                <p class="card-text">
                                    <strong>{{ $member->position }}</strong>
                                    @if($member->department)
                                        <br><small class="text-muted">{{ $member->department }}</small>
                                    @endif
                                </p>
                                @if($member->bio)
                                    <p class="card-text text-muted small">{{ Str::limit($member->bio, 100) }}</p>
                                @endif
                                
                                <!-- Social Links -->
                                @if($member->email || $member->linkedin_url || $member->twitter_url)
                                    <div class="mt-auto">
                                        <div class="d-flex gap-2">
                                            @if($member->email)
                                                <a href="mailto:{{ $member->email }}" class="btn btn-sm btn-outline-primary" title="Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            @endif
                                            @if($member->linkedin_url)
                                                <a href="{{ $member->linkedin_url }}" target="_blank" class="btn btn-sm btn-outline-info" title="LinkedIn">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            @endif
                                            @if($member->twitter_url)
                                                <a href="{{ $member->twitter_url }}" target="_blank" class="btn btn-sm btn-outline-info" title="Twitter">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="{{ route('admin.team.show', $member->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('admin.team.edit', $member->id) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteMember({{ $member->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No team members found</p>
                            <a href="{{ route('admin.team.create') }}" class="btn btn-primary">Add Your First Team Member</a>
                        </div>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- List View (Initially Hidden) -->
            <div id="listContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Photo</th>
                                <th>Name</th>
                                <th>Position</th>
                                <th>Department</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($teamMembers as $member)
                            <tr>
                                <td>
                                    @if($member->photo)
                                        <img src="{{ $member->photo }}" alt="{{ $member->name }}" class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $member->name }}</div>
                                        <small class="text-muted">{{ $member->slug }}</small>
                                    </div>
                                </td>
                                <td>
                                    <strong>{{ $member->position }}</strong>
                                </td>
                                <td>
                                    @if($member->department)
                                        <span class="badge bg-secondary">{{ $member->department }}</span>
                                    @else
                                        <span class="text-muted">Not specified</span>
                                    @endif
                                </td>
                                <td>
                                    @if($member->email)
                                        <div>
                                            <a href="mailto:{{ $member->email }}" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>{{ $member->email }}
                                            </a>
                                        </div>
                                    @endif
                                    <div class="mt-1">
                                        @if($member->linkedin_url)
                                            <a href="{{ $member->linkedin_url }}" target="_blank" class="text-decoration-none me-2">
                                                <i class="fab fa-linkedin text-primary"></i>
                                            </a>
                                        @endif
                                        @if($member->twitter_url)
                                            <a href="{{ $member->twitter_url }}" target="_blank" class="text-decoration-none">
                                                <i class="fab fa-twitter text-info"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $member->is_active ? 'success' : 'secondary' }}">
                                        {{ $member->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.team.show', $member->id) }}" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.team.edit', $member->id) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMember({{ $member->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No team members found</p>
                                    <a href="{{ route('admin.team.create') }}" class="btn btn-primary">Add Your First Team Member</a>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    Showing {{ $teamMembers->firstItem() }} to {{ $teamMembers->lastItem() }} of {{ $teamMembers->total() }} results
                </div>
                {{ $teamMembers->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this team member? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteMember">Delete Member</button>
            </div>
        </div>
    </div>
</div>

<script>
// View toggle functionality
document.getElementById('gridView').addEventListener('click', function() {
    document.getElementById('gridContainer').style.display = 'block';
    document.getElementById('listContainer').style.display = 'none';
    this.classList.add('active');
    document.getElementById('listView').classList.remove('active');
});

document.getElementById('listView').addEventListener('click', function() {
    document.getElementById('gridContainer').style.display = 'none';
    document.getElementById('listContainer').style.display = 'block';
    this.classList.add('active');
    document.getElementById('gridView').classList.remove('active');
});

function deleteMember(memberId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteMemberModal'));
    modal.show();
    
    document.getElementById('confirmDeleteMember').onclick = function() {
        console.log('Delete member:', memberId);
        modal.hide();
        // Add AJAX call to delete team member
        // After success, remove the card/row from the page
    };
}

// Search functionality
document.getElementById('searchTeam').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const cards = document.querySelectorAll('.team-member-card');
    const rows = document.querySelectorAll('#listContainer tbody tr');
    
    // Filter grid view
    cards.forEach(card => {
        const name = card.querySelector('.card-title').textContent.toLowerCase();
        const position = card.querySelector('.card-text strong').textContent.toLowerCase();
        
        if (name.includes(searchTerm) || position.includes(searchTerm)) {
            card.closest('.col-xl-3').style.display = 'block';
        } else {
            card.closest('.col-xl-3').style.display = 'none';
        }
    });
    
    // Filter list view
    rows.forEach(row => {
        if (row.cells.length > 1) { // Skip empty state row
            const name = row.cells[1].textContent.toLowerCase();
            const position = row.cells[2].textContent.toLowerCase();
            
            if (name.includes(searchTerm) || position.includes(searchTerm)) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        }
    });
});

// Filter functionality
document.getElementById('filterDepartment').addEventListener('change', function() {
    console.log('Filter department:', this.value);
    // Implement department filtering
});

document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
    // Implement status filtering
});

// Add hover effects for team member cards
document.querySelectorAll('.team-member-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.transition = 'transform 0.3s ease';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
});
</script>

<style>
.team-member-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
@endsection 