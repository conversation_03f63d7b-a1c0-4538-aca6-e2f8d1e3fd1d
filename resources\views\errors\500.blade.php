<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - HALIMAKQ Foundation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto text-center">
            <!-- 500 Icon -->
            <div class="floating-animation mb-8">
                <div class="w-32 h-32 mx-auto bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <i class="fas fa-server text-6xl text-white"></i>
                </div>
            </div>

            <!-- Error Message -->
            <div class="text-white mb-8">
                <h1 class="text-6xl font-bold mb-4">500</h1>
                <h2 class="text-3xl font-semibold mb-4">Server Error</h2>
                <p class="text-xl text-white/90 mb-2">
                    Something went wrong on our end.
                </p>
                <p class="text-lg text-white/80">
                    We're working to fix this issue. Please try again later.
                </p>
            </div>

            <!-- Navigation Options -->
            <div class="space-y-4 mb-8">
                <!-- Primary Action - Go to Next.js Frontend -->
                <div class="pulse-animation">
                    <a href="{{ frontendUrl() }}" 
                       class="inline-flex items-center px-8 py-4 bg-white text-red-600 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-home mr-3"></i>
                        Go to Homepage
                    </a>
                </div>

                <!-- Secondary Actions -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="window.location.reload()" 
                            class="inline-flex items-center px-6 py-3 bg-white/20 text-white rounded-lg font-medium hover:bg-white/30 transition-all duration-300 backdrop-blur-sm">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </button>
                    
                    <a href="{{ frontendUrl('contact') }}" 
                       class="inline-flex items-center px-6 py-3 bg-white/20 text-white rounded-lg font-medium hover:bg-white/30 transition-all duration-300 backdrop-blur-sm">
                        <i class="fas fa-envelope mr-2"></i>
                        Report Issue
                    </a>
                </div>
            </div>

            <!-- Dashboard Links for Authenticated Users -->
            @auth
            <div class="border-t border-white/20 pt-6">
                <p class="text-white/80 mb-4">Or go back to your dashboard:</p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    @if(Auth::user()->role === 'student')
                        <a href="{{ route('student.dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors duration-300">
                            <i class="fas fa-user-graduate mr-2"></i>
                            Student Dashboard
                        </a>
                    @elseif(Auth::user()->role === 'partner')
                        <a href="{{ route('partner.dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300">
                            <i class="fas fa-building mr-2"></i>
                            Partner Dashboard
                        </a>
                    @elseif(Auth::user()->role === 'admin')
                        <a href="{{ route('admin.dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors duration-300">
                            <i class="fas fa-cog mr-2"></i>
                            Admin Dashboard
                        </a>
                    @else
                        <a href="{{ route('user.dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors duration-300">
                            <i class="fas fa-user mr-2"></i>
                            User Dashboard
                        </a>
                    @endif
                </div>
            </div>
            @endauth

            <!-- Footer -->
            <div class="mt-12 text-white/60 text-sm">
                <p>&copy; {{ date('Y') }} HALIMAKQ Foundation. All rights reserved.</p>
                <p class="mt-2">Empowering Communities Through Education</p>
            </div>
        </div>
    </div>

    <!-- Background Decoration -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
    </div>
</body>
</html>
