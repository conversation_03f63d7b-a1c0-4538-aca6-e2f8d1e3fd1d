import { Arrow<PERSON><PERSON>, <PERSON>, BookO<PERSON>, Heart, GraduationCap, Target, Zap } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export function EnhancedGetInvolvedBanner() {
  return (
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-green-700 via-green-800 to-green-900 shadow-2xl">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid opacity-20"></div>
      <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-amber-500/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-2/3 bg-gradient-to-tr from-blue-500/20 to-transparent"></div>

      {/* Floating Elements */}
      <div className="absolute top-8 left-8 w-16 h-16 bg-amber-400/20 rounded-full blur-xl animate-float"></div>
      <div
        className="absolute bottom-12 right-12 w-20 h-20 bg-green-300/20 rounded-full blur-2xl animate-float"
        style={{ animationDelay: "2s" }}
      ></div>
      <div
        className="absolute top-1/3 right-1/4 w-12 h-12 bg-white/10 rounded-full blur-lg animate-float"
        style={{ animationDelay: "1s" }}
      ></div>

      <div className="relative z-10 px-8 py-16 lg:py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
            {/* Main Content */}
            <div className="lg:col-span-7 space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-amber-500/20 backdrop-blur-sm px-4 py-2 rounded-full border border-amber-400/30">
                  <Zap className="h-4 w-4 text-amber-400" />
                  <span className="text-amber-200 text-sm font-medium">Make an Impact Today</span>
                </div>

                <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white tracking-tight leading-tight">
                  Get{" "}
                  <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
                    Involved
                  </span>
                </h2>

                <p className="text-xl md:text-2xl text-green-100 leading-relaxed max-w-2xl">
                  Join our mission to transform lives through education. Every contribution brings a child closer to
                  their dreams and builds a stronger Nigeria.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <Link href="/donate">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-green-950 font-bold text-lg px-8 py-4 transition-all duration-300 hover:scale-105 hover:shadow-xl rounded-full group"
                  >
                    <Heart className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                    Donate Now
                  </Button>
                </Link>
                <Link href="/volunteer">
                  <Button
                    size="lg"
                    variant="outline"
                    className="text-white border-white/50 hover:bg-white/10 font-bold text-lg px-8 py-4 transition-all duration-300 hover:scale-105 rounded-full backdrop-blur-sm border-2 group"
                  >
                    Volunteer With Us
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </Link>
              </div>

              {/* Quick Impact Stats */}
              <div className="grid grid-cols-3 gap-4 pt-4">
                <div className="text-center group cursor-pointer">
                  <div className="flex justify-center mb-2 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-3xl font-bold text-white group-hover:text-amber-400 transition-colors duration-300">
                    1,200+
                  </div>
                  <div className="text-green-200 text-sm">Students Helped</div>
                </div>
                <div className="text-center group cursor-pointer">
                  <div className="flex justify-center mb-2 group-hover:scale-110 transition-transform duration-300">
                    <BookOpen className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-3xl font-bold text-white group-hover:text-amber-400 transition-colors duration-300">
                    5,000+
                  </div>
                  <div className="text-green-200 text-sm">Books Donated</div>
                </div>
                <div className="text-center group cursor-pointer">
                  <div className="flex justify-center mb-2 group-hover:scale-110 transition-transform duration-300">
                    <GraduationCap className="h-8 w-8 text-amber-400" />
                  </div>
                  <div className="text-3xl font-bold text-white group-hover:text-amber-400 transition-colors duration-300">
                    ₦12M
                  </div>
                  <div className="text-green-200 text-sm">In Scholarships</div>
                </div>
              </div>
            </div>

            {/* Side Cards */}
            <div className="lg:col-span-5 space-y-6">
              {/* Donation Impact Card */}
              <Card className="bg-white/10 backdrop-blur-lg border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300 hover:scale-105 group rounded-2xl">
                <CardContent className="p-8">
                  <div className="text-center space-y-4">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-500/20 rounded-full group-hover:bg-amber-500/30 transition-colors duration-300">
                      <Target className="h-8 w-8 text-amber-400" />
                    </div>
                    <div className="text-4xl font-bold text-white group-hover:text-amber-400 transition-colors duration-300">
                      ₦5,000
                    </div>
                    <div className="text-amber-300 font-semibold text-lg">Can Provide</div>
                    <ul className="text-green-100 space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                        School supplies for 1 child
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                        Uniform and shoes
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                        Textbooks for a term
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                        Nutritious meals for a month
                      </li>
                    </ul>
                    <Link href="/donate">
                      <Button className="w-full bg-amber-500 hover:bg-amber-600 text-green-950 font-medium transition-all duration-300 hover:scale-105 rounded-full">
                        Sponsor a Child
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* Volunteer Impact Card */}
              <Card className="bg-white/10 backdrop-blur-lg border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300 hover:scale-105 group rounded-2xl">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center group-hover:bg-green-500/30 transition-colors duration-300">
                        <Users className="h-6 w-6 text-green-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white group-hover:text-green-400 transition-colors duration-300">
                          Volunteer Impact
                        </h3>
                        <p className="text-green-200 text-sm">Join our community of changemakers</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-white">50+</div>
                        <div className="text-green-200 text-xs">Active Volunteers</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-white">15</div>
                        <div className="text-green-200 text-xs">Programs Running</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
