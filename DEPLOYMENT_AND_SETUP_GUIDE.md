# 🚀 HLTKKQ Foundation - Deployment & Setup Guide

## Overview

This guide provides comprehensive instructions for setting up and deploying the HLTKKQ Foundation NGO platform in both development and production environments.

## Prerequisites

### System Requirements
- **PHP 8.2+** with required extensions
- **Node.js 18+** with npm/yarn
- **MySQL 8.0+** or MariaDB 10.4+
- **Composer** for PHP dependency management
- **Git** for version control

### PHP Extensions Required
```bash
# Ubuntu/Debian
sudo apt install php-mysql php-mbstring php-xml php-bcmath php-curl php-gd php-zip

# CentOS/RHEL
sudo yum install php-mysql php-mbstring php-xml php-bcmath php-curl php-gd php-zip
```

## Development Setup

### 1. Project Installation

```bash
# Clone the repository
git clone [repository-url]
cd laravel-api-ngo

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Install frontend dependencies
cd front-end
npm install
cd ..
```

### 2. Environment Configuration

#### Backend Environment (.env)
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

**Configure .env file:**
```env
# Application Settings
APP_NAME="HLTKKQ Foundation"
APP_ENV=local
APP_KEY=base64:generated-key-here
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=your-password

# Authentication
SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000

# File Storage
FILESYSTEM_DISK=public

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=587
MAIL_USERNAME=your-mailtrap-username
MAIL_PASSWORD=your-mailtrap-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Queue Configuration
QUEUE_CONNECTION=sync

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120
```

#### Frontend Environment
```bash
# Navigate to frontend directory
cd front-end

# Create environment file
cp .env.example .env.local
```

**Configure .env.local:**
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

### 3. Database Setup

```bash
# Create database
mysql -u root -p
CREATE DATABASE hltkkq_foundation;
EXIT;

# Run migrations and seeders
php artisan migrate:fresh --seed

# Link storage directory
php artisan storage:link
```

### 4. Start Development Servers

**Terminal 1 - Backend:**
```bash
php artisan serve
```

**Terminal 2 - Frontend:**
```bash
cd front-end
npm run dev
```

### 5. Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api/v1
- **API Documentation**: http://localhost:8000/api/documentation

### 6. Default Admin Account
```
Email: <EMAIL>
Password: password
```

## Production Deployment

### 1. Server Requirements

#### Recommended Server Specifications
- **CPU**: 2+ cores
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 50GB+ SSD
- **Bandwidth**: Unmetered or high limit

#### Software Stack
- **Web Server**: Nginx or Apache
- **PHP**: 8.2+ with FPM
- **Database**: MySQL 8.0+ or MariaDB 10.4+
- **Process Manager**: PM2 for Node.js (if applicable)
- **SSL Certificate**: Let's Encrypt or commercial

### 2. Server Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/hltkkqfoundation.org
server {
    listen 80;
    server_name hltkkqfoundation.org www.hltkkqfoundation.org;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name hltkkqfoundation.org www.hltkkqfoundation.org;
    root /var/www/hltkkq-foundation/public;
    index index.php;

    ssl_certificate /etc/ssl/certs/hltkkqfoundation.org.crt;
    ssl_certificate_key /etc/ssl/private/hltkkqfoundation.org.key;

    # Laravel Backend
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Frontend Proxy (if using Node.js server)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
```

### 3. Backend Deployment

#### Deploy Laravel Application
```bash
# Upload code to server
git clone [repository-url] /var/www/hltkkq-foundation
cd /var/www/hltkkq-foundation

# Install dependencies (production)
composer install --no-dev --optimize-autoloader

# Set up environment
cp .env.example .env.production
php artisan key:generate

# Configure permissions
sudo chown -R www-data:www-data /var/www/hltkkq-foundation
sudo chmod -R 755 /var/www/hltkkq-foundation
sudo chmod -R 775 /var/www/hltkkq-foundation/storage
sudo chmod -R 775 /var/www/hltkkq-foundation/bootstrap/cache

# Run migrations
php artisan migrate --force

# Seed initial data (if needed)
php artisan db:seed --force

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan storage:link
```

#### Production Environment (.env.production)
```env
APP_NAME="HLTKKQ Foundation"
APP_ENV=production
APP_KEY=base64:your-production-key
APP_DEBUG=false
APP_URL=https://hltkkqfoundation.org

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=hltkkq_production
DB_USERNAME=hltkkq_user
DB_PASSWORD=secure-production-password

SANCTUM_STATEFUL_DOMAINS=hltkkqfoundation.org,www.hltkkqfoundation.org
SESSION_DOMAIN=.hltkkqfoundation.org

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

LOG_CHANNEL=daily
LOG_LEVEL=error
```

### 4. Frontend Deployment

#### Option 1: Static Export (Recommended)
```bash
cd front-end

# Install dependencies
npm ci

# Build for production
npm run build

# Export static files
npm run export

# Deploy to web server
sudo cp -r out/* /var/www/html/
```

#### Option 2: Node.js Server
```bash
# Install PM2 globally
npm install -g pm2

# Build application
npm run build

# Start with PM2
pm2 start npm --name "hltkkq-frontend" -- start
pm2 startup
pm2 save
```

**Frontend Environment (.env.production):**
```env
NEXT_PUBLIC_API_URL=https://api.hltkkqfoundation.org
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

### 5. Database Configuration

#### Create Production Database
```sql
-- Create database and user
CREATE DATABASE hltkkq_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hltkkq_user'@'localhost' IDENTIFIED BY 'secure-password';
GRANT ALL PRIVILEGES ON hltkkq_production.* TO 'hltkkq_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Database Optimization
```sql
-- MySQL optimization settings
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 64M
```

### 6. SSL Certificate Setup

#### Using Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d hltkkqfoundation.org -d www.hltkkqfoundation.org

# Auto-renewal cron job
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. Monitoring and Logging

#### Log Management
```bash
# Laravel logs
tail -f /var/www/hltkkq-foundation/storage/logs/laravel.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# PHP-FPM logs
tail -f /var/log/php8.2-fpm.log
```

#### Performance Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop

# Monitor processes
htop
iotop

# Check disk space
df -h

# Check memory usage
free -h
```

## Docker Deployment (Alternative)

### Docker Compose Setup
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - mysql

  frontend:
    build: ./front-end
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://app:8000

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: hltkkq_foundation
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - mysql_data:/var/lib/mysql

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
      - frontend

volumes:
  mysql_data:
```

### Docker Commands
```bash
# Build and run
docker-compose up -d

# Run migrations
docker-compose exec app php artisan migrate

# View logs
docker-compose logs -f app
```

## Backup and Recovery

### Database Backup
```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/hltkkq"
DB_NAME="hltkkq_production"
DB_USER="hltkkq_user"
DB_PASS="secure-password"

mkdir -p $BACKUP_DIR

mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql
gzip $BACKUP_DIR/backup_$DATE.sql

# Keep only last 30 backups
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

### File Backup
```bash
#!/bin/bash
# backup-files.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/hltkkq"
SOURCE_DIR="/var/www/hltkkq-foundation"

tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C $SOURCE_DIR storage public/uploads .env
```

### Automated Backups
```bash
# Add to crontab
0 2 * * * /path/to/backup-database.sh
0 3 * * * /path/to/backup-files.sh
```

## Security Considerations

### Server Security
1. **Firewall Configuration**
2. **SSH Key Authentication**
3. **Regular Security Updates**
4. **File Permission Management**
5. **Database Security**

### Application Security
1. **Environment Variables Protection**
2. **HTTPS Enforcement**
3. **CORS Configuration**
4. **Rate Limiting**
5. **Input Validation**

## Troubleshooting

### Common Issues

#### Permission Errors
```bash
sudo chown -R www-data:www-data /var/www/hltkkq-foundation
sudo chmod -R 755 /var/www/hltkkq-foundation
sudo chmod -R 775 storage bootstrap/cache
```

#### Cache Issues
```bash
php artisan optimize:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

#### Database Connection Issues
```bash
# Test database connection
php artisan tinker
DB::connection()->getPdo();
```

### Performance Optimization

#### Laravel Optimization
```bash
# Production optimizations
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer install --optimize-autoloader --no-dev
```

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_donations_created_at ON donations(created_at);
```

## Health Checks

### Application Health Check
```bash
# Check if application is running
curl -f http://localhost:8000/api/v1/status

# Check database connection
php artisan tinker
>>> DB::connection()->getPdo()
```

### Frontend Health Check
```bash
# Check if frontend is accessible
curl -f http://localhost:3000
```

---

> **🚀 Deploy with confidence! This platform serves the mission of educational empowerment in Nigeria. Every deployment brings us closer to transforming lives through technology.** 