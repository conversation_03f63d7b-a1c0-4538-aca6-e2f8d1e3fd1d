

<?php $__env->startSection('title', 'Team Member Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Team Member Details</h1>
        <div>
            <a href="<?php echo e(route('admin.team.index')); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Team
            </a>
            <a href="<?php echo e(route('admin.team.edit', $teamMember->id)); ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Member
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Photo Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Photo</h6>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo e($teamMember->photo ?? asset('img/default-avatar.png')); ?>" alt="Profile Photo" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <h4><?php echo e($teamMember->name); ?></h4>
                    <p class="text-muted"><?php echo e($teamMember->position); ?></p>
                    <div class="row text-center">
                        <div class="col">
                            <span class="badge bg-<?php echo e($teamMember->status === 'active' ? 'success' : 'secondary'); ?> p-2">
                                <i class="fas fa-circle me-1" style="font-size: 8px;"></i>
                                <?php echo e(ucfirst($teamMember->status)); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Email</label>
                        <p class="form-control-plaintext">
                            <?php if($teamMember->email): ?>
                                <a href="mailto:<?php echo e($teamMember->email); ?>"><?php echo e($teamMember->email); ?></a>
                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Phone</label>
                        <p class="form-control-plaintext"><?php echo e($teamMember->phone ?? 'N/A'); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Department</label>
                        <p class="form-control-plaintext"><?php echo e($teamMember->department ?? 'N/A'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Social Media Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Social Media</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if($teamMember->linkedin_url): ?>
                        <a href="<?php echo e($teamMember->linkedin_url); ?>" target="_blank" class="btn btn-outline-primary">
                            <i class="fab fa-linkedin me-2"></i>LinkedIn Profile
                        </a>
                        <?php endif; ?>
                        <?php if($teamMember->twitter_url): ?>
                        <a href="<?php echo e($teamMember->twitter_url); ?>" target="_blank" class="btn btn-outline-info">
                            <i class="fab fa-twitter me-2"></i>Twitter Profile
                        </a>
                        <?php endif; ?>
                        <?php if(!$teamMember->linkedin_url && !$teamMember->twitter_url): ?>
                        <p class="text-muted text-center">No social media profiles available</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Biography Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Biography</h6>
                </div>
                <div class="card-body">
                    <?php if($teamMember->bio): ?>
                        <p class="text-justify"><?php echo e($teamMember->bio); ?></p>
                    <?php else: ?>
                        <p class="text-muted">No biography available for this team member.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Professional Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Professional Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Position</label>
                                <p class="form-control-plaintext"><?php echo e($teamMember->position); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Department</label>
                                <p class="form-control-plaintext"><?php echo e($teamMember->department ?? 'N/A'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Join Date</label>
                                <p class="form-control-plaintext"><?php echo e(date('F j, Y', strtotime($teamMember->join_date))); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-<?php echo e($teamMember->status === 'active' ? 'success' : 'secondary'); ?> p-2">
                                        <?php echo e(ucfirst($teamMember->status)); ?>

                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo e(rand(2, 8)); ?></h4>
                                <p class="text-muted mb-0">Years of Service</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success"><?php echo e(rand(15, 50)); ?></h4>
                                <p class="text-muted mb-0">Projects Completed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-info"><?php echo e(rand(100, 500)); ?></h4>
                                <p class="text-muted mb-0">People Impacted</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning"><?php echo e(rand(5, 20)); ?></h4>
                            <p class="text-muted mb-0">Awards Received</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                    <small class="text-muted">Last 30 days</small>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="timeline-marker bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px;">
                                    <i class="fas fa-project-diagram text-white" style="font-size: 12px;"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Completed Project: Education Initiative</h6>
                                    <p class="text-muted mb-0">Successfully completed the education initiative project in Lagos State</p>
                                    <small class="text-muted">2 days ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="timeline-marker bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px;">
                                    <i class="fas fa-users text-white" style="font-size: 12px;"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Led Team Meeting</h6>
                                    <p class="text-muted mb-0">Conducted weekly team meeting and project review</p>
                                    <small class="text-muted">5 days ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="timeline-marker bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px;">
                                    <i class="fas fa-calendar text-white" style="font-size: 12px;"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Attended Conference</h6>
                                    <p class="text-muted mb-0">Participated in NGO Leadership Conference in Abuja</p>
                                    <small class="text-muted">1 week ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-marker {
    min-width: 30px;
    min-height: 30px;
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/team/show.blade.php ENDPATH**/ ?>