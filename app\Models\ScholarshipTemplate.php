<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScholarshipTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'description',
        'default_fields',
        'validation_rules',
        'settings',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'default_fields' => 'array',
        'validation_rules' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific category
     */
    public function scopeForCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get default fields for this template
     */
    public function getDefaultFieldsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * Get validation rules for this template
     */
    public function getValidationRulesAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * Get settings for this template
     */
    public function getSettingsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * Create a scholarship from this template
     */
    public function createScholarship($data = [])
    {
        $scholarshipData = array_merge([
            'category' => $this->category,
            'custom_fields' => $this->default_fields,
        ], $data);

        $scholarship = Scholarship::create($scholarshipData);

        // Create fields from template
        foreach ($this->default_fields as $index => $fieldData) {
            ScholarshipField::create([
                'scholarship_id' => $scholarship->id,
                'field_name' => $fieldData['field_name'],
                'field_label' => $fieldData['field_label'],
                'field_type' => $fieldData['field_type'],
                'field_options' => $fieldData['field_options'] ?? null,
                'is_required' => $fieldData['is_required'] ?? false,
                'field_order' => $index + 1,
                'is_active' => true,
            ]);
        }

        return $scholarship;
    }
}
