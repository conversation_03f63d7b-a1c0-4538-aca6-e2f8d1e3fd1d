<?php $__env->startSection('title', 'Edit Volunteer Opportunity'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Volunteer Opportunity</h1>
        <div>
            <a href="<?php echo e(route('admin.volunteers.opportunities.show', $opportunity->id)); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="<?php echo e(route('admin.volunteers.opportunities.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Opportunities
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Information</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.volunteers.opportunities.update', $opportunity->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Opportunity Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="<?php echo e(old('title', $opportunity->title)); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required><?php echo e(old('description', $opportunity->description)); ?></textarea>
                        </div>

                        <!-- Category and Time Commitment -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="education" <?php echo e(old('category', $opportunity->category) == 'education' ? 'selected' : ''); ?>>Education</option>
                                        <option value="health" <?php echo e(old('category', $opportunity->category) == 'health' ? 'selected' : ''); ?>>Health</option>
                                        <option value="environment" <?php echo e(old('category', $opportunity->category) == 'environment' ? 'selected' : ''); ?>>Environment</option>
                                        <option value="community" <?php echo e(old('category', $opportunity->category) == 'community' ? 'selected' : ''); ?>>Community</option>
                                        <option value="events" <?php echo e(old('category', $opportunity->category) == 'events' ? 'selected' : ''); ?>>Events</option>
                                        <option value="administration" <?php echo e(old('category', $opportunity->category) == 'administration' ? 'selected' : ''); ?>>Administration</option>
                                        <option value="fundraising" <?php echo e(old('category', $opportunity->category) == 'fundraising' ? 'selected' : ''); ?>>Fundraising</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="time_commitment" class="form-label">Time Commitment <span class="text-danger">*</span></label>
                                    <select class="form-select" id="time_commitment" name="time_commitment" required>
                                        <option value="one-time" <?php echo e(old('time_commitment', $opportunity->time_commitment) == 'one-time' ? 'selected' : ''); ?>>One-time</option>
                                        <option value="weekly" <?php echo e(old('time_commitment', $opportunity->time_commitment) == 'weekly' ? 'selected' : ''); ?>>Weekly</option>
                                        <option value="monthly" <?php echo e(old('time_commitment', $opportunity->time_commitment) == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                                        <option value="flexible" <?php echo e(old('time_commitment', $opportunity->time_commitment) == 'flexible' ? 'selected' : ''); ?>>Flexible</option>
                                        <option value="ongoing" <?php echo e(old('time_commitment', $opportunity->time_commitment) == 'ongoing' ? 'selected' : ''); ?>>Ongoing</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Required Skills -->
                        <div class="mb-3">
                            <label for="required_skills" class="form-label">Required Skills</label>
                            <input type="text" class="form-control" id="required_skills" name="required_skills" value="<?php echo e(old('required_skills', is_array($opportunity->required_skills) ? implode(', ', $opportunity->required_skills) : '')); ?>" placeholder="Enter skills separated by commas">
                            <div class="form-text">Enter skills separated by commas (e.g., Communication, Leadership, Computer Skills)</div>
                        </div>

                        <!-- Location and Capacity -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" value="<?php echo e(old('location', $opportunity->location)); ?>" placeholder="Volunteer location">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_volunteers" class="form-label">Maximum Volunteers</label>
                                    <input type="number" class="form-control" id="max_volunteers" name="max_volunteers" value="<?php echo e(old('max_volunteers', $opportunity->max_volunteers)); ?>" min="1" placeholder="Leave empty for unlimited">
                                </div>
                            </div>
                        </div>

                        <!-- Timeline -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="application_deadline" name="application_deadline" value="<?php echo e(old('application_deadline', $opportunity->application_deadline ? $opportunity->application_deadline->format('Y-m-d') : '')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e(old('start_date', $opportunity->start_date ? $opportunity->start_date->format('Y-m-d') : '')); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e(old('end_date', $opportunity->end_date ? $opportunity->end_date->format('Y-m-d') : '')); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Status Options -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', $opportunity->is_active) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active (accepting applications)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" <?php echo e(old('is_featured', $opportunity->is_featured) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_featured">
                                            Featured opportunity
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Opportunity
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <span class="badge bg-<?php echo e($opportunity->is_active ? 'success' : 'danger'); ?> p-3 mb-3" style="font-size: 1rem;">
                            <?php echo e($opportunity->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                        <p class="text-muted mb-0">Current opportunity status</p>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0"><?php echo e($opportunity->created_at->format('F j, Y')); ?></p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0"><?php echo e($opportunity->updated_at->format('F j, Y g:i A')); ?></p>
                    </div>
                    <?php if($opportunity->creator): ?>
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0"><?php echo e($opportunity->creator->first_name); ?> <?php echo e($opportunity->creator->last_name); ?></p>
                    </div>
                    <?php endif; ?>
                    <?php if($opportunity->max_volunteers): ?>
                    <div class="mb-3">
                        <small class="text-muted">Max Volunteers</small>
                        <p class="mb-0"><?php echo e($opportunity->max_volunteers); ?></p>
                    </div>
                    <?php endif; ?>
                    <?php if($opportunity->applications): ?>
                    <div class="mb-3">
                        <small class="text-muted">Applications</small>
                        <p class="mb-0"><?php echo e($opportunity->applications->count()); ?> received</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Preview</h6>
                </div>
                <div class="card-body">
                    <h6 id="preview-title"><?php echo e($opportunity->title); ?></h6>
                    <p class="text-muted small" id="preview-category"><?php echo e(ucfirst(str_replace('_', ' ', $opportunity->category))); ?></p>
                    <p class="text-muted small" id="preview-commitment">
                        <i class="fas fa-clock me-1"></i><?php echo e(ucfirst(str_replace('_', ' ', $opportunity->time_commitment))); ?>

                    </p>
                    <?php if($opportunity->location): ?>
                    <p class="text-muted small" id="preview-location">
                        <i class="fas fa-map-marker-alt me-1"></i><?php echo e($opportunity->location); ?>

                    </p>
                    <?php endif; ?>
                    <p class="text-muted small" id="preview-deadline">
                        <i class="fas fa-calendar-times me-1"></i>Apply by <?php echo e($opportunity->application_deadline ? $opportunity->application_deadline->format('M j, Y') : 'Not set'); ?>

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const categoryInput = document.getElementById('category');
    const commitmentInput = document.getElementById('time_commitment');
    const locationInput = document.getElementById('location');
    const deadlineInput = document.getElementById('application_deadline');
    
    titleInput.addEventListener('input', function() {
        document.getElementById('preview-title').textContent = this.value || '<?php echo e($opportunity->title); ?>';
    });
    
    categoryInput.addEventListener('change', function() {
        document.getElementById('preview-category').textContent = this.options[this.selectedIndex].text;
    });
    
    commitmentInput.addEventListener('change', function() {
        document.getElementById('preview-commitment').innerHTML = '<i class="fas fa-clock me-1"></i>' + this.options[this.selectedIndex].text;
    });
    
    if (locationInput) {
        locationInput.addEventListener('input', function() {
            const previewLocation = document.getElementById('preview-location');
            if (this.value) {
                if (previewLocation) {
                    previewLocation.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i>' + this.value;
                } else {
                    const newLocation = document.createElement('p');
                    newLocation.className = 'text-muted small';
                    newLocation.id = 'preview-location';
                    newLocation.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i>' + this.value;
                    document.getElementById('preview-commitment').parentNode.appendChild(newLocation);
                }
            } else if (previewLocation) {
                previewLocation.remove();
            }
        });
    }
    
    deadlineInput.addEventListener('change', function() {
        if (this.value) {
            const date = new Date(this.value);
            document.getElementById('preview-deadline').innerHTML = '<i class="fas fa-calendar-times me-1"></i>Apply by ' + date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/volunteers/opportunities/edit.blade.php ENDPATH**/ ?>