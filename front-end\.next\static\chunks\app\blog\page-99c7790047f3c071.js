(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return i}});let s=r(8229),a=r(1264),l=r(3063),n=s._(r(1193));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=l.Image},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:i}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let l=a(t)||a(s);return n[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return l(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var s=r(2596),a=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(5155),a=r(6874),l=r.n(a),n=r(6766),i=r(8145),o=r(7168),c=r(9852),d=r(8482),x=r(7924),u=r(1154),m=r(9074),h=r(1007),g=r(4186),f=r(2138),p=r(2115);let v="http://localhost:8000/api/v1";function b(){let[e,t]=(0,p.useState)([]),[r,a]=(0,p.useState)([]),[b,y]=(0,p.useState)(!0),[j,N]=(0,p.useState)(null),[w,k]=(0,p.useState)(""),[A,S]=(0,p.useState)("All"),[C,E]=(0,p.useState)(1),[_,R]=(0,p.useState)(1),[O,P]=(0,p.useState)(0),z=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{let a;y(!0),N(null);let l=new URLSearchParams({page:e.toString(),per_page:"9",...r&&{search:r},...s&&"All"!==s&&{category:s}}),n=await fetch("".concat(v,"/blog/posts?").concat(l));if(!n.ok)throw Error("HTTP error! status: ".concat(n.status));let i=await n.text();try{a=JSON.parse(i)}catch(e){throw console.error("Response was not valid JSON:",i.substring(0,200)),Error("Server returned invalid response")}if(a.success)t(a.posts.data||[]),E(a.posts.current_page||1),R(a.posts.last_page||1),P(a.posts.total||0);else throw Error("API request failed")}catch(e){console.error("Error fetching posts:",e),N("Failed to load blog posts. Please check if the server is running."),t([])}finally{y(!1)}},$=async()=>{try{let e;let t=await fetch("".concat(v,"/blog/categories"));if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let r=await t.text();try{e=JSON.parse(r)}catch(e){throw console.error("Categories response was not valid JSON:",r.substring(0,200)),Error("Server returned invalid response")}if(e.success)a(e.data||[]);else throw Error("Failed to fetch categories")}catch(e){console.error("Error fetching categories:",e),a([])}};(0,p.useEffect)(()=>{z(),$()},[]),(0,p.useEffect)(()=>{let e=setTimeout(()=>{z(1,w,A)},300);return()=>clearTimeout(e)},[w,A]);let L=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),F=e=>e.reading_time||"5 min read",T=e=>{if(e.featured_image_url)return e.featured_image_url;let t=["https://images.unsplash.com/photo-1529070538774-1843cb3265df","https://images.unsplash.com/photo-1593113598332-cd288d649433","https://images.unsplash.com/photo-1559027615-cd4628902d4a","https://images.unsplash.com/photo-1454165804606-c3d57bc86b40","https://images.unsplash.com/photo-1517048676732-d65bc937f952","https://images.unsplash.com/photo-1488521787991-ed7bbaae773c","https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca","https://images.unsplash.com/photo-1469571486292-0ba58a3f068b","https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a","https://images.unsplash.com/photo-1484712401471-05c7215830eb"],r=e.id%t.length;return t[r]+"?w=800&h=500&fit=crop&crop=center"},W=["All",...(r||[]).map(e=>e.name)],M=e&&e.length>0?e[0]:null,V=e&&e.length>1?e.slice(1):[];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("section",{className:"relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white py-20 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"container relative px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6 leading-tight",children:["Stories of ",(0,s.jsx)("span",{className:"text-amber-300",children:"Impact"})]}),(0,s.jsx)("p",{className:"text-xl md:text-2xl text-green-100 mb-8 leading-relaxed",children:"Discover the transformative stories behind our work. Read about communities we serve, challenges we overcome, and victories we celebrate together."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(o.$,{size:"lg",className:"bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full",children:"Latest Stories"}),(0,s.jsx)(l(),{href:"/contact",children:(0,s.jsx)(o.$,{size:"lg",variant:"outline",className:"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white px-8 py-3 rounded-full font-semibold",children:"Become a Volunteer"})})]})]})})]}),(0,s.jsxs)("main",{className:"flex-1 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)("section",{className:"py-12 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 items-center justify-between",children:[(0,s.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,s.jsx)(x.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)(c.p,{placeholder:"Search articles, authors, topics...",value:w,onChange:e=>k(e.target.value),className:"pl-12 pr-4 py-3 rounded-full border-2 border-gray-200 focus:border-green-500 text-lg"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:W.map(e=>(0,s.jsx)(o.$,{variant:A===e?"default":"outline",size:"sm",onClick:()=>S(e),className:"transition-all duration-200 rounded-full px-4 py-2 ".concat(A===e?"bg-green-600 hover:bg-green-700 text-white":"border-gray-300 hover:bg-green-50 hover:border-green-300 text-gray-600"),children:e},e))})]}),(0,s.jsx)("div",{className:"mt-4 text-gray-600 dark:text-gray-400",children:b?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"}),"Loading articles..."]}):"Showing ".concat(e.length," of ").concat(O," articles")})]})})}),b&&(0,s.jsx)("section",{className:"py-16",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 animate-spin text-green-600 mr-2"}),(0,s.jsx)("span",{className:"text-lg text-gray-600",children:"Loading articles..."})]})})})}),j&&!b&&(0,s.jsx)("section",{className:"py-16",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"text-center bg-red-50 border border-red-200 rounded-lg p-8",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"h-12 w-12 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h3",{className:"text-2xl font-semibold text-red-900 mb-2",children:"Unable to Load Blog Posts"}),(0,s.jsx)("p",{className:"text-red-700 mb-6",children:j}),(0,s.jsx)(o.$,{onClick:()=>{N(null),z(),$()},className:"bg-red-600 hover:bg-red-700 text-white rounded-full px-6 py-2",children:"Try Again"})]})})})}),!b&&!j&&M&&"All"===A&&!w&&(0,s.jsx)("section",{className:"py-16 bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-900",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(i.E,{className:"bg-amber-500 hover:bg-amber-600 text-black font-semibold px-4 py-2 rounded-full text-sm",children:"✨ Featured Story"})}),(0,s.jsx)(d.Zp,{className:"overflow-hidden shadow-2xl rounded-3xl bg-white dark:bg-gray-800 border-0",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2",children:[(0,s.jsxs)("div",{className:"relative h-80 lg:h-full",children:[(0,s.jsx)(n.default,{src:T(M),alt:M.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent lg:bg-gradient-to-r"})]}),(0,s.jsxs)(d.Wu,{className:"p-8 lg:p-12 flex flex-col justify-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[M.category&&(0,s.jsx)(i.E,{variant:"secondary",className:"bg-green-100 text-green-800 rounded-full px-3 py-1",children:M.category.name}),(0,s.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1"}),L(M.published_at)]})]}),(0,s.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 leading-tight",children:M.title}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed",children:M.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:M.author.avatar?(0,s.jsx)(n.default,{src:M.author.avatar,alt:M.author.name,width:40,height:40,className:"rounded-full"}):(0,s.jsx)(h.A,{className:"h-5 w-5 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:M.author.name}),(0,s.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,s.jsx)(g.A,{className:"h-3 w-3 mr-1"}),F(M)]})]})]}),(0,s.jsx)(l(),{href:"/blog/".concat(M.slug),children:(0,s.jsxs)(o.$,{className:"bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group",children:["Read Story",(0,s.jsx)(f.A,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})})]})]})]})})]})})}),!b&&!j&&(0,s.jsx)("section",{className:"py-16",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:e.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:("All"!==A||w?e:V).map(e=>(0,s.jsx)(d.Zp,{className:"group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 rounded-2xl bg-white dark:bg-gray-800 border-0 hover:-translate-y-2",children:(0,s.jsxs)(l(),{href:"/blog/".concat(e.slug),children:[(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[(0,s.jsx)(n.default,{src:T(e),alt:e.title,fill:!0,className:"object-cover group-hover:scale-110 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"}),e.category&&(0,s.jsx)("div",{className:"absolute top-4 left-4",children:(0,s.jsx)(i.E,{className:"bg-white/90 text-gray-800 rounded-full px-3 py-1 text-xs font-medium",children:e.category.name})})]}),(0,s.jsxs)(d.Wu,{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-gray-500 text-sm mb-3",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:L(e.published_at)}),(0,s.jsx)("span",{children:"•"}),(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:F(e)})]}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-green-600 transition-colors line-clamp-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:e.author.avatar?(0,s.jsx)(n.default,{src:e.author.avatar,alt:e.author.name,width:32,height:32,className:"rounded-full"}):(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-600"})}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e.author.name})]}),(0,s.jsxs)("div",{className:"flex items-center text-green-600 font-medium group-hover:text-green-700 transition-colors",children:[(0,s.jsx)("span",{className:"text-sm",children:"Read more"}),(0,s.jsx)(f.A,{className:"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform"})]})]}),e.tags&&e.tags.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-1 mt-4",children:e.tags.slice(0,3).map(e=>(0,s.jsx)(i.E,{variant:"outline",className:"text-xs px-2 py-1 rounded-full border-gray-300 text-gray-600",children:e},e))})]})]})},e.slug))}),_>1&&(0,s.jsx)("div",{className:"flex justify-center mt-12",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",onClick:()=>z(C-1,w,A),disabled:1===C,className:"rounded-full",children:"Previous"}),Array.from({length:Math.min(5,_)},(e,t)=>{let r=t+1;return(0,s.jsx)(o.$,{variant:C===r?"default":"outline",onClick:()=>z(r,w,A),className:"rounded-full w-10 h-10 p-0",children:r},r)}),(0,s.jsx)(o.$,{variant:"outline",onClick:()=>z(C+1,w,A),disabled:C===_,className:"rounded-full",children:"Next"})]})})]}):(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 dark:text-white mb-2",children:"No articles found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Try adjusting your search terms or category filters"}),(0,s.jsx)(o.$,{onClick:()=>{k(""),S("All")},variant:"outline",className:"rounded-full px-6 py-2",children:"Clear Filters"})]})})})}),(0,s.jsx)("section",{className:"py-16 bg-green-600 text-white",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Stay Updated with Our Stories"}),(0,s.jsx)("p",{className:"text-xl text-green-100 mb-8",children:"Get the latest impact stories, news, and insights delivered directly to your inbox"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto",children:[(0,s.jsx)(c.p,{placeholder:"Enter your email",className:"bg-white text-gray-900 border-0 rounded-full px-6 py-3"}),(0,s.jsx)(o.$,{className:"bg-amber-500 hover:bg-amber-600 text-black font-semibold rounded-full px-8 py-3",children:"Subscribe"})]}),(0,s.jsx)("p",{className:"text-sm text-green-200 mt-4",children:"Join 5,000+ supporters staying informed about our impact"})]})})})]})]})}},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>l});var s=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function n(...e){return s.useCallback(l(...e),e)}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(1469),a=r.n(s)},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(5155),a=r(2115),l=r(9708),n=r(2085),i=r(3999);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:c=!1,...d}=e,x=c?l.DX:"button";return(0,s.jsx)(x,{className:(0,i.cn)(o({variant:a,size:n,className:r})),ref:t,suppressHydrationWarning:!0,...d})});c.displayName="Button"},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8145:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(5155);r(2115);var a=r(2085),l=r(3999);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:r}),t),...a})}},8380:(e,t,r)=>{Promise.resolve().then(r.bind(r,5406))},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>n});var s=r(2115),a=r(6101),l=r(5155),n=s.forwardRef((e,t)=>{let{children:r,...a}=e,n=s.Children.toArray(r),o=n.find(c);if(o){let e=o.props.children,r=n.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(i,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,l.jsx)(i,{...a,ref:t,children:r})});n.displayName="Slot";var i=s.forwardRef((e,t)=>{let{children:r,...l}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let a=e[s],l=t[s];/^on[A-Z]/.test(s)?a&&l?r[s]=(...e)=>{l(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...l}:"className"===s&&(r[s]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:t?(0,a.t)(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});i.displayName="SlotClone";var o=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===o}},9852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,suppressHydrationWarning:!0,...n})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[598,874,63,441,684,358],()=>t(8380)),_N_E=e.O()}]);