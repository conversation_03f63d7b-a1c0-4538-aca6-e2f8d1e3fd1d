@extends('layouts.admin')

@section('title', 'Create Newsletter Campaign')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create Newsletter Campaign</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.newsletter.index') }}">Newsletter</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.newsletter.campaigns') }}">Campaigns</a></li>
                        <li class="breadcrumb-item active">Create Campaign</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-envelope-open me-2"></i>
                        Newsletter Campaign Details
                    </h6>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('admin.newsletter.campaigns.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="title" class="form-label">Campaign Title <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="subject" name="subject" value="{{ old('subject') }}" required>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="content" class="form-label">Campaign Content <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="content" name="content" rows="12" required>{{ old('content') }}</textarea>
                                            <div class="form-text">Use HTML tags for formatting</div>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="preview_text" class="form-label">Preview Text</label>
                                            <input type="text" class="form-control" id="preview_text" name="preview_text" value="{{ old('preview_text') }}" placeholder="Text that appears in email preview...">
                                            <div class="form-text">This text appears in the email preview (max 150 characters)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Targeting -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Targeting</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="recipient_type" class="form-label">Send To <span class="text-danger">*</span></label>
                                                    <select class="form-control" id="recipient_type" name="recipient_type" required>
                                                        <option value="">Select Recipients</option>
                                                        <option value="all" {{ old('recipient_type') == 'all' ? 'selected' : '' }}>All Subscribers</option>
                                                        <option value="active" {{ old('recipient_type') == 'active' ? 'selected' : '' }}>Active Subscribers Only</option>
                                                        <option value="volunteers" {{ old('recipient_type') == 'volunteers' ? 'selected' : '' }}>Volunteers Only</option>
                                                        <option value="donors" {{ old('recipient_type') == 'donors' ? 'selected' : '' }}>Donors Only</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="estimated_recipients" class="form-label">Estimated Recipients</label>
                                                    <input type="number" class="form-control" id="estimated_recipients" name="estimated_recipients" value="{{ old('estimated_recipients', 0) }}" readonly>
                                                    <div class="form-text">Automatically calculated based on selection</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-4">
                                <!-- Campaign Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Campaign Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="scheduled" {{ old('status') == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                                <option value="sent" {{ old('status') == 'sent' ? 'selected' : '' }}>Send Now</option>
                                            </select>
                                        </div>

                                        <div class="form-group mb-3" id="schedule_group" style="display: none;">
                                            <label for="scheduled_at" class="form-label">Schedule Date & Time</label>
                                            <input type="datetime-local" class="form-control" id="scheduled_at" name="scheduled_at" value="{{ old('scheduled_at') }}">
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" id="from_name" name="from_name" value="{{ old('from_name', 'HLTKKQ Foundation') }}">
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="from_email" class="form-label">From Email</label>
                                            <input type="email" class="form-control" id="from_email" name="from_email" value="{{ old('from_email', '<EMAIL>') }}">
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="template" class="form-label">Email Template</label>
                                            <select class="form-control" id="template" name="template">
                                                <option value="default" {{ old('template') == 'default' ? 'selected' : '' }}>Default Template</option>
                                                <option value="newsletter" {{ old('template') == 'newsletter' ? 'selected' : '' }}>Newsletter Template</option>
                                                <option value="announcement" {{ old('template') == 'announcement' ? 'selected' : '' }}>Announcement Template</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary" name="action" value="save">
                                                <i class="fas fa-save me-2"></i>Save Campaign
                                            </button>
                                            <button type="submit" class="btn btn-info" name="action" value="preview">
                                                <i class="fas fa-eye me-2"></i>Preview & Test
                                            </button>
                                            <a href="{{ route('admin.newsletter.campaigns') }}" class="btn btn-secondary">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const scheduleGroup = document.getElementById('schedule_group');
    const recipientTypeSelect = document.getElementById('recipient_type');
    const estimatedRecipientsInput = document.getElementById('estimated_recipients');

    // Show/hide schedule date based on status
    statusSelect.addEventListener('change', function() {
        if (this.value === 'scheduled') {
            scheduleGroup.style.display = 'block';
            document.getElementById('scheduled_at').required = true;
        } else {
            scheduleGroup.style.display = 'none';
            document.getElementById('scheduled_at').required = false;
        }
    });

    // Estimate recipients based on type
    recipientTypeSelect.addEventListener('change', function() {
        const estimatedCounts = {
            'all': 1250,
            'active': 1180,
            'volunteers': 156,
            'donors': 340
        };
        
        estimatedRecipientsInput.value = estimatedCounts[this.value] || 0;
    });

    // Trigger initial state
    statusSelect.dispatchEvent(new Event('change'));
});
</script>
@endsection 