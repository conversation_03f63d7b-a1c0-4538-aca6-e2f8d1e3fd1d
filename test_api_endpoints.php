<?php

// Test Laravel API endpoints for scholarship data
$baseUrl = 'http://127.0.0.1:8000';

echo "=== Testing Laravel API Endpoints ===\n\n";

// Test 1: Get all public scholarships
echo "1. Testing GET /api/v1/public-scholarships\n";
$response = file_get_contents($baseUrl . '/api/v1/public-scholarships');
$data = json_decode($response, true);

if ($data && isset($data['success']) && $data['success']) {
    echo "✓ API endpoint working\n";
    echo "✓ Found " . count($data['data']) . " scholarships\n";
    
    if (!empty($data['data'])) {
        $firstScholarship = $data['data'][0];
        echo "✓ First scholarship: " . $firstScholarship['title'] . "\n";
        echo "✓ Amount: ₦" . number_format($firstScholarship['amount']) . "\n";
        echo "✓ Category: " . $firstScholarship['category'] . "\n";
        echo "✓ Status: " . $firstScholarship['status'] . "\n";
        echo "✓ Custom fields: " . count($firstScholarship['custom_fields'] ?? []) . "\n";
    }
} else {
    echo "✗ API endpoint failed\n";
    echo "Response: " . $response . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Get specific scholarship (ID 5)
echo "2. Testing GET /api/v1/public-scholarships/5\n";
$response = file_get_contents($baseUrl . '/api/v1/public-scholarships/5');
$data = json_decode($response, true);

if ($data && isset($data['success']) && $data['success']) {
    echo "✓ Individual scholarship API working\n";
    $scholarship = $data['data'];
    echo "✓ Title: " . $scholarship['title'] . "\n";
    echo "✓ Amount: ₦" . number_format($scholarship['amount']) . "\n";
    echo "✓ Category: " . $scholarship['category'] . "\n";
    echo "✓ Deadline: " . $scholarship['application_deadline'] . "\n";
    echo "✓ Custom fields: " . count($scholarship['custom_fields'] ?? []) . "\n";
    
    if (!empty($scholarship['custom_fields'])) {
        echo "✓ Sample custom fields:\n";
        foreach (array_slice($scholarship['custom_fields'], 0, 3) as $field) {
            echo "  - " . $field['field_name'] . " (" . $field['field_type'] . ")\n";
        }
    }
    
    if (isset($scholarship['category_instructions'])) {
        echo "✓ Category instructions available\n";
        echo "  - Filled by: " . $scholarship['category_instructions']['filled_by'] . "\n";
    }
} else {
    echo "✗ Individual scholarship API failed\n";
    echo "Response: " . $response . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Check database directly
echo "3. Testing database connection\n";
require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    $scholarships = \App\Models\Scholarship::with('customFields')->get();
    echo "✓ Database connection working\n";
    echo "✓ Found " . $scholarships->count() . " scholarships in database\n";
    
    foreach ($scholarships as $scholarship) {
        echo "  - ID {$scholarship->id}: {$scholarship->title} (₦" . number_format($scholarship->amount) . ")\n";
        echo "    Category: {$scholarship->category}, Status: {$scholarship->status}\n";
        echo "    Custom fields: " . $scholarship->customFields->count() . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Database test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
