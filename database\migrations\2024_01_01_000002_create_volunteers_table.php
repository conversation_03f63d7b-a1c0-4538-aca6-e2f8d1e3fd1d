<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('volunteers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('application_status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->json('skills')->nullable();
            $table->json('interests')->nullable();
            $table->json('availability')->nullable();
            $table->text('experience')->nullable();
            $table->text('motivation')->nullable();
            $table->enum('background_check_status', ['pending', 'passed', 'failed'])->nullable();
            $table->json('emergency_contact')->nullable();
            $table->decimal('hours_logged', 8, 2)->default(0.00);
            $table->json('certifications')->nullable();
            $table->timestamp('applied_at')->default(now());
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'application_status']);
            $table->index('application_status');
            $table->index('approved_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('volunteers');
    }
}; 