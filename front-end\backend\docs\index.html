<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLTKKQ Foundation API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <div class="sidebar-header">
                        <h3>HLTKKQ API Docs</h3>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#overview">
                                <i class="fas fa-home"></i> Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#authentication">
                                <i class="fas fa-lock"></i> Authentication
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#endpoints">
                                <i class="fas fa-code"></i> API Endpoints
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#setup">
                                <i class="fas fa-cog"></i> Setup Guide
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#nextjs">
                                <i class="fas fa-react"></i> Next.js Integration
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1>HLTKKQ Foundation API Documentation</h1>
                </div>

                <!-- Overview Section -->
                <section id="overview" class="mb-5">
                    <h2>Overview</h2>
                    <p>The HLTKKQ Foundation API is a comprehensive RESTful API system built with Laravel, designed to support the foundation's operations including:</p>
                    <ul>
                        <li>User Management & Authentication</li>
                        <li>Volunteer Management</li>
                        <li>Program & Event Management</li>
                        <li>Blog & Content Management</li>
                        <li>Donation Processing</li>
                        <li>Newsletter Management</li>
                    </ul>
                </section>

                <!-- Authentication Section -->
                <section id="authentication" class="mb-5">
                    <h2>Authentication</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Session-based Authentication</h5>
                            <p>The API uses Laravel Sanctum for session-based authentication. Here's how it works:</p>
                            <pre><code>
// 1. Get CSRF Token
GET /sanctum/csrf-cookie

// 2. Login
POST /api/auth/login
{
    "email": "<EMAIL>",
    "password": "password"
}

// 3. Include CSRF Token in subsequent requests
Headers: {
    'X-CSRF-TOKEN': 'your-csrf-token'
}
                            </code></pre>
                        </div>
                    </div>
                </section>

                <!-- API Endpoints Section -->
                <section id="endpoints" class="mb-5">
                    <h2>API Endpoints</h2>
                    <div class="accordion" id="endpointsAccordion">
                        <!-- Authentication Endpoints -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#authEndpoints">
                                    Authentication Endpoints
                                </button>
                            </h2>
                            <div id="authEndpoints" class="accordion-collapse collapse show" data-bs-parent="#endpointsAccordion">
                                <div class="accordion-body">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Method</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>/api/auth/register</td>
                                                <td>POST</td>
                                                <td>Register new user</td>
                                            </tr>
                                            <tr>
                                                <td>/api/auth/login</td>
                                                <td>POST</td>
                                                <td>User login</td>
                                            </tr>
                                            <tr>
                                                <td>/api/auth/logout</td>
                                                <td>POST</td>
                                                <td>User logout</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- User Management Endpoints -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#userEndpoints">
                                    User Management
                                </button>
                            </h2>
                            <div id="userEndpoints" class="accordion-collapse collapse" data-bs-parent="#endpointsAccordion">
                                <div class="accordion-body">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Method</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>/api/user/profile</td>
                                                <td>GET</td>
                                                <td>Get user profile</td>
                                            </tr>
                                            <tr>
                                                <td>/api/user/profile</td>
                                                <td>PUT</td>
                                                <td>Update user profile</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Setup Guide Section -->
                <section id="setup" class="mb-5">
                    <h2>Setup Guide</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Laravel Backend Setup</h5>
                            <ol>
                                <li>Clone the repository</li>
                                <li>Install dependencies: <code>composer install</code></li>
                                <li>Copy .env.example to .env</li>
                                <li>Configure database settings</li>
                                <li>Run migrations: <code>php artisan migrate</code></li>
                                <li>Start the server: <code>php artisan serve</code></li>
                            </ol>
                        </div>
                    </div>
                </section>

                <!-- Next.js Integration Section -->
                <section id="nextjs" class="mb-5">
                    <h2>Next.js Integration</h2>
                    <div class="card">
                        <div class="card-body">
                            <h5>Frontend Setup</h5>
                            <ol>
                                <li>Create .env.local file with API configuration</li>
                                <li>Install required dependencies</li>
                                <li>Configure axios instance</li>
                                <li>Set up authentication hooks</li>
                            </ol>
                            <pre><code>
// .env.local
NEXT_PUBLIC_API_URL=http://hltkkq.test
NEXT_PUBLIC_APP_URL=http://localhost:3000
                            </code></pre>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>