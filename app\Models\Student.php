<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Student extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'student_id',
        'first_name',
        'last_name',
        'middle_name',
        'date_of_birth',
        'gender',
        'phone_number',
        'email',
        'address',
        'city',
        'state',
        'country',
        'current_grade',
        'academic_year',
        'school_id',
        'matriculation_number',
        'student_type',
        'guardian_name',
        'guardian_phone',
        'guardian_email',
        'guardian_relationship',
        'status',
        'enrollment_date',
        'graduation_date',
        'additional_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'enrollment_date' => 'date',
        'graduation_date' => 'date',
        'additional_info' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = ['full_name', 'grade_display', 'next_grade'];

    /**
     * Boot method to auto-generate student_id
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->student_id)) {
                $model->student_id = static::generateStudentId();
            }
        });
    }

    /**
     * Generate a unique student ID
     */
    public static function generateStudentId(): string
    {
        do {
            $studentId = 'HLTKKQ-STU-' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('student_id', $studentId)->exists());

        return $studentId;
    }

    /**
     * Get the student's full name.
     */
    public function getFullNameAttribute(): string
    {
        $name = "{$this->first_name} {$this->last_name}";
        if ($this->middle_name) {
            $name = "{$this->first_name} {$this->middle_name} {$this->last_name}";
        }
        return $name;
    }

    /**
     * Get the display format for the current grade.
     */
    public function getGradeDisplayAttribute(): string
    {
        return match($this->current_grade) {
            'primary_1' => 'Primary 1',
            'primary_2' => 'Primary 2',
            'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4',
            'primary_5' => 'Primary 5',
            'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1 (JSS 1)',
            'secondary_2' => 'Secondary 2 (JSS 2)',
            'secondary_3' => 'Secondary 3 (JSS 3)',
            'secondary_4' => 'Secondary 4 (SSS 1)',
            'secondary_5' => 'Secondary 5 (SSS 2)',
            'secondary_6' => 'Secondary 6 (SSS 3)',
            'university_1' => 'University Year 1',
            'university_2' => 'University Year 2',
            'university_3' => 'University Year 3',
            'university_4' => 'University Year 4',
            'university_5' => 'University Year 5',
            'university_6' => 'University Year 6',
            'graduated' => 'Graduated',
            default => ucfirst(str_replace('_', ' ', $this->current_grade))
        };
    }

    /**
     * Get the next grade for progression.
     */
    public function getNextGradeAttribute(): ?string
    {
        return match($this->current_grade) {
            'primary_1' => 'primary_2',
            'primary_2' => 'primary_3',
            'primary_3' => 'primary_4',
            'primary_4' => 'primary_5',
            'primary_5' => 'primary_6',
            'primary_6' => 'secondary_1',
            'secondary_1' => 'secondary_2',
            'secondary_2' => 'secondary_3',
            'secondary_3' => 'secondary_4',
            'secondary_4' => 'secondary_5',
            'secondary_5' => 'secondary_6',
            'secondary_6' => 'university_1',
            'university_1' => 'university_2',
            'university_2' => 'university_3',
            'university_3' => 'university_4',
            'university_4' => 'university_5',
            'university_5' => 'university_6',
            'university_6' => 'graduated',
            default => null
        };
    }

    /**
     * Check if student is eligible for next grade progression.
     */
    public function isEligibleForProgression(): bool
    {
        return $this->status === 'active' && 
               $this->current_grade !== 'graduated' && 
               $this->next_grade !== null;
    }

    /**
     * Get the school this student belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(PartnerOrganization::class, 'school_id');
    }

    /**
     * Get all progressions for this student.
     */
    public function progressions(): HasMany
    {
        return $this->hasMany(StudentProgression::class);
    }

    /**
     * Get all scholarship applications for this student.
     */
    public function scholarshipApplications(): HasMany
    {
        return $this->hasMany(ScholarshipApplication::class);
    }

    /**
     * Alias for scholarshipApplications for easier access
     */
    public function applications(): HasMany
    {
        return $this->scholarshipApplications();
    }

    /**
     * Get schools this student has been enrolled in.
     */
    public function schools(): BelongsToMany
    {
        return $this->belongsToMany(PartnerOrganization::class, 'school_students', 'student_id', 'school_id')
                    ->withPivot(['enrollment_date', 'graduation_date', 'status', 'enrollment_grade', 'current_grade', 'academic_year', 'notes'])
                    ->withTimestamps();
    }

    /**
     * Get current school enrollment.
     */
    public function currentSchoolEnrollment()
    {
        return $this->schools()->wherePivot('status', 'active')->first();
    }

    /**
     * Scope for active students.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for students by grade.
     */
    public function scopeByGrade($query, $grade)
    {
        return $query->where('current_grade', $grade);
    }

    /**
     * Scope for students by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('student_type', $type);
    }

    /**
     * Scope for students by school.
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope for students by academic year.
     */
    public function scopeByAcademicYear($query, $year)
    {
        return $query->where('academic_year', $year);
    }
}
