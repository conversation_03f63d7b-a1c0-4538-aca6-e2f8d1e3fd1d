<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\ScholarshipTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class AdminScholarshipTemplateController extends Controller
{
    /**
     * Get all scholarship templates
     */
    public function index(Request $request): JsonResponse
    {
        $query = ScholarshipTemplate::with('creator:id,name,email');

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Search by name
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $templates = $query->latest()->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Store a new template
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:200|unique:scholarship_templates,name',
            'category' => 'required|string|in:primary,secondary,university',
            'description' => 'nullable|string|max:1000',
            'default_fields' => 'required|array|min:1',
            'default_fields.*.field_name' => 'required|string|max:100|regex:/^[a-zA-Z0-9_]+$/',
            'default_fields.*.field_label' => 'required|string|max:200',
            'default_fields.*.field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'default_fields.*.field_options' => 'nullable|array',
            'default_fields.*.is_required' => 'boolean',
            'validation_rules' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate field names within the template
        $fieldNames = collect($request->default_fields)->pluck('field_name')->toArray();
        if (count($fieldNames) !== count(array_unique($fieldNames))) {
            return response()->json([
                'success' => false,
                'message' => 'Duplicate field names found within the template'
            ], 422);
        }

        $data = $validator->validated();
        $data['created_by'] = Auth::id();

        $template = ScholarshipTemplate::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Template created successfully',
            'data' => $template->load('creator:id,name,email')
        ], 201);
    }

    /**
     * Show a specific template
     */
    public function show($id): JsonResponse
    {
        $template = ScholarshipTemplate::with('creator:id,name,email')->findOrFail($id);

        // Add usage statistics
        $template->usage_count = $this->getTemplateUsageCount($template);

        return response()->json([
            'success' => true,
            'data' => $template
        ]);
    }

    /**
     * Update a template
     */
    public function update(Request $request, $id): JsonResponse
    {
        $template = ScholarshipTemplate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:200|unique:scholarship_templates,name,' . $id,
            'category' => 'string|in:primary,secondary,university',
            'description' => 'nullable|string|max:1000',
            'default_fields' => 'array|min:1',
            'default_fields.*.field_name' => 'required|string|max:100|regex:/^[a-zA-Z0-9_]+$/',
            'default_fields.*.field_label' => 'required|string|max:200',
            'default_fields.*.field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'default_fields.*.field_options' => 'nullable|array',
            'default_fields.*.is_required' => 'boolean',
            'validation_rules' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate field names within the template if default_fields is being updated
        if ($request->has('default_fields')) {
            $fieldNames = collect($request->default_fields)->pluck('field_name')->toArray();
            if (count($fieldNames) !== count(array_unique($fieldNames))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Duplicate field names found within the template'
                ], 422);
            }
        }

        $template->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Template updated successfully',
            'data' => $template->fresh()->load('creator:id,name,email')
        ]);
    }

    /**
     * Delete a template
     */
    public function destroy($id): JsonResponse
    {
        $template = ScholarshipTemplate::findOrFail($id);

        // Check if template is being used
        $usageCount = $this->getTemplateUsageCount($template);
        if ($usageCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete template. It has been used {$usageCount} time(s). Consider deactivating it instead."
            ], 422);
        }

        $template->delete();

        return response()->json([
            'success' => true,
            'message' => 'Template deleted successfully'
        ]);
    }

    /**
     * Toggle template active status
     */
    public function toggleActive($id): JsonResponse
    {
        $template = ScholarshipTemplate::findOrFail($id);
        $template->update(['is_active' => !$template->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Template status updated successfully',
            'data' => [
                'template_id' => $template->id,
                'is_active' => $template->is_active
            ]
        ]);
    }

    /**
     * Get predefined templates for each category
     */
    public function getPredefinedTemplates(): JsonResponse
    {
        $templates = [
            'primary' => [
                'name' => 'Primary School Scholarship Template',
                'description' => 'Standard template for primary school scholarship applications',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'student_class', 'field_label' => 'Student Class', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_name', 'field_label' => 'Parent/Guardian Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_phone', 'field_label' => 'Parent/Guardian Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'headmaster_name', 'field_label' => 'Headmaster Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'student_photo', 'field_label' => 'Student Photo', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'school_fee', 'field_label' => 'School Fee Amount', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true],
                ]
            ],
            'secondary' => [
                'name' => 'Secondary School Scholarship Template',
                'description' => 'Standard template for secondary school scholarship applications',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'student_class', 'field_label' => 'Student Class', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_name', 'field_label' => 'Parent/Guardian Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'parent_phone', 'field_label' => 'Parent/Guardian Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'principal_name', 'field_label' => 'Principal/Financial Officer Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'student_photo', 'field_label' => 'Student Photo', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'school_fee', 'field_label' => 'School Fee Amount', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'account_number', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true],
                ]
            ],
            'university' => [
                'name' => 'University Scholarship Template',
                'description' => 'Standard template for university scholarship applications',
                'default_fields' => [
                    ['field_name' => 'student_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true],
                    ['field_name' => 'matric_number', 'field_label' => 'Matriculation Number', 'field_type' => 'text', 'is_required' => true],
                    ['field_name' => 'student_email', 'field_label' => 'Student Email', 'field_type' => 'email', 'is_required' => true],
                    ['field_name' => 'student_phone', 'field_label' => 'Student Phone', 'field_type' => 'tel', 'is_required' => true],
                    ['field_name' => 'student_id_card', 'field_label' => 'Student ID Card', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'remita_payment', 'field_label' => 'Remita Payment Receipt', 'field_type' => 'file', 'is_required' => true],
                    ['field_name' => 'reason', 'field_label' => 'Reason for Application', 'field_type' => 'textarea', 'is_required' => true],
                    ['field_name' => 'supporting_docs', 'field_label' => 'Supporting Documents', 'field_type' => 'file', 'is_required' => false],
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Create template from predefined template
     */
    public function createFromPredefined(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'category' => 'required|string|in:primary,secondary,university',
            'name' => 'nullable|string|max:200',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $predefinedTemplates = $this->getPredefinedTemplates()->getData()->data;
        $templateData = $predefinedTemplates->{$request->category};

        // Check if template with this name already exists
        $name = $request->name ?? $templateData->name;
        if (ScholarshipTemplate::where('name', $name)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'A template with this name already exists'
            ], 422);
        }

        $template = ScholarshipTemplate::create([
            'name' => $name,
            'category' => $request->category,
            'description' => $templateData->description,
            'default_fields' => $templateData->default_fields,
            'is_active' => true,
            'created_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Template created successfully from predefined template',
            'data' => $template->load('creator:id,name,email')
        ], 201);
    }

    /**
     * Get template usage count
     */
    private function getTemplateUsageCount(ScholarshipTemplate $template): int
    {
        // This would track how many times the template was used to create scholarships
        // For now, we'll return 0 as we don't have a direct relationship
        // In a real implementation, you might track this in a separate table
        return 0;
    }
}
