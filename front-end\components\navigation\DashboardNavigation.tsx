'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useRoleAccess } from '@/hooks/useRoleAccess'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import {
  Home,
  Users,
  GraduationCap,
  FileText,
  BarChart3,
  Settings,
  User,
  LogOut,
  Menu,
  Bell,
  Search,
  BookOpen,
  School,
  TrendingUp,
  Calendar,
  Award,
  UserCheck,
  Building,
  ChevronDown
} from 'lucide-react'

interface NavigationItem {
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  permission?: string
  children?: NavigationItem[]
}

export default function DashboardNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const { user, logout } = useAuth()
  const { permissions, roleInfo } = useRoleAccess()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // Define navigation items based on user role
  const getNavigationItems = (): NavigationItem[] => {
    if (!user || !roleInfo) return []

    const baseItems: NavigationItem[] = [
      {
        label: 'Dashboard',
        href: roleInfo.isAdmin ? '/admin/dashboard' : 
              roleInfo.isPartnerOrganization ? '/dashboard/partner' : 
              '/dashboard/student',
        icon: Home
      }
    ]

    // Admin navigation
    if (roleInfo.isAdmin) {
      return [
        ...baseItems,
        {
          label: 'Scholarships',
          href: '/admin/scholarships',
          icon: Award,
          children: [
            { label: 'All Scholarships', href: '/admin/scholarships', icon: Award },
            { label: 'Applications', href: '/admin/applications', icon: FileText },
            { label: 'Categories', href: '/admin/scholarship-categories', icon: BookOpen }
          ]
        },
        {
          label: 'Users',
          href: '/admin/users',
          icon: Users,
          children: [
            { label: 'All Users', href: '/admin/users', icon: Users },
            { label: 'Partner Organizations', href: '/admin/partners', icon: Building },
            { label: 'Students', href: '/admin/students', icon: GraduationCap }
          ]
        },
        {
          label: 'Analytics',
          href: '/admin/analytics',
          icon: BarChart3
        },
        {
          label: 'Settings',
          href: '/admin/settings',
          icon: Settings
        }
      ]
    }

    // Partner Organization navigation
    if (roleInfo.isPartnerOrganization) {
      return [
        ...baseItems,
        {
          label: 'Students',
          href: '/dashboard/partner/students',
          icon: Users,
          children: [
            { label: 'All Students', href: '/dashboard/partner/students', icon: Users },
            { label: 'Add Student', href: '/dashboard/partner/students/create', icon: UserCheck },
            { label: 'Progression', href: '/dashboard/partner/progression', icon: TrendingUp }
          ]
        },
        {
          label: 'Applications',
          href: '/dashboard/partner/applications',
          icon: FileText,
          children: [
            { label: 'All Applications', href: '/dashboard/partner/applications', icon: FileText },
            { label: 'New Application', href: '/dashboard/partner/applications/create', icon: Award },
            { label: 'Primary Scholarships', href: '/dashboard/partner/applications?category=primary', icon: School },
            { label: 'Secondary Scholarships', href: '/dashboard/partner/applications?category=secondary', icon: GraduationCap }
          ]
        },
        {
          label: 'Scholarships',
          href: '/scholarships?category=primary,secondary',
          icon: Award
        },
        {
          label: 'Reports',
          href: '/dashboard/partner/reports',
          icon: BarChart3
        }
      ]
    }

    // Individual Student navigation
    if (roleInfo.isIndividualStudent) {
      return [
        ...baseItems,
        {
          label: 'My Applications',
          href: '/dashboard/student/applications',
          icon: FileText,
          children: [
            { label: 'All Applications', href: '/dashboard/student/applications', icon: FileText },
            { label: 'University Scholarships', href: '/dashboard/student/applications?category=university', icon: GraduationCap }
          ]
        },
        {
          label: 'Scholarships',
          href: '/scholarships?category=university',
          icon: Award
        },
        {
          label: 'Profile',
          href: '/dashboard/student/profile',
          icon: User
        }
      ]
    }

    return baseItems
  }

  const navigationItems = getNavigationItems()

  const handleLogout = async () => {
    await logout()
    router.push('/')
  }

  const isActiveLink = (href: string) => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }

  const renderNavigationItem = (item: NavigationItem, isMobile = false) => {
    const IconComponent = item.icon
    const isActive = isActiveLink(item.href)

    if (item.children && item.children.length > 0) {
      return (
        <DropdownMenu key={item.label}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={isActive ? "secondary" : "ghost"}
              className={`w-full justify-start ${isMobile ? 'h-12' : 'h-10'}`}
            >
              <IconComponent className={`${isMobile ? 'h-5 w-5' : 'h-4 w-4'} mr-2`} />
              {item.label}
              <ChevronDown className="ml-auto h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            {item.children.map((child) => {
              const ChildIcon = child.icon
              return (
                <DropdownMenuItem key={child.href} asChild>
                  <Link href={child.href} className="flex items-center">
                    <ChildIcon className="h-4 w-4 mr-2" />
                    {child.label}
                    {child.badge && (
                      <Badge variant="secondary" className="ml-auto">
                        {child.badge}
                      </Badge>
                    )}
                  </Link>
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    return (
      <Button
        key={item.label}
        variant={isActive ? "secondary" : "ghost"}
        className={`w-full justify-start ${isMobile ? 'h-12' : 'h-10'}`}
        asChild
      >
        <Link href={item.href}>
          <IconComponent className={`${isMobile ? 'h-5 w-5' : 'h-4 w-4'} mr-2`} />
          {item.label}
          {item.badge && (
            <Badge variant="secondary" className="ml-auto">
              {item.badge}
            </Badge>
          )}
        </Link>
      </Button>
    )
  }

  if (!user) return null

  return (
    <>
      {/* Desktop Navigation */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex flex-col flex-grow pt-5 bg-white dark:bg-gray-900 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4">
            <Link href="/" className="flex items-center space-x-2">
              <GraduationCap className="h-8 w-8 text-green-600" />
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                NGO Platform
              </span>
            </Link>
          </div>

          {/* User Info */}
          <div className="mt-5 px-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                  <User className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {user.first_name} {user.last_name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {roleInfo?.displayName}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-5 flex-1 px-2 space-y-1">
            {navigationItems.map((item) => renderNavigationItem(item))}
          </nav>

          {/* Footer Actions */}
          <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <Link href="/" className="flex items-center space-x-2">
            <GraduationCap className="h-6 w-6 text-green-600" />
            <span className="text-lg font-bold text-gray-900 dark:text-white">
              NGO Platform
            </span>
          </Link>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Bell className="h-5 w-5" />
            </Button>
            
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle className="flex items-center space-x-2">
                    <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                      <User className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="text-left">
                      <p className="text-sm font-medium">
                        {user.first_name} {user.last_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {roleInfo?.displayName}
                      </p>
                    </div>
                  </SheetTitle>
                </SheetHeader>

                <nav className="mt-6 space-y-2">
                  {navigationItems.map((item) => renderNavigationItem(item, true))}
                </nav>

                <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 h-12"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-5 w-5 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  )
}
