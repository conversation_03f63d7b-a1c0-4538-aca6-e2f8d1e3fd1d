<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class TeamMember extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'position',
        'department',
        'bio',
        'photo',
        'email',
        'phone',
        'linkedin_url',
        'twitter_url',
        'facebook_url',
        'instagram_url',
        'sort_order',
        'is_active',
        'achievements',
        'qualifications',
        'join_date',
        'first_name',
        'last_name',
        'joined_date',
        'display_order',
        'education',
        'experience'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'achievements' => 'array',
        'qualifications' => 'array',
        'join_date' => 'date',
        'joined_date' => 'date',
    ];

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute(): string
    {
        if ($this->first_name && $this->last_name) {
            return $this->first_name . ' ' . $this->last_name;
        }
        return $this->name ?? '';
    }

    /**
     * Get the photo URL attribute.
     */
    public function getPhotoUrlAttribute(): ?string
    {
        if ($this->photo) {
            return Storage::disk('public')->url($this->photo);
        }
        return null;
    }

    /**
     * Scope a query to only include active team members.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('display_order')->orderBy('name');
    }
}
