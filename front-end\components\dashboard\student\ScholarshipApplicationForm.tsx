'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  GraduationCap, 
  Upload, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Search,
  Filter
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  amount: number;
  application_deadline: string;
  eligibility_criteria: string;
  required_documents: string;
  is_active: boolean;
}

interface ScholarshipApplication {
  id: number;
  scholarship: Scholarship;
  status: string;
  submitted_at: string;
  documents?: any;
}

interface ScholarshipApplicationFormProps {
  onApplicationSubmitted?: () => void;
}

export default function ScholarshipApplicationForm({ onApplicationSubmitted }: ScholarshipApplicationFormProps) {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [applications, setApplications] = useState<ScholarshipApplication[]>([]);
  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('available');

  const [formData, setFormData] = useState({
    personal_statement: '',
    academic_achievements: '',
    financial_need_statement: '',
    career_goals: '',
    additional_info: '',
    student_id_number: '',
    current_gpa: '',
    year_of_study: '',
    institution_name: ''
  });

  const [files, setFiles] = useState({
    payment_receipt: null as File | null,
    school_id_card: null as File | null,
    academic_transcript: null as File | null,
    recommendation_letter: null as File | null,
    additional_documents: [] as File[]
  });

  useEffect(() => {
    fetchScholarships();
    fetchMyApplications();
  }, []);

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/scholarships');
      if (response.success) {
        setScholarships(response.data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch scholarships:', error);
      toast.error('Failed to load scholarships');
    } finally {
      setLoading(false);
    }
  };

  const fetchMyApplications = async () => {
    try {
      const response = await apiClient.get('/scholarships/my-applications');
      if (response.success) {
        setApplications(response.data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field: string, file: File | null) => {
    setFiles(prev => ({ ...prev, [field]: file }));
  };

  const handleSubmitApplication = async () => {
    if (!selectedScholarship) {
      toast.error('Please select a scholarship');
      return;
    }

    try {
      setSubmitting(true);

      // Prepare application data as JSON
      const applicationData = {
        application_data: {
          personal_statement: formData.personal_statement,
          academic_achievements: formData.academic_achievements,
          financial_need_statement: formData.financial_need_statement,
          career_goals: formData.career_goals,
          additional_info: formData.additional_info,
          student_id_number: formData.student_id_number,
          current_gpa: formData.current_gpa,
          year_of_study: formData.year_of_study,
          institution_name: formData.institution_name
        },
        documents: [] // For now, we'll handle file uploads separately
      };

      const response = await apiClient.post(`/scholarships/${selectedScholarship.id}/apply`, applicationData);

      if (response.success) {
        toast.success('Application submitted successfully!');
        setSelectedScholarship(null);
        setFormData({
          personal_statement: '',
          academic_achievements: '',
          financial_need_statement: '',
          career_goals: '',
          additional_info: '',
          student_id_number: '',
          current_gpa: '',
          year_of_study: '',
          institution_name: ''
        });
        setFiles({
          payment_receipt: null,
          school_id_card: null,
          academic_transcript: null,
          recommendation_letter: null,
          additional_documents: []
        });
        fetchMyApplications();
        onApplicationSubmitted?.();
      } else {
        toast.error(response.message || 'Failed to submit application');
      }
    } catch (error: any) {
      console.error('Failed to submit application:', error);
      toast.error(error.response?.data?.message || 'Failed to submit application');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredScholarships = scholarships.filter(scholarship =>
    scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    scholarship.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const hasApplied = (scholarshipId: number) => {
    return applications.some(app => app.scholarship.id === scholarshipId);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="available">Available Scholarships</TabsTrigger>
          <TabsTrigger value="applications">My Applications</TabsTrigger>
        </TabsList>

        <TabsContent value="available" className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardHeader>
              <CardTitle>Find Scholarships</CardTitle>
              <CardDescription>
                Search and apply for available scholarships
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search scholarships..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Scholarships List */}
          <div className="grid gap-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading scholarships...</p>
              </div>
            ) : filteredScholarships.length > 0 ? (
              filteredScholarships.map((scholarship) => (
                <Card key={scholarship.id} className="relative">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{scholarship.title}</CardTitle>
                        <CardDescription className="mt-2">
                          {scholarship.description}
                        </CardDescription>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-600">
                          ₦{scholarship.amount.toLocaleString()}
                        </div>
                        <p className="text-sm text-gray-500">
                          Deadline: {new Date(scholarship.application_deadline).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Eligibility Criteria:</h4>
                        <p className="text-sm text-gray-600">{scholarship.eligibility_criteria}</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Required Documents:</h4>
                        <p className="text-sm text-gray-600">{scholarship.required_documents}</p>
                      </div>
                      <div className="flex justify-between items-center">
                        {hasApplied(scholarship.id) ? (
                          <Badge className="bg-blue-100 text-blue-800">
                            Already Applied
                          </Badge>
                        ) : (
                          <Button 
                            onClick={() => setSelectedScholarship(scholarship)}
                            disabled={new Date(scholarship.application_deadline) < new Date()}
                          >
                            Apply Now
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No scholarships found</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="applications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Applications</CardTitle>
              <CardDescription>
                Track the status of your scholarship applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {applications.length > 0 ? (
                <div className="space-y-4">
                  {applications.map((application) => (
                    <div key={application.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h4 className="font-medium">{application.scholarship.title}</h4>
                          <p className="text-sm text-gray-600">
                            Amount: ₦{application.scholarship.amount.toLocaleString()}
                          </p>
                          <p className="text-xs text-gray-500">
                            Submitted: {new Date(application.submitted_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge className={getStatusColor(application.status)}>
                          {application.status}
                        </Badge>
                      </div>
                      {application.documents && (
                        <div className="mt-4">
                          <h5 className="font-medium mb-2">Uploaded Documents:</h5>
                          <div className="flex flex-wrap gap-2">
                            {Object.keys(application.documents).map((docType) => (
                              <Badge key={docType} variant="outline">
                                {docType.replace('_', ' ')}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No applications submitted yet</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Application Form Modal/Dialog would go here */}
      {selectedScholarship && (
        <Card className="fixed inset-0 z-50 bg-white m-4 overflow-y-auto">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>Apply for {selectedScholarship.title}</CardTitle>
                <CardDescription>
                  Complete the application form and upload required documents
                </CardDescription>
              </div>
              <Button 
                variant="outline" 
                onClick={() => setSelectedScholarship(null)}
              >
                Close
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Application form fields would go here */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="student_id">Student ID Number</Label>
                <Input
                  id="student_id"
                  value={formData.student_id_number}
                  onChange={(e) => handleInputChange('student_id_number', e.target.value)}
                  placeholder="Enter your student ID"
                />
              </div>
              <div>
                <Label htmlFor="gpa">Current GPA</Label>
                <Input
                  id="gpa"
                  type="number"
                  step="0.01"
                  value={formData.current_gpa}
                  onChange={(e) => handleInputChange('current_gpa', e.target.value)}
                  placeholder="e.g., 3.75"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="personal_statement">Personal Statement</Label>
              <Textarea
                id="personal_statement"
                value={formData.personal_statement}
                onChange={(e) => handleInputChange('personal_statement', e.target.value)}
                placeholder="Tell us about yourself and why you deserve this scholarship..."
                rows={4}
              />
            </div>

            {/* File upload sections would go here */}
            <div className="space-y-4">
              <h4 className="font-medium">Required Documents</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payment_receipt">Payment Receipt</Label>
                  <Input
                    id="payment_receipt"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileChange('payment_receipt', e.target.files?.[0] || null)}
                  />
                </div>
                <div>
                  <Label htmlFor="school_id">School ID Card</Label>
                  <Input
                    id="school_id"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileChange('school_id_card', e.target.files?.[0] || null)}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button 
                variant="outline" 
                onClick={() => setSelectedScholarship(null)}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmitApplication}
                disabled={submitting}
              >
                {submitting ? 'Submitting...' : 'Submit Application'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
