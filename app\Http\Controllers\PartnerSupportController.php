<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SupportTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PartnerSupportController extends Controller
{
    /**
     * Display the support dashboard.
     */
    public function index()
    {
        $partner = Auth::user();
        
        // Ensure user is a partner organization
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get partner's support tickets
        $tickets = SupportTicket::where('user_id', $partner->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get ticket statistics
        $ticketStats = [
            'total' => SupportTicket::where('user_id', $partner->id)->count(),
            'open' => SupportTicket::where('user_id', $partner->id)->where('status', 'open')->count(),
            'in_progress' => SupportTicket::where('user_id', $partner->id)->where('status', 'in_progress')->count(),
            'resolved' => SupportTicket::where('user_id', $partner->id)->where('status', 'resolved')->count(),
        ];

        // FAQ data
        $faqs = $this->getFaqData();

        return view('partner.support.index', compact('partner', 'tickets', 'ticketStats', 'faqs'));
    }

    /**
     * Show the form for creating a new support ticket.
     */
    public function create()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        return view('partner.support.create', compact('partner'));
    }

    /**
     * Store a newly created support ticket.
     */
    public function store(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $request->validate([
            'subject' => 'required|string|max:255',
            'category' => 'required|string|in:technical,billing,general,feature_request,bug_report',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'description' => 'required|string|min:10',
            'attachments.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB max
        ]);

        // Create support ticket
        $ticket = SupportTicket::create([
            'ticket_id' => 'TKT-' . strtoupper(Str::random(8)),
            'user_id' => $partner->id,
            'subject' => $request->subject,
            'category' => $request->category,
            'priority' => $request->priority,
            'description' => $request->description,
            'status' => 'open',
        ]);

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            $attachments = [];
            foreach ($request->file('attachments') as $file) {
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('support_tickets', $filename, 'public');
                $attachments[] = $path;
            }
            $ticket->update(['attachments' => json_encode($attachments)]);
        }

        return redirect()->route('partner.support.show', $ticket->id)
            ->with('success', 'Support ticket created successfully. We will respond within 24 hours.');
    }

    /**
     * Display the specified support ticket.
     */
    public function show($id)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $ticket = SupportTicket::where('user_id', $partner->id)->findOrFail($id);

        return view('partner.support.show', compact('partner', 'ticket'));
    }

    /**
     * Show help documentation.
     */
    public function help()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $helpSections = $this->getHelpDocumentation();

        return view('partner.support.help', compact('partner', 'helpSections'));
    }

    /**
     * Show contact information.
     */
    public function contact()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        return view('partner.support.contact', compact('partner'));
    }

    /**
     * Get FAQ data.
     */
    private function getFaqData()
    {
        return [
            [
                'category' => 'Getting Started',
                'questions' => [
                    [
                        'question' => 'How do I add students to my organization?',
                        'answer' => 'Navigate to the "Manage Students" section and click "Add New Student". Fill in the required information including student details, academic information, and contact details.'
                    ],
                    [
                        'question' => 'How do I apply for scholarships on behalf of my students?',
                        'answer' => 'Go to the "Applications" section, click "New Application", select the student and scholarship, then complete the application form with all required documents.'
                    ],
                    [
                        'question' => 'What documents are required for scholarship applications?',
                        'answer' => 'Required documents typically include academic transcripts, identification documents, proof of enrollment, and any category-specific requirements listed in the scholarship details.'
                    ]
                ]
            ],
            [
                'category' => 'Student Management',
                'questions' => [
                    [
                        'question' => 'Can I edit student information after adding them?',
                        'answer' => 'Yes, you can edit student information by going to "Manage Students", finding the student, and clicking the "Edit" button. Make sure to save your changes.'
                    ],
                    [
                        'question' => 'How do I track student progress?',
                        'answer' => 'Student progress can be tracked through the "Reports" section where you can view application success rates, academic performance, and other metrics.'
                    ],
                    [
                        'question' => 'What happens when a student graduates?',
                        'answer' => 'You can update the student\'s status to "Graduated" in their profile. This will maintain their records while indicating their current status.'
                    ]
                ]
            ],
            [
                'category' => 'Applications & Scholarships',
                'questions' => [
                    [
                        'question' => 'How long does the application review process take?',
                        'answer' => 'Application review typically takes 2-4 weeks depending on the scholarship type and completeness of the application. You will receive notifications about status updates.'
                    ],
                    [
                        'question' => 'Can I submit multiple applications for the same student?',
                        'answer' => 'Yes, students can apply for multiple scholarships as long as they meet the eligibility criteria for each scholarship program.'
                    ],
                    [
                        'question' => 'What should I do if an application is rejected?',
                        'answer' => 'Review the rejection reason provided, address any issues mentioned, and consider applying for other suitable scholarships. You can also contact support for guidance.'
                    ]
                ]
            ],
            [
                'category' => 'Technical Support',
                'questions' => [
                    [
                        'question' => 'I\'m having trouble uploading documents. What should I do?',
                        'answer' => 'Ensure your files are in supported formats (PDF, DOC, DOCX, JPG, PNG) and under 5MB. Clear your browser cache and try again. If issues persist, contact technical support.'
                    ],
                    [
                        'question' => 'How do I reset my password?',
                        'answer' => 'Click "Forgot Password" on the login page, enter your email address, and follow the instructions sent to your email to reset your password.'
                    ],
                    [
                        'question' => 'Can I access the system from mobile devices?',
                        'answer' => 'Yes, the platform is fully responsive and works on mobile devices and tablets. For the best experience, we recommend using the latest version of your mobile browser.'
                    ]
                ]
            ]
        ];
    }

    /**
     * Get help documentation sections.
     */
    private function getHelpDocumentation()
    {
        return [
            [
                'title' => 'Getting Started Guide',
                'icon' => 'play-circle',
                'description' => 'Learn the basics of using the partner dashboard',
                'sections' => [
                    'Setting up your organization profile',
                    'Adding your first students',
                    'Understanding the dashboard layout',
                    'Navigating between sections'
                ]
            ],
            [
                'title' => 'Student Management',
                'icon' => 'users',
                'description' => 'Complete guide to managing your students',
                'sections' => [
                    'Adding new students',
                    'Editing student information',
                    'Managing student documents',
                    'Tracking student progress',
                    'Student status management'
                ]
            ],
            [
                'title' => 'Scholarship Applications',
                'icon' => 'graduation-cap',
                'description' => 'How to apply for scholarships effectively',
                'sections' => [
                    'Understanding scholarship categories',
                    'Application requirements',
                    'Document preparation',
                    'Submission process',
                    'Tracking application status'
                ]
            ],
            [
                'title' => 'Reports & Analytics',
                'icon' => 'chart-bar',
                'description' => 'Making the most of your data',
                'sections' => [
                    'Understanding dashboard metrics',
                    'Generating reports',
                    'Exporting data',
                    'Performance analysis',
                    'Success rate optimization'
                ]
            ],
            [
                'title' => 'Account Management',
                'icon' => 'cog',
                'description' => 'Managing your organization account',
                'sections' => [
                    'Profile settings',
                    'Password management',
                    'Notification preferences',
                    'Security settings',
                    'Account verification'
                ]
            ],
            [
                'title' => 'Troubleshooting',
                'icon' => 'exclamation-triangle',
                'description' => 'Common issues and solutions',
                'sections' => [
                    'Login problems',
                    'File upload issues',
                    'Browser compatibility',
                    'Performance optimization',
                    'Error message explanations'
                ]
            ]
        ];
    }
}
