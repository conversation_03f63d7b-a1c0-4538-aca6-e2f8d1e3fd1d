<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Partner;
use App\Models\PartnershipProposal;
use App\Models\SharedResource;
use App\Models\CollaborativeProgram;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

/**
 * @OA\Tag(
 *     name="Partners",
 *     description="API Endpoints for Partner Organizations"
 * )
 */
class PartnerController extends Controller
{
    /**
     * Register as a partner organization
     */
    public function register(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'organization_name' => 'required|string|max:255',
                'organization_type' => 'required|in:school,ngo,government,corporate,international',
                'registration_number' => 'nullable|string|max:100',
                'contact_person' => 'required|string|max:255',
                'contact_email' => 'required|email|max:255',
                'contact_phone' => 'required|string|max:20',
                'address' => 'required|string',
                'city' => 'required|string|max:100',
                'state' => 'required|string|max:100',
                'country' => 'required|string|max:100',
                'website' => 'nullable|url',
                'description' => 'required|string|max:2000',
                'areas_of_focus' => 'required|array',
                'partnership_interests' => 'required|array',
                'resources_to_share' => 'nullable|array',
                'expected_collaboration' => 'required|string|max:1000',
                'verification_documents.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle document uploads
            $uploadedDocs = [];
            if ($request->hasFile('verification_documents')) {
                foreach ($request->file('verification_documents') as $index => $file) {
                    $filename = time() . '_verification_' . $index . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('partner_documents/' . Auth::id(), $filename, 'public');
                    $uploadedDocs[] = $path;
                }
            }

            $partnerData = $validator->validated();
            $partnerData['user_id'] = Auth::id();
            $partnerData['verification_documents'] = $uploadedDocs;
            $partnerData['status'] = 'pending';
            $partnerData['applied_at'] = now();

            $partner = Partner::create($partnerData);

            return response()->json([
                'success' => true,
                'message' => 'Partner registration submitted successfully',
                'data' => $partner
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to register partner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get partner profile
     */
    public function getProfile(): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->first();

            if (!$partner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Partner profile not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $partner
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve partner profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit partnership proposal
     */
    public function submitProposal(Request $request): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->firstOrFail();

            if ($partner->status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Partner must be approved before submitting proposals'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:2000',
                'collaboration_type' => 'required|in:joint_program,resource_sharing,event_hosting,research,funding,training',
                'proposed_duration' => 'required|string|max:100',
                'expected_outcomes' => 'required|string|max:1000',
                'resources_required' => 'nullable|array',
                'budget_estimate' => 'nullable|numeric|min:0',
                'timeline' => 'nullable|string|max:500',
                'target_beneficiaries' => 'required|string|max:500',
                'proposal_documents.*' => 'nullable|file|mimes:pdf,doc,docx|max:10240'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle document uploads
            $uploadedDocs = [];
            if ($request->hasFile('proposal_documents')) {
                foreach ($request->file('proposal_documents') as $index => $file) {
                    $filename = time() . '_proposal_' . $index . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('proposal_documents/' . $partner->id, $filename, 'public');
                    $uploadedDocs[] = $path;
                }
            }

            $proposalData = $validator->validated();
            $proposalData['partner_id'] = $partner->id;
            $proposalData['proposal_documents'] = $uploadedDocs;
            $proposalData['status'] = 'submitted';
            $proposalData['submitted_at'] = now();

            $proposal = PartnershipProposal::create($proposalData);

            return response()->json([
                'success' => true,
                'message' => 'Partnership proposal submitted successfully',
                'data' => $proposal
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit proposal',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get partner's proposals
     */
    public function getProposals(Request $request): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->firstOrFail();
            $perPage = $request->get('per_page', 15);

            $proposals = PartnershipProposal::where('partner_id', $partner->id)
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $proposals
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve proposals',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Share a resource
     */
    public function shareResource(Request $request): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->firstOrFail();

            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:1000',
                'resource_type' => 'required|in:document,video,tool,template,curriculum,training_material',
                'category' => 'required|string|max:100',
                'access_level' => 'required|in:public,partners_only,restricted',
                'file' => 'nullable|file|mimes:pdf,doc,docx,ppt,pptx,mp4,avi,mov|max:51200',
                'external_url' => 'nullable|url',
                'tags' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $resourceData = $validator->validated();
            $resourceData['partner_id'] = $partner->id;
            $resourceData['shared_by'] = Auth::id();
            $resourceData['status'] = 'active';

            // Handle file upload
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('shared_resources/' . $partner->id, $filename, 'public');
                $resourceData['file_path'] = $path;
            }

            $resource = SharedResource::create($resourceData);

            return response()->json([
                'success' => true,
                'message' => 'Resource shared successfully',
                'data' => $resource
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to share resource',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get shared resources
     */
    public function getSharedResources(Request $request): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->first();
            $perPage = $request->get('per_page', 15);

            $query = SharedResource::with(['partner', 'sharedBy'])
                ->where('status', 'active');

            // If user is a partner, show partner-only resources
            if ($partner && $partner->status === 'approved') {
                $query->whereIn('access_level', ['public', 'partners_only']);
            } else {
                $query->where('access_level', 'public');
            }

            // Filter by category
            if ($request->has('category')) {
                $query->where('category', $request->category);
            }

            // Filter by resource type
            if ($request->has('resource_type')) {
                $query->where('resource_type', $request->resource_type);
            }

            // Search functionality
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            $resources = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $resources
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve shared resources',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get partner analytics
     */
    public function getAnalytics(): JsonResponse
    {
        try {
            $partner = Partner::where('user_id', Auth::id())->firstOrFail();

            $analytics = [
                'total_proposals' => PartnershipProposal::where('partner_id', $partner->id)->count(),
                'approved_proposals' => PartnershipProposal::where('partner_id', $partner->id)
                    ->where('status', 'approved')->count(),
                'pending_proposals' => PartnershipProposal::where('partner_id', $partner->id)
                    ->where('status', 'submitted')->count(),
                'shared_resources' => SharedResource::where('partner_id', $partner->id)->count(),
                'active_collaborations' => CollaborativeProgram::where('partner_id', $partner->id)
                    ->where('status', 'active')->count(),
                'total_impact_reach' => 0, // This would be calculated based on program participants
                'partnership_duration' => $partner->approved_at ? 
                    $partner->approved_at->diffInDays(now()) : 0
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
