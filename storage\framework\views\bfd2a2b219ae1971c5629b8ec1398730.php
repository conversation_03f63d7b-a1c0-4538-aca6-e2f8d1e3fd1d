

<?php $__env->startSection('title', 'Newsletter Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Newsletter Management</h1>
        <div>
            <a href="<?php echo e(route('admin.newsletter.campaigns.create')); ?>" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i>Create Campaign
            </a>
            <a href="<?php echo e(route('admin.newsletter.subscribers.import')); ?>" class="btn btn-success">
                <i class="fas fa-upload me-2"></i>Import Subscribers
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Subscribers</div>
                            <div class="h4"><?php echo e($subscribers->total()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Subscribers</div>
                            <div class="h4"><?php echo e($subscribers->where('status', 'subscribed')->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Campaigns Sent</div>
                            <div class="h4">24</div>
                        </div>
                        <div>
                            <i class="fas fa-paper-plane fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Open Rate</div>
                            <div class="h4">68.5%</div>
                        </div>
                        <div>
                            <i class="fas fa-envelope-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Newsletter Tabs -->
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="newsletterTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#subscribers">
                        <i class="fas fa-users me-2"></i>Subscribers (<?php echo e($subscribers->total()); ?>)
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#campaigns">
                        <i class="fas fa-paper-plane me-2"></i>Campaigns
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#analytics">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <!-- Subscribers Tab -->
                <div class="tab-pane fade show active" id="subscribers">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search subscribers..." id="searchSubscribers">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatus">
                                <option value="">All Status</option>
                                <option value="subscribed">Subscribed</option>
                                <option value="unsubscribed">Unsubscribed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    Bulk Actions
                                </button>
                                <ul class="dropdown-menu w-100">
                                    <li><a class="dropdown-item" href="#" onclick="bulkAction('unsubscribe')">Unsubscribe Selected</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkAction('delete')">Delete Selected</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportSelected()">Export Selected</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Source</th>
                                    <th>Subscribed Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $subscribers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscriber): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><input type="checkbox" class="subscriber-checkbox" value="<?php echo e($subscriber->id); ?>"></td>
                                    <td>
                                        <div>
                                            <div class="fw-bold"><?php echo e($subscriber->email); ?></div>
                                            <?php if($subscriber->preferences): ?>
                                                <small class="text-muted">
                                                    Preferences: <?php echo e(is_array($subscriber->preferences) ? implode(', ', $subscriber->preferences) : $subscriber->preferences); ?>

                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($subscriber->status === 'subscribed' ? 'success' : 'secondary'); ?>">
                                            <?php echo e(ucfirst($subscriber->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php echo e($subscriber->subscription_source ? ucfirst(str_replace('_', ' ', $subscriber->subscription_source)) : 'Direct'); ?>

                                    </td>
                                    <td>
                                        <?php echo e($subscriber->subscribed_at ? $subscriber->subscribed_at->format('M d, Y') : 'N/A'); ?>

                                        <?php if($subscriber->subscribed_at): ?>
                                            <br><small class="text-muted"><?php echo e($subscriber->subscribed_at->diffForHumans()); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if($subscriber->status === 'subscribed'): ?>
                                                <button class="btn btn-sm btn-outline-warning" onclick="unsubscribeUser(<?php echo e($subscriber->id); ?>)" title="Unsubscribe">
                                                    <i class="fas fa-user-minus"></i>
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-outline-success" onclick="resubscribeUser(<?php echo e($subscriber->id); ?>)" title="Resubscribe">
                                                    <i class="fas fa-user-plus"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteSubscriber(<?php echo e($subscriber->id); ?>)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No subscribers found</p>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            Showing <?php echo e($subscribers->firstItem()); ?> to <?php echo e($subscribers->lastItem()); ?> of <?php echo e($subscribers->total()); ?> results
                        </div>
                        <?php echo e($subscribers->links()); ?>

                    </div>
                </div>

                <!-- Campaigns Tab -->
                <div class="tab-pane fade" id="campaigns">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Recent Campaigns</h5>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Monthly Impact Report - December 2024</h6>
                                        <small class="text-success">Sent</small>
                                    </div>
                                    <p class="mb-1">Share our December impact statistics and upcoming events</p>
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">Sent to 1,234 subscribers • Open rate: 72%</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary me-1">View</button>
                                            <button class="btn btn-sm btn-outline-secondary">Duplicate</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Volunteer Appreciation Newsletter</h6>
                                        <small class="text-warning">Draft</small>
                                    </div>
                                    <p class="mb-1">Recognize our amazing volunteers and their contributions</p>
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">Draft • Created 2 days ago</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary me-1">Edit</button>
                                            <button class="btn btn-sm btn-outline-success">Send</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">New Education Program Launch</h6>
                                        <small class="text-success">Sent</small>
                                    </div>
                                    <p class="mb-1">Announcing our new literacy program for underserved communities</p>
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">Sent to 1,156 subscribers • Open rate: 68%</small>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary me-1">View</button>
                                            <button class="btn btn-sm btn-outline-secondary">Duplicate</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>Campaign Stats</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div class="text-center">
                                        <h3 class="text-primary">24</h3>
                                        <p class="text-muted">Total Campaigns</p>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h5 class="text-success">68.5%</h5>
                                            <small class="text-muted">Avg Open Rate</small>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-info">12.3%</h5>
                                            <small class="text-muted">Avg Click Rate</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div class="tab-pane fade" id="analytics">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Subscriber Growth</h5>
                            <div class="card">
                                <div class="card-body">
                                    <canvas id="subscriberGrowthChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Campaign Performance</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Average Open Rate</span>
                                            <span class="fw-bold">68.5%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: 68.5%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Average Click Rate</span>
                                            <span class="fw-bold">12.3%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-info" style="width: 12.3%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Unsubscribe Rate</span>
                                            <span class="fw-bold">2.1%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" style="width: 2.1%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Action Confirmation Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bulkActionMessage">
                <!-- Message will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkAction">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.subscriber-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function bulkAction(action) {
    const selectedIds = [];
    document.querySelectorAll('.subscriber-checkbox:checked').forEach(checkbox => {
        selectedIds.push(checkbox.value);
    });
    
    if (selectedIds.length === 0) {
        alert('Please select at least one subscriber');
        return;
    }
    
    const actionText = action === 'unsubscribe' ? 'unsubscribe' : 'delete';
    document.getElementById('bulkActionMessage').innerHTML = 
        `Are you sure you want to ${actionText} ${selectedIds.length} selected subscriber(s)?`;
    
    const modal = new bootstrap.Modal(document.getElementById('bulkActionModal'));
    modal.show();
    
    document.getElementById('confirmBulkAction').onclick = function() {
        console.log(`Bulk ${action}:`, selectedIds);
        modal.hide();
        // Add AJAX call for bulk action
    };
}

function exportSelected() {
    const selectedIds = [];
    document.querySelectorAll('.subscriber-checkbox:checked').forEach(checkbox => {
        selectedIds.push(checkbox.value);
    });
    
    if (selectedIds.length === 0) {
        alert('Please select at least one subscriber');
        return;
    }
    
    console.log('Export selected:', selectedIds);
    // Add export functionality
}

function unsubscribeUser(subscriberId) {
    console.log('Unsubscribe user:', subscriberId);
    // Add AJAX call to unsubscribe user
}

function resubscribeUser(subscriberId) {
    console.log('Resubscribe user:', subscriberId);
    // Add AJAX call to resubscribe user
}

function deleteSubscriber(subscriberId) {
    if (confirm('Are you sure you want to delete this subscriber?')) {
        console.log('Delete subscriber:', subscriberId);
        // Add AJAX call to delete subscriber
    }
}

// Search functionality
document.getElementById('searchSubscribers').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

// Chart initialization (requires Chart.js)
document.addEventListener('DOMContentLoaded', function() {
    // Initialize subscriber growth chart
    const ctx = document.getElementById('subscriberGrowthChart');
    if (ctx) {
        // Chart implementation would go here
        // new Chart(ctx, { ... });
    }
});
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/newsletter/index.blade.php ENDPATH**/ ?>