<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\ScholarshipApplication;
use App\Models\Scholarship;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Partner Dashboard",
 *     description="API Endpoints for Partner Organization Dashboard"
 * )
 */
class PartnerDashboardController extends Controller
{
    // Middleware is applied at route level in Laravel 11

    /**
     * Get comprehensive dashboard data for Partner Organization
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // Handle admin access - they can view any partner organization or get aggregated data
            if ($user->role === 'admin') {
                // If partner_id is provided in request, show specific partner data
                $partnerId = $request->get('partner_id');
                if ($partnerId) {
                    $partnerOrg = PartnerOrganization::findOrFail($partnerId);
                } else {
                    // For admin without specific partner, return aggregated data from first partner or create mock data
                    $partnerOrg = PartnerOrganization::first();
                    if (!$partnerOrg) {
                        // Return empty dashboard data for admin when no partners exist
                        return response()->json([
                            'success' => true,
                            'data' => [
                                'overview' => [
                                    'total_students' => 0,
                                    'total_applications' => 0,
                                    'approved_applications' => 0,
                                    'pending_applications' => 0,
                                    'active_progressions' => 0,
                                    'success_rate' => 0
                                ],
                                'recent_activities' => [],
                                'upcoming_deadlines' => [],
                                'progression_alerts' => [],
                                'quick_stats' => [
                                    'applications_this_month' => 0,
                                    'application_change' => 0,
                                    'active_scholarships' => 0,
                                    'pending_reviews' => 0
                                ],
                                'organization' => null
                            ]
                        ]);
                    }
                }
            } else {
                // Regular partner organization access
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
            }

            // Get overview statistics
            $overview = $this->getOverviewStatistics($partnerOrg);

            // Get recent activities
            $recentActivities = $this->getRecentActivities($partnerOrg);

            // Get upcoming deadlines
            $upcomingDeadlines = $this->getUpcomingDeadlines($partnerOrg);

            // Get student progression alerts
            $progressionAlerts = $this->getProgressionAlerts($partnerOrg);

            // Get quick stats
            $quickStats = $this->getQuickStats($partnerOrg);

            return response()->json([
                'success' => true,
                'data' => [
                    'overview' => $overview,
                    'recent_activities' => $recentActivities,
                    'upcoming_deadlines' => $upcomingDeadlines,
                    'progression_alerts' => $progressionAlerts,
                    'quick_stats' => $quickStats,
                    'organization' => $partnerOrg
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load dashboard data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStatistics(PartnerOrganization $partnerOrg): array
    {
        $totalStudents = Student::where('school_id', $partnerOrg->id)->count();
        
        $totalApplications = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->count();

        $approvedApplications = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->where('status', 'approved')->count();

        $pendingApplications = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->where('status', 'pending')->count();

        $activeProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->where('status', 'approved')->count();

        $successRate = $totalApplications > 0 ? round(($approvedApplications / $totalApplications) * 100, 1) : 0;

        return [
            'total_students' => $totalStudents,
            'total_applications' => $totalApplications,
            'approved_applications' => $approvedApplications,
            'pending_applications' => $pendingApplications,
            'active_progressions' => $activeProgressions,
            'success_rate' => $successRate
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities(PartnerOrganization $partnerOrg): array
    {
        $activities = [];

        // Recent applications
        $recentApplications = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })
        ->with(['student', 'scholarship'])
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();

        foreach ($recentApplications as $application) {
            $activities[] = [
                'type' => 'application',
                'title' => 'New scholarship application',
                'description' => "{$application->student->first_name} {$application->student->last_name} applied for {$application->scholarship->title}",
                'timestamp' => $application->created_at,
                'status' => $application->status,
                'data' => [
                    'student_id' => $application->student->id,
                    'scholarship_id' => $application->scholarship->id,
                    'application_id' => $application->id
                ]
            ];
        }

        // Recent progressions
        $recentProgressions = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })
        ->with(['student'])
        ->orderBy('created_at', 'desc')
        ->limit(3)
        ->get();

        foreach ($recentProgressions as $progression) {
            $activities[] = [
                'type' => 'progression',
                'title' => 'Student progression update',
                'description' => "{$progression->student->first_name} {$progression->student->last_name} progressed to Grade {$progression->grade_level}",
                'timestamp' => $progression->created_at,
                'status' => $progression->status,
                'data' => [
                    'student_id' => $progression->student->id,
                    'progression_id' => $progression->id
                ]
            ];
        }

        // Sort activities by timestamp
        usort($activities, function ($a, $b) {
            return $b['timestamp'] <=> $a['timestamp'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Get upcoming deadlines
     */
    private function getUpcomingDeadlines(PartnerOrganization $partnerOrg): array
    {
        $deadlines = [];

        // Scholarship application deadlines
        $upcomingScholarships = Scholarship::where('status', 'active')
            ->where('is_open', true)
            ->whereIn('category', ['primary', 'secondary'])
            ->where('application_deadline', '>', now())
            ->where('application_deadline', '<=', now()->addDays(30))
            ->orderBy('application_deadline', 'asc')
            ->get();

        foreach ($upcomingScholarships as $scholarship) {
            $deadlines[] = [
                'type' => 'scholarship_deadline',
                'title' => $scholarship->title,
                'description' => 'Application deadline approaching',
                'deadline' => $scholarship->application_deadline,
                'days_remaining' => now()->diffInDays($scholarship->application_deadline),
                'category' => $scholarship->category,
                'data' => [
                    'scholarship_id' => $scholarship->id
                ]
            ];
        }

        // Progression deadlines (if any)
        $progressionDeadlines = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })
        ->where('status', 'pending')
        ->where('created_at', '<=', now()->subDays(7)) // Pending for more than 7 days
        ->with(['student'])
        ->get();

        foreach ($progressionDeadlines as $progression) {
            $deadlines[] = [
                'type' => 'progression_pending',
                'title' => 'Pending progression review',
                'description' => "{$progression->student->first_name} {$progression->student->last_name} - Grade {$progression->grade_level}",
                'deadline' => $progression->created_at->addDays(14), // 14 days to review
                'days_remaining' => $progression->created_at->addDays(14)->diffInDays(now()),
                'category' => 'progression',
                'data' => [
                    'student_id' => $progression->student->id,
                    'progression_id' => $progression->id
                ]
            ];
        }

        return $deadlines;
    }

    /**
     * Get progression alerts
     */
    private function getProgressionAlerts(PartnerOrganization $partnerOrg): array
    {
        $alerts = [];

        // Students eligible for progression
        $eligibleStudents = Student::where('school_id', $partnerOrg->id)
            ->whereHas('progressions', function ($query) {
                $query->where('status', 'completed')
                      ->where('to_academic_year', now()->year - 1 . '/' . now()->year); // Last academic year
            })
            ->whereDoesntHave('progressions', function ($query) {
                $query->where('status', 'approved')
                      ->where('to_academic_year', now()->year . '/' . (now()->year + 1)); // Current academic year
            })
            ->with(['progressions' => function ($query) {
                $query->where('status', 'completed')->orderBy('to_academic_year', 'desc');
            }])
            ->get();

        foreach ($eligibleStudents as $student) {
            $currentProgression = $student->progressions->first();
            $alerts[] = [
                'type' => 'progression_eligible',
                'title' => 'Student eligible for progression',
                'description' => "{$student->first_name} {$student->last_name} completed Grade {$currentProgression->grade_level}",
                'priority' => 'medium',
                'data' => [
                    'student_id' => $student->id,
                    'current_grade' => $currentProgression->grade_level,
                    'next_grade' => $currentProgression->grade_level + 1
                ]
            ];
        }

        return $alerts;
    }

    /**
     * Get quick stats for dashboard cards
     */
    private function getQuickStats(PartnerOrganization $partnerOrg): array
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        // Applications this month vs last month
        $applicationsThisMonth = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->where('created_at', '>=', $currentMonth)->count();

        $applicationsLastMonth = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->whereBetween('created_at', [$lastMonth, $currentMonth])->count();

        $applicationChange = $applicationsLastMonth > 0 
            ? round((($applicationsThisMonth - $applicationsLastMonth) / $applicationsLastMonth) * 100, 1)
            : ($applicationsThisMonth > 0 ? 100 : 0);

        // Active scholarships
        $activeScholarships = Scholarship::where('status', 'active')
            ->where('is_open', true)
            ->whereIn('category', ['primary', 'secondary'])
            ->where('application_deadline', '>', now())
            ->count();

        return [
            'applications_this_month' => $applicationsThisMonth,
            'application_change' => $applicationChange,
            'active_scholarships' => $activeScholarships,
            'pending_reviews' => ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })->where('status', 'pending')->count()
        ];
    }
}
