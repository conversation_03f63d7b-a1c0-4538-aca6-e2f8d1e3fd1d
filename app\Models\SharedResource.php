<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SharedResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'partner_id',
        'shared_by',
        'title',
        'description',
        'resource_type',
        'category',
        'file_path',
        'external_url',
        'access_level',
        'tags',
        'status',
        'download_count',
        'view_count'
    ];

    protected $casts = [
        'tags' => 'array',
        'download_count' => 'integer',
        'view_count' => 'integer'
    ];

    /**
     * Get the partner that owns this resource
     */
    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    /**
     * Get the user who shared this resource
     */
    public function sharedBy()
    {
        return $this->belongsTo(User::class, 'shared_by');
    }

    /**
     * Scope for active resources
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for public resources
     */
    public function scopePublic($query)
    {
        return $query->where('access_level', 'public');
    }

    /**
     * Scope for partner-only resources
     */
    public function scopePartnersOnly($query)
    {
        return $query->where('access_level', 'partners_only');
    }

    /**
     * Scope for specific resource type
     */
    public function scopeType($query, $type)
    {
        return $query->where('resource_type', $type);
    }

    /**
     * Scope for specific category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        if ($this->file_path) {
            return asset('storage/' . $this->file_path);
        }
        return $this->external_url;
    }

    /**
     * Get resource type label
     */
    public function getResourceTypeLabelAttribute()
    {
        $types = [
            'document' => 'Document',
            'video' => 'Video',
            'tool' => 'Tool',
            'template' => 'Template',
            'curriculum' => 'Curriculum',
            'training_material' => 'Training Material'
        ];

        return $types[$this->resource_type] ?? $this->resource_type;
    }

    /**
     * Get access level label
     */
    public function getAccessLevelLabelAttribute()
    {
        $levels = [
            'public' => 'Public',
            'partners_only' => 'Partners Only',
            'restricted' => 'Restricted'
        ];

        return $levels[$this->access_level] ?? $this->access_level;
    }

    /**
     * Get access level color
     */
    public function getAccessLevelColorAttribute()
    {
        switch ($this->access_level) {
            case 'public':
                return 'green';
            case 'partners_only':
                return 'blue';
            case 'restricted':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Increment view count
     */
    public function incrementViews()
    {
        $this->increment('view_count');
    }

    /**
     * Increment download count
     */
    public function incrementDownloads()
    {
        $this->increment('download_count');
    }

    /**
     * Check if resource has file
     */
    public function hasFile()
    {
        return !empty($this->file_path);
    }

    /**
     * Check if resource has external URL
     */
    public function hasExternalUrl()
    {
        return !empty($this->external_url);
    }

    /**
     * Check if user can access this resource
     */
    public function canAccess($user = null)
    {
        if ($this->access_level === 'public') {
            return true;
        }

        if (!$user) {
            return false;
        }

        if ($this->access_level === 'partners_only') {
            $partner = Partner::where('user_id', $user->id)
                ->where('status', 'approved')
                ->first();
            return $partner !== null;
        }

        if ($this->access_level === 'restricted') {
            // Only the sharing partner can access
            return $this->shared_by === $user->id;
        }

        return false;
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSizeAttribute()
    {
        if (!$this->file_path || !file_exists(storage_path('app/public/' . $this->file_path))) {
            return null;
        }

        $bytes = filesize(storage_path('app/public/' . $this->file_path));
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
