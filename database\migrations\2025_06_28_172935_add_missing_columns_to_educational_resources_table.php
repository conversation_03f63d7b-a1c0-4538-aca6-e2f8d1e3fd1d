<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('educational_resources', function (Blueprint $table) {
            // Add missing columns for better resource management
            $table->string('featured_image')->nullable()->after('video_url');
            $table->string('content_url')->nullable()->after('featured_image'); // For external links
            $table->enum('target_audience', ['primary', 'secondary', 'university', 'all'])->default('all')->after('difficulty_level');
            $table->text('learning_objectives')->nullable()->after('target_audience');
            $table->text('prerequisites')->nullable()->after('learning_objectives');

            // Update type enum to include more options
            $table->dropColumn('type');
        });

        // Re-add type column with updated enum values
        Schema::table('educational_resources', function (Blueprint $table) {
            $table->enum('type', ['study_guide', 'video_course', 'document', 'external_link', 'interactive'])->default('document')->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('educational_resources', function (Blueprint $table) {
            $table->dropColumn([
                'featured_image',
                'content_url',
                'target_audience',
                'learning_objectives',
                'prerequisites'
            ]);

            // Restore original type enum
            $table->dropColumn('type');
        });

        Schema::table('educational_resources', function (Blueprint $table) {
            $table->enum('type', ['video', 'pdf', 'document', 'interactive'])->after('description');
        });
    }
};
