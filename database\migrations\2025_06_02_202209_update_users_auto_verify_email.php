<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Modify email_verified_at to have a default value of current timestamp
            $table->timestamp('email_verified_at')->default(now())->change();
        });
        
        // Also verify any existing users who aren't verified
        \App\Models\User::whereNull('email_verified_at')->update([
            'email_verified_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Revert email_verified_at to nullable without default
            $table->timestamp('email_verified_at')->nullable()->default(null)->change();
        });
    }
};
