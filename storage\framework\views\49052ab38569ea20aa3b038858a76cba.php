<?php $__env->startSection('title', 'Edit Program'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Program</h1>
        <div>
            <a href="<?php echo e(route('admin.programs.show', $program->id)); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="<?php echo e(route('admin.programs.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Programs
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Information</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.programs.update', $program->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Program Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="<?php echo e(old('title', $program->title)); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Program Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required placeholder="Detailed description of the program, its purpose, and expected outcomes"><?php echo e(old('description', $program->description)); ?></textarea>
                        </div>

                        <!-- Program Category and Status -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Program Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="education" <?php echo e(old('category', $program->category) == 'education' ? 'selected' : ''); ?>>Education</option>
                                        <option value="healthcare" <?php echo e(old('category', $program->category) == 'healthcare' ? 'selected' : ''); ?>>Healthcare</option>
                                        <option value="environment" <?php echo e(old('category', $program->category) == 'environment' ? 'selected' : ''); ?>>Environment</option>
                                        <option value="poverty_alleviation" <?php echo e(old('category', $program->category) == 'poverty_alleviation' ? 'selected' : ''); ?>>Poverty Alleviation</option>
                                        <option value="youth_development" <?php echo e(old('category', $program->category) == 'youth_development' ? 'selected' : ''); ?>>Youth Development</option>
                                        <option value="women_empowerment" <?php echo e(old('category', $program->category) == 'women_empowerment' ? 'selected' : ''); ?>>Women Empowerment</option>
                                        <option value="community_development" <?php echo e(old('category', $program->category) == 'community_development' ? 'selected' : ''); ?>>Community Development</option>
                                        <option value="emergency_relief" <?php echo e(old('category', $program->category) == 'emergency_relief' ? 'selected' : ''); ?>>Emergency Relief</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="planning" <?php echo e(old('status', $program->status) == 'planning' ? 'selected' : ''); ?>>Planning</option>
                                        <option value="active" <?php echo e(old('status', $program->status) == 'active' ? 'selected' : ''); ?>>Active</option>
                                        <option value="completed" <?php echo e(old('status', $program->status) == 'completed' ? 'selected' : ''); ?>>Completed</option>
                                        <option value="suspended" <?php echo e(old('status', $program->status) == 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Objectives and Target Beneficiaries -->
                        <div class="mb-3">
                            <label for="objectives" class="form-label">Program Objectives</label>
                            <textarea class="form-control" id="objectives" name="objectives" rows="4" placeholder="List the main objectives of this program (one per line)"><?php echo e(old('objectives', is_array($program->objectives) ? implode("\n", $program->objectives) : $program->objectives)); ?></textarea>
                            <div class="form-text">Enter each objective on a new line</div>
                        </div>

                        <div class="mb-3">
                            <label for="target_beneficiaries" class="form-label">Target Beneficiaries</label>
                            <textarea class="form-control" id="target_beneficiaries" name="target_beneficiaries" rows="3" placeholder="Describe who will benefit from this program"><?php echo e(old('target_beneficiaries', $program->target_beneficiaries)); ?></textarea>
                        </div>

                        <!-- Timeline and Budget -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e(old('start_date', $program->start_date ? $program->start_date->format('Y-m-d') : '')); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e(old('end_date', $program->end_date ? $program->end_date->format('Y-m-d') : '')); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="budget" class="form-label">Budget (₦)</label>
                                    <input type="number" class="form-control" id="budget" name="budget" min="0" step="0.01" value="<?php echo e(old('budget', $program->budget)); ?>" placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="mb-3">
                            <label for="location_address" class="form-label">Location/Address</label>
                            <input type="text" class="form-control" id="location_address" name="location_address" value="<?php echo e(old('location_address', is_array($program->location) ? ($program->location['address'] ?? '') : $program->location)); ?>" placeholder="Program location or address">
                        </div>

                        <!-- Featured Image -->
                        <div class="mb-3">
                            <label for="featured_image" class="form-label">Featured Image</label>
                            <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                            <div class="form-text">Upload a new image to replace the current one. Leave empty to keep current image.</div>
                            <?php if($program->featured_image): ?>
                                <div class="mt-2">
                                    <small class="text-muted">Current image: <?php echo e(basename($program->featured_image)); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Program Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <span class="badge bg-<?php echo e($program->status === 'active' ? 'success' : ($program->status === 'completed' ? 'info' : ($program->status === 'suspended' ? 'danger' : 'warning'))); ?> p-3 mb-3" style="font-size: 1rem;">
                            <?php echo e(ucfirst($program->status)); ?>

                        </span>
                        <p class="text-muted mb-0">Current program status</p>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Program Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0"><?php echo e($program->created_at->format('F j, Y')); ?></p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0"><?php echo e($program->updated_at->format('F j, Y g:i A')); ?></p>
                    </div>
                    <?php if($program->creator): ?>
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0"><?php echo e($program->creator->first_name); ?> <?php echo e($program->creator->last_name); ?></p>
                    </div>
                    <?php endif; ?>
                    <?php if($program->budget): ?>
                    <div class="mb-3">
                        <small class="text-muted">Budget</small>
                        <p class="mb-0">₦<?php echo e(number_format($program->budget)); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/programs/edit.blade.php ENDPATH**/ ?>