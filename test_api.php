<?php

// Test API endpoints
$baseUrl = 'http://localhost:8000/api/v1';

// Test login
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Login Test:\n";
echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if (isset($data['data']['access_token'])) {
        $token = $data['data']['access_token'];
        echo "Token received successfully!\n";
        
        // Test admin dashboard endpoint
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/admin/dashboard');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $dashboardResponse = curl_exec($ch);
        $dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "Dashboard API Test:\n";
        echo "HTTP Code: $dashboardHttpCode\n";
        if ($dashboardHttpCode === 200) {
            echo "✅ Dashboard API working!\n";
            echo "Response: " . substr($dashboardResponse, 0, 200) . "...\n";
        } else {
            echo "❌ Dashboard API failed\n";
            echo "Response: " . substr($dashboardResponse, 0, 500) . "...\n";
        }
    }
}
