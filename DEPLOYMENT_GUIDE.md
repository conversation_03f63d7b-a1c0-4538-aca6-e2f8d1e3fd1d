# Hybrid Deployment Guide
## Next.js Frontend + Laravel Backend Architecture

This guide explains how to deploy the NGO platform using a hybrid architecture where:
- **Next.js Frontend**: Handles all public pages (main domain)
- **<PERSON>vel Backend**: Handles authentication, dashboards, and APIs (subdomain)

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION SETUP                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📱 Next.js Frontend                                        │
│  Domain: https://www.mydomain.com                           │
│  Handles: /about, /blog, /scholarships, /contact, etc.     │
│                                                             │
│  🔗 API Calls to <PERSON>vel Backend                            │
│                                                             │
│  🚀 Laravel Backend                                         │
│  Domain: https://api.mydomain.com                           │
│  Handles: /login, /register, /dashboard/*, /api/*          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## � Environment Configuration

### Laravel Backend (.env)
```env
# Basic Configuration
APP_NAME="HALIMAKQ Foundation"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.mydomain.com

# Hybrid Deployment Configuration
FRONTEND_URL=https://www.mydomain.com
BACKEND_URL=https://api.mydomain.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### Next.js Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=https://api.mydomain.com/api/v1
NEXT_PUBLIC_BACKEND_URL=https://api.mydomain.com
```

## �🚀 Deployment Steps

### 1. Laravel Backend Deployment (api.mydomain.com)

1. **Upload Laravel files** to your subdomain directory
2. **Configure environment**:
   ```bash
   cp .env.production.example .env
   # Edit .env with your production values (see configuration above)
   ```
3. **Install dependencies**:
   ```bash
   composer install --optimize-autoloader --no-dev
   ```
4. **Generate application key**:
   ```bash
   php artisan key:generate
   ```
5. **Run migrations**:
   ```bash
   php artisan migrate --force
   ```
6. **Clear caches**:
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

### 2. Next.js Frontend Deployment (www.mydomain.com)

1. **Configure environment variables**:
   ```bash
   # .env.local or .env.production
   NEXT_PUBLIC_API_URL=https://api.mydomain.com/api/v1
   NEXT_PUBLIC_BACKEND_URL=https://api.mydomain.com
   ```

2. **Build the application**:
   ```bash
   cd front-end
   npm install
   npm run build
   ```

3. **Deploy static files** to your main domain

### 3. Domain Configuration

#### Main Domain (www.mydomain.com)
- Point to Next.js build files
- Configure for static hosting (Vercel, Netlify, or traditional hosting)

#### Subdomain (api.mydomain.com)
- Point to Laravel application
- Configure PHP/Apache/Nginx
- Ensure SSL certificate is installed

## 🔧 Route Configuration

### Laravel Routes (Cleaned Up)
The `routes/web.php` file has been cleaned to only include:

✅ **Authentication Routes**:
- `/login` - Login form
- `/register` - Registration form
- POST handlers for auth

✅ **Dashboard Routes** (Protected):
- `/dashboard/*` - All dashboard pages
- Middleware: `auth`

✅ **API Routes**:
- `/api/v1/*` - All API endpoints (in `routes/api.php`)

❌ **Removed Public Routes**:
- `/about`, `/blog`, `/scholarships`, `/contact`
- These are now handled by Next.js

### Next.js Routes
Handles all public pages:
- `/` - Homepage
- `/about` - About page
- `/blog` - Blog listing and posts
- `/scholarships` - Scholarship pages
- `/contact` - Contact page
- `/donate` - Donation pages

## 🔐 Authentication Flow

1. **User visits Next.js frontend** (www.mydomain.com)
2. **Clicks "Apply" or "Login"** → Redirected to Laravel (api.mydomain.com/login)
3. **After authentication** → Redirected to appropriate dashboard
4. **API calls from Next.js** → Laravel backend with authentication

## 🌐 CORS Configuration

Ensure Laravel allows requests from your Next.js domain:

```php
// config/cors.php
'allowed_origins' => [
    'https://www.mydomain.com',
    'https://mydomain.com',
],
```

## 📝 Environment Variables

### Laravel (.env)
```env
APP_URL=https://api.mydomain.com
FRONTEND_URL=https://www.mydomain.com
SANCTUM_STATEFUL_DOMAINS=www.mydomain.com,mydomain.com
SESSION_DOMAIN=.mydomain.com
```

### Next.js (.env.local)
```env
NEXT_PUBLIC_API_URL=https://api.mydomain.com/api/v1
NEXT_PUBLIC_BACKEND_URL=https://api.mydomain.com
```

## ✅ Testing Checklist

- [ ] Next.js frontend loads on main domain
- [ ] Laravel backend accessible on subdomain
- [ ] Authentication redirects work properly
- [ ] API calls from frontend to backend succeed
- [ ] Dashboard access is properly protected
- [ ] File uploads work correctly
- [ ] PayStack integration functions
- [ ] Email notifications are sent
- [ ] Mobile responsiveness maintained

## 🚨 Troubleshooting

### Common Issues:

1. **CORS Errors**: Check `config/cors.php` configuration
2. **Authentication Issues**: Verify `SESSION_DOMAIN` and `SANCTUM_STATEFUL_DOMAINS`
3. **API 404 Errors**: Ensure API routes are in `routes/api.php`
4. **Redirect Loops**: Check `FRONTEND_URL` environment variable

### Debug Commands:
```bash
# Laravel
php artisan route:list
php artisan config:show
tail -f storage/logs/laravel.log

# Next.js
npm run dev
npm run build && npm run start
```

## 📞 Support

If you encounter issues during deployment, check:
1. Server error logs
2. Browser developer console
3. Network tab for failed API calls
4. Laravel logs in `storage/logs/`
