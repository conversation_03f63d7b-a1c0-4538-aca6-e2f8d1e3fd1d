<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentProgression extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'student_id',
        'from_grade',
        'to_grade',
        'from_academic_year',
        'to_academic_year',
        'status',
        'progression_date',
        'final_grade',
        'notes',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'progression_date' => 'date',
        'final_grade' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = ['from_grade_display', 'to_grade_display', 'progression_display'];

    /**
     * Get the display format for the from grade.
     */
    public function getFromGradeDisplayAttribute(): string
    {
        return $this->formatGradeDisplay($this->from_grade);
    }

    /**
     * Get the display format for the to grade.
     */
    public function getToGradeDisplayAttribute(): string
    {
        return $this->formatGradeDisplay($this->to_grade);
    }

    /**
     * Get the progression display text.
     */
    public function getProgressionDisplayAttribute(): string
    {
        return "{$this->from_grade_display} → {$this->to_grade_display} ({$this->from_academic_year} → {$this->to_academic_year})";
    }

    /**
     * Format grade for display.
     */
    private function formatGradeDisplay(string $grade): string
    {
        return match($grade) {
            'primary_1' => 'Primary 1',
            'primary_2' => 'Primary 2',
            'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4',
            'primary_5' => 'Primary 5',
            'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1 (JSS 1)',
            'secondary_2' => 'Secondary 2 (JSS 2)',
            'secondary_3' => 'Secondary 3 (JSS 3)',
            'secondary_4' => 'Secondary 4 (SSS 1)',
            'secondary_5' => 'Secondary 5 (SSS 2)',
            'secondary_6' => 'Secondary 6 (SSS 3)',
            'university_1' => 'University Year 1',
            'university_2' => 'University Year 2',
            'university_3' => 'University Year 3',
            'university_4' => 'University Year 4',
            'university_5' => 'University Year 5',
            'university_6' => 'University Year 6',
            'graduated' => 'Graduated',
            default => ucfirst(str_replace('_', ' ', $grade))
        };
    }

    /**
     * Check if this progression represents a level transition.
     */
    public function isLevelTransition(): bool
    {
        $primaryGrades = ['primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6'];
        $secondaryGrades = ['secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6'];
        $universityGrades = ['university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6'];

        // Primary to Secondary transition
        if ($this->from_grade === 'primary_6' && $this->to_grade === 'secondary_1') {
            return true;
        }

        // Secondary to University transition
        if ($this->from_grade === 'secondary_6' && $this->to_grade === 'university_1') {
            return true;
        }

        return false;
    }

    /**
     * Get the level transition type.
     */
    public function getTransitionType(): ?string
    {
        if ($this->from_grade === 'primary_6' && $this->to_grade === 'secondary_1') {
            return 'primary_to_secondary';
        }

        if ($this->from_grade === 'secondary_6' && $this->to_grade === 'university_1') {
            return 'secondary_to_university';
        }

        if ($this->to_grade === 'graduated') {
            return 'graduation';
        }

        return 'grade_advancement';
    }

    /**
     * Check if progression is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved' || $this->status === 'completed';
    }

    /**
     * Check if progression is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Get the student this progression belongs to.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the user who approved this progression.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope for pending progressions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved progressions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for completed progressions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for progressions by academic year.
     */
    public function scopeByAcademicYear($query, $year)
    {
        return $query->where('from_academic_year', $year);
    }

    /**
     * Scope for level transitions.
     */
    public function scopeLevelTransitions($query)
    {
        return $query->where(function ($q) {
            $q->where('from_grade', 'primary_6')->where('to_grade', 'secondary_1')
              ->orWhere(function ($q2) {
                  $q2->where('from_grade', 'secondary_6')->where('to_grade', 'university_1');
              });
        });
    }

    /**
     * Scope for graduations.
     */
    public function scopeGraduations($query)
    {
        return $query->where('to_grade', 'graduated');
    }
}
