(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{2521:(e,r,t)=>{"use strict";t.d(r,{Navigation:()=>T});var a=t(5155),s=t(2115),n=t(5695),o=t(6874),l=t.n(o),i=t(7168),d=t(5452),c=t(2085),g=t(4416),m=t(3999);let h=d.bL,x=d.l9;d.bm;let u=d.ZL,p=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(d.hJ,{className:(0,m.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s,ref:r})});p.displayName=d.hJ.displayName;let f=(0,c.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=s.forwardRef((e,r)=>{let{side:t="right",className:s,children:n,...o}=e;return(0,a.jsxs)(u,{children:[(0,a.jsx)(p,{}),(0,a.jsxs)(d.UC,{ref:r,className:(0,m.cn)(f({side:t}),s),...o,children:[n,(0,a.jsxs)(d.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});v.displayName=d.UC.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(d.hE,{ref:r,className:(0,m.cn)("text-lg font-semibold text-foreground",t),...s})}).displayName=d.hE.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(d.VY,{ref:r,className:(0,m.cn)("text-sm text-muted-foreground",t),...s})}).displayName=d.VY.displayName;var b=t(8698),j=t(3052),N=t(5196),y=t(9428);let w=b.bL,k=b.l9;b.YJ,b.ZL,b.Pb,b.z6,s.forwardRef((e,r)=>{let{className:t,inset:s,children:n,...o}=e;return(0,a.jsxs)(b.ZP,{ref:r,className:(0,m.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",t),...o,children:[n,(0,a.jsx)(j.A,{className:"ml-auto"})]})}).displayName=b.ZP.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(b.G5,{ref:r,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})}).displayName=b.G5.displayName;let _=s.forwardRef((e,r)=>{let{className:t,sideOffset:s=4,...n}=e;return(0,a.jsx)(b.ZL,{children:(0,a.jsx)(b.UC,{ref:r,sideOffset:s,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});_.displayName=b.UC.displayName;let A=s.forwardRef((e,r)=>{let{className:t,inset:s,...n}=e;return(0,a.jsx)(b.q7,{ref:r,className:(0,m.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",t),...n})});A.displayName=b.q7.displayName,s.forwardRef((e,r)=>{let{className:t,children:s,checked:n,...o}=e;return(0,a.jsxs)(b.H_,{ref:r,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:n,...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(b.VF,{children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})}),s]})}).displayName=b.H_.displayName,s.forwardRef((e,r)=>{let{className:t,children:s,...n}=e;return(0,a.jsxs)(b.hN,{ref:r,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(b.VF,{children:(0,a.jsx)(y.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=b.hN.displayName,s.forwardRef((e,r)=>{let{className:t,inset:s,...n}=e;return(0,a.jsx)(b.JU,{ref:r,className:(0,m.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...n})}).displayName=b.JU.displayName,s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(b.wv,{ref:r,className:(0,m.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=b.wv.displayName;var z=t(1976),C=t(7580),L=t(6785),E=t(7949),S=t(5040),O=t(1497),P=t(6474),R=t(4783),$=t(3109),H=t(4531);function T(){let[e,r]=(0,s.useState)(!1),[t,o]=(0,s.useState)(!1),[c,m]=(0,s.useState)(!1),[u,p]=(0,s.useState)(null),[f,b]=(0,s.useState)(!1),j=(0,n.usePathname)(),{settings:N,loading:y}=(0,H.t)(),T=(0,s.useRef)(null);(0,s.useEffect)(()=>{b(!0)},[]),(0,s.useEffect)(()=>{let e=()=>{m(window.scrollY>10)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)},[]);let U=e=>{T.current&&clearTimeout(T.current),p(e)},W=()=>{T.current=setTimeout(()=>{p(null)},150)},M=[{href:"/about",label:"About Us",icon:z.A,description:"Learn about our mission"},{href:"/team",label:"Our Team",icon:C.A,description:"Meet our dedicated team"}],F=[{href:"/projects",label:"Projects",icon:L.A,description:"Our community projects"},{href:"/scholarships",label:"Scholarships",icon:E.A,description:"Educational support"},{href:"/learn-with-us",label:"Learn With Us",icon:S.A,description:"Educational programs"}],I=[{href:"/contact",label:"Contact",icon:O.A,description:"Get in touch"},{href:"/blog",label:"Blog",icon:S.A,description:"Latest updates"}],B=e=>e.some(e=>j===e.href),G=(null==N?void 0:N.app_name)||"Laravel NGO",Y=null==N?void 0:N.app_logo;return console.log("Navigation - Settings:",N),console.log("Navigation - App Logo:",Y),(0,a.jsx)("header",{className:"fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ".concat(c?"bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5":"bg-white/95 backdrop-blur-md border-b border-gray-200/30"," dark:bg-gray-950/95 dark:supports-[backdrop-filter]:bg-gray-950/80"),children:(0,a.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(l(),{href:"/",className:"group flex items-center gap-3 hover:scale-105 transition-all duration-300 ease-out",children:[Y?(0,a.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,a.jsx)("img",{src:Y,alt:"".concat(G," Logo"),className:"h-9 w-9 object-cover transition-transform duration-300 group-hover:scale-110"})}):(0,a.jsxs)("div",{className:"relative h-9 w-9 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg group-hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"}),(0,a.jsx)(z.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white transition-colors duration-300",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:G})})]}),(0,a.jsxs)("nav",{className:"hidden lg:flex items-center space-x-1",children:[(0,a.jsxs)(l(),{href:"/",className:"group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ".concat("/"===j?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Home"}),"/"===j&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>U("about"),onMouseLeave:W,children:(0,a.jsxs)(w,{open:"about"===u,onOpenChange:e=>p(e?"about":null),children:[(0,a.jsx)(k,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(B(M)||"about"===u?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"About"}),(0,a.jsx)(P.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("about"===u?"rotate-180":"")}),(B(M)||"about"===u)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(_,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:M.map(e=>{let r=e.icon;return(0,a.jsx)(A,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(r,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>U("programs"),onMouseLeave:W,children:(0,a.jsxs)(w,{open:"programs"===u,onOpenChange:e=>p(e?"programs":null),children:[(0,a.jsx)(k,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(B(F)||"programs"===u?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Programs"}),(0,a.jsx)(P.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("programs"===u?"rotate-180":"")}),(B(F)||"programs"===u)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(_,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:F.map(e=>{let r=e.icon;return(0,a.jsx)(A,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(r,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,a.jsxs)(l(),{href:"/impact",className:"group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ".concat("/impact"===j?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Impact"}),"/impact"===j&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>U("involved"),onMouseLeave:W,children:(0,a.jsxs)(w,{open:"involved"===u,onOpenChange:e=>p(e?"involved":null),children:[(0,a.jsx)(k,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(B(I)||"involved"===u?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Get Involved"}),(0,a.jsx)(P.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("involved"===u?"rotate-180":"")}),(B(I)||"involved"===u)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(_,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:I.map(e=>{let r=e.icon;return(0,a.jsx)(A,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(r,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})})]}),f&&(0,a.jsx)("div",{className:"hidden lg:flex items-center gap-3",suppressHydrationWarning:!0,children:t?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l(),{href:"/donate",children:(0,a.jsxs)(i.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Donate Now"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l(),{href:"/auth/login",children:(0,a.jsxs)(i.$,{variant:"outline",className:"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Login"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300"})]})}),(0,a.jsx)(l(),{href:"/auth/register",children:(0,a.jsxs)(i.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Register"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})]})}),(0,a.jsxs)(h,{open:e,onOpenChange:r,children:[(0,a.jsx)(x,{asChild:!0,className:"lg:hidden",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"icon",className:"group relative overflow-hidden transition-all duration-300 hover:scale-110 focus:scale-110 rounded-xl hover:bg-green-50",children:[(0,a.jsx)("div",{className:"absolute inset-0 transition-all duration-300 ".concat(e?"rotate-180 scale-75":""),children:e?(0,a.jsx)(g.A,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"}):(0,a.jsx)(R.A,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"})}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,a.jsxs)(v,{side:"right",className:"w-[320px] sm:w-[400px] z-[100] p-0 bg-white/95 backdrop-blur-xl border-l border-gray-200/50",children:[(0,a.jsx)(d.hE,{className:"sr-only",children:"Mobile Navigation Menu"}),(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200/50",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[Y?(0,a.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,a.jsx)("img",{src:Y,alt:"".concat(G," Logo"),className:"h-8 w-auto object-contain"})}):(0,a.jsx)("div",{className:"h-8 w-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsx)(z.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:G})})]})}),(0,a.jsxs)("div",{className:"flex-1 p-6 space-y-6 overflow-y-auto",children:[(0,a.jsx)(l(),{href:"/",className:"block text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat("/"===j?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>r(!1),children:"Home"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"About"}),M.map(e=>{let t=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(j===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>r(!1),children:[(0,a.jsx)(t,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Programs"}),F.map(e=>{let t=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(j===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>r(!1),children:[(0,a.jsx)(t,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,a.jsxs)(l(),{href:"/impact",className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat("/impact"===j?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>r(!1),children:[(0,a.jsx)($.A,{className:"h-5 w-5"}),"Impact"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Get Involved"}),I.map(e=>{let t=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(j===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>r(!1),children:[(0,a.jsx)(t,{className:"h-5 w-5"}),e.label]},e.href)})]}),!t&&(0,a.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,a.jsx)(l(),{href:"/auth/login",onClick:()=>r(!1),children:(0,a.jsx)(i.$,{variant:"outline",className:"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3",children:"Login"})}),(0,a.jsx)(l(),{href:"/auth/register",onClick:()=>r(!1),children:(0,a.jsx)(i.$,{className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg",children:"Register"})})]})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-200/50",children:(0,a.jsx)(l(),{href:"/donate",onClick:()=>r(!1),children:(0,a.jsx)(i.$,{className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg",children:"Donate Now"})})})]})]})]})]})})}},3999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(2596),s=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},4531:(e,r,t)=>{"use strict";t.d(r,{t:()=>n});var a=t(2115),s=t(1886);let n=()=>{let[e,r]=(0,a.useState)(null),[t,n]=(0,a.useState)(!0),[o,l]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{try{let e=await s.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),r(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),l(e instanceof Error?e.message:"Unknown error"),r({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{n(!1)}})()},[]),{settings:e,loading:t,error:o}}},6552:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,9324,23)),Promise.resolve().then(t.bind(t,7746)),Promise.resolve().then(t.bind(t,2521)),Promise.resolve().then(t.bind(t,9304)),Promise.resolve().then(t.t.bind(t,4699,23))},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(5155),s=t(2115),n=t(9708),o=t(2085),l=t(3999);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:d=!1,...c}=e,g=d?n.DX:"button";return(0,a.jsx)(g,{className:(0,l.cn)(i({variant:s,size:o,className:t})),ref:r,suppressHydrationWarning:!0,...c})});d.displayName="Button"},7746:(e,r,t)=>{"use strict";t.d(r,{Footer:()=>j});var a=t(5155),s=t(2115),n=t(6874),o=t.n(n),l=t(488),i=t(8175),d=t(5684),c=t(2894),g=t(2925),m=t(1976),h=t(1366),x=t(4516),u=t(9420),p=t(8883),f=t(7168),v=t(9852),b=t(4531);function j(){let[e,r]=(0,s.useState)(!1);(0,s.useEffect)(()=>{r(!0)},[]);let{settings:t,loading:n}=(0,b.t)(),j=(null==t?void 0:t.app_name)||"Laravel NGO",N=null==t?void 0:t.app_logo,y=(null==t?void 0:t.contact_email)||"<EMAIL>",w=(null==t?void 0:t.contact_phone)||"+234 ************",k=(null==t?void 0:t.site_description)||"Empowering communities through education, development programs, and sustainable initiatives.",_=null==t?void 0:t.organization_address,A=null==t?void 0:t.organization_city,z=null==t?void 0:t.organization_state,C=[_,A,z,null==t?void 0:t.organization_country,null==t?void 0:t.organization_postal_code].filter(Boolean).join(", "),L=[{key:"social_facebook",icon:l.A,label:"Facebook"},{key:"social_twitter",icon:i.A,label:"Twitter"},{key:"social_instagram",icon:d.A,label:"Instagram"},{key:"social_linkedin",icon:c.A,label:"LinkedIn"},{key:"social_youtube",icon:g.A,label:"YouTube"}];return(0,a.jsx)("footer",{className:"bg-green-950 dark:bg-black text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o(),{href:"/",className:"flex items-center gap-2",children:[N?(0,a.jsx)("img",{src:N,alt:"".concat(j," Logo"),className:"h-8 w-auto object-contain"}):(0,a.jsx)("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-green-600",children:(0,a.jsx)(m.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold",children:(0,a.jsx)("span",{className:"text-amber-400 dark:text-amber-400",children:j})})]}),(0,a.jsx)("p",{className:"text-green-200 text-sm",children:k}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[L.map(e=>{let{key:r,icon:s,label:n}=e,l=null==t?void 0:t[r];return l?(0,a.jsx)(o(),{href:l,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":n,children:(0,a.jsx)(s,{className:"h-4 w-4"})})},r):null}),(null==t?void 0:t.social_whatsapp)&&(0,a.jsx)(o(),{href:"https://wa.me/".concat(t.social_whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"WhatsApp",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})}),(null==t?void 0:t.social_telegram)&&(0,a.jsx)(o(),{href:t.social_telegram,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"Telegram",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"})})})}),(null==t?void 0:t.social_tiktok)&&(0,a.jsx)(o(),{href:t.social_tiktok,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"TikTok",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"})})})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/about",className:"text-green-200 hover:text-amber-400 transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/projects",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Projects"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/scholarships",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Scholarships"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/learn-with-us",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Learn With Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/impact",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Impact"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/team",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Team"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Info"}),(0,a.jsxs)("div",{className:"space-y-3",children:[C&&(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-green-200 text-sm",children:C})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("a",{href:"tel:".concat(w),className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:w})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("a",{href:"mailto:".concat(y),className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:y})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-green-200 text-sm",children:"Subscribe to our newsletter for updates on our programs and impact."}),e&&(0,a.jsxs)("div",{className:"flex gap-2",suppressHydrationWarning:!0,children:[(0,a.jsx)(v.p,{type:"email",placeholder:"Your email",className:"bg-green-900 border-green-700 text-white placeholder:text-green-300 focus:ring-amber-500 focus:border-amber-500 rounded-md",suppressHydrationWarning:!0}),(0,a.jsx)(f.$,{className:"bg-amber-500 hover:bg-amber-600 text-green-950 dark:text-green-950",suppressHydrationWarning:!0,children:"Subscribe"})]}),(0,a.jsxs)("div",{className:"pt-4 space-y-1",children:[(0,a.jsx)(o(),{href:"/privacy",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Privacy Policy"}),(0,a.jsx)(o(),{href:"/terms",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Terms of Service"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-green-800 mt-8 pt-8 text-center",children:[(0,a.jsxs)("p",{className:"text-green-200 text-sm",children:["\xa9 ",new Date().getFullYear()," ",j,". All rights reserved."]}),(0,a.jsx)("p",{className:"text-green-300 text-xs mt-2",children:"Built with ❤️ to empower communities and create lasting impact."})]})]})})}},9304:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var a=t(5155),s=t(1362);function n(e){let{children:r,...t}=e;return(0,a.jsx)(s.N,{...t,children:r})}},9324:()=>{},9852:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(5155),s=t(2115),n=t(3999);let o=s.forwardRef((e,r)=>{let{className:t,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,suppressHydrationWarning:!0,...o})});o.displayName="Input"}},e=>{var r=r=>e(e.s=r);e.O(0,[533,702,598,874,514,114,572,886,441,684,358],()=>r(6552)),_N_E=e.O()}]);