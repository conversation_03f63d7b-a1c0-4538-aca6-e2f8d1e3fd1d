"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[886],{1886:(t,e,s)=>{s.d(e,{uE:()=>a});class r{async request(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.baseURL.includes("/api/v1")?"".concat(this.baseURL).concat(t):"".concat(this.baseURL,"/api/v1").concat(t),r={headers:{"Content-Type":"application/json",Accept:"application/json",...e.headers},...e};try{console.log("API Request: ".concat(e.method||"GET"," ").concat(s));let a=await fetch(s,r),n=await a.json();return console.log("API Response for ".concat(t,":"),n),n}catch(t){throw console.error("API request failed:",t),t}}async get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.request(t,{...e,method:"GET"})}async post(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={...s,method:"POST"};return e&&(r.body=JSON.stringify(e)),this.request(t,r)}async put(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={...s,method:"PUT"};return e&&(r.body=JSON.stringify(e)),this.request(t,r)}async patch(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={...s,method:"PATCH"};return e&&(r.body=JSON.stringify(e)),this.request(t,r)}async delete(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.request(t,{...e,method:"DELETE"})}async register(t){var e;let s=await this.request("/register",{method:"POST",body:JSON.stringify({first_name:t.first_name,last_name:t.last_name,email:t.email,password:t.password,password_confirmation:t.password_confirmation,phone_number:t.phone_number,address:t.address,date_of_birth:t.date_of_birth,city:t.city,state:t.state,country:t.country})});if(s.success&&(null===(e=s.data)||void 0===e?void 0:e.token)&&(localStorage.setItem("authToken",s.data.token),localStorage.setItem("user",JSON.stringify(s.data.user))),s.success&&t.user_type&&t.additional_data)try{if("volunteer"===t.user_type)await this.applyAsVolunteer(t.additional_data);else if("student"===t.user_type||"partner"===t.user_type){let e={user_type:t.user_type,profile_data:t.additional_data,profile_completed:!0};await this.updateUserPreferences(e)}}catch(t){return console.error("Additional data submission failed:",t),{...s,message:s.message+" However, additional profile information could not be saved. You can complete your profile later."}}return s}async getProfile(){return this.request("/profile")}async updateProfile(t){return this.request("/profile",{method:"PUT",body:JSON.stringify(t)})}async updateUserPreferences(t){return this.request("/profile",{method:"PUT",body:JSON.stringify({preferences:t})})}async uploadAvatar(t){let e=new FormData;e.append("avatar",t);let s=this.baseURL.includes("/api/v1")?"".concat(this.baseURL,"/profile/avatar"):"".concat(this.baseURL,"/api/v1/profile/avatar");try{let t=await fetch(s,{method:"POST",headers:{Accept:"application/json"},body:e});return await t.json()}catch(t){throw console.error("Avatar upload failed:",t),t}}async changePassword(t){return this.request("/profile/password",{method:"PUT",body:JSON.stringify(t)})}async generateQrCode(){return this.request("/profile/generate-qr",{method:"POST"})}async getIdCard(){return this.request("/profile/id-card")}async getDashboardSummary(){return this.request("/dashboard/summary")}async getVolunteerApplication(){return this.request("/volunteer/application")}async getVolunteerHours(){return this.request("/volunteer/hours")}async getVolunteerOpportunities(){return this.request("/volunteer/opportunities")}async logVolunteerHours(t){return this.request("/volunteer/hours",{method:"POST",body:JSON.stringify(t)})}async getMyScholarshipApplications(){return this.request("/scholarships/my-applications")}async getScholarships(){return this.request("/scholarships")}async applyForScholarship(t,e){return this.request("/scholarships/".concat(t,"/apply"),{method:"POST",body:JSON.stringify(e)})}async getMyEventRegistrations(){return this.request("/events/my-registrations")}async getUpcomingEvents(){return this.request("/events/upcoming")}buildQueryString(t){let e=new URLSearchParams;return Object.entries(t).forEach(t=>{let[s,r]=t;null!=r&&""!==r&&(Array.isArray(r)?r.forEach(t=>e.append("".concat(s,"[]"),t)):e.append(s,r.toString()))}),e.toString()}async getAdminDashboard(){return this.request("/admin/dashboard")}async getAdminAnalytics(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/dashboard/analytics".concat(e))}async getAdminStats(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/dashboard/stats".concat(e))}async getAdminUsers(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/users".concat(e))}async createAdminUser(t){return this.request("/admin/users",{method:"POST",body:JSON.stringify(t)})}async updateAdminUser(t,e){return this.request("/admin/users/".concat(t),{method:"PUT",body:JSON.stringify(e)})}async deleteAdminUser(t){return this.request("/admin/users/".concat(t),{method:"DELETE"})}async bulkActionUsers(t){return this.request("/admin/users/bulk-action",{method:"POST",body:JSON.stringify(t)})}async exportUsers(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/users/export".concat(e))}async getScholarshipApplications(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/scholarship-applications".concat(e))}async reviewScholarshipApplication(t,e){return this.request("/admin/scholarship-applications/".concat(t,"/review"),{method:"PUT",body:JSON.stringify(e)})}async bulkActionScholarshipApplications(t){return this.request("/admin/scholarship-applications/bulk-action",{method:"POST",body:JSON.stringify(t)})}async exportScholarshipApplications(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/scholarship-applications/export".concat(e))}async getScholarshipStatistics(){return this.request("/admin/scholarships/statistics")}async getAdminEvents(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/events".concat(e))}async createAdminEvent(t){return this.request("/admin/events",{method:"POST",body:JSON.stringify(t)})}async updateAdminEvent(t,e){return this.request("/admin/events/".concat(t),{method:"PUT",body:JSON.stringify(e)})}async deleteAdminEvent(t){return this.request("/admin/events/".concat(t),{method:"DELETE"})}async bulkActionEvents(t){return this.request("/admin/events/bulk-action",{method:"POST",body:JSON.stringify(t)})}async exportEvents(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/events/export".concat(e))}async getEventStatistics(){return this.request("/admin/events/statistics")}async getAdminPrograms(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/programs".concat(e))}async createAdminProgram(t){return this.request("/admin/programs",{method:"POST",body:JSON.stringify(t)})}async updateAdminProgram(t,e){return this.request("/admin/programs/".concat(t),{method:"PUT",body:JSON.stringify(e)})}async deleteAdminProgram(t){return this.request("/admin/programs/".concat(t),{method:"DELETE"})}async getAdminBlogPosts(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/blog/posts".concat(e))}async createAdminBlogPost(t){return this.request("/admin/blog/posts",{method:"POST",body:JSON.stringify(t)})}async updateAdminBlogPost(t,e){return this.request("/admin/blog/posts/".concat(t),{method:"PUT",body:JSON.stringify(e)})}async deleteAdminBlogPost(t){return this.request("/admin/blog/posts/".concat(t),{method:"DELETE"})}async exportBlogPosts(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/admin/blog/posts/export".concat(e))}async getMyDonations(){return this.request("/donations/my-donations")}async getDonationCampaigns(){return this.request("/donations/campaigns")}async getBlogPosts(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.request("/blog/posts?page=".concat(t))}async getBlogPost(t){return this.request("/blog/posts/".concat(t))}async getEvents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.request("/events?page=".concat(t))}async getEvent(t){return this.request("/events/".concat(t))}async registerForEvent(t,e){return this.request("/events/".concat(t,"/register"),{method:"POST",body:JSON.stringify({additional_info:e})})}async getPrograms(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.request("/programs?page=".concat(t))}async getProgram(t){return this.request("/programs/".concat(t))}async getScholarship(t){return this.request("/scholarships/".concat(t))}async getPublicScholarships(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/public-scholarships".concat(e))}async getPublicScholarship(t){return this.request("/public-scholarships/".concat(t))}async getProjectScholarshipStatistics(){return this.request("/project-scholarships/statistics")}async getProjectScholarshipPortfolio(){return this.request("/project-scholarships/portfolio")}async getScholarshipsByStatus(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"active";return this.request("/project-scholarships/by-status?status=".concat(t))}async getImpactStatistics(){return this.request("/impact/statistics")}async getScholarshipImpactStatistics(){return this.request("/impact/scholarship-statistics")}async submitScholarshipApplication(t,e){let s=this.baseURL.includes("/api/v1")?"".concat(this.baseURL,"/apply-scholarship/").concat(t):"".concat(this.baseURL,"/api/v1/apply-scholarship/").concat(t);try{let t=await fetch(s,{method:"POST",headers:{Accept:"application/json"},body:e}),r=await t.json();if(console.log("API Response for scholarship application:",r),!t.ok)throw Error(r.message||"HTTP error! status: ".concat(t.status));return r}catch(t){throw console.error("Error submitting scholarship application:",t),t}}async applyAsVolunteer(t){return this.request("/volunteer/apply",{method:"POST",body:JSON.stringify(t)})}async submitContactForm(t){return this.request("/contact",{method:"POST",body:JSON.stringify(t)})}async subscribeToNewsletter(t){return this.request("/newsletter/subscribe",{method:"POST",body:JSON.stringify({email:t})})}async getSettings(){return this.request("/settings")}async getPartnerDashboard(){return this.request("/partner-dashboard")}async getPartnerAvailableScholarships(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/partner-scholarships/available".concat(e))}async getPartnerScholarshipDetails(t){return this.request("/partner-scholarships/".concat(t,"/details"))}async applyForStudentScholarship(t,e){return this.request("/partner-scholarships/".concat(t,"/apply"),{method:"POST",body:e})}async getPartnerApplications(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/partner-scholarships/my-applications".concat(e))}async getPartnerStudents(t){let e=t?"?".concat(this.buildQueryString(t)):"";return this.request("/partner-students".concat(e))}async addPartnerStudent(t){return this.request("/partner-students",{method:"POST",body:t})}constructor(t="https://halimakqfoundation.fnskills.ng/api/v1"){this.baseURL=t}}let a=new r}}]);