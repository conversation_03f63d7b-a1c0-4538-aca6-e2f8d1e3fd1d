<?php

/**
 * Comprehensive API Test Runner
 * 
 * This script runs all API tests and generates a detailed report
 * Usage: php tests/run-api-tests.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

class ApiTestRunner
{
    private $testResults = [];
    private $totalTests = 0;
    private $passedTests = 0;
    private $failedTests = 0;

    public function run()
    {
        echo "🚀 Starting NGO Platform API Test Suite\n";
        echo "=====================================\n\n";

        $this->runTestSuite('Authentication Tests', [
            'php artisan test tests/Feature/Api/AuthenticationTest.php --verbose'
        ]);

        $this->runTestSuite('Authorization Tests', [
            'php artisan test tests/Feature/Api/AuthorizationTest.php --verbose'
        ]);

        $this->runTestSuite('Scholarship Tests', [
            'php artisan test tests/Feature/Api/ScholarshipTest.php --verbose'
        ]);

        $this->runTestSuite('Security Tests', [
            'php artisan test tests/Feature/Api/SecurityTest.php --verbose'
        ]);

        $this->runTestSuite('Performance Tests', [
            'php artisan test tests/Feature/Api/PerformanceTest.php --verbose'
        ]);

        $this->generateReport();
    }

    private function runTestSuite($suiteName, $commands)
    {
        echo "📋 Running {$suiteName}\n";
        echo str_repeat('-', 50) . "\n";

        foreach ($commands as $command) {
            $output = [];
            $returnCode = 0;
            
            exec($command . ' 2>&1', $output, $returnCode);
            
            $this->processTestOutput($suiteName, $output, $returnCode);
        }

        echo "\n";
    }

    private function processTestOutput($suiteName, $output, $returnCode)
    {
        $outputString = implode("\n", $output);
        
        // Parse PHPUnit output
        preg_match_all('/(\d+) tests?, (\d+) assertions?/', $outputString, $matches);
        
        if (!empty($matches[1])) {
            $tests = (int) $matches[1][0];
            $assertions = (int) $matches[2][0];
            
            $this->totalTests += $tests;
            
            if ($returnCode === 0) {
                $this->passedTests += $tests;
                echo "✅ {$suiteName}: {$tests} tests passed ({$assertions} assertions)\n";
            } else {
                $this->failedTests += $tests;
                echo "❌ {$suiteName}: {$tests} tests failed\n";
                
                // Extract failure details
                $failures = $this->extractFailures($outputString);
                foreach ($failures as $failure) {
                    echo "   💥 {$failure}\n";
                }
            }
        } else {
            echo "⚠️  {$suiteName}: Could not parse test results\n";
        }

        $this->testResults[$suiteName] = [
            'output' => $outputString,
            'return_code' => $returnCode,
            'tests' => $tests ?? 0,
            'passed' => $returnCode === 0
        ];
    }

    private function extractFailures($output)
    {
        $failures = [];
        $lines = explode("\n", $output);
        
        foreach ($lines as $line) {
            if (strpos($line, 'FAILED') !== false || strpos($line, 'ERROR') !== false) {
                $failures[] = trim($line);
            }
        }
        
        return $failures;
    }

    private function generateReport()
    {
        echo "📊 Test Summary Report\n";
        echo "=====================\n\n";

        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests} ✅\n";
        echo "Failed: {$this->failedTests} ❌\n";
        
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        echo "Success Rate: {$successRate}%\n\n";

        // Detailed breakdown
        echo "📋 Detailed Results:\n";
        echo "-------------------\n";
        
        foreach ($this->testResults as $suite => $result) {
            $status = $result['passed'] ? '✅' : '❌';
            echo "{$status} {$suite}: {$result['tests']} tests\n";
        }

        echo "\n";

        // Recommendations
        $this->generateRecommendations();

        // Save detailed report
        $this->saveDetailedReport();
    }

    private function generateRecommendations()
    {
        echo "💡 Recommendations:\n";
        echo "------------------\n";

        if ($this->failedTests > 0) {
            echo "• Fix failing tests before deploying to production\n";
            echo "• Review error logs for detailed failure information\n";
            echo "• Consider adding more test coverage for edge cases\n";
        } else {
            echo "• All tests passing! ✨\n";
            echo "• Consider adding integration tests with external services\n";
            echo "• Monitor API performance in production\n";
        }

        echo "• Run tests regularly in CI/CD pipeline\n";
        echo "• Add load testing for high-traffic scenarios\n";
        echo "• Implement automated security scanning\n\n";
    }

    private function saveDetailedReport()
    {
        $reportData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => [
                'total_tests' => $this->totalTests,
                'passed_tests' => $this->passedTests,
                'failed_tests' => $this->failedTests,
                'success_rate' => $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0
            ],
            'results' => $this->testResults
        ];

        $reportFile = __DIR__ . '/reports/api-test-report-' . date('Y-m-d-H-i-s') . '.json';
        
        // Create reports directory if it doesn't exist
        $reportsDir = dirname($reportFile);
        if (!is_dir($reportsDir)) {
            mkdir($reportsDir, 0755, true);
        }

        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        echo "📄 Detailed report saved to: {$reportFile}\n";
    }
}

// Additional test utilities
class ApiTestUtilities
{
    public static function checkDatabaseConnection()
    {
        echo "🔍 Checking database connection...\n";
        
        $output = [];
        exec('php artisan migrate:status 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ Database connection successful\n";
            return true;
        } else {
            echo "❌ Database connection failed\n";
            echo "Output: " . implode("\n", $output) . "\n";
            return false;
        }
    }

    public static function setupTestEnvironment()
    {
        echo "🔧 Setting up test environment...\n";
        
        $commands = [
            'php artisan config:clear',
            'php artisan cache:clear',
            'php artisan migrate:fresh --seed --env=testing',
        ];

        foreach ($commands as $command) {
            echo "Running: {$command}\n";
            $output = [];
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode !== 0) {
                echo "❌ Command failed: {$command}\n";
                echo "Output: " . implode("\n", $output) . "\n";
                return false;
            }
        }

        echo "✅ Test environment setup complete\n\n";
        return true;
    }

    public static function runSecurityChecks()
    {
        echo "🔒 Running security checks...\n";
        
        $checks = [
            'Check for exposed .env files' => 'find . -name ".env*" -not -path "./vendor/*" -not -path "./.git/*"',
            'Check for debug mode in production' => 'grep -r "APP_DEBUG=true" . --exclude-dir=vendor --exclude-dir=.git',
            'Check for hardcoded secrets' => 'grep -r "password.*=" . --include="*.php" --exclude-dir=vendor --exclude-dir=.git | head -5'
        ];

        foreach ($checks as $description => $command) {
            echo "• {$description}...\n";
            $output = [];
            exec($command . ' 2>&1', $output, $returnCode);
            
            if (empty($output) || $returnCode !== 0) {
                echo "  ✅ No issues found\n";
            } else {
                echo "  ⚠️  Potential issues:\n";
                foreach (array_slice($output, 0, 3) as $line) {
                    echo "    - {$line}\n";
                }
            }
        }

        echo "\n";
    }
}

// Main execution
if (php_sapi_name() === 'cli') {
    $runner = new ApiTestRunner();
    
    // Pre-flight checks
    if (!ApiTestUtilities::checkDatabaseConnection()) {
        echo "❌ Cannot proceed without database connection\n";
        exit(1);
    }

    if (!ApiTestUtilities::setupTestEnvironment()) {
        echo "❌ Failed to setup test environment\n";
        exit(1);
    }

    // Run security checks
    ApiTestUtilities::runSecurityChecks();

    // Run the test suite
    $runner->run();

    echo "🎉 API Test Suite Complete!\n";
    echo "Check the generated report for detailed results.\n";
} else {
    echo "This script must be run from the command line.\n";
    exit(1);
}
