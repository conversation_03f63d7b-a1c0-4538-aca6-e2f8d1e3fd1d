@extends('layouts.admin')

@section('title', 'User Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">User Management</h1>
        <div>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New User
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Users</div>
                            <div class="h4">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Users</div>
                            <div class="h4">{{ $stats['active'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Volunteers</div>
                            <div class="h4">{{ $stats['volunteers'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-hands-helping fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New This Month</div>
                            <div class="h4">{{ $stats['new_this_month'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Users</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search users..." id="searchUsers">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterRole">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="volunteer">Volunteer</option>
                        <option value="donor">Donor</option>
                        <option value="user">User</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>User</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Joined Date</th>
                            <th>Last Activity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_users[]" value="{{ $user->id }}" class="form-check-input">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($user->profile_picture)
                                        <img src="{{ $user->profile_picture }}" alt="{{ $user->first_name }}" class="rounded-circle me-3" width="50" height="50">
                                    @else
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            {{ strtoupper(substr($user->first_name ?? 'U', 0, 1)) }}
                                        </div>
                                    @endif
                                    <div>
                                        <div class="fw-bold">
                                            {{ $user->first_name }} {{ $user->last_name }}
                                        </div>
                                        <small class="text-muted">{{ $user->email }}</small><br>
                                        @if($user->phone_number)
                                            <small class="text-muted">{{ $user->phone_number }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $user->role === 'admin' ? 'danger' :
                                    ($user->role === 'volunteer' ? 'info' :
                                    ($user->role === 'donor' ? 'success' : 'secondary'))
                                }}">
                                    {{ ucfirst($user->role ?? 'user') }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $user->status === 'active' ? 'success' :
                                    ($user->status === 'suspended' ? 'danger' : 'warning')
                                }}">
                                    {{ ucfirst($user->status ?? 'inactive') }}
                                </span>
                                @if($user->email_verified_at)
                                    <br><small class="text-success"><i class="fas fa-check-circle"></i> Verified</small>
                                @else
                                    <br><small class="text-warning"><i class="fas fa-exclamation-circle"></i> Unverified</small>
                                @endif
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>{{ $user->created_at ? $user->created_at->format('M d, Y') : 'N/A' }}</strong>
                                    @if($user->created_at)
                                        <br><small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    @if($user->last_login_at)
                                        <strong class="text-success">{{ $user->last_login_at->format('M d, Y') }}</strong>
                                        <br><small class="text-muted">{{ $user->last_login_at->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">Never</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($user->status === 'active')
                                        <form method="POST" action="{{ route('admin.users.update', $user->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="suspended">
                                            <input type="hidden" name="first_name" value="{{ $user->first_name }}">
                                            <input type="hidden" name="last_name" value="{{ $user->last_name }}">
                                            <input type="hidden" name="email" value="{{ $user->email }}">
                                            <input type="hidden" name="phone_number" value="{{ $user->phone_number }}">
                                            <input type="hidden" name="role" value="{{ $user->role }}">
                                            <button type="submit" class="btn btn-sm btn-outline-warning" title="Suspend">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.users.update', $user->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="active">
                                            <input type="hidden" name="first_name" value="{{ $user->first_name }}">
                                            <input type="hidden" name="last_name" value="{{ $user->last_name }}">
                                            <input type="hidden" name="email" value="{{ $user->email }}">
                                            <input type="hidden" name="phone_number" value="{{ $user->phone_number }}">
                                            <input type="hidden" name="role" value="{{ $user->role }}">
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="Activate">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <form method="POST" action="{{ route('admin.users.destroy', $user->id) }}" style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <h5>No Users Found</h5>
                                    <p>There are no users matching your criteria.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Bulk Actions -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <select id="bulkAction" class="form-select d-inline-block w-auto me-2">
                        <option value="">Bulk Actions</option>
                        <option value="activate">Activate Selected</option>
                        <option value="deactivate">Deactivate Selected</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button id="bulk-action-btn" class="btn btn-secondary">Apply</button>
                </div>
                <div>
                    @if(isset($users) && method_exists($users, 'links'))
                        {{ $users->links() }}
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('input[name="selected_users[]"]');

    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('input[name="selected_users[]"]:checked').length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
            selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
        });
    });



    // Bulk action functionality
    const bulkActionBtn = document.getElementById('bulk-action-btn');
    if (bulkActionBtn) {
        bulkActionBtn.addEventListener('click', function() {
            bulkAction();
        });
    }
});



function bulkAction() {
    const action = document.getElementById('bulkAction').value;
    const selectedUsers = Array.from(document.querySelectorAll('input[name="selected_users[]"]:checked'))
                               .map(cb => cb.value);

    if (!action) {
        alert('Please select an action');
        return;
    }

    if (selectedUsers.length === 0) {
        alert('Please select users first');
        return;
    }

    let confirmMessage;
    switch(action) {
        case 'activate':
            confirmMessage = `Activate ${selectedUsers.length} selected users?`;
            break;
        case 'deactivate':
            confirmMessage = `Deactivate ${selectedUsers.length} selected users?`;
            break;
        case 'delete':
            confirmMessage = `Delete ${selectedUsers.length} selected users? This action cannot be undone.`;
            break;
    }

    if (confirm(confirmMessage)) {
        // Create a form and submit it for bulk action
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/users/bulk-action';
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').content;
        form.appendChild(csrfToken);

        // Add action field
        const actionField = document.createElement('input');
        actionField.type = 'hidden';
        actionField.name = 'action';
        actionField.value = action;
        form.appendChild(actionField);

        // Add selected users
        selectedUsers.forEach(userId => {
            const userField = document.createElement('input');
            userField.type = 'hidden';
            userField.name = 'selected_users[]';
            userField.value = userId;
            form.appendChild(userField);
        });

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection