@extends('layouts.admin')

@section('title', 'User Management')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
<li class="breadcrumb-item active">Users</li>
@endsection

@section('content')
<!-- Enhanced Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div class="mb-3 mb-md-0">
                <h2 class="mb-1 text-gradient fw-bold">
                    <i class="fas fa-users me-3"></i>User Management
                </h2>
                <p class="text-muted mb-0">Manage all registered users and their permissions</p>
            </div>
            <div class="d-flex align-items-center gap-2">
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#importUsersModal">
                    <i class="fas fa-upload me-2"></i>Import
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportUsers('csv')">
                            <i class="fas fa-file-csv me-2"></i>Export CSV
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportUsers('excel')">
                            <i class="fas fa-file-excel me-2"></i>Export Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportUsers('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>Export PDF
                        </a></li>
                    </ul>
                </div>
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New User
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filter Section -->
<div class="filter-section mb-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0 fw-semibold">
            <i class="fas fa-filter me-2"></i>Advanced Filters
        </h6>
        <button class="btn btn-outline-secondary btn-sm" onclick="resetUserFilters()">
            <i class="fas fa-undo me-2"></i>Reset Filters
        </button>
    </div>
    <div class="filter-row">
        <div class="filter-group">
            <label class="form-label">Search</label>
            <input type="text" class="form-control" id="advancedSearch" placeholder="Search by name, email, or ID...">
        </div>
        <div class="filter-group">
            <label class="form-label">Role</label>
            <select class="form-select select2" id="roleFilterAdvanced">
                <option value="">All Roles</option>
                <option value="admin">Administrator</option>
                <option value="volunteer">Volunteer</option>
                <option value="donor">Donor</option>
                <option value="user">User</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="form-label">Status</label>
            <select class="form-select select2" id="statusFilterAdvanced">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="form-label">Registration Date</label>
            <select class="form-select select2" id="dateFilter">
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="form-label">&nbsp;</label>
            <button class="btn btn-primary w-100" onclick="applyAdvancedFilters()">
                <i class="fas fa-search me-2"></i>Apply Filters
            </button>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="stats-label">Total Users</div>
                    <div class="stats-number">{{ number_format($stats['total']) }}</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +{{ $stats['new_this_month'] }} this month
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-color);">
                    <i class="fas fa-users fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="stats-label">Active Users</div>
                    <div class="stats-number">{{ number_format($stats['active']) }}</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        {{ number_format(($stats['active'] / $stats['total']) * 100, 1) }}%
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                    <i class="fas fa-user-check fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="stats-label">Volunteers</div>
                    <div class="stats-number">{{ number_format($stats['volunteers']) }}</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +{{ $stats['new_volunteers'] }} new
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                    <i class="fas fa-hands-helping fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="stats-label">Administrators</div>
                    <div class="stats-number">{{ number_format($stats['admins']) }}</div>
                    <div class="stats-change neutral">
                        <i class="fas fa-minus me-1"></i>
                        No change
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(239, 68, 68, 0.1); color: var(--danger-color);">
                    <i class="fas fa-user-shield fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Card -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    All Users
                </h5>
            </div>
            <div class="col-auto">
                <div class="d-flex gap-3 align-items-center">
                    <!-- Search -->
                    <div class="input-group" style="width: 300px;">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 ps-0" 
                               placeholder="Search users..." 
                               id="searchUsersTable">
                    </div>
                    
                    <!-- Role Filter -->
                    <select class="form-select" id="roleFilter" style="width: 150px;">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="volunteer">Volunteer</option>
                        <option value="donor">Donor</option>
                        <option value="user">User</option>
                    </select>
                    
                    <!-- Status Filter -->
                    <select class="form-select" id="statusFilter" style="width: 150px;">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th>User</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Last Active</th>
                        <th style="width: 120px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="selected_users[]" value="{{ $user->id }}">
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3" style="width: 40px; height: 40px; font-size: 0.875rem;">
                                    {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ $user->first_name }} {{ $user->last_name }}</div>
                                    <small class="text-muted">ID: #{{ str_pad($user->id, 4, '0', STR_PAD_LEFT) }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="text-primary">{{ $user->email }}</span>
                            @if($user->email_verified_at)
                                <i class="fas fa-check-circle text-success ms-1" data-bs-toggle="tooltip" title="Email Verified"></i>
                            @else
                                <i class="fas fa-exclamation-circle text-warning ms-1" data-bs-toggle="tooltip" title="Email Not Verified"></i>
                            @endif
                        </td>
                        <td>
                            <span class="badge 
                                @if($user->role === 'admin') bg-danger-subtle text-danger
                                @elseif($user->role === 'volunteer') bg-warning-subtle text-warning
                                @elseif($user->role === 'donor') bg-success-subtle text-success
                                @else bg-secondary-subtle text-secondary
                                @endif">
                                <i class="fas 
                                    @if($user->role === 'admin') fa-user-shield
                                    @elseif($user->role === 'volunteer') fa-hands-helping
                                    @elseif($user->role === 'donor') fa-heart
                                    @else fa-user
                                    @endif me-1"></i>
                                {{ ucfirst($user->role) }}
                            </span>
                        </td>
                        <td>
                            <span class="badge 
                                @if($user->status === 'active') bg-success-subtle text-success
                                @elseif($user->status === 'inactive') bg-secondary-subtle text-secondary
                                @else bg-danger-subtle text-danger
                                @endif">
                                <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                                {{ ucfirst($user->status) }}
                            </span>
                        </td>
                        <td>
                            <span data-bs-toggle="tooltip" title="{{ $user->created_at->format('M d, Y h:i A') }}">
                                {{ $user->created_at->diffForHumans() }}
                            </span>
                        </td>
                        <td>
                            @if($user->last_login_at)
                                <span class="text-success" data-bs-toggle="tooltip" title="{{ $user->last_login_at->format('M d, Y h:i A') }}">
                                    {{ $user->last_login_at->diffForHumans() }}
                                </span>
                            @else
                                <span class="text-muted">Never</span>
                            @endif
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.users.show', $user->id) }}">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.users.edit', $user->id) }}">
                                            <i class="fas fa-edit me-2"></i>Edit User
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    @if($user->status === 'active')
                                    <li>
                                        <a class="dropdown-item text-warning" href="#" onclick="toggleUserStatus({{ $user->id }}, 'inactive')">
                                            <i class="fas fa-pause me-2"></i>Deactivate
                                        </a>
                                    </li>
                                    @else
                                    <li>
                                        <a class="dropdown-item text-success" href="#" onclick="toggleUserStatus({{ $user->id }}, 'active')">
                                            <i class="fas fa-play me-2"></i>Activate
                                        </a>
                                    </li>
                                    @endif
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" 
                                           onclick="deleteUser({{ $user->id }}, '{{ $user->first_name }} {{ $user->last_name }}')">
                                            <i class="fas fa-trash me-2"></i>Delete User
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h5>No Users Found</h5>
                                <p>No users match your current filters.</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($users->hasPages())
    <div class="card-footer bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Showing {{ $users->firstItem() }} to {{ $users->lastItem() }} of {{ $users->total() }} users
            </div>
            {{ $users->links() }}
        </div>
    </div>
    @endif
</div>

<!-- Bulk Actions Bar -->
<div id="bulkActionsBar" class="position-fixed bottom-0 start-50 translate-middle-x bg-dark text-white rounded-top px-4 py-3 shadow-lg" style="display: none; z-index: 1050;">
    <div class="d-flex align-items-center gap-3">
        <span id="selectedCount">0</span> users selected
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-light" onclick="bulkAction('activate')">
                <i class="fas fa-check me-1"></i>Activate
            </button>
            <button class="btn btn-outline-light" onclick="bulkAction('deactivate')">
                <i class="fas fa-pause me-1"></i>Deactivate
            </button>
            <button class="btn btn-outline-danger" onclick="bulkAction('delete')">
                <i class="fas fa-trash me-1"></i>Delete
            </button>
        </div>
        <button class="btn btn-sm btn-outline-light" onclick="clearSelection()">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<!-- Import Users Modal -->
<div class="modal fade" id="importUsersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Import Users
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importUsersForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label class="form-label">CSV File</label>
                        <input type="file" class="form-control" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            Upload a CSV file with columns: first_name, last_name, email, role, status
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Sample Format:</strong><br>
                        first_name,last_name,email,role,status<br>
                        John,Doe,<EMAIL>,user,active
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitImport()">
                    <i class="fas fa-upload me-2"></i>Import Users
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search functionality
    initializeSearch('usersTable');
    
    // Initialize filter functionality
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    
    [roleFilter, statusFilter].forEach(filter => {
        filter.addEventListener('change', function() {
            filterTable();
        });
    });
    
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('input[name="selected_users[]"]');
    
    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsBar();
    });
    
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionsBar();
            
            // Update select all checkbox state
            const checkedCount = document.querySelectorAll('input[name="selected_users[]"]:checked').length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
            selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
        });
    });
});

function filterTable() {
    const roleFilter = document.getElementById('roleFilter').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
    const rows = document.querySelectorAll('#usersTable tbody tr');
    
    rows.forEach(row => {
        const roleText = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
        const statusText = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
        
        const roleMatch = !roleFilter || roleText.includes(roleFilter);
        const statusMatch = !statusFilter || statusText.includes(statusFilter);
        
        row.style.display = (roleMatch && statusMatch) ? '' : 'none';
    });
}

function updateBulkActionsBar() {
    const checkedBoxes = document.querySelectorAll('input[name="selected_users[]"]:checked');
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    const selectedCount = document.getElementById('selectedCount');
    
    selectedCount.textContent = checkedBoxes.length;
    bulkActionsBar.style.display = checkedBoxes.length > 0 ? 'block' : 'none';
}

function clearSelection() {
    document.querySelectorAll('input[name="selected_users[]"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    document.getElementById('selectAll').indeterminate = false;
    updateBulkActionsBar();
}

function toggleUserStatus(userId, status) {
    if (confirm(`Are you sure you want to ${status} this user?`)) {
        // Make API call to update user status
        fetch(`/admin/users/${userId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`User ${status}d successfully!`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Failed to update user status', 'error');
            }
        })
        .catch(error => {
            showNotification('An error occurred', 'error');
        });
    }
}

function deleteUser(userId, userName) {
    if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
        fetch(`/admin/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('User deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Failed to delete user', 'error');
            }
        })
        .catch(error => {
            showNotification('An error occurred', 'error');
        });
    }
}

function bulkAction(action) {
    const selectedUsers = Array.from(document.querySelectorAll('input[name="selected_users[]"]:checked'))
                               .map(cb => cb.value);
    
    if (selectedUsers.length === 0) {
        showNotification('Please select users first', 'warning');
        return;
    }
    
    let confirmMessage;
    switch(action) {
        case 'activate':
            confirmMessage = `Activate ${selectedUsers.length} selected users?`;
            break;
        case 'deactivate':
            confirmMessage = `Deactivate ${selectedUsers.length} selected users?`;
            break;
        case 'delete':
            confirmMessage = `Delete ${selectedUsers.length} selected users? This action cannot be undone.`;
            break;
    }
    
    if (confirm(confirmMessage)) {
        fetch('/admin/users/bulk-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                action: action,
                user_ids: selectedUsers
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Bulk ${action} completed successfully!`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(`Failed to perform bulk ${action}`, 'error');
            }
        })
        .catch(error => {
            showNotification('An error occurred', 'error');
        });
    }
}

function submitImport() {
    const form = document.getElementById('importUsersForm');
    const formData = new FormData(form);
    
    fetch('/admin/users/import', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Users imported successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('importUsersModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(data.message || 'Failed to import users', 'error');
        }
    })
    .catch(error => {
        showNotification('An error occurred during import', 'error');
    });
}

// Enhanced export function
function exportUsers(format) {
    showNotification(`Preparing ${format.toUpperCase()} export...`, 'info');

    const filters = {
        role: document.getElementById('roleFilterAdvanced')?.value || '',
        status: document.getElementById('statusFilterAdvanced')?.value || '',
        search: document.getElementById('advancedSearch')?.value || '',
        date_filter: document.getElementById('dateFilter')?.value || ''
    };

    // Build query string
    const queryParams = new URLSearchParams(filters);
    queryParams.append('export', format);

    // Create download link
    const link = document.createElement('a');
    link.href = `{{ route('admin.users.index') }}?${queryParams.toString()}`;
    link.download = `users-${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        showNotification(`Users exported as ${format.toUpperCase()}!`, 'success');
    }, 1000);
}

// Enhanced filter functions
function resetUserFilters() {
    document.getElementById('advancedSearch').value = '';
    document.getElementById('roleFilterAdvanced').value = '';
    document.getElementById('statusFilterAdvanced').value = '';
    document.getElementById('dateFilter').value = '';

    // Reset legacy filters too
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchUsersTable').value = '';

    // Trigger Select2 update if available
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').trigger('change');
    }

    showNotification('Filters reset to default', 'info');
    applyAdvancedFilters();
}

function applyAdvancedFilters() {
    const search = document.getElementById('advancedSearch').value.toLowerCase();
    const role = document.getElementById('roleFilterAdvanced').value.toLowerCase();
    const status = document.getElementById('statusFilterAdvanced').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;

    // Also update legacy filters
    document.getElementById('roleFilter').value = role;
    document.getElementById('statusFilter').value = status;
    document.getElementById('searchUsersTable').value = search;

    const rows = document.querySelectorAll('#usersTable tbody tr');
    let visibleCount = 0;

    rows.forEach(row => {
        if (row.querySelector('td[colspan]')) return; // Skip "no data" row

        const userText = row.textContent.toLowerCase();
        const roleText = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
        const statusText = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
        const dateText = row.querySelector('td:nth-child(6)').textContent;

        const searchMatch = !search || userText.includes(search);
        const roleMatch = !role || roleText.includes(role);
        const statusMatch = !status || statusText.includes(status);
        const dateMatch = !dateFilter || checkDateFilter(dateText, dateFilter);

        const shouldShow = searchMatch && roleMatch && statusMatch && dateMatch;
        row.style.display = shouldShow ? '' : 'none';

        if (shouldShow) visibleCount++;
    });

    // Update results count
    updateResultsCount(visibleCount);

    showNotification(`Filters applied. Showing ${visibleCount} users.`, 'info');
}

function checkDateFilter(dateText, filter) {
    const now = new Date();
    const rowDate = new Date(dateText);

    switch(filter) {
        case 'today':
            return rowDate.toDateString() === now.toDateString();
        case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            return rowDate >= weekAgo;
        case 'month':
            return rowDate.getMonth() === now.getMonth() && rowDate.getFullYear() === now.getFullYear();
        case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3);
            const rowQuarter = Math.floor(rowDate.getMonth() / 3);
            return rowQuarter === quarter && rowDate.getFullYear() === now.getFullYear();
        case 'year':
            return rowDate.getFullYear() === now.getFullYear();
        default:
            return true;
    }
}

function updateResultsCount(count) {
    // Update or create results counter
    let counter = document.getElementById('resultsCounter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'resultsCounter';
        counter.className = 'text-muted small mt-2';
        document.querySelector('.filter-section').appendChild(counter);
    }
    counter.textContent = `Showing ${count} users`;
}

// Initialize enhanced features on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 dropdowns
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }

    // Auto-apply filters on change
    ['roleFilterAdvanced', 'statusFilterAdvanced', 'dateFilter'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', applyAdvancedFilters);
        }
    });

    // Real-time search
    const searchInput = document.getElementById('advancedSearch');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(applyAdvancedFilters, 300);
        });
    }

    // Initialize DataTable if available
    if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
        const table = initDataTable('#usersTable', {
            columnDefs: [
                { orderable: false, targets: [0, 7] }, // Disable sorting for checkbox and actions columns
                { searchable: false, targets: [0, 7] }
            ],
            order: [[5, 'desc']], // Sort by join date descending
            pageLength: 25,
            drawCallback: function() {
                // Re-initialize bulk actions after table redraw
                initBulkActions();
            }
        });

        // Hide default search and use our custom one
        $('.dataTables_filter').hide();
        $('.dataTables_length').hide(); // Hide default length selector

        // Connect our search to DataTable
        $('#advancedSearch').on('input', function() {
            table.search(this.value).draw();
        });

        // Custom filtering for role and status
        $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
            const role = $('#roleFilterAdvanced').val().toLowerCase();
            const status = $('#statusFilterAdvanced').val().toLowerCase();
            const dateFilter = $('#dateFilter').val();

            const rowRole = data[3].toLowerCase(); // Role column
            const rowStatus = data[4].toLowerCase(); // Status column
            const rowDate = data[5]; // Date column

            const roleMatch = !role || rowRole.includes(role);
            const statusMatch = !status || rowStatus.includes(status);
            const dateMatch = !dateFilter || checkDateFilter(rowDate, dateFilter);

            return roleMatch && statusMatch && dateMatch;
        });

        // Trigger filter when dropdowns change
        $('#roleFilterAdvanced, #statusFilterAdvanced, #dateFilter').on('change', function() {
            table.draw();
        });
    }
});
</script>
@endsection