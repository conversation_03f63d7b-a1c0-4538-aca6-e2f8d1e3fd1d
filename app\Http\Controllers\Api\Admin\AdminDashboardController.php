<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Volunteer;
use App\Models\BlogPost;
use App\Models\Program;
use App\Models\Event;
use App\Models\Donation;
use App\Models\DonationCampaign;
use App\Models\ContactMessage;
use App\Models\ScholarshipApplication;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * @OA\Tag(
 *     name="Admin Dashboard",
 *     description="Admin dashboard and statistics endpoints"
 * )
 */
class AdminDashboardController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/dashboard",
     *     summary="Get admin dashboard overview",
     *     tags={"Admin Dashboard"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Dashboard data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="dashboard", type="object",
     *                 @OA\Property(property="overview", type="object"),
     *                 @OA\Property(property="recent_activities", type="array"),
     *                 @OA\Property(property="upcoming_events", type="array"),
     *                 @OA\Property(property="pending_items", type="object")
     *             ),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied - Admin role required"
     *     )
     * )
     */
    public function index()
    {
        $overview = [
            'total_users' => User::count(),
            'new_users_this_month' => User::whereMonth('created_at', Carbon::now()->month)
                                         ->whereYear('created_at', Carbon::now()->year)
                                         ->count(),
            'active_volunteers' => Volunteer::where('application_status', 'approved')->count(),
            'pending_volunteers' => Volunteer::where('application_status', 'pending')->count(),
            'total_donations' => Donation::where('payment_status', 'completed')->sum('amount'),
            'donations_this_month' => Donation::where('payment_status', 'completed')
                                             ->whereMonth('created_at', Carbon::now()->month)
                                             ->whereYear('created_at', Carbon::now()->year)
                                             ->sum('amount'),
            'active_programs' => Program::where('status', 'active')->count(),
            'upcoming_events' => Event::where('start_datetime', '>', Carbon::now())->count(),
            'published_blog_posts' => BlogPost::where('status', 'published')->count(),
            'pending_blog_posts' => BlogPost::where('status', 'draft')->count(),
            'unread_messages' => ContactMessage::where('status', 'unread')->count(),
            'newsletter_subscribers' => NewsletterSubscriber::where('status', 'subscribed')->count(),
            'scholarship_applications' => ScholarshipApplication::where('status', 'pending')->count()
        ];

        $recentActivities = $this->getRecentActivities();
        $upcomingEvents = $this->getUpcomingEvents();
        $pendingItems = $this->getPendingItems();

        return response()->json([
            'dashboard' => [
                'overview' => $overview,
                'recent_activities' => $recentActivities,
                'upcoming_events' => $upcomingEvents,
                'pending_items' => $pendingItems
            ],
            'success' => true
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/dashboard/stats",
     *     summary="Get detailed statistics",
     *     tags={"Admin Dashboard"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Time period for statistics (week, month, quarter, year)",
     *         required=false,
     *         @OA\Schema(type="string", default="month")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Statistics retrieved successfully"
     *     )
     * )
     */
    public function stats(Request $request)
    {
        $period = $request->get('period', 'month');
        
        $startDate = match($period) {
            'week' => Carbon::now()->startOfWeek(),
            'month' => Carbon::now()->startOfMonth(),
            'quarter' => Carbon::now()->startOfQuarter(),
            'year' => Carbon::now()->startOfYear(),
            default => Carbon::now()->startOfMonth()
        };

        $endDate = Carbon::now();

        $stats = [
            'users' => $this->getUserStats($startDate, $endDate),
            'donations' => $this->getDonationStats($startDate, $endDate),
            'volunteers' => $this->getVolunteerStats($startDate, $endDate),
            'events' => $this->getEventStats($startDate, $endDate),
            'blog' => $this->getBlogStats($startDate, $endDate),
            'programs' => $this->getProgramStats($startDate, $endDate)
        ];

        return response()->json([
            'stats' => $stats,
            'period' => $period,
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ],
            'success' => true
        ]);
    }

    private function getRecentActivities()
    {
        $activities = [];

        // Recent user registrations
        $recentUsers = User::orderBy('created_at', 'desc')
                          ->limit(5)
                          ->get(['id', 'first_name', 'last_name', 'email', 'created_at']);
        
        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_registration',
                'title' => 'New user registered',
                'description' => "{$user->full_name} joined the platform",
                'user' => $user->full_name,
                'timestamp' => $user->created_at->format('Y-m-d H:i:s'),
                'icon' => 'user-plus'
            ];
        }

        // Recent donations
        $recentDonations = Donation::with('user')
                                  ->where('payment_status', 'completed')
                                  ->orderBy('created_at', 'desc')
                                  ->limit(5)
                                  ->get();

        foreach ($recentDonations as $donation) {
            $activities[] = [
                'type' => 'donation',
                'title' => 'New donation received',
                'description' => "₦" . number_format($donation->amount, 2) . " donated" . ($donation->user ? " by {$donation->user->full_name}" : ''),
                'amount' => $donation->amount,
                'timestamp' => $donation->created_at->format('Y-m-d H:i:s'),
                'icon' => 'heart'
            ];
        }

        // Recent volunteer applications
        $recentVolunteers = Volunteer::with('user')
                                   ->orderBy('created_at', 'desc')
                                   ->limit(3)
                                   ->get();

        foreach ($recentVolunteers as $volunteer) {
            $activities[] = [
                'type' => 'volunteer_application',
                'title' => 'New volunteer application',
                'description' => "{$volunteer->user->full_name} applied to volunteer",
                'status' => $volunteer->application_status,
                'timestamp' => $volunteer->created_at->format('Y-m-d H:i:s'),
                'icon' => 'users'
            ];
        }

        // Sort by timestamp
        usort($activities, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        return array_slice($activities, 0, 10);
    }

    private function getUpcomingEvents()
    {
        return Event::where('start_datetime', '>', Carbon::now())
                   ->orderBy('start_datetime')
                   ->limit(5)
                   ->get(['id', 'title', 'start_datetime', 'venue', 'capacity'])
                   ->map(function($event) {
                       return [
                           'id' => $event->id,
                           'title' => $event->title,
                           'start_datetime' => $event->start_datetime->format('Y-m-d H:i'),
                           'venue' => $event->venue,
                           'capacity' => $event->capacity,
                           'registrations_count' => $event->registrations()->count()
                       ];
                   });
    }

    private function getPendingItems()
    {
        return [
            'volunteer_applications' => Volunteer::where('application_status', 'pending')->count(),
            'blog_comments' => \DB::table('blog_comments')->where('status', 'pending')->count(),
            'contact_messages' => ContactMessage::where('status', 'unread')->count(),
            'scholarship_applications' => ScholarshipApplication::where('status', 'pending')->count(),
            'blog_posts_draft' => BlogPost::where('status', 'draft')->count()
        ];
    }

    private function getUserStats($startDate, $endDate)
    {
        $totalUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeUsers = User::whereBetween('last_login_at', [$startDate, $endDate])->count();
        
        // Daily user registrations
        $dailyRegistrations = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                 ->whereBetween('created_at', [$startDate, $endDate])
                                 ->groupBy('date')
                                 ->orderBy('date')
                                 ->get();

        return [
            'total_new_users' => $totalUsers,
            'active_users' => $activeUsers,
            'daily_registrations' => $dailyRegistrations,
            'by_role' => User::selectRaw('role, COUNT(*) as count')
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->groupBy('role')
                            ->get()
        ];
    }

    private function getDonationStats($startDate, $endDate)
    {
        $donations = Donation::where('payment_status', 'completed')
                            ->whereBetween('created_at', [$startDate, $endDate]);

        $totalAmount = $donations->sum('amount');
        $totalCount = $donations->count();
        $averageAmount = $totalCount > 0 ? $totalAmount / $totalCount : 0;

        // Daily donations
        $dailyDonations = Donation::selectRaw('DATE(created_at) as date, SUM(amount) as total_amount, COUNT(*) as count')
                                 ->where('payment_status', 'completed')
                                 ->whereBetween('created_at', [$startDate, $endDate])
                                 ->groupBy('date')
                                 ->orderBy('date')
                                 ->get();

        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'average_amount' => $averageAmount,
            'daily_donations' => $dailyDonations,
            'by_type' => Donation::selectRaw('donation_type, SUM(amount) as total_amount, COUNT(*) as count')
                                ->where('payment_status', 'completed')
                                ->whereBetween('created_at', [$startDate, $endDate])
                                ->groupBy('donation_type')
                                ->get()
        ];
    }

    private function getVolunteerStats($startDate, $endDate)
    {
        return [
            'new_applications' => Volunteer::whereBetween('created_at', [$startDate, $endDate])->count(),
            'approved_applications' => Volunteer::where('application_status', 'approved')
                                               ->whereBetween('updated_at', [$startDate, $endDate])
                                               ->count(),
            'total_hours_logged' => Volunteer::whereBetween('updated_at', [$startDate, $endDate])
                                            ->sum('hours_logged'),
            'by_status' => Volunteer::selectRaw('application_status, COUNT(*) as count')
                                   ->whereBetween('created_at', [$startDate, $endDate])
                                   ->groupBy('application_status')
                                   ->get()
        ];
    }

    private function getEventStats($startDate, $endDate)
    {
        return [
            'events_created' => Event::whereBetween('created_at', [$startDate, $endDate])->count(),
            'events_completed' => Event::where('end_datetime', '<', Carbon::now())
                                       ->whereBetween('start_datetime', [$startDate, $endDate])
                                       ->count(),
            'total_registrations' => \DB::table('event_registrations')
                                       ->whereBetween('created_at', [$startDate, $endDate])
                                       ->count(),
            'by_type' => Event::selectRaw('event_type, COUNT(*) as count')
                             ->whereBetween('created_at', [$startDate, $endDate])
                             ->groupBy('event_type')
                             ->get()
        ];
    }

    private function getBlogStats($startDate, $endDate)
    {
        return [
            'posts_published' => BlogPost::where('status', 'published')
                                        ->whereBetween('published_at', [$startDate, $endDate])
                                        ->count(),
            'total_views' => BlogPost::whereBetween('updated_at', [$startDate, $endDate])
                                    ->sum('views_count'),
            'comments_posted' => \DB::table('blog_comments')
                                   ->whereBetween('created_at', [$startDate, $endDate])
                                   ->count(),
            'top_posts' => BlogPost::selectRaw('title, views_count, slug')
                                  ->where('status', 'published')
                                  ->whereBetween('published_at', [$startDate, $endDate])
                                  ->orderBy('views_count', 'desc')
                                  ->limit(5)
                                  ->get()
        ];
    }

    private function getProgramStats($startDate, $endDate)
    {
        return [
            'programs_created' => Program::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_programs' => Program::where('status', 'active')
                                       ->whereBetween('created_at', [$startDate, $endDate])
                                       ->count(),
            'total_budget' => Program::whereBetween('created_at', [$startDate, $endDate])
                                    ->sum('budget'),
            'by_status' => Program::selectRaw('status, COUNT(*) as count')
                                 ->whereBetween('created_at', [$startDate, $endDate])
                                 ->groupBy('status')
                                 ->get()
        ];
    }

    /**
     * Get comprehensive analytics data for admin dashboard
     */
    public function analytics(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = Carbon::now()->subDays($days);

        // User growth analytics with monthly breakdown
        $userGrowth = User::selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->where('created_at', '>=', $startDate)
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return [
                    'month' => $monthNames[$item->month - 1],
                    'count' => $item->count
                ];
            });

        // Donation trends with monthly breakdown
        $donationTrends = Donation::selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(amount) as amount')
            ->where('payment_status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return [
                    'month' => $monthNames[$item->month - 1],
                    'amount' => (float) $item->amount
                ];
            });

        // Volunteer engagement with hours tracking
        $volunteerEngagement = Volunteer::selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(hours_logged) as hours')
            ->where('application_status', 'approved')
            ->where('created_at', '>=', $startDate)
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return [
                    'month' => $monthNames[$item->month - 1],
                    'hours' => (float) $item->hours ?? 0
                ];
            });

        // Program performance analytics
        $programPerformance = Program::select('title', 'status', 'budget')
            ->where('status', '!=', 'planning')
            ->get()
            ->map(function ($program) {
                // Calculate completion rate based on status
                $completionRate = match($program->status) {
                    'completed' => 100,
                    'active' => rand(60, 90), // Mock data - replace with actual progress tracking
                    'suspended' => rand(20, 50),
                    default => 0
                };

                return [
                    'program' => $program->title,
                    'completion_rate' => $completionRate
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'user_growth' => $userGrowth,
                'donation_trends' => $donationTrends,
                'volunteer_engagement' => $volunteerEngagement,
                'program_performance' => $programPerformance,
                'summary' => [
                    'total_users' => User::count(),
                    'growth_rate' => $this->calculateGrowthRate('users', $days),
                    'donation_growth_rate' => $this->calculateGrowthRate('donations', $days),
                    'volunteer_growth_rate' => $this->calculateGrowthRate('volunteers', $days),
                ]
            ]
        ]);
    }

    /**
     * Calculate growth rate for different metrics
     */
    private function calculateGrowthRate(string $type, int $days): float
    {
        $currentPeriod = Carbon::now()->subDays($days);
        $previousPeriod = Carbon::now()->subDays($days * 2);

        switch ($type) {
            case 'users':
                $current = User::where('created_at', '>=', $currentPeriod)->count();
                $previous = User::where('created_at', '>=', $previousPeriod)
                               ->where('created_at', '<', $currentPeriod)->count();
                break;
            case 'donations':
                $current = Donation::where('payment_status', 'completed')
                                  ->where('created_at', '>=', $currentPeriod)->sum('amount');
                $previous = Donation::where('payment_status', 'completed')
                                   ->where('created_at', '>=', $previousPeriod)
                                   ->where('created_at', '<', $currentPeriod)->sum('amount');
                break;
            case 'volunteers':
                $current = Volunteer::where('application_status', 'approved')
                                   ->where('created_at', '>=', $currentPeriod)->count();
                $previous = Volunteer::where('application_status', 'approved')
                                    ->where('created_at', '>=', $previousPeriod)
                                    ->where('created_at', '<', $currentPeriod)->count();
                break;
            default:
                return 0;
        }

        if ($previous == 0) return $current > 0 ? 100 : 0;

        return round((($current - $previous) / $previous) * 100, 1);
    }
}