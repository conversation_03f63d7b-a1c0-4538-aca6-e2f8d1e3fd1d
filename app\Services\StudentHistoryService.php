<?php

namespace App\Services;

use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\ScholarshipApplication;
use App\Models\PartnerOrganization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class StudentHistoryService
{
    /**
     * Get comprehensive student history
     */
    public function getStudentHistory(Student $student, array $options = []): array
    {
        try {
            $includeProgressions = $options['include_progressions'] ?? true;
            $includeApplications = $options['include_applications'] ?? true;
            $includeAchievements = $options['include_achievements'] ?? true;
            $academicYear = $options['academic_year'] ?? null;
            $limit = $options['limit'] ?? null;

            $history = [
                'student' => $student,
                'summary' => $this->getStudentSummary($student),
                'timeline' => [],
            ];

            // Get progressions
            if ($includeProgressions) {
                $progressions = $this->getStudentProgressions($student, $academicYear, $limit);
                $history['progressions'] = $progressions;
                
                // Add to timeline
                foreach ($progressions as $progression) {
                    $history['timeline'][] = [
                        'type' => 'progression',
                        'date' => $progression->progression_date,
                        'academic_year' => $progression->academic_year,
                        'title' => "Grade Progression: {$progression->from_grade} → {$progression->to_grade}",
                        'status' => $progression->status,
                        'data' => $progression,
                    ];
                }
            }

            // Get scholarship applications
            if ($includeApplications) {
                $applications = $this->getStudentApplications($student, $academicYear, $limit);
                $history['applications'] = $applications;
                
                // Add to timeline
                foreach ($applications as $application) {
                    $history['timeline'][] = [
                        'type' => 'application',
                        'date' => $application->created_at,
                        'academic_year' => $this->getAcademicYearFromDate($application->created_at),
                        'title' => "Scholarship Application: {$application->scholarship->title}",
                        'status' => $application->status,
                        'data' => $application,
                    ];
                }
            }

            // Get achievements (awards, completions, etc.)
            if ($includeAchievements) {
                $achievements = $this->getStudentAchievements($student, $academicYear, $limit);
                $history['achievements'] = $achievements;
                
                // Add to timeline
                foreach ($achievements as $achievement) {
                    $history['timeline'][] = [
                        'type' => 'achievement',
                        'date' => $achievement['date'],
                        'academic_year' => $achievement['academic_year'],
                        'title' => $achievement['title'],
                        'status' => $achievement['status'],
                        'data' => $achievement,
                    ];
                }
            }

            // Sort timeline by date (most recent first)
            usort($history['timeline'], function ($a, $b) {
                return Carbon::parse($b['date'])->timestamp - Carbon::parse($a['date'])->timestamp;
            });

            return $history;

        } catch (Exception $e) {
            Log::error("Failed to get student history", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get student summary statistics
     */
    public function getStudentSummary(Student $student): array
    {
        try {
            $progressions = StudentProgression::where('student_id', $student->id)->get();
            $applications = ScholarshipApplication::where('student_id', $student->id)->get();

            $summary = [
                'total_progressions' => $progressions->count(),
                'completed_progressions' => $progressions->where('status', 'completed')->count(),
                'pending_progressions' => $progressions->where('status', 'pending')->count(),
                'total_applications' => $applications->count(),
                'approved_applications' => $applications->where('status', 'approved')->count(),
                'pending_applications' => $applications->where('status', 'pending')->count(),
                'rejected_applications' => $applications->where('status', 'rejected')->count(),
                'current_grade' => $student->grade_level,
                'enrollment_date' => $student->created_at,
                'years_enrolled' => $this->calculateYearsEnrolled($student),
                'academic_years_active' => $this->getAcademicYearsActive($student),
            ];

            // Calculate success rates
            $summary['progression_success_rate'] = $progressions->count() > 0 
                ? round(($summary['completed_progressions'] / $summary['total_progressions']) * 100, 1) 
                : 0;

            $summary['application_success_rate'] = $applications->count() > 0 
                ? round(($summary['approved_applications'] / $summary['total_applications']) * 100, 1) 
                : 0;

            return $summary;

        } catch (Exception $e) {
            Log::error("Failed to get student summary", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get student progressions with optional filtering
     */
    public function getStudentProgressions(Student $student, string $academicYear = null, int $limit = null)
    {
        $query = StudentProgression::where('student_id', $student->id)
            ->orderBy('progression_date', 'desc');

        if ($academicYear) {
            $query->where('academic_year', $academicYear);
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get student scholarship applications with optional filtering
     */
    public function getStudentApplications(Student $student, string $academicYear = null, int $limit = null)
    {
        $query = ScholarshipApplication::with(['scholarship'])
            ->where('student_id', $student->id)
            ->orderBy('created_at', 'desc');

        if ($academicYear) {
            $yearStart = (int) substr($academicYear, 0, 4);
            $query->whereYear('created_at', '>=', $yearStart)
                  ->whereYear('created_at', '<=', $yearStart + 1);
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get student achievements (awards, completions, etc.)
     */
    public function getStudentAchievements(Student $student, string $academicYear = null, int $limit = null): array
    {
        $achievements = [];

        // Get approved scholarship applications as achievements
        $approvedApplications = ScholarshipApplication::with(['scholarship'])
            ->where('student_id', $student->id)
            ->where('status', 'approved')
            ->orderBy('updated_at', 'desc');

        if ($academicYear) {
            $yearStart = (int) substr($academicYear, 0, 4);
            $approvedApplications->whereYear('updated_at', '>=', $yearStart)
                               ->whereYear('updated_at', '<=', $yearStart + 1);
        }

        if ($limit) {
            $approvedApplications->limit($limit);
        }

        foreach ($approvedApplications->get() as $application) {
            $achievements[] = [
                'type' => 'scholarship_award',
                'title' => "Scholarship Awarded: {$application->scholarship->title}",
                'description' => "Received scholarship worth ₦" . number_format($application->scholarship->amount),
                'date' => $application->updated_at,
                'academic_year' => $this->getAcademicYearFromDate($application->updated_at),
                'status' => 'completed',
                'category' => $application->category,
                'amount' => $application->scholarship->amount,
            ];
        }

        // Get completed progressions as achievements
        $completedProgressions = StudentProgression::where('student_id', $student->id)
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc');

        if ($academicYear) {
            $completedProgressions->where('academic_year', $academicYear);
        }

        foreach ($completedProgressions->get() as $progression) {
            $achievements[] = [
                'type' => 'grade_completion',
                'title' => "Grade Completed: " . $this->formatGrade($progression->from_grade),
                'description' => "Successfully advanced from {$this->formatGrade($progression->from_grade)} to {$this->formatGrade($progression->to_grade)}",
                'date' => $progression->completed_at,
                'academic_year' => $progression->academic_year,
                'status' => 'completed',
                'from_grade' => $progression->from_grade,
                'to_grade' => $progression->to_grade,
            ];
        }

        // Sort achievements by date (most recent first)
        usort($achievements, function ($a, $b) {
            return Carbon::parse($b['date'])->timestamp - Carbon::parse($a['date'])->timestamp;
        });

        return $limit ? array_slice($achievements, 0, $limit) : $achievements;
    }

    /**
     * Get historical data for multiple students (for partner organizations)
     */
    public function getOrganizationStudentHistory(PartnerOrganization $partnerOrg, array $options = []): array
    {
        try {
            $academicYear = $options['academic_year'] ?? null;
            $limit = $options['limit'] ?? 50;

            $students = Student::where('school_id', $partnerOrg->id)->get();
            
            $organizationHistory = [
                'partner_organization' => $partnerOrg,
                'summary' => $this->getOrganizationSummary($partnerOrg, $academicYear),
                'students' => [],
                'recent_activities' => [],
            ];

            foreach ($students as $student) {
                $studentHistory = $this->getStudentHistory($student, [
                    'academic_year' => $academicYear,
                    'limit' => 10, // Limit per student for organization view
                ]);

                $organizationHistory['students'][] = $studentHistory;
                
                // Add recent activities to organization timeline
                foreach ($studentHistory['timeline'] as $activity) {
                    $activity['student'] = $student;
                    $organizationHistory['recent_activities'][] = $activity;
                }
            }

            // Sort recent activities by date
            usort($organizationHistory['recent_activities'], function ($a, $b) {
                return Carbon::parse($b['date'])->timestamp - Carbon::parse($a['date'])->timestamp;
            });

            // Limit recent activities
            $organizationHistory['recent_activities'] = array_slice(
                $organizationHistory['recent_activities'], 
                0, 
                $limit
            );

            return $organizationHistory;

        } catch (Exception $e) {
            Log::error("Failed to get organization student history", [
                'partner_org_id' => $partnerOrg->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get organization summary statistics
     */
    public function getOrganizationSummary(PartnerOrganization $partnerOrg, string $academicYear = null): array
    {
        try {
            $studentsQuery = Student::where('school_id', $partnerOrg->id);
            $totalStudents = $studentsQuery->count();

            $progressionsQuery = StudentProgression::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            });

            $applicationsQuery = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            });

            if ($academicYear) {
                $progressionsQuery->where('academic_year', $academicYear);
                
                $yearStart = (int) substr($academicYear, 0, 4);
                $applicationsQuery->whereYear('created_at', '>=', $yearStart)
                                ->whereYear('created_at', '<=', $yearStart + 1);
            }

            $progressions = $progressionsQuery->get();
            $applications = $applicationsQuery->get();

            return [
                'total_students' => $totalStudents,
                'total_progressions' => $progressions->count(),
                'completed_progressions' => $progressions->where('status', 'completed')->count(),
                'total_applications' => $applications->count(),
                'approved_applications' => $applications->where('status', 'approved')->count(),
                'total_scholarship_amount' => $this->calculateTotalScholarshipAmount($applications->where('status', 'approved')),
                'academic_year' => $academicYear ?? 'All Years',
                'most_active_grade' => $this->getMostActiveGrade($partnerOrg, $academicYear),
                'success_rate' => $applications->count() > 0 
                    ? round(($applications->where('status', 'approved')->count() / $applications->count()) * 100, 1) 
                    : 0,
            ];

        } catch (Exception $e) {
            Log::error("Failed to get organization summary", [
                'partner_org_id' => $partnerOrg->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Export student history to CSV
     */
    public function exportStudentHistory(Student $student, array $options = []): string
    {
        $history = $this->getStudentHistory($student, $options);
        
        $csvData = [];
        $csvData[] = ['Student History Export'];
        $csvData[] = ['Student Name', $student->full_name];
        $csvData[] = ['Current Grade', $student->grade_level];
        $csvData[] = ['School', $student->school->name ?? 'Unknown'];
        $csvData[] = ['Export Date', Carbon::now()->format('Y-m-d H:i:s')];
        $csvData[] = []; // Empty row

        // Summary
        $csvData[] = ['SUMMARY'];
        $csvData[] = ['Total Progressions', $history['summary']['total_progressions']];
        $csvData[] = ['Completed Progressions', $history['summary']['completed_progressions']];
        $csvData[] = ['Total Applications', $history['summary']['total_applications']];
        $csvData[] = ['Approved Applications', $history['summary']['approved_applications']];
        $csvData[] = ['Success Rate', $history['summary']['application_success_rate'] . '%'];
        $csvData[] = []; // Empty row

        // Timeline
        $csvData[] = ['TIMELINE'];
        $csvData[] = ['Date', 'Type', 'Title', 'Status', 'Academic Year'];
        
        foreach ($history['timeline'] as $item) {
            $csvData[] = [
                Carbon::parse($item['date'])->format('Y-m-d'),
                ucfirst($item['type']),
                $item['title'],
                ucfirst($item['status']),
                $item['academic_year'],
            ];
        }

        // Convert to CSV string
        $output = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csvString = stream_get_contents($output);
        fclose($output);

        return $csvString;
    }

    /**
     * Helper methods
     */
    private function calculateYearsEnrolled(Student $student): float
    {
        return Carbon::now()->diffInYears($student->created_at);
    }

    private function getAcademicYearsActive(Student $student): array
    {
        $progressions = StudentProgression::where('student_id', $student->id)
            ->distinct('academic_year')
            ->pluck('academic_year')
            ->toArray();

        $applications = ScholarshipApplication::where('student_id', $student->id)
            ->get()
            ->map(function ($app) {
                return $this->getAcademicYearFromDate($app->created_at);
            })
            ->unique()
            ->values()
            ->toArray();

        return array_unique(array_merge($progressions, $applications));
    }

    private function getAcademicYearFromDate(Carbon $date): string
    {
        $year = $date->year;
        return $date->month >= 9 ? $year . '/' . ($year + 1) : ($year - 1) . '/' . $year;
    }

    private function formatGrade(string $grade): string
    {
        $gradeMap = [
            'primary_1' => 'Primary 1', 'primary_2' => 'Primary 2', 'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4', 'primary_5' => 'Primary 5', 'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1', 'secondary_2' => 'Secondary 2', 'secondary_3' => 'Secondary 3',
            'secondary_4' => 'Secondary 4', 'secondary_5' => 'Secondary 5', 'secondary_6' => 'Secondary 6',
            'university' => 'University',
        ];

        return $gradeMap[$grade] ?? ucfirst(str_replace('_', ' ', $grade));
    }

    private function calculateTotalScholarshipAmount($approvedApplications): float
    {
        return $approvedApplications->sum(function ($application) {
            return $application->scholarship->amount ?? 0;
        });
    }

    private function getMostActiveGrade(PartnerOrganization $partnerOrg, string $academicYear = null): ?string
    {
        $query = Student::where('school_id', $partnerOrg->id)
            ->select('grade_level', DB::raw('COUNT(*) as count'))
            ->groupBy('grade_level')
            ->orderBy('count', 'desc');

        $result = $query->first();
        return $result ? $this->formatGrade($result->grade_level) : null;
    }
}
