<?php

namespace App\Services;

use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Services\ScholarshipEligibilityService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class StudentProgressionService
{
    protected $eligibilityService;

    public function __construct(ScholarshipEligibilityService $eligibilityService = null)
    {
        $this->eligibilityService = $eligibilityService ?? app(ScholarshipEligibilityService::class);
    }

    /**
     * Grade level mappings and progression rules
     */
    private const GRADE_LEVELS = [
        // Primary levels (1-6)
        'primary_1' => ['next' => 'primary_2', 'category' => 'primary', 'level' => 1],
        'primary_2' => ['next' => 'primary_3', 'category' => 'primary', 'level' => 2],
        'primary_3' => ['next' => 'primary_4', 'category' => 'primary', 'level' => 3],
        'primary_4' => ['next' => 'primary_5', 'category' => 'primary', 'level' => 4],
        'primary_5' => ['next' => 'primary_6', 'category' => 'primary', 'level' => 5],
        'primary_6' => ['next' => 'secondary_1', 'category' => 'secondary', 'level' => 6],
        
        // Secondary levels (7-12)
        'secondary_1' => ['next' => 'secondary_2', 'category' => 'secondary', 'level' => 7],
        'secondary_2' => ['next' => 'secondary_3', 'category' => 'secondary', 'level' => 8],
        'secondary_3' => ['next' => 'secondary_4', 'category' => 'secondary', 'level' => 9],
        'secondary_4' => ['next' => 'secondary_5', 'category' => 'secondary', 'level' => 10],
        'secondary_5' => ['next' => 'secondary_6', 'category' => 'secondary', 'level' => 11],
        'secondary_6' => ['next' => 'university', 'category' => 'university', 'level' => 12],
        
        // University (post-secondary)
        'university' => ['next' => null, 'category' => 'university', 'level' => 13],
    ];

    /**
     * Advance a student to the next grade level
     */
    public function advanceStudent(Student $student, array $options = []): StudentProgression
    {
        DB::beginTransaction();
        
        try {
            // Validate current grade level
            $currentGrade = $student->grade_level;
            if (!isset(self::GRADE_LEVELS[$currentGrade])) {
                throw new Exception("Invalid current grade level: {$currentGrade}");
            }

            $gradeInfo = self::GRADE_LEVELS[$currentGrade];
            $nextGrade = $gradeInfo['next'];

            // Check if student can be advanced
            if (!$nextGrade) {
                throw new Exception("Student is already at the highest level (University)");
            }

            // Get current academic year
            $academicYear = $options['academic_year'] ?? $this->getCurrentAcademicYear();

            // Check if student has already been advanced this academic year
            $existingProgression = StudentProgression::where('student_id', $student->id)
                ->where('academic_year', $academicYear)
                ->where('status', '!=', 'rejected')
                ->first();

            if ($existingProgression) {
                throw new Exception("Student has already been processed for progression in academic year {$academicYear}");
            }

            // Create progression record
            $progression = StudentProgression::create([
                'student_id' => $student->id,
                'from_grade' => $currentGrade,
                'to_grade' => $nextGrade,
                'academic_year' => $academicYear,
                'status' => $options['auto_approve'] ?? false ? 'completed' : 'pending',
                'progression_date' => $options['progression_date'] ?? Carbon::now(),
                'notes' => $options['notes'] ?? null,
                'created_by' => $options['created_by'] ?? null,
            ]);

            // If auto-approved, update student's grade level immediately
            if ($progression->status === 'completed') {
                $this->completeProgression($progression);
            } else {
                // Send pending progression notification
                try {
                    $notificationService = app(\App\Services\ProgressionNotificationService::class);
                    $notificationService->sendProgressionPendingNotification($progression);
                } catch (Exception $notificationError) {
                    Log::warning("Failed to send progression pending notification", [
                        'progression_id' => $progression->id,
                        'error' => $notificationError->getMessage()
                    ]);
                }
            }

            DB::commit();

            Log::info("Student progression created", [
                'student_id' => $student->id,
                'from_grade' => $currentGrade,
                'to_grade' => $nextGrade,
                'progression_id' => $progression->id
            ]);

            return $progression;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Failed to advance student", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Complete a progression (approve and update student grade)
     */
    public function completeProgression(StudentProgression $progression): bool
    {
        DB::beginTransaction();
        
        try {
            // Update progression status
            $progression->update([
                'status' => 'completed',
                'completed_at' => Carbon::now(),
            ]);

            // Update student's grade level
            $student = $progression->student;
            $student->update([
                'grade_level' => $progression->to_grade,
                'updated_at' => Carbon::now(),
            ]);

            // Check for automatic scholarship eligibility
            $this->checkScholarshipEligibility($student, $progression);

            DB::commit();

            // Send progression completed notification
            try {
                $notificationService = app(\App\Services\ProgressionNotificationService::class);
                $notificationService->sendProgressionCompletedNotification($progression);
            } catch (Exception $notificationError) {
                Log::warning("Failed to send progression completed notification", [
                    'progression_id' => $progression->id,
                    'error' => $notificationError->getMessage()
                ]);
            }

            Log::info("Student progression completed", [
                'progression_id' => $progression->id,
                'student_id' => $student->id,
                'new_grade' => $progression->to_grade
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Failed to complete progression", [
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Reject a progression
     */
    public function rejectProgression(StudentProgression $progression, string $reason = null): bool
    {
        try {
            $progression->update([
                'status' => 'rejected',
                'notes' => $reason ? ($progression->notes ? $progression->notes . "\n\nRejection reason: " . $reason : "Rejection reason: " . $reason) : $progression->notes,
                'updated_at' => Carbon::now(),
            ]);

            Log::info("Student progression rejected", [
                'progression_id' => $progression->id,
                'student_id' => $progression->student_id,
                'reason' => $reason
            ]);

            return true;

        } catch (Exception $e) {
            Log::error("Failed to reject progression", [
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get students eligible for progression
     */
    public function getEligibleStudents(PartnerOrganization $partnerOrg, string $academicYear = null): array
    {
        $academicYear = $academicYear ?? $this->getCurrentAcademicYear();

        // Get all students who haven't been processed for progression this academic year
        $students = Student::where('school_id', $partnerOrg->id)
            ->whereNotIn('id', function ($query) use ($academicYear) {
                $query->select('student_id')
                    ->from('student_progressions')
                    ->where('academic_year', $academicYear)
                    ->where('status', '!=', 'rejected');
            })
            ->where('grade_level', '!=', 'university') // University students don't progress through our system
            ->get();

        return $students->map(function ($student) {
            $gradeInfo = self::GRADE_LEVELS[$student->grade_level] ?? null;
            
            return [
                'student' => $student,
                'current_grade' => $student->grade_level,
                'next_grade' => $gradeInfo['next'] ?? null,
                'can_progress' => $gradeInfo && $gradeInfo['next'],
                'category_change' => $gradeInfo && self::GRADE_LEVELS[$gradeInfo['next']]['category'] !== $gradeInfo['category'],
            ];
        })->filter(function ($item) {
            return $item['can_progress'];
        })->values()->toArray();
    }

    /**
     * Bulk advance multiple students
     */
    public function bulkAdvanceStudents(array $studentIds, array $options = []): array
    {
        $results = [
            'successful' => [],
            'failed' => [],
        ];

        foreach ($studentIds as $studentId) {
            try {
                $student = Student::findOrFail($studentId);
                $progression = $this->advanceStudent($student, $options);
                
                $results['successful'][] = [
                    'student_id' => $studentId,
                    'progression_id' => $progression->id,
                    'from_grade' => $progression->from_grade,
                    'to_grade' => $progression->to_grade,
                ];
            } catch (Exception $e) {
                $results['failed'][] = [
                    'student_id' => $studentId,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Get progression statistics for a partner organization
     */
    public function getProgressionStatistics(PartnerOrganization $partnerOrg, string $academicYear = null): array
    {
        $academicYear = $academicYear ?? $this->getCurrentAcademicYear();

        $stats = DB::table('student_progressions')
            ->join('students', 'student_progressions.student_id', '=', 'students.id')
            ->where('students.school_id', $partnerOrg->id)
            ->where('student_progressions.academic_year', $academicYear)
            ->select(
                DB::raw('COUNT(*) as total_progressions'),
                DB::raw('SUM(CASE WHEN student_progressions.status = "completed" THEN 1 ELSE 0 END) as completed'),
                DB::raw('SUM(CASE WHEN student_progressions.status = "pending" THEN 1 ELSE 0 END) as pending'),
                DB::raw('SUM(CASE WHEN student_progressions.status = "rejected" THEN 1 ELSE 0 END) as rejected')
            )
            ->first();

        $eligibleCount = count($this->getEligibleStudents($partnerOrg, $academicYear));

        return [
            'academic_year' => $academicYear,
            'total_progressions' => (int) ($stats->total_progressions ?? 0),
            'completed' => (int) ($stats->completed ?? 0),
            'pending' => (int) ($stats->pending ?? 0),
            'rejected' => (int) ($stats->rejected ?? 0),
            'eligible_for_progression' => $eligibleCount,
            'completion_rate' => $stats->total_progressions > 0 ? round(($stats->completed / $stats->total_progressions) * 100, 1) : 0,
        ];
    }

    /**
     * Check if student is eligible for scholarships after progression
     */
    private function checkScholarshipEligibility(Student $student, StudentProgression $progression): void
    {
        try {
            $eligibility = $this->eligibilityService->checkEligibilityAfterProgression($student, $progression);

            if ($eligibility['eligible']) {
                Log::info("Student eligible for scholarships after progression", [
                    'student_id' => $student->id,
                    'progression_id' => $progression->id,
                    'category' => $eligibility['category'],
                    'available_scholarships' => count($eligibility['scholarships'])
                ]);

                // Optionally trigger notifications here
                // The eligibility service handles the detailed logic
            }
        } catch (Exception $e) {
            Log::error("Error checking scholarship eligibility in progression", [
                'student_id' => $student->id,
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get current academic year
     */
    private function getCurrentAcademicYear(): string
    {
        $currentDate = Carbon::now();
        $year = $currentDate->year;
        
        // Academic year typically starts in September
        if ($currentDate->month >= 9) {
            return $year . '/' . ($year + 1);
        } else {
            return ($year - 1) . '/' . $year;
        }
    }

    /**
     * Get grade level information
     */
    public static function getGradeLevelInfo(string $gradeLevel): ?array
    {
        return self::GRADE_LEVELS[$gradeLevel] ?? null;
    }

    /**
     * Get all grade levels
     */
    public static function getAllGradeLevels(): array
    {
        return self::GRADE_LEVELS;
    }

    /**
     * Validate grade progression
     */
    public function validateProgression(string $fromGrade, string $toGrade): bool
    {
        $gradeInfo = self::GRADE_LEVELS[$fromGrade] ?? null;
        return $gradeInfo && $gradeInfo['next'] === $toGrade;
    }
}
