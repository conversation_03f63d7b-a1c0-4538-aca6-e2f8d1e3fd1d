{{-- Dashboard Sidebar Component --}}
@props([
    'userType' => 'user',
    'menuItems' => []
])

<div class="bg-white shadow-lg h-full">
    <!-- User Profile Section -->
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-lg">
                    {{ strtoupper(substr(Auth::user()->first_name, 0, 1)) }}{{ strtoupper(substr(Auth::user()->last_name, 0, 1)) }}
                </span>
            </div>
            <div class="ml-4">
                <h3 class="font-semibold text-gray-800">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</h3>
                <p class="text-sm text-gray-600 capitalize">{{ str_replace('_', ' ', Auth::user()->role) }}</p>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="p-4">
        <ul class="space-y-2">
            @foreach($menuItems as $item)
                <li>
                    <a href="{{ $item['url'] }}"
                       @if(isset($item['external']) && $item['external']) target="_blank" rel="noopener noreferrer" @endif
                       class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors {{ (isset($item['active']) && $item['active']) || (!isset($item['external']) && request()->is(trim($item['url'], '/'))) ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-500' : '' }}">
                        <i class="fas fa-{{ $item['icon'] }} mr-3 text-lg"></i>
                        <span class="font-medium">{{ $item['label'] }}</span>
                        @if(isset($item['external']) && $item['external'])
                            <i class="fas fa-external-link-alt ml-auto text-xs text-gray-400"></i>
                        @endif
                        @if(isset($item['badge']))
                            <span class="ml-auto px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                {{ $item['badge'] }}
                            </span>
                        @endif
                    </a>
                </li>
            @endforeach
        </ul>
    </nav>

    <!-- Quick Actions -->
    <div class="p-4 border-t border-gray-200 mt-auto">
        <div class="space-y-2">
            <a href="{{ route('profile.edit') ?? '#' }}" 
               class="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                <i class="fas fa-cog mr-2"></i>
                Settings
            </a>
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" 
                        class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:text-red-800 transition-colors">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Logout
                </button>
            </form>
        </div>
    </div>
</div>
