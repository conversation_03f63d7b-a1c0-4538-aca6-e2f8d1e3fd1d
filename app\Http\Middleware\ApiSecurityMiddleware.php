<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class ApiSecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Security headers
        $response = $next($request);
        
        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        // Add API version header
        $response->headers->set('X-API-Version', 'v1');
        
        // Add rate limit headers if applicable
        $this->addRateLimitHeaders($request, $response);
        
        // Log suspicious activity
        $this->detectSuspiciousActivity($request);
        
        return $response;
    }

    /**
     * Add rate limit headers to response
     */
    private function addRateLimitHeaders(Request $request, $response): void
    {
        if (auth()->check()) {
            $user = auth()->user();
            $key = 'api_rate_limit:' . $user->id;
            $maxAttempts = $this->getRateLimitForRole($user->role);
            
            $remaining = RateLimiter::remaining($key, $maxAttempts);
            $retryAfter = RateLimiter::availableIn($key);
            
            $response->headers->set('X-RateLimit-Limit', $maxAttempts);
            $response->headers->set('X-RateLimit-Remaining', max(0, $remaining));
            
            if ($remaining === 0) {
                $response->headers->set('X-RateLimit-Reset', now()->addSeconds($retryAfter)->timestamp);
            }
        }
    }

    /**
     * Get rate limit based on user role
     */
    private function getRateLimitForRole(string $role): int
    {
        $limits = [
            'admin' => 2000,
            'partner_organization' => 1000,
            'volunteer' => 500,
            'user' => 300,
            'student' => 300,
        ];

        return $limits[$role] ?? 100;
    }

    /**
     * Detect and log suspicious activity
     */
    private function detectSuspiciousActivity(Request $request): void
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        // Check for suspicious patterns
        $suspiciousPatterns = [
            'sql injection' => $this->detectSqlInjection($request),
            'xss attempt' => $this->detectXssAttempt($request),
            'path traversal' => $this->detectPathTraversal($request),
            'suspicious user agent' => $this->detectSuspiciousUserAgent($userAgent),
            'rapid requests' => $this->detectRapidRequests($ip),
        ];

        foreach ($suspiciousPatterns as $pattern => $detected) {
            if ($detected) {
                $this->logSecurityThreat($pattern, $request);
                
                // Increment threat score for IP
                $this->incrementThreatScore($ip);
                
                // Block IP if threat score is too high
                if ($this->shouldBlockIp($ip)) {
                    $this->blockIp($ip);
                }
            }
        }
    }

    /**
     * Detect SQL injection attempts
     */
    private function detectSqlInjection(Request $request): bool
    {
        $sqlPatterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bor\b.*1.*=.*1)/i',
            '/(\band\b.*1.*=.*1)/i',
        ];

        $input = json_encode($request->all());
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect XSS attempts
     */
    private function detectXssAttempt(Request $request): bool
    {
        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b/i',
            '/<object\b/i',
            '/<embed\b/i',
        ];

        $input = json_encode($request->all());
        
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect path traversal attempts
     */
    private function detectPathTraversal(Request $request): bool
    {
        $pathTraversalPatterns = [
            '/\.\.\//',
            '/\.\.\\\\/',
            '/%2e%2e%2f/',
            '/%2e%2e\\\\/',
        ];

        $path = $request->getPathInfo();
        $input = json_encode($request->all());
        
        foreach ($pathTraversalPatterns as $pattern) {
            if (preg_match($pattern, $path) || preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect suspicious user agents
     */
    private function detectSuspiciousUserAgent(?string $userAgent): bool
    {
        if (!$userAgent) {
            return true; // No user agent is suspicious
        }

        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'zap',
            'burp',
            'wget',
            'curl', // Be careful with this one as legitimate apps use curl
        ];

        $userAgent = strtolower($userAgent);
        
        foreach ($suspiciousAgents as $agent) {
            if (str_contains($userAgent, $agent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect rapid requests from same IP
     */
    private function detectRapidRequests(string $ip): bool
    {
        $key = "rapid_requests:{$ip}";
        $requests = Cache::get($key, 0);
        
        Cache::put($key, $requests + 1, 60); // Track for 1 minute
        
        return $requests > 100; // More than 100 requests per minute
    }

    /**
     * Log security threats
     */
    private function logSecurityThreat(string $threat, Request $request): void
    {
        Log::channel('security')->critical("Security threat detected: {$threat}", [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'payload' => $request->all(),
            'headers' => $request->headers->all(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Increment threat score for IP
     */
    private function incrementThreatScore(string $ip): void
    {
        $key = "threat_score:{$ip}";
        $score = Cache::get($key, 0);
        Cache::put($key, $score + 1, 3600); // Track for 1 hour
    }

    /**
     * Check if IP should be blocked
     */
    private function shouldBlockIp(string $ip): bool
    {
        $key = "threat_score:{$ip}";
        $score = Cache::get($key, 0);
        return $score >= 5; // Block after 5 threats
    }

    /**
     * Block IP address
     */
    private function blockIp(string $ip): void
    {
        $key = "blocked_ip:{$ip}";
        Cache::put($key, true, 86400); // Block for 24 hours
        
        Log::channel('security')->alert("IP address blocked due to suspicious activity", [
            'ip' => $ip,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
