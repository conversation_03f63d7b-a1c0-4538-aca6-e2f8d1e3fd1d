<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class PartnerOrganizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();

        // Check if user has partner_organization role
        if ($user->role !== 'partner_organization') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Partner Organization role required.'
            ], 403);
        }

        // Check if user has a linked partner organization
        if (!$user->partner_organization_id) {
            return response()->json([
                'success' => false,
                'message' => 'No partner organization linked to this account.'
            ], 403);
        }

        return $next($request);
    }
}
