<?php $__env->startSection('title', 'Organization Profile'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile', 'active' => true],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile', 'active' => true],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile', 'active' => true],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile', 'active' => true],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Organization Profile</h1>
                            <p class="text-gray-600">Manage your organization information and settings</p>
                        </div>
                        <div class="flex gap-3">
                            <a href="<?php echo e(route('partner.profile.edit')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-edit mr-2"></i>Edit Profile
                            </a>
                            <a href="<?php echo e(route('partner.profile.settings')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Profile Information -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Organization Overview -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Organization Overview</h2>
                            </div>
                            <div class="p-6">
                                <div class="flex items-start space-x-6 mb-6">
                                    <?php if($partner->profile_photo): ?>
                                        <img class="h-24 w-24 rounded-2xl object-cover shadow-lg" src="<?php echo e(Storage::url($partner->profile_photo)); ?>" alt="<?php echo e($partner->organization_name); ?>">
                                    <?php else: ?>
                                        <div class="h-24 w-24 rounded-2xl bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-building text-white text-2xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex-1">
                                        <h3 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($partner->organization_name); ?></h3>
                                        <p class="text-gray-600 mb-2"><?php echo e(ucfirst($partner->organization_type ?? 'Organization')); ?></p>
                                        <?php if($partner->established_year): ?>
                                            <p class="text-gray-500 text-sm">Established <?php echo e($partner->established_year); ?></p>
                                        <?php endif; ?>
                                        <?php if($partner->registration_number): ?>
                                            <p class="text-gray-500 text-sm">Registration: <?php echo e($partner->registration_number); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <?php if($partner->description): ?>
                                    <div class="mb-6">
                                        <h4 class="text-lg font-semibold text-gray-900 mb-2">About Us</h4>
                                        <p class="text-gray-600 leading-relaxed"><?php echo e($partner->description); ?></p>
                                    </div>
                                <?php endif; ?>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <?php if($partner->student_capacity): ?>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Student Capacity</label>
                                            <p class="text-gray-900 font-semibold"><?php echo e(number_format($partner->student_capacity)); ?> students</p>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($partner->website): ?>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                                            <a href="<?php echo e($partner->website); ?>" target="_blank" class="text-green-600 hover:text-green-800 font-medium">
                                                <?php echo e($partner->website); ?>

                                                <i class="fas fa-external-link-alt ml-1 text-sm"></i>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Contact Information</h2>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                                        <p class="text-gray-900"><?php echo e($partner->name); ?></p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                        <p class="text-gray-900"><?php echo e($partner->email); ?></p>
                                    </div>
                                    <?php if($partner->phone): ?>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                            <p class="text-gray-900"><?php echo e($partner->phone); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?php if($partner->address || $partner->city || $partner->state || $partner->country): ?>
                                    <div class="mt-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                        <div class="bg-gray-50 rounded-xl p-4">
                                            <?php if($partner->address): ?>
                                                <p class="text-gray-900"><?php echo e($partner->address); ?></p>
                                            <?php endif; ?>
                                            <p class="text-gray-900">
                                                <?php if($partner->city): ?><?php echo e($partner->city); ?><?php endif; ?>
                                                <?php if($partner->city && $partner->state): ?>, <?php endif; ?>
                                                <?php if($partner->state): ?><?php echo e($partner->state); ?><?php endif; ?>
                                                <?php if($partner->postal_code): ?> <?php echo e($partner->postal_code); ?><?php endif; ?>
                                            </p>
                                            <?php if($partner->country): ?>
                                                <p class="text-gray-900"><?php echo e($partner->country); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Information -->
                    <div class="space-y-6">
                        <!-- Profile Completion -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Profile Completion</h3>
                            </div>
                            <div class="p-6">
                                <?php
                                    $fields = ['name', 'email', 'phone', 'organization_name', 'organization_type', 'address', 'city', 'state', 'country', 'description', 'website'];
                                    $completed = 0;
                                    $total = count($fields);
                                    
                                    foreach ($fields as $field) {
                                        if (!empty($partner->$field)) {
                                            $completed++;
                                        }
                                    }
                                    
                                    if ($partner->profile_photo) {
                                        $completed += 0.5;
                                        $total += 0.5;
                                    }
                                    
                                    $percentage = round(($completed / $total) * 100);
                                ?>
                                
                                <div class="text-center mb-4">
                                    <div class="text-3xl font-bold text-green-600 mb-2"><?php echo e($percentage); ?>%</div>
                                    <p class="text-gray-600">Complete</p>
                                </div>
                                
                                <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                                    <div class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500" style="width: <?php echo e($percentage); ?>%"></div>
                                </div>
                                
                                <?php if($percentage < 100): ?>
                                    <p class="text-sm text-gray-600 text-center">Complete your profile to improve visibility</p>
                                <?php else: ?>
                                    <p class="text-sm text-green-600 text-center font-medium">Profile Complete! 🎉</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quick Statistics -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Quick Statistics</h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Total Students</span>
                                    <span class="font-semibold text-gray-900"><?php echo e($partner->partnerStudents()->count()); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Active Students</span>
                                    <span class="font-semibold text-green-600"><?php echo e($partner->partnerStudents()->where('status', 'active')->count()); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Total Applications</span>
                                    <span class="font-semibold text-gray-900"><?php echo e(\App\Models\ScholarshipApplication::where('school_id', $partner->id)->count()); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Approved Applications</span>
                                    <span class="font-semibold text-green-600"><?php echo e(\App\Models\ScholarshipApplication::where('school_id', $partner->id)->where('status', 'approved')->count()); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="<?php echo e(route('partner.profile.edit')); ?>" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300 text-center block">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </a>
                                <a href="<?php echo e(route('partner.profile.settings')); ?>" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300 text-center block">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                                <a href="<?php echo e(route('partner.students.create')); ?>" class="w-full bg-amber-600 hover:bg-amber-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300 text-center block">
                                    <i class="fas fa-user-plus mr-2"></i>Add Student
                                </a>
                                <a href="<?php echo e(route('partner.applications.create')); ?>" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-xl font-semibold transition-colors duration-300 text-center block">
                                    <i class="fas fa-file-plus mr-2"></i>New Application
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/profile/show.blade.php ENDPATH**/ ?>