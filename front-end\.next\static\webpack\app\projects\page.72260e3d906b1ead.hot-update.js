"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/page",{

/***/ "(app-pages-browser)/./app/projects/page.tsx":
/*!*******************************!*\
  !*** ./app/projects/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// API functions\nconst API_BASE_URL = \"https://halimakqfoundation.fnskills.ng/api/v1\" || 0;\nconst fetchStatistics = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/statistics\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchPortfolio = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/portfolio\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchScholarshipsByStatus = async (status)=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/by-status?status=\").concat(status));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nfunction ProjectsPage() {\n    _s();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [portfolio, setPortfolio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeProjects, setActiveProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedProjects, setCompletedProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [plannedProjects, setPlannedProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [categoryScholarships, setCategoryScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCategoryResults, setShowCategoryResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            const loadData = {\n                \"ProjectsPage.useEffect.loadData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [statsData, portfolioData, activeData, completedData, plannedData] = await Promise.all([\n                            fetchStatistics(),\n                            fetchPortfolio(),\n                            fetchScholarshipsByStatus('active'),\n                            fetchScholarshipsByStatus('completed'),\n                            fetchScholarshipsByStatus('planned')\n                        ]);\n                        setStatistics(statsData);\n                        setPortfolio(portfolioData);\n                        setActiveProjects(activeData);\n                        setCompletedProjects(completedData);\n                        setPlannedProjects(plannedData);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ProjectsPage.useEffect\"], []);\n    // Handle category click to show scholarships for that category\n    const handleCategoryClick = async (category)=>{\n        try {\n            setLoading(true);\n            setSelectedCategory(category);\n            // Fetch scholarships for the selected category\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/public-scholarships?category=\").concat(category));\n            const result = await response.json();\n            if (result.success) {\n                setCategoryScholarships(result.data || []);\n                setShowCategoryResults(true);\n            } else {\n                setError(\"Failed to load \".concat(category, \" scholarships\"));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load \".concat(category, \" scholarships\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle back to main view\n    const handleBackToMain = ()=>{\n        setShowCategoryResults(false);\n        setSelectedCategory(null);\n        setCategoryScholarships([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"active\":\n                return \"bg-green-500\";\n            case \"completed\":\n                return \"bg-blue-500\";\n            case \"planned\":\n                return \"bg-amber-500\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const getAllProjects = ()=>[\n            ...activeProjects,\n            ...completedProjects,\n            ...plannedProjects\n        ];\n    const getProjectsByStatus = (status)=>{\n        switch(status){\n            case 'active':\n                return activeProjects;\n            case 'completed':\n                return completedProjects;\n            case 'planned':\n                return plannedProjects;\n            default:\n                return getAllProjects();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading projects...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        className: \"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2\",\n                                        children: \"Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                        children: \"Transforming Lives Through Education\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                        children: \"Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Active Scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_approved) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Students Supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_amount_awarded) || '₦0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Total Investment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: portfolio.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Program Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Scholarship Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Our comprehensive scholarship programs support students at every educational level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: portfolio.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full capitalize\",\n                                                                    children: category.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                category.approved_count,\n                                                                                \" approved\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-green-600\",\n                                                                            children: [\n                                                                                category.active_count,\n                                                                                \" active\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 256,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Total: \",\n                                                                        category.total_amount\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-green-600 border-green-600 hover:bg-green-50\",\n                                                                    onClick: ()=>handleCategoryClick(category.category),\n                                                                    children: \"View Available Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, category.category, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    showCategoryResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: handleBackToMain,\n                                                className: \"mb-4\",\n                                                children: \"← Back to Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tight mb-2\",\n                                                children: [\n                                                    selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : '',\n                                                    \" Scholarships\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: [\n                                                    \"Available scholarships for \",\n                                                    selectedCategory || 'selected',\n                                                    \" education level\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this) : categoryScholarships.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryScholarships.map((scholarship)=>{\n                                        var _scholarship_category, _scholarship_category1, _scholarship_amount;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    className: \"bg-green-600 text-white rounded-full\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full\",\n                                                                    children: ((_scholarship_category = scholarship.category) === null || _scholarship_category === void 0 ? void 0 : _scholarship_category.charAt(0).toUpperCase()) + ((_scholarship_category1 = scholarship.category) === null || _scholarship_category1 === void 0 ? void 0 : _scholarship_category1.slice(1))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: scholarship.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm line-clamp-3\",\n                                                            children: scholarship.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-green-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"₦\",\n                                                                        ((_scholarship_amount = scholarship.amount) === null || _scholarship_amount === void 0 ? void 0 : _scholarship_amount.toLocaleString()) || 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 334,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                            onClick: ()=>{\n                                                                const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                                window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                                            },\n                                                            children: \"Apply Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, scholarship.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"No scholarships available for \",\n                                            selectedCategory,\n                                            \" category at the moment.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-50 dark:bg-green-950/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Measuring Our Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Every project we undertake is designed to create measurable, lasting change in the communities we serve.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-6 w-6 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Primary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.primary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Secondary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.secondary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-6 w-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"University Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-amber-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.university) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-6 w-6 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Active Programs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Currently accepting applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-900 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight sm:text-4xl\",\n                                        children: \"Support Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-lg\",\n                                        children: \"Your contribution can help us expand our reach and create even more educational opportunities for underprivileged students across Northern Nigeria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Donate to Projects\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold\",\n                                                children: \"Become a Partner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-green-950 dark:bg-black text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Building Tomorrow's Leaders\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-200 max-w-2xl mx-auto\",\n                                children: \"Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this transformative journey.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"BqlvbRaLva9pRQa6x4aTllWpgEM=\");\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/projects/page.tsx\n"));

/***/ })

});