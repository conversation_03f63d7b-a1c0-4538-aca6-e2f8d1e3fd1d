# HLTKKQ Foundation - Complete Laravel Backend Requirements

## Project Overview

Based on your Next.js frontend, this document outlines the complete Laravel backend requirements for the HLTKKQ Foundation NGO platform. The backend will provide RESTful APIs to support all frontend features including user management, donations, blog, volunteer programs, events, and administrative functions.

## Technology Stack

- **Framework**: Laravel 11+
- **Database**: MySQL 8.0+
- **Authentication**: Laravel Sanctum (SPA Authentication)
- **File Storage**: Laravel Storage (LOCAL STORAGE)
- **Mail Service**: Laravel Mail with SendGrid/Mailgun
- **Payment Gateway**: Paystack (Nigerian payment provider)
- **Queue System**: Redis/Database queues
- **Caching**: Redis
- **API Documentation**: L5-Swagger (OpenAPI 3.0)
- **Testing**: PHPUnit + Laravel Testing

## Frontend Analysis & API Requirements

### Detected Frontend Features:
1. **Homepage** - Hero sections, stats, testimonials, programs showcase
2. **Authentication** - Login/Register forms
3. **Blog System** - Blog posts, categories, search, newsletter
4. **Donation System** - Donation forms, tiers, impact tracking
5. **Contact System** - Contact forms, support
6. **Team Management** - Team member profiles
7. **Programs/Projects** - Program listings and details
8. **Events** - Event management and registration
9. **Impact Tracking** - Statistics and metrics
10. **Scholarships** - Scholarship management
11. **Learning Platform** - Educational content
12. **Gallery** - Photo/media galleries

## Complete API Endpoints Specification

### 1. Authentication & User Management

#### Authentication Endpoints
```
POST   /api/auth/register          - User registration
POST   /api/auth/login             - User login
POST   /api/auth/logout            - User logout
POST   /api/auth/refresh           - Refresh token
POST   /api/auth/forgot-password   - Password reset request
POST   /api/auth/reset-password    - Reset password
GET    /api/auth/verify-email/{id}/{hash} - Email verification
POST   /api/auth/resend-verification - Resend verification email
```

#### User Profile Endpoints
```
GET    /api/user/profile           - Get current user profile
PUT    /api/user/profile           - Update user profile
POST   /api/user/avatar            - Upload profile picture
DELETE /api/user/avatar            - Delete profile picture
GET    /api/user/activity          - Get user activity log
PUT    /api/user/preferences       - Update notification preferences
DELETE /api/user/account           - Delete user account
```

#### Admin User Management
```
GET    /api/admin/users            - List all users (paginated)
GET    /api/admin/users/{id}       - Get specific user details
PUT    /api/admin/users/{id}/status - Update user status (active/inactive)
PUT    /api/admin/users/{id}/role  - Update user role
DELETE /api/admin/users/{id}       - Delete user account
GET    /api/admin/users/export     - Export users to CSV/Excel
```

### 2. Volunteer Management System

#### Volunteer Application & Management
```
POST   /api/volunteers/apply       - Submit volunteer application
GET    /api/volunteers/opportunities - List volunteer opportunities
GET    /api/volunteers/my-applications - Get user's applications
PUT    /api/volunteers/applications/{id} - Update application status
GET    /api/volunteers/schedule    - Get volunteer schedule
POST   /api/volunteers/hours       - Log volunteer hours
GET    /api/volunteers/hours       - Get logged hours
GET    /api/volunteers/certificates - Get volunteer certificates
```

#### Admin Volunteer Management
```
GET    /api/admin/volunteers       - List all volunteers (paginated)
GET    /api/admin/volunteers/{id}  - Get volunteer details
PUT    /api/admin/volunteers/{id}/approve - Approve volunteer
PUT    /api/admin/volunteers/{id}/reject  - Reject volunteer
GET    /api/admin/volunteers/analytics - Volunteer analytics
POST   /api/admin/volunteers/bulk-action - Bulk approve/reject
```

### 3. Blog & Content Management

#### Public Blog Endpoints
```
GET    /api/blog/posts             - List published posts (paginated)
GET    /api/blog/posts/{slug}      - Get single post by slug
GET    /api/blog/categories        - Get blog categories
GET    /api/blog/tags              - Get blog tags
GET    /api/blog/search            - Search blog posts
POST   /api/blog/posts/{id}/like   - Like/unlike post
GET    /api/blog/posts/{id}/comments - Get post comments
POST   /api/blog/posts/{id}/comments - Add comment (authenticated)
```

#### Admin Blog Management
```
GET    /api/admin/blog/posts       - List all posts (including drafts)
POST   /api/admin/blog/posts       - Create new post
GET    /api/admin/blog/posts/{id}  - Get post for editing
PUT    /api/admin/blog/posts/{id}  - Update post
DELETE /api/admin/blog/posts/{id}  - Delete post
PUT    /api/admin/blog/posts/{id}/publish - Publish/unpublish post
POST   /api/admin/blog/categories  - Create category
PUT    /api/admin/blog/categories/{id} - Update category
DELETE /api/admin/blog/categories/{id} - Delete category
GET    /api/admin/blog/comments    - Moderate comments
PUT    /api/admin/blog/comments/{id}/approve - Approve/reject comment
```

### 4. Donation Management

#### Public Donation Endpoints
```
GET    /api/donations/campaigns    - List active campaigns
GET    /api/donations/tiers        - Get donation tiers
POST   /api/donations/create       - Create donation intent
POST   /api/donations/process      - Process payment (Paystack)
GET    /api/donations/verify/{reference} - Verify payment
POST   /api/donations/recurring/setup - Setup recurring donation
PUT    /api/donations/recurring/{id}/cancel - Cancel recurring
GET    /api/donations/receipt/{id} - Download donation receipt
```

#### Donor Management
```
GET    /api/donor/donations        - Get donor's donation history
GET    /api/donor/receipts         - Get donor's receipts
GET    /api/donor/impact           - Get donor's impact stats
PUT    /api/donor/preferences      - Update donation preferences
```

#### Admin Donation Management
```
GET    /api/admin/donations        - List all donations
GET    /api/admin/donations/analytics - Donation analytics
GET    /api/admin/donations/export - Export donations data
POST   /api/admin/donations/campaigns - Create campaign
PUT    /api/admin/donations/campaigns/{id} - Update campaign
GET    /api/admin/donors           - List all donors
GET    /api/admin/donors/{id}      - Get donor details
POST   /api/admin/donations/receipt/{id}/send - Resend receipt
```

### 5. Programs & Projects Management

#### Public Programs
```
GET    /api/programs               - List active programs
GET    /api/programs/{slug}        - Get program details
GET    /api/programs/{id}/gallery  - Get program gallery
GET    /api/programs/search        - Search programs
```

#### Admin Programs Management
```
GET    /api/admin/programs         - List all programs
POST   /api/admin/programs         - Create new program
GET    /api/admin/programs/{id}    - Get program details
PUT    /api/admin/programs/{id}    - Update program
DELETE /api/admin/programs/{id}    - Delete program
POST   /api/admin/programs/{id}/gallery - Upload program images
PUT    /api/admin/programs/{id}/status - Update program status
GET    /api/admin/programs/{id}/beneficiaries - Get beneficiaries
POST   /api/admin/programs/{id}/beneficiaries - Add beneficiary
```

### 6. Events Management

#### Public Events
```
GET    /api/events                 - List upcoming events
GET    /api/events/{slug}          - Get event details
POST   /api/events/{id}/register   - Register for event
DELETE /api/events/{id}/register   - Cancel registration
GET    /api/events/my-registrations - Get user's registrations
```

#### Admin Events Management
```
GET    /api/admin/events           - List all events
POST   /api/admin/events           - Create new event
GET    /api/admin/events/{id}      - Get event details
PUT    /api/admin/events/{id}      - Update event
DELETE /api/admin/events/{id}      - Delete event
GET    /api/admin/events/{id}/registrations - Get event registrations
POST   /api/admin/events/{id}/checkin/{user_id} - Check-in participant
GET    /api/admin/events/{id}/export - Export attendees
```

### 7. Team Management

#### Public Team
```
GET    /api/team                   - List team members
GET    /api/team/{slug}            - Get team member details
```

#### Admin Team Management
```
GET    /api/admin/team             - List all team members
POST   /api/admin/team             - Add new team member
GET    /api/admin/team/{id}        - Get team member details
PUT    /api/admin/team/{id}        - Update team member
DELETE /api/admin/team/{id}        - Delete team member
POST   /api/admin/team/{id}/photo  - Upload team member photo
```

### 8. Scholarship Management

#### Public Scholarships
```
GET    /api/scholarships           - List available scholarships
GET    /api/scholarships/{slug}    - Get scholarship details
POST   /api/scholarships/{id}/apply - Apply for scholarship
```

#### Admin Scholarship Management
```
GET    /api/admin/scholarships     - List all scholarships
POST   /api/admin/scholarships     - Create new scholarship
PUT    /api/admin/scholarships/{id} - Update scholarship
DELETE /api/admin/scholarships/{id} - Delete scholarship
GET    /api/admin/scholarships/{id}/applications - Get applications
PUT    /api/admin/scholarships/applications/{id}/status - Update application
```

### 9. Learning Platform

#### Public Learning
```
GET    /api/learning/courses       - List available courses
GET    /api/learning/courses/{slug} - Get course details
GET    /api/learning/courses/{id}/lessons - Get course lessons
POST   /api/learning/courses/{id}/enroll - Enroll in course
GET    /api/learning/my-courses    - Get enrolled courses
POST   /api/learning/courses/{id}/progress - Update progress
```

#### Admin Learning Management
```
GET    /api/admin/learning/courses - List all courses
POST   /api/admin/learning/courses - Create new course
PUT    /api/admin/learning/courses/{id} - Update course
DELETE /api/admin/learning/courses/{id} - Delete course
POST   /api/admin/learning/courses/{id}/lessons - Add lesson
GET    /api/admin/learning/analytics - Learning analytics
```

### 10. Contact & Support

#### Public Contact
```
POST   /api/contact/submit         - Submit contact form
GET    /api/contact/faq            - Get FAQ items
```

#### Admin Contact Management
```
GET    /api/admin/contact/messages - List contact messages
GET    /api/admin/contact/messages/{id} - Get message details
PUT    /api/admin/contact/messages/{id}/status - Update message status
POST   /api/admin/contact/messages/{id}/reply - Reply to message
GET    /api/admin/contact/faq      - Manage FAQ items
POST   /api/admin/contact/faq      - Create FAQ item
PUT    /api/admin/contact/faq/{id} - Update FAQ item
DELETE /api/admin/contact/faq/{id} - Delete FAQ item
```

### 11. Newsletter Management

#### Public Newsletter
```
POST   /api/newsletter/subscribe   - Subscribe to newsletter
POST   /api/newsletter/unsubscribe - Unsubscribe from newsletter
```

#### Admin Newsletter Management
```
GET    /api/admin/newsletter/subscribers - List subscribers
POST   /api/admin/newsletter/campaigns - Create campaign
POST   /api/admin/newsletter/send/{id} - Send campaign
GET    /api/admin/newsletter/analytics - Newsletter analytics
POST   /api/admin/newsletter/import    - Import subscribers
GET    /api/admin/newsletter/export    - Export subscribers
```

### 12. Gallery & Media Management

#### Public Gallery
```
GET    /api/gallery                - List gallery items
GET    /api/gallery/categories     - Get gallery categories
GET    /api/gallery/{category}     - Get category items
```

#### Admin Gallery Management
```
GET    /api/admin/gallery          - List all gallery items
POST   /api/admin/gallery          - Upload new items
PUT    /api/admin/gallery/{id}     - Update gallery item
DELETE /api/admin/gallery/{id}     - Delete gallery item
POST   /api/admin/gallery/bulk-upload - Bulk upload images
```

### 13. Analytics & Reports

#### Public Analytics (Limited)
```
GET    /api/analytics/impact       - Public impact statistics
GET    /api/analytics/programs     - Public program stats
```

#### Admin Analytics & Reports
```
GET    /api/admin/analytics/dashboard - Dashboard overview
GET    /api/admin/analytics/users     - User analytics
GET    /api/admin/analytics/donations - Donation analytics
GET    /api/admin/analytics/volunteers - Volunteer analytics
GET    /api/admin/analytics/programs  - Program analytics
GET    /api/admin/analytics/events    - Event analytics
POST   /api/admin/reports/generate    - Generate custom reports
GET    /api/admin/reports/download/{id} - Download generated report
```

### 14. File & Media Management

#### File Upload Endpoints
```
POST   /api/upload/image           - Upload single image
POST   /api/upload/document        - Upload document
POST   /api/upload/bulk            - Bulk file upload
DELETE /api/upload/{file_id}       - Delete uploaded file
GET    /api/upload/signed-url      - Get signed upload URL (S3)
```

## Database Schema Design

### Core Tables

#### 1. Users Table
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NULL,
    date_of_birth DATE NULL,
    gender ENUM('male', 'female', 'other') NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    country VARCHAR(50) DEFAULT 'Nigeria',
    profile_picture VARCHAR(255) NULL,
    role ENUM('admin', 'volunteer', 'donor', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    preferences JSON NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. Volunteers Table
```sql
CREATE TABLE volunteers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    application_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    skills JSON NULL,
    interests JSON NULL,
    availability JSON NULL,
    experience TEXT NULL,
    motivation TEXT NULL,
    background_check_status ENUM('pending', 'passed', 'failed') NULL,
    emergency_contact JSON NULL,
    hours_logged DECIMAL(8,2) DEFAULT 0.00,
    certifications JSON NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 3. Programs Table
```sql
CREATE TABLE programs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(220) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    objectives JSON NULL,
    target_beneficiaries TEXT NULL,
    start_date DATE NULL,
    end_date DATE NULL,
    budget DECIMAL(12,2) NULL,
    status ENUM('planning', 'active', 'completed', 'suspended') DEFAULT 'planning',
    location JSON NULL,
    impact_metrics JSON NULL,
    featured_image VARCHAR(255) NULL,
    gallery JSON NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### 4. Events Table
```sql
CREATE TABLE events (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(220) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    event_type ENUM('fundraising', 'volunteer', 'awareness', 'training', 'other') NOT NULL,
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    venue VARCHAR(200) NULL,
    address TEXT NULL,
    capacity INT NULL,
    registration_deadline TIMESTAMP NULL,
    requirements JSON NULL,
    agenda JSON NULL,
    featured_image VARCHAR(255) NULL,
    status ENUM('draft', 'published', 'cancelled') DEFAULT 'draft',
    organizer_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizer_id) REFERENCES users(id)
);
```

#### 5. Blog Posts Table
```sql
CREATE TABLE blog_posts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(275) UNIQUE NOT NULL,
    excerpt TEXT NULL,
    content LONGTEXT NOT NULL,
    featured_image VARCHAR(255) NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    seo_title VARCHAR(60) NULL,
    seo_description VARCHAR(160) NULL,
    seo_keywords JSON NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES blog_categories(id)
);
```

#### 6. Donations Table
```sql
CREATE TABLE donations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    donor_id BIGINT UNSIGNED NULL,
    donor_email VARCHAR(100) NULL,
    donor_name VARCHAR(100) NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NGN',
    donation_type ENUM('one-time', 'recurring') DEFAULT 'one-time',
    frequency ENUM('monthly', 'quarterly', 'annually') NULL,
    purpose VARCHAR(200) NULL,
    campaign_id BIGINT UNSIGNED NULL,
    payment_method VARCHAR(50) NULL,
    payment_provider VARCHAR(50) DEFAULT 'paystack',
    transaction_reference VARCHAR(100) UNIQUE NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    is_anonymous BOOLEAN DEFAULT FALSE,
    receipt_number VARCHAR(50) UNIQUE NULL,
    receipt_issued_at TIMESTAMP NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES users(id)
);
```

#### 7. Additional Supporting Tables
```sql
-- Blog Categories
CREATE TABLE blog_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(110) UNIQUE NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Event Registrations
CREATE TABLE event_registrations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    status ENUM('registered', 'attended', 'cancelled') DEFAULT 'registered',
    registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    checked_in_at TIMESTAMP NULL,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_registration (event_id, user_id)
);

-- Newsletter Subscribers
CREATE TABLE newsletter_subscribers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    status ENUM('subscribed', 'unsubscribed') DEFAULT 'subscribed',
    subscription_source VARCHAR(100) NULL,
    preferences JSON NULL,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL
);

-- Contact Messages
CREATE TABLE contact_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'in_progress', 'resolved') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    assigned_to BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Team Members
CREATE TABLE team_members (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(110) UNIQUE NOT NULL,
    position VARCHAR(100) NOT NULL,
    department VARCHAR(100) NULL,
    bio TEXT NULL,
    photo VARCHAR(255) NULL,
    email VARCHAR(100) NULL,
    linkedin_url VARCHAR(255) NULL,
    twitter_url VARCHAR(255) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Scholarships
CREATE TABLE scholarships (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(220) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    eligibility_criteria TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    application_deadline DATE NOT NULL,
    requirements JSON NULL,
    status ENUM('draft', 'open', 'closed') DEFAULT 'draft',
    max_applicants INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Laravel Project Structure

```
backend/
├── app/
│   ├── Console/
│   │   └── Commands/
│   ├── Exceptions/
│   │   └── Handler.php
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Api/
│   │   │   │   ├── AuthController.php
│   │   │   │   ├── BlogController.php
│   │   │   │   ├── DonationController.php
│   │   │   │   ├── EventController.php
│   │   │   │   ├── ProgramController.php
│   │   │   │   ├── VolunteerController.php
│   │   │   │   └── ContactController.php
│   │   │   └── Admin/
│   │   │       ├── UserController.php
│   │   │       ├── BlogController.php
│   │   │       ├── DonationController.php
│   │   │       └── AnalyticsController.php
│   │   ├── Middleware/
│   │   │   ├── AdminMiddleware.php
│   │   │   ├── RoleMiddleware.php
│   │   │   └── ApiRateLimitMiddleware.php
│   │   ├── Requests/
│   │   │   ├── Auth/
│   │   │   ├── Blog/
│   │   │   ├── Donation/
│   │   │   └── User/
│   │   └── Resources/
│   │       ├── UserResource.php
│   │       ├── BlogPostResource.php
│   │       ├── DonationResource.php
│   │       └── EventResource.php
│   ├── Models/
│   │   ├── User.php
│   │   ├── Volunteer.php
│   │   ├── Program.php
│   │   ├── Event.php
│   │   ├── BlogPost.php
│   │   ├── Donation.php
│   │   └── ContactMessage.php
│   ├── Services/
│   │   ├── PaymentService.php
│   │   ├── EmailService.php
│   │   ├── FileUploadService.php
│   │   └── AnalyticsService.php
│   └── Traits/
│       ├── HasSlug.php
│       ├── HasStatus.php
│       └── Searchable.php
├── config/
│   ├── sanctum.php
│   ├── cors.php
│   ├── filesystems.php
│   └── services.php
├── database/
│   ├── migrations/
│   │   ├── 2024_01_01_000000_create_users_table.php
│   │   ├── 2024_01_01_000001_create_volunteers_table.php
│   │   ├── 2024_01_01_000002_create_programs_table.php
│   │   ├── 2024_01_01_000003_create_events_table.php
│   │   ├── 2024_01_01_000004_create_blog_posts_table.php
│   │   └── 2024_01_01_000005_create_donations_table.php
│   ├── seeders/
│   │   ├── DatabaseSeeder.php
│   │   ├── UserSeeder.php
│   │   ├── BlogSeeder.php
│   │   └── ProgramSeeder.php
│   └── factories/
│       ├── UserFactory.php
│       ├── BlogPostFactory.php
│       └── DonationFactory.php
├── routes/
│   ├── api.php
│   ├── web.php
│   └── channels.php
├── storage/
│   ├── app/
│   │   ├── public/
│   │   └── uploads/
│   └── logs/
├── tests/
│   ├── Feature/
│   │   ├── AuthTest.php
│   │   ├── BlogTest.php
│   │   ├── DonationTest.php
│   │   └── EventTest.php
│   └── Unit/
│       ├── UserTest.php
│       └── PaymentServiceTest.php
├── .env.example
├── composer.json
├── phpunit.xml
└── README.md
```

## Required Laravel Packages

```json
{
  "require": {
    "php": "^8.2",
    "laravel/framework": "^11.0",
    "laravel/sanctum": "^4.0",
    "laravel/tinker": "^2.8",
    "spatie/laravel-permission": "^6.0",
    "spatie/laravel-sluggable": "^3.6",
    "spatie/laravel-medialibrary": "^11.0",
    "intervention/image": "^3.0",
    "league/flysystem-aws-s3-v3": "^3.0",
    "predis/predis": "^2.0",
    "pusher/pusher-php-server": "^7.2",
    "darkaonline/l5-swagger": "^8.5",
    "maatwebsite/excel": "^3.1",
    "barryvdh/laravel-dompdf": "^2.0"
  },
  "require-dev": {
    "fakerphp/faker": "^1.9.1",
    "laravel/pint": "^1.0",
    "laravel/sail": "^1.18",
    "mockery/mockery": "^1.4.4",
    "nunomaduro/collision": "^8.0",
    "phpunit/phpunit": "^10.1"
  }
}
```

## Environment Configuration (.env.example)

```env
APP_NAME="HLTKKQ Foundation"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# SendGrid Configuration
SENDGRID_API_KEY=

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Paystack Configuration
PAYSTACK_PUBLIC_KEY=
PAYSTACK_SECRET_KEY=
PAYSTACK_PAYMENT_URL=https://api.paystack.co

# Cloudinary Configuration
CLOUDINARY_URL=
CLOUDINARY_UPLOAD_PRESET=

# API Rate Limiting
API_RATE_LIMIT=60

# JWT Configuration
JWT_SECRET=
JWT_ALGO=HS256
JWT_TTL=60

# Social Login
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=

# Analytics
GOOGLE_ANALYTICS_ID=

# Notification Channels
SLACK_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
```

## Security & Performance Considerations

### Security Measures
1. **Input Validation**: Laravel Form Request validation
2. **CSRF Protection**: Enabled for web routes
3. **XSS Protection**: Blade template escaping
4. **SQL Injection Prevention**: Eloquent ORM with parameterized queries
5. **Rate Limiting**: API throttling middleware
6. **CORS Configuration**: Proper origin restrictions
7. **File Upload Security**: File type validation and storage restrictions
8. **Authentication**: Sanctum SPA authentication
9. **Authorization**: Role-based permissions with Spatie

### Performance Optimizations
1. **Database Indexing**: Proper indexes on foreign keys and search fields
2. **Query Optimization**: Eager loading and query builders
3. **Caching**: Redis for session, cache, and queues
4. **File Storage**: CDN integration for static assets
5. **Queue Processing**: Background job processing
6. **Response Caching**: API response caching for static data
7. **Image Optimization**: Intervention Image for resizing and compression

## Testing Strategy

### Feature Tests
- Authentication flow testing
- API endpoint testing
- Database transaction testing
- File upload testing
- Payment processing testing

### Unit Tests
- Model relationship testing
- Service class testing
- Utility function testing
- Validation rule testing

## Deployment Checklist

### Production Setup
1. **Server Configuration**: PHP 8.2+, MySQL 8.0+, Redis, Queue Worker
2. **Environment Variables**: All production keys and secrets
3. **Database Migration**: Run migrations and seeders
4. **File Permissions**: Storage and cache directories
5. **SSL Certificate**: HTTPS configuration
6. **Monitoring**: Error tracking and performance monitoring
7. **Backup Strategy**: Database and file backups
8. **Queue Workers**: Supervisor configuration for queue processing

This comprehensive backend specification provides everything needed to build a robust Laravel API that will seamlessly integrate with your Next.js frontend for the HLTKKQ Foundation platform.