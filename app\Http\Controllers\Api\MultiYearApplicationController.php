<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\ScholarshipApplication;
use App\Models\Scholarship;
use App\Models\PartnerOrganization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Multi-Year Applications",
 *     description="API Endpoints for Multi-Year Scholarship Application Management"
 * )
 */
class MultiYearApplicationController extends Controller
{
    // Middleware is applied at route level in Laravel 11

    /**
     * Get multi-year application history for a student
     */
    public function getStudentApplicationHistory(Request $request, $studentId): JsonResponse
    {
        try {
            $user = Auth::user();
            $student = Student::findOrFail($studentId);

            // Check authorization
            if (!$this->canAccessStudentData($user, $student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to student data'
                ], 403);
            }

            // Get all applications for this student grouped by academic year
            $applications = ScholarshipApplication::where('student_id', $studentId)
                ->with(['scholarship', 'student.progressions'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->groupBy(function ($application) {
                    return $application->created_at->year;
                });

            // Get student progressions for context
            $progressions = StudentProgression::where('student_id', $studentId)
                ->orderBy('academic_year', 'desc')
                ->get();

            // Calculate multi-year statistics
            $stats = [
                'total_applications' => ScholarshipApplication::where('student_id', $studentId)->count(),
                'approved_applications' => ScholarshipApplication::where('student_id', $studentId)->where('status', 'approved')->count(),
                'total_amount_awarded' => ScholarshipApplication::where('student_id', $studentId)
                    ->where('status', 'approved')
                    ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->sum('scholarships.amount'),
                'years_in_system' => $progressions->pluck('academic_year')->unique()->count(),
                'current_grade' => $progressions->where('is_current', true)->first()?->grade_level,
                'scholarship_categories' => ScholarshipApplication::where('student_id', $studentId)
                    ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->pluck('scholarships.category')
                    ->unique()
                    ->values()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => $student,
                    'applications_by_year' => $applications,
                    'progressions' => $progressions,
                    'statistics' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve application history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get eligible scholarships for a student based on their progression
     */
    public function getEligibleScholarships(Request $request, $studentId): JsonResponse
    {
        try {
            $user = Auth::user();
            $student = Student::findOrFail($studentId);

            // Check authorization
            if (!$this->canAccessStudentData($user, $student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to student data'
                ], 403);
            }

            // Get current progression
            $currentProgression = StudentProgression::where('student_id', $studentId)
                ->where('is_current', true)
                ->first();

            if (!$currentProgression) {
                return response()->json([
                    'success' => false,
                    'message' => 'No current progression found for student'
                ], 404);
            }

            // Determine eligible scholarship categories based on grade level
            $eligibleCategories = $this->getEligibleCategories($currentProgression->grade_level);

            // Get available scholarships
            $scholarships = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->whereIn('category', $eligibleCategories)
                ->where('application_deadline', '>', now())
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->get();

            // Check which scholarships the student has already applied for this year
            $appliedScholarshipIds = ScholarshipApplication::where('student_id', $studentId)
                ->whereYear('created_at', now()->year)
                ->pluck('scholarship_id')
                ->toArray();

            // Mark scholarships as already applied
            $scholarships->each(function ($scholarship) use ($appliedScholarshipIds) {
                $scholarship->already_applied = in_array($scholarship->id, $appliedScholarshipIds);
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => $student,
                    'current_progression' => $currentProgression,
                    'eligible_categories' => $eligibleCategories,
                    'scholarships' => $scholarships,
                    'applied_this_year' => count($appliedScholarshipIds)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve eligible scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a multi-year application tracking record
     */
    public function createMultiYearApplication(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'scholarship_id' => 'required|exists:scholarships,id',
                'academic_years' => 'required|array|min:1',
                'academic_years.*' => 'required|string',
                'renewal_criteria' => 'nullable|array',
                'expected_progression' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $student = Student::findOrFail($request->student_id);

            // Check authorization
            if (!$this->canAccessStudentData($user, $student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to create application for this student'
                ], 403);
            }

            DB::beginTransaction();

            try {
                // Create the main application record
                $application = ScholarshipApplication::create([
                    'student_id' => $request->student_id,
                    'scholarship_id' => $request->scholarship_id,
                    'status' => 'pending',
                    'application_data' => $request->application_data ?? [],
                    'submitted_at' => now(),
                    'is_multi_year' => true,
                    'academic_years' => $request->academic_years,
                    'renewal_criteria' => $request->renewal_criteria,
                    'expected_progression' => $request->expected_progression
                ]);

                // Create tracking records for each academic year
                foreach ($request->academic_years as $year) {
                    DB::table('multi_year_application_tracking')->insert([
                        'application_id' => $application->id,
                        'academic_year' => $year,
                        'status' => 'pending',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'data' => $application->load(['student', 'scholarship']),
                    'message' => 'Multi-year application created successfully'
                ], 201);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create multi-year application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get multi-year application analytics for partner organizations
     */
    public function getMultiYearAnalytics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Multi-year applications are those with renewal_count > 0 or have previous_scholarship_id
            $query = ScholarshipApplication::where(function($q) {
                $q->where('renewal_count', '>', 0)
                  ->orWhereNotNull('previous_scholarship_id');
            });

            // Filter by partner organization if applicable
            if ($user->role === 'partner_organization') {
                $query->whereHas('student', function ($q) use ($user) {
                    $q->where('school_id', $user->partner_organization_id);
                });
            }

            $period = $request->get('period', '12months');
            $endDate = now();
            $startDate = match($period) {
                '3months' => $endDate->copy()->subMonths(3),
                '6months' => $endDate->copy()->subMonths(6),
                '12months' => $endDate->copy()->subMonths(12),
                '2years' => $endDate->copy()->subYears(2),
                default => $endDate->copy()->subMonths(12)
            };

            $applications = $query->whereBetween('created_at', [$startDate, $endDate])
                ->with(['student', 'scholarship'])
                ->get();

            // Calculate analytics
            $analytics = [
                'total_multi_year_applications' => $applications->count(),
                'active_multi_year_applications' => $applications->where('status', 'approved')->count(),
                'renewal_success_rate' => $this->calculateRenewalSuccessRate($applications),
                'average_duration' => $this->calculateAverageDuration($applications),
                'category_distribution' => $this->getCategoryDistribution($applications),
                'yearly_trends' => $this->getYearlyTrends($applications),
                'student_retention_rate' => $this->calculateStudentRetentionRate($applications)
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics,
                'period' => $period
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve multi-year analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user can access student data
     */
    private function canAccessStudentData($user, Student $student): bool
    {
        // Admins can access all student data
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can only access their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual students can only access their own data
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Get eligible scholarship categories based on grade level
     */
    private function getEligibleCategories(int $gradeLevel): array
    {
        if ($gradeLevel >= 1 && $gradeLevel <= 6) {
            return ['primary'];
        } elseif ($gradeLevel >= 7 && $gradeLevel <= 12) {
            return ['secondary'];
        } else {
            return ['university'];
        }
    }

    /**
     * Calculate renewal success rate
     */
    private function calculateRenewalSuccessRate($applications): float
    {
        $renewalApplications = $applications->filter(function ($app) {
            return count($app->academic_years ?? []) > 1;
        });

        if ($renewalApplications->isEmpty()) {
            return 0;
        }

        $successfulRenewals = $renewalApplications->filter(function ($app) {
            return $app->status === 'approved';
        });

        return round(($successfulRenewals->count() / $renewalApplications->count()) * 100, 1);
    }

    /**
     * Calculate average duration of multi-year applications
     */
    private function calculateAverageDuration($applications): float
    {
        $durations = $applications->map(function ($app) {
            return count($app->academic_years ?? []);
        });

        return $durations->isEmpty() ? 0 : round($durations->average(), 1);
    }

    /**
     * Get category distribution
     */
    private function getCategoryDistribution($applications): array
    {
        return $applications->groupBy('scholarship.category')
            ->map(function ($group) {
                return $group->count();
            })
            ->toArray();
    }

    /**
     * Get yearly trends
     */
    private function getYearlyTrends($applications): array
    {
        return $applications->groupBy(function ($app) {
            return $app->created_at->year;
        })
        ->map(function ($group, $year) {
            return [
                'year' => $year,
                'total' => $group->count(),
                'approved' => $group->where('status', 'approved')->count(),
                'pending' => $group->where('status', 'pending')->count()
            ];
        })
        ->values()
        ->toArray();
    }

    /**
     * Calculate student retention rate
     */
    private function calculateStudentRetentionRate($applications): float
    {
        $studentsWithMultiYear = $applications->pluck('student_id')->unique();
        
        if ($studentsWithMultiYear->isEmpty()) {
            return 0;
        }

        $retainedStudents = $studentsWithMultiYear->filter(function ($studentId) {
            return ScholarshipApplication::where('student_id', $studentId)
                ->where('is_multi_year', true)
                ->where('status', 'approved')
                ->exists();
        });

        return round(($retainedStudents->count() / $studentsWithMultiYear->count()) * 100, 1);
    }

    /**
     * Renew a multi-year application for the next academic year
     */
    public function renewApplication(Request $request, $applicationId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'academic_year' => 'required|string',
                'renewal_notes' => 'nullable|string|max:1000',
                'updated_criteria' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $originalApplication = ScholarshipApplication::findOrFail($applicationId);

            // Check authorization
            if (!$this->canAccessStudentData($user, $originalApplication->student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to application data'
                ], 403);
            }

            // Check if application is eligible for renewal
            if ($originalApplication->status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only approved applications can be renewed'
                ], 422);
            }

            DB::beginTransaction();

            try {
                // Create renewal application
                $renewalApplication = ScholarshipApplication::create([
                    'student_id' => $originalApplication->student_id,
                    'scholarship_id' => $originalApplication->scholarship_id,
                    'school_id' => $originalApplication->school_id,
                    'grade_level' => $originalApplication->grade_level + 1, // Next grade
                    'academic_year' => $request->academic_year,
                    'application_type' => $originalApplication->application_type,
                    'previous_scholarship_id' => $originalApplication->id,
                    'renewal_count' => $originalApplication->renewal_count + 1,
                    'application_data' => $originalApplication->application_data,
                    'status' => 'pending',
                    'is_multi_year' => true,
                    'renewal_criteria' => $request->updated_criteria ?? $originalApplication->renewal_criteria,
                    'submitted_at' => now(),
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Application renewed successfully',
                    'data' => [
                        'renewal_application' => $renewalApplication->load(['student', 'scholarship']),
                        'original_application' => $originalApplication,
                    ]
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to renew application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get application tracking details
     */
    public function getApplicationTracking(Request $request, $applicationId): JsonResponse
    {
        try {
            $user = Auth::user();
            $application = ScholarshipApplication::findOrFail($applicationId);

            // Check authorization
            if (!$this->canAccessStudentData($user, $application->student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to application data'
                ], 403);
            }

            // Get related applications (renewals)
            $relatedApplications = ScholarshipApplication::where('previous_scholarship_id', $applicationId)
                ->orWhere('id', $application->previous_scholarship_id)
                ->with(['student', 'scholarship'])
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'application' => $application->load(['student', 'scholarship']),
                    'related_applications' => $relatedApplications,
                    'renewal_history' => $this->getRenewalHistory($application),
                    'tracking_summary' => [
                        'total_renewals' => $relatedApplications->count(),
                        'approved_renewals' => $relatedApplications->where('status', 'approved')->count(),
                        'current_year' => $application->academic_year,
                        'years_active' => $relatedApplications->pluck('academic_year')->unique()->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve application tracking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk renew multiple applications
     */
    public function bulkRenewApplications(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'application_ids' => 'required|array|min:1',
                'application_ids.*' => 'required|exists:scholarship_applications,id',
                'academic_year' => 'required|string',
                'renewal_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $applicationIds = $request->application_ids;
            $results = [
                'successful' => [],
                'failed' => [],
            ];

            foreach ($applicationIds as $applicationId) {
                try {
                    $application = ScholarshipApplication::findOrFail($applicationId);

                    // Check authorization
                    if (!$this->canAccessStudentData($user, $application->student)) {
                        $results['failed'][] = [
                            'application_id' => $applicationId,
                            'error' => 'Unauthorized access to application data'
                        ];
                        continue;
                    }

                    // Check if eligible for renewal
                    if ($application->status !== 'approved') {
                        $results['failed'][] = [
                            'application_id' => $applicationId,
                            'error' => 'Application is not approved'
                        ];
                        continue;
                    }

                    // Create renewal
                    $renewalApplication = ScholarshipApplication::create([
                        'student_id' => $application->student_id,
                        'scholarship_id' => $application->scholarship_id,
                        'school_id' => $application->school_id,
                        'grade_level' => $application->grade_level + 1,
                        'academic_year' => $request->academic_year,
                        'application_type' => $application->application_type,
                        'previous_scholarship_id' => $application->id,
                        'renewal_count' => $application->renewal_count + 1,
                        'application_data' => $application->application_data,
                        'status' => 'pending',
                        'is_multi_year' => true,
                        'submitted_at' => now(),
                    ]);

                    $results['successful'][] = [
                        'original_application_id' => $applicationId,
                        'renewal_application_id' => $renewalApplication->id,
                        'student_name' => $application->student->name
                    ];

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'application_id' => $applicationId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk renewal completed',
                'data' => [
                    'successful' => $results['successful'],
                    'failed' => $results['failed'],
                    'summary' => [
                        'total_attempted' => count($applicationIds),
                        'successful_count' => count($results['successful']),
                        'failed_count' => count($results['failed']),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk renewal',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get renewal eligibility for applications
     */
    public function getRenewalEligibility(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $academicYear = $request->get('academic_year', now()->year);

            $query = ScholarshipApplication::where('is_multi_year', true)
                ->where('status', 'approved')
                ->with(['student', 'scholarship']);

            // Filter by partner organization if not admin
            if ($user->role !== 'admin') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $query->where('school_id', $partnerOrg->id);
            }

            $applications = $query->get();

            $eligibleApplications = $applications->filter(function ($application) use ($academicYear) {
                // Check if already renewed for this year
                $alreadyRenewed = ScholarshipApplication::where('previous_scholarship_id', $application->id)
                    ->where('academic_year', $academicYear)
                    ->exists();

                return !$alreadyRenewed;
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'eligible_applications' => $eligibleApplications->values(),
                    'academic_year' => $academicYear,
                    'summary' => [
                        'total_multi_year' => $applications->count(),
                        'eligible_for_renewal' => $eligibleApplications->count(),
                        'already_renewed' => $applications->count() - $eligibleApplications->count(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve renewal eligibility',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get renewal history for an application
     */
    private function getRenewalHistory($application): array
    {
        $renewals = ScholarshipApplication::where('previous_scholarship_id', $application->id)
            ->orderBy('academic_year')
            ->get();

        return $renewals->map(function ($renewal) {
            return [
                'id' => $renewal->id,
                'academic_year' => $renewal->academic_year,
                'status' => $renewal->status,
                'renewal_count' => $renewal->renewal_count,
                'submitted_at' => $renewal->submitted_at,
                'reviewed_at' => $renewal->reviewed_at,
            ];
        })->toArray();
    }
}
