<?php

namespace Database\Seeders;

use App\Models\EducationalResource;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EducationalResourceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get an admin user to be the creator
        $admin = User::where('role', 'admin')->first();
        if (!$admin) {
            $admin = User::create([
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);
        }

        $resources = [
            [
                'title' => 'University Application Guide',
                'description' => 'A comprehensive guide on how to apply for university scholarships and prepare your application documents.',
                'type' => 'study_guide',
                'category' => 'Academic Guidance',
                'target_audience' => 'university',
                'difficulty_level' => 'beginner',
                'duration' => 30.0,
                'learning_objectives' => 'Learn how to prepare scholarship applications, understand requirements, and increase your chances of success.',
                'prerequisites' => 'Basic understanding of university application process',
                'tags' => ['scholarship', 'university', 'application', 'guide'],
                'is_active' => true,
                'is_featured' => true,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Financial Literacy for Students',
                'description' => 'Learn essential financial skills including budgeting, saving, and managing student loans.',
                'type' => 'video_course',
                'category' => 'Financial Education',
                'target_audience' => 'all',
                'difficulty_level' => 'beginner',
                'duration' => 45.0,
                'video_url' => 'https://www.youtube.com/watch?v=example',
                'learning_objectives' => 'Understand basic financial concepts, create budgets, and manage money effectively.',
                'prerequisites' => 'None',
                'tags' => ['finance', 'budgeting', 'money management', 'students'],
                'is_active' => true,
                'is_featured' => true,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Study Skills and Time Management',
                'description' => 'Effective study techniques and time management strategies for academic success.',
                'type' => 'document',
                'category' => 'Study Skills',
                'target_audience' => 'university',
                'difficulty_level' => 'intermediate',
                'duration' => 25.0,
                'learning_objectives' => 'Master effective study techniques, improve time management, and boost academic performance.',
                'prerequisites' => 'Currently enrolled in educational program',
                'tags' => ['study skills', 'time management', 'productivity', 'academic success'],
                'is_active' => true,
                'is_featured' => false,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Career Planning and Development',
                'description' => 'Guide to career planning, job search strategies, and professional development.',
                'type' => 'study_guide',
                'category' => 'Career Development',
                'target_audience' => 'university',
                'difficulty_level' => 'intermediate',
                'duration' => 60.0,
                'learning_objectives' => 'Develop career goals, create professional networks, and prepare for job market.',
                'prerequisites' => 'Final year students or recent graduates',
                'tags' => ['career', 'job search', 'professional development', 'networking'],
                'is_active' => true,
                'is_featured' => true,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Research Methods and Academic Writing',
                'description' => 'Learn research methodologies and academic writing skills for university-level work.',
                'type' => 'document',
                'category' => 'Academic Skills',
                'target_audience' => 'university',
                'difficulty_level' => 'advanced',
                'duration' => 90.0,
                'learning_objectives' => 'Master research techniques, citation methods, and academic writing standards.',
                'prerequisites' => 'University-level education',
                'tags' => ['research', 'academic writing', 'methodology', 'citations'],
                'is_active' => true,
                'is_featured' => false,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Digital Literacy and Technology Skills',
                'description' => 'Essential computer and internet skills for modern education and work.',
                'type' => 'interactive',
                'category' => 'Technology',
                'target_audience' => 'all',
                'difficulty_level' => 'beginner',
                'duration' => 40.0,
                'content_url' => 'https://example.com/digital-literacy-course',
                'learning_objectives' => 'Develop basic computer skills, internet navigation, and digital communication.',
                'prerequisites' => 'Basic computer access',
                'tags' => ['technology', 'digital literacy', 'computer skills', 'internet'],
                'is_active' => true,
                'is_featured' => true,
                'created_by' => $admin->id,
            ],
        ];

        foreach ($resources as $resource) {
            EducationalResource::create($resource);
        }
    }
}
