<?php

namespace App\Services;

use Yabacon\Paystack;
use Exception;
use Illuminate\Support\Facades\Log;

class PaystackService
{
    protected $paystack;
    protected $publicKey;
    protected $secretKey;

    public function __construct()
    {
        $this->publicKey = config('services.paystack.public_key');
        $this->secretKey = config('services.paystack.secret_key');
        $this->paystack = new Paystack($this->secretKey);
    }

    /**
     * Initialize a payment transaction
     */
    public function initializePayment($email, $amount, $reference = null, $metadata = [])
    {
        try {
            $tranx = $this->paystack->transaction->initialize([
                'amount' => $amount * 100, // Convert to kobo
                'email' => $email,
                'reference' => $reference ?: $this->generateReference(),
                'metadata' => $metadata,
                'callback_url' => route('donations.callback'),
            ]);

            if ($tranx->status) {
                return [
                    'status' => true,
                    'data' => $tranx->data,
                    'authorization_url' => $tranx->data->authorization_url,
                    'access_code' => $tranx->data->access_code,
                    'reference' => $tranx->data->reference,
                ];
            }

            return [
                'status' => false,
                'message' => $tranx->message ?? 'Payment initialization failed',
            ];
        } catch (Exception $e) {
            Log::error('PayStack initialization error: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => 'Payment initialization failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verifyPayment($reference)
    {
        try {
            $tranx = $this->paystack->transaction->verify([
                'reference' => $reference,
            ]);

            if ($tranx->status) {
                return [
                    'status' => true,
                    'data' => $tranx->data,
                    'amount' => $tranx->data->amount / 100, // Convert from kobo
                    'currency' => $tranx->data->currency,
                    'paid_at' => $tranx->data->paid_at,
                    'channel' => $tranx->data->authorization->channel,
                    'card_type' => $tranx->data->authorization->card_type ?? null,
                    'bank' => $tranx->data->authorization->bank ?? null,
                ];
            }

            return [
                'status' => false,
                'message' => $tranx->message ?? 'Payment verification failed',
            ];
        } catch (Exception $e) {
            Log::error('PayStack verification error: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get transaction details
     */
    public function getTransaction($reference)
    {
        try {
            $tranx = $this->paystack->transaction->fetch([
                'reference' => $reference,
            ]);

            if ($tranx->status) {
                return [
                    'status' => true,
                    'data' => $tranx->data,
                ];
            }

            return [
                'status' => false,
                'message' => $tranx->message ?? 'Transaction fetch failed',
            ];
        } catch (Exception $e) {
            Log::error('PayStack transaction fetch error: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => 'Transaction fetch failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate a unique payment reference
     */
    public function generateReference($prefix = 'HALIMAKQ')
    {
        return $prefix . '_' . time() . '_' . uniqid();
    }

    /**
     * Get public key for frontend
     */
    public function getPublicKey()
    {
        return $this->publicKey;
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhook($input, $signature)
    {
        return hash_hmac('sha512', $input, $this->secretKey) === $signature;
    }
}
