'use client'

import { ReactNode } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useRoleAccess } from '@/hooks/useRoleAccess'
import DashboardNavigation from '@/components/navigation/DashboardNavigation'
import AuthGuard from '@/components/guards/AuthGuard'
import { Loader2 } from 'lucide-react'

interface DashboardLayoutProps {
  children: ReactNode
  requireAuth?: boolean
  requiredRole?: string | string[]
  title?: string
  description?: string
  showNavigation?: boolean
}

export default function DashboardLayout({
  children,
  requireAuth = true,
  requiredRole,
  title,
  description,
  showNavigation = true
}: DashboardLayoutProps) {
  const { user, isLoading } = useAuth()
  const { roleInfo } = useRoleAccess()

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <AuthGuard 
      requireAuth={requireAuth} 
      requiredRole={requiredRole}
      showAccessDenied={true}
    >
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {showNavigation && <DashboardNavigation />}
        
        {/* Main Content */}
        <div className={showNavigation ? "md:pl-64" : ""}>
          {/* Page Header */}
          {(title || description) && (
            <div className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
              <div className="px-4 sm:px-6 lg:px-8 py-6">
                <div className="max-w-7xl mx-auto">
                  {title && (
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {title}
                    </h1>
                  )}
                  {description && (
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      {description}
                    </p>
                  )}
                  {roleInfo && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {roleInfo.displayName}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Page Content */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </AuthGuard>
  )
}

/**
 * Specific layout components for different dashboard types
 */

export function AdminDashboardLayout({ children, title, description }: { 
  children: ReactNode
  title?: string
  description?: string
}) {
  return (
    <DashboardLayout
      requiredRole="admin"
      title={title || "Admin Dashboard"}
      description={description || "Manage the NGO platform"}
    >
      {children}
    </DashboardLayout>
  )
}

export function PartnerDashboardLayout({ children, title, description }: { 
  children: ReactNode
  title?: string
  description?: string
}) {
  return (
    <DashboardLayout
      requiredRole="partner_organization"
      title={title || "Partner Dashboard"}
      description={description || "Manage your students and scholarship applications"}
    >
      {children}
    </DashboardLayout>
  )
}

export function StudentDashboardLayout({ children, title, description }: { 
  children: ReactNode
  title?: string
  description?: string
}) {
  return (
    <DashboardLayout
      requiredRole={["user", "student"]}
      title={title || "Student Dashboard"}
      description={description || "Track your scholarship applications and academic progress"}
    >
      {children}
    </DashboardLayout>
  )
}

/**
 * Higher-order component for wrapping pages with dashboard layout
 */
export function withDashboardLayout<P extends object>(
  Component: React.ComponentType<P>,
  layoutProps: Omit<DashboardLayoutProps, 'children'> = {}
) {
  return function DashboardWrappedComponent(props: P) {
    return (
      <DashboardLayout {...layoutProps}>
        <Component {...props} />
      </DashboardLayout>
    )
  }
}

/**
 * Breadcrumb component for dashboard navigation
 */
interface BreadcrumbItem {
  label: string
  href?: string
}

interface DashboardBreadcrumbProps {
  items: BreadcrumbItem[]
}

export function DashboardBreadcrumb({ items }: DashboardBreadcrumbProps) {
  return (
    <nav className="flex mb-4" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <svg
                className="w-6 h-6 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            {item.href ? (
              <a
                href={item.href}
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-500"
              >
                {item.label}
              </a>
            ) : (
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

/**
 * Quick stats component for dashboard pages
 */
interface QuickStat {
  label: string
  value: string | number
  change?: string
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon?: React.ComponentType<{ className?: string }>
}

interface DashboardStatsProps {
  stats: QuickStat[]
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon
        return (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {IconComponent && (
                    <IconComponent className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {stat.label}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
              {stat.change && (
                <div className="mt-2">
                  <span
                    className={`text-sm font-medium ${
                      stat.changeType === 'increase'
                        ? 'text-green-600'
                        : stat.changeType === 'decrease'
                        ? 'text-red-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {stat.change}
                  </span>
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
