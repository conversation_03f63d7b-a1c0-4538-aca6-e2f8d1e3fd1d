@extends('layouts.app')

@section('title', 'Partner Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Welcome Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Welcome, {{ Auth::user()->organization_name ?? Auth::user()->first_name }}!</h1>
                    <p class="text-gray-600 mt-2">Manage your students and scholarship applications.</p>
                    @if(Auth::user()->organization_type)
                        <p class="text-sm text-indigo-600 mt-1">{{ ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) }}</p>
                    @endif
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Total Students</p>
                    <p class="text-lg font-semibold text-indigo-600">{{ Auth::user()->partnerStudents()->count() }}</p>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Total Students -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-800">{{ Auth::user()->partnerStudents()->count() }}</div>
                        <div class="text-sm text-gray-600">Total Students</div>
                    </div>
                </div>
            </div>

            <!-- Active Applications -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-800">
                            {{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) {
                                $q->whereIn('status', ['pending', 'under_review']);
                            })->count() }}
                        </div>
                        <div class="text-sm text-gray-600">Active Applications</div>
                    </div>
                </div>
            </div>

            <!-- Approved Applications -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-800">
                            {{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) {
                                $q->where('status', 'approved');
                            })->count() }}
                        </div>
                        <div class="text-sm text-gray-600">Approved</div>
                    </div>
                </div>
            </div>

            <!-- Available Scholarships -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-orange-100 rounded-full">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <div class="text-2xl font-bold text-gray-800">5</div>
                        <div class="text-sm text-gray-600">Available Scholarships</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Manage Students -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Manage Students</h3>
                        <p class="text-sm text-gray-600">Add and manage your students</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Manage Students →</a>
                </div>
            </div>

            <!-- Apply for Scholarships -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Apply for Scholarships</h3>
                        <p class="text-sm text-gray-600">Apply on behalf of students</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ route('scholarships') }}" class="text-green-600 hover:text-green-800 font-medium">View Scholarships →</a>
                </div>
            </div>

            <!-- Track Applications -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800">Track Applications</h3>
                        <p class="text-sm text-gray-600">Monitor application status</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-purple-600 hover:text-purple-800 font-medium">View Applications →</a>
                </div>
            </div>
        </div>

        <!-- Recent Students and Applications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Students -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Recent Students</h2>
                    <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>
                @php
                    $recentStudents = Auth::user()->partnerStudents()->latest()->take(5)->get();
                @endphp
                @if($recentStudents->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentStudents as $student)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <h4 class="font-semibold text-gray-800">{{ $student->name }}</h4>
                                    <p class="text-sm text-gray-600">Class {{ $student->class }} • {{ $student->gender }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="px-2 py-1 text-xs rounded-full
                                        @if($student->status === 'active') bg-green-100 text-green-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($student->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <p class="text-gray-600">No students added yet</p>
                        <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium mt-2 inline-block">Add Your First Student →</a>
                    </div>
                @endif
            </div>

            <!-- Organization Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Organization Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Organization Name:</span>
                        <span class="font-medium">{{ Auth::user()->organization_name ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Type:</span>
                        <span class="font-medium">{{ Auth::user()->organization_type ? ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) : 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Principal:</span>
                        <span class="font-medium">{{ Auth::user()->principal_name ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Contact:</span>
                        <span class="font-medium">{{ Auth::user()->phone_number ?? 'Not specified' }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">Update Information →</a>
                </div>
            </div>
        </div>

        <!-- Guidelines -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-blue-800">Partner Organization Guidelines</h3>
                    <div class="text-blue-700 mt-2 space-y-1">
                        <p>• Add students to your organization to manage their scholarship applications</p>
                        <p>• You can apply for Primary and Secondary scholarships on behalf of your students</p>
                        <p>• Track application progress and download approval letters for successful applications</p>
                        <p>• Keep student information updated for better application processing</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
