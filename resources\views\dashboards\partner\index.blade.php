@extends('layouts.app')

@section('title', 'Partner Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]"
            />
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
        <!-- Welcome Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">Welcome, {{ Auth::user()->organization_name ?? Auth::user()->first_name }}!</h1>
                    <p class="text-gray-600 mt-2">Manage your students and scholarship applications.</p>
                    @if(Auth::user()->organization_type)
                        <p class="text-sm text-indigo-600 mt-1">{{ ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) }}</p>
                    @endif
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Total Students</p>
                    <p class="text-lg font-semibold text-indigo-600">{{ Auth::user()->partnerStudents()->count() }}</p>
                </div>
            </div>
        </div>

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <x-dashboard.stat-card
                    title="Total Students"
                    value="{{ Auth::user()->partnerStudents()->count() }}"
                    icon="users"
                    color="blue"
                />

                <x-dashboard.stat-card
                    title="Active Applications"
                    value="{{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) {
                        $q->whereIn('status', ['pending', 'under_review']);
                    })->count() }}"
                    icon="file-alt"
                    color="green"
                />

                <x-dashboard.stat-card
                    title="Approved Applications"
                    value="{{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) {
                        $q->where('status', 'approved');
                    })->count() }}"
                    icon="check-circle"
                    color="purple"
                />

                <x-dashboard.stat-card
                    title="Available Scholarships"
                    value="{{ \App\Models\Scholarship::where('status', 'active')->whereIn('category', ['primary', 'secondary'])->count() }}"
                    icon="graduation-cap"
                    color="orange"
                />
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <x-dashboard.card
                    title="Manage Students"
                    subtitle="Add and manage your students"
                    icon="users"
                    iconColor="blue"
                    href="/partner/students"
                />

                <x-dashboard.card
                    title="Apply for Scholarships"
                    subtitle="Apply on behalf of students"
                    icon="graduation-cap"
                    iconColor="green"
                    :href="route('scholarships')"
                />

                <x-dashboard.card
                    title="Track Applications"
                    subtitle="Monitor application status"
                    icon="chart-bar"
                    iconColor="purple"
                    href="/partner/applications"
                />
            </div>

        <!-- Recent Students and Applications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Students -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Recent Students</h2>
                    <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>
                @php
                    $recentStudents = Auth::user()->partnerStudents()->latest()->take(5)->get();
                @endphp
                @if($recentStudents->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentStudents as $student)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <h4 class="font-semibold text-gray-800">{{ $student->name }}</h4>
                                    <p class="text-sm text-gray-600">Class {{ $student->class }} • {{ $student->gender }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="px-2 py-1 text-xs rounded-full
                                        @if($student->status === 'active') bg-green-100 text-green-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($student->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <p class="text-gray-600">No students added yet</p>
                        <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium mt-2 inline-block">Add Your First Student →</a>
                    </div>
                @endif
            </div>

            <!-- Organization Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Organization Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Organization Name:</span>
                        <span class="font-medium">{{ Auth::user()->organization_name ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Type:</span>
                        <span class="font-medium">{{ Auth::user()->organization_type ? ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) : 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Principal:</span>
                        <span class="font-medium">{{ Auth::user()->principal_name ?? 'Not specified' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Contact:</span>
                        <span class="font-medium">{{ Auth::user()->phone_number ?? 'Not specified' }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">Update Information →</a>
                </div>
            </div>
        </div>

            <!-- Student Management Tools -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Student Management Tools</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-user-plus text-blue-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Add New Student</h3>
                        <p class="text-sm text-gray-600 mt-1">Register new students to your organization</p>
                        <a href="/partner/students/create" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">Add Student →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-file-import text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Bulk Import</h3>
                        <p class="text-sm text-gray-600 mt-1">Import multiple students from CSV file</p>
                        <a href="/partner/students/import" class="text-green-600 hover:text-green-800 text-sm font-medium mt-2 inline-block">Import Students →</a>
                    </div>

                    <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <i class="fas fa-file-export text-purple-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-gray-800">Export Reports</h3>
                        <p class="text-sm text-gray-600 mt-1">Generate student and application reports</p>
                        <a href="/partner/reports" class="text-purple-600 hover:text-purple-800 text-sm font-medium mt-2 inline-block">Generate Reports →</a>
                    </div>
                </div>
            </div>

            <!-- Guidelines -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-blue-800">Partner Organization Guidelines</h3>
                        <div class="text-blue-700 mt-2 space-y-1">
                            <p>• Add students to your organization to manage their scholarship applications</p>
                            <p>• You can apply for Primary and Secondary scholarships on behalf of your students</p>
                            <p>• Track application progress and download approval letters for successful applications</p>
                            <p>• Keep student information updated for better application processing</p>
                            <p>• Use bulk import feature to add multiple students efficiently</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
