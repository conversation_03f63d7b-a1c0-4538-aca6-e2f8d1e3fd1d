<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner Dashboard - HALIMAKQ NGO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-green: #059669;
            --secondary-green: #047857;
        }
        
        .navbar-brand {
            color: var(--primary-green) !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            border: none;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-school me-2"></i>HALIMAKQ NGO - Partner Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ Auth::user()->first_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Partner Organization Dashboard</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Welcome to your partner dashboard, {{ Auth::user()->first_name }}!
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                                        <h5>Manage Students</h5>
                                        <p class="text-muted">Add and manage your students</p>
                                        <a href="#" class="btn btn-primary">Manage Students</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                                        <h5>Scholarship Applications</h5>
                                        <p class="text-muted">Apply for scholarships on behalf of students</p>
                                        <a href="#" class="btn btn-outline-primary">View Applications</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-handshake fa-3x text-warning mb-3"></i>
                                        <h5>Collaboration</h5>
                                        <p class="text-muted">Manage joint programs and partnerships</p>
                                        <a href="#" class="btn btn-outline-primary">View Programs</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Organization Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Contact Person:</strong> {{ Auth::user()->full_name }}</p>
                                    <p><strong>Email:</strong> {{ Auth::user()->email }}</p>
                                    <p><strong>Role:</strong> {{ Auth::user()->role_name }}</p>
                                </div>
                                <div class="col-md-6">
                                    @if(Auth::user()->organization_name)
                                        <p><strong>Organization:</strong> {{ Auth::user()->organization_name }}</p>
                                    @endif
                                    @if(Auth::user()->organization_type)
                                        <p><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) }}</p>
                                    @endif
                                    @if(Auth::user()->organization_phone)
                                        <p><strong>Phone:</strong> {{ Auth::user()->organization_phone }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
