<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Contact",
 *     description="Contact form and support message endpoints"
 * )
 */
class ContactController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/contact",
     *     summary="Submit contact form",
     *     tags={"Contact"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name","email","subject","message"},
     *             @OA\Property(property="name", type="string", example="John Doe"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="phone", type="string", example="+234800000000"),
     *             @OA\Property(property="subject", type="string", example="General Inquiry"),
     *             @OA\Property(property="message", type="string", example="Hello, I would like to know more about your programs.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Contact message submitted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Your message has been sent successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:200',
            'message' => 'required|string|max:2000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $contactMessage = ContactMessage::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => 'new',
            'priority' => $this->determinePriority($request->subject, $request->message),
        ]);

        // TODO: Send email notification to admin
        // TODO: Send auto-reply to user

        return response()->json([
            'success' => true,
            'message' => 'Your message has been sent successfully. We will get back to you soon.',
            'data' => [
                'id' => $contactMessage->id,
                'reference' => 'MSG-' . str_pad($contactMessage->id, 6, '0', STR_PAD_LEFT),
            ]
        ], 201);
    }

    /**
     * Get FAQ items
     *
     * @OA\Get(
     *     path="/api/v1/contact/faq",
     *     summary="Get FAQ items",
     *     tags={"Contact"},
     *     @OA\Response(
     *         response=200,
     *         description="FAQ items retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function faq(): JsonResponse
    {
        // For now, return static FAQ data
        // In a full implementation, this would come from a database table
        $faqItems = [
            [
                'id' => 1,
                'question' => 'How can I volunteer with HLTKKQ Foundation?',
                'answer' => 'You can apply to become a volunteer by filling out our volunteer application form. We will review your application and contact you with available opportunities that match your skills and interests.',
                'category' => 'Volunteering'
            ],
            [
                'id' => 2,
                'question' => 'Are donations tax-deductible?',
                'answer' => 'Yes, HLTKKQ Foundation is a registered NGO and all donations are tax-deductible. You will receive a receipt for your donation that can be used for tax purposes.',
                'category' => 'Donations'
            ],
            [
                'id' => 3,
                'question' => 'How can I apply for a scholarship?',
                'answer' => 'You can browse our available scholarships and apply online through our scholarship portal. Make sure you meet the eligibility criteria before applying.',
                'category' => 'Scholarships'
            ],
            [
                'id' => 4,
                'question' => 'How do you use donated funds?',
                'answer' => 'We are committed to transparency and use donated funds directly for our programs and initiatives. You can view our impact reports to see how your donations are making a difference.',
                'category' => 'Donations'
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $faqItems
        ]);
    }

    /**
     * Determine message priority based on subject and content
     */
    private function determinePriority(string $subject, string $message): string
    {
        $urgentKeywords = ['urgent', 'emergency', 'immediate', 'critical', 'asap'];
        $highKeywords = ['complaint', 'problem', 'issue', 'concern', 'help'];

        $text = strtolower($subject . ' ' . $message);

        foreach ($urgentKeywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                return 'high';
            }
        }

        foreach ($highKeywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                return 'medium';
            }
        }

        return 'low';
    }
}
