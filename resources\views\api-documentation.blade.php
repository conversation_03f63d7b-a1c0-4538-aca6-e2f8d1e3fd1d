<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGO Platform API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .api-section { scroll-margin-top: 100px; }
        .endpoint-card { transition: all 0.3s ease; }
        .endpoint-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold text-gray-800">NGO Platform API</h1>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded">v1.0</span>
                </div>
                <div class="flex space-x-4">
                    <a href="#overview" class="text-gray-600 hover:text-blue-600">Overview</a>
                    <a href="#authentication" class="text-gray-600 hover:text-blue-600">Authentication</a>
                    <a href="#endpoints" class="text-gray-600 hover:text-blue-600">Endpoints</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-20">
        <!-- Overview Section -->
        <section id="overview" class="api-section py-12 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">Multi-Tier Scholarship System API</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Comprehensive API for managing scholarship applications across Primary, Secondary, and University levels 
                        with role-based access control and multi-year tracking capabilities.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-blue-600 text-3xl mb-4">🎓</div>
                        <h3 class="text-xl font-semibold mb-2">Multi-Tier System</h3>
                        <p class="text-gray-600">Support for Primary (1-6), Secondary (7-12), and University scholarship categories with category-specific workflows.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-green-600 text-3xl mb-4">👥</div>
                        <h3 class="text-xl font-semibold mb-2">Role-Based Access</h3>
                        <p class="text-gray-600">Granular permissions for Admin, Partner Organizations, and Individual Students with category-specific access control.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-purple-600 text-3xl mb-4">📊</div>
                        <h3 class="text-xl font-semibold mb-2">Analytics & Tracking</h3>
                        <p class="text-gray-600">Comprehensive progression tracking, multi-year applications, and detailed analytics for institutional performance.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Authentication Section -->
        <section id="authentication" class="api-section py-12">
            <div class="max-w-7xl mx-auto px-4">
                <h2 class="text-3xl font-bold text-gray-800 mb-8">Authentication</h2>
                
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-semibold mb-4">Bearer Token Authentication</h3>
                    <p class="text-gray-600 mb-4">All API requests require authentication using Laravel Sanctum tokens.</p>
                    
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <pre><code class="language-http">Authorization: Bearer {your-api-token}</code></pre>
                    </div>
                </div>

                <div class="grid md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="font-semibold text-lg mb-3 text-blue-600">Admin Role</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Full system access</li>
                            <li>• All scholarship categories</li>
                            <li>• Application review & approval</li>
                            <li>• System analytics</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="font-semibold text-lg mb-3 text-green-600">Partner Organization</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• Primary & Secondary scholarships</li>
                            <li>• Student management</li>
                            <li>• Progression tracking</li>
                            <li>• Institutional analytics</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="font-semibold text-lg mb-3 text-purple-600">Individual Student</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• University scholarships only</li>
                            <li>• Personal applications</li>
                            <li>• Own progression history</li>
                            <li>• Application status tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Endpoints Section -->
        <section id="endpoints" class="api-section py-12 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4">
                <h2 class="text-3xl font-bold text-gray-800 mb-8">API Endpoints</h2>

                <!-- Partner Dashboard Endpoints -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6">Partner Dashboard</h3>
                    
                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">GET</span>
                                <code class="text-lg font-mono">/api/v1/partner-dashboard</code>
                            </div>
                            <span class="text-sm text-gray-500">Partner Organization</span>
                        </div>
                        <p class="text-gray-600 mb-4">Get comprehensive dashboard data including overview statistics, recent activities, upcoming deadlines, and progression alerts.</p>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">Response Example:</h4>
                            <pre><code class="language-json">{
  "success": true,
  "data": {
    "overview": {
      "total_students": 150,
      "total_applications": 89,
      "approved_applications": 67,
      "success_rate": 75.3
    },
    "recent_activities": [...],
    "upcoming_deadlines": [...],
    "progression_alerts": [...]
  }
}</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Student Progression Endpoints -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6">Student Progression</h3>
                    
                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">GET</span>
                                <code class="text-lg font-mono">/api/v1/student-progression/student/{studentId}/history</code>
                            </div>
                            <span class="text-sm text-gray-500">All Roles</span>
                        </div>
                        <p class="text-gray-600 mb-4">Get complete progression history for a specific student including statistics and current status.</p>
                    </div>

                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">GET</span>
                                <code class="text-lg font-mono">/api/v1/student-progression/analytics</code>
                            </div>
                            <span class="text-sm text-gray-500">Admin, Partner</span>
                        </div>
                        <p class="text-gray-600 mb-4">Get progression analytics including trends, completion rates, and grade distribution.</p>
                    </div>

                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">POST</span>
                                <code class="text-lg font-mono">/api/v1/student-progression/create</code>
                            </div>
                            <span class="text-sm text-gray-500">Admin, Partner</span>
                        </div>
                        <p class="text-gray-600 mb-4">Create a new progression record for a student.</p>
                    </div>
                </div>

                <!-- Multi-Year Applications -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6">Multi-Year Applications</h3>
                    
                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">GET</span>
                                <code class="text-lg font-mono">/api/v1/multi-year-applications/student/{studentId}/history</code>
                            </div>
                            <span class="text-sm text-gray-500">All Roles</span>
                        </div>
                        <p class="text-gray-600 mb-4">Get multi-year application history for a student grouped by academic year.</p>
                    </div>

                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">GET</span>
                                <code class="text-lg font-mono">/api/v1/multi-year-applications/student/{studentId}/eligible-scholarships</code>
                            </div>
                            <span class="text-sm text-gray-500">All Roles</span>
                        </div>
                        <p class="text-gray-600 mb-4">Get scholarships eligible for a student based on their current progression.</p>
                    </div>

                    <div class="endpoint-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">POST</span>
                                <code class="text-lg font-mono">/api/v1/multi-year-applications/create</code>
                            </div>
                            <span class="text-sm text-gray-500">All Roles</span>
                        </div>
                        <p class="text-gray-600 mb-4">Create a multi-year application with tracking across academic years.</p>
                    </div>
                </div>

                <!-- Error Responses -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-6">Error Responses</h3>
                    
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="font-semibold mb-4">Standard Error Format</h4>
                        <div class="bg-red-50 p-4 rounded-lg mb-4">
                            <pre><code class="language-json">{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "errors": {
    "field_name": ["Validation error message"]
  }
}</code></pre>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="font-semibold mb-2">HTTP Status Codes</h5>
                                <ul class="text-sm space-y-1">
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">200</code> Success</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">201</code> Created</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">400</code> Bad Request</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">401</code> Unauthorized</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">403</code> Forbidden</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">404</code> Not Found</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">422</code> Validation Error</li>
                                    <li><code class="bg-gray-100 px-2 py-1 rounded">500</code> Server Error</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-2">Common Error Types</h5>
                                <ul class="text-sm space-y-1">
                                    <li>• Authentication required</li>
                                    <li>• Insufficient permissions</li>
                                    <li>• Category access denied</li>
                                    <li>• Student access denied</li>
                                    <li>• Validation failed</li>
                                    <li>• Resource not found</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p>&copy; 2024 NGO Platform. Multi-Tier Scholarship System API Documentation.</p>
            <p class="text-gray-400 mt-2">For technical support, contact the development team.</p>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
