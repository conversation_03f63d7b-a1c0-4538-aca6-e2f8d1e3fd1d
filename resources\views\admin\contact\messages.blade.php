@extends('layouts.admin')

@section('title', 'Contact Messages')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Contact Messages</h1>
        <div>
            <button class="btn btn-outline-secondary me-2" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i>Mark All as Read
            </button>
            <a href="{{ route('admin.contact.export') }}" class="btn btn-success">
                <i class="fas fa-download me-2"></i>Export Messages
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Messages</div>
                            <div class="h4">{{ $messages->total() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New Messages</div>
                            <div class="h4">{{ $messages->where('status', 'new')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">In Progress</div>
                            <div class="h4">{{ $messages->where('status', 'in_progress')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Resolved</div>
                            <div class="h4">{{ $messages->where('status', 'resolved')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Contact Messages</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search messages..." id="searchMessages">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="new">New</option>
                        <option value="in_progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterPriority">
                        <option value="">All Priority</option>
                        <option value="high">High Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="low">Low Priority</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" id="filterDate" value="{{ now()->format('Y-m-d') }}">
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Priority</th>
                            <th>Sender</th>
                            <th>Subject</th>
                            <th>Message</th>
                            <th>Status</th>
                            <th>Received</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($messages as $message)
                        <tr class="{{ $message->status === 'new' ? 'table-warning' : '' }}">
                            <td>
                                <span class="badge bg-{{ 
                                    $message->priority === 'high' ? 'danger' : 
                                    ($message->priority === 'medium' ? 'warning' : 'success') 
                                }}">
                                    <i class="fas fa-{{ 
                                        $message->priority === 'high' ? 'exclamation-triangle' : 
                                        ($message->priority === 'medium' ? 'exclamation-circle' : 'info-circle') 
                                    }}"></i>
                                    {{ ucfirst($message->priority) }}
                                </span>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $message->name }}</div>
                                    <div class="small text-muted">{{ $message->email }}</div>
                                    @if($message->phone)
                                        <div class="small text-muted">{{ $message->phone }}</div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="fw-bold">{{ Str::limit($message->subject, 40) }}</div>
                                @if($message->status === 'new')
                                    <span class="badge bg-warning">New</span>
                                @endif
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="{{ $message->message }}">
                                    {{ Str::limit($message->message, 80) }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $message->status === 'new' ? 'warning' : 
                                    ($message->status === 'resolved' ? 'success' : 'info') 
                                }}">
                                    {{ ucfirst(str_replace('_', ' ', $message->status)) }}
                                </span>
                                @if($message->assigned_to)
                                    <br><small class="text-muted">Assigned to: {{ $message->assignedUser->name ?? 'Unknown' }}</small>
                                @endif
                            </td>
                            <td>
                                <div class="small">
                                    {{ $message->created_at->format('M d, Y') }}<br>
                                    {{ $message->created_at->format('g:i A') }}
                                </div>
                                <div class="small text-muted">
                                    {{ $message->created_at->diffForHumans() }}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewMessage({{ $message->id }})" title="View Full Message">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="replyToMessage({{ $message->id }})" title="Reply">
                                        <i class="fas fa-reply"></i>
                                    </button>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="Change Status">
                                            <i class="fas fa-tasks"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'new')">Mark as New</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'in_progress')">In Progress</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'resolved')">Mark Resolved</a></li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage({{ $message->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No contact messages found</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $messages->firstItem() }} to {{ $messages->lastItem() }} of {{ $messages->total() }} results
                </div>
                {{ $messages->links() }}
            </div>
        </div>
    </div>
</div>

<!-- View Message Modal -->
<div class="modal fade" id="viewMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageDetails">
                <!-- Message details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="replyFromModal()">Reply</button>
            </div>
        </div>
    </div>
</div>

<!-- Reply Modal -->
<div class="modal fade" id="replyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reply to Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="replyForm">
                    <div class="mb-3">
                        <label for="replyTo" class="form-label">To:</label>
                        <input type="email" class="form-control" id="replyTo" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="replySubject" class="form-label">Subject:</label>
                        <input type="text" class="form-control" id="replySubject">
                    </div>
                    <div class="mb-3">
                        <label for="replyMessage" class="form-label">Message:</label>
                        <textarea class="form-control" id="replyMessage" rows="8" placeholder="Type your reply here..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="markAsResolved">
                            <label class="form-check-label" for="markAsResolved">
                                Mark as resolved after sending
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendReply()">Send Reply</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this message? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteMessage">Delete Message</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentMessageId = null;

function viewMessage(messageId) {
    currentMessageId = messageId;
    // In a real implementation, you would fetch message details via AJAX
    const modal = new bootstrap.Modal(document.getElementById('viewMessageModal'));
    
    // Mock message details - replace with actual AJAX call
    document.getElementById('messageDetails').innerHTML = `
        <div class="mb-3">
            <strong>From:</strong> John Doe (<EMAIL>)<br>
            <strong>Phone:</strong> +234 ************<br>
            <strong>Subject:</strong> Volunteer Inquiry<br>
            <strong>Priority:</strong> <span class="badge bg-warning">Medium</span><br>
            <strong>Received:</strong> January 15, 2024 at 2:30 PM
        </div>
        <hr>
        <div>
            <strong>Message:</strong><br>
            <p class="mt-2">Hello, I am interested in volunteering for your education programs. I have a background in teaching and would love to contribute to your cause. Please let me know how I can get involved.</p>
        </div>
    `;
    
    modal.show();
}

function replyToMessage(messageId) {
    currentMessageId = messageId;
    // In a real implementation, fetch message details to populate reply form
    document.getElementById('replyTo').value = '<EMAIL>';
    document.getElementById('replySubject').value = 'Re: Volunteer Inquiry';
    
    const modal = new bootstrap.Modal(document.getElementById('replyModal'));
    modal.show();
}

function replyFromModal() {
    document.getElementById('viewMessageModal').querySelector('.btn-close').click();
    setTimeout(() => replyToMessage(currentMessageId), 300);
}

function sendReply() {
    const replyData = {
        to: document.getElementById('replyTo').value,
        subject: document.getElementById('replySubject').value,
        message: document.getElementById('replyMessage').value,
        markAsResolved: document.getElementById('markAsResolved').checked
    };
    
    console.log('Sending reply:', replyData);
    // Add AJAX call to send reply
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('replyModal'));
    modal.hide();
    
    // Show success message
    alert('Reply sent successfully!');
}

function updateStatus(messageId, status) {
    console.log('Update message status:', messageId, status);
    // Add AJAX call to update message status
    // After success, refresh the page or update the row
}

function deleteMessage(messageId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteMessageModal'));
    modal.show();
    
    document.getElementById('confirmDeleteMessage').onclick = function() {
        console.log('Delete message:', messageId);
        modal.hide();
        // Add AJAX call to delete message
    };
}

function markAllAsRead() {
    console.log('Mark all messages as read');
    // Add AJAX call to mark all messages as read
}

// Search functionality
document.getElementById('searchMessages').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterPriority').addEventListener('change', function() {
    console.log('Filter priority:', this.value);
});

document.getElementById('filterDate').addEventListener('change', function() {
    console.log('Filter date:', this.value);
});
</script>
@endsection 