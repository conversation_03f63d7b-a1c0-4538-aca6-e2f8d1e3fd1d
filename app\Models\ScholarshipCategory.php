<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ScholarshipCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'default_fields',
        'validation_rules',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'default_fields' => 'array',
        'validation_rules' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Boot method to auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name') && empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * Get scholarships in this category
     */
    public function scholarships()
    {
        return $this->hasMany(Scholarship::class, 'category', 'slug');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get default fields for this category
     */
    public function getDefaultFieldsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * Get validation rules for this category
     */
    public function getValidationRulesAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }
}
