@extends('layouts.admin')

@section('title', 'View Educational Resource')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ $resource->title }}</h1>
                    <p class="text-gray-600">Educational Resource Details</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.educational-resources.edit', $resource->id) }}" 
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Resource
                    </a>
                    <a href="{{ route('admin.educational-resources.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 text-white font-semibold rounded-lg shadow-md hover:bg-gray-700 transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Resources
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Basic Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                            <p class="text-gray-900">{{ $resource->title }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <p class="text-gray-900">{{ $resource->category }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($resource->type === 'video_course') bg-red-100 text-red-800
                                @elseif($resource->type === 'study_guide') bg-blue-100 text-blue-800
                                @elseif($resource->type === 'document') bg-green-100 text-green-800
                                @elseif($resource->type === 'interactive') bg-purple-100 text-purple-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst(str_replace('_', ' ', $resource->type)) }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($resource->target_audience === 'all') bg-indigo-100 text-indigo-800
                                @elseif($resource->target_audience === 'university') bg-purple-100 text-purple-800
                                @elseif($resource->target_audience === 'secondary') bg-yellow-100 text-yellow-800
                                @else bg-green-100 text-green-800 @endif">
                                {{ ucfirst($resource->target_audience) }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Difficulty Level</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($resource->difficulty_level === 'beginner') bg-green-100 text-green-800
                                @elseif($resource->difficulty_level === 'intermediate') bg-yellow-100 text-yellow-800
                                @else bg-red-100 text-red-800 @endif">
                                {{ ucfirst($resource->difficulty_level) }}
                            </span>
                        </div>
                        
                        @if($resource->duration)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                            <p class="text-gray-900">{{ $resource->duration }} minutes</p>
                        </div>
                        @endif
                    </div>
                    
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <p class="text-gray-900">{{ $resource->description }}</p>
                    </div>
                </div>

                <!-- Learning Details -->
                @if($resource->learning_objectives || $resource->prerequisites)
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Learning Details</h2>
                    
                    @if($resource->learning_objectives)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Learning Objectives</label>
                        <p class="text-gray-900">{{ $resource->learning_objectives }}</p>
                    </div>
                    @endif
                    
                    @if($resource->prerequisites)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Prerequisites</label>
                        <p class="text-gray-900">{{ $resource->prerequisites }}</p>
                    </div>
                    @endif
                </div>
                @endif

                <!-- Content Links -->
                @if($resource->video_url || $resource->content_url || $resource->file_path)
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Content & Files</h2>
                    
                    <div class="space-y-4">
                        @if($resource->video_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Video URL</label>
                            <a href="{{ $resource->video_url }}" target="_blank" 
                               class="text-green-600 hover:text-green-800 underline break-all">
                                {{ $resource->video_url }}
                            </a>
                        </div>
                        @endif
                        
                        @if($resource->content_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">External Content URL</label>
                            <a href="{{ $resource->content_url }}" target="_blank" 
                               class="text-green-600 hover:text-green-800 underline break-all">
                                {{ $resource->content_url }}
                            </a>
                        </div>
                        @endif
                        
                        @if($resource->file_path)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Resource File</label>
                            <a href="{{ asset('storage/' . $resource->file_path) }}" target="_blank" 
                               class="inline-flex items-center text-green-600 hover:text-green-800 underline">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                {{ basename($resource->file_path) }}
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Tags -->
                @if($resource->tags && count($resource->tags) > 0)
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Tags</h2>
                    <div class="flex flex-wrap gap-2">
                        @foreach($resource->tags as $tag)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                            {{ $tag }}
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Status & Metadata -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Status & Metadata</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                @if($resource->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                @endif
                                
                                @if($resource->is_featured)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Featured
                                    </span>
                                @endif
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                            <p class="text-gray-900">{{ $resource->creator ? $resource->creator->first_name . ' ' . $resource->creator->last_name : 'Unknown' }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
                            <p class="text-gray-900">{{ $resource->created_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                            <p class="text-gray-900">{{ $resource->updated_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                @if($resource->featured_image)
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Featured Image</h2>
                    <img src="{{ asset('storage/' . $resource->featured_image) }}" alt="Featured Image" 
                         class="w-full h-48 object-cover rounded-lg">
                </div>
                @endif

                <!-- Statistics -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Statistics</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Views</span>
                            <span class="text-lg font-bold text-gray-900">{{ $resource->view_count ?? 0 }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Downloads</span>
                            <span class="text-lg font-bold text-gray-900">{{ $resource->download_count ?? 0 }}</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Actions</h2>
                    
                    <div class="space-y-3">
                        <a href="{{ route('admin.educational-resources.edit', $resource->id) }}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Resource
                        </a>
                        
                        <form action="{{ route('admin.educational-resources.destroy', $resource->id) }}" method="POST" class="w-full">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 text-white font-semibold rounded-lg shadow-md hover:bg-red-700 transition-all duration-200"
                                    onclick="return confirm('Are you sure you want to delete this resource?')">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Resource
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
