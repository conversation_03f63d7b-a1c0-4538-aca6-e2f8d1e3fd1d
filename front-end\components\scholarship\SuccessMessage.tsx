'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Home, FileText } from 'lucide-react'

interface SuccessMessageProps {
  onBackToForm: () => void
  onGoHome: () => void
}

export default function SuccessMessage({ onBackToForm, onGoHome }: SuccessMessageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 py-8 flex items-center justify-center">
      <div className="container mx-auto px-4 max-w-2xl">
        <Card className="shadow-xl border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-green-500 to-blue-500 text-white text-center py-12">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-white bg-opacity-20 rounded-full">
                <CheckCircle className="h-16 w-16 text-white" />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold mb-2">Application Submitted Successfully!</CardTitle>
            <CardDescription className="text-green-100 text-lg">
              Your scholarship application has been received and is being processed
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-8 text-center">
            <div className="space-y-6">
              <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
                <h3 className="font-semibold text-blue-900 mb-2">What happens next?</h3>
                <ul className="text-blue-800 text-left space-y-2">
                  <li className="flex items-start">
                    <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Your application will be reviewed by our scholarship committee
                  </li>
                  <li className="flex items-start">
                    <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    You will receive an email confirmation within 24 hours
                  </li>
                  <li className="flex items-start">
                    <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    The review process typically takes 5-10 business days
                  </li>
                  <li className="flex items-start">
                    <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    You will be notified of the decision via email and SMS
                  </li>
                </ul>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                <p className="text-yellow-800 font-medium">
                  <strong>Important:</strong> Please keep your phone and email accessible for any follow-up communications.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <Button 
                  onClick={onBackToForm}
                  variant="outline"
                  className="flex items-center justify-center px-6 py-3"
                >
                  <FileText className="mr-2 h-5 w-5" />
                  Submit Another Application
                </Button>
                
                <Button 
                  onClick={onGoHome}
                  className="bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center px-6 py-3"
                >
                  <Home className="mr-2 h-5 w-5" />
                  Go to Dashboard
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
