@extends('layouts.dashboard')

@section('title', 'Create Support Ticket')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support', 'active' => true]
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <a href="{{ route('partner.support.index') }}" class="text-green-600 hover:text-green-700 mr-4">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Create Support Ticket</h1>
                            <p class="text-gray-600">Submit a new support request and we'll get back to you within 24 hours</p>
                        </div>
                    </div>
                </div>

                <!-- Create Ticket Form -->
                <div class="max-w-4xl mx-auto">
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-900">Support Request Details</h3>
                            <p class="text-gray-600 mt-1">Please provide as much detail as possible to help us assist you quickly</p>
                        </div>

                        <form action="{{ route('partner.support.store') }}" method="POST" enctype="multipart/form-data" class="p-6">
                            @csrf

                            <!-- Subject -->
                            <div class="mb-6">
                                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                    Subject <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="subject" name="subject" value="{{ old('subject') }}" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-300 @error('subject') border-red-500 @enderror"
                                       placeholder="Brief description of your issue or question" required>
                                @error('subject')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Category and Priority -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <select id="category" name="category" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-300 @error('category') border-red-500 @enderror" required>
                                        <option value="">Select a category</option>
                                        <option value="technical" {{ old('category') == 'technical' ? 'selected' : '' }}>Technical Support</option>
                                        <option value="billing" {{ old('category') == 'billing' ? 'selected' : '' }}>Billing & Payments</option>
                                        <option value="general" {{ old('category') == 'general' ? 'selected' : '' }}>General Inquiry</option>
                                        <option value="feature_request" {{ old('category') == 'feature_request' ? 'selected' : '' }}>Feature Request</option>
                                        <option value="bug_report" {{ old('category') == 'bug_report' ? 'selected' : '' }}>Bug Report</option>
                                    </select>
                                    @error('category')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                        Priority <span class="text-red-500">*</span>
                                    </label>
                                    <select id="priority" name="priority" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-300 @error('priority') border-red-500 @enderror" required>
                                        <option value="">Select priority level</option>
                                        <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low - General question</option>
                                        <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>Medium - Standard issue</option>
                                        <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High - Important issue</option>
                                        <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent - Critical issue</option>
                                    </select>
                                    @error('priority')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-6">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea id="description" name="description" rows="6" 
                                          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-300 @error('description') border-red-500 @enderror"
                                          placeholder="Please provide detailed information about your issue, including steps to reproduce if applicable..." required>{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Minimum 10 characters required</p>
                            </div>

                            <!-- File Attachments -->
                            <div class="mb-6">
                                <label for="attachments" class="block text-sm font-medium text-gray-700 mb-2">
                                    Attachments (Optional)
                                </label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-green-400 transition-colors duration-300">
                                    <input type="file" id="attachments" name="attachments[]" multiple 
                                           class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <label for="attachments" class="cursor-pointer">
                                        <div class="text-gray-400 mb-2">
                                            <i class="fas fa-cloud-upload-alt text-3xl"></i>
                                        </div>
                                        <p class="text-gray-600 font-medium">Click to upload files or drag and drop</p>
                                        <p class="text-sm text-gray-500 mt-1">PDF, DOC, DOCX, JPG, PNG (Max 5MB each)</p>
                                    </label>
                                </div>
                                <div id="file-list" class="mt-3 space-y-2"></div>
                                @error('attachments.*')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Submit Buttons -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                                <button type="submit" 
                                        class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Submit Ticket
                                </button>
                                <a href="{{ route('partner.support.index') }}" 
                                   class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-3 rounded-xl font-semibold transition-colors duration-300 text-center">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Help Tips -->
                    <div class="mt-8 bg-blue-50 rounded-2xl p-6">
                        <h4 class="text-lg font-semibold text-blue-900 mb-4">
                            <i class="fas fa-lightbulb mr-2"></i>Tips for Better Support
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-blue-600 mr-2 mt-0.5"></i>
                                <span>Be specific about the issue you're experiencing</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-blue-600 mr-2 mt-0.5"></i>
                                <span>Include steps to reproduce the problem</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-blue-600 mr-2 mt-0.5"></i>
                                <span>Attach screenshots or relevant documents</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-blue-600 mr-2 mt-0.5"></i>
                                <span>Mention your browser and device information</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }

    // File upload handling
    const fileInput = document.getElementById('attachments');
    const fileList = document.getElementById('file-list');

    fileInput.addEventListener('change', function() {
        fileList.innerHTML = '';
        
        if (this.files.length > 0) {
            Array.from(this.files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
                fileItem.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-file text-gray-400 mr-2"></i>
                        <span class="text-sm text-gray-700">${file.name}</span>
                        <span class="text-xs text-gray-500 ml-2">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    </div>
                    <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                fileList.appendChild(fileItem);
            });
        }
    });

    function removeFile(index) {
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        fileInput.files = dt.files;
        fileInput.dispatchEvent(new Event('change'));
    }

    // Drag and drop functionality
    const dropZone = document.querySelector('[for="attachments"]').parentElement;
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-green-400', 'bg-green-50');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('border-green-400', 'bg-green-50');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-green-400', 'bg-green-50');
        
        const files = e.dataTransfer.files;
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    });
</script>
@endpush
@endsection
