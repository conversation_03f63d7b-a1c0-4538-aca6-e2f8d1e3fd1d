# HLTKKQ Foundation - Static HTML Version

This is a standalone HTML/CSS/JavaScript version of the HLTKKQ Foundation NGO platform, converted from the original Next.js/React application.

## Features

- **Authentication**: Login and registration forms with API integration
- **Multi-User Dashboards**: Admin, Partner Organization, Student, and Volunteer dashboards
- **Responsive Design**: Mobile-first design using Tailwind CSS
- **API Integration**: Vanilla JavaScript API client for Laravel backend
- **Complete Functionality**: All features from the original React application

## Structure

```
html-version/
├── index.html                 # Homepage
├── auth/
│   ├── login.html            # Login page
│   └── register.html         # Registration page
├── dashboard/
│   ├── admin.html            # Admin dashboard
│   ├── partner.html          # Partner organization dashboard
│   ├── student.html          # Student dashboard
│   └── volunteer.html        # Volunteer dashboard
├── pages/
│   ├── about.html
│   ├── blog.html
│   ├── contact.html
│   ├── donate.html
│   ├── projects.html
│   ├── scholarships.html
│   └── team.html
├── assets/
│   ├── css/
│   │   ├── tailwind.css      # Tailwind CSS
│   │   └── custom.css        # Custom styles
│   ├── js/
│   │   ├── api.js            # API client
│   │   ├── auth.js           # Authentication logic
│   │   ├── dashboard.js      # Dashboard functionality
│   │   └── main.js           # Main application logic
│   └── img/                  # Images and assets
└── components/
    ├── navigation.html       # Navigation component
    ├── footer.html           # Footer component
    └── modals.html           # Modal components
```

## Setup

1. Ensure your Laravel backend is running on `http://localhost:8000`
2. Open `index.html` in a web browser or serve via a local web server
3. All functionality should work identically to the React version

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Progressive enhancement for older browsers
