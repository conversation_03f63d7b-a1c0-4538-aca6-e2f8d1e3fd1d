@extends('api-docs.layout')

@section('title', 'Authentication')

@section('content')
<div class="row">
    <div class="col-12">
        <h1 class="display-5 fw-bold text-primary mb-4">
            <i class="fas fa-key me-3"></i>
            Authentication
        </h1>
        <p class="lead text-muted mb-5">Learn how to authenticate with the NGO Platform API using Bearer tokens.</p>
    </div>
</div>

<!-- Overview -->
<div class="row mb-5">
    <div class="col-12">
        <div class="api-card card">
            <div class="card-body">
                <h3 class="card-title">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    Authentication Overview
                </h3>
                <p class="card-text">
                    The NGO Platform API uses <strong>Bearer Token authentication</strong> powered by Laravel Sanctum. 
                    All protected endpoints require a valid access token in the Authorization header.
                </p>
                
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Security Note:</strong> Tokens have configurable expiration times and are automatically 
                    revoked on logout. Rate limiting is applied based on user roles.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registration -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>User Registration</h3>
            <p class="text-muted">Create a new user account and receive an access token.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="method-badge method-post">POST</span>
                            <code class="ms-2">/api/v1/register</code>
                        </div>
                        <span class="role-badge role-public">public</span>
                    </div>
                    
                    <h5>Request Body</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-json">{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "password_confirmation": "securepassword123",
  "role": "student",
  "phone": "+1234567890",
  "date_of_birth": "1995-06-15",
  "state": "Lagos",
  "lga": "Ikeja"
}</code></pre>
                    </div>
                    
                    <h5 class="mt-4">Response</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-json">{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "student",
      "email_verified_at": null,
      "created_at": "2024-01-15T10:30:00.000000Z"
    },
    "token": "1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>User Login</h3>
            <p class="text-muted">Authenticate with email and password to receive an access token.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="method-badge method-post">POST</span>
                            <code class="ms-2">/api/v1/login</code>
                        </div>
                        <span class="role-badge role-public">public</span>
                    </div>
                    
                    <h5>Request Body</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-json">{
  "email": "<EMAIL>",
  "password": "securepassword123"
}</code></pre>
                    </div>
                    
                    <h5 class="mt-4">Response</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-json">{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "student",
      "email_verified_at": "2024-01-15T11:00:00.000000Z"
    },
    "token": "2|def456ghi789jkl012mno345pqr678stu901vwx234yz567abc"
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Using Tokens -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>Using Access Tokens</h3>
            <p class="text-muted">Include the Bearer token in the Authorization header for all protected requests.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <h5>Header Format</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-http">Authorization: Bearer YOUR_ACCESS_TOKEN
Accept: application/json
Content-Type: application/json</code></pre>
                    </div>
                    
                    <h5 class="mt-4">Example Request</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-bash">curl -X GET {{ url('/api/v1/user') }} \
  -H "Authorization: Bearer 2|def456ghi789jkl012mno345pqr678stu901vwx234yz567abc" \
  -H "Accept: application/json"</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Logout -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>Logout</h3>
            <p class="text-muted">Revoke the current access token and logout the user.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="method-badge method-post">POST</span>
                            <code class="ms-2">/api/v1/logout</code>
                        </div>
                        <span class="role-badge role-user">authenticated</span>
                    </div>
                    
                    <h5>Headers</h5>
                    <div class="code-block">
                        <pre><code class="language-http">Authorization: Bearer YOUR_ACCESS_TOKEN</code></pre>
                    </div>
                    
                    <h5 class="mt-4">Response</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-json">{
  "success": true,
  "message": "Logout successful"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Handling -->
<div class="row">
    <div class="col-12">
        <div class="api-card card">
            <div class="card-body">
                <h3 class="card-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Common Authentication Errors
                </h3>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Error Code</th>
                                <th>HTTP Status</th>
                                <th>Description</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>AUTH_001</code></td>
                                <td><span class="badge bg-danger">401</span></td>
                                <td>Invalid credentials</td>
                                <td>Check email and password</td>
                            </tr>
                            <tr>
                                <td><code>AUTH_002</code></td>
                                <td><span class="badge bg-danger">401</span></td>
                                <td>Token expired</td>
                                <td>Login again to get new token</td>
                            </tr>
                            <tr>
                                <td><code>AUTH_003</code></td>
                                <td><span class="badge bg-danger">401</span></td>
                                <td>Token invalid</td>
                                <td>Provide valid Bearer token</td>
                            </tr>
                            <tr>
                                <td><code>AUTH_004</code></td>
                                <td><span class="badge bg-warning">403</span></td>
                                <td>Account not verified</td>
                                <td>Verify email address</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
