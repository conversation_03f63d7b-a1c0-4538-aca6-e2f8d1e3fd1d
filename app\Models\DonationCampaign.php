<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class DonationCampaign extends Model
{
    use HasFactory, HasSlug;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'description',
        'goal_amount',
        'raised_amount',
        'currency',
        'category',
        'start_date',
        'end_date',
        'featured_image',
        'image',
        'gallery',
        'status',
        'featured',
        'impact_description',
        'milestones',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'goal_amount' => 'decimal:2',
        'raised_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'featured' => 'boolean',
        'gallery' => 'array',
        'milestones' => 'array',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the user who created the campaign.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the donations for this campaign.
     */
    public function donations()
    {
        return $this->hasMany(Donation::class, 'campaign_id');
    }

    /**
     * Get completed donations for this campaign.
     */
    public function completedDonations()
    {
        return $this->hasMany(Donation::class, 'campaign_id')->completed();
    }

    /**
     * Check if campaign is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->start_date <= now()->toDateString() &&
               $this->end_date >= now()->toDateString();
    }

    /**
     * Check if campaign is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed' || $this->end_date < now()->toDateString();
    }

    /**
     * Check if goal is reached.
     */
    public function isGoalReached(): bool
    {
        return $this->raised_amount >= $this->goal_amount;
    }

    /**
     * Get progress percentage.
     */
    public function getProgressPercentageAttribute(): float
    {
        if ($this->goal_amount <= 0) {
            return 0;
        }
        return min(100, ($this->raised_amount / $this->goal_amount) * 100);
    }

    /**
     * Get remaining amount to reach goal.
     */
    public function getRemainingAmountAttribute(): float
    {
        return max(0, $this->goal_amount - $this->raised_amount);
    }

    /**
     * Get days remaining.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->end_date < now()->toDateString()) {
            return 0;
        }
        return now()->diffInDays($this->end_date);
    }

    /**
     * Scope a query to only include active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('start_date', '<=', now()->toDateString())
                    ->where('end_date', '>=', now()->toDateString());
    }

    /**
     * Scope a query to only include completed campaigns.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed')
                    ->orWhere('end_date', '<', now()->toDateString());
    }

    /**
     * Add donation amount to raised amount.
     */
    public function addDonation(float $amount): void
    {
        $this->increment('raised_amount', $amount);
    }

    /**
     * Remove donation amount from raised amount.
     */
    public function removeDonation(float $amount): void
    {
        $this->decrement('raised_amount', $amount);
    }
}
