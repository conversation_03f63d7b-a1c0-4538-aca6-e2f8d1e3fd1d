"use client"


import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, Users, Clock, Download, Calendar, MapPin, Star, FileText, Video, Play } from "lucide-react"

export default function LearnWithUsPage() {
  const courses = [
    {
      id: 1,
      title: "Basic Computer Literacy",
      description: "Learn fundamental computer skills including typing, internet browsing, and basic software usage.",
      duration: "4 weeks",
      level: "Beginner",
      students: 245,
      rating: 4.8,
      category: "Technology",
      instructor: "Dr. <PERSON>",
      price: "Free",
      modules: 8,
      certificate: true,
    },
    {
      id: 2,
      title: "English Language Proficiency",
      description: "Improve your English speaking, reading, and writing skills for better communication.",
      duration: "8 weeks",
      level: "Intermediate",
      students: 189,
      rating: 4.9,
      category: "Language",
      instructor: "<PERSON><PERSON>",
      price: "Free",
      modules: 12,
      certificate: true,
    },
    {
      id: 3,
      title: "Mathematics Fundamentals",
      description: "Master basic mathematics concepts essential for everyday life and further education.",
      duration: "6 weeks",
      level: "Beginner",
      students: 156,
      rating: 4.7,
      category: "Mathematics",
      instructor: "Dr. Fatima Usman",
      price: "Free",
      modules: 10,
      certificate: true,
    },
    {
      id: 4,
      title: "Entrepreneurship Basics",
      description: "Learn how to start and manage a small business in your community.",
      duration: "5 weeks",
      level: "Intermediate",
      students: 98,
      rating: 4.6,
      category: "Business",
      instructor: "Engr. Ibrahim Musa",
      price: "Free",
      modules: 8,
      certificate: true,
    },
  ]

  const workshops = [
    {
      title: "Digital Literacy Workshop",
      date: "March 15, 2024",
      time: "10:00 AM - 2:00 PM",
      location: "Lagos Learning Center",
      spots: 25,
      available: 8,
      type: "In-Person",
    },
    {
      title: "Women in Leadership",
      date: "March 22, 2024",
      time: "9:00 AM - 4:00 PM",
      location: "Abuja Community Hall",
      spots: 30,
      available: 12,
      type: "In-Person",
    },
  ]

  const resources = [
    {
      title: "Study Guides Collection",
      description: "Comprehensive study materials for various subjects",
      type: "PDF",
      downloads: 1250,
      icon: FileText,
      size: "25 MB",
    },
    {
      title: "Educational Videos",
      description: "Video tutorials and lectures in local languages",
      type: "Video",
      downloads: 890,
      icon: Video,
      size: "2.5 GB",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">

      <main>
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Learn With Us</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Empowering Nigerian Minds</h1>
              <p className="text-xl text-green-100 max-w-3xl mx-auto">
                Discover our comprehensive educational programs designed to empower communities across Nigeria with
                knowledge and skills for a brighter future.
              </p>

            </div>
          </div>
        </section>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <Tabs defaultValue="courses" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8 bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700">
              <TabsTrigger value="courses" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                Courses
              </TabsTrigger>
              <TabsTrigger
                value="workshops"
                className="data-[state=active]:bg-green-600 data-[state=active]:text-white"
              >
                Workshops
              </TabsTrigger>
              <TabsTrigger
                value="resources"
                className="data-[state=active]:bg-green-600 data-[state=active]:text-white"
              >
                Resources
              </TabsTrigger>
            </TabsList>

            {/* Courses Tab */}
            <TabsContent value="courses" className="space-y-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-green-800 dark:text-green-200 mb-4">Our Courses</h2>
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  Structured learning paths designed to build essential skills and knowledge for personal and
                  professional growth across Nigeria.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {courses.map((course) => (
                  <Card
                    key={course.id}
                    className="shadow-lg border-green-200 dark:border-green-800 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  >
                    <div className="relative">
                      <div className="h-48 bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
                        <BookOpen className="h-16 w-16 text-white" />
                      </div>
                      <Badge className="absolute top-2 right-2 bg-green-600 text-white">{course.category}</Badge>
                      <Badge className="absolute top-2 left-2 bg-amber-500 text-white">{course.price}</Badge>
                    </div>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg text-green-800 dark:text-green-200 line-clamp-2">
                        {course.title}
                      </CardTitle>
                      <CardDescription className="text-sm line-clamp-3">{course.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-4">
                      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {course.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          {course.students}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {course.level}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">{course.rating}</span>
                        </div>
                      </div>

                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <p>Instructor: {course.instructor}</p>
                        <p>{course.modules} modules • Certificate included</p>
                      </div>

                      <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                        <Play className="h-4 w-4 mr-2" />
                        Start Learning
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Workshops Tab */}
            <TabsContent value="workshops" className="space-y-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-green-800 dark:text-green-200 mb-4">Upcoming Workshops</h2>
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  Join our interactive workshops for hands-on learning experiences and community networking across
                  Nigeria.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {workshops.map((workshop, index) => (
                  <Card
                    key={index}
                    className="shadow-lg border-green-200 dark:border-green-800 hover:shadow-xl transition-shadow duration-300"
                  >
                    <CardHeader>
                      <div className="flex justify-between items-start mb-2">
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{workshop.title}</CardTitle>
                        <Badge
                          variant={workshop.type === "Virtual" ? "secondary" : "default"}
                          className={workshop.type === "Virtual" ? "bg-blue-500" : "bg-green-500"}
                        >
                          {workshop.type}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <Calendar className="h-4 w-4" />
                        <span>{workshop.date}</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <Clock className="h-4 w-4" />
                        <span>{workshop.time}</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <MapPin className="h-4 w-4" />
                        <span>{workshop.location}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {workshop.available} of {workshop.spots} spots available
                        </span>
                        <Badge variant={workshop.available > 5 ? "secondary" : "destructive"}>
                          {workshop.available > 5 ? "Available" : "Limited"}
                        </Badge>
                      </div>
                      <Button className="w-full bg-green-600 hover:bg-green-700 text-white">Register Now</Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Resources Tab */}
            <TabsContent value="resources" className="space-y-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-green-800 dark:text-green-200 mb-4">Learning Resources</h2>
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  Access our comprehensive library of educational materials, available for free download to support
                  learners across Nigeria.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {resources.map((resource, index) => (
                  <Card
                    key={index}
                    className="shadow-lg border-green-200 dark:border-green-800 hover:shadow-xl transition-shadow duration-300"
                  >
                    <CardHeader className="text-center">
                      <div className="flex justify-center mb-4">
                        <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                          <resource.icon className="h-8 w-8 text-green-600 dark:text-green-400" />
                        </div>
                      </div>
                      <CardTitle className="text-lg text-green-800 dark:text-green-200">{resource.title}</CardTitle>
                      <CardDescription className="text-sm">{resource.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="text-center space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <Badge
                          variant="outline"
                          className="border-green-300 text-green-700 dark:border-green-600 dark:text-green-300"
                        >
                          {resource.type}
                        </Badge>
                        <span className="text-gray-600 dark:text-gray-400">{resource.size}</span>
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">{resource.downloads} downloads</div>
                      <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
