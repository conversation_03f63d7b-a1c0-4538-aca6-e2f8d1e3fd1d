@extends('layouts.admin')

@section('title', 'Newsletter Campaigns')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Newsletter Campaigns</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.newsletter.index') }}">Newsletter</a></li>
                        <li class="breadcrumb-item active">Campaigns</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.newsletter.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Campaign
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?status=draft">Draft</a></li>
                            <li><a class="dropdown-item" href="?status=scheduled">Scheduled</a></li>
                            <li><a class="dropdown-item" href="?status=sent">Sent</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('admin.newsletter.campaigns') }}">All Campaigns</a></li>
                        </ul>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshPage()">
                        <i class="fas fa-refresh me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Campaigns</h5>
                            <h3 class="mb-0">{{ $stats['total'] ?? 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope-open fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Sent Campaigns</h5>
                            <h3 class="mb-0">{{ $stats['sent'] ?? 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-paper-plane fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Scheduled</h5>
                            <h3 class="mb-0">{{ $stats['scheduled'] ?? 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Draft</h5>
                            <h3 class="mb-0">{{ $stats['draft'] ?? 0 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-edit fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Newsletter Campaigns
                    </h6>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Campaign</th>
                                    <th>Subject</th>
                                    <th>Recipients</th>
                                    <th>Status</th>
                                    <th>Scheduled/Sent</th>
                                    <th>Open Rate</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($campaigns as $campaign)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="bg-{{
                                                    $campaign->status === 'sent' ? 'success' :
                                                    ($campaign->status === 'scheduled' ? 'warning' : 'secondary')
                                                }} rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-{{
                                                        $campaign->status === 'sent' ? 'paper-plane' :
                                                        ($campaign->status === 'scheduled' ? 'clock' : 'edit')
                                                    }} text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $campaign->title }}</h6>
                                                <small class="text-muted">Created {{ $campaign->created_at->diffForHumans() }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ Str::limit($campaign->subject, 50) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ number_format($campaign->actual_recipients ?? $campaign->estimated_recipients) }}
                                            {{ ucfirst($campaign->recipient_type) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{
                                            $campaign->status === 'sent' ? 'success' :
                                            ($campaign->status === 'scheduled' ? 'warning' : 'secondary')
                                        }}">
                                            {{ ucfirst($campaign->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($campaign->status === 'sent' && $campaign->sent_at)
                                            <small class="text-muted">{{ $campaign->sent_at->format('M d, Y g:i A') }}</small>
                                        @elseif($campaign->status === 'scheduled' && $campaign->scheduled_at)
                                            <small class="text-muted">{{ $campaign->scheduled_at->format('M d, Y g:i A') }}</small>
                                        @else
                                            <small class="text-muted">Not scheduled</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($campaign->status === 'sent' && $campaign->actual_recipients > 0)
                                            <span class="text-success fw-bold">{{ number_format($campaign->open_rate, 1) }}%</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                @if($campaign->status === 'draft')
                                                    <li><a class="dropdown-item" href="{{ route('admin.newsletter.campaigns.edit', $campaign->id) }}"><i class="fas fa-edit me-2"></i>Continue Editing</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>Preview</a></li>
                                                @elseif($campaign->status === 'scheduled')
                                                    <li><a class="dropdown-item" href="{{ route('admin.newsletter.campaigns.edit', $campaign->id) }}"><i class="fas fa-edit me-2"></i>Edit</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>Preview</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-paper-plane me-2"></i>Send Now</a></li>
                                                @else
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>View Details</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-chart-bar me-2"></i>Analytics</a></li>
                                                @endif
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>Duplicate</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCampaign({{ $campaign->id }})"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No newsletter campaigns found</p>
                                        <a href="{{ route('admin.newsletter.campaigns.create') }}" class="btn btn-primary">Create Your First Campaign</a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if(method_exists($campaigns, 'links'))
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            Showing {{ $campaigns->firstItem() }} to {{ $campaigns->lastItem() }} of {{ $campaigns->total() }} campaigns
                        </div>
                        <div>
                            {{ $campaigns->links() }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCampaignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this newsletter campaign? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteCampaign">Delete Campaign</button>
            </div>
        </div>
    </div>
</div>

<script>
function refreshPage() {
    window.location.reload();
}

function deleteCampaign(campaignId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteCampaignModal'));
    modal.show();

    document.getElementById('confirmDeleteCampaign').onclick = function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/newsletter/campaigns/${campaignId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();

        modal.hide();
    };
}
</script>
@endsection