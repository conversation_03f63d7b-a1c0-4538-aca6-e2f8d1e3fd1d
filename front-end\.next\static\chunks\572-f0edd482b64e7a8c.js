(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[572],{488:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},1362:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var n=r(2115),o=(e,t,r,n,o,a,l,i)=>{let s=document.documentElement,u=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(s.classList.remove(...n),s.classList.add(a&&a[t]?a[t]:t)):s.setAttribute(e,t)}),r=t,i&&u.includes(r)&&(s.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},a=["light","dark"],l="(prefers-color-scheme: dark)",i=n.createContext(void 0),s=e=>n.useContext(i)?n.createElement(n.Fragment,null,e.children):n.createElement(d,{...e}),u=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:d="theme",themes:h=u,defaultTheme:v=o?"system":"light",attribute:g="data-theme",value:y,children:w,nonce:x,scriptProps:b}=e,[C,k]=n.useState(()=>p(d,v)),[M,j]=n.useState(()=>"system"===C?m():C),R=y?Object.values(y):h,D=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=m());let n=y?y[t]:t,l=r?f(x):null,i=document.documentElement,u=e=>{"class"===e?(i.classList.remove(...R),n&&i.classList.add(n)):e.startsWith("data-")&&(n?i.setAttribute(e,n):i.removeAttribute(e))};if(Array.isArray(g)?g.forEach(u):u(g),s){let e=a.includes(v)?v:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==l||l()},[x]),A=n.useCallback(e=>{let t="function"==typeof e?e(C):e;k(t);try{localStorage.setItem(d,t)}catch(e){}},[C]),_=n.useCallback(e=>{j(m(e)),"system"===C&&o&&!t&&D("system")},[C,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(_),_(e),()=>e.removeListener(_)},[_]),n.useEffect(()=>{let e=e=>{e.key===d&&(e.newValue?k(e.newValue):A(v))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),n.useEffect(()=>{D(null!=t?t:C)},[t,C]);let E=n.useMemo(()=>({theme:C,setTheme:A,forcedTheme:t,resolvedTheme:"system"===C?M:C,themes:o?[...h,"system"]:h,systemTheme:o?M:void 0}),[C,A,t,M,o,h]);return n.createElement(i.Provider,{value:E},n.createElement(c,{forcedTheme:t,storageKey:d,attribute:g,enableSystem:o,enableColorScheme:s,defaultTheme:v,value:y,themes:h,nonce:x,scriptProps:b}),w)},c=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:l,enableColorScheme:i,defaultTheme:s,value:u,themes:d,nonce:c,scriptProps:p}=e,f=JSON.stringify([a,r,s,t,d,u,l,i]).slice(1,-1);return n.createElement("script",{...p,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(f,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},f=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},1366:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1497:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},1976:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2894:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2925:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},3052:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4699:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4783:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},5452:(e,t,r)=>{"use strict";r.d(t,{UC:()=>et,VY:()=>en,ZL:()=>$,bL:()=>J,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>Q});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(1285),s=r(5845),u=r(9178),d=r(7900),c=r(4378),p=r(8905),f=r(3655),m=r(2293),h=r(3795),v=r(8168),g=r(9708),y=r(5155),w="Dialog",[x,b]=(0,l.A)(w),[C,k]=x(w),M=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=n.useRef(null),c=n.useRef(null),[p=!1,f]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,y.jsx)(C,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};M.displayName=w;var j="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=k(j,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=j;var D="DialogPortal",[A,_]=x(D,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=k(D,t);return(0,y.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=D;var P="DialogOverlay",I=n.forwardRef((e,t)=>{let r=_(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(P,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(N,{...o,ref:t})}):null});I.displayName=P;var N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(P,r);return(0,y.jsx)(h.A,{as:g.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),T="DialogContent",O=n.forwardRef((e,t)=>{let r=_(T,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(T,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(S,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});O.displayName=T;var S=n.forwardRef((e,t)=>{let r=k(T,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=k(T,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=k(T,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:c.titleId}),(0,y.jsx)(Y,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(G,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});K.displayName=G;var B="DialogDescription",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(B,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});z.displayName=B;var q="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=k(q,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}V.displayName=q;var H="DialogTitleWarning",[Z,W]=(0,l.q)(H,{contentName:T,titleName:G,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,r=W(H),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=W("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},J=M,Q=R,$=E,ee=I,et=O,er=K,en=z,eo=V},5684:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6785:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},8175:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8698:(e,t,r)=>{"use strict";r.d(t,{H_:()=>e6,UC:()=>e2,YJ:()=>e9,q7:()=>e4,VF:()=>e7,JU:()=>e5,ZL:()=>e1,z6:()=>e8,hN:()=>e3,bL:()=>e$,wv:()=>te,Pb:()=>tt,G5:()=>tn,ZP:()=>tr,l9:()=>e0});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(5845),s=r(3655),u=r(2284),d=r(4315),c=r(9178),p=r(2293),f=r(7900),m=r(1285),h=r(5152),v=r(4378),g=r(8905),y=r(9196),w=r(9708),x=r(9033),b=r(8168),C=r(3795),k=r(5155),M=["Enter"," "],j=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...j],D={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_="Menu",[E,P,I]=(0,u.N)(_),[N,T]=(0,l.A)(_,[I,h.Bk,y.RG]),O=(0,h.Bk)(),S=(0,y.RG)(),[F,L]=N(_),[G,K]=N(_),B=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,s=O(t),[u,c]=n.useState(null),p=n.useRef(!1),f=(0,x.c)(l),m=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,k.jsx)(h.bL,{...s,children:(0,k.jsx)(F,{scope:t,open:r,onOpenChange:f,content:u,onContentChange:c,children:(0,k.jsx)(G,{scope:t,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:i,children:o})})})};B.displayName=_;var z=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=O(r);return(0,k.jsx)(h.Mz,{...o,...n,ref:t})});z.displayName="MenuAnchor";var q="MenuPortal",[V,U]=N(q,{forceMount:void 0}),H=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=L(q,t);return(0,k.jsx)(V,{scope:t,forceMount:r,children:(0,k.jsx)(g.C,{present:r||a.open,children:(0,k.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};H.displayName=q;var Z="MenuContent",[W,X]=N(Z),Y=n.forwardRef((e,t)=>{let r=U(Z,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=L(Z,e.__scopeMenu),l=K(Z,e.__scopeMenu);return(0,k.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:n||a.open,children:(0,k.jsx)(E.Slot,{scope:e.__scopeMenu,children:l.modal?(0,k.jsx)(J,{...o,ref:t}):(0,k.jsx)(Q,{...o,ref:t})})})})}),J=n.forwardRef((e,t)=>{let r=L(Z,e.__scopeMenu),l=n.useRef(null),i=(0,a.s)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,k.jsx)($,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=L(Z,e.__scopeMenu);return(0,k.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:M,disableOutsideScroll:D,...A}=e,_=L(Z,r),E=K(Z,r),I=O(r),N=S(r),T=P(r),[F,G]=n.useState(null),B=n.useRef(null),z=(0,a.s)(t,B,_.onContentChange),q=n.useRef(0),V=n.useRef(""),U=n.useRef(0),H=n.useRef(null),X=n.useRef("right"),Y=n.useRef(0),J=D?C.A:n.Fragment,Q=D?{as:w.DX,allowPinchZoom:!0}:void 0,$=e=>{var t,r;let n=V.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,l=null===(t=o.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),n,l),s=null===(r=o.find(e=>e.textValue===i))||void 0===r?void 0:r.ref.current;!function e(t){V.current=t,window.clearTimeout(q.current),""!==t&&(q.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};n.useEffect(()=>()=>window.clearTimeout(q.current),[]),(0,p.Oh)();let ee=n.useCallback(e=>{var t,r;return X.current===(null===(t=H.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e].x,i=t[e].y,s=t[a].x,u=t[a].y;i>n!=u>n&&r<(s-l)*(n-i)/(u-i)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(r=H.current)||void 0===r?void 0:r.area)},[]);return(0,k.jsx)(W,{scope:r,searchRef:V,onItemEnter:n.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:n.useCallback(e=>{var t;ee(e)||(null===(t=B.current)||void 0===t||t.focus(),G(null))},[ee]),onTriggerLeave:n.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:U,onPointerGraceIntentChange:n.useCallback(e=>{H.current=e},[]),children:(0,k.jsx)(J,{...Q,children:(0,k.jsx)(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(s,e=>{var t;e.preventDefault(),null===(t=B.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:M,children:(0,k.jsx)(y.bL,{asChild:!0,...N,dir:E.dir,orientation:"vertical",loop:l,currentTabStopId:F,onCurrentTabStopIdChange:G,onEntryFocus:(0,o.m)(m,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eD(_.open),"data-radix-menu-content":"",dir:E.dir,...I,...A,ref:z,style:{outline:"none",...A.style},onKeyDown:(0,o.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&$(e.key));let o=B.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(q.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let t=e.target,r=Y.current!==e.clientX;e.currentTarget.contains(t)&&r&&(X.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=Z;var ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.sG.div,{role:"group",...n,ref:t})});ee.displayName="MenuGroup";var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.sG.div,{...n,ref:t})});et.displayName="MenuLabel";var er="MenuItem",en="menu.itemSelect",eo=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...i}=e,u=n.useRef(null),d=K(er,e.__scopeMenu),c=X(er,e.__scopeMenu),p=(0,a.s)(t,u),f=n.useRef(!1);return(0,k.jsx)(ea,{...i,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(en,{bubbles:!0,cancelable:!0});e.addEventListener(en,e=>null==l?void 0:l(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;f.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=er;var ea=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:i,...u}=e,d=X(er,r),c=S(r),p=n.useRef(null),f=(0,a.s)(t,p),[m,h]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=p.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[u.children]),(0,k.jsx)(E.ItemSlot,{scope:r,disabled:l,textValue:null!=i?i:v,children:(0,k.jsx)(y.q7,{asChild:!0,...c,focusable:!l,children:(0,k.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...u,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),el=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,k.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eA(r)?"mixed":r,...a,ref:t,"data-state":e_(r),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eA(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,eu]=N(ei,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,x.c)(n);return(0,k.jsx)(es,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,k.jsx)(ee,{...o,ref:t})})});ed.displayName=ei;var ec="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eu(ec,e.__scopeMenu),l=r===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,k.jsx)(eo,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":e_(l),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=ec;var ef="MenuItemIndicator",[em,eh]=N(ef,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eh(ef,r);return(0,k.jsx)(g.C,{present:n||eA(a.checked)||!0===a.checked,children:(0,k.jsx)(s.sG.span,{...o,ref:t,"data-state":e_(a.checked)})})});ev.displayName=ef;var eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eg.displayName="MenuSeparator";var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=O(r);return(0,k.jsx)(h.i3,{...o,...n,ref:t})});ey.displayName="MenuArrow";var ew="MenuSub",[ex,eb]=N(ew),eC=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,l=L(ew,t),i=O(t),[s,u]=n.useState(null),[d,c]=n.useState(null),p=(0,x.c)(a);return n.useEffect(()=>(!1===l.open&&p(!1),()=>p(!1)),[l.open,p]),(0,k.jsx)(h.bL,{...i,children:(0,k.jsx)(F,{scope:t,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,k.jsx)(ex,{scope:t,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:s,onTriggerChange:u,children:r})})})};eC.displayName=ew;var ek="MenuSubTrigger",eM=n.forwardRef((e,t)=>{let r=L(ek,e.__scopeMenu),l=K(ek,e.__scopeMenu),i=eb(ek,e.__scopeMenu),s=X(ek,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=s,p={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,k.jsx)(z,{asChild:!0,...p,children:(0,k.jsx)(ea,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eD(r.open),...e,ref:(0,a.t)(t,i.onTriggerChange),onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(t=>{s.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{var t,n;f();let o=null===(t=r.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(n=r.content)||void 0===n?void 0:n.dataset.side,a="right"===t,l=o[a?"left":"right"],i=o[a?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&D[l.dir].includes(t.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eM.displayName=ek;var ej="MenuSubContent",eR=n.forwardRef((e,t)=>{let r=U(Z,e.__scopeMenu),{forceMount:l=r.forceMount,...i}=e,s=L(Z,e.__scopeMenu),u=K(Z,e.__scopeMenu),d=eb(ej,e.__scopeMenu),c=n.useRef(null),p=(0,a.s)(t,c);return(0,k.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:l||s.open,children:(0,k.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)($,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null===(t=c.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=A[u.dir].includes(e.key);if(t&&r){var n;s.onOpenChange(!1),null===(n=d.trigger)||void 0===n||n.focus(),e.preventDefault()}})})})})})});function eD(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function e_(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return t=>"mouse"===t.pointerType?e(t):void 0}eR.displayName=ej;var eP="DropdownMenu",[eI,eN]=(0,l.A)(eP,[T]),eT=T(),[eO,eS]=eI(eP),eF=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:s,modal:u=!0}=e,d=eT(t),c=n.useRef(null),[p=!1,f]=(0,i.i)({prop:a,defaultProp:l,onChange:s});return(0,k.jsx)(eO,{scope:t,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,k.jsx)(B,{...d,open:p,onOpenChange:f,dir:o,modal:u,children:r})})};eF.displayName=eP;var eL="DropdownMenuTrigger",eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,i=eS(eL,r),u=eT(r);return(0,k.jsx)(z,{asChild:!0,...u,children:(0,k.jsx)(s.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(t,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eL;var eK=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eT(t);return(0,k.jsx)(H,{...n,...r})};eK.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",ez=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eS(eB,r),i=eT(r),s=n.useRef(!1);return(0,k.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;s.current||null===(t=l.triggerRef.current)||void 0===t||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eB;var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ee,{...o,...n,ref:t})});eq.displayName="DropdownMenuGroup";var eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(et,{...o,...n,ref:t})});eV.displayName="DropdownMenuLabel";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eo,{...o,...n,ref:t})});eU.displayName="DropdownMenuItem";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(el,{...o,...n,ref:t})});eH.displayName="DropdownMenuCheckboxItem";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ed,{...o,...n,ref:t})});eZ.displayName="DropdownMenuRadioGroup";var eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ep,{...o,...n,ref:t})});eW.displayName="DropdownMenuRadioItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ev,{...o,...n,ref:t})});eX.displayName="DropdownMenuItemIndicator";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eg,{...o,...n,ref:t})});eY.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(ey,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eM,{...o,...n,ref:t})});eJ.displayName="DropdownMenuSubTrigger";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eT(r);return(0,k.jsx)(eR,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var e$=eF,e0=eG,e1=eK,e2=ez,e9=eq,e5=eV,e4=eU,e6=eH,e8=eZ,e3=eW,e7=eX,te=eY,tt=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,l=eT(t),[s=!1,u]=(0,i.i)({prop:n,defaultProp:a,onChange:o});return(0,k.jsx)(eC,{...l,open:s,onOpenChange:u,children:r})},tr=eJ,tn=eQ},8883:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9420:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9428:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])}}]);