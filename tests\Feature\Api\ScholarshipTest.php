<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\PartnerOrganization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ScholarshipTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /**
     * Test public can view available scholarships
     */
    public function test_public_can_view_available_scholarships()
    {
        // Create active scholarships
        Scholarship::factory()->count(3)->create([
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        // Create inactive scholarship (should not appear)
        Scholarship::factory()->create([
            'status' => 'inactive',
            'application_deadline' => now()->addMonth()
        ]);

        $response = $this->getJson('/api/v1/public-scholarships');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                            'category',
                            'amount',
                            'application_deadline',
                            'description'
                        ]
                    ]
                ])
                ->assertJsonCount(3, 'data');
    }

    /**
     * Test student can apply for university scholarship
     */
    public function test_student_can_apply_for_university_scholarship()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        $applicationData = [
            'scholarship_id' => $scholarship->id,
            'matric_number' => 'UNI/2024/001',
            'course_of_study' => 'Computer Science',
            'year_of_study' => 2,
            'cgpa' => 3.5,
            'personal_statement' => 'I am passionate about technology...',
            'documents' => [
                UploadedFile::fake()->create('transcript.pdf', 1000, 'application/pdf'),
                UploadedFile::fake()->create('admission_letter.pdf', 800, 'application/pdf')
            ]
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/student-scholarships/{$scholarship->id}/apply", $applicationData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'scholarship_id',
                        'user_id',
                        'status',
                        'application_data'
                    ]
                ]);

        $this->assertDatabaseHas('scholarship_applications', [
            'scholarship_id' => $scholarship->id,
            'user_id' => $student->id,
            'status' => 'pending'
        ]);
    }

    /**
     * Test partner organization can apply for primary/secondary scholarship
     */
    public function test_partner_organization_can_apply_for_primary_secondary_scholarship()
    {
        $partner = User::factory()->create(['role' => 'partner_organization']);
        $partnerOrg = PartnerOrganization::factory()->create(['user_id' => $partner->id]);
        $token = $partner->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        $applicationData = [
            'scholarship_id' => $scholarship->id,
            'student_name' => 'John Doe',
            'student_age' => 10,
            'student_class' => 'Primary 4',
            'parent_name' => 'Jane Doe',
            'parent_phone' => '+*************',
            'parent_email' => '<EMAIL>',
            'school_account_details' => 'Account: **********, Bank: First Bank',
            'academic_records' => UploadedFile::fake()->create('report_card.pdf', 500, 'application/pdf')
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/partner-scholarships/{$scholarship->id}/apply", $applicationData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'scholarship_id',
                        'partner_organization_id',
                        'status',
                        'student_data'
                    ]
                ]);

        $this->assertDatabaseHas('scholarship_applications', [
            'scholarship_id' => $scholarship->id,
            'partner_organization_id' => $partnerOrg->id,
            'status' => 'pending'
        ]);
    }

    /**
     * Test application validation for required fields
     */
    public function test_application_validation_for_required_fields()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        $invalidData = [
            'scholarship_id' => $scholarship->id,
            // Missing required fields
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/student-scholarships/{$scholarship->id}/apply", $invalidData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['matric_number', 'course_of_study', 'personal_statement']);
    }

    /**
     * Test cannot apply after deadline
     */
    public function test_cannot_apply_after_deadline()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'application_deadline' => now()->subDay() // Past deadline
        ]);

        $applicationData = [
            'scholarship_id' => $scholarship->id,
            'matric_number' => 'UNI/2024/001',
            'course_of_study' => 'Computer Science',
            'personal_statement' => 'I am passionate about technology...'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/student-scholarships/{$scholarship->id}/apply", $applicationData);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Application deadline has passed'
                ]);
    }

    /**
     * Test cannot apply twice for same scholarship
     */
    public function test_cannot_apply_twice_for_same_scholarship()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        // Create existing application
        ScholarshipApplication::factory()->create([
            'scholarship_id' => $scholarship->id,
            'user_id' => $student->id,
            'status' => 'pending'
        ]);

        $applicationData = [
            'scholarship_id' => $scholarship->id,
            'matric_number' => 'UNI/2024/001',
            'course_of_study' => 'Computer Science',
            'personal_statement' => 'I am passionate about technology...'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/student-scholarships/{$scholarship->id}/apply", $applicationData);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'You have already applied for this scholarship'
                ]);
    }

    /**
     * Test user can view their own applications
     */
    public function test_user_can_view_their_own_applications()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        // Create applications for this user
        ScholarshipApplication::factory()->count(2)->create([
            'user_id' => $student->id
        ]);

        // Create application for another user (should not appear)
        ScholarshipApplication::factory()->create([
            'user_id' => User::factory()->create()->id
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->getJson('/api/v1/student-scholarships/my-applications');

        $response->assertStatus(200)
                ->assertJsonCount(2, 'data');
    }

    /**
     * Test admin can view all applications
     */
    public function test_admin_can_view_all_applications()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $token = $admin->createToken('test-token')->plainTextToken;

        ScholarshipApplication::factory()->count(5)->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->getJson('/api/v1/admin/scholarship-applications');

        $response->assertStatus(200)
                ->assertJsonCount(5, 'data');
    }

    /**
     * Test admin can approve/reject applications
     */
    public function test_admin_can_approve_reject_applications()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $token = $admin->createToken('test-token')->plainTextToken;

        $application = ScholarshipApplication::factory()->create([
            'status' => 'pending'
        ]);

        // Test approval
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->putJson("/api/v1/admin/scholarship-applications/{$application->id}/approve", [
            'comments' => 'Application meets all requirements'
        ]);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('scholarship_applications', [
            'id' => $application->id,
            'status' => 'approved'
        ]);

        // Test rejection
        $application2 = ScholarshipApplication::factory()->create([
            'status' => 'pending'
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->putJson("/api/v1/admin/scholarship-applications/{$application2->id}/reject", [
            'comments' => 'Incomplete documentation'
        ]);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('scholarship_applications', [
            'id' => $application2->id,
            'status' => 'rejected'
        ]);
    }

    /**
     * Test file upload validation
     */
    public function test_file_upload_validation()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'application_deadline' => now()->addMonth()
        ]);

        $applicationData = [
            'scholarship_id' => $scholarship->id,
            'matric_number' => 'UNI/2024/001',
            'course_of_study' => 'Computer Science',
            'personal_statement' => 'I am passionate about technology...',
            'documents' => [
                UploadedFile::fake()->create('large_file.pdf', 10000, 'application/pdf'), // Too large
                UploadedFile::fake()->create('invalid.txt', 100, 'text/plain') // Wrong type
            ]
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/student-scholarships/{$scholarship->id}/apply", $applicationData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['documents']);
    }
}
