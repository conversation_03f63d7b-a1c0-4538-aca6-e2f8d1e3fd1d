"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { CreditCard, Lock, ArrowRight } from "lucide-react"

export function DonationForm() {
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState("")
  const [donationType, setDonationType] = useState("one-time")

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setCustomAmount("")
  }

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value)
    setSelected<PERSON><PERSON>(null)
  }

  const finalAmount = selectedAmount || Number.parseInt(customAmount) || 0

  return (
    <Card className="overflow-hidden shadow-xl neumorphic rounded-2xl">
      <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white">
        <CardTitle className="text-2xl text-center">Complete Your Donation</CardTitle>
      </CardHeader>
      <CardContent className="p-8">
        <Tabs value={donationType} onValueChange={setDonationType} className="space-y-8">
          <TabsList className="grid w-full grid-cols-2 rounded-xl">
            <TabsTrigger value="one-time" className="rounded-l-xl">
              One-Time Donation
            </TabsTrigger>
            <TabsTrigger value="recurring" className="rounded-r-xl">
              Monthly Giving
            </TabsTrigger>
          </TabsList>

          <TabsContent value="one-time" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Select Amount</h3>
              <RadioGroup
                value={selectedAmount?.toString() || ""}
                onValueChange={(value) => handleAmountSelect(Number.parseInt(value))}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
              >
                {[5000, 10000, 25000, 50000].map((amount) => (
                  <div key={amount} className="relative">
                    <RadioGroupItem value={amount.toString()} id={`amount-${amount}`} className="peer sr-only" />
                    <Label
                      htmlFor={`amount-${amount}`}
                      className="flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300"
                    >
                      ₦{amount.toLocaleString()}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="custom-amount" className="text-sm font-medium">
                Or enter custom amount
              </Label>
              <Input
                id="custom-amount"
                type="number"
                placeholder="Enter amount in Naira"
                value={customAmount}
                onChange={(e) => handleCustomAmountChange(e.target.value)}
                className="mt-2 rounded-xl"
              />
            </div>
          </TabsContent>

          <TabsContent value="recurring" className="space-y-6">
            <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800">
              <h3 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">Monthly Giving Program</h3>
              <p className="text-sm text-amber-700 dark:text-amber-300">
                Join our monthly giving program and provide sustained support for our ongoing projects. You can modify
                or cancel your recurring donation at any time.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Monthly Amount</h3>
              <RadioGroup
                value={selectedAmount?.toString() || ""}
                onValueChange={(value) => handleAmountSelect(Number.parseInt(value))}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
              >
                {[2000, 5000, 10000, 20000].map((amount) => (
                  <div key={amount} className="relative">
                    <RadioGroupItem value={amount.toString()} id={`monthly-${amount}`} className="peer sr-only" />
                    <Label
                      htmlFor={`monthly-${amount}`}
                      className="flex flex-col items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300"
                    >
                      <span className="font-semibold">₦{amount.toLocaleString()}</span>
                      <span className="text-xs text-muted-foreground">/month</span>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="custom-monthly" className="text-sm font-medium">
                Or enter custom monthly amount
              </Label>
              <Input
                id="custom-monthly"
                type="number"
                placeholder="Enter monthly amount in Naira"
                value={customAmount}
                onChange={(e) => handleCustomAmountChange(e.target.value)}
                className="mt-2 rounded-xl"
              />
            </div>
          </TabsContent>

          {/* Donor Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Donor Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first-name">First Name</Label>
                <Input id="first-name" placeholder="Enter your first name" className="mt-1 rounded-xl" />
              </div>
              <div>
                <Label htmlFor="last-name">Last Name</Label>
                <Input id="last-name" placeholder="Enter your last name" className="mt-1 rounded-xl" />
              </div>
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" placeholder="Enter your email" className="mt-1 rounded-xl" />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" placeholder="Enter your phone number" className="mt-1 rounded-xl" />
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Information
            </h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="card-number">Card Number</Label>
                <Input id="card-number" placeholder="1234 5678 9012 3456" className="mt-1 rounded-xl" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input id="expiry" placeholder="MM/YY" className="mt-1 rounded-xl" />
                </div>
                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input id="cvv" placeholder="123" className="mt-1 rounded-xl" />
                </div>
              </div>
            </div>
          </div>

          {/* Summary and Submit */}
          <div className="space-y-6 pt-6 border-t">
            <div className="bg-green-50 dark:bg-green-950/20 p-6 rounded-xl">
              <h3 className="font-semibold text-green-800 dark:text-green-200 mb-4">Donation Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span className="font-semibold">₦{finalAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Type:</span>
                  <span className="font-semibold">
                    {donationType === "recurring" ? "Monthly Recurring" : "One-Time"}
                  </span>
                </div>
                <div className="flex justify-between text-lg font-bold text-green-600 pt-2 border-t">
                  <span>Total:</span>
                  <span>₦{finalAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="terms" />
              <Label htmlFor="terms" className="text-sm">
                I agree to the{" "}
                <a href="#" className="text-green-600 hover:underline">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-green-600 hover:underline">
                  Privacy Policy
                </a>
              </Label>
            </div>

            <Button
              size="lg"
              className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-lg py-6 transition-all duration-200 hover:scale-105 rounded-xl"
              disabled={!finalAmount}
            >
              <Lock className="mr-2 h-5 w-5" />
              Complete Secure Donation
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>

            <p className="text-xs text-muted-foreground text-center">
              Your donation is secure and encrypted. We never store your payment information.
            </p>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
