"use client"

import { useState, useEffect } from "react"
import { PAYMENT_METHODS, DONATION_TYPES, VALIDATION_RULES, APP_CONFIG, formatCurrency, validateDonationAmount } from '@/lib/constants'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CreditCard, Lock, ArrowRight, Building2, AlertCircle, CheckCircle2, <PERSON>ader2 } from "lucide-react"
import { apiClient } from "@/lib/api"
import { useSettings } from "@/hooks/useSettings"
import { BankTransferReceipt } from "./bank-transfer-receipt"

interface DonationCampaign {
  id: number
  title: string
  description: string
  goal_amount: number
  raised_amount: number
  currency: string
  status: string
}

interface DonationFormData {
  donor_name: string
  donor_email: string
  donor_phone?: string
  amount: number
  currency?: string
  donation_type: 'one-time' | 'recurring'
  frequency?: 'monthly' | 'quarterly' | 'annually'
  purpose?: string
  campaign_id?: number
  payment_method: 'paystack' | 'bank_transfer'
  is_anonymous: boolean
  notes?: string
}

export function DonationForm() {
  const { settings, loading: settingsLoading } = useSettings()
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState("")
  const [donationType, setDonationType] = useState<"one-time" | "recurring">("one-time")
  const [campaigns, setCampaigns] = useState<DonationCampaign[]>([])
  const [campaignsLoading, setCampaignsLoading] = useState(true)
  const [formData, setFormData] = useState<DonationFormData>({
    donor_name: '',
    donor_email: '',
    donor_phone: '',
    amount: 0,
    currency: 'NGN',
    donation_type: 'one-time',
    payment_method: 'paystack',
    is_anonymous: false,
    notes: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [donationId, setDonationId] = useState<number | null>(null)
  const [termsAccepted, setTermsAccepted] = useState(false)

  // Load donation campaigns
  useEffect(() => {
    const loadCampaigns = async () => {
      try {
        setCampaignsLoading(true)
        const response = await apiClient.getDonationCampaigns()
        if (response.success && response.data) {
          const campaignsData = Array.isArray(response.data)
            ? response.data
            : response.data.data || []
          setCampaigns(campaignsData)
        }
      } catch (error) {
        console.error('Error loading campaigns:', error)
      } finally {
        setCampaignsLoading(false)
      }
    }

    loadCampaigns()
  }, [])

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setCustomAmount("")
    setFormData(prev => ({ ...prev, amount }))
  }

  const handleCustomAmountChange = (value: string) => {
    const amount = Number.parseInt(value) || 0
    setCustomAmount(value)
    setSelectedAmount(null)
    setFormData(prev => ({ ...prev, amount }))
  }

  const handleInputChange = (field: keyof DonationFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleDonationTypeChange = (value: string) => {
    const type = value as "one-time" | "recurring"
    setDonationType(type)
    setFormData(prev => ({
      ...prev,
      donation_type: type,
      frequency: type === 'recurring' ? 'monthly' : undefined
    }))
  }

  const finalAmount = selectedAmount || Number.parseInt(customAmount) || 0

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const amountValidation = validateDonationAmount(finalAmount)
    if (!amountValidation.isValid) {
      setSubmitError(amountValidation.error || 'Invalid donation amount')
      return
    }

    if (!formData.donor_name || !formData.donor_email) {
      setSubmitError('Please fill in all required fields')
      return
    }

    if (!termsAccepted) {
      setSubmitError('Please accept the terms and conditions')
      return
    }

    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const donationData = {
        ...formData,
        amount: finalAmount
      }

      const response = await apiClient.createDonation(donationData)

      if (response.success && response.data) {
        // Store donation ID for receipt upload
        setDonationId(response.data.id)

        if (formData.payment_method === 'paystack' && response.data.payment_url) {
          // Redirect to Paystack payment page
          window.location.href = response.data.payment_url
        } else {
          // Bank transfer success - show receipt upload
          setSubmitSuccess(true)
        }
      } else {
        setSubmitError(response.message || 'Failed to process donation')
      }
    } catch (error) {
      console.error('Donation submission error:', error)
      setSubmitError('An error occurred while processing your donation. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitSuccess) {
    // For bank transfers, show receipt upload component
    if (formData.payment_method === 'bank_transfer' && donationId) {
      return (
        <div className="space-y-6">
          {/* Bank Account Details */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-700">
                <Building2 className="h-5 w-5" />
                Bank Transfer Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-600">Account Name:</span>
                  <span className="font-semibold">{settings?.donation_account_name || 'Loading...'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Account Number:</span>
                  <span className="font-mono font-semibold">{settings?.donation_account_number || 'Loading...'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Bank Name:</span>
                  <span className="font-semibold">{settings?.donation_bank_name || 'Loading...'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Amount:</span>
                  <span className="font-semibold text-green-600">₦{finalAmount.toLocaleString()}</span>
                </div>
              </div>
              <Alert className="mt-4 border-blue-200 bg-blue-50">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-700">
                  Please complete your bank transfer using the details above, then upload your receipt below for verification.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Receipt Upload Component */}
          <BankTransferReceipt
            donationId={donationId}
            onSuccess={() => {
              // Receipt uploaded successfully
              console.log('Receipt uploaded successfully')
            }}
            onError={(error) => {
              console.error('Receipt upload error:', error)
            }}
          />
        </div>
      )
    }

    // For other payment methods or fallback
    return (
      <Card className="overflow-hidden shadow-xl neumorphic rounded-2xl">
        <CardContent className="p-8 text-center">
          <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-green-600 mb-4">Thank You!</h3>
          <p className="text-gray-600 mb-6">
            Your donation has been received successfully.
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-green-600 hover:bg-green-700"
          >
            Make Another Donation
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="overflow-hidden shadow-xl neumorphic rounded-2xl">
      <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white">
        <CardTitle className="text-2xl text-center">Complete Your Donation</CardTitle>
      </CardHeader>
      <CardContent className="p-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          {submitError && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-600">
                {submitError}
              </AlertDescription>
            </Alert>
          )}

          <Tabs value={donationType} onValueChange={handleDonationTypeChange} className="space-y-8">
            <TabsList className="grid w-full grid-cols-2 rounded-xl">
              <TabsTrigger value="one-time" className="rounded-l-xl">
                One-Time Donation
              </TabsTrigger>
              <TabsTrigger value="recurring" className="rounded-r-xl">
                Monthly Giving
              </TabsTrigger>
            </TabsList>

          <TabsContent value="one-time" className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Select Amount</h3>
              <RadioGroup
                value={selectedAmount?.toString() || ""}
                onValueChange={(value) => handleAmountSelect(Number.parseInt(value))}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
              >
                {[15000, 25000, 50000, 100000].map((amount) => (
                  <div key={amount} className="relative">
                    <RadioGroupItem value={amount.toString()} id={`amount-${amount}`} className="peer sr-only" />
                    <Label
                      htmlFor={`amount-${amount}`}
                      className="flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300"
                    >
                      ₦{amount.toLocaleString()}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="custom-amount" className="text-sm font-medium">
                Or enter custom amount (minimum {formatCurrency(VALIDATION_RULES.DONATION_MIN_AMOUNT)})
              </Label>
              <Input
                id="custom-amount"
                type="number"
                min={VALIDATION_RULES.DONATION_MIN_AMOUNT}
                placeholder="Enter amount in Naira"
                value={customAmount}
                onChange={(e) => handleCustomAmountChange(e.target.value)}
                className="mt-2 rounded-xl"
              />
            </div>

            {/* Campaign Selection */}
            {!campaignsLoading && campaigns.length > 0 && (
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  Support a Specific Campaign (Optional)
                </Label>
                <Select onValueChange={(value) => handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined)}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Choose a campaign or leave blank for general donation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Donation</SelectItem>
                    {campaigns.map((campaign) => (
                      <SelectItem key={campaign.id} value={campaign.id.toString()}>
                        {campaign.title} - ₦{campaign.raised_amount.toLocaleString()} / ₦{campaign.goal_amount.toLocaleString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </TabsContent>

          <TabsContent value="recurring" className="space-y-6">
            <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800">
              <h3 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">Monthly Giving Program</h3>
              <p className="text-sm text-amber-700 dark:text-amber-300">
                Join our monthly giving program and provide sustained support for our ongoing projects. You can modify
                or cancel your recurring donation at any time.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Monthly Amount</h3>
              <RadioGroup
                value={selectedAmount?.toString() || ""}
                onValueChange={(value) => handleAmountSelect(Number.parseInt(value))}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
              >
                {[15000, 25000, 50000, 100000].map((amount) => (
                  <div key={amount} className="relative">
                    <RadioGroupItem value={amount.toString()} id={`monthly-${amount}`} className="peer sr-only" />
                    <Label
                      htmlFor={`monthly-${amount}`}
                      className="flex flex-col items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300"
                    >
                      <span className="font-semibold">₦{amount.toLocaleString()}</span>
                      <span className="text-xs text-muted-foreground">/month</span>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="custom-monthly" className="text-sm font-medium">
                Or enter custom monthly amount (minimum {formatCurrency(VALIDATION_RULES.DONATION_MIN_AMOUNT)})
              </Label>
              <Input
                id="custom-monthly"
                type="number"
                min={VALIDATION_RULES.DONATION_MIN_AMOUNT}
                placeholder="Enter monthly amount in Naira"
                value={customAmount}
                onChange={(e) => handleCustomAmountChange(e.target.value)}
                className="mt-2 rounded-xl"
              />
            </div>

            <div>
              <Label className="text-sm font-medium mb-2 block">Frequency</Label>
              <Select
                value={formData.frequency || 'monthly'}
                onValueChange={(value) => handleInputChange('frequency', value as 'monthly' | 'quarterly' | 'annually')}
              >
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="annually">Annually</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Campaign Selection for Recurring */}
            {!campaignsLoading && campaigns.length > 0 && (
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  Support a Specific Campaign (Optional)
                </Label>
                <Select onValueChange={(value) => handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined)}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Choose a campaign or leave blank for general donation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Donation</SelectItem>
                    {campaigns.map((campaign) => (
                      <SelectItem key={campaign.id} value={campaign.id.toString()}>
                        {campaign.title} - ₦{campaign.raised_amount.toLocaleString()} / ₦{campaign.goal_amount.toLocaleString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </TabsContent>

          {/* Donor Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Donor Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="donor-name">Full Name <span className="text-red-500">*</span></Label>
                <Input
                  id="donor-name"
                  placeholder="Enter your full name"
                  value={formData.donor_name}
                  onChange={(e) => handleInputChange('donor_name', e.target.value)}
                  className="mt-1 rounded-xl"
                  required
                />
              </div>
              <div>
                <Label htmlFor="donor-email">Email Address <span className="text-red-500">*</span></Label>
                <Input
                  id="donor-email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.donor_email}
                  onChange={(e) => handleInputChange('donor_email', e.target.value)}
                  className="mt-1 rounded-xl"
                  required
                />
              </div>
              <div>
                <Label htmlFor="donor-phone">Phone Number</Label>
                <Input
                  id="donor-phone"
                  placeholder="Enter your phone number"
                  value={formData.donor_phone}
                  onChange={(e) => handleInputChange('donor_phone', e.target.value)}
                  className="mt-1 rounded-xl"
                />
              </div>
              <div>
                <Label htmlFor="purpose">Purpose (Optional)</Label>
                <Input
                  id="purpose"
                  placeholder="e.g., Education, Healthcare, General"
                  value={formData.purpose || ''}
                  onChange={(e) => handleInputChange('purpose', e.target.value)}
                  className="mt-1 rounded-xl"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="anonymous"
                checked={formData.is_anonymous}
                onCheckedChange={(checked) => handleInputChange('is_anonymous', checked)}
              />
              <Label htmlFor="anonymous" className="text-sm">
                Make this donation anonymous
              </Label>
            </div>
          </div>

          {/* Payment Method */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Method
            </h3>

            <RadioGroup
              value={formData.payment_method}
              onValueChange={(value) => handleInputChange('payment_method', value as 'paystack' | 'bank_transfer')}
              className="space-y-4"
            >
              <div className="flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900">
                <RadioGroupItem value="paystack" id="paystack" />
                <Label htmlFor="paystack" className="flex-1 cursor-pointer">
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium">Pay with Card (Paystack)</div>
                      <div className="text-sm text-muted-foreground">Secure online payment with debit/credit card</div>
                    </div>
                  </div>
                </Label>
              </div>

              <div className="flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900">
                <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                <Label htmlFor="bank_transfer" className="flex-1 cursor-pointer">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Bank Transfer</div>
                      <div className="text-sm text-muted-foreground">Transfer directly to our bank account</div>
                    </div>
                  </div>
                </Label>
              </div>
            </RadioGroup>

            {/* Bank Transfer Details */}
            {formData.payment_method === 'bank_transfer' && !settingsLoading && settings && (
              <div className="bg-blue-50 dark:bg-blue-950/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Bank Account Details
                </h4>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700 dark:text-blue-300">Account Name:</span>
                    <span className="font-medium text-blue-900 dark:text-blue-100">
                      {settings.donation_account_name || 'HALIMAKQ Foundation'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700 dark:text-blue-300">Account Number:</span>
                    <span className="font-medium text-blue-900 dark:text-blue-100">
                      {settings.donation_account_number || '**********'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700 dark:text-blue-300">Bank Name:</span>
                    <span className="font-medium text-blue-900 dark:text-blue-100">
                      {settings.donation_bank_name || 'First Bank of Nigeria'}
                    </span>
                  </div>
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-4">
                  Please use your email address as the transfer reference for easy identification.
                </p>
              </div>
            )}
          </div>

          {/* Summary and Submit */}
          {finalAmount > 0 && (
            <div className="space-y-6 pt-6 border-t">
              <div className="bg-green-50 dark:bg-green-950/20 p-6 rounded-xl">
                <h3 className="font-semibold text-green-800 dark:text-green-200 mb-4">Donation Summary</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-semibold">₦{finalAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Type:</span>
                    <span className="font-semibold">
                      {donationType === "recurring" ? `${formData.frequency || 'Monthly'} Recurring` : "One-Time"}
                    </span>
                  </div>
                  {formData.campaign_id && campaigns.find(c => c.id === formData.campaign_id) && (
                    <div className="flex justify-between">
                      <span>Campaign:</span>
                      <span className="font-semibold text-sm">
                        {campaigns.find(c => c.id === formData.campaign_id)?.title}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Payment Method:</span>
                    <span className="font-semibold">
                      {formData.payment_method === 'paystack' ? 'Card Payment' : 'Bank Transfer'}
                    </span>
                  </div>
                  <div className="flex justify-between text-lg font-bold text-green-600 pt-2 border-t">
                    <span>Total:</span>
                    <span>₦{finalAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  checked={termsAccepted}
                  onCheckedChange={(checked) => setTermsAccepted(checked === true)}
                />
                <Label htmlFor="terms" className="text-sm">
                  I agree to the{" "}
                  <a href="#" className="text-green-600 hover:underline">
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="#" className="text-green-600 hover:underline">
                    Privacy Policy
                  </a>
                </Label>
              </div>

              <Button
                type="submit"
                size="lg"
                disabled={isSubmitting || finalAmount < VALIDATION_RULES.DONATION_MIN_AMOUNT || !termsAccepted || !formData.donor_name || !formData.donor_email}
                className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-lg py-6 transition-all duration-200 hover:scale-105 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-5 w-5" />
                    {formData.payment_method === 'paystack' ? 'Pay Now' : 'Submit Donation'}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </>
                )}
              </Button>
            </div>
          )}

          <p className="text-xs text-muted-foreground text-center mt-4">
            Your donation is secure and encrypted. We never store your payment information.
          </p>
        </Tabs>
        </form>
      </CardContent>
    </Card>
  )
}
