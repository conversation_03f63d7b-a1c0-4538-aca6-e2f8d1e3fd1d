"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/partner/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/partner/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PartnerDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/partner/ProposalManager */ \"(app-pages-browser)/./components/dashboard/partner/ProposalManager.tsx\");\n/* harmony import */ var _components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/partner/ResourceSharing */ \"(app-pages-browser)/./components/dashboard/partner/ResourceSharing.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PartnerDashboard() {\n    var _user_preferences, _dashboardData_overview, _dashboardData_quick_stats, _dashboardData_overview1, _dashboardData_overview2;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [partnershipApplications, setPartnershipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collaborationOpportunities, setCollaborationOpportunities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [upcomingEvents, setUpcomingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [programs, setPrograms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Scholarship-related state\n    const [availableScholarships, setAvailableScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [myStudents, setMyStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scholarshipApplications, setScholarshipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentStats, setStudentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Dashboard overview state\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PartnerDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"PartnerDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        // Fetch user data first\n                        const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getProfile();\n                        if (userData.success) {\n                            setUser(userData.data);\n                        }\n                        // Fetch comprehensive dashboard data\n                        try {\n                            console.log('Fetching partner dashboard data...');\n                            const dashboardResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-dashboard');\n                            console.log('Dashboard response:', dashboardResponse);\n                            if (dashboardResponse.success) {\n                                setDashboardData(dashboardResponse.data);\n                                // Update student stats from dashboard data\n                                if (dashboardResponse.data.overview) {\n                                    setStudentStats({\n                                        total_students: dashboardResponse.data.overview.total_students,\n                                        active_students: dashboardResponse.data.overview.total_students,\n                                        recent_enrollments: 0,\n                                        scholarship_applications: dashboardResponse.data.overview.total_applications\n                                    });\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error fetching dashboard data:', error);\n                        }\n                        // Fetch partner-specific data\n                        const [eventsResponse, programsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getUpcomingEvents(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getPrograms()\n                        ]);\n                        const eventsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(eventsResponse);\n                        const programsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(programsResponse);\n                        setUpcomingEvents(eventsData.slice(0, 5));\n                        setPrograms(programsData.slice(0, 3));\n                        // Fetch scholarship-related data\n                        await fetchScholarshipData();\n                        setLoading(false);\n                    } catch (error) {\n                        console.error('Error fetching dashboard data:', error);\n                        setLoading(false);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchDashboardData\"];\n            const fetchScholarshipData = {\n                \"PartnerDashboard.useEffect.fetchScholarshipData\": async ()=>{\n                    try {\n                        console.log('Starting to fetch scholarship data...');\n                        // Fetch available scholarships for partner organizations (Primary/Secondary only)\n                        try {\n                            console.log('Fetching available scholarships...');\n                            const scholarshipsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/available');\n                            console.log('Scholarships response:', scholarshipsResponse);\n                            if (scholarshipsResponse.success) {\n                                setAvailableScholarships(scholarshipsResponse.data || []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching scholarships:', error);\n                        }\n                        // Fetch partner students\n                        try {\n                            console.log('Fetching partner students...');\n                            const studentsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students');\n                            console.log('Students response:', studentsResponse);\n                            if (studentsResponse.success) {\n                                var _studentsResponse_data;\n                                // Handle both paginated and direct array responses\n                                const studentsData = ((_studentsResponse_data = studentsResponse.data) === null || _studentsResponse_data === void 0 ? void 0 : _studentsResponse_data.data) || studentsResponse.data || [];\n                                setMyStudents(Array.isArray(studentsData) ? studentsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching students:', error);\n                        }\n                        // Fetch scholarship applications submitted by this partner\n                        try {\n                            console.log('Fetching my applications...');\n                            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/my-applications');\n                            console.log('Applications response:', applicationsResponse);\n                            if (applicationsResponse.success) {\n                                var _applicationsResponse_data;\n                                const applicationsData = ((_applicationsResponse_data = applicationsResponse.data) === null || _applicationsResponse_data === void 0 ? void 0 : _applicationsResponse_data.data) || applicationsResponse.data || [];\n                                setScholarshipApplications(Array.isArray(applicationsData) ? applicationsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching applications:', error);\n                        }\n                        // Fetch student statistics\n                        try {\n                            console.log('Fetching student statistics...');\n                            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students/statistics');\n                            console.log('Stats response:', statsResponse);\n                            if (statsResponse.success) {\n                                setStudentStats(statsResponse.data || {});\n                            }\n                        } catch (error) {\n                            console.error('Error fetching stats:', error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching scholarship data:', error);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchScholarshipData\"];\n            fetchDashboardData();\n        }\n    }[\"PartnerDashboard.useEffect\"], []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 12\n        }, this);\n    }\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'approved':\n                return 'bg-green-500';\n            case 'rejected':\n                return 'bg-red-500';\n            case 'pending':\n                return 'bg-yellow-500';\n            case 'under_review':\n                return 'bg-blue-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const partnerInfo = (user === null || user === void 0 ? void 0 : (_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.partner_data) || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    partnerInfo.organization_name || (user === null || user === void 0 ? void 0 : user.first_name),\n                                                    \"!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Partnership Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.organization_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: [\n                                                    partnerInfo.organization_type,\n                                                    \" • \",\n                                                    partnerInfo.sector\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/opportunities\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Opportunities\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/apply\",\n                                            children: \"Apply for Partnership\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"My Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview = dashboardData.overview) === null || _dashboardData_overview === void 0 ? void 0 : _dashboardData_overview.total_students) || (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Enrolled students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Available Scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quick_stats = dashboardData.quick_stats) === null || _dashboardData_quick_stats === void 0 ? void 0 : _dashboardData_quick_stats.active_scholarships) || availableScholarships.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Open for applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview1 = dashboardData.overview) === null || _dashboardData_overview1 === void 0 ? void 0 : _dashboardData_overview1.total_applications) || scholarshipApplications.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Total applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: [\n                                                (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview2 = dashboardData.overview) === null || _dashboardData_overview2 === void 0 ? void 0 : _dashboardData_overview2.success_rate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Application success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    defaultValue: \"scholarships\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"scholarships\",\n                                    children: \"Scholarships\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"students\",\n                                    children: \"My Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"applications\",\n                                    children: \"Applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"progression\",\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"opportunities\",\n                                    children: \"Opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"proposals\",\n                                    children: \"Proposals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"resources\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"scholarships\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Available Scholarships\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                children: [\n                                                                    availableScholarships.length,\n                                                                    \" Available\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        availableScholarships.slice(0, 3).map((scholarship)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: scholarship.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 347,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                                                    children: scholarship.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 350,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 350,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 353,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \"Deadline: \",\n                                                                                                new Date(scholarship.application_deadline).toLocaleDateString()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 353,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3 flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: scholarship.category === 'primary' ? 'default' : 'secondary',\n                                                                                            children: scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 358,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium text-green-600\",\n                                                                                            children: [\n                                                                                                \"₦\",\n                                                                                                scholarship.amount ? Number(scholarship.amount).toLocaleString() : 'Amount varies'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 361,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 357,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarship-application?scholarship_id=\".concat(scholarship.id, \"&category=\").concat(scholarship.category),\n                                                                                    children: \"Apply for Student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 369,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarships/\".concat(scholarship.id),\n                                                                                    children: \"View Details\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, scholarship.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        availableScholarships.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"No scholarships available at the moment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-2\",\n                                                                    children: \"Check back later for new opportunities\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        availableScholarships.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                \"View All Scholarships (\",\n                                                                availableScholarships.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Quick Actions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Add New Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 411,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Register a new student to your school\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 412,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 421,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Manage Students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 423,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View and manage all your students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 424,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Browse Scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 435,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Explore all available scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 436,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/analytics\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Analytics & Reports\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View performance insights and statistics\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 448,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Recent Scholarship Applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            scholarshipApplications.length,\n                                                            \" Total\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    scholarshipApplications.slice(0, 5).map((application)=>{\n                                                        var _application_student, _application_scholarship, _application_scholarship1, _application_scholarship2, _application_status, _application_status1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'Student Name'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 475,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.category) || application.scholarship_category || 'Category'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 478,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.title) || application.scholarship_title || 'Scholarship Title'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Applied: \",\n                                                                                        new Date(application.created_at).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Amount: ₦\",\n                                                                                        Number(application.scholarship.amount).toLocaleString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 488,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                            children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: \"View\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No scholarship applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Start applying for scholarships on behalf of your students\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: \"Browse Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"students\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All Students\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students/add\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add New Student\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.active_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Active Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.recent_enrollments) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Recent Enrollments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.scholarship_applications) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"My Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    myStudents.slice(0, 10).map((student)=>{\n                                                        var _student_progressions_, _student_progressions;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: student.full_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Age: \",\n                                                                                        student.age || 'N/A'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Grade: \",\n                                                                                        ((_student_progressions = student.progressions) === null || _student_progressions === void 0 ? void 0 : (_student_progressions_ = _student_progressions[0]) === null || _student_progressions_ === void 0 ? void 0 : _student_progressions_.grade_level) || 'Not set'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Status: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: \"outline\",\n                                                                                            className: \"text-xs ml-1\",\n                                                                                            children: student.status || 'Active'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 579,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        student.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                student.student_id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            children: \"View Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                href: \"/scholarship-application?student_id=\".concat(student.id),\n                                                                                children: \"Apply for Scholarship\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, student.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    myStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No students registered yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Add students to start managing their scholarship applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: \"Add Your First Student\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Collaboration Opportunities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        collaborationOpportunities.slice(0, 3).map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: opportunity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: opportunity.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 630,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.location\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 631,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.timeline\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: opportunity.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                                children: opportunity.budget_range\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, opportunity.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/partnerships/opportunities\",\n                                                                children: \"View All Opportunities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Available Programs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        programs.map((program)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: program.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: program.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: program.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 659,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: \"Learn More\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 660,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, program.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        programs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-center py-4\",\n                                                            children: \"No programs available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Partnership Applications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    partnershipApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: application.program\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"Submitted: \",\n                                                                                new Date(application.submitted_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getStatusColor(application.status),\n                                                                    children: application.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    partnershipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No partnership applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/partnerships/apply\",\n                                                                    children: \"Submit Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"opportunities\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Partnership Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Explore collaboration opportunities that align with your organization's mission\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: collaborationOpportunities.map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: opportunity.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Apply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: opportunity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Location: \",\n                                                                        opportunity.location\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Timeline: \",\n                                                                        opportunity.timeline\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Type: \",\n                                                                        opportunity.type\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Budget: \",\n                                                                        opportunity.budget_range\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, opportunity.id, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"proposals\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"resources\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"applications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Scholarship Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track scholarship applications submitted on behalf of your students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                scholarshipApplications.map((application)=>{\n                                                    var _application_scholarship, _application_status, _application_status1, _application_student, _application_scholarship1, _application_scholarship2, _application_scholarship3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4 hover:shadow-sm transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.title) || 'Scholarship Application'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                        children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Student:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 765,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Amount:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" ₦\",\n                                                                            ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.amount) ? Number(application.scholarship.amount).toLocaleString() : 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Category:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.category) || application.category || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Submitted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 768,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            new Date(application.submitted_at || application.created_at).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            ((_application_scholarship3 = application.scholarship) === null || _application_scholarship3 === void 0 ? void 0 : _application_scholarship3.application_deadline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Deadline:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" \",\n                                                                    new Date(application.scholarship.application_deadline).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"View Details\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    application.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"Edit Application\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    application.status === 'approved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓ Approved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, application.id, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No scholarship applications submitted yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-4\",\n                                                            children: \"Start applying for scholarships on behalf of your students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/scholarships\",\n                                                                children: \"Browse Available Scholarships\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 746,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"progression\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Academic Progression Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                            href: \"/dashboard/partner/progression\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View All Progressions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track and manage student academic progression through grade levels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-8 w-8 text-yellow-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pending Approval\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 830,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"Recent Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/progression/create\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Create Progression\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-2\",\n                                                                children: \"No progressions found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Start tracking student academic progression\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 803,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 802,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Manage your organization's information and partnership preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_name || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_type || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Contact Person\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    user === null || user === void 0 ? void 0 : user.first_name,\n                                                                    \" \",\n                                                                    user === null || user === void 0 ? void 0 : user.last_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    partnerInfo.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Website\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.website\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.sector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Sector\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.sector\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 891,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.size\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.annual_budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Budget\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.annual_budget\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.partnership_interests && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Partnership Interests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: partnerInfo.partnership_interests\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: \"Edit Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(PartnerDashboard, \"Bk3UO5XMfRUlkQk2ciyYz20OBnE=\");\n_c = PartnerDashboard;\nvar _c;\n$RefreshReg$(_c, \"PartnerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/partner/page.tsx\n"));

/***/ })

});