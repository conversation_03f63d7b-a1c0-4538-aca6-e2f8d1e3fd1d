<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Volunteer;
use Illuminate\Auth\Access\HandlesAuthorization;

class VolunteerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any volunteers.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'volunteer']);
    }

    /**
     * Determine whether the user can view the volunteer.
     */
    public function view(User $user, Volunteer $volunteer): bool
    {
        // Admins can view all volunteers
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view their own volunteer profile
        if ($volunteer->user_id === $user->id) {
            return true;
        }

        // Partner organizations can view volunteers for their programs/events
        if ($user->role === 'partner_organization') {
            // Check if volunteer is assigned to any of the partner's programs/events
            return $this->isVolunteerAssignedToPartner($volunteer, $user);
        }

        return false;
    }

    /**
     * Determine whether the user can create volunteers.
     */
    public function create(User $user): bool
    {
        // Only authenticated users can create volunteer applications
        return $user->isActive();
    }

    /**
     * Determine whether the user can update the volunteer.
     */
    public function update(User $user, Volunteer $volunteer): bool
    {
        // Admins can update any volunteer
        if ($user->role === 'admin') {
            return true;
        }

        // Users can update their own volunteer profile
        return $volunteer->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the volunteer.
     */
    public function delete(User $user, Volunteer $volunteer): bool
    {
        // Only admins can delete volunteer records
        if ($user->role === 'admin') {
            return true;
        }

        // Users can delete their own volunteer application if it's still pending
        return $volunteer->user_id === $user->id && $volunteer->application_status === 'pending';
    }

    /**
     * Determine whether the user can approve volunteer applications.
     */
    public function approve(User $user, Volunteer $volunteer): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can reject volunteer applications.
     */
    public function reject(User $user, Volunteer $volunteer): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can log volunteer hours.
     */
    public function logHours(User $user, Volunteer $volunteer): bool
    {
        // Admins can log hours for any volunteer
        if ($user->role === 'admin') {
            return true;
        }

        // Volunteers can log their own hours
        return $volunteer->user_id === $user->id && $volunteer->application_status === 'approved';
    }

    /**
     * Determine whether the user can view volunteer hours.
     */
    public function viewHours(User $user, Volunteer $volunteer): bool
    {
        // Admins can view all volunteer hours
        if ($user->role === 'admin') {
            return true;
        }

        // Volunteers can view their own hours
        if ($volunteer->user_id === $user->id) {
            return true;
        }

        // Partner organizations can view hours for volunteers working on their programs
        if ($user->role === 'partner_organization') {
            return $this->isVolunteerAssignedToPartner($volunteer, $user);
        }

        return false;
    }

    /**
     * Determine whether the user can access volunteer training modules.
     */
    public function accessTraining(User $user, Volunteer $volunteer): bool
    {
        // Admins can access all training
        if ($user->role === 'admin') {
            return true;
        }

        // Approved volunteers can access training
        return $volunteer->user_id === $user->id && $volunteer->application_status === 'approved';
    }

    /**
     * Determine whether the user can apply for volunteer opportunities.
     */
    public function applyForOpportunity(User $user, Volunteer $volunteer): bool
    {
        // Approved volunteers can apply for opportunities
        return $volunteer->user_id === $user->id && 
               $volunteer->application_status === 'approved' && 
               $volunteer->status === 'active';
    }

    /**
     * Determine whether the user can view volunteer analytics.
     */
    public function viewAnalytics(User $user, Volunteer $volunteer = null): bool
    {
        // Admins can view all analytics
        if ($user->role === 'admin') {
            return true;
        }

        // Volunteers can view their own analytics
        if ($volunteer && $volunteer->user_id === $user->id) {
            return true;
        }

        // Partner organizations can view analytics for their volunteers
        if ($user->role === 'partner_organization' && $volunteer) {
            return $this->isVolunteerAssignedToPartner($volunteer, $user);
        }

        return false;
    }

    /**
     * Check if volunteer is assigned to partner organization's programs/events
     */
    private function isVolunteerAssignedToPartner(Volunteer $volunteer, User $partnerUser): bool
    {
        // This would need to be implemented based on your volunteer assignment logic
        // For now, return false as a placeholder
        return false;
    }
}
