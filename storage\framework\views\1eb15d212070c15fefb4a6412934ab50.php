<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?> - <?php echo e(config('app.name', 'HALIMAKQ Foundation')); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Dashboard for HALIMAKQ Foundation NGO platform.'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="bg-gray-50 font-sans antialiased">
    <!-- Dashboard Content -->
    <div id="app">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Scripts -->
    <script>
        // Mobile menu toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileSidebar = document.getElementById('mobile-sidebar');
            const mobileSidebarContent = document.getElementById('mobile-sidebar-content');
            const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');

            if (mobileMenuBtn && mobileSidebar) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileSidebar.classList.remove('hidden');
                    setTimeout(() => {
                        mobileSidebarContent.classList.remove('-translate-x-full');
                    }, 10);
                });
            }

            // Close mobile menu when clicking overlay
            if (mobileSidebarOverlay) {
                mobileSidebarOverlay.addEventListener('click', function() {
                    closeMobileMenu();
                });
            }

            // Function to close mobile menu
            function closeMobileMenu() {
                if (mobileSidebarContent) {
                    mobileSidebarContent.classList.add('-translate-x-full');
                    setTimeout(() => {
                        if (mobileSidebar) {
                            mobileSidebar.classList.add('hidden');
                        }
                    }, 300);
                }
            }

            // Close mobile menu on escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeMobileMenu();
                }
            });
        });

        // Show Laravel session messages
        <?php if(session('success')): ?>
            document.addEventListener('DOMContentLoaded', function() {
                // You can add a toast notification library here
                alert('<?php echo e(session('success')); ?>');
            });
        <?php endif; ?>

        <?php if(session('error')): ?>
            document.addEventListener('DOMContentLoaded', function() {
                alert('<?php echo e(session('error')); ?>');
            });
        <?php endif; ?>
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/layouts/dashboard.blade.php ENDPATH**/ ?>