<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\User;
use App\Models\EducationalResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class StudentDashboardController extends Controller
{
    /**
     * Display the main student dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Ensure user is a university student
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Get dashboard statistics
        $stats = [
            'available_scholarships' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'university')
                ->where('application_deadline', '>', now())
                ->count(),
            'my_applications' => $user->scholarshipApplications()->count(),
            'pending_applications' => $user->scholarshipApplications()
                ->where('status', 'pending')
                ->count(),
            'approved_applications' => $user->scholarshipApplications()
                ->where('status', 'approved')
                ->count(),
        ];

        // Get recent application
        $recentApplication = $user->scholarshipApplications()
            ->with('scholarship')
            ->latest()
            ->first();

        // Get available scholarships (limited for dashboard)
        $availableScholarships = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->where('category', 'university')
            ->where('application_deadline', '>', now())
            ->orderBy('application_deadline', 'asc')
            ->take(3)
            ->get();

        return view('dashboards.student.index', compact('stats', 'recentApplication', 'availableScholarships'));
    }

    /**
     * Display My Applications section
     */
    public function applications()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $applications = $user->scholarshipApplications()
            ->with(['scholarship', 'files'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $stats = [
            'total' => $user->scholarshipApplications()->count(),
            'pending' => $user->scholarshipApplications()->where('status', 'pending')->count(),
            'under_review' => $user->scholarshipApplications()->where('status', 'under_review')->count(),
            'approved' => $user->scholarshipApplications()->where('status', 'approved')->count(),
            'rejected' => $user->scholarshipApplications()->where('status', 'rejected')->count(),
        ];

        return view('dashboards.student.applications', compact('applications', 'stats'));
    }

    /**
     * Display Available Scholarships section
     */
    public function scholarships(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $query = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->where('category', 'university')
            ->where('application_deadline', '>', now());

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply amount filter
        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->get('min_amount'));
        }
        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->get('max_amount'));
        }

        $scholarships = $query->orderBy('application_deadline', 'asc')->paginate(12);

        // Check which scholarships user has already applied for
        $appliedScholarshipIds = $user->scholarshipApplications()
            ->pluck('scholarship_id')
            ->toArray();

        return view('dashboards.student.scholarships', compact('scholarships', 'appliedScholarshipIds'));
    }

    /**
     * Display Academic Profile section
     */
    public function profile()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        return view('dashboards.student.profile', compact('user'));
    }

    /**
     * Update Academic Profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone_number' => 'nullable|string|max:20',
            'university_name' => 'required|string|max:255',
            'matric_number' => 'required|string|max:50|unique:users,matric_number,' . $user->id,
            'course_of_study' => 'required|string|max:255',
            'current_level' => 'required|string|max:50',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user->update($validator->validated());

        return redirect()->route('student.profile')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Display Documents section
     */
    public function documents()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Get user's uploaded documents from scholarship applications
        $documents = collect();
        
        $applications = $user->scholarshipApplications()->with('files')->get();
        foreach ($applications as $application) {
            foreach ($application->files as $file) {
                $documents->push([
                    'id' => $file->id,
                    'name' => $file->original_name,
                    'type' => $file->field_name,
                    'size' => $file->file_size ?? 0,
                    'uploaded_at' => $file->created_at,
                    'application' => $application->scholarship->title ?? 'Unknown',
                    'path' => $file->file_path,
                ]);
            }
        }

        $documents = $documents->sortByDesc('uploaded_at');

        return view('dashboards.student.documents', compact('documents'));
    }

    /**
     * Display Resources section
     */
    public function resources()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $resources = EducationalResource::where('is_active', true)
            ->where(function($query) {
                $query->where('target_audience', 'university')
                      ->orWhere('target_audience', 'all');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('dashboards.student.resources', compact('resources'));
    }

    /**
     * Display Support & Settings section
     */
    public function support()
    {
        $user = Auth::user();

        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Get website settings for support information
        $settings = [
            'support_email' => '<EMAIL>',
            'support_phone' => '+234 ************',
        ];

        return view('dashboards.student.support', compact('user', 'settings'));
    }

    /**
     * Download document
     */
    public function downloadDocument($fileId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Find the file and verify ownership
        $file = \App\Models\ScholarshipApplicationFile::where('id', $fileId)
            ->whereHas('application', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->firstOrFail();

        if (!Storage::exists($file->file_path)) {
            return redirect()->back()->with('error', 'File not found.');
        }

        return Storage::download($file->file_path, $file->original_name);
    }
}
