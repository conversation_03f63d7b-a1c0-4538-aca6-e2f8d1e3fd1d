import { Card, CardContent } from "@/components/ui/card"
import type { ReactNode } from "react"

interface IconCardProps {
  icon: ReactNode
  title: string
  description: string
}

export function IconCard({ icon, title, description }: IconCardProps) {
  return (
    <Card className="overflow-hidden border-green-100 dark:border-green-800 transition-all duration-300 hover:shadow-md hover:border-green-200 dark:hover:border-green-700 hover:-translate-y-1 neumorphic">
      <CardContent className="p-6">
        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-50 dark:bg-green-900/20 animate-float">
          {icon}
        </div>
        <h3 className="mb-2 text-xl font-semibold text-green-800 dark:text-green-200">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
}
