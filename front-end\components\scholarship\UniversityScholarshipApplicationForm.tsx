'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Upload,
  FileText,
  User,
  GraduationCap,
  Award,
  AlertCircle,
  CheckCircle,
  Loader2,
  X
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'

interface UniversityApplicationFormData {
  // Personal Information
  full_name: string
  email: string
  phone: string
  date_of_birth: string
  address: string
  
  // Academic Information
  institution: string
  matriculation_number: string
  field_of_study: string
  academic_level: string
  current_year: string
  current_gpa: string
  expected_graduation: string
  
  // Application Details
  reason_for_scholarship: string
  career_goals: string
  academic_achievements: string
  extracurricular_activities: string
  financial_need_explanation: string
  how_scholarship_helps: string
  
  // References
  reference1_name: string
  reference1_position: string
  reference1_email: string
  reference1_phone: string
  reference2_name: string
  reference2_position: string
  reference2_email: string
  reference2_phone: string
}

interface UniversityScholarshipApplicationFormProps {
  scholarshipId: string
  scholarshipTitle: string
  onSuccess: () => void
}

export default function UniversityScholarshipApplicationForm({
  scholarshipId,
  scholarshipTitle,
  onSuccess
}: UniversityScholarshipApplicationFormProps) {
  const { toast } = useToast()
  
  const [formData, setFormData] = useState<UniversityApplicationFormData>({
    // Personal Information
    full_name: '',
    email: '',
    phone: '',
    date_of_birth: '',
    address: '',
    
    // Academic Information
    institution: '',
    matriculation_number: '',
    field_of_study: '',
    academic_level: '',
    current_year: '',
    current_gpa: '',
    expected_graduation: '',
    
    // Application Details
    reason_for_scholarship: '',
    career_goals: '',
    academic_achievements: '',
    extracurricular_activities: '',
    financial_need_explanation: '',
    how_scholarship_helps: '',
    
    // References
    reference1_name: '',
    reference1_position: '',
    reference1_email: '',
    reference1_phone: '',
    reference2_name: '',
    reference2_position: '',
    reference2_email: '',
    reference2_phone: ''
  })
  
  const [files, setFiles] = useState<{[key: string]: File | null}>({
    academic_transcript: null,
    personal_statement: null,
    recommendation_letter1: null,
    recommendation_letter2: null,
    financial_documents: null,
    additional_documents: null
  })
  
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  // Pre-populate user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await apiClient.getProfile()
        if (response.success && response.data) {
          const userData = response.data
          setFormData(prev => ({
            ...prev,
            full_name: `${userData.first_name} ${userData.last_name}`,
            email: userData.email,
            phone: userData.phone_number || '',
            // Pre-populate from user preferences if available
            institution: userData.preferences?.student_data?.institution || '',
            field_of_study: userData.preferences?.student_data?.field_of_study || '',
            academic_level: userData.preferences?.student_data?.academic_level || '',
            current_gpa: userData.preferences?.student_data?.current_gpa || '',
            matriculation_number: userData.preferences?.student_data?.matriculation_number || ''
          }))
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      }
    }

    fetchUserData()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: string) => {
    const file = e.target.files?.[0] || null
    setFiles(prev => ({ ...prev, [fileType]: file }))
    if (errors[fileType]) {
      setErrors(prev => ({ ...prev, [fileType]: '' }))
    }
  }

  const removeFile = (fileType: string) => {
    setFiles(prev => ({ ...prev, [fileType]: null }))
  }

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    // Required fields validation
    const requiredFields = [
      'full_name', 'email', 'phone', 'date_of_birth', 'address',
      'institution', 'matriculation_number', 'field_of_study', 'academic_level',
      'current_year', 'reason_for_scholarship', 'career_goals', 'financial_need_explanation'
    ]

    requiredFields.forEach(field => {
      if (!formData[field as keyof UniversityApplicationFormData]) {
        newErrors[field] = 'This field is required'
      }
    })

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    if (formData.phone && !phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // File validation - at least academic transcript is required
    if (!files.academic_transcript) {
      newErrors.academic_transcript = 'Academic transcript is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields correctly.",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      // Create FormData for file upload
      const submitData = new FormData()
      
      // Add form data
      Object.entries(formData).forEach(([key, value]) => {
        submitData.append(key, value)
      })
      
      // Add scholarship ID
      submitData.append('scholarship_id', scholarshipId)
      
      // Add files
      Object.entries(files).forEach(([key, file]) => {
        if (file) {
          submitData.append(key, file)
        }
      })

      const response = await apiClient.post(`/api/v1/student-scholarships/apply/${scholarshipId}`, submitData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      if (response.data.success) {
        toast({
          title: "Application Submitted Successfully",
          description: "Your university scholarship application has been submitted for review.",
        })
        onSuccess()
      } else {
        throw new Error(response.data.message || 'Failed to submit application')
      }
    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Submission Failed",
        description: error.response?.data?.message || "Failed to submit application. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <Card className="shadow-lg border-0">
        <CardHeader className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white rounded-t-lg">
          <CardTitle className="text-2xl font-bold">University Scholarship Application</CardTitle>
          <CardDescription className="text-blue-100">
            Apply for: {scholarshipTitle}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold">Personal Information</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="full_name">Full Name *</Label>
                  <Input
                    id="full_name"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    className={errors.full_name ? 'border-red-500' : ''}
                  />
                  {errors.full_name && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.full_name}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.email}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="+234 xxx xxx xxxx"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.phone}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="date_of_birth">Date of Birth *</Label>
                  <Input
                    id="date_of_birth"
                    name="date_of_birth"
                    type="date"
                    value={formData.date_of_birth}
                    onChange={handleInputChange}
                    className={errors.date_of_birth ? 'border-red-500' : ''}
                  />
                  {errors.date_of_birth && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.date_of_birth}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address">Home Address *</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Enter your complete home address"
                  rows={3}
                  className={errors.address ? 'border-red-500' : ''}
                />
                {errors.address && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.address}
                  </p>
                )}
              </div>
            </div>

            <Separator />

            {/* Academic Information Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <GraduationCap className="h-5 w-5 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold">Academic Information</h3>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="institution">University/Institution *</Label>
                <Input
                  id="institution"
                  name="institution"
                  value={formData.institution}
                  onChange={handleInputChange}
                  placeholder="University of Lagos"
                  className={errors.institution ? 'border-red-500' : ''}
                />
                {errors.institution && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.institution}
                  </p>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="matriculation_number">Matriculation Number *</Label>
                  <Input
                    id="matriculation_number"
                    name="matriculation_number"
                    value={formData.matriculation_number}
                    onChange={handleInputChange}
                    placeholder="MAT/2024/12345"
                    className={errors.matriculation_number ? 'border-red-500' : ''}
                  />
                  {errors.matriculation_number && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.matriculation_number}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="field_of_study">Field of Study *</Label>
                  <Select onValueChange={(value) => handleSelectChange('field_of_study', value)} value={formData.field_of_study}>
                    <SelectTrigger className={errors.field_of_study ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select your field of study" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="medicine">Medicine & Health Sciences</SelectItem>
                      <SelectItem value="sciences">Natural Sciences</SelectItem>
                      <SelectItem value="social_sciences">Social Sciences</SelectItem>
                      <SelectItem value="arts_humanities">Arts & Humanities</SelectItem>
                      <SelectItem value="business">Business & Management</SelectItem>
                      <SelectItem value="law">Law</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="agriculture">Agriculture</SelectItem>
                      <SelectItem value="technology">Information Technology</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.field_of_study && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.field_of_study}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="academic_level">Academic Level *</Label>
                  <Select onValueChange={(value) => handleSelectChange('academic_level', value)} value={formData.academic_level}>
                    <SelectTrigger className={errors.academic_level ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select academic level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="undergraduate">Undergraduate</SelectItem>
                      <SelectItem value="graduate">Graduate/Masters</SelectItem>
                      <SelectItem value="phd">PhD/Doctorate</SelectItem>
                      <SelectItem value="postdoc">Post-Doctorate</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.academic_level && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.academic_level}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current_year">Current Year of Study *</Label>
                  <Select onValueChange={(value) => handleSelectChange('current_year', value)} value={formData.current_year}>
                    <SelectTrigger className={errors.current_year ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1st Year</SelectItem>
                      <SelectItem value="2">2nd Year</SelectItem>
                      <SelectItem value="3">3rd Year</SelectItem>
                      <SelectItem value="4">4th Year</SelectItem>
                      <SelectItem value="5">5th Year</SelectItem>
                      <SelectItem value="6">6th Year</SelectItem>
                      <SelectItem value="graduate">Graduate</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.current_year && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.current_year}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="current_gpa">Current GPA/CGPA</Label>
                  <Input
                    id="current_gpa"
                    name="current_gpa"
                    value={formData.current_gpa}
                    onChange={handleInputChange}
                    placeholder="3.75 (out of 4.0 or 5.0)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expected_graduation">Expected Graduation Date</Label>
                  <Input
                    id="expected_graduation"
                    name="expected_graduation"
                    type="date"
                    value={formData.expected_graduation}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Application Essays Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileText className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold">Application Essays</h3>
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason_for_scholarship">Why do you deserve this scholarship? *</Label>
                <Textarea
                  id="reason_for_scholarship"
                  name="reason_for_scholarship"
                  value={formData.reason_for_scholarship}
                  onChange={handleInputChange}
                  placeholder="Explain why you are a deserving candidate for this scholarship (minimum 200 words)"
                  rows={5}
                  className={errors.reason_for_scholarship ? 'border-red-500' : ''}
                />
                {errors.reason_for_scholarship && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.reason_for_scholarship}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="career_goals">Career Goals and Aspirations *</Label>
                <Textarea
                  id="career_goals"
                  name="career_goals"
                  value={formData.career_goals}
                  onChange={handleInputChange}
                  placeholder="Describe your career goals and how this scholarship will help you achieve them"
                  rows={4}
                  className={errors.career_goals ? 'border-red-500' : ''}
                />
                {errors.career_goals && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.career_goals}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="financial_need_explanation">Financial Need Explanation *</Label>
                <Textarea
                  id="financial_need_explanation"
                  name="financial_need_explanation"
                  value={formData.financial_need_explanation}
                  onChange={handleInputChange}
                  placeholder="Explain your financial situation and why you need this scholarship"
                  rows={4}
                  className={errors.financial_need_explanation ? 'border-red-500' : ''}
                />
                {errors.financial_need_explanation && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.financial_need_explanation}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="academic_achievements">Academic Achievements</Label>
                <Textarea
                  id="academic_achievements"
                  name="academic_achievements"
                  value={formData.academic_achievements}
                  onChange={handleInputChange}
                  placeholder="List your academic achievements, awards, honors, etc."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="extracurricular_activities">Extracurricular Activities</Label>
                <Textarea
                  id="extracurricular_activities"
                  name="extracurricular_activities"
                  value={formData.extracurricular_activities}
                  onChange={handleInputChange}
                  placeholder="Describe your involvement in clubs, sports, volunteer work, etc."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="how_scholarship_helps">How will this scholarship help you?</Label>
                <Textarea
                  id="how_scholarship_helps"
                  name="how_scholarship_helps"
                  value={formData.how_scholarship_helps}
                  onChange={handleInputChange}
                  placeholder="Explain how receiving this scholarship will impact your education and future"
                  rows={3}
                />
              </div>
            </div>

            <Separator />

            {/* Document Upload Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Upload className="h-5 w-5 text-orange-600" />
                </div>
                <h3 className="text-xl font-semibold">Required Documents</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Academic Transcript */}
                <div className="space-y-2">
                  <Label htmlFor="academic_transcript">Academic Transcript *</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      id="academic_transcript"
                      type="file"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      onChange={(e) => handleFileChange(e, 'academic_transcript')}
                      className="hidden"
                    />
                    <label htmlFor="academic_transcript" className="cursor-pointer">
                      {files.academic_transcript ? (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-green-600" />
                            <span className="text-sm text-green-600">{files.academic_transcript.name}</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile('academic_transcript')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Click to upload transcript</p>
                          <p className="text-xs text-gray-400">PDF, DOC, or Image files</p>
                        </div>
                      )}
                    </label>
                  </div>
                  {errors.academic_transcript && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.academic_transcript}
                    </p>
                  )}
                </div>

                {/* Personal Statement */}
                <div className="space-y-2">
                  <Label htmlFor="personal_statement">Personal Statement</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      id="personal_statement"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={(e) => handleFileChange(e, 'personal_statement')}
                      className="hidden"
                    />
                    <label htmlFor="personal_statement" className="cursor-pointer">
                      {files.personal_statement ? (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-green-600" />
                            <span className="text-sm text-green-600">{files.personal_statement.name}</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile('personal_statement')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Upload personal statement</p>
                          <p className="text-xs text-gray-400">PDF or DOC files</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Recommendation Letter 1 */}
                <div className="space-y-2">
                  <Label htmlFor="recommendation_letter1">Recommendation Letter 1</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      id="recommendation_letter1"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={(e) => handleFileChange(e, 'recommendation_letter1')}
                      className="hidden"
                    />
                    <label htmlFor="recommendation_letter1" className="cursor-pointer">
                      {files.recommendation_letter1 ? (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-green-600" />
                            <span className="text-sm text-green-600">{files.recommendation_letter1.name}</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile('recommendation_letter1')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Upload recommendation</p>
                          <p className="text-xs text-gray-400">PDF or DOC files</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>

                {/* Financial Documents */}
                <div className="space-y-2">
                  <Label htmlFor="financial_documents">Financial Documents</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      id="financial_documents"
                      type="file"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      onChange={(e) => handleFileChange(e, 'financial_documents')}
                      className="hidden"
                    />
                    <label htmlFor="financial_documents" className="cursor-pointer">
                      {files.financial_documents ? (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-green-600" />
                            <span className="text-sm text-green-600">{files.financial_documents.name}</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile('financial_documents')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Upload financial docs</p>
                          <p className="text-xs text-gray-400">Income statements, etc.</p>
                        </div>
                      )}
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* References Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <User className="h-5 w-5 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold">References</h3>
                <Badge variant="secondary">Optional</Badge>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Reference 1 */}
                <div className="space-y-4 p-4 border rounded-lg">
                  <h4 className="font-medium text-gray-900">Reference 1</h4>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reference1_name">Full Name</Label>
                      <Input
                        id="reference1_name"
                        name="reference1_name"
                        value={formData.reference1_name}
                        onChange={handleInputChange}
                        placeholder="Dr. John Smith"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1_position">Position/Title</Label>
                      <Input
                        id="reference1_position"
                        name="reference1_position"
                        value={formData.reference1_position}
                        onChange={handleInputChange}
                        placeholder="Professor, Department of Engineering"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1_email">Email</Label>
                      <Input
                        id="reference1_email"
                        name="reference1_email"
                        type="email"
                        value={formData.reference1_email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1_phone">Phone</Label>
                      <Input
                        id="reference1_phone"
                        name="reference1_phone"
                        value={formData.reference1_phone}
                        onChange={handleInputChange}
                        placeholder="+234 xxx xxx xxxx"
                      />
                    </div>
                  </div>
                </div>

                {/* Reference 2 */}
                <div className="space-y-4 p-4 border rounded-lg">
                  <h4 className="font-medium text-gray-900">Reference 2</h4>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reference2_name">Full Name</Label>
                      <Input
                        id="reference2_name"
                        name="reference2_name"
                        value={formData.reference2_name}
                        onChange={handleInputChange}
                        placeholder="Prof. Jane Doe"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2_position">Position/Title</Label>
                      <Input
                        id="reference2_position"
                        name="reference2_position"
                        value={formData.reference2_position}
                        onChange={handleInputChange}
                        placeholder="Head of Department"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2_email">Email</Label>
                      <Input
                        id="reference2_email"
                        name="reference2_email"
                        type="email"
                        value={formData.reference2_email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2_phone">Phone</Label>
                      <Input
                        id="reference2_phone"
                        name="reference2_phone"
                        value={formData.reference2_phone}
                        onChange={handleInputChange}
                        placeholder="+234 xxx xxx xxxx"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-6 border-t">
              <Button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                size="lg"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting Application...
                  </>
                ) : (
                  <>
                    <Award className="mr-2 h-4 w-4" />
                    Submit University Application
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
