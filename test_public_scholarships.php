<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "Testing Public Scholarships API Endpoint\n";
echo "========================================\n\n";

// Test 1: Basic endpoint test
echo "Test 1: GET /api/v1/public-scholarships\n";
echo "----------------------------------------\n";

$request = Request::create('/api/v1/public-scholarships', 'GET');
$request->headers->set('Accept', 'application/json');

try {
    $response = $kernel->handle($request);
    
    echo "Status Code: " . $response->getStatusCode() . "\n";
    echo "Content-Type: " . $response->headers->get('Content-Type') . "\n";
    
    $content = $response->getContent();
    $data = json_decode($content, true);
    
    if ($data) {
        echo "Response Structure:\n";
        echo "- success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "- message: " . ($data['message'] ?? 'N/A') . "\n";
        
        if (isset($data['data'])) {
            if (isset($data['data']['data'])) {
                echo "- scholarships count: " . count($data['data']['data']) . "\n";
                
                if (count($data['data']['data']) > 0) {
                    $firstScholarship = $data['data']['data'][0];
                    echo "- first scholarship fields: " . implode(', ', array_keys($firstScholarship)) . "\n";
                    
                    // Check for required fields
                    $requiredFields = ['id', 'title', 'category', 'category_instructions', 'custom_fields', 'form_metadata'];
                    $missingFields = [];
                    foreach ($requiredFields as $field) {
                        if (!isset($firstScholarship[$field])) {
                            $missingFields[] = $field;
                        }
                    }
                    
                    if (empty($missingFields)) {
                        echo "✓ All required fields present\n";
                    } else {
                        echo "✗ Missing fields: " . implode(', ', $missingFields) . "\n";
                    }
                }
            }
        }
        
        if (isset($data['categories'])) {
            echo "- categories count: " . count($data['categories']) . "\n";
        }
    } else {
        echo "Response Body:\n" . $content . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n";

// Test 2: Test with category filter
echo "Test 2: GET /api/v1/public-scholarships?category=primary\n";
echo "-------------------------------------------------------\n";

$request2 = Request::create('/api/v1/public-scholarships?category=primary', 'GET');
$request2->headers->set('Accept', 'application/json');

try {
    $response2 = $kernel->handle($request2);
    echo "Status Code: " . $response2->getStatusCode() . "\n";
    
    $content2 = $response2->getContent();
    $data2 = json_decode($content2, true);
    
    if ($data2 && isset($data2['data']['data'])) {
        echo "Primary scholarships count: " . count($data2['data']['data']) . "\n";
        
        if (count($data2['data']['data']) > 0) {
            $primaryScholarship = $data2['data']['data'][0];
            if (isset($primaryScholarship['category_instructions'])) {
                echo "Category instructions present: ✓\n";
                echo "Instruction: " . $primaryScholarship['category_instructions']['instruction'] . "\n";
            } else {
                echo "Category instructions missing: ✗\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test individual scholarship endpoint
echo "Test 3: GET /api/v1/public-scholarships/{id}\n";
echo "--------------------------------------------\n";

// First get a scholarship ID from the list
$listRequest = Request::create('/api/v1/public-scholarships', 'GET');
$listRequest->headers->set('Accept', 'application/json');

try {
    $listResponse = $kernel->handle($listRequest);
    $listData = json_decode($listResponse->getContent(), true);
    
    if ($listData && isset($listData['data']['data']) && count($listData['data']['data']) > 0) {
        $scholarshipId = $listData['data']['data'][0]['id'];
        
        $detailRequest = Request::create('/api/v1/public-scholarships/' . $scholarshipId, 'GET');
        $detailRequest->headers->set('Accept', 'application/json');
        
        $detailResponse = $kernel->handle($detailRequest);
        echo "Status Code: " . $detailResponse->getStatusCode() . "\n";
        
        $detailData = json_decode($detailResponse->getContent(), true);
        
        if ($detailData && isset($detailData['data'])) {
            echo "Individual scholarship data retrieved: ✓\n";
            
            $scholarship = $detailData['data'];
            $detailFields = ['application_instructions', 'terms_conditions', 'requirements'];
            $presentDetailFields = [];
            
            foreach ($detailFields as $field) {
                if (isset($scholarship[$field])) {
                    $presentDetailFields[] = $field;
                }
            }
            
            echo "Detail fields present: " . implode(', ', $presentDetailFields) . "\n";
        }
    } else {
        echo "No scholarships available for detail test\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n========================================\n";
echo "API Testing Complete\n";

$kernel->terminate($request, $response);
