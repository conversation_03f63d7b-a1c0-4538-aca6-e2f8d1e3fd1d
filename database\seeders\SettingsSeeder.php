<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'app_name',
                'value' => config('app.name', 'Laravel NGO'),
                'type' => 'text',
                'description' => 'Application name displayed throughout the site',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'app_logo',
                'value' => null,
                'type' => 'image',
                'description' => 'Application logo displayed in header and navigation',
                'group' => 'appearance',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'Making a difference in communities through sustainable development programs, education initiatives, and empowerment projects.',
                'type' => 'textarea',
                'description' => 'Site description for SEO and public display',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'contact_email',
                'value' => config('mail.from.address', '<EMAIL>'),
                'type' => 'email',
                'description' => 'Main contact email address',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+234 ************',
                'type' => 'text',
                'description' => 'Main contact phone number',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_address',
                'value' => '123 Foundation Street, Lagos, Nigeria',
                'type' => 'textarea',
                'description' => 'Organization physical address',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_city',
                'value' => 'Lagos',
                'type' => 'text',
                'description' => 'Organization city',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_state',
                'value' => 'Lagos State',
                'type' => 'text',
                'description' => 'Organization state/province',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_country',
                'value' => 'Nigeria',
                'type' => 'text',
                'description' => 'Organization country',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_postal_code',
                'value' => '100001',
                'type' => 'text',
                'description' => 'Organization postal/zip code',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'social_facebook',
                'value' => null,
                'type' => 'url',
                'description' => 'Facebook page URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_twitter',
                'value' => null,
                'type' => 'url',
                'description' => 'Twitter profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_instagram',
                'value' => null,
                'type' => 'url',
                'description' => 'Instagram profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_linkedin',
                'value' => null,
                'type' => 'url',
                'description' => 'LinkedIn page URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_youtube',
                'value' => null,
                'type' => 'url',
                'description' => 'YouTube channel URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_tiktok',
                'value' => null,
                'type' => 'url',
                'description' => 'TikTok profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_whatsapp',
                'value' => null,
                'type' => 'text',
                'description' => 'WhatsApp contact number (with country code)',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_telegram',
                'value' => null,
                'type' => 'url',
                'description' => 'Telegram channel/group URL',
                'group' => 'social',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
