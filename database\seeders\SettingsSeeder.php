<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'app_name',
                'value' => config('app.name', 'Laravel NGO'),
                'type' => 'text',
                'description' => 'Application name displayed throughout the site',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'app_logo',
                'value' => null,
                'type' => 'image',
                'description' => 'Application logo displayed in header and navigation',
                'group' => 'appearance',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'Making a difference in communities through sustainable development programs, education initiatives, and empowerment projects.',
                'type' => 'textarea',
                'description' => 'Site description for SEO and public display',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'contact_email',
                'value' => config('mail.from.address', '<EMAIL>'),
                'type' => 'email',
                'description' => 'Main contact email address',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+234 ************',
                'type' => 'text',
                'description' => 'Main contact phone number',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_address',
                'value' => '123 Foundation Street, Lagos, Nigeria',
                'type' => 'textarea',
                'description' => 'Organization physical address',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_city',
                'value' => 'Lagos',
                'type' => 'text',
                'description' => 'Organization city',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_state',
                'value' => 'Lagos State',
                'type' => 'text',
                'description' => 'Organization state/province',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_country',
                'value' => 'Nigeria',
                'type' => 'text',
                'description' => 'Organization country',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'organization_postal_code',
                'value' => '100001',
                'type' => 'text',
                'description' => 'Organization postal/zip code',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'social_facebook',
                'value' => null,
                'type' => 'url',
                'description' => 'Facebook page URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_twitter',
                'value' => null,
                'type' => 'url',
                'description' => 'Twitter profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_instagram',
                'value' => null,
                'type' => 'url',
                'description' => 'Instagram profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_linkedin',
                'value' => null,
                'type' => 'url',
                'description' => 'LinkedIn page URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_youtube',
                'value' => null,
                'type' => 'url',
                'description' => 'YouTube channel URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_tiktok',
                'value' => null,
                'type' => 'url',
                'description' => 'TikTok profile URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_whatsapp',
                'value' => null,
                'type' => 'text',
                'description' => 'WhatsApp contact number (with country code)',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'social_telegram',
                'value' => null,
                'type' => 'url',
                'description' => 'Telegram channel/group URL',
                'group' => 'social',
                'is_public' => true,
            ],
            [
                'key' => 'office_hours',
                'value' => 'Monday - Friday: 9:00 AM - 5:00 PM\nSaturday: 10:00 AM - 2:00 PM\nSunday: Closed',
                'type' => 'textarea',
                'description' => 'Office operating hours',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'emergency_contact',
                'value' => 'For urgent educational support or emergency assistance',
                'type' => 'textarea',
                'description' => 'Emergency contact description',
                'group' => 'contact',
                'is_public' => true,
            ],
            [
                'key' => 'emergency_hotline',
                'value' => '+234 800 KOFA (5632)',
                'type' => 'text',
                'description' => '24/7 emergency hotline number',
                'group' => 'contact',
                'is_public' => true,
            ],
            // Donation Settings
            [
                'key' => 'donation_account_number',
                'value' => '**********',
                'type' => 'text',
                'description' => 'Organization bank account number for donations',
                'group' => 'donation',
                'is_public' => true,
            ],
            [
                'key' => 'donation_account_name',
                'value' => 'HALIMAKQ Foundation',
                'type' => 'text',
                'description' => 'Organization bank account name for donations',
                'group' => 'donation',
                'is_public' => true,
            ],
            [
                'key' => 'donation_bank_name',
                'value' => 'First Bank of Nigeria',
                'type' => 'text',
                'description' => 'Organization bank name for donations',
                'group' => 'donation',
                'is_public' => true,
            ],
            [
                'key' => 'paystack_public_key',
                'value' => '',
                'type' => 'text',
                'description' => 'Paystack public key for payment processing',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'paystack_secret_key',
                'value' => '',
                'type' => 'password',
                'description' => 'Paystack secret key for payment processing',
                'group' => 'payment',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
