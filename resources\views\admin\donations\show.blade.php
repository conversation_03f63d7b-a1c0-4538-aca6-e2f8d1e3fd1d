@extends('admin.layouts.app')

@section('title', 'Donation Details')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Donation Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.donations.index') }}">Donations</a></li>
                    <li class="breadcrumb-item active">Details</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.donations.edit', $donation->id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Donation
            </a>
            <a href="{{ route('admin.donations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Donation Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Information</h6>
                    <span class="badge badge-{{ $donation->payment_status === 'completed' ? 'success' : ($donation->payment_status === 'pending' ? 'warning' : 'danger') }}">
                        {{ ucfirst($donation->payment_status) }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Donation ID:</label>
                                <p class="text-muted">#{{ $donation->id }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Amount:</label>
                                <p class="text-success font-weight-bold">₦{{ number_format($donation->amount, 2) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Currency:</label>
                                <p class="text-muted">{{ strtoupper($donation->currency) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Donation Type:</label>
                                <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $donation->donation_type)) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Payment Method:</label>
                                <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $donation->payment_method)) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Payment Status:</label>
                                <p class="text-muted">{{ ucfirst($donation->payment_status) }}</p>
                            </div>
                        </div>
                        @if($donation->purpose)
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="font-weight-bold">Purpose:</label>
                                <p class="text-muted">{{ $donation->purpose }}</p>
                            </div>
                        </div>
                        @endif
                        @if($donation->notes)
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="font-weight-bold">Notes:</label>
                                <p class="text-muted">{{ $donation->notes }}</p>
                            </div>
                        </div>
                        @endif
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Created:</label>
                                <p class="text-muted">{{ $donation->created_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Last Updated:</label>
                                <p class="text-muted">{{ $donation->updated_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Donor Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donor Information</h6>
                </div>
                <div class="card-body">
                    @if($donation->donor)
                        <div class="form-group">
                            <label class="font-weight-bold">Name:</label>
                            <p class="text-muted">{{ $donation->donor->first_name }} {{ $donation->donor->last_name }}</p>
                        </div>
                        <div class="form-group">
                            <label class="font-weight-bold">Email:</label>
                            <p class="text-muted">{{ $donation->donor->email }}</p>
                        </div>
                        @if($donation->donor->phone)
                        <div class="form-group">
                            <label class="font-weight-bold">Phone:</label>
                            <p class="text-muted">{{ $donation->donor->phone }}</p>
                        </div>
                        @endif
                    @else
                        <p class="text-muted">{{ $donation->is_anonymous ? 'Anonymous Donor' : 'Donor information not available' }}</p>
                    @endif
                </div>
            </div>

            @if($donation->campaign)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Information</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Campaign:</label>
                        <p class="text-muted">{{ $donation->campaign->title }}</p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Goal:</label>
                        <p class="text-muted">₦{{ number_format($donation->campaign->goal_amount, 2) }}</p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
