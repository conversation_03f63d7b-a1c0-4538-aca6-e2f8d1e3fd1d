'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, School, GraduationCap, Calendar, Users, DollarSign, ArrowRight, Filter, Search, AlertCircle } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { apiClient } from '@/lib/api'

interface Scholarship {
  id: number
  title: string
  slug: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria?: string
  requirements?: string
  amount: number
  application_deadline: string
  status: string
  current_applicants: number
  max_applicants?: number
  created_at: string
  updated_at: string
}

const categoryIcons = {
  primary: BookOpen,
  secondary: School,
  university: GraduationCap
}

const categoryColors = {
  primary: 'bg-blue-100 text-blue-800 border-blue-200',
  secondary: 'bg-green-100 text-green-800 border-green-200',
  university: 'bg-purple-100 text-purple-800 border-purple-200'
}

const categoryDescriptions = {
  primary: 'For students in grades 1-6',
  secondary: 'For students in grades 7-12',
  university: 'For undergraduate and graduate students'
}

export default function ScholarshipsPage() {
  const [scholarships, setScholarships] = useState<Scholarship[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  useEffect(() => {
    fetchScholarships()
  }, [])

  const fetchScholarships = async () => {
    try {
      setLoading(true)
      const response = await apiClient.get('/api/v1/public-scholarships')
      
      if (response.success) {
        setScholarships(response.data || [])
      } else {
        setError(response.message || 'Failed to fetch scholarships')
      }
    } catch (err) {
      console.error('Error fetching scholarships:', err)
      setError('Failed to load scholarships. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const filteredScholarships = scholarships.filter(scholarship => {
    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || scholarship.category === selectedCategory
    const matchesStatus = selectedStatus === 'all' || scholarship.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const isDeadlinePassed = (deadline: string) => {
    return new Date(deadline) < new Date()
  }

  const getDaysUntilDeadline = (deadline: string) => {
    const deadlineDate = new Date(deadline)
    const today = new Date()
    const diffTime = deadlineDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading scholarships...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Scholarships</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={fetchScholarships} className="bg-green-600 hover:bg-green-700">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Educational Scholarships
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Empowering students through accessible education funding opportunities
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold">
                Browse Scholarships
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-white shadow-sm">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search scholarships..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="primary">Primary</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="university">University</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Scholarships Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {filteredScholarships.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">No Scholarships Found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all'
                  ? 'Try adjusting your search criteria or filters.'
                  : 'No scholarships are currently available.'}
              </p>
              {(searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all') && (
                <Button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                    setSelectedStatus('all')
                  }}
                  variant="outline"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <>
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  Available Scholarships
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Discover opportunities that match your educational goals and aspirations
                </p>
                <div className="mt-6">
                  <Badge variant="secondary" className="text-lg px-4 py-2">
                    {filteredScholarships.length} {filteredScholarships.length === 1 ? 'Scholarship' : 'Scholarships'} Available
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredScholarships.map((scholarship) => {
                  const IconComponent = categoryIcons[scholarship.category]
                  const isExpired = isDeadlinePassed(scholarship.application_deadline)
                  const daysLeft = getDaysUntilDeadline(scholarship.application_deadline)
                  const spotsLeft = scholarship.max_applicants ? scholarship.max_applicants - scholarship.current_applicants : null

                  return (
                    <Card key={scholarship.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden">
                      <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                        <div className="flex items-center justify-between mb-4">
                          <IconComponent className="h-8 w-8" />
                          <Badge className={`${categoryColors[scholarship.category]} text-xs font-medium`}>
                            {scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)}
                          </Badge>
                        </div>
                        <h3 className="text-xl font-bold mb-2 group-hover:text-blue-100 transition-colors">
                          {scholarship.title}
                        </h3>
                        <p className="text-blue-100 text-sm">
                          {categoryDescriptions[scholarship.category]}
                        </p>
                      </div>

                      <CardContent className="p-6">
                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {scholarship.description}
                        </p>

                        <div className="space-y-3 mb-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span className="text-sm text-gray-600">Amount</span>
                            </div>
                            <span className="font-semibold text-green-600">
                              {formatCurrency(scholarship.amount)}
                            </span>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-blue-600" />
                              <span className="text-sm text-gray-600">Deadline</span>
                            </div>
                            <span className={`text-sm font-medium ${isExpired ? 'text-red-600' : daysLeft <= 7 ? 'text-orange-600' : 'text-gray-900'}`}>
                              {formatDate(scholarship.application_deadline)}
                            </span>
                          </div>

                          {spotsLeft !== null && (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-purple-600" />
                                <span className="text-sm text-gray-600">Spots Left</span>
                              </div>
                              <span className={`text-sm font-medium ${spotsLeft <= 5 ? 'text-red-600' : 'text-gray-900'}`}>
                                {spotsLeft > 0 ? spotsLeft : 'Full'}
                              </span>
                            </div>
                          )}
                        </div>

                        {!isExpired && (spotsLeft === null || spotsLeft > 0) && (
                          <div className="mb-4">
                            {daysLeft <= 7 && daysLeft > 0 && (
                              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3">
                                <div className="flex items-center gap-2">
                                  <AlertCircle className="h-4 w-4 text-orange-600" />
                                  <span className="text-sm font-medium text-orange-800">
                                    Only {daysLeft} {daysLeft === 1 ? 'day' : 'days'} left to apply!
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        <div className="flex gap-3">
                          <Link href={`/scholarships/${scholarship.id}`} className="flex-1">
                            <Button variant="outline" className="w-full">
                              View Details
                            </Button>
                          </Link>
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700 flex-1"
                            disabled={isExpired || (spotsLeft !== null && spotsLeft <= 0)}
                            onClick={() => {
                              const laravelBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
                              window.location.href = `${laravelBaseUrl}/login`
                            }}
                          >
                            {isExpired ? 'Expired' : (spotsLeft !== null && spotsLeft <= 0) ? 'Full' : 'Apply Now'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Educational Journey?
            </h2>
            <p className="text-xl mb-8 text-green-100">
              Don't miss out on these life-changing opportunities. Apply today and take the first step towards your dreams.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold"
                onClick={() => {
                  const laravelBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
                  window.location.href = `${laravelBaseUrl}/login`
                }}
              >
                Start Application
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                  Need Help?
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
