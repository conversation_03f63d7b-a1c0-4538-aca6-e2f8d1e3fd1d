'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, School, GraduationCap, Calendar, Users, DollarSign, ArrowRight, Filter, Search, AlertCircle } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { apiClient } from '@/lib/api'
import { BACKEND_BASE_URL } from '@/lib/constants'

interface Scholarship {
  id: number
  title: string
  slug: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria?: string
  requirements?: string
  amount: number
  application_deadline: string
  status: string
  current_applicants: number
  max_applicants?: number
  created_at: string
  updated_at: string
}

const categoryIcons = {
  primary: BookOpen,
  secondary: School,
  university: GraduationCap
}

const categoryColors = {
  primary: 'bg-blue-100 text-blue-800 border-blue-200',
  secondary: 'bg-green-100 text-green-800 border-green-200',
  university: 'bg-purple-100 text-purple-800 border-purple-200'
}

const categoryDescriptions = {
  primary: 'For students in grades 1-6',
  secondary: 'For students in grades 7-12',
  university: 'For undergraduate and graduate students'
}

export default function ScholarshipsPage() {
  const [scholarships, setScholarships] = useState<Scholarship[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  useEffect(() => {
    fetchScholarships()
  }, [])

  const fetchScholarships = async () => {
    try {
      setLoading(true)
      const response = await apiClient.get('/public-scholarships')
      
      if (response.success) {
        // Handle paginated response structure
        const scholarshipsData = response.data?.data || response.data || []
        setScholarships(Array.isArray(scholarshipsData) ? scholarshipsData : [])
      } else {
        setError(response.message || 'Failed to fetch scholarships')
      }
    } catch (err) {
      console.error('Error fetching scholarships:', err)
      setError('Failed to load scholarships. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const filteredScholarships = (Array.isArray(scholarships) ? scholarships : []).filter(scholarship => {
    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || scholarship.category === selectedCategory
    const matchesStatus = selectedStatus === 'all' || scholarship.status === selectedStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const isDeadlinePassed = (deadline: string) => {
    return new Date(deadline) < new Date()
  }

  const getDaysUntilDeadline = (deadline: string) => {
    const deadlineDate = new Date(deadline)
    const today = new Date()
    const diffTime = deadlineDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col bg-pattern">
        <main className="flex-1">
          <div className="container py-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading scholarships...</p>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col bg-pattern">
        <main className="flex-1">
          <div className="container py-20">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Error Loading Scholarships</h2>
              <p className="text-muted-foreground mb-6">{error}</p>
              <Button onClick={fetchScholarships} className="bg-green-600 hover:bg-green-700">
                Try Again
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-pattern">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden">
          <div className="absolute inset-0 bg-grid opacity-20"></div>
          <div className="container relative">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Scholarships</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up">
                Educational Scholarships
              </h1>
              <p
                className="text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto"
                style={{ animationDelay: "0.2s" }}
              >
                Empowering students through accessible education funding opportunities across Nigeria
              </p>
              <div
                className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{filteredScholarships.length}</div>
                  <div className="text-green-200 text-sm">Available Scholarships</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">3</div>
                  <div className="text-green-200 text-sm">Categories</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Filters Section */}
        <AnimatedSection className="py-12 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold tracking-tight mb-4">Find Your Perfect Scholarship</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Use our filters to discover scholarships that match your educational level and goals
              </p>
            </div>
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-center max-w-4xl mx-auto">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search scholarships..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 neumorphic border-green-200 focus:border-green-400"
                  />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[180px] neumorphic border-green-200">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="primary">Primary</SelectItem>
                    <SelectItem value="secondary">Secondary</SelectItem>
                    <SelectItem value="university">University</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[180px] neumorphic border-green-200">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Scholarships Grid */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            {filteredScholarships.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-2xl font-semibold text-gray-900 mb-2">No Scholarships Found</h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all'
                    ? 'Try adjusting your search criteria or filters.'
                    : 'No scholarships are currently available.'}
                </p>
                {(searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all') && (
                  <Button
                    onClick={() => {
                      setSearchTerm('')
                      setSelectedCategory('all')
                      setSelectedStatus('all')
                    }}
                    variant="outline"
                    className="border-green-200 text-green-700 hover:bg-green-50"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <>
                <div className="text-center mb-16">
                  <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Available Scholarships</h2>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Discover opportunities that match your educational goals and aspirations
                  </p>
                  <div className="mt-6">
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full text-lg px-4 py-2">
                      {filteredScholarships.length} {filteredScholarships.length === 1 ? 'Scholarship' : 'Scholarships'} Available
                    </Badge>
                  </div>
                </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredScholarships.map((scholarship) => {
                  const IconComponent = categoryIcons[scholarship.category]
                  const isExpired = isDeadlinePassed(scholarship.application_deadline)
                  const daysLeft = getDaysUntilDeadline(scholarship.application_deadline)
                  const spotsLeft = scholarship.max_applicants ? scholarship.max_applicants - scholarship.current_applicants : null

                  return (
                    <Card
                      key={scholarship.id}
                      className="overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-6">
                          <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                            <IconComponent className="h-6 w-6 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-green-800 dark:text-green-200 mb-1">{scholarship.title}</h3>
                            <Badge className={`${categoryColors[scholarship.category]} text-xs font-medium rounded-full`}>
                              {scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)}
                            </Badge>
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-4 mb-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">{formatCurrency(scholarship.amount)}</div>
                            <div className="text-xs text-muted-foreground">Amount</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">{daysLeft > 0 ? daysLeft : 0}</div>
                            <div className="text-xs text-muted-foreground">Days Left</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">{spotsLeft !== null ? (spotsLeft > 0 ? spotsLeft : 0) : '∞'}</div>
                            <div className="text-xs text-muted-foreground">Spots Left</div>
                          </div>
                        </div>

                        <div className="mb-6">
                          <div className="flex justify-between text-sm mb-2">
                            <span>Application Status</span>
                            <span className={isExpired ? 'text-red-600' : 'text-green-600'}>
                              {isExpired ? 'Closed' : 'Open'}
                            </span>
                          </div>
                          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className={`h-full transition-all duration-300 ${isExpired ? 'bg-red-500' : 'bg-green-500'}`}
                              style={{ width: isExpired ? '100%' : `${Math.max(10, 100 - (daysLeft / 30) * 100)}%` }}
                            />
                          </div>
                        </div>

                        {daysLeft <= 7 && daysLeft > 0 && !isExpired && (
                          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                            <div className="flex items-center gap-2">
                              <AlertCircle className="h-4 w-4 text-orange-600" />
                              <span className="text-sm font-medium text-orange-800">
                                Only {daysLeft} {daysLeft === 1 ? 'day' : 'days'} left to apply!
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="flex gap-3">
                          <Link href={`/scholarships/${scholarship.id}`} className="flex-1">
                            <Button variant="outline" className="w-full border-green-200 text-green-700 hover:bg-green-50">
                              View Details
                            </Button>
                          </Link>
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700 flex-1"
                            disabled={isExpired || (spotsLeft !== null && spotsLeft <= 0)}
                            onClick={() => {
                              window.location.href = `${BACKEND_BASE_URL}/login`
                            }}
                          >
                            {isExpired ? 'Expired' : (spotsLeft !== null && spotsLeft <= 0) ? 'Full' : 'Apply Now'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </>
          )}
          </div>
        </AnimatedSection>

        {/* CTA Section */}
        <AnimatedSection className="py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Ready to Start Your Educational Journey?
              </h2>
              <p className="text-xl text-green-100 mb-8">
                Don't miss out on these life-changing opportunities. Apply today and take the first step towards your dreams.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold"
                  onClick={() => {
                    window.location.href = `${BACKEND_BASE_URL}/login`
                  }}
                >
                  Start Application
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Link href="/contact">
                  <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                    Need Help?
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </main>
    </div>
  )
}
