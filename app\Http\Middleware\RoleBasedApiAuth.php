<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Laravel\Sanctum\PersonalAccessToken;

class RoleBasedApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  ...$roles
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$roles)
    {
        // Check authentication
        if (!Auth::check()) {
            $this->logSecurityEvent('unauthenticated_access', $request);
            return $this->unauthorizedResponse('Authentication required');
        }

        $user = Auth::user();

        // Check if user is active
        if (!$user->isActive()) {
            $this->logSecurityEvent('inactive_user_access', $request, $user);
            return $this->forbiddenResponse('Account is inactive');
        }

        // Validate token if using Sanctum
        if ($request->bearerToken()) {
            $token = PersonalAccessToken::findToken($request->bearerToken());
            if (!$token || $this->isTokenExpired($token)) {
                $this->logSecurityEvent('expired_token_access', $request, $user);
                return $this->unauthorizedResponse('Token expired or invalid');
            }

            // Update token last used timestamp
            $token->forceFill(['last_used_at' => now()])->save();
        }

        // Check role-based access
        if (!empty($roles) && !$this->hasRequiredRole($user, $roles)) {
            $this->logSecurityEvent('insufficient_permissions', $request, $user, [
                'required_roles' => $roles,
                'user_role' => $user->role
            ]);
            return $this->forbiddenResponse('Insufficient permissions', $roles, $user->role);
        }

        // Apply rate limiting based on user role
        $this->applyRateLimiting($request, $user);

        // Log successful access for audit trail
        $this->logApiAccess($request, $user);

        return $next($request);
    }

    /**
     * Check if user has any of the required roles
     */
    private function hasRequiredRole($user, array $roles): bool
    {
        // Check direct role match
        if (in_array($user->role, $roles)) {
            return true;
        }

        // Check Spatie permissions if available
        if (method_exists($user, 'hasAnyRole')) {
            return $user->hasAnyRole($roles);
        }

        return false;
    }

    /**
     * Check if token is expired
     */
    private function isTokenExpired($token): bool
    {
        if (!$token->expires_at) {
            return false;
        }

        return $token->expires_at->isPast();
    }

    /**
     * Apply role-based rate limiting
     */
    private function applyRateLimiting(Request $request, $user): void
    {
        $key = 'api_rate_limit:' . $user->id;
        $maxAttempts = $this->getRateLimitForRole($user->role);
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $this->logSecurityEvent('rate_limit_exceeded', $request, $user);
            abort(429, 'Too many requests');
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Get rate limit based on user role
     */
    private function getRateLimitForRole(string $role): int
    {
        $limits = [
            'admin' => 2000,
            'partner_organization' => 1000,
            'volunteer' => 500,
            'user' => 300,
            'student' => 300,
        ];

        return $limits[$role] ?? 100;
    }

    /**
     * Log security events
     */
    private function logSecurityEvent(string $event, Request $request, $user = null, array $context = []): void
    {
        $logData = [
            'event' => $event,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString(),
        ];

        if ($user) {
            $logData['user_id'] = $user->id;
            $logData['user_role'] = $user->role;
            $logData['user_email'] = $user->email;
        }

        $logData = array_merge($logData, $context);

        Log::channel('security')->warning("API Security Event: {$event}", $logData);
    }

    /**
     * Log successful API access for audit trail
     */
    private function logApiAccess(Request $request, $user): void
    {
        // Only log for sensitive endpoints or admin actions
        if ($this->shouldLogAccess($request)) {
            Log::channel('api')->info('API Access', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Determine if access should be logged
     */
    private function shouldLogAccess(Request $request): bool
    {
        $sensitiveEndpoints = [
            'admin/',
            'scholarships/',
            'users/',
            'donations/',
            'volunteer/',
        ];

        $path = $request->path();
        foreach ($sensitiveEndpoints as $endpoint) {
            if (str_contains($path, $endpoint)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Return unauthorized response
     */
    private function unauthorizedResponse(string $message): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => 'Unauthenticated',
            'error_code' => 'AUTH_REQUIRED'
        ], 401);
    }

    /**
     * Return forbidden response
     */
    private function forbiddenResponse(string $message, array $requiredRoles = [], string $userRole = null): \Illuminate\Http\JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'error' => 'Forbidden',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ];

        if (!empty($requiredRoles)) {
            $response['required_roles'] = $requiredRoles;
        }

        if ($userRole) {
            $response['user_role'] = $userRole;
        }

        return response()->json($response, 403);
    }
}
