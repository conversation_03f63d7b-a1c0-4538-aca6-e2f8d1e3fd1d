@extends('layouts.admin')

@section('title', 'Create New Volunteer')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Volunteer</h1>
        <a href="{{ route('admin.volunteers.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Volunteers
        </a>
    </div>

    <!-- Display Validation Errors -->
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Volunteer Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.volunteers.store') }}" method="POST">
                        @csrf
                        
                        <!-- User Selection -->
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Select User <span class="text-danger">*</span></label>
                            <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                <option value="">Select a user</option>
                                @foreach(\App\Models\User::all() as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Application Status -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="application_status" class="form-label">Application Status</label>
                                    <select class="form-select @error('application_status') is-invalid @enderror" id="application_status" name="application_status">
                                        <option value="pending" {{ old('application_status', 'pending') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="approved" {{ old('application_status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                        <option value="rejected" {{ old('application_status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                    </select>
                                    @error('application_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Skills -->
                        <div class="mb-3">
                            <label for="skills" class="form-label">Skills</label>
                            <textarea class="form-control @error('skills') is-invalid @enderror" 
                                      id="skills" name="skills" rows="3" placeholder="Enter skills separated by commas">{{ old('skills') }}</textarea>
                            @error('skills')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Enter skills separated by commas (e.g., Teaching, Healthcare, IT Support)</small>
                        </div>

                        <!-- Interests -->
                        <div class="mb-3">
                            <label for="interests" class="form-label">Interests</label>
                            <textarea class="form-control @error('interests') is-invalid @enderror" 
                                      id="interests" name="interests" rows="3" placeholder="Enter interests separated by commas">{{ old('interests') }}</textarea>
                            @error('interests')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Enter interests separated by commas (e.g., Education, Healthcare, Environment)</small>
                        </div>

                        <!-- Experience -->
                        <div class="mb-3">
                            <label for="experience" class="form-label">Experience</label>
                            <textarea class="form-control @error('experience') is-invalid @enderror" 
                                      id="experience" name="experience" rows="4" placeholder="Describe relevant experience">{{ old('experience') }}</textarea>
                            @error('experience')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Motivation -->
                        <div class="mb-3">
                            <label for="motivation" class="form-label">Motivation</label>
                            <textarea class="form-control @error('motivation') is-invalid @enderror" 
                                      id="motivation" name="motivation" rows="4" placeholder="Why do you want to volunteer?">{{ old('motivation') }}</textarea>
                            @error('motivation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Availability -->
                        <div class="mb-3">
                            <label for="availability" class="form-label">Availability</label>
                            <textarea class="form-control @error('availability') is-invalid @enderror" 
                                      id="availability" name="availability" rows="3" placeholder="When are you available?">{{ old('availability') }}</textarea>
                            @error('availability')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Describe your availability (e.g., Weekends, Evenings, Full-time)</small>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="mb-3">
                            <label for="emergency_contact" class="form-label">Emergency Contact</label>
                            <textarea class="form-control @error('emergency_contact') is-invalid @enderror" 
                                      id="emergency_contact" name="emergency_contact" rows="3" placeholder="Emergency contact information">{{ old('emergency_contact') }}</textarea>
                            @error('emergency_contact')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Name, relationship, phone number</small>
                        </div>

                        <!-- Background Check -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="background_check_status" class="form-label">Background Check Status</label>
                                    <select class="form-select @error('background_check_status') is-invalid @enderror" id="background_check_status" name="background_check_status">
                                        <option value="">Not Started</option>
                                        <option value="pending" {{ old('background_check_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="passed" {{ old('background_check_status') == 'passed' ? 'selected' : '' }}>Passed</option>
                                        <option value="failed" {{ old('background_check_status') == 'failed' ? 'selected' : '' }}>Failed</option>
                                    </select>
                                    @error('background_check_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="background_check_date" class="form-label">Background Check Date</label>
                                    <input type="date" class="form-control @error('background_check_date') is-invalid @enderror" 
                                           id="background_check_date" name="background_check_date" value="{{ old('background_check_date') }}">
                                    @error('background_check_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Hours Logged -->
                        <div class="mb-3">
                            <label for="hours_logged" class="form-label">Hours Logged</label>
                            <input type="number" step="0.01" class="form-control @error('hours_logged') is-invalid @enderror" 
                                   id="hours_logged" name="hours_logged" value="{{ old('hours_logged', 0) }}" min="0">
                            @error('hours_logged')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" placeholder="Internal notes about this volunteer">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Volunteer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Guidelines -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Volunteer Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Select an existing user from the system
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Set appropriate application status
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Document relevant skills and experience
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Complete background check if required
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Track volunteer hours accurately
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
