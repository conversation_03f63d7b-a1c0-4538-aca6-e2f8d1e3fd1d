

<?php $__env->startSection('title', 'Programs Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Programs Management</h1>
        <a href="<?php echo e(route('admin.programs.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Program
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Programs</div>
                            <div class="h4"><?php echo e($stats['total'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-project-diagram fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Programs</div>
                            <div class="h4"><?php echo e($stats['active'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Completed Programs</div>
                            <div class="h4"><?php echo e($stats['completed'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Budget</div>
                            <div class="h4">₦<?php echo e(number_format(\App\Models\Program::sum('budget') ?? 0, 0)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Programs Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Programs</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search programs..." id="searchPrograms">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="planning">Planning</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterType">
                        <option value="">All Types</option>
                        <option value="education">Education</option>
                        <option value="health">Health</option>
                        <option value="poverty">Poverty Alleviation</option>
                        <option value="environment">Environment</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Program</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Budget</th>
                            <th>Beneficiaries</th>
                            <th>Progress</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($program->featured_image): ?>
                                        <img src="<?php echo e($program->featured_image); ?>" alt="<?php echo e($program->title); ?>" class="rounded me-3" width="50" height="50" style="object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-primary rounded d-flex align-items-center justify-content-center me-3 text-white" style="width: 50px; height: 50px;">
                                            <i class="fas fa-project-diagram"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-bold"><?php echo e($program->title); ?></div>
                                        <small class="text-muted"><?php echo e($program->slug); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <?php echo e(Str::limit($program->description, 100)); ?>

                                </div>
                                <?php if($program->location): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i> 
                                        <?php echo e(is_array($program->location) ? ($program->location['address'] ?? 'Multiple locations') : $program->location); ?>

                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($program->status === 'active' ? 'success' : 
                                    ($program->status === 'completed' ? 'info' : 
                                    ($program->status === 'suspended' ? 'danger' : 'warning'))); ?>">
                                    <?php echo e(ucfirst($program->status)); ?>

                                </span>
                            </td>
                            <td>
                                <?php if($program->start_date): ?>
                                    <div class="small">
                                        <strong>Start:</strong> <?php echo e($program->start_date->format('M d, Y')); ?><br>
                                        <?php if($program->end_date): ?>
                                            <strong>End:</strong> <?php echo e($program->end_date->format('M d, Y')); ?>

                                        <?php else: ?>
                                            <span class="text-muted">Ongoing</span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Not scheduled</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($program->budget): ?>
                                    <strong>₦<?php echo e(number_format($program->budget, 0)); ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if(isset($program->impact_metrics['peopleReached'])): ?>
                                    <span class="text-success"><?php echo e(number_format($program->impact_metrics['peopleReached'])); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">0</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                    $progress = 0;
                                    if($program->start_date && $program->end_date) {
                                        $total = $program->start_date->diffInDays($program->end_date);
                                        $elapsed = $program->start_date->diffInDays(now());
                                        $progress = $total > 0 ? min(100, ($elapsed / $total) * 100) : 0;
                                    }
                                ?>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" style="width: <?php echo e($progress); ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo e(round($progress)); ?>%</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('programs.show', $program->slug)); ?>" target="_blank" class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.programs.show', $program->id)); ?>" class="btn btn-sm btn-outline-primary" title="Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.programs.edit', $program->id)); ?>" class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger delete-program-btn"
                                            data-program-id="<?php echo e($program->id); ?>"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No programs found</p>
                                <a href="<?php echo e(route('admin.programs.create')); ?>" class="btn btn-primary">Create Your First Program</a>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing <?php echo e($programs->firstItem()); ?> to <?php echo e($programs->lastItem()); ?> of <?php echo e($programs->total()); ?> results
                </div>
                <?php echo e($programs->links()); ?>

            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteProgramModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this program? This action cannot be undone and will affect all related data.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteProgram">Delete Program</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete program functionality
    const deleteProgramButtons = document.querySelectorAll('.delete-program-btn');
    deleteProgramButtons.forEach(button => {
        button.addEventListener('click', function() {
            const programId = this.getAttribute('data-program-id');
            deleteProgram(programId);
        });
    });
});

function deleteProgram(programId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteProgramModal'));
    modal.show();

    document.getElementById('confirmDeleteProgram').addEventListener('click', function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/programs/${programId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();

        modal.hide();
    };
}

// Search functionality
document.getElementById('searchPrograms').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    const status = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        if (!status) {
            row.style.display = '';
            return;
        }

        const statusBadge = row.querySelector('.badge');
        if (statusBadge) {
            const rowStatus = statusBadge.textContent.toLowerCase();
            row.style.display = rowStatus.includes(status) ? '' : 'none';
        }
    });
});

document.getElementById('filterType').addEventListener('change', function() {
    console.log('Filter type:', this.value);
    // Type filtering can be implemented when program types are added to the database
});
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/programs/index.blade.php ENDPATH**/ ?>