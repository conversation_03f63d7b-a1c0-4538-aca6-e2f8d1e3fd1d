"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/partner/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/partner/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PartnerDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,School,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/partner/ProposalManager */ \"(app-pages-browser)/./components/dashboard/partner/ProposalManager.tsx\");\n/* harmony import */ var _components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/partner/ResourceSharing */ \"(app-pages-browser)/./components/dashboard/partner/ResourceSharing.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PartnerDashboard() {\n    var _user_preferences;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [partnershipApplications, setPartnershipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collaborationOpportunities, setCollaborationOpportunities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [upcomingEvents, setUpcomingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [programs, setPrograms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Scholarship-related state\n    const [availableScholarships, setAvailableScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [myStudents, setMyStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scholarshipApplications, setScholarshipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentStats, setStudentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PartnerDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"PartnerDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        // Fetch user data first\n                        const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getProfile();\n                        if (userData.success) {\n                            setUser(userData.data);\n                        }\n                        // Fetch partner-specific data\n                        const [eventsResponse, programsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getUpcomingEvents(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getPrograms()\n                        ]);\n                        const eventsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(eventsResponse);\n                        const programsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(programsResponse);\n                        setUpcomingEvents(eventsData.slice(0, 5));\n                        setPrograms(programsData.slice(0, 3));\n                        // Fetch scholarship-related data\n                        await fetchScholarshipData();\n                        setLoading(false);\n                    } catch (error) {\n                        console.error('Error fetching dashboard data:', error);\n                        setLoading(false);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchDashboardData\"];\n            const fetchScholarshipData = {\n                \"PartnerDashboard.useEffect.fetchScholarshipData\": async ()=>{\n                    try {\n                        console.log('Starting to fetch scholarship data...');\n                        // Fetch available scholarships for partner organizations (Primary/Secondary only)\n                        try {\n                            console.log('Fetching available scholarships...');\n                            const scholarshipsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/available');\n                            console.log('Scholarships response:', scholarshipsResponse);\n                            if (scholarshipsResponse.success) {\n                                setAvailableScholarships(scholarshipsResponse.data || []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching scholarships:', error);\n                        }\n                        // Fetch partner students\n                        try {\n                            console.log('Fetching partner students...');\n                            const studentsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students');\n                            console.log('Students response:', studentsResponse);\n                            if (studentsResponse.success) {\n                                var _studentsResponse_data;\n                                // Handle both paginated and direct array responses\n                                const studentsData = ((_studentsResponse_data = studentsResponse.data) === null || _studentsResponse_data === void 0 ? void 0 : _studentsResponse_data.data) || studentsResponse.data || [];\n                                setMyStudents(Array.isArray(studentsData) ? studentsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching students:', error);\n                        }\n                        // Fetch scholarship applications submitted by this partner\n                        try {\n                            console.log('Fetching my applications...');\n                            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/my-applications');\n                            console.log('Applications response:', applicationsResponse);\n                            if (applicationsResponse.success) {\n                                var _applicationsResponse_data;\n                                const applicationsData = ((_applicationsResponse_data = applicationsResponse.data) === null || _applicationsResponse_data === void 0 ? void 0 : _applicationsResponse_data.data) || applicationsResponse.data || [];\n                                setScholarshipApplications(Array.isArray(applicationsData) ? applicationsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching applications:', error);\n                        }\n                        // Fetch student statistics\n                        try {\n                            console.log('Fetching student statistics...');\n                            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students/statistics');\n                            console.log('Stats response:', statsResponse);\n                            if (statsResponse.success) {\n                                setStudentStats(statsResponse.data || {});\n                            }\n                        } catch (error) {\n                            console.error('Error fetching stats:', error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching scholarship data:', error);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchScholarshipData\"];\n            fetchDashboardData();\n        }\n    }[\"PartnerDashboard.useEffect\"], []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 170,\n            columnNumber: 12\n        }, this);\n    }\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'approved':\n                return 'bg-green-500';\n            case 'rejected':\n                return 'bg-red-500';\n            case 'pending':\n                return 'bg-yellow-500';\n            case 'under_review':\n                return 'bg-blue-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const partnerInfo = (user === null || user === void 0 ? void 0 : (_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.partner_data) || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    partnerInfo.organization_name || (user === null || user === void 0 ? void 0 : user.first_name),\n                                                    \"!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Partnership Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.organization_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: [\n                                                    partnerInfo.organization_type,\n                                                    \" • \",\n                                                    partnerInfo.sector\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/opportunities\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Opportunities\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/apply\",\n                                            children: \"Apply for Partnership\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"My Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Enrolled students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: availableScholarships.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Available scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: scholarshipApplications.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Scholarship applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Active Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.active_students) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Currently active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    defaultValue: \"scholarships\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"scholarships\",\n                                    children: \"Scholarships\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"students\",\n                                    children: \"My Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"applications\",\n                                    children: \"Applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"progression\",\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"opportunities\",\n                                    children: \"Opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"proposals\",\n                                    children: \"Proposals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"resources\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"scholarships\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Available Scholarships\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                children: [\n                                                                    availableScholarships.length,\n                                                                    \" Available\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        availableScholarships.slice(0, 3).map((scholarship)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: scholarship.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                                                    children: scholarship.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 322,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 322,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 325,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \"Deadline: \",\n                                                                                                new Date(scholarship.application_deadline).toLocaleDateString()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 325,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3 flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: scholarship.category === 'primary' ? 'default' : 'secondary',\n                                                                                            children: scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 330,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium text-green-600\",\n                                                                                            children: [\n                                                                                                \"₦\",\n                                                                                                scholarship.amount ? Number(scholarship.amount).toLocaleString() : 'Amount varies'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 333,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 329,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarship-application?scholarship_id=\".concat(scholarship.id, \"&category=\").concat(scholarship.category),\n                                                                                    children: \"Apply for Student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 341,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarships/\".concat(scholarship.id),\n                                                                                    children: \"View Details\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, scholarship.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        availableScholarships.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"No scholarships available at the moment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-2\",\n                                                                    children: \"Check back later for new opportunities\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        availableScholarships.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                \"View All Scholarships (\",\n                                                                availableScholarships.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Quick Actions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 381,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Add New Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 383,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Register a new student to your school\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 382,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Manage Students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 395,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View and manage all your students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 396,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 394,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Browse Scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 407,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Explore all available scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 408,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 406,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/analytics\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 417,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Analytics & Reports\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 419,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View performance insights and statistics\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 420,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Recent Scholarship Applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            scholarshipApplications.length,\n                                                            \" Total\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    scholarshipApplications.slice(0, 5).map((application)=>{\n                                                        var _application_student, _application_scholarship, _application_scholarship1, _application_scholarship2, _application_status, _application_status1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'Student Name'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.category) || application.scholarship_category || 'Category'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.title) || application.scholarship_title || 'Scholarship Title'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Applied: \",\n                                                                                        new Date(application.created_at).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 458,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Amount: ₦\",\n                                                                                        Number(application.scholarship.amount).toLocaleString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                            children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: \"View\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No scholarship applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Start applying for scholarships on behalf of your students\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: \"Browse Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"students\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All Students\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students/add\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add New Student\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.active_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Active Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.recent_enrollments) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Recent Enrollments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.scholarship_applications) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"My Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    myStudents.slice(0, 10).map((student)=>{\n                                                        var _student_progressions_, _student_progressions;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: student.full_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Age: \",\n                                                                                        student.age || 'N/A'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Grade: \",\n                                                                                        ((_student_progressions = student.progressions) === null || _student_progressions === void 0 ? void 0 : (_student_progressions_ = _student_progressions[0]) === null || _student_progressions_ === void 0 ? void 0 : _student_progressions_.grade_level) || 'Not set'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 550,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Status: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: \"outline\",\n                                                                                            className: \"text-xs ml-1\",\n                                                                                            children: student.status || 'Active'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 551,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        student.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                student.student_id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            children: \"View Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                href: \"/scholarship-application?student_id=\".concat(student.id),\n                                                                                children: \"Apply for Scholarship\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, student.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    myStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No students registered yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Add students to start managing their scholarship applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: \"Add Your First Student\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Collaboration Opportunities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        collaborationOpportunities.slice(0, 3).map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: opportunity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: opportunity.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 602,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.location\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 603,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.timeline\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 603,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: opportunity.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                                children: opportunity.budget_range\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, opportunity.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/partnerships/opportunities\",\n                                                                children: \"View All Opportunities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Available Programs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        programs.map((program)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: program.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: program.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: program.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: \"Learn More\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, program.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        programs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-center py-4\",\n                                                            children: \"No programs available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Partnership Applications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    partnershipApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: application.program\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"Submitted: \",\n                                                                                new Date(application.submitted_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 655,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getStatusColor(application.status),\n                                                                    children: application.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    partnershipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No partnership applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/partnerships/apply\",\n                                                                    children: \"Submit Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"opportunities\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Partnership Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Explore collaboration opportunities that align with your organization's mission\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: collaborationOpportunities.map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: opportunity.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Apply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: opportunity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Location: \",\n                                                                        opportunity.location\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Timeline: \",\n                                                                        opportunity.timeline\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Type: \",\n                                                                        opportunity.type\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Budget: \",\n                                                                        opportunity.budget_range\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, opportunity.id, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"proposals\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"resources\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"applications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Scholarship Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track scholarship applications submitted on behalf of your students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                scholarshipApplications.map((application)=>{\n                                                    var _application_scholarship, _application_student, _application_scholarship_amount, _application_scholarship1, _application_scholarship2;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.title) || 'Scholarship Application'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: getStatusColor(application.status),\n                                                                        children: application.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"Student: \",\n                                                                            ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"Amount: ₦\",\n                                                                            ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : (_application_scholarship_amount = _application_scholarship1.amount) === null || _application_scholarship_amount === void 0 ? void 0 : _application_scholarship_amount.toLocaleString()) || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"Category: \",\n                                                                            ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.category) || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"Submitted: \",\n                                                                            new Date(application.submitted_at || application.created_at).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 740,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"View Details\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    application.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"Edit Application\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, application.id, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No scholarship applications submitted yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/scholarships\",\n                                                                children: \"Browse Available Scholarships\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"progression\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Academic Progression Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                            href: \"/dashboard/partner/progression\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View All Progressions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track and manage student academic progression through grade levels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-8 w-8 text-yellow-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pending Approval\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"Recent Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/progression/create\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 805,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Create Progression\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_School_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-2\",\n                                                                children: \"No progressions found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Start tracking student academic progression\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 766,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Manage your organization's information and partnership preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_name || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_type || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Contact Person\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    user === null || user === void 0 ? void 0 : user.first_name,\n                                                                    \" \",\n                                                                    user === null || user === void 0 ? void 0 : user.last_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 845,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    partnerInfo.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Website\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.website\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.sector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Sector\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.sector\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.size\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 862,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.annual_budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Budget\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.annual_budget\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.partnership_interests && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Partnership Interests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: partnerInfo.partnership_interests\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: \"Edit Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 821,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(PartnerDashboard, \"THY8IzzxE8OuoqLuZSPOkjXQ/CM=\");\n_c = PartnerDashboard;\nvar _c;\n$RefreshReg$(_c, \"PartnerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/partner/page.tsx\n"));

/***/ })

});