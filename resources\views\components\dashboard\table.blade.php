{{-- Dashboard Table Component --}}
@props([
    'title' => '',
    'headers' => [],
    'rows' => [],
    'actions' => true,
    'searchable' => false,
    'exportable' => false
])

<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">{{ $title }}</h3>
            <div class="flex space-x-2">
                @if($searchable)
                    <div class="relative">
                        <input type="text" placeholder="Search..." 
                               class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                @endif
                @if($exportable)
                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                @endif
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    @foreach($headers as $header)
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ $header }}
                        </th>
                    @endforeach
                    @if($actions)
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    @endif
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($rows as $row)
                    <tr class="hover:bg-gray-50">
                        @foreach($row as $key => $cell)
                            @if($key !== 'actions')
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {!! $cell !!}
                                </td>
                            @endif
                        @endforeach
                        @if($actions && isset($row['actions']))
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                {!! $row['actions'] !!}
                            </td>
                        @endif
                    </tr>
                @empty
                    <tr>
                        <td colspan="{{ count($headers) + ($actions ? 1 : 0) }}" class="px-6 py-4 text-center text-gray-500">
                            No data available
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($slot->isNotEmpty())
        <div class="p-6 border-t border-gray-200">
            {{ $slot }}
        </div>
    @endif
</div>
