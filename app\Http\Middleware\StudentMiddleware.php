<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class StudentMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();

        // Check if user has user role (for individual students) or student role
        if (!in_array($user->role, ['user', 'student'])) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Student role required.'
            ], 403);
        }

        // Ensure user is not linked to a partner organization (individual student)
        if ($user->partner_organization_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Individual student account required.'
            ], 403);
        }

        return $next($request);
    }
}
