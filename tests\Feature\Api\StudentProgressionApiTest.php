<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\StudentProgression;
use Laravel\Sanctum\Sanctum;

class StudentProgressionApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $partnerUser;
    protected $partnerOrganization;
    protected $adminUser;
    protected $studentUser;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create partner organization
        $this->partnerOrganization = PartnerOrganization::factory()->create([
            'name' => 'Test School',
            'type' => 'school',
            'status' => 'active'
        ]);

        // Create partner user
        $this->partnerUser = User::factory()->create([
            'role' => 'partner_organization',
            'partner_organization_id' => $this->partnerOrganization->id
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create student user
        $this->studentUser = User::factory()->create([
            'role' => 'user'
        ]);

        // Create student
        $this->student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id,
            'user_id' => $this->studentUser->id
        ]);
    }

    /** @test */
    public function partner_can_access_student_progression_history()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create progression history
        StudentProgression::factory()->count(3)->create([
            'student_id' => $this->student->id,
            'status' => 'completed'
        ]);

        $response = $this->getJson("/api/v1/student-progression/student/{$this->student->id}/history");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'student',
                        'progressions',
                        'stats' => [
                            'total_progressions',
                            'years_in_system',
                            'current_grade',
                            'progression_rate'
                        ]
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals(3, $response->json('data.stats.total_progressions'));
    }

    /** @test */
    public function student_can_access_own_progression_history()
    {
        Sanctum::actingAs($this->studentUser);

        StudentProgression::factory()->count(2)->create([
            'student_id' => $this->student->id,
            'status' => 'completed'
        ]);

        $response = $this->getJson("/api/v1/student-progression/student/{$this->student->id}/history");

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function partner_cannot_access_other_school_student_progression()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create student from different school
        $otherStudent = Student::factory()->create([
            'school_id' => 999 // Different school
        ]);

        $response = $this->getJson("/api/v1/student-progression/student/{$otherStudent->id}/history");

        $response->assertStatus(403);
    }

    /** @test */
    public function student_cannot_access_other_student_progression()
    {
        Sanctum::actingAs($this->studentUser);

        // Create another student
        $otherStudent = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id,
            'user_id' => User::factory()->create(['role' => 'user'])->id
        ]);

        $response = $this->getJson("/api/v1/student-progression/student/{$otherStudent->id}/history");

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_access_progression_analytics()
    {
        Sanctum::actingAs($this->adminUser);

        // Create test data
        StudentProgression::factory()->count(5)->create([
            'status' => 'completed'
        ]);

        $response = $this->getJson('/api/v1/student-progression/analytics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_progressions',
                        'completion_rate',
                        'average_completion_time',
                        'grade_distribution',
                        'monthly_trends'
                    ]
                ]);
    }

    /** @test */
    public function partner_can_access_progression_analytics()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create progressions for partner's students
        $students = Student::factory()->count(3)->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        foreach ($students as $student) {
            StudentProgression::factory()->create([
                'student_id' => $student->id,
                'status' => 'completed'
            ]);
        }

        $response = $this->getJson('/api/v1/student-progression/analytics');

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function student_cannot_access_progression_analytics()
    {
        Sanctum::actingAs($this->studentUser);

        $response = $this->getJson('/api/v1/student-progression/analytics');

        $response->assertStatus(403);
    }

    /** @test */
    public function partner_can_create_progression()
    {
        Sanctum::actingAs($this->partnerUser);

        $progressionData = [
            'student_id' => $this->student->id,
            'from_grade' => 5,
            'to_grade' => 6,
            'academic_year' => now()->year,
            'progression_date' => now()->format('Y-m-d'),
            'notes' => 'Student successfully completed grade 5'
        ];

        $response = $this->postJson('/api/v1/student-progression/create', $progressionData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'progression'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertDatabaseHas('student_progressions', [
            'student_id' => $this->student->id,
            'from_grade' => 5,
            'to_grade' => 6
        ]);
    }

    /** @test */
    public function student_cannot_create_progression()
    {
        Sanctum::actingAs($this->studentUser);

        $progressionData = [
            'student_id' => $this->student->id,
            'from_grade' => 5,
            'to_grade' => 6,
            'academic_year' => now()->year,
            'progression_date' => now()->format('Y-m-d')
        ];

        $response = $this->postJson('/api/v1/student-progression/create', $progressionData);

        $response->assertStatus(403);
    }

    /** @test */
    public function progression_creation_validates_required_fields()
    {
        Sanctum::actingAs($this->partnerUser);

        $response = $this->postJson('/api/v1/student-progression/create', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'student_id',
                    'from_grade',
                    'to_grade',
                    'academic_year'
                ]);
    }

    /** @test */
    public function progression_creation_validates_grade_progression()
    {
        Sanctum::actingAs($this->partnerUser);

        $progressionData = [
            'student_id' => $this->student->id,
            'from_grade' => 6,
            'to_grade' => 5, // Invalid: going backwards
            'academic_year' => now()->year,
            'progression_date' => now()->format('Y-m-d')
        ];

        $response = $this->postJson('/api/v1/student-progression/create', $progressionData);

        $response->assertStatus(422);
    }

    /** @test */
    public function partner_can_get_eligible_students()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create students with different progression statuses
        $eligibleStudent = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id
        ]);

        // Create completed progression from last year
        StudentProgression::factory()->create([
            'student_id' => $eligibleStudent->id,
            'grade_level' => 5,
            'academic_year' => now()->year - 1,
            'status' => 'completed'
        ]);

        $response = $this->getJson('/api/v1/student-progression/eligible-students');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'eligible_students',
                        'total_eligible',
                        'criteria'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function unauthenticated_user_cannot_access_progression_endpoints()
    {
        $endpoints = [
            "/api/v1/student-progression/student/{$this->student->id}/history",
            '/api/v1/student-progression/analytics',
            '/api/v1/student-progression/eligible-students'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            $response->assertStatus(401);
        }

        $response = $this->postJson('/api/v1/student-progression/create', []);
        $response->assertStatus(401);
    }
}
