<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Admin Programs",
 *     description="Admin API Endpoints for Program Management"
 * )
 */
class AdminProgramController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/programs",
     *     summary="Get all programs for admin",
     *     tags={"Admin Programs"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Programs retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $status = $request->get('status');
            $dateRange = $request->get('date_range');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = Program::with(['creator']);

            // Apply search filter
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('target_beneficiaries', 'like', "%{$search}%");
                });
            }

            // Apply status filter
            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            // Apply date range filter
            if ($dateRange && $dateRange !== 'all') {
                $startDate = match($dateRange) {
                    'today' => Carbon::today(),
                    'week' => Carbon::now()->startOfWeek(),
                    'month' => Carbon::now()->startOfMonth(),
                    'year' => Carbon::now()->startOfYear(),
                    default => null
                };

                if ($startDate) {
                    $query->where('created_at', '>=', $startDate);
                }
            }

            // Apply sorting
            if (in_array($sortBy, ['created_at', 'title', 'status', 'start_date', 'end_date', 'budget'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $programs = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Programs retrieved successfully',
                'data' => [
                    'data' => $programs->items(),
                    'meta' => [
                        'total' => $programs->total(),
                        'per_page' => $programs->perPage(),
                        'current_page' => $programs->currentPage(),
                        'last_page' => $programs->lastPage(),
                        'from' => $programs->firstItem(),
                        'to' => $programs->lastItem(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve programs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/admin/programs",
     *     summary="Create a new program",
     *     tags={"Admin Programs"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "description", "category"},
     *             @OA\Property(property="title", type="string"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="category", type="string"),
     *             @OA\Property(property="target_audience", type="string"),
     *             @OA\Property(property="duration", type="string"),
     *             @OA\Property(property="location", type="string"),
     *             @OA\Property(property="requirements", type="string"),
     *             @OA\Property(property="benefits", type="string"),
     *             @OA\Property(property="is_featured", type="boolean"),
     *             @OA\Property(property="status", type="string", enum={"draft", "active", "completed", "cancelled"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Program created successfully"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'category' => 'required|string|max:100',
                'target_audience' => 'nullable|string',
                'duration' => 'nullable|string|max:100',
                'location' => 'nullable|string|max:255',
                'requirements' => 'nullable|string',
                'benefits' => 'nullable|string',
                'is_featured' => 'boolean',
                'status' => 'in:draft,active,completed,cancelled',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['slug'] = Str::slug($data['title']);

            // Ensure unique slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Program::where('slug', $data['slug'])->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }

            $program = Program::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Program created successfully',
                'data' => $program
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create program',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/programs/{id}",
     *     summary="Get specific program details",
     *     tags={"Admin Programs"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Program retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $program = Program::findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Program retrieved successfully',
                'data' => $program
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Program not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/programs/{id}",
     *     summary="Update a program",
     *     tags={"Admin Programs"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Program updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $program = Program::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'string|max:255',
                'description' => 'string',
                'category' => 'string|max:100',
                'target_audience' => 'nullable|string',
                'duration' => 'nullable|string|max:100',
                'location' => 'nullable|string|max:255',
                'requirements' => 'nullable|string',
                'benefits' => 'nullable|string',
                'is_featured' => 'boolean',
                'status' => 'in:draft,active,completed,cancelled',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Update slug if title changed
            if (isset($data['title']) && $data['title'] !== $program->title) {
                $data['slug'] = Str::slug($data['title']);
                
                // Ensure unique slug
                $originalSlug = $data['slug'];
                $counter = 1;
                while (Program::where('slug', $data['slug'])->where('id', '!=', $id)->exists()) {
                    $data['slug'] = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            $program->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Program updated successfully',
                'data' => $program
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update program',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/programs/{id}",
     *     summary="Delete a program",
     *     tags={"Admin Programs"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Program deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $program = Program::findOrFail($id);
            $program->delete();

            return response()->json([
                'success' => true,
                'message' => 'Program deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete program',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Activate a program
     */
    public function activate($id): JsonResponse
    {
        try {
            $program = Program::findOrFail($id);
            $program->update(['status' => 'active']);

            return response()->json([
                'success' => true,
                'message' => 'Program activated successfully',
                'data' => $program
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate program',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete a program
     */
    public function complete($id): JsonResponse
    {
        try {
            $program = Program::findOrFail($id);
            $program->update(['status' => 'completed']);

            return response()->json([
                'success' => true,
                'message' => 'Program marked as completed',
                'data' => $program
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete program',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
