<?php

// Test individual scholarship endpoint
$url = 'http://127.0.0.1:8000/api/v1/public-scholarships/2';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Individual Scholarship API Test:\n";
echo "===============================\n";
echo "HTTP Code: $httpCode\n";

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "Response received successfully!\n\n";
    
    $data = json_decode($response, true);
    
    if ($data && isset($data['data'])) {
        $scholarship = $data['data'];
        
        echo "Scholarship Details:\n";
        echo "- ID: " . $scholarship['id'] . "\n";
        echo "- Title: " . $scholarship['title'] . "\n";
        echo "- Category: " . $scholarship['category'] . "\n";
        echo "- Status: " . $scholarship['status'] . "\n";
        echo "- Amount: $" . number_format($scholarship['amount']) . "\n";
        echo "- Deadline: " . $scholarship['application_deadline'] . "\n";
        
        echo "\nCategory Instructions:\n";
        if (isset($scholarship['category_instructions'])) {
            $instructions = $scholarship['category_instructions'];
            echo "- Title: " . $instructions['title'] . "\n";
            echo "- Age Range: " . $instructions['age_range'] . "\n";
            echo "- Filled By: " . $instructions['filled_by'] . "\n";
            echo "- Instruction: " . $instructions['instruction'] . "\n";
            echo "- Required Info Count: " . count($instructions['required_info']) . "\n";
        }
        
        echo "\nCustom Fields:\n";
        if (isset($scholarship['custom_fields']) && is_array($scholarship['custom_fields'])) {
            echo "- Custom Fields Count: " . count($scholarship['custom_fields']) . "\n";
            foreach ($scholarship['custom_fields'] as $field) {
                echo "  - " . $field['field_name'] . " (" . $field['field_type'] . ")" . 
                     ($field['is_required'] ? " *required*" : "") . "\n";
            }
        }
        
        echo "\nForm Metadata:\n";
        if (isset($scholarship['form_metadata'])) {
            $metadata = $scholarship['form_metadata'];
            echo "- Validation Rules Count: " . count($metadata['validation_rules']) . "\n";
            echo "- Field Constraints Count: " . count($metadata['field_constraints']) . "\n";
            echo "- Has File Upload Fields: " . (isset($metadata['has_file_uploads']) && $metadata['has_file_uploads'] ? 'Yes' : 'No') . "\n";
        }
        
        echo "\nDetailed Fields (for form generation):\n";
        if (isset($scholarship['application_instructions'])) {
            echo "- Has Application Instructions: Yes\n";
        }
        if (isset($scholarship['terms_conditions'])) {
            echo "- Has Terms & Conditions: Yes\n";
        }
        if (isset($scholarship['requirements'])) {
            echo "- Has Requirements: Yes\n";
        }
        
    } else {
        echo "Failed to parse response or no data found\n";
        echo "Raw response (first 500 chars):\n";
        echo substr($response, 0, 500) . "\n";
    }
}

echo "\n===============================\n";
echo "Detail test completed!\n";
