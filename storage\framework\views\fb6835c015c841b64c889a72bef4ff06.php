

<?php $__env->startSection('title', $post->title . ' | HLTKKQ Foundation Blog'); ?>

<?php $__env->startSection('content'); ?>
<div class="container my-5">
    <!-- Article Header -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold mb-3"><?php echo e($post->title); ?></h1>
                <div class="text-muted mb-4">
                    <span><i class="fas fa-calendar-alt me-2"></i><?php echo e($post->published_at ? $post->published_at->format('F j, Y') : $post->created_at->format('F j, Y')); ?></span>
                    <span class="mx-3">|</span>
                    <span><i class="fas fa-user me-2"></i><?php echo e($post->author->first_name ?? 'HLTKKQ Foundation'); ?> <?php echo e($post->author->last_name ?? ''); ?></span>
                    <?php if($post->category): ?>
                        <span class="mx-3">|</span>
                        <span><i class="fas fa-tag me-2"></i><?php echo e($post->category->name ?? 'General'); ?></span>
                    <?php endif; ?>
                </div>
                <?php if($post->excerpt): ?>
                    <p class="lead text-muted"><?php echo e($post->excerpt); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Featured Image -->
    <?php if($post->featured_image): ?>
        <div class="row justify-content-center mb-5">
            <div class="col-lg-10">
                <div class="card border-0 shadow-sm">
                    <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                         class="card-img-top rounded" 
                         alt="<?php echo e($post->title); ?>" 
                         style="height: 400px; object-fit: cover;">
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Article Content -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="content-wrapper">
                <div class="article-content" style="line-height: 1.8; font-size: 1.1rem;">
                    <?php echo nl2br(e($post->content)); ?>

                </div>
                
                <!-- Article Tags -->
                <?php if($post->seo_keywords): ?>
                    <div class="tags mt-5 pt-4 border-top">
                        <h5 class="mb-3">Tags:</h5>
                        <?php $__currentLoopData = json_decode($post->seo_keywords, true) ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="badge bg-primary me-2 mb-2 px-3 py-2" style="font-size: 0.9rem;"><?php echo e(trim($tag)); ?></span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Share Section -->
                <div class="share-section mt-5 pt-4 border-top">
                    <h5 class="mb-3">Share this article:</h5>
                    <div class="d-flex gap-3">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->url())); ?>" 
                           target="_blank" 
                           class="btn btn-outline-primary">
                            <i class="fab fa-facebook-f me-2"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->url())); ?>&text=<?php echo e(urlencode($post->title)); ?>" 
                           target="_blank" 
                           class="btn btn-outline-info">
                            <i class="fab fa-twitter me-2"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(request()->url())); ?>" 
                           target="_blank" 
                           class="btn btn-outline-primary">
                            <i class="fab fa-linkedin-in me-2"></i>LinkedIn
                        </a>
                        <a href="https://wa.me/?text=<?php echo e(urlencode($post->title . ' - ' . request()->url())); ?>" 
                           target="_blank" 
                           class="btn btn-outline-success">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Posts -->
    <?php if($relatedPosts && $relatedPosts->count() > 0): ?>
        <div class="row justify-content-center mt-5 pt-5">
            <div class="col-lg-10">
                <h3 class="text-center mb-5">Related Articles</h3>
                <div class="row">
                    <?php $__currentLoopData = $relatedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <?php if($related->featured_image): ?>
                                    <img src="<?php echo e(asset('storage/' . $related->featured_image)); ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo e($related->title); ?>"
                                         style="height: 200px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-newspaper fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo e(Str::limit($related->title, 60)); ?></h5>
                                    <?php if($related->excerpt): ?>
                                        <p class="card-text text-muted flex-grow-1"><?php echo e(Str::limit($related->excerpt, 100)); ?></p>
                                    <?php endif; ?>
                                    <div class="mt-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            <?php echo e($related->published_at ? $related->published_at->format('M j, Y') : $related->created_at->format('M j, Y')); ?>

                                        </small>
                                        <div class="mt-2">
                                            <a href="<?php echo e(route('blog.show', $related->slug)); ?>" class="btn btn-outline-primary btn-sm">
                                                Read More <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Back to Blog -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-8 text-center">
            <a href="<?php echo e(route('blog.index')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Blog
            </a>
        </div>
    </div>
</div>

<style>
.article-content h1, .article-content h2, .article-content h3, 
.article-content h4, .article-content h5, .article-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content ul, .article-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content li {
    margin-bottom: 0.5rem;
}

.article-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #555;
}

.article-content img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/blog-post.blade.php ENDPATH**/ ?>