<?php

namespace App\Policies;

use App\Models\User;
use App\Models\StudentProgression;
use App\Models\Student;
use Illuminate\Auth\Access\HandlesAuthorization;

class StudentProgressionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any student progressions.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization', 'user']);
    }

    /**
     * Determine whether the user can view the student progression.
     */
    public function view(User $user, StudentProgression $progression): bool
    {
        // Admins can view all progressions
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view progressions of their students
        if ($user->role === 'partner_organization') {
            return $progression->student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own progressions
        if ($user->role === 'user') {
            return $progression->student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create student progressions.
     */
    public function create(User $user, ?Student $student = null): bool
    {
        // Ad<PERSON> can create progressions for any student
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create progressions for their students
        if ($user->role === 'partner_organization' && $student) {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users cannot create their own progressions
        return false;
    }

    /**
     * Determine whether the user can update the student progression.
     */
    public function update(User $user, StudentProgression $progression): bool
    {
        // Admins can update all progressions
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can update progressions of their students
        if ($user->role === 'partner_organization') {
            return $progression->student->school_id === $user->partner_organization_id;
        }

        // Individual users cannot update progressions
        return false;
    }

    /**
     * Determine whether the user can delete the student progression.
     */
    public function delete(User $user, StudentProgression $progression): bool
    {
        // Only admins can delete progressions
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can approve/reject progressions.
     */
    public function review(User $user, StudentProgression $progression): bool
    {
        // Admins can review all progressions
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can review progressions of their students
        if ($user->role === 'partner_organization') {
            return $progression->student->school_id === $user->partner_organization_id;
        }

        return false;
    }

    /**
     * Determine whether the user can view progression statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can perform bulk operations on progressions.
     */
    public function bulkOperations(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can view progression analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can export progression data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can access student progression history.
     */
    public function viewStudentHistory(User $user, Student $student): bool
    {
        // Admins can view any student's history
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view history of their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own history
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }
}
