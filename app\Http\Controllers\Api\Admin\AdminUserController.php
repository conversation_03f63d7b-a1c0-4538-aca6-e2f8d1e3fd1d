<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin - Users",
 *     description="Admin endpoints for user management"
 * )
 */
class AdminUserController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/users",
     *     summary="Get all users (Admin only)",
     *     tags={"Admin - Users"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search by name or email",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="role",
     *         in="query",
     *         description="Filter by role",
     *         required=false,
     *         @OA\Schema(type="string", enum={"admin","volunteer","donor","user"})
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"active","inactive","suspended"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Users retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $role = $request->get('role');
        $status = $request->get('status');
        $dateRange = $request->get('date_range');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = User::with(['volunteer', 'scholarshipApplications', 'donations']);

        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        // Apply role filter (support both role_id and legacy role string)
        if ($role && $role !== 'all') {
            if (is_numeric($role)) {
                $query->where('role_id', $role);
            } else {
                // Legacy role string support
                $roleId = match($role) {
                    'user' => [1, 2], // Normal users and university students
                    'partner_organization' => 3,
                    'admin' => 4,
                    default => null
                };

                if ($roleId) {
                    if (is_array($roleId)) {
                        $query->whereIn('role_id', $roleId);
                    } else {
                        $query->where('role_id', $roleId);
                    }
                }
            }
        }

        // Apply status filter
        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }

        // Apply date range filter
        if ($dateRange && $dateRange !== 'all') {
            $startDate = match($dateRange) {
                'today' => Carbon::today(),
                'week' => Carbon::now()->startOfWeek(),
                'month' => Carbon::now()->startOfMonth(),
                'year' => Carbon::now()->startOfYear(),
                default => null
            };

            if ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }
        }

        // Apply sorting
        if (in_array($sortBy, ['created_at', 'last_login_at', 'first_name', 'last_name', 'email'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $users = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => $users->items(),
                'meta' => [
                    'total' => $users->total(),
                    'per_page' => $users->perPage(),
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'from' => $users->firstItem(),
                    'to' => $users->lastItem(),
                ]
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/users/{id}",
     *     summary="Get user details (Admin only)",
     *     tags={"Admin - Users"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User details retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $user = User::with([
            'volunteer',
            'donations' => function($query) {
                $query->latest()->take(5);
            },
            'eventRegistrations' => function($query) {
                $query->with('event:id,title,start_datetime')->latest()->take(5);
            },
            'scholarshipApplications' => function($query) {
                $query->with('scholarship:id,title')->latest()->take(5);
            }
        ])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/users/{id}",
     *     summary="Update user (Admin only)",
     *     tags={"Admin - Users"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="phone_number", type="string", example="+234800000000"),
     *             @OA\Property(property="role", type="string", enum={"admin","volunteer","donor","user"}, example="user"),
     *             @OA\Property(property="status", type="string", enum={"active","inactive","suspended"}, example="active")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|required|string|max:50',
            'last_name' => 'sometimes|required|string|max:50',
            'email' => 'sometimes|required|email|max:100|unique:users,email,' . $user->id,
            'phone_number' => 'nullable|string|max:20',
            'role' => 'sometimes|required|in:admin,volunteer,donor,user',
            'status' => 'sometimes|required|in:active,inactive,suspended',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'city' => 'nullable|string|max:50',
            'state' => 'nullable|string|max:50',
            'country' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only([
            'first_name', 'last_name', 'email', 'phone_number', 'role', 'status',
            'date_of_birth', 'gender', 'city', 'state', 'country'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully',
            'data' => $user->fresh()
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/users/{id}",
     *     summary="Delete user (Admin only)",
     *     tags={"Admin - Users"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        $user = User::findOrFail($id);
        
        // Prevent deletion of current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot delete your own account'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Assign role to user
     */
    public function assignRole(Request $request, $id): JsonResponse
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'role' => 'required|string|exists:roles,name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->assignRole($request->role);

        return response()->json([
            'success' => true,
            'message' => 'Role assigned successfully',
            'data' => $user->fresh()
        ]);
    }

    /**
     * Remove role from user
     */
    public function removeRole(Request $request, $id): JsonResponse
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'role' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->removeRole($request->role);

        return response()->json([
            'success' => true,
            'message' => 'Role removed successfully',
            'data' => $user->fresh()
        ]);
    }

    /**
     * Toggle user status
     */
    public function toggleStatus($id): JsonResponse
    {
        $user = User::findOrFail($id);

        // Prevent deactivating current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot change your own status'
            ], 400);
        }

        $newStatus = $user->status === 'active' ? 'inactive' : 'active';
        $user->update(['status' => $newStatus]);

        return response()->json([
            'success' => true,
            'message' => "User status changed to {$newStatus}",
            'data' => $user->fresh()
        ]);
    }

    /**
     * Create a new user
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Base validation rules
            $rules = [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone_number' => 'nullable|string|max:20',
                'role_id' => 'required|integer|in:1,2,3,4',
                'status' => 'required|in:active,inactive,suspended',
                'password' => 'required|string|min:8|confirmed'
            ];

            // Add role-specific validation rules
            if ($request->role_id == 2) { // University Student
                $rules = array_merge($rules, [
                    'university_name' => 'nullable|string|max:100',
                    'course_of_study' => 'nullable|string|max:100',
                    'year_of_study' => 'nullable|integer|min:1|max:7',
                    'matriculation_number' => 'nullable|string|max:50',
                    'cgpa' => 'nullable|numeric|min:0|max:5'
                ]);
            } elseif ($request->role_id == 3) { // Partner Organization
                $rules = array_merge($rules, [
                    'organization_name' => 'nullable|string|max:100',
                    'organization_type' => 'nullable|in:primary_school,secondary_school,university,ngo,government',
                    'organization_address' => 'nullable|string|max:255',
                    'organization_phone' => 'nullable|string|max:20',
                    'principal_name' => 'nullable|string|max:100',
                    'principal_email' => 'nullable|email|max:100',
                    'principal_phone' => 'nullable|string|max:20'
                ]);
            }

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['password'] = Hash::make($data['password']);
            $data['email_verified_at'] = now(); // Auto-verify admin created users

            // Add role string for backward compatibility
            $data['role'] = match($data['role_id']) {
                1 => 'user',
                2 => 'user', // University students are still 'user' type for legacy compatibility
                3 => 'partner_organization',
                4 => 'admin',
                default => 'user'
            };

            $user = User::create($data);

            return response()->json([
                'success' => true,
                'message' => 'User created successfully',
                'data' => $user->load(['volunteer', 'scholarshipApplications', 'donations'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on users
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:activate,suspend,delete,export',
                'user_ids' => 'required|array|min:1',
                'user_ids.*' => 'integer|exists:users,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $action = $request->action;
            $userIds = $request->user_ids;

            $users = User::whereIn('id', $userIds)->get();

            if ($users->count() !== count($userIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some users not found'
                ], 404);
            }

            $successCount = 0;
            $errors = [];

            foreach ($users as $user) {
                try {
                    // Prevent actions on admin users and current user
                    if ($user->role === 'admin' && in_array($action, ['suspend', 'delete'])) {
                        $errors[] = "Cannot {$action} admin user: {$user->email}";
                        continue;
                    }

                    if ($user->id === auth()->id() && in_array($action, ['suspend', 'delete'])) {
                        $errors[] = "Cannot {$action} your own account";
                        continue;
                    }

                    switch ($action) {
                        case 'activate':
                            $user->update(['status' => 'active']);
                            break;
                        case 'suspend':
                            $user->update(['status' => 'suspended']);
                            break;
                        case 'delete':
                            // Check for related data before deletion
                            $hasRelatedData = $user->scholarshipApplications()->count() > 0 ||
                                             $user->donations()->count() > 0 ||
                                             $user->volunteer()->exists();

                            if ($hasRelatedData) {
                                $user->update(['status' => 'inactive']);
                            } else {
                                $user->delete();
                            }
                            break;
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to process user {$user->email}: " . $e->getMessage();
                }
            }

            $message = "Successfully processed {$successCount} out of " . count($userIds) . " users";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $successCount,
                    'total_count' => count($userIds),
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export users data
     */
    public function exportUsers(Request $request): JsonResponse
    {
        try {
            $format = $request->get('format', 'csv');
            $role = $request->get('role');
            $status = $request->get('status');

            $query = User::with(['volunteer', 'scholarshipApplications', 'donations']);

            if ($role && $role !== 'all') {
                $query->where('role', $role);
            }

            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            $users = $query->get();

            // Prepare data for export
            $exportData = $users->map(function ($user) {
                return [
                    'ID' => $user->id,
                    'First Name' => $user->first_name,
                    'Last Name' => $user->last_name,
                    'Email' => $user->email,
                    'Phone' => $user->phone_number,
                    'Role' => $user->role,
                    'Status' => $user->status,
                    'City' => $user->city,
                    'State' => $user->state,
                    'Country' => $user->country,
                    'Registered At' => $user->created_at->format('Y-m-d H:i:s'),
                    'Last Login' => $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                    'Total Donations' => $user->donations->sum('amount'),
                    'Scholarship Applications' => $user->scholarshipApplications->count(),
                    'Volunteer Status' => $user->volunteer ? $user->volunteer->application_status : 'Not Applied'
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Export data prepared successfully',
                'data' => [
                    'format' => $format,
                    'count' => $exportData->count(),
                    'data' => $exportData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export users',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}