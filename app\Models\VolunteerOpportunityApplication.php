<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteerOpportunityApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'volunteer_id',
        'opportunity_id',
        'motivation',
        'relevant_experience',
        'availability_notes',
        'status',
        'applied_at',
        'reviewed_at',
        'reviewed_by'
    ];

    protected $casts = [
        'applied_at' => 'datetime',
        'reviewed_at' => 'datetime'
    ];

    /**
     * Get the volunteer that owns this application
     */
    public function volunteer()
    {
        return $this->belongsTo(Volunteer::class);
    }

    /**
     * Get the opportunity for this application
     */
    public function opportunity()
    {
        return $this->belongsTo(VolunteerOpportunity::class, 'opportunity_id');
    }

    /**
     * Get the user who reviewed this application
     */
    public function reviewedBy()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope for pending applications
     */
    public function scopePending($query)
    {
        return $query->where('status', 'applied');
    }

    /**
     * Scope for accepted applications
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope for rejected applications
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Check if application is pending
     */
    public function isPending()
    {
        return $this->status === 'applied';
    }

    /**
     * Check if application is accepted
     */
    public function isAccepted()
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if application is rejected
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Accept application
     */
    public function accept($reviewedBy = null)
    {
        $this->update([
            'status' => 'accepted',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy
        ]);
    }

    /**
     * Reject application
     */
    public function reject($reviewedBy = null)
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy
        ]);
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'applied':
                return 'yellow';
            case 'accepted':
                return 'green';
            case 'rejected':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Get days since application
     */
    public function getDaysSinceApplicationAttribute()
    {
        return $this->applied_at ? $this->applied_at->diffInDays(now()) : 0;
    }
}
