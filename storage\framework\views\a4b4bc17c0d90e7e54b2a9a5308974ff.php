

<?php $__env->startSection('title', 'Edit Team Member'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Team Member</h1>
        <div>
            <a href="<?php echo e(route('admin.team.show', $id)); ?>" class="btn btn-info me-2">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="<?php echo e(route('admin.team.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Team
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Team Member Information</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.team.update', $id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="Sample Team Member" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="position" class="form-label">Position <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="position" name="position" value="Program Manager" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <select class="form-select" id="department" name="department">
                                        <option value="">Select Department</option>
                                        <option value="Operations" selected>Operations</option>
                                        <option value="Programs">Programs</option>
                                        <option value="Finance">Finance</option>
                                        <option value="Communications">Communications</option>
                                        <option value="Human Resources">Human Resources</option>
                                        <option value="Administration">Administration</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?php echo e(old('status', $teamMember->is_active ? 'active' : 'inactive') == 'active' ? 'selected' : ''); ?>>Active</option>
                                        <option value="inactive" <?php echo e(old('status', $teamMember->is_active ? 'active' : 'inactive') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="+234800000000">
                                </div>
                            </div>
                        </div>

                        <!-- Biography -->
                        <div class="mb-3">
                            <label for="bio" class="form-label">Biography</label>
                            <textarea class="form-control" id="bio" name="bio" rows="4">Sample bio text here...</textarea>
                        </div>

                        <!-- Social Media -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                                    <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" value="https://linkedin.com/in/sample">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="twitter_url" class="form-label">Twitter URL</label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url" value="https://twitter.com/sample">
                                </div>
                            </div>
                        </div>

                        <!-- Join Date -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="join_date" class="form-label">Join Date</label>
                                    <input type="date" class="form-control" id="join_date" name="join_date" value="2023-01-15">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Display Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-2"></i>Delete Member
                            </button>
                            <div>
                                <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Team Member
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Profile Photo -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Photo</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="photoPreview" src="<?php echo e(asset('img/default-avatar.png')); ?>" alt="Profile Preview" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="photo" name="photo" accept="image/*" onchange="previewPhoto(this)">
                    </div>
                    <small class="text-muted">Leave empty to keep current photo</small>
                </div>
            </div>

            <!-- Member Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Member Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-primary"><?php echo e(rand(2, 8)); ?></h4>
                                <p class="text-muted mb-0">Years</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success"><?php echo e(rand(15, 50)); ?></h4>
                                <p class="text-muted mb-0">Projects</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="sendEmail('<EMAIL>')">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </button>
                        <button class="btn btn-outline-info" onclick="viewActivity(<?php echo e($id); ?>)">
                            <i class="fas fa-history me-2"></i>View Activity
                        </button>
                        <button class="btn btn-outline-warning" onclick="changeStatus(<?php echo e($id); ?>)">
                            <i class="fas fa-toggle-on me-2"></i>Toggle Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('photoPreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function confirmDelete() {
    if (confirm('Are you sure you want to delete this team member? This action cannot be undone.')) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("admin.team.destroy", $id)); ?>';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function sendEmail(email) {
    window.location.href = `mailto:${email}`;
}

function viewActivity(memberId) {
    alert('Activity viewer not implemented yet');
}

function changeStatus(memberId) {
    if (confirm('Are you sure you want to change this member\'s status?')) {
        alert('Status change not implemented yet');
    }
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/team/edit.blade.php ENDPATH**/ ?>