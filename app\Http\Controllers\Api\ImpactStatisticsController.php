<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ImpactStatisticsController extends Controller
{
    /**
     * Get comprehensive impact statistics for the impact page
     */
    public function getImpactStatistics(): JsonResponse
    {
        try {
            // Students Supported - Number of students who got scholarships (approved applications)
            $studentsSupported = ScholarshipApplication::where('status', 'approved')->count();

            // Schools Partnered - Total number of partner organizations
            $schoolsPartnered = User::where('role', 'partner')->count();

            // Scholarships Awarded - Total amount of scholarships awarded
            $scholarshipsAwarded = ScholarshipApplication::where('status', 'approved')
                ->sum('award_amount');

            // If award_amount is null, calculate from scholarship amounts
            if ($scholarshipsAwarded == 0) {
                $scholarshipsAwarded = ScholarshipApplication::where('scholarship_applications.status', 'approved')
                    ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->sum('scholarships.amount');
            }

            // Regional Impact - By state
            $regionalImpact = $this->getRegionalImpact();

            // Additional statistics
            $totalApplications = ScholarshipApplication::count();
            $activeScholarships = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->where('application_deadline', '>', now())
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'students_supported' => $studentsSupported,
                    'schools_partnered' => $schoolsPartnered,
                    'scholarships_awarded' => $scholarshipsAwarded,
                    'total_applications' => $totalApplications,
                    'active_scholarships' => $activeScholarships,
                    'regional_impact' => $regionalImpact,
                ],
                'message' => 'Impact statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve impact statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get regional impact statistics by state
     */
    private function getRegionalImpact(): array
    {
        try {
            // Get states with student applications and school registrations
            $stateStats = DB::table('users')
                ->select('state')
                ->selectRaw('
                    COUNT(CASE WHEN role = "partner" THEN 1 END) as schools_registered,
                    COUNT(CASE WHEN id IN (
                        SELECT DISTINCT user_id FROM scholarship_applications
                    ) THEN 1 END) as students_applied
                ')
                ->whereNotNull('state')
                ->where('state', '!=', '')
                ->groupBy('state')
                ->orderBy('students_applied', 'desc')
                ->limit(10) // Top 10 states
                ->get();

            return $stateStats->map(function ($stat) {
                return [
                    'state' => $stat->state,
                    'schools_registered' => (int) $stat->schools_registered,
                    'students_applied' => (int) $stat->students_applied,
                ];
            })->toArray();

        } catch (\Exception $e) {
            // Return empty array if there's an error
            return [];
        }
    }

    /**
     * Get detailed scholarship statistics by category
     */
    public function getScholarshipStatistics(): JsonResponse
    {
        try {
            $stats = [
                'by_category' => ScholarshipApplication::select('scholarships.category')
                    ->selectRaw('COUNT(*) as total_applications')
                    ->selectRaw('COUNT(CASE WHEN scholarship_applications.status = "approved" THEN 1 END) as approved')
                    ->selectRaw('SUM(CASE WHEN scholarship_applications.status = "approved" THEN COALESCE(scholarship_applications.award_amount, scholarships.amount) ELSE 0 END) as total_awarded')
                    ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->groupBy('scholarships.category')
                    ->get(),
                
                'by_status' => ScholarshipApplication::select('scholarship_applications.status')
                    ->selectRaw('COUNT(*) as count')
                    ->groupBy('scholarship_applications.status')
                    ->get(),
                
                'monthly_trends' => ScholarshipApplication::select(
                        DB::raw('YEAR(created_at) as year'),
                        DB::raw('MONTH(created_at) as month'),
                        DB::raw('COUNT(*) as applications'),
                        DB::raw('COUNT(CASE WHEN scholarship_applications.status = "approved" THEN 1 END) as approved')
                    )
                    ->where('created_at', '>=', now()->subMonths(12))
                    ->groupBy('year', 'month')
                    ->orderBy('year', 'desc')
                    ->orderBy('month', 'desc')
                    ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Scholarship statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarship statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
