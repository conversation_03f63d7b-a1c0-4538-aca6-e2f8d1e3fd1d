"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[482],{704:(e,t,n)=>{n.d(t,{B8:()=>I,UC:()=>E,bL:()=>M,l9:()=>N});var a=n(2115),r=n(5185),o=n(6081),l=n(9196),i=n(8905),s=n(3655),d=n(4315),c=n(5845),u=n(1285),p=n(5155),f="Tabs",[v,y]=(0,o.A)(f,[l.RG]),h=(0,l.RG)(),[g,m]=v(f),b=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,onValueChange:r,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:f="automatic",...v}=e,y=(0,d.jH)(i),[h,m]=(0,c.i)({prop:a,onChange:r,defaultProp:o});return(0,p.jsx)(g,{scope:n,baseId:(0,u.B)(),value:h,onValueChange:m,orientation:l,dir:y,activationMode:f,children:(0,p.jsx)(s.sG.div,{dir:y,"data-orientation":l,...v,ref:t})})});b.displayName=f;var x="TabsList",D=a.forwardRef((e,t)=>{let{__scopeTabs:n,loop:a=!0,...r}=e,o=m(x,n),i=h(n);return(0,p.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:a,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...r,ref:t})})});D.displayName=x;var k="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,disabled:o=!1,...i}=e,d=m(k,n),c=h(n),u=A(d.baseId,a),f=R(d.baseId,a),v=a===d.value;return(0,p.jsx)(l.q7,{asChild:!0,...c,focusable:!o,active:v,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;v||o||!e||d.onValueChange(a)})})})});w.displayName=k;var C="TabsContent",j=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:o,children:l,...d}=e,c=m(C,n),u=A(c.baseId,r),f=R(c.baseId,r),v=r===c.value,y=a.useRef(v);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||v,children:n=>{let{present:a}=n;return(0,p.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:a&&l})}})});function A(e,t){return"".concat(e,"-trigger-").concat(t)}function R(e,t){return"".concat(e,"-content-").concat(t)}j.displayName=C;var M=b,I=D,N=w,E=j},968:(e,t,n)=>{n.d(t,{b:()=>i});var a=n(2115),r=n(3655),o=n(5155),l=a.forwardRef((e,t)=>(0,o.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},2657:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>et,VY:()=>ea,ZL:()=>$,bL:()=>Y,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>Q});var a=n(2115),r=n(5185),o=n(6101),l=n(6081),i=n(1285),s=n(5845),d=n(9178),c=n(7900),u=n(4378),p=n(8905),f=n(3655),v=n(2293),y=n(3795),h=n(8168),g=n(9708),m=n(5155),b="Dialog",[x,D]=(0,l.A)(b),[k,w]=x(b),C=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[p=!1,f]=(0,s.i)({prop:r,defaultProp:o,onChange:l});return(0,m.jsx)(k,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};C.displayName=b;var j="DialogTrigger",A=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,l=w(j,n),i=(0,o.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...a,ref:i,onClick:(0,r.m)(e.onClick,l.onOpenToggle)})});A.displayName=j;var R="DialogPortal",[M,I]=x(R,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,l=w(R,t);return(0,m.jsx)(M,{scope:t,forceMount:n,children:a.Children.map(r,e=>(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};N.displayName=R;var E="DialogOverlay",F=a.forwardRef((e,t)=>{let n=I(E,e.__scopeDialog),{forceMount:a=n.forceMount,...r}=e,o=w(E,e.__scopeDialog);return o.modal?(0,m.jsx)(p.C,{present:a||o.open,children:(0,m.jsx)(O,{...r,ref:t})}):null});F.displayName=E;var O=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(E,n);return(0,m.jsx)(y.A,{as:g.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":W(r.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),_="DialogContent",P=a.forwardRef((e,t)=>{let n=I(_,e.__scopeDialog),{forceMount:a=n.forceMount,...r}=e,o=w(_,e.__scopeDialog);return(0,m.jsx)(p.C,{present:a||o.open,children:o.modal?(0,m.jsx)(G,{...r,ref:t}):(0,m.jsx)(T,{...r,ref:t})})});P.displayName=_;var G=a.forwardRef((e,t)=>{let n=w(_,e.__scopeDialog),l=a.useRef(null),i=(0,o.s)(t,n.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,h.Eq)(e)},[]),(0,m.jsx)(q,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=a.forwardRef((e,t)=>{let n=w(_,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return(0,m.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,l;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let i=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=a.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=w(_,n),p=a.useRef(null),f=(0,o.s)(t,p);return(0,v.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(X,{titleId:u.titleId}),(0,m.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",L=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(B,n);return(0,m.jsx)(f.sG.h2,{id:r.titleId,...a,ref:t})});L.displayName=B;var V="DialogDescription",H=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(V,n);return(0,m.jsx)(f.sG.p,{id:r.descriptionId,...a,ref:t})});H.displayName=V;var K="DialogClose",S=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,o=w(K,n);return(0,m.jsx)(f.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function W(e){return e?"open":"closed"}S.displayName=K;var z="DialogTitleWarning",[U,Z]=(0,l.q)(z,{contentName:_,titleName:B,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=Z(z),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return a.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,r=Z("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return a.useEffect(()=>{var e;let a=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&a&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},Y=C,Q=A,$=N,ee=F,et=P,en=L,ea=H,er=S},5868:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6785:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7265:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},9074:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9629:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(9946).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}}]);