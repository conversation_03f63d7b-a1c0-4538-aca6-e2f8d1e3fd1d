(()=>{var e={};e.id=974,e.ids=[974],e.modules={1322:(e,r)=>{"use strict";function t(e){let{widthInt:r,heightInt:t,blurWidth:a,blurHeight:s,blurDataURL:o,objectFit:n}=e,l=a?40*a:r,i=s?40*s:t,d=l&&i?"viewBox='0 0 "+l+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImageBlurSvg",{enumerable:!0,get:function(){return t}})},2649:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eR});var a=t(37413),s=t(4536),o=t.n(s),n=t(70099),l=t.n(n),i=t(61120);function d(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var c=i.forwardRef((e,r)=>{let{children:t,...s}=e,o=i.Children.toArray(t),n=o.find(g);if(n){let e=n.props.children,t=o.map(r=>r!==n?r:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,t):null})}return(0,a.jsx)(u,{...s,ref:r,children:t})});c.displayName="Slot";var u=i.forwardRef((e,r)=>{let{children:t,...a}=e;if(i.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t);return i.cloneElement(t,{...function(e,r){let t={...r};for(let a in r){let s=e[a],o=r[a];/^on[A-Z]/.test(a)?s&&o?t[a]=(...e)=>{o(...e),s(...e)}:s&&(t[a]=s):"style"===a?t[a]={...s,...o}:"className"===a&&(t[a]=[s,o].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props),ref:r?function(...e){return r=>{let t=!1,a=e.map(e=>{let a=d(e,r);return t||"function"!=typeof a||(t=!0),a});if(t)return()=>{for(let r=0;r<a.length;r++){let t=a[r];"function"==typeof t?t():d(e[r],null)}}}}(r,e):e})}return i.Children.count(t)>1?i.Children.only(null):null});u.displayName="SlotClone";var m=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function g(e){return i.isValidElement(e)&&e.type===m}function p(){for(var e,r,t=0,a="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,a,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r){if(Array.isArray(r)){var o=r.length;for(t=0;t<o;t++)r[t]&&(a=e(r[t]))&&(s&&(s+=" "),s+=a)}else for(a in r)r[a]&&(s&&(s+=" "),s+=a)}return s}(e))&&(a&&(a+=" "),a+=r);return a}let h=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,x=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return p(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:o}=r,n=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let n=h(r)||h(a);return s[e][n]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return p(e,n,null==r?void 0:null===(a=r.compoundVariants)||void 0===a?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...l}[r]):({...o,...l})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},f=e=>{let r=w(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),b(t,r)||y(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&a[e]?[...s,...a[e]]:s}}},b=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],a=r.nextPart.get(t),s=a?b(e.slice(1),a):void 0;if(s)return s;if(0===r.validators.length)return;let o=e.join("-");return r.validators.find(({validator:e})=>e(o))?.classGroupId},v=/^\[(.+)\]$/,y=e=>{if(v.test(e)){let r=v.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},w=e=>{let{theme:r,prefix:t}=e,a={nextPart:new Map,validators:[]};return z(Object.entries(e.classGroups),t).forEach(([e,t])=>{j(t,a,e,r)}),a},j=(e,r,t,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:N(r,e)).classGroupId=t;return}if("function"==typeof e){if(k(e)){j(e(a),r,t,a);return}r.validators.push({validator:e,classGroupId:t});return}Object.entries(e).forEach(([e,s])=>{j(s,N(r,e),t,a)})})},N=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},k=e=>e.isThemeGetter,z=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,C=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,a=new Map,s=(s,o)=>{t.set(s,o),++r>e&&(r=0,a=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=a.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},E=e=>{let{separator:r,experimentalParseClassName:t}=e,a=1===r.length,s=r[0],o=r.length,n=e=>{let t;let n=[],l=0,i=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===l){if(c===s&&(a||e.slice(d,d+o)===r)){n.push(e.slice(i,d)),i=d+o;continue}if("/"===c){t=d;continue}}"["===c?l++:"]"===c&&l--}let d=0===n.length?e:e.substring(i),c=d.startsWith("!"),u=c?d.substring(1):d;return{modifiers:n,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:n}):n},P=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},_=e=>({cache:C(e.cacheSize),parseClassName:E(e),...f(e)}),S=/\s+/,M=(e,r)=>{let{parseClassName:t,getClassGroupId:a,getConflictingClassGroupIds:s}=r,o=[],n=e.trim().split(S),l="";for(let e=n.length-1;e>=0;e-=1){let r=n[e],{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:u}=t(r),m=!!u,g=a(m?c.substring(0,u):c);if(!g){if(!m||!(g=a(c))){l=r+(l.length>0?" "+l:l);continue}m=!1}let p=P(i).join(":"),h=d?p+"!":p,x=h+g;if(o.includes(x))continue;o.push(x);let f=s(g,m);for(let e=0;e<f.length;++e){let r=f[e];o.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function O(){let e,r,t=0,a="";for(;t<arguments.length;)(e=arguments[t++])&&(r=R(e))&&(a&&(a+=" "),a+=r);return a}let R=e=>{let r;if("string"==typeof e)return e;let t="";for(let a=0;a<e.length;a++)e[a]&&(r=R(e[a]))&&(t&&(t+=" "),t+=r);return t},A=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},I=/^\[(?:([a-z-]+):)?(.+)\]$/i,L=/^\d+\/\d+$/,G=new Set(["px","full","screen"]),D=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,W=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,B=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=e=>$(e)||G.has(e)||L.test(e),F=e=>es(e,"length",eo),$=e=>!!e&&!Number.isNaN(Number(e)),U=e=>es(e,"number",$),H=e=>!!e&&Number.isInteger(Number(e)),J=e=>e.endsWith("%")&&$(e.slice(0,-1)),K=e=>I.test(e),X=e=>D.test(e),Y=new Set(["length","size","percentage"]),Z=e=>es(e,Y,en),Q=e=>es(e,"position",en),ee=new Set(["image","url"]),er=e=>es(e,ee,ei),et=e=>es(e,"",el),ea=()=>!0,es=(e,r,t)=>{let a=I.exec(e);return!!a&&(a[1]?"string"==typeof r?a[1]===r:r.has(a[1]):t(a[2]))},eo=e=>W.test(e)&&!q.test(e),en=()=>!1,el=e=>B.test(e),ei=e=>T.test(e);Symbol.toStringTag;let ed=function(e,...r){let t,a,s;let o=function(l){return a=(t=_(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,o=n,n(l)};function n(e){let r=a(e);if(r)return r;let o=M(e,t);return s(e,o),o}return function(){return o(O.apply(null,arguments))}}(()=>{let e=A("colors"),r=A("spacing"),t=A("blur"),a=A("brightness"),s=A("borderColor"),o=A("borderRadius"),n=A("borderSpacing"),l=A("borderWidth"),i=A("contrast"),d=A("grayscale"),c=A("hueRotate"),u=A("invert"),m=A("gap"),g=A("gradientColorStops"),p=A("gradientColorStopPositions"),h=A("inset"),x=A("margin"),f=A("opacity"),b=A("padding"),v=A("saturate"),y=A("scale"),w=A("sepia"),j=A("skew"),N=A("space"),k=A("translate"),z=()=>["auto","contain","none"],C=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto",K,r],P=()=>[K,r],_=()=>["",V,F],S=()=>["auto",$,K],M=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],O=()=>["solid","dashed","dotted","double","none"],R=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>["start","end","center","between","around","evenly","stretch"],L=()=>["","0",K],G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[$,K];return{cacheSize:500,separator:":",theme:{colors:[ea],spacing:[V,F],blur:["none","",X,K],brightness:D(),borderColor:[e],borderRadius:["none","","full",X,K],borderSpacing:P(),borderWidth:_(),contrast:D(),grayscale:L(),hueRotate:D(),invert:L(),gap:P(),gradientColorStops:[e],gradientColorStopPositions:[J,F],inset:E(),margin:E(),opacity:D(),padding:P(),saturate:D(),scale:D(),sepia:L(),skew:D(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",K]}],container:["container"],columns:[{columns:[X]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...M(),K]}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",H,K]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",K]}],grow:[{grow:L()}],shrink:[{shrink:L()}],order:[{order:["first","last","none",H,K]}],"grid-cols":[{"grid-cols":[ea]}],"col-start-end":[{col:["auto",{span:["full",H,K]},K]}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":[ea]}],"row-start-end":[{row:["auto",{span:[H,K]},K]}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",K]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",K]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...I()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...I(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...I(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",K,r]}],"min-w":[{"min-w":[K,r,"min","max","fit"]}],"max-w":[{"max-w":[K,r,"none","full","min","max","fit","prose",{screen:[X]},X]}],h:[{h:[K,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[K,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[K,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[K,r,"auto","min","max","fit"]}],"font-size":[{text:["base",X,F]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",U]}],"font-family":[{font:[ea]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",K]}],"line-clamp":[{"line-clamp":["none",$,U]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",V,K]}],"list-image":[{"list-image":["none",K]}],"list-style-type":[{list:["none","disc","decimal",K]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...O(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",V,F]}],"underline-offset":[{"underline-offset":["auto",V,K]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...M(),Q]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Z]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},er]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...O(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:O()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...O()]}],"outline-offset":[{"outline-offset":[V,K]}],"outline-w":[{outline:[V,F]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:_()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[V,F]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",X,et]}],"shadow-color":[{shadow:[ea]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...R(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":R()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[a]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",X,K]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[a]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",K]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",K]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",K]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[H,K]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",K]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[V,F,U]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function ec(...e){return ed(p(e))}let eu=x("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),em=i.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...o},n)=>{let l=s?c:"button";return(0,a.jsx)(l,{className:ec(eu({variant:r,size:t,className:e})),ref:n,suppressHydrationWarning:!0,...o})});em.displayName="Button";let eg=i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));eg.displayName="Card",i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let ep=i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("p-6 pt-0",e),...r}));ep.displayName="CardContent",i.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:ec("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let eh=x("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function ex({className:e,variant:r,...t}){return(0,a.jsx)("div",{className:ec(eh({variant:r}),e),...t})}let ef=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eb=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var ev={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ey=(0,i.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:s="",children:o,iconNode:n,...l},d)=>(0,i.createElement)("svg",{ref:d,...ev,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:eb("lucide",s),...l},[...n.map(([e,r])=>(0,i.createElement)(e,r)),...Array.isArray(o)?o:[o]])),ew=(e,r)=>{let t=(0,i.forwardRef)(({className:t,...a},s)=>(0,i.createElement)(ey,{ref:s,iconNode:r,className:eb(`lucide-${ef(e)}`,t),...a}));return t.displayName=`${e}`,t},ej=ew("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),eN=ew("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),ek=ew("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),ez=ew("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),eC=ew("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),eE=ew("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),eP=ew("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),e_=ew("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),eS=ew("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),eM=ew("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),eO=ew("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);function eR(){return(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("main",{className:"flex-1",children:[(0,a.jsx)("section",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-amber-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900",children:(0,a.jsx)("div",{className:"container relative z-10 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 rounded-full bg-green-100 dark:bg-green-900/30 px-4 py-2 text-sm font-medium text-green-700 dark:text-green-300 mb-8",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Transforming Lives Since 2015"]}),(0,a.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 dark:text-white leading-tight mb-6",children:["Empowering Nigeria Through"," ",(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-amber-500 bg-clip-text text-transparent",children:"Education"})]}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8 leading-relaxed",children:"Providing scholarships, school supplies, and hope to students, underprivileged people, and those in need across Nigeria."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[(0,a.jsx)(o(),{href:"/donate",children:(0,a.jsx)(em,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full px-8 py-4 text-lg font-semibold",children:"Donate Now"})}),(0,a.jsx)(o(),{href:"/auth/register",children:(0,a.jsx)(em,{size:"lg",variant:"outline",className:"border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 rounded-full px-8 py-4 text-lg font-semibold",children:"Become a Volunteer"})})]})]})})}),(0,a.jsx)("section",{className:"py-20 bg-white dark:bg-gray-950",children:(0,a.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200",children:"About Us"}),(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:"Building Brighter Futures Through Education"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed",children:"The Halimatu Abdullahi Kofa Knowledge Quest Foundation has been transforming lives across Nigeria since 2015. We believe that education is the key to breaking the cycle of poverty and creating lasting change in communities, supporting students, underprivileged individuals, and those in need."}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Our comprehensive approach includes providing scholarships, school supplies, literacy programs, and community development initiatives to ensure every child has access to quality education, regardless of their background."}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)(o(),{href:"/about",children:(0,a.jsxs)(em,{className:"bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group",children:["Learn More About Us",(0,a.jsx)(ej,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"})]})}),(0,a.jsx)(o(),{href:"/impact",children:(0,a.jsx)(em,{variant:"outline",className:"border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white rounded-full px-6 py-3 font-semibold transition-all duration-300",children:"View Our Impact"})})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl",children:[(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=600&h=450&fit=crop&crop=center",alt:"Students in classroom receiving quality education",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover",priority:!0}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,a.jsx)("div",{className:"absolute -bottom-6 -right-6 bg-white dark:bg-gray-900 rounded-xl shadow-lg p-4 border border-green-100 dark:border-green-800",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center",children:(0,a.jsx)(eN,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"9+"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Years of Impact"})]})]})})]})]})})}),(0,a.jsx)("section",{className:"py-20 bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"container px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("div",{className:"inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4",children:"Our Programs"}),(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"How We Create Change"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Discover our comprehensive educational programs that are transforming lives across Nigeria."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1566911294210-d11bb2b0ad55?w=400&h=300&fit=crop&crop=center",alt:"Primary school children learning in classroom",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:"Primary Schools"})]}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(ek,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Early Foundation"})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Building strong educational foundations with basic literacy, numeracy, and learning skills for children aged 6-12 years."}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400 font-medium mb-4",children:"Ages 6-12 • Foundation Learning"}),(0,a.jsx)(o(),{href:"/projects",children:(0,a.jsx)(em,{variant:"outline",className:"w-full rounded-full border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300",children:"Learn More"})})]})]}),(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden ring-2 ring-green-500/20",children:[(0,a.jsx)(ex,{className:"absolute top-4 right-4 z-10 bg-green-500 text-white rounded-full",children:"Featured"}),(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=300&fit=crop&crop=center",alt:"Secondary school students in science laboratory",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:"Secondary Schools"})]}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(ez,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Academic Excellence"})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Preparing students for WAEC, NECO, and JAMB with comprehensive subject support and career guidance for ages 13-18."}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400 font-medium mb-4",children:"Ages 13-18 • WAEC/NECO Prep"}),(0,a.jsx)(o(),{href:"/scholarships",children:(0,a.jsx)(em,{className:"w-full bg-green-600 hover:bg-green-700 text-white rounded-full",children:"Explore Programs"})})]})]}),(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=300&fit=crop&crop=center",alt:"University students in graduation ceremony",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:"University"})]}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(eC,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Higher Education"})]}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Comprehensive scholarships and mentorship programs for undergraduate and postgraduate studies across various fields."}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400 font-medium mb-4",children:"Full Scholarships • Career Mentoring"}),(0,a.jsx)(o(),{href:"/scholarships",children:(0,a.jsx)(em,{variant:"outline",className:"w-full rounded-full border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-300",children:"Apply Now"})})]})]})]})]})}),(0,a.jsx)("section",{className:"py-20 bg-white dark:bg-gray-950",children:(0,a.jsxs)("div",{className:"container px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("div",{className:"inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4",children:"Our Impact"}),(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Making a Real Difference"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"See how your support is transforming lives and communities across Northern Nigeria."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)(eg,{className:"text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(eE,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600 dark:text-green-400 mb-2",children:"1,200+"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Students Supported"})]}),(0,a.jsxs)(eg,{className:"text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(eC,{className:"h-6 w-6 text-amber-600"})}),(0,a.jsx)("div",{className:"text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2",children:"45"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Schools Partnered"})]}),(0,a.jsxs)(eg,{className:"text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(ek,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2",children:"5,000+"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Books Donated"})]}),(0,a.jsxs)(eg,{className:"text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(ez,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsx)("div",{className:"text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2",children:"₦12M"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"In Scholarships"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Transforming Communities Through Education"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed",children:"Every donation, every scholarship, and every book donated creates a ripple effect that transforms not just individual lives, but entire communities. Our holistic approach ensures sustainable impact that lasts for generations."}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5",children:(0,a.jsx)(eP,{className:"h-3 w-3 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:"Quality Education Access"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Ensuring every child has access to quality educational resources and opportunities."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5",children:(0,a.jsx)(e_,{className:"h-3 w-3 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:"Community Development"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Building stronger communities through literacy and educational programs."})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5",children:(0,a.jsx)(eS,{className:"h-3 w-3 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white",children:"Sustainable Impact"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Creating lasting change that benefits future generations."})]})]})]}),(0,a.jsx)(o(),{href:"/impact",children:(0,a.jsxs)(em,{className:"bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group",children:["View Full Impact Report",(0,a.jsx)(ej,{className:"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"})]})})]})]})]})}),(0,a.jsx)("section",{className:"py-20 bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"container px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("div",{className:"inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4",children:"Upcoming Events"}),(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Join Us in Action"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Participate in our upcoming events and be part of the change in Northern Nigeria."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?w=400&h=300&fit=crop&crop=center",alt:"Back-to-School Initiative Launch",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)(ex,{className:"bg-amber-500 text-white rounded-full",children:"Featured"})})]}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2",children:[(0,a.jsx)(eM,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"June 15, 2024"})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Back-to-School Initiative Launch"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-sm mb-4",children:"Kickstarting the academic year with essential school supplies for underprivileged students."}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,a.jsx)(eO,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Kaduna North Local Government"})]}),(0,a.jsx)(em,{variant:"outline",className:"w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300",children:"Register Now"})]})]}),(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden",children:[(0,a.jsx)("div",{className:"relative h-48",children:(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=300&fit=crop&crop=center",alt:"Scholarship Award Ceremony",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"})}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2",children:[(0,a.jsx)(eM,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"July 22, 2024"})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Scholarship Award Ceremony"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-sm mb-4",children:"Recognizing and awarding scholarships to deserving students."}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,a.jsx)(eO,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Katsina State Secretariat"})]}),(0,a.jsx)(em,{variant:"outline",className:"w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300",children:"Learn More"})]})]}),(0,a.jsxs)(eg,{className:"group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden",children:[(0,a.jsx)("div",{className:"relative h-48",children:(0,a.jsx)(l(),{src:"https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=400&h=300&fit=crop&crop=center",alt:"Community Literacy Program",fill:!0,sizes:"(max-width: 768px) 100vw, 33vw",className:"object-cover group-hover:scale-105 transition-transform duration-300"})}),(0,a.jsxs)(ep,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2",children:[(0,a.jsx)(eM,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"August 5, 2024"})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Community Literacy Program"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-sm mb-4",children:"Empowering adults with essential literacy skills for development."}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,a.jsx)(eO,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Kano Municipal Library"})]}),(0,a.jsx)(em,{variant:"outline",className:"w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300",children:"Join Program"})]})]})]}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsx)(o(),{href:"/events",children:(0,a.jsx)(em,{variant:"outline",className:"rounded-full px-8 py-3 font-semibold border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300",children:"View All Events"})})})]})}),(0,a.jsx)("section",{className:"py-20 bg-green-600 dark:bg-green-800 text-white",children:(0,a.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold mb-6",children:"Ready to Make a Difference?"}),(0,a.jsx)("p",{className:"text-lg text-green-100 mb-8 leading-relaxed",children:"Join us in our mission to transform lives through education. Whether through donations, volunteering, or spreading awareness, every action counts."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(o(),{href:"/donate",children:(0,a.jsxs)(em,{size:"lg",className:"bg-white text-green-600 hover:bg-gray-100 hover:text-green-700 rounded-full px-8 py-4 font-semibold group shadow-lg",children:["Donate Today",(0,a.jsx)(eS,{className:"ml-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300"})]})}),(0,a.jsx)(o(),{href:"/auth/register",children:(0,a.jsx)(em,{size:"lg",variant:"outline",className:"border-2 border-white text-white hover:bg-white hover:text-green-600 rounded-full px-8 py-4 font-semibold transition-all duration-300",children:"Become a Volunteer"})})]})]})})})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9131:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImgProps",{enumerable:!0,get:function(){return l}}),t(21122);let a=t(1322),s=t(27894);function o(e){return void 0!==e.default}function n(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,r){var t,l;let i,d,c,{src:u,sizes:m,unoptimized:g=!1,priority:p=!1,loading:h,className:x,quality:f,width:b,height:v,fill:y=!1,style:w,overrideSrc:j,onLoad:N,onLoadingComplete:k,placeholder:z="empty",blurDataURL:C,fetchPriority:E,decoding:P="async",layout:_,objectFit:S,objectPosition:M,lazyBoundary:O,lazyRoot:R,...A}=e,{imgConf:I,showAltText:L,blurComplete:G,defaultLoader:D}=r,W=I||s.imageConfigDefault;if("allSizes"in W)i=W;else{let e=[...W.deviceSizes,...W.imageSizes].sort((e,r)=>e-r),r=W.deviceSizes.sort((e,r)=>e-r),a=null==(t=W.qualities)?void 0:t.sort((e,r)=>e-r);i={...W,allSizes:e,deviceSizes:r,qualities:a}}if(void 0===D)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let q=A.loader||D;delete A.loader,delete A.srcSet;let B="__next_img_default"in q;if(B){if("custom"===i.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=q;q=r=>{let{config:t,...a}=r;return e(a)}}if(_){"fill"===_&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[_];e&&(w={...w,...e});let r={responsive:"100vw",fill:"100vw"}[_];r&&!m&&(m=r)}let T="",V=n(b),F=n(v);if((l=u)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let e=o(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,C=C||e.blurDataURL,T=e.src,!y){if(V||F){if(V&&!F){let r=V/e.width;F=Math.round(e.height*r)}else if(!V&&F){let r=F/e.height;V=Math.round(e.width*r)}}else V=e.width,F=e.height}}let $=!p&&("lazy"===h||void 0===h);(!(u="string"==typeof u?u:T)||u.startsWith("data:")||u.startsWith("blob:"))&&(g=!0,$=!1),i.unoptimized&&(g=!0),B&&!i.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(g=!0);let U=n(f),H=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:S,objectPosition:M}:{},L?{}:{color:"transparent"},w),J=G||"empty"===z?null:"blur"===z?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:V,heightInt:F,blurWidth:d,blurHeight:c,blurDataURL:C||"",objectFit:H.objectFit})+'")':'url("'+z+'")',K=J?{backgroundSize:H.objectFit||"cover",backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},X=function(e){let{config:r,src:t,unoptimized:a,width:s,quality:o,sizes:n,loader:l}=e;if(a)return{src:t,srcSet:void 0,sizes:void 0};let{widths:i,kind:d}=function(e,r,t){let{deviceSizes:a,allSizes:s}=e;if(t){let e=/(^|\s)(1?\d?\d)vw/g,r=[];for(let a;a=e.exec(t);a)r.push(parseInt(a[2]));if(r.length){let e=.01*Math.min(...r);return{widths:s.filter(r=>r>=a[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof r?{widths:a,kind:"w"}:{widths:[...new Set([r,2*r].map(e=>s.find(r=>r>=e)||s[s.length-1]))],kind:"x"}}(r,s,n),c=i.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:i.map((e,a)=>l({config:r,src:t,quality:o,width:e})+" "+("w"===d?e:a+1)+d).join(", "),src:l({config:r,src:t,quality:o,width:i[c]})}}({config:i,src:u,unoptimized:g,width:V,quality:U,sizes:m,loader:q});return{props:{...A,loading:$?"lazy":h,fetchPriority:E,width:V,height:F,decoding:P,className:x,style:{...H,...K},sizes:X.sizes,srcSet:X.srcSet,src:j||X.src},meta:{unoptimized:g,priority:p,placeholder:z,fill:y}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11925:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),s=t(48088),o=t(88170),n=t.n(o),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2649)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},27894:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{VALID_LOADERS:function(){return t},imageConfigDefault:function(){return a}});let t=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,r)=>{"use strict";function t(e){var r;let{config:t,src:a,width:s,quality:o}=e,n=o||(null==(r=t.qualities)?void 0:r.reduce((e,r)=>Math.abs(r-75)<Math.abs(e-75)?r:e))||75;return t.path+"?url="+encodeURIComponent(a)+"&w="+s+"&q="+n+(a.startsWith("/_next/static/media/"),"")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a}}),t.__next_img_default=!0;let a=t},33873:e=>{"use strict";e.exports=require("path")},49603:(e,r,t)=>{let{createProxy:a}=t(39844);e.exports=a("C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\image-component.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70099:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return i},getImageProps:function(){return l}});let a=t(72639),s=t(9131),o=t(49603),n=a._(t(32091));function l(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let i=o.Image},72648:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.t.bind(t,46533,23))},85800:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,49603,23))}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[555,533,702],()=>t(11925));module.exports=a})();