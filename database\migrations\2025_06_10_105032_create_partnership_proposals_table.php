<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partnership_proposals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('partner_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->enum('collaboration_type', ['joint_program', 'resource_sharing', 'event_hosting', 'research', 'funding', 'training']);
            $table->string('proposed_duration');
            $table->text('expected_outcomes');
            $table->json('resources_required')->nullable();
            $table->decimal('budget_estimate', 15, 2)->nullable();
            $table->text('timeline')->nullable();
            $table->text('target_beneficiaries');
            $table->json('proposal_documents')->nullable();
            $table->enum('status', ['submitted', 'under_review', 'approved', 'rejected'])->default('submitted');
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('review_notes')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->date('implementation_start_date')->nullable();
            $table->date('implementation_end_date')->nullable();
            $table->timestamps();

            $table->index(['status', 'collaboration_type']);
            $table->index(['partner_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partnership_proposals');
    }
};
