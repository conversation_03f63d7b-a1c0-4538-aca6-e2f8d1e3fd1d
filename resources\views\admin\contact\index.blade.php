@extends('layouts.admin')

@section('title', 'Contact Messages')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Contact Messages</h1>
        <div>
            <a href="{{ route('admin.contact.faq') }}" class="btn btn-info me-2">
                <i class="fas fa-question-circle me-2"></i>Manage FAQ
            </a>
            <button class="btn btn-primary">
                <i class="fas fa-download me-2"></i>Export Messages
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Messages</div>
                            <div class="h4">{{ $messages->total() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New Messages</div>
                            <div class="h4">{{ $messages->where('status', 'new')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">In Progress</div>
                            <div class="h4">{{ $messages->where('status', 'in_progress')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Resolved</div>
                            <div class="h4">{{ $messages->where('status', 'resolved')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Messages</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.contact.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="new">New</option>
                            <option value="in_progress">In Progress</option>
                            <option value="resolved">Resolved</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="priority" class="form-select">
                            <option value="">All Priorities</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Search messages..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Contact Messages</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($messages ?? [] as $message)
                        <tr>
                            <td>{{ $message->id ?? rand(1, 100) }}</td>
                            <td>{{ $message->name ?? 'John Doe' }}</td>
                            <td>{{ $message->email ?? '<EMAIL>' }}</td>
                            <td>{{ Str::limit($message->subject ?? 'Sample Subject', 30) }}</td>
                            <td>
                                @php
                                    $status = $message->status ?? 'new';
                                    $statusClass = $status === 'new' ? 'warning' : ($status === 'in_progress' ? 'info' : 'success');
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ ucfirst(str_replace('_', ' ', $status)) }}</span>
                            </td>
                            <td>
                                @php
                                    $priority = $message->priority ?? 'medium';
                                    $priorityClass = $priority === 'high' ? 'danger' : ($priority === 'medium' ? 'warning' : 'secondary');
                                @endphp
                                <span class="badge bg-{{ $priorityClass }}">{{ ucfirst($priority) }}</span>
                            </td>
                            <td>{{ $message->created_at ? $message->created_at->format('M j, Y') : now()->format('M j, Y') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.contact.messages.show', $message->id ?? 1) }}" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-success" onclick="markAsResolved({{ $message->id ?? 1 }})" title="Mark as Resolved">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteMessage({{ $message->id ?? 1 }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        @for($i = 1; $i <= 10; $i++)
                        <tr>
                            <td>{{ $i }}</td>
                            <td>Contact Person {{ $i }}</td>
                            <td>contact{{ $i }}@example.com</td>
                            <td>{{ ['Inquiry about programs', 'Volunteer application', 'Donation question', 'General inquiry', 'Partnership proposal'][array_rand(['Inquiry about programs', 'Volunteer application', 'Donation question', 'General inquiry', 'Partnership proposal'])] }}</td>
                            <td>
                                @php $status = ['new', 'in_progress', 'resolved'][array_rand(['new', 'in_progress', 'resolved'])]; @endphp
                                <span class="badge bg-{{ $status === 'new' ? 'warning' : ($status === 'in_progress' ? 'info' : 'success') }}">{{ ucfirst(str_replace('_', ' ', $status)) }}</span>
                            </td>
                            <td>
                                @php $priority = ['low', 'medium', 'high'][array_rand(['low', 'medium', 'high'])]; @endphp
                                <span class="badge bg-{{ $priority === 'high' ? 'danger' : ($priority === 'medium' ? 'warning' : 'secondary') }}">{{ ucfirst($priority) }}</span>
                            </td>
                            <td>{{ now()->subDays(rand(1, 30))->format('M j, Y') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.contact.messages.show', $i) }}" class="btn btn-sm btn-primary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form method="POST" action="{{ route('admin.contact.messages.update', $i) }}" style="display: inline;">
                                        @csrf
                                        @method('PUT')
                                        <input type="hidden" name="status" value="resolved">
                                        <button type="submit" class="btn btn-sm btn-success" title="Mark as Resolved">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <form method="POST" action="{{ route('admin.contact.messages.destroy', $i) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this message?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endfor
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($messages) && method_exists($messages, 'links'))
                <div class="d-flex justify-content-center">
                    {{ $messages->links() }}
                </div>
            @endif
        </div>
    </div>
</div>


@endsection 