"use client"


import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, Users, MapPin, Mail, Phone } from "lucide-react"

const leadership = [
  {
    name: "<PERSON><PERSON> <PERSON>",
    role: "Chairman / Trustee",
    tenure: "2015",
    image: "/placeholder.svg?height=400&width=400&text=Chairman&bg=22c55e&color=ffffff",
    bio: "Experienced chairman providing strategic leadership and governance oversight for the foundation's mission and vision.",
    quote: "Leading with integrity and commitment to educational excellence.",
    contact: {
      email: "<EMAIL>",
      phone: "***********",
    },
  },
  {
    name: "<PERSON><PERSON>",
    role: "Trustee & Secretary",
    tenure: "2017",
    image: "/placeholder.svg?height=400&width=400&text=Secretary&bg=059669&color=ffffff",
    bio: "Serves dual role as trustee and secretary, ensuring proper documentation and administrative oversight.",
    quote: "Transparency and accountability are the foundations of effective governance.",
    contact: {
      email: "adamuab<PERSON><PERSON><EMAIL>",
      phone: "***********",
    },
  },
]

const boardMembers = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Trustee",
    tenure: "2018",
    image: "/placeholder.svg?height=300&width=300&text=Trustee&bg=f59e0b&color=ffffff",
    contact: {
      email: "<EMAIL>",
      phone: "***********",
    },
  },
  {
    name: "Fatima Abdullahi Aliyu",
    role: "Trustee",
    tenure: "2019",
    image: "/placeholder.svg?height=300&width=300&text=Trustee&bg=7c3aed&color=ffffff",
    contact: {
      email: "<EMAIL>",
      phone: "***********",
    },
  },
  {
    name: "Abubakar Aliyu",
    role: "Trustee",
    tenure: "2020",
    image: "/placeholder.svg?height=300&width=300&text=Trustee&bg=dc2626&color=ffffff",
    contact: {
      email: "<EMAIL>",
      phone: "***********",
    },
  },
  {
    name: "Aliyu Jibril",
    role: "Trustee",
    tenure: "2021",
    image: "/placeholder.svg?height=300&width=300&text=Trustee&bg=0891b2&color=ffffff",
    contact: {
      email: "<EMAIL>",
      phone: "07068186021",
    },
  },
  {
    name: "Halimatu Aliyu Sani",
    role: "Trustee",
    tenure: "2022",
    image: "/placeholder.svg?height=300&width=300&text=Trustee&bg=16a34a&color=ffffff",
    contact: {
      email: "<EMAIL>",
      phone: "08030570384",
    },
  },
]

const staff = [
  {
    name: "Usman Yakubu",
    role: "Coordinator",
    location: "Zaria Office",
    image: "/placeholder.svg?height=250&width=250&text=Coordinator&bg=22c55e&color=ffffff",
    responsibilities: ["Program coordination", "Team management", "Strategic planning"],
  },
  {
    name: "Abubakar Alamin Sabo",
    role: "ICT",
    location: "Zaria Office",
    image: "/placeholder.svg?height=250&width=250&text=ICT&bg=059669&color=ffffff",
    responsibilities: ["IT infrastructure", "System maintenance", "Digital solutions"],
  },
  {
    name: "Aliyu Abubakar",
    role: "Financial Officer",
    location: "Zaria Office",
    image: "/placeholder.svg?height=250&width=250&text=Finance&bg=f59e0b&color=ffffff",
    responsibilities: ["Budget management", "Financial reporting", "Audit coordination"],
  },
]

const volunteers = [
  {
    name: "Dr. Usman Bello",
    role: "Medical Volunteer",
    contribution: "Provides health screenings for students",
    years: "3 years",
  },
  {
    name: "Eng. Fatima Aliyu",
    role: "STEM Mentor",
    contribution: "Mentors female students in science and technology",
    years: "2 years",
  },
  {
    name: "Malam Sani Ahmad",
    role: "Community Mobilizer",
    contribution: "Organizes community education programs",
    years: "4 years",
  },
  {
    name: "Hajiya Zainab Umar",
    role: "Literacy Instructor",
    contribution: "Teaches adult literacy classes",
    years: "5 years",
  },
]

export default function TeamPage() {
  return (
    <div className="flex min-h-screen flex-col bg-pattern">

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden">
          <div className="absolute inset-0 bg-grid opacity-20"></div>
          <div className="container relative">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <div className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2 inline-block">Our Team</div>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up">
                Meet the People Behind the Mission
              </h1>
              <p
                className="text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto"
                style={{ animationDelay: "0.2s" }}
              >
                Our dedicated team of leaders, staff, and volunteers work tirelessly to transform lives through
                education and support initiatives across Nigeria, assisting students, the underprivileged, and those in need.
              </p>
              <div
                className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">2</div>
                  <div className="text-green-200 text-sm">Leadership Team</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">7</div>
                  <div className="text-green-200 text-sm">Board Members</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">3</div>
                  <div className="text-green-200 text-sm">Full-time Staff</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">50+</div>
                  <div className="text-green-200 text-sm">Volunteers</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Leadership Team */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Leadership Team</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Visionary leaders who guide our mission and ensure we stay true to our values of transparency,
                compassion, and sustainable impact.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {leadership.map((leader, index) => (
                <Card
                  key={index}
                  className="overflow-hidden neumorphic rounded-3xl border-green-100 dark:border-green-800 hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
                >
                  <CardContent className="p-8">
                    <div className="flex flex-col md:flex-row gap-6">
                      <div className="flex-shrink-0">
                        <div className="relative w-32 h-32 mx-auto md:mx-0">
                          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-700 rounded-full opacity-20 blur-xl"></div>
                          <div className="relative w-full h-full rounded-full overflow-hidden shadow-lg border-4 border-white dark:border-gray-800">
                            <div className="w-full h-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
                              <span className="text-3xl font-bold text-white">{leader.name.charAt(0)}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex-1 space-y-4">
                        <div className="text-center md:text-left">
                          <h3 className="text-2xl font-bold text-green-800 dark:text-green-200">{leader.name}</h3>
                          <p className="text-green-600 dark:text-green-400 font-semibold">{leader.role}</p>
                          <p className="text-sm text-muted-foreground">{leader.tenure}</p>
                        </div>

                        <blockquote className="text-muted-foreground italic border-l-4 border-green-500 pl-4">
                          "{leader.quote}"
                        </blockquote>

                        <p className="text-muted-foreground leading-relaxed">{leader.bio}</p>

                        <div className="flex gap-4 pt-4">
                          <Button variant="outline" size="sm" className="rounded-full">
                            <Mail className="h-4 w-4 mr-2" />
                            Contact
                          </Button>
                          <Button variant="outline" size="sm" className="rounded-full">
                            <Phone className="h-4 w-4 mr-2" />
                            Call
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Board Members */}
        <AnimatedSection className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Board of Trustees</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Experienced professionals who provide governance, oversight, and strategic direction to our foundation.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {boardMembers.map((member, index) => (
                <Card
                  key={index}
                  className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
                >
                  <CardContent className="p-6">
                    <div className="relative w-24 h-24 mx-auto mb-4">
                      <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-700 rounded-full opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
                      <div className="relative w-full h-full rounded-full overflow-hidden shadow-lg">
                        <div className="w-full h-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
                          <span className="text-xl font-bold text-white">
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </span>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-lg font-bold text-green-800 dark:text-green-200 mb-1">{member.name}</h3>
                    <p className="text-green-600 dark:text-green-400 font-medium mb-3">{member.role}</p>
                    <p className="text-xs text-muted-foreground mb-2">{member.tenure}</p>

                    {member.contact && (
                      <div className="space-y-1 pt-2 border-t border-green-100 dark:border-green-800">
                        <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          <span className="truncate">{member.contact.email}</span>
                        </div>
                        <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          <span>{member.contact.phone}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Staff Team */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Our Staff</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Dedicated professionals who implement our programs and ensure day-to-day operations run smoothly.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {staff.map((member, index) => (
                <Card
                  key={index}
                  className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
                >
                  <CardContent className="p-6">
                    <div className="relative w-20 h-20 mx-auto mb-4">
                      <div className="w-full h-full rounded-full overflow-hidden shadow-lg">
                        <div className="w-full h-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                          <span className="text-lg font-bold text-white">
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </span>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-lg font-bold text-green-800 dark:text-green-200 mb-1">{member.name}</h3>
                    <p className="text-green-600 dark:text-green-400 font-medium mb-2">{member.role}</p>
                    <div className="flex items-center justify-center gap-1 mb-3">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">{member.location}</span>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 text-sm">Responsibilities</h4>
                      <ul className="space-y-1">
                        {member.responsibilities.map((resp, i) => (
                          <li key={i} className="text-xs text-muted-foreground">
                            • {resp}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Volunteers */}
        <AnimatedSection className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Our Volunteers</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Community champions who donate their time and expertise to support our educational mission.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {volunteers.map((volunteer, index) => (
                <Card
                  key={index}
                  className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Heart className="h-8 w-8 text-amber-600" />
                    </div>
                    <h3 className="text-lg font-bold text-green-800 dark:text-green-200 mb-1">{volunteer.name}</h3>
                    <p className="text-green-600 dark:text-green-400 font-medium mb-2">{volunteer.role}</p>
                    <p className="text-sm text-muted-foreground mb-3">{volunteer.contribution}</p>
                    <div className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 rounded-full px-3 py-1 text-xs inline-block">
                      {volunteer.years}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <Card className="inline-block neumorphic rounded-2xl border-green-100 dark:border-green-800 p-8">
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-bold text-green-800 dark:text-green-200">Join Our Team</h3>
                  <p className="text-muted-foreground max-w-md">
                    We're always looking for passionate individuals to join our mission. Whether you have a few hours a
                    week or want to make a career change, we have opportunities for you.
                  </p>
                  <Button className="bg-green-600 hover:bg-green-700 transition-all duration-300 hover:scale-105 rounded-full">
                    <Heart className="mr-2 h-4 w-4" />
                    Volunteer With Us
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </AnimatedSection>

        {/* Call to Action */}
        <AnimatedSection className="py-20 bg-green-900 text-white">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Together, We're Building a Brighter Future
              </h2>
              <p className="text-green-100 text-lg max-w-2xl mx-auto mb-8">
                Our team is united by a shared vision: ensuring every child in Northern Nigeria has access to quality
                education. Join us in this transformative journey.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-amber-500 hover:bg-amber-600 text-white rounded-full transition-all duration-300 hover:scale-105"
                >
                  <Heart className="mr-2 h-5 w-5" />
                  Join Our Team
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-green-900 rounded-full transition-all duration-300 hover:scale-105"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Partner With Us
                </Button>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </main>
    </div>
  )
}
