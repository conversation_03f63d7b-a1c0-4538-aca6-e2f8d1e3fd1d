<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EducationalResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'category',
        'file_path',
        'video_url',
        'duration',
        'difficulty_level',
        'tags',
        'is_active',
        'is_featured',
        'view_count',
        'download_count',
        'created_by'
    ];

    protected $casts = [
        'tags' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'view_count' => 'integer',
        'download_count' => 'integer'
    ];

    /**
     * Get the user who created this resource
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get user progress for this resource
     */
    public function userProgress()
    {
        return $this->hasMany(UserResourceProgress::class, 'resource_id');
    }

    /**
     * Get user bookmarks for this resource
     */
    public function bookmarks()
    {
        return $this->hasMany(UserResourceBookmark::class, 'resource_id');
    }

    /**
     * Scope for active resources
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured resources
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for specific category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for specific type
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        if ($this->file_path) {
            return asset('storage/' . $this->file_path);
        }
        return null;
    }

    /**
     * Get thumbnail URL
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->type === 'video' && $this->video_url) {
            // Extract YouTube thumbnail if it's a YouTube URL
            if (strpos($this->video_url, 'youtube.com') !== false || strpos($this->video_url, 'youtu.be') !== false) {
                preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $this->video_url, $matches);
                if (isset($matches[1])) {
                    return "https://img.youtube.com/vi/{$matches[1]}/maxresdefault.jpg";
                }
            }
        }
        return asset('img/default-resource-thumbnail.jpg');
    }

    /**
     * Increment view count
     */
    public function incrementViews()
    {
        $this->increment('view_count');
    }

    /**
     * Increment download count
     */
    public function incrementDownloads()
    {
        $this->increment('download_count');
    }

    /**
     * Check if resource is video
     */
    public function isVideo()
    {
        return $this->type === 'video';
    }

    /**
     * Check if resource is PDF
     */
    public function isPdf()
    {
        return $this->type === 'pdf';
    }

    /**
     * Check if resource is document
     */
    public function isDocument()
    {
        return $this->type === 'document';
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return null;
        }

        $minutes = intval($this->duration);
        $seconds = ($this->duration - $minutes) * 60;

        if ($minutes >= 60) {
            $hours = intval($minutes / 60);
            $minutes = $minutes % 60;
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get difficulty level badge color
     */
    public function getDifficultyColorAttribute()
    {
        switch ($this->difficulty_level) {
            case 'beginner':
                return 'green';
            case 'intermediate':
                return 'yellow';
            case 'advanced':
                return 'red';
            default:
                return 'gray';
        }
    }
}
