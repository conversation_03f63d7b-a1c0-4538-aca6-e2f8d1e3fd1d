"use client"

import { Suspense } from "react"

import { DonationForm } from "@/components/donation-form"
import { DonationTiers } from "@/components/donation-tiers"
import { DonationImpact } from "@/components/donation-impact"
import { AnimatedSection } from "@/components/animated-section"
import { Users, Check, Heart, Star } from "lucide-react"

const impactStats = [
  { number: "50,000+", label: "Lives Impacted", icon: <Users className="h-6 w-6" /> },
  { number: "200+", label: "Projects Completed", icon: <Check className="h-6 w-6" /> },
  { number: "15", label: "States Reached", icon: <Heart className="h-6 w-6" /> },
  { number: "98%", label: "Funds to Programs", icon: <Star className="h-6 w-6" /> },
]

export default function DonatePage() {
  return (
    <div className="flex min-h-screen flex-col bg-pattern">

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden">
          <div className="absolute inset-0 bg-grid opacity-20"></div>
          <div className="container relative">
            <div className="max-w-3xl mx-auto text-center space-y-6">
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up">
                Make a Difference Today
              </h1>
              <p className="text-xl text-green-100 animate-fade-in-up" style={{ animationDelay: "0.2s" }}>
                Your donation directly impacts communities across Nigeria. Every contribution, no matter the size,
                creates lasting change and builds hope for a better future.
              </p>
              <div
                className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                {impactStats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2 text-amber-400">{stat.icon}</div>
                    <div className="text-2xl font-bold">{stat.number}</div>
                    <div className="text-sm text-green-200">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Donation Tiers */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Choose Your Impact Level</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Select a donation tier that matches your desire to create change. Each level provides specific benefits
                and directly supports our mission.
              </p>
            </div>

            <Suspense fallback={<div className="h-96 flex items-center justify-center">Loading donation tiers...</div>}>
              <DonationTiers />
            </Suspense>
          </div>
        </AnimatedSection>

        {/* Donation Form */}
        <AnimatedSection className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container max-w-4xl">
            <Suspense fallback={<div className="h-96 flex items-center justify-center">Loading donation form...</div>}>
              <DonationForm />
            </Suspense>
          </div>
        </AnimatedSection>

        {/* Impact Section */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Your Impact in Action</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                See how your donations are making a real difference in communities across Nigeria.
              </p>
            </div>

            <Suspense fallback={<div className="h-96 flex items-center justify-center">Loading impact data...</div>}>
              <DonationImpact />
            </Suspense>
          </div>
        </AnimatedSection>
      </main>

    </div>
  )
}
