<?php

namespace App\Policies;

use App\Models\User;
use App\Models\ScholarshipApplication;
use App\Models\Student;
use App\Models\Scholarship;
use Illuminate\Auth\Access\HandlesAuthorization;

class ScholarshipApplicationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any scholarship applications.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization', 'user']);
    }

    /**
     * Determine whether the user can view the scholarship application.
     */
    public function view(User $user, ScholarshipApplication $application): bool
    {
        // Admins can view all applications
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view applications from their students
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own applications
        if ($user->role === 'user') {
            return $application->student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create scholarship applications.
     */
    public function create(User $user, ?Scholarship $scholarship = null): bool
    {
        if (!$scholarship) {
            return false;
        }

        // Admins can create applications for any scholarship
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create applications for primary and secondary scholarships
        if ($user->role === 'partner_organization') {
            return in_array($scholarship->category, ['primary', 'secondary']);
        }

        // Individual users can create applications for university scholarships
        if ($user->role === 'user') {
            return $scholarship->category === 'university';
        }

        return false;
    }

    /**
     * Determine whether the user can update the scholarship application.
     */
    public function update(User $user, ScholarshipApplication $application): bool
    {
        // Admins can update all applications
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can update applications from their students (if not yet submitted)
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id 
                && $application->status === 'draft';
        }

        // Individual users can update their own applications (if not yet submitted)
        if ($user->role === 'user') {
            return $application->student->user_id === $user->id 
                && $application->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can delete the scholarship application.
     */
    public function delete(User $user, ScholarshipApplication $application): bool
    {
        // Only admins can delete applications
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations and users can delete draft applications
        if ($application->status === 'draft') {
            if ($user->role === 'partner_organization') {
                return $application->student->school_id === $user->partner_organization_id;
            }

            if ($user->role === 'user') {
                return $application->student->user_id === $user->id;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can approve/reject scholarship applications.
     */
    public function review(User $user, ScholarshipApplication $application): bool
    {
        // Only admins can review applications
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can submit scholarship applications.
     */
    public function submit(User $user, ScholarshipApplication $application): bool
    {
        // Admins can submit any application
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can submit applications for their students
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id;
        }

        // Individual users can submit their own applications
        if ($user->role === 'user') {
            return $application->student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create applications for a specific student.
     */
    public function createForStudent(User $user, Student $student, Scholarship $scholarship): bool
    {
        // Admins can create applications for any student
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create applications for their students (primary/secondary only)
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id 
                && in_array($scholarship->category, ['primary', 'secondary']);
        }

        // Individual users can only create applications for themselves (university only)
        if ($user->role === 'user') {
            return $student->user_id === $user->id 
                && $scholarship->category === 'university';
        }

        return false;
    }

    /**
     * Determine whether the user can view application statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can export application data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can perform bulk operations on applications.
     */
    public function bulkOperations(User $user): bool
    {
        return $user->role === 'admin';
    }
}
