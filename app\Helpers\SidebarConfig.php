<?php

namespace App\Helpers;

class SidebarConfig
{
    /**
     * Get sidebar configuration for student dashboard
     */
    public static function getStudentSidebar($currentRoute = null)
    {
        $items = [
            ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/student-dashboard'],
            ['label' => 'My Applications', 'icon' => 'file-alt', 'url' => '/student/applications'],
            ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/student/scholarships'],
            ['label' => 'Academic Profile', 'icon' => 'user-graduate', 'url' => '/student/profile'],
            ['label' => 'Documents', 'icon' => 'folder', 'url' => '/student/documents'],
            ['label' => 'Resources', 'icon' => 'book', 'url' => '/student/resources'],
            ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/student/support']
        ];

        // Mark current route as active
        if ($currentRoute) {
            foreach ($items as &$item) {
                if ($item['url'] === $currentRoute) {
                    $item['active'] = true;
                }
            }
        }

        return $items;
    }

    /**
     * Get sidebar configuration for partner dashboard
     */
    public static function getPartnerSidebar($currentRoute = null)
    {
        $items = [
            ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
            ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
            ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
            ['label' => 'Available Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships'],
            ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
            ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
            ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
        ];

        // Mark current route as active
        if ($currentRoute) {
            foreach ($items as &$item) {
                if ($item['url'] === $currentRoute) {
                    $item['active'] = true;
                }
            }
        }

        return $items;
    }

    /**
     * Get sidebar configuration for user dashboard
     */
    public static function getUserSidebar($currentRoute = null)
    {
        $items = [
            ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/dashboard/user'],
            ['label' => 'Profile', 'icon' => 'user', 'url' => '/user/profile'],
            ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => frontendUrl('scholarships'), 'external' => true],
            ['label' => 'Programs', 'icon' => 'users', 'url' => frontendUrl('programs'), 'external' => true],
            ['label' => 'Events', 'icon' => 'calendar', 'url' => frontendUrl('events'), 'external' => true],
            ['label' => 'Contact', 'icon' => 'envelope', 'url' => frontendUrl('contact'), 'external' => true]
        ];

        // Mark current route as active
        if ($currentRoute) {
            foreach ($items as &$item) {
                if ($item['url'] === $currentRoute) {
                    $item['active'] = true;
                }
            }
        }

        return $items;
    }

    /**
     * Get sidebar configuration for admin dashboard
     */
    public static function getAdminSidebar($currentRoute = null)
    {
        $items = [
            ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/admin/dashboard'],
            ['label' => 'Users', 'icon' => 'users', 'url' => '/admin/users'],
            ['label' => 'Programs', 'icon' => 'project-diagram', 'url' => '/admin/programs'],
            ['label' => 'Events', 'icon' => 'calendar', 'url' => '/admin/events'],
            ['label' => 'Volunteers', 'icon' => 'hands-helping', 'url' => '/admin/volunteers'],
            ['label' => 'Donations', 'icon' => 'heart', 'url' => '/admin/donations'],
            ['label' => 'Blog', 'icon' => 'blog', 'url' => '/admin/blog'],
            ['label' => 'Newsletter', 'icon' => 'envelope', 'url' => '/admin/newsletter'],
            ['label' => 'Contact', 'icon' => 'phone', 'url' => '/admin/contact'],
            ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/admin/scholarships'],
            ['label' => 'Team', 'icon' => 'user-friends', 'url' => '/admin/team'],
            ['label' => 'Analytics', 'icon' => 'chart-line', 'url' => '/admin/analytics'],
            ['label' => 'Settings', 'icon' => 'cog', 'url' => '/admin/settings']
        ];

        // Mark current route as active
        if ($currentRoute) {
            foreach ($items as &$item) {
                if ($item['url'] === $currentRoute) {
                    $item['active'] = true;
                }
            }
        }

        return $items;
    }
}
