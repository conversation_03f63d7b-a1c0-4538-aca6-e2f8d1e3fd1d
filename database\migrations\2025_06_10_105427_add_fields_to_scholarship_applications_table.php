<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            $table->string('student_id_number')->nullable()->after('application_data');
            $table->decimal('current_gpa', 3, 2)->nullable()->after('student_id_number');
            $table->string('year_of_study')->nullable()->after('current_gpa');
            $table->string('institution_name')->nullable()->after('year_of_study');
            $table->text('personal_statement')->nullable()->after('institution_name');
            $table->text('academic_achievements')->nullable()->after('personal_statement');
            $table->text('financial_need_statement')->nullable()->after('academic_achievements');
            $table->text('career_goals')->nullable()->after('financial_need_statement');
            $table->text('additional_info')->nullable()->after('career_goals');
            $table->decimal('award_amount', 10, 2)->nullable()->after('reviewed_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            $table->dropColumn([
                'student_id_number',
                'current_gpa',
                'year_of_study',
                'institution_name',
                'personal_statement',
                'academic_achievements',
                'financial_need_statement',
                'career_goals',
                'additional_info',
                'award_amount'
            ]);
        });
    }
};
