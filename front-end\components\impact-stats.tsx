"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Users, School, BookOpen, DollarSign } from "lucide-react"

interface StatItem {
  icon: React.ReactNode
  number: string
  label: string
  color: string
}

const stats: StatItem[] = [
  {
    icon: <Users className="h-8 w-8" />,
    number: "1,200+",
    label: "Students Supported",
    color: "text-blue-600",
  },
  {
    icon: <School className="h-8 w-8" />,
    number: "45",
    label: "Schools Partnered",
    color: "text-green-600",
  },
  {
    icon: <BookOpen className="h-8 w-8" />,
    number: "5,000+",
    label: "Books Donated",
    color: "text-purple-600",
  },
  {
    icon: <DollarSign className="h-8 w-8" />,
    number: "₦12M",
    label: "in Scholarships Awarded",
    color: "text-amber-600",
  },
]

export function ImpactStats() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card
          key={index}
          className={`text-center overflow-hidden border-green-100 dark:border-green-800 transition-all duration-500 hover:shadow-lg hover:-translate-y-2 hover:border-green-300 dark:hover:border-green-600 neumorphic rounded-2xl group ${
            isVisible ? "animate-fade-in-up" : "opacity-0"
          }`}
          style={{ animationDelay: `${index * 0.2}s` }}
        >
          <CardContent className="p-6">
            <div
              className={`flex justify-center mb-4 ${stat.color} group-hover:scale-110 transition-transform duration-300`}
            >
              {stat.icon}
            </div>
            <div className="text-3xl font-bold text-green-800 dark:text-green-200 mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
              {stat.number}
            </div>
            <div className="text-sm text-muted-foreground group-hover:text-foreground transition-colors duration-300">
              {stat.label}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
