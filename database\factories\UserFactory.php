<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'phone_number' => fake()->phoneNumber(),
            'date_of_birth' => fake()->date('Y-m-d', '2000-01-01'),
            'gender' => fake()->randomElement(['male', 'female']),
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'country' => 'Nigeria',
            'role' => 'user',
            'status' => 'active',
            'short_id' => 'HLTKKQ' . fake()->unique()->numberBetween(1000, 9999),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Create an admin user.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
        ]);
    }

    /**
     * Create a partner organization user.
     */
    public function partnerOrganization(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'partner_organization',
        ]);
    }

    /**
     * Create a volunteer user.
     */
    public function volunteer(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'volunteer',
        ]);
    }

    /**
     * Create a donor user.
     */
    public function donor(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'donor',
        ]);
    }
}
