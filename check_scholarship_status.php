<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Checking Scholarship Status Values ===\n\n";

$scholarships = \App\Models\Scholarship::all();

foreach ($scholarships as $scholarship) {
    echo "ID {$scholarship->id}: {$scholarship->title}\n";
    echo "  Status: '{$scholarship->status}'\n";
    echo "  Deadline: {$scholarship->application_deadline}\n";
    echo "  Is deadline future? " . ($scholarship->application_deadline > now() ? 'YES' : 'NO') . "\n";
    echo "  Custom fields: " . $scholarship->customFields->count() . "\n";
    echo "\n";
}

echo "=== Testing API Query ===\n\n";

// Test the exact query from the API
$query = \App\Models\Scholarship::where('status', 'active')
    ->orWhere('status', 'open')
    ->where('application_deadline', '>', now())
    ->with([
        'fields' => function($query) {
            $query->where('is_active', true)->orderBy('field_order');
        }
    ])
    ->withCount(['applications as current_applicants']);

$results = $query->get();
echo "API Query Results: " . $results->count() . " scholarships\n";

foreach ($results as $scholarship) {
    echo "  - ID {$scholarship->id}: {$scholarship->title} (Status: {$scholarship->status})\n";
}

echo "\n=== Testing Individual Scholarship ===\n";

$scholarship5 = \App\Models\Scholarship::where(function($query) {
        $query->where('status', 'active')->orWhere('status', 'open');
    })
    ->where('application_deadline', '>', now())
    ->with([
        'fields' => function($query) {
            $query->where('is_active', true)->orderBy('field_order');
        }
    ])
    ->withCount('applications as current_applicants')
    ->find(5);

if ($scholarship5) {
    echo "Scholarship 5 found: {$scholarship5->title}\n";
    echo "Custom fields: " . $scholarship5->fields->count() . "\n";
} else {
    echo "Scholarship 5 not found with API query\n";
}
