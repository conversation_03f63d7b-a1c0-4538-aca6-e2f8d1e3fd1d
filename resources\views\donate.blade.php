@extends('layouts.app')

@section('title', 'Donate - Support Our Mission')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- Hero Section -->
    <section class="py-20 px-4">
        <div class="container mx-auto text-center">
            <h1 class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-6">
                Make a Difference Today
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Your donation helps us provide education, healthcare, and essential services to communities in need. 
                Every contribution, no matter the size, creates lasting impact.
            </p>
        </div>
    </section>

    <!-- Donation Form Section -->
    <section class="py-16 px-4">
        <div class="container mx-auto max-w-4xl">
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                    {{ session('error') }}
                </div>
            @endif

            <div class="bg-white rounded-3xl shadow-2xl p-8 md:p-12">
                <h2 class="text-3xl font-bold text-center mb-8 text-gray-800">Choose Your Donation Method</h2>
                
                <!-- Payment Method Selection -->
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <!-- PayStack Payment -->
                    <div class="border-2 border-green-200 rounded-2xl p-6 hover:border-green-400 transition-colors cursor-pointer" onclick="selectPaymentMethod('paystack')">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">Online Payment</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Pay securely with your debit card, credit card, or bank transfer through PayStack.</p>
                        <div class="flex items-center text-green-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-sm font-medium">Instant verification</span>
                        </div>
                    </div>

                    <!-- Bank Transfer -->
                    <div class="border-2 border-blue-200 rounded-2xl p-6 hover:border-blue-400 transition-colors cursor-pointer" onclick="selectPaymentMethod('bank_transfer')">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">Bank Transfer</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Transfer directly to our bank account and upload your receipt for verification.</p>
                        <div class="flex items-center text-blue-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm font-medium">Manual verification</span>
                        </div>
                    </div>
                </div>

                <!-- Donation Form -->
                <form id="donationForm" action="/api/v1/donations" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" id="payment_method" name="payment_method" value="paystack">
                    
                    <!-- Personal Information -->
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="donor_name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" id="donor_name" name="donor_name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label for="donor_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="donor_email" name="donor_email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="donor_phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="donor_phone" name="donor_phone" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Donation Amount (₦) *</label>
                            <input type="number" id="amount" name="amount" min="100" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Quick Amount Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Quick Amount Selection</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <button type="button" onclick="setAmount(5000)" class="amount-btn px-4 py-2 border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">₦5,000</button>
                            <button type="button" onclick="setAmount(10000)" class="amount-btn px-4 py-2 border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">₦10,000</button>
                            <button type="button" onclick="setAmount(25000)" class="amount-btn px-4 py-2 border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">₦25,000</button>
                            <button type="button" onclick="setAmount(50000)" class="amount-btn px-4 py-2 border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">₦50,000</button>
                        </div>
                    </div>

                    <!-- Campaign Selection -->
                    @if($campaigns->count() > 0)
                    <div class="mb-6">
                        <label for="campaign_id" class="block text-sm font-medium text-gray-700 mb-2">Support a Specific Campaign (Optional)</label>
                        <select id="campaign_id" name="campaign_id" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <option value="">General Donation</option>
                            @foreach($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->title }} - ₦{{ number_format($campaign->raised_amount) }} / ₦{{ number_format($campaign->goal_amount) }}</option>
                            @endforeach
                        </select>
                    </div>
                    @endif

                    <!-- Donation Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Donation Type</label>
                        <div class="flex gap-4">
                            <label class="flex items-center">
                                <input type="radio" name="donation_type" value="one-time" checked class="mr-2">
                                <span>One-time</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="donation_type" value="recurring" class="mr-2">
                                <span>Monthly Recurring</span>
                            </label>
                        </div>
                    </div>

                    <!-- Purpose -->
                    <div class="mb-6">
                        <label for="purpose" class="block text-sm font-medium text-gray-700 mb-2">Purpose (Optional)</label>
                        <textarea id="purpose" name="purpose" rows="3" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Tell us what inspired you to donate..."></textarea>
                    </div>

                    <!-- Bank Transfer Fields (Hidden by default) -->
                    <div id="bankTransferFields" class="hidden mb-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-4">
                            <h4 class="font-semibold text-blue-800 mb-3">Bank Transfer Details</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Account Name:</span>
                                    <span class="font-medium">{{ $settings['donation_account_name'] ?? 'HALIMAKQ Foundation' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Account Number:</span>
                                    <span class="font-medium">{{ $settings['donation_account_number'] ?? '**********' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-blue-700">Bank Name:</span>
                                    <span class="font-medium">{{ $settings['donation_bank_name'] ?? 'First Bank of Nigeria' }}</span>
                                </div>
                            </div>
                            <p class="text-xs text-blue-600 mt-3">
                                Please use your email address as the transfer reference for easy identification.
                            </p>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="sender_name" class="block text-sm font-medium text-gray-700 mb-2">Sender Name</label>
                                <input type="text" id="sender_name" name="sender_name" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="sender_bank" class="block text-sm font-medium text-gray-700 mb-2">Sender Bank</label>
                                <input type="text" id="sender_bank" name="sender_bank" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label for="receipt_file" class="block text-sm font-medium text-gray-700 mb-2">Upload Receipt</label>
                            <input type="file" id="receipt_file" name="receipt_file" accept="image/*,.pdf" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Upload your bank transfer receipt (JPG, PNG, or PDF)</p>
                        </div>
                    </div>

                    <!-- Anonymous Donation -->
                    <div class="mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_anonymous" value="1" class="mr-2">
                            <span class="text-sm text-gray-700">Make this donation anonymous</span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" id="submitBtn" 
                                class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <span id="btnText">Proceed to Payment</span>
                            <svg id="btnLoader" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Impact Section -->
    <section class="py-16 px-4 bg-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl font-bold mb-12 text-gray-800">Your Impact</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="p-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Education</h3>
                    <p class="text-gray-600">₦10,000 provides school supplies for 5 children for a full academic year.</p>
                </div>
                <div class="p-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Healthcare</h3>
                    <p class="text-gray-600">₦25,000 provides medical care and treatment for 10 families in need.</p>
                </div>
                <div class="p-6">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Community</h3>
                    <p class="text-gray-600">₦50,000 supports community development projects and infrastructure.</p>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
function selectPaymentMethod(method) {
    document.getElementById('payment_method').value = method;
    
    // Update UI
    const paystackCard = document.querySelector('[onclick="selectPaymentMethod(\'paystack\')"]');
    const bankCard = document.querySelector('[onclick="selectPaymentMethod(\'bank_transfer\')"]');
    const bankFields = document.getElementById('bankTransferFields');
    const submitBtn = document.getElementById('btnText');
    
    if (method === 'paystack') {
        paystackCard.classList.add('border-green-400', 'bg-green-50');
        paystackCard.classList.remove('border-green-200');
        bankCard.classList.remove('border-blue-400', 'bg-blue-50');
        bankCard.classList.add('border-blue-200');
        bankFields.classList.add('hidden');
        submitBtn.textContent = 'Proceed to Payment';
    } else {
        bankCard.classList.add('border-blue-400', 'bg-blue-50');
        bankCard.classList.remove('border-blue-200');
        paystackCard.classList.remove('border-green-400', 'bg-green-50');
        paystackCard.classList.add('border-green-200');
        bankFields.classList.remove('hidden');
        submitBtn.textContent = 'Submit Donation';
    }
}

function setAmount(amount) {
    document.getElementById('amount').value = amount;
    
    // Update button styles
    document.querySelectorAll('.amount-btn').forEach(btn => {
        btn.classList.remove('bg-green-100', 'border-green-400', 'text-green-700');
        btn.classList.add('border-gray-300');
    });
    
    event.target.classList.add('bg-green-100', 'border-green-400', 'text-green-700');
    event.target.classList.remove('border-gray-300');
}

// Form submission
document.getElementById('donationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const btnText = document.getElementById('btnText');
    const btnLoader = document.getElementById('btnLoader');
    
    // Show loading state
    submitBtn.disabled = true;
    btnText.classList.add('hidden');
    btnLoader.classList.remove('hidden');
    
    try {
        const formData = new FormData(this);
        const response = await fetch('/api/v1/donations', {
            method: 'POST',
            body: formData,
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            if (result.data.payment_url) {
                // Redirect to PayStack
                window.location.href = result.data.payment_url;
            } else {
                // Bank transfer success
                alert('Donation submitted successfully! Please complete the bank transfer and your donation will be processed after verification.');
                this.reset();
            }
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    } finally {
        // Reset button state
        submitBtn.disabled = false;
        btnText.classList.remove('hidden');
        btnLoader.classList.add('hidden');
    }
});

// Initialize with PayStack selected
selectPaymentMethod('paystack');
</script>
@endsection
