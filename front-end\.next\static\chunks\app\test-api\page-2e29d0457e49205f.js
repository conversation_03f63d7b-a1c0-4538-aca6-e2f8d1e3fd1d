(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[990],{649:(e,s,a)=>{Promise.resolve().then(a.bind(a,8327))},8327:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var t=a(5155),o=a(2115),r=a(1886);function l(){let[e,s]=(0,o.useState)({}),[a,l]=(0,o.useState)(!1),[n,c]=(0,o.useState)(null),[i,d]=(0,o.useState)(null);(0,o.useEffect)(()=>{let e=localStorage.getItem("user"),s=localStorage.getItem("authToken");e&&c(JSON.parse(e)),d(s)},[]);let u=async(e,a)=>{try{console.log("Testing ".concat(e,": ").concat(a)),console.log("Current token:",localStorage.getItem("authToken")),console.log("Current user:",localStorage.getItem("user"));let t=await r.uE.get(a);console.log("".concat(e," response:"),t),s(s=>({...s,[e]:{success:!0,data:t}}))}catch(a){var t;console.error("".concat(e," error:"),a),console.error("".concat(e," error response:"),null===(t=a.response)||void 0===t?void 0:t.data),s(s=>{var t;return{...s,[e]:{success:!1,error:a.message,data:null===(t=a.response)||void 0===t?void 0:t.data}}})}},g=async()=>{l(!0),s({}),await u("User Info","/user"),await u("Public Scholarships","/public-scholarships"),await u("Partner Scholarships Available","/partner-scholarships/available"),await u("Partner Students","/partner-students"),await u("Partner Applications","/partner-scholarships/my-applications"),await u("Partner Statistics","/partner-students/statistics"),await u("Upcoming Events","/events/upcoming"),await u("Programs","/programs"),l(!1)},p=async()=>{try{console.log("Attempting login with:",{email:"<EMAIL>",password:"password123"});let e=await r.uE.post("/login",{email:"<EMAIL>",password:"password123"});console.log("Full login response:",e),e.success&&e.data?(console.log("User data from login:",e.data.user),console.log("User role:",e.data.user.role),console.log("Access token:",e.data.access_token),localStorage.setItem("authToken",e.data.access_token),localStorage.setItem("user",JSON.stringify(e.data.user)),c(e.data.user),d(e.data.access_token),console.log("Login successful - stored in localStorage")):console.error("Login failed - no success or data in response:",e)}catch(e){console.error("Login failed with error:",e)}};return(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"API Testing Page"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Current User Info:"}),(0,t.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:n?JSON.stringify(n,null,2):"No user data found"})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Auth Token:"}),(0,t.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:i||"No token found"}),(0,t.jsx)("div",{className:"mt-2 space-x-2",children:i?(0,t.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("user"),d(null),c(null),console.log("Cleared authentication")},className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Logout"}):(0,t.jsx)("button",{onClick:p,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600",children:"Login as Partner"})})]}),(0,t.jsx)("button",{onClick:g,disabled:a,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6",children:a?"Testing...":"Test All Endpoints"}),(0,t.jsx)("div",{className:"space-y-4",children:Object.entries(e).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"border rounded p-4",children:[(0,t.jsxs)("h3",{className:"font-semibold mb-2 flex items-center",children:[s,(0,t.jsx)("span",{className:"ml-2 px-2 py-1 rounded text-xs ".concat(a.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:a.success?"SUCCESS":"ERROR"})]}),(0,t.jsx)("pre",{className:"bg-gray-50 p-3 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(a.success?a.data:a.error,null,2)})]},s)})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[886,441,684,358],()=>s(649)),_N_E=e.O()}]);