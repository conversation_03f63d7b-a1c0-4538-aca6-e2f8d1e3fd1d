<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Volunteer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Volunteers",
 *     description="Volunteer application and management endpoints"
 * )
 */
class VolunteerController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/volunteer/apply",
     *     summary="Submit volunteer application",
     *     tags={"Volunteers"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"skills","interests","availability","experience","motivation"},
     *             @OA\Property(property="skills", type="array", @OA\Items(type="string"), example={"Teaching", "Event Management", "Social Media"}),
     *             @OA\Property(property="interests", type="array", @OA\Items(type="string"), example={"Education", "Community Development", "Youth Programs"}),
     *             @OA\Property(property="availability", type="object", example={"weekdays": ["Monday", "Wednesday", "Friday"], "weekends": true, "time_slots": ["Morning", "Evening"]}),
     *             @OA\Property(property="experience", type="string", example="I have 2 years of experience working with children..."),
     *             @OA\Property(property="motivation", type="string", example="I want to give back to my community..."),
     *             @OA\Property(property="emergency_contact", type="object", example={"name": "John Doe", "relationship": "Father", "phone": "+234800000000"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Application submitted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Volunteer application submitted successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Already applied or already a volunteer"
     *     )
     * )
     */
    public function apply(Request $request): JsonResponse
    {
        $user = Auth::user();

        // Check if user already has a volunteer application
        $existingVolunteer = $user->volunteer;
        if ($existingVolunteer) {
            return response()->json([
                'success' => false,
                'message' => 'You have already submitted a volunteer application'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'skills' => 'required|array|min:1',
            'skills.*' => 'string|max:100',
            'interests' => 'required|array|min:1',
            'interests.*' => 'string|max:100',
            'availability' => 'required|array',
            'availability.weekdays' => 'array',
            'availability.weekends' => 'boolean',
            'availability.time_slots' => 'array',
            'experience' => 'required|string|max:1000',
            'motivation' => 'required|string|max:1000',
            'emergency_contact' => 'required|array',
            'emergency_contact.name' => 'required|string|max:100',
            'emergency_contact.relationship' => 'required|string|max:50',
            'emergency_contact.phone' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $volunteer = Volunteer::create([
            'user_id' => $user->id,
            'skills' => $request->skills,
            'interests' => $request->interests,
            'availability' => $request->availability,
            'experience' => $request->experience,
            'motivation' => $request->motivation,
            'emergency_contact' => $request->emergency_contact,
            'application_status' => 'pending',
        ]);

        // Update user role to volunteer
        $user->update(['role' => 'volunteer']);

        return response()->json([
            'success' => true,
            'message' => 'Volunteer application submitted successfully. We will review your application and get back to you soon.',
            'data' => $volunteer
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/volunteer/application",
     *     summary="Get user's volunteer application",
     *     tags={"Volunteers"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Application retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="No application found"
     *     )
     * )
     */
    public function getApplication(): JsonResponse
    {
        $user = Auth::user();
        $volunteer = $user->volunteer;

        if (!$volunteer) {
            return response()->json([
                'success' => false,
                'message' => 'No volunteer application found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $volunteer
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/volunteer/application",
     *     summary="Update volunteer application",
     *     tags={"Volunteers"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="skills", type="array", @OA\Items(type="string")),
     *             @OA\Property(property="interests", type="array", @OA\Items(type="string")),
     *             @OA\Property(property="availability", type="object"),
     *             @OA\Property(property="experience", type="string"),
     *             @OA\Property(property="motivation", type="string"),
     *             @OA\Property(property="emergency_contact", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Application updated successfully"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Cannot update approved application"
     *     )
     * )
     */
    public function updateApplication(Request $request): JsonResponse
    {
        $user = Auth::user();
        $volunteer = $user->volunteer;

        if (!$volunteer) {
            return response()->json([
                'success' => false,
                'message' => 'No volunteer application found'
            ], 404);
        }

        // Don't allow updates to approved applications
        if ($volunteer->isApproved()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update an approved application'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'skills' => 'array|min:1',
            'skills.*' => 'string|max:100',
            'interests' => 'array|min:1',
            'interests.*' => 'string|max:100',
            'availability' => 'array',
            'experience' => 'string|max:1000',
            'motivation' => 'string|max:1000',
            'emergency_contact' => 'array',
            'emergency_contact.name' => 'string|max:100',
            'emergency_contact.relationship' => 'string|max:50',
            'emergency_contact.phone' => 'string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $volunteer->update($request->only([
            'skills', 'interests', 'availability', 'experience', 'motivation', 'emergency_contact'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Application updated successfully',
            'data' => $volunteer->fresh()
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/volunteer/hours",
     *     summary="Log volunteer hours",
     *     tags={"Volunteers"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"hours","date","activity"},
     *             @OA\Property(property="hours", type="number", format="float", example=4.5),
     *             @OA\Property(property="date", type="string", format="date", example="2024-01-15"),
     *             @OA\Property(property="activity", type="string", example="Teaching at community center"),
     *             @OA\Property(property="notes", type="string", example="Taught math to 15 children")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Hours logged successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Volunteer hours logged successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Not an approved volunteer"
     *     )
     * )
     */
    public function logHours(Request $request): JsonResponse
    {
        $user = Auth::user();
        $volunteer = $user->volunteer;

        if (!$volunteer || !$volunteer->isApproved()) {
            return response()->json([
                'success' => false,
                'message' => 'You must be an approved volunteer to log hours'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'hours' => 'required|numeric|min:0.5|max:24',
            'date' => 'required|date|before_or_equal:today',
            'activity' => 'required|string|max:200',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Add hours to volunteer record
        $volunteer->addHours($request->hours);

        // TODO: Create a separate VolunteerHours model to track individual entries
        // For now, just update the total

        return response()->json([
            'success' => true,
            'message' => 'Volunteer hours logged successfully',
            'data' => [
                'hours_logged' => $request->hours,
                'date' => $request->date,
                'activity' => $request->activity,
                'notes' => $request->notes,
                'total_hours' => $volunteer->fresh()->hours_logged,
            ]
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/volunteer/hours",
     *     summary="Get volunteer hours summary",
     *     tags={"Volunteers"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Hours summary retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function getHours(): JsonResponse
    {
        $user = Auth::user();
        $volunteer = $user->volunteer;

        if (!$volunteer) {
            return response()->json([
                'success' => false,
                'message' => 'No volunteer record found'
            ], 404);
        }

        $data = [
            'total_hours' => $volunteer->hours_logged,
            'badges_earned' => $this->calculateBadges($volunteer->hours_logged),
            'volunteer_since' => $volunteer->approved_at?->format('F j, Y'),
            'status' => $volunteer->application_status,
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get volunteer opportunities
     * 
     * @OA\Get(
     *     path="/api/v1/volunteer/opportunities",
     *     summary="Get volunteer opportunities",
     *     tags={"Volunteers"},
     *     @OA\Parameter(
     *         name="skills",
     *         in="query",
     *         description="Filter by skills (comma-separated)",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Opportunities retrieved successfully"
     *     )
     * )
     */
    public function opportunities(Request $request): JsonResponse
    {
        // For now, return mock data
        // In a full implementation, this would come from a volunteer_opportunities table
        
        $opportunities = [
            [
                'id' => 1,
                'title' => 'Community Teaching Assistant',
                'description' => 'Help teach basic literacy and numeracy to community members',
                'skills_required' => ['Teaching', 'Patience', 'Communication'],
                'time_commitment' => '4 hours per week',
                'location' => 'Lagos Community Center',
                'status' => 'open'
            ],
            [
                'id' => 2,
                'title' => 'Event Coordination Helper',
                'description' => 'Assist with organizing and managing foundation events',
                'skills_required' => ['Event Management', 'Organization', 'Leadership'],
                'time_commitment' => '6 hours per event',
                'location' => 'Various locations',
                'status' => 'open'
            ],
            [
                'id' => 3,
                'title' => 'Social Media Content Creator',
                'description' => 'Create engaging content for foundation social media platforms',
                'skills_required' => ['Social Media', 'Content Creation', 'Design'],
                'time_commitment' => '3 hours per week',
                'location' => 'Remote',
                'status' => 'open'
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $opportunities
        ]);
    }

    /**
     * Get skill-matched volunteer opportunities
     */
    public function getMatchedOpportunities(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $volunteer = $user->volunteer;

            if (!$volunteer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Volunteer profile not found'
                ], 404);
            }

            $query = VolunteerOpportunity::where('is_active', true)
                ->where('application_deadline', '>', now());

            // Skill-based matching
            if ($volunteer->skills) {
                $userSkills = $volunteer->skills;
                $query->orderByRaw("
                    CASE
                        WHEN JSON_OVERLAPS(required_skills, ?) THEN 1
                        ELSE 2
                    END, created_at DESC
                ", [json_encode($userSkills)]);
            }

            $perPage = $request->get('per_page', 10);
            $opportunities = $query->paginate($perPage);

            // Calculate skill match percentage
            foreach ($opportunities as $opportunity) {
                if ($volunteer->skills && $opportunity->required_skills) {
                    $opportunity->skill_match_percentage = $this->calculateSkillMatch(
                        $volunteer->skills,
                        $opportunity->required_skills
                    );
                } else {
                    $opportunity->skill_match_percentage = 0;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $opportunities
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve opportunities',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get volunteer training modules
     */
    public function getTrainingModules(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $volunteer = $user->volunteer;

            if (!$volunteer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Volunteer profile not found'
                ], 404);
            }

            $query = VolunteerTrainingModule::where('is_active', true);

            // Filter by category
            if ($request->has('category')) {
                $query->where('category', $request->category);
            }

            $modules = $query->orderBy('order', 'asc')->get();

            // Add completion status for each module
            foreach ($modules as $module) {
                $progress = VolunteerTrainingProgress::where('volunteer_id', $volunteer->id)
                    ->where('training_module_id', $module->id)
                    ->first();

                $module->is_completed = $progress ? $progress->is_completed : false;
                $module->completion_date = $progress ? $progress->completed_at : null;
                $module->progress_percentage = $progress ? $progress->progress_percentage : 0;
            }

            return response()->json([
                'success' => true,
                'data' => $modules
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve training modules',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update training progress
     */
    public function updateTrainingProgress(Request $request, $moduleId): JsonResponse
    {
        try {
            $user = Auth::user();
            $volunteer = $user->volunteer;

            if (!$volunteer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Volunteer profile not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'progress_percentage' => 'required|numeric|min:0|max:100',
                'is_completed' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $module = VolunteerTrainingModule::findOrFail($moduleId);

            $progress = VolunteerTrainingProgress::updateOrCreate(
                [
                    'volunteer_id' => $volunteer->id,
                    'training_module_id' => $module->id
                ],
                [
                    'progress_percentage' => $request->progress_percentage,
                    'is_completed' => $request->progress_percentage >= 100 || $request->is_completed,
                    'completed_at' => $request->progress_percentage >= 100 ? now() : null
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Training progress updated successfully',
                'data' => $progress
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update training progress',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get volunteer analytics and impact
     */
    public function getAnalytics(): JsonResponse
    {
        try {
            $user = Auth::user();
            $volunteer = $user->volunteer;

            if (!$volunteer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Volunteer profile not found'
                ], 404);
            }

            $analytics = [
                'total_hours' => $volunteer->hours_logged,
                'hours_this_month' => VolunteerHourLog::where('volunteer_id', $volunteer->id)
                    ->whereMonth('activity_date', now()->month)
                    ->whereYear('activity_date', now()->year)
                    ->sum('hours'),
                'volunteer_since' => $volunteer->approved_at?->format('F j, Y'),
                'badges_earned' => $this->calculateBadges($volunteer->hours_logged),
                'training_completed' => VolunteerTrainingProgress::where('volunteer_id', $volunteer->id)
                    ->where('is_completed', true)
                    ->count(),
                'opportunities_applied' => VolunteerOpportunityApplication::where('volunteer_id', $volunteer->id)
                    ->count(),
                'impact_score' => $this->calculateImpactScore($volunteer)
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate skill match percentage
     */
    private function calculateSkillMatch(array $userSkills, array $requiredSkills): int
    {
        if (empty($requiredSkills)) {
            return 0;
        }

        $matchingSkills = array_intersect($userSkills, $requiredSkills);
        return round((count($matchingSkills) / count($requiredSkills)) * 100);
    }

    /**
     * Calculate impact score based on various factors
     */
    private function calculateImpactScore($volunteer): int
    {
        $score = 0;

        // Hours contribution (max 40 points)
        $score += min(40, $volunteer->hours_logged * 0.2);

        // Training completion (max 30 points)
        $completedTraining = VolunteerTrainingProgress::where('volunteer_id', $volunteer->id)
            ->where('is_completed', true)
            ->count();
        $score += min(30, $completedTraining * 5);

        // Consistency (max 30 points) - based on regular activity
        $monthsActive = VolunteerHourLog::where('volunteer_id', $volunteer->id)
            ->selectRaw('COUNT(DISTINCT YEAR(activity_date), MONTH(activity_date)) as months')
            ->value('months') ?? 0;
        $score += min(30, $monthsActive * 3);

        return min(100, round($score));
    }

    /**
     * Calculate volunteer badges based on hours logged
     */
    private function calculateBadges(float $hours): array
    {
        $badges = [];

        if ($hours >= 10) $badges[] = 'Getting Started';
        if ($hours >= 25) $badges[] = 'Committed Helper';
        if ($hours >= 50) $badges[] = 'Community Champion';
        if ($hours >= 100) $badges[] = 'Dedicated Volunteer';
        if ($hours >= 200) $badges[] = 'Community Hero';
        if ($hours >= 500) $badges[] = 'Volunteer Legend';

        return $badges;
    }
}
