<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PartnerStudent;
use App\Models\ScholarshipApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PartnerReportController extends Controller
{
    /**
     * Display the reports and analytics dashboard.
     */
    public function index(Request $request)
    {
        $partner = Auth::user();
        
        // Ensure user is a partner organization
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get date range from request or default to last 12 months
        $startDate = $request->get('start_date', Carbon::now()->subMonths(12)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        // Convert to Carbon instances
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // Get comprehensive analytics data
        $analytics = $this->getAnalyticsData($partner, $startDate, $endDate);

        return view('partner.reports.index', compact('partner', 'analytics', 'startDate', 'endDate'));
    }

    /**
     * Export reports data.
     */
    public function export(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $format = $request->get('format', 'csv');
        $reportType = $request->get('type', 'overview');

        // Get date range
        $startDate = Carbon::parse($request->get('start_date', Carbon::now()->subMonths(12)->format('Y-m-d')));
        $endDate = Carbon::parse($request->get('end_date', Carbon::now()->format('Y-m-d')));

        switch ($reportType) {
            case 'students':
                return $this->exportStudentsReport($partner, $startDate, $endDate, $format);
            case 'applications':
                return $this->exportApplicationsReport($partner, $startDate, $endDate, $format);
            case 'overview':
            default:
                return $this->exportOverviewReport($partner, $startDate, $endDate, $format);
        }
    }

    /**
     * Get comprehensive analytics data.
     */
    private function getAnalyticsData($partner, $startDate, $endDate)
    {
        // Basic statistics
        $totalStudents = $partner->partnerStudents()->count();
        $activeStudents = $partner->partnerStudents()->where('status', 'active')->count();
        $totalApplications = ScholarshipApplication::where('school_id', $partner->id)->count();
        $approvedApplications = ScholarshipApplication::where('school_id', $partner->id)->where('status', 'approved')->count();

        // Success rate
        $successRate = $totalApplications > 0 ? round(($approvedApplications / $totalApplications) * 100, 1) : 0;

        // Monthly application trends
        $monthlyApplications = ScholarshipApplication::where('school_id', $partner->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'count' => $item->count
                ];
            });

        // Application status distribution
        $applicationsByStatus = ScholarshipApplication::where('school_id', $partner->id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        // Student grade distribution
        $studentsByGrade = $partner->partnerStudents()
            ->selectRaw('class, COUNT(*) as count')
            ->groupBy('class')
            ->orderBy('class')
            ->get()
            ->pluck('count', 'class')
            ->toArray();

        // Top performing students (by approved applications)
        $topStudents = $partner->partnerStudents()
            ->withCount(['scholarshipApplications as approved_applications' => function ($query) {
                $query->where('status', 'approved');
            }])
            ->having('approved_applications', '>', 0)
            ->orderBy('approved_applications', 'desc')
            ->limit(10)
            ->get();

        // Recent activities
        $recentApplications = ScholarshipApplication::where('school_id', $partner->id)
            ->with(['student', 'scholarship'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Scholarship categories performance
        $scholarshipPerformance = ScholarshipApplication::where('school_id', $partner->id)
            ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
            ->selectRaw('scholarships.category, 
                        COUNT(*) as total_applications,
                        SUM(CASE WHEN scholarship_applications.status = "approved" THEN 1 ELSE 0 END) as approved_applications')
            ->groupBy('scholarships.category')
            ->get()
            ->map(function ($item) {
                $successRate = $item->total_applications > 0 ? 
                    round(($item->approved_applications / $item->total_applications) * 100, 1) : 0;
                return [
                    'category' => ucfirst($item->category),
                    'total_applications' => $item->total_applications,
                    'approved_applications' => $item->approved_applications,
                    'success_rate' => $successRate
                ];
            });

        // Monthly student registrations
        $monthlyStudents = $partner->partnerStudents()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'count' => $item->count
                ];
            });

        return [
            'overview' => [
                'total_students' => $totalStudents,
                'active_students' => $activeStudents,
                'total_applications' => $totalApplications,
                'approved_applications' => $approvedApplications,
                'success_rate' => $successRate,
                'pending_applications' => ScholarshipApplication::where('school_id', $partner->id)->where('status', 'pending')->count(),
                'rejected_applications' => ScholarshipApplication::where('school_id', $partner->id)->where('status', 'rejected')->count(),
            ],
            'charts' => [
                'monthly_applications' => $monthlyApplications,
                'applications_by_status' => $applicationsByStatus,
                'students_by_grade' => $studentsByGrade,
                'monthly_students' => $monthlyStudents,
            ],
            'performance' => [
                'scholarship_categories' => $scholarshipPerformance,
                'top_students' => $topStudents,
            ],
            'recent_activities' => $recentApplications,
        ];
    }

    /**
     * Export overview report.
     */
    private function exportOverviewReport($partner, $startDate, $endDate, $format)
    {
        $analytics = $this->getAnalyticsData($partner, $startDate, $endDate);
        
        $filename = "overview_report_{$partner->organization_name}_{$startDate->format('Y-m-d')}_to_{$endDate->format('Y-m-d')}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($analytics) {
            $file = fopen('php://output', 'w');
            
            // Overview section
            fputcsv($file, ['ORGANIZATION OVERVIEW REPORT']);
            fputcsv($file, ['Generated on', Carbon::now()->format('Y-m-d H:i:s')]);
            fputcsv($file, []);
            
            fputcsv($file, ['SUMMARY STATISTICS']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Total Students', $analytics['overview']['total_students']]);
            fputcsv($file, ['Active Students', $analytics['overview']['active_students']]);
            fputcsv($file, ['Total Applications', $analytics['overview']['total_applications']]);
            fputcsv($file, ['Approved Applications', $analytics['overview']['approved_applications']]);
            fputcsv($file, ['Success Rate', $analytics['overview']['success_rate'] . '%']);
            fputcsv($file, []);
            
            // Applications by status
            fputcsv($file, ['APPLICATIONS BY STATUS']);
            fputcsv($file, ['Status', 'Count']);
            foreach ($analytics['charts']['applications_by_status'] as $status => $count) {
                fputcsv($file, [ucfirst($status), $count]);
            }
            fputcsv($file, []);
            
            // Students by grade
            fputcsv($file, ['STUDENTS BY GRADE']);
            fputcsv($file, ['Grade', 'Count']);
            foreach ($analytics['charts']['students_by_grade'] as $grade => $count) {
                fputcsv($file, [$grade, $count]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export students report.
     */
    private function exportStudentsReport($partner, $startDate, $endDate, $format)
    {
        $students = $partner->partnerStudents()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['scholarshipApplications'])
            ->get();
        
        $filename = "students_report_{$partner->organization_name}_{$startDate->format('Y-m-d')}_to_{$endDate->format('Y-m-d')}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($students) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, ['STUDENTS REPORT']);
            fputcsv($file, ['Generated on', Carbon::now()->format('Y-m-d H:i:s')]);
            fputcsv($file, []);
            
            fputcsv($file, [
                'Student ID', 'Full Name', 'Current Grade', 'Status', 
                'Date of Birth', 'Gender', 'Total Applications', 
                'Approved Applications', 'Registration Date'
            ]);
            
            foreach ($students as $student) {
                fputcsv($file, [
                    $student->student_id,
                    $student->full_name,
                    $student->current_grade,
                    $student->status,
                    $student->date_of_birth,
                    $student->gender,
                    $student->scholarshipApplications->count(),
                    $student->scholarshipApplications->where('status', 'approved')->count(),
                    $student->created_at->format('Y-m-d')
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export applications report.
     */
    private function exportApplicationsReport($partner, $startDate, $endDate, $format)
    {
        $applications = ScholarshipApplication::where('school_id', $partner->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['student', 'scholarship'])
            ->get();
        
        $filename = "applications_report_{$partner->organization_name}_{$startDate->format('Y-m-d')}_to_{$endDate->format('Y-m-d')}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($applications) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, ['APPLICATIONS REPORT']);
            fputcsv($file, ['Generated on', Carbon::now()->format('Y-m-d H:i:s')]);
            fputcsv($file, []);
            
            fputcsv($file, [
                'Application ID', 'Student Name', 'Scholarship Title', 
                'Category', 'Status', 'Application Date', 'Amount'
            ]);
            
            foreach ($applications as $application) {
                fputcsv($file, [
                    $application->application_id,
                    $application->student->full_name ?? 'N/A',
                    $application->scholarship->title ?? 'N/A',
                    $application->scholarship->category ?? 'N/A',
                    ucfirst($application->status),
                    $application->created_at->format('Y-m-d'),
                    $application->scholarship->amount ?? 'N/A'
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
