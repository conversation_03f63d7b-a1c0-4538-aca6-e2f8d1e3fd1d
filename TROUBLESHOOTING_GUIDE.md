# 🔧 HLTKKQ Foundation - Troubleshooting Guide

## Common Issues and Solutions

### Backend Issues

#### 1. Laravel Application Issues

**Problem: Application Key Not Set**
```
Error: No application encryption key has been specified.
```
**Solution:**
```bash
php artisan key:generate
```

**Problem: Database Connection Failed**
```
Error: SQLSTATE[HY000] [2002] Connection refused
```
**Solution:**
```bash
# Check MySQL is running
sudo systemctl status mysql

# Check database credentials in .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=your_password

# Test connection
php artisan tinker
>>> DB::connection()->getPdo();
```

**Problem: Migration Failed**
```
Error: Class 'CreateUsersTable' not found
```
**Solution:**
```bash
composer dump-autoload
php artisan migrate:fresh
```

**Problem: Storage Symlink Issues**
```
Error: The file could not be found
```
**Solution:**
```bash
php artisan storage:link
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

#### 2. Authentication Issues

**Problem: CORS Errors**
```
Error: Access to fetch blocked by CORS policy
```
**Solution:**
```php
// config/cors.php
'allowed_origins' => ['http://localhost:3000'],
'allowed_methods' => ['*'],
'allowed_headers' => ['*'],
```

**Problem: Sanctum Token Not Working**
```
Error: Unauthenticated
```
**Solution:**
```php
// config/sanctum.php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', 'localhost:3000')),

// .env
SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000
```

### Frontend Issues

#### 1. Next.js Application Issues

**Problem: Module Not Found**
```
Error: Module not found: Can't resolve '@/components/ui/button'
```
**Solution:**
```bash
# Check tsconfig.json paths
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

**Problem: Hydration Mismatch**
```
Error: Text content does not match server-rendered HTML
```
**Solution:**
```tsx
// Add suppressHydrationWarning for client-only content
<div suppressHydrationWarning>
  {typeof window !== 'undefined' && content}
</div>
```

**Problem: API Connection Failed**
```
Error: fetch failed
```
**Solution:**
```javascript
// Check .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000

// Check API client configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
```

#### 2. Authentication State Issues

**Problem: User State Not Persisting**
```
Error: User logged out on page refresh
```
**Solution:**
```typescript
// Check localStorage token
const token = localStorage.getItem('authToken')
if (token) {
  // Verify token with backend
  await apiClient.checkAuth()
}
```

**Problem: Infinite Authentication Loop**
```
Error: useEffect keeps calling authentication
```
**Solution:**
```typescript
useEffect(() => {
  checkAuth()
}, []) // Add empty dependency array
```

### Database Issues

#### 1. Migration Problems

**Problem: Migration Rollback Failed**
```
Error: Nothing to rollback
```
**Solution:**
```bash
# Check migration status
php artisan migrate:status

# Force rollback
php artisan migrate:rollback --force

# Fresh migration
php artisan migrate:fresh --seed
```

**Problem: Foreign Key Constraint**
```
Error: Cannot add foreign key constraint
```
**Solution:**
```php
// Ensure parent table exists first
Schema::create('parent_table', function (Blueprint $table) {
    $table->id();
});

Schema::create('child_table', function (Blueprint $table) {
    $table->id();
    $table->foreignId('parent_id')->constrained();
});
```

#### 2. Seeder Issues

**Problem: Seeder Class Not Found**
```
Error: Class 'UserSeeder' not found
```
**Solution:**
```bash
composer dump-autoload
php artisan db:seed --class=UserSeeder
```

### Performance Issues

#### 1. Slow API Responses

**Problem: Database Queries Taking Too Long**
**Solution:**
```php
// Add database indexes
Schema::table('users', function (Blueprint $table) {
    $table->index('email');
    $table->index('created_at');
});

// Use eager loading
$posts = BlogPost::with(['author', 'category'])->get();
```

**Problem: Large Response Payload**
**Solution:**
```php
// Use API Resources
class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->full_name,
            // Only return needed fields
        ];
    }
}
```

#### 2. Frontend Performance

**Problem: Large Bundle Size**
**Solution:**
```typescript
// Use dynamic imports
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>
})

// Code splitting by route
```

**Problem: Slow Image Loading**
**Solution:**
```tsx
import Image from 'next/image'

<Image
  src="/image.jpg"
  alt="Description"
  width={800}
  height={600}
  priority // For above-the-fold images
/>
```

### Environment Issues

#### 1. Development Environment

**Problem: Port Already in Use**
```
Error: Port 3000 is already in use
```
**Solution:**
```bash
# Kill process on port
lsof -ti:3000 | xargs kill -9

# Use different port
npm run dev -- -p 3001
```

**Problem: Permission Denied**
```
Error: EACCES: permission denied
```
**Solution:**
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules

# Laravel permissions
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
```

#### 2. Production Environment

**Problem: 500 Internal Server Error**
**Solution:**
```bash
# Check Laravel logs
tail -f storage/logs/laravel.log

# Check web server logs
tail -f /var/log/nginx/error.log

# Check PHP-FPM logs
tail -f /var/log/php8.2-fpm.log
```

**Problem: Assets Not Loading**
**Solution:**
```bash
# Clear caches
php artisan optimize:clear

# Rebuild assets
npm run build

# Check file permissions
chmod -R 755 public/
```

### Security Issues

#### 1. Authentication Vulnerabilities

**Problem: Token Exposed in URL**
**Solution:**
```javascript
// Use headers instead of query parameters
headers: {
  'Authorization': `Bearer ${token}`
}
```

**Problem: CSRF Token Mismatch**
**Solution:**
```php
// Ensure CSRF middleware is applied
Route::middleware(['web'])->group(function () {
    // Routes that need CSRF protection
});
```

### Debugging Tools

#### 1. Laravel Debugging

```bash
# Enable query logging
DB::enableQueryLog();
// Your code here
dd(DB::getQueryLog());

# Use Tinker for testing
php artisan tinker
>>> User::first()
>>> DB::table('users')->count()
```

#### 2. Frontend Debugging

```javascript
// Debug API calls
console.log('API Request:', endpoint, options)
console.log('API Response:', data)

// React DevTools
// Install React Developer Tools browser extension

// Debug state changes
useEffect(() => {
  console.log('State changed:', state)
}, [state])
```

### Testing Issues

#### 1. Backend Tests Failing

**Problem: Database Not Found in Tests**
**Solution:**
```php
// phpunit.xml
<env name="DB_DATABASE" value="hltkkq_testing"/>

// Create test database
CREATE DATABASE hltkkq_testing;
```

**Problem: Authentication in Tests**
**Solution:**
```php
// Use Sanctum in tests
use Laravel\Sanctum\Sanctum;

public function test_authenticated_route()
{
    Sanctum::actingAs(User::factory()->create());
    
    $response = $this->getJson('/api/v1/profile');
    $response->assertStatus(200);
}
```

#### 2. Frontend Tests Failing

**Problem: Component Not Rendering**
**Solution:**
```javascript
// Mock API calls
jest.mock('@/lib/api', () => ({
  apiClient: {
    getData: jest.fn().mockResolvedValue({ success: true, data: [] })
  }
}))
```

### Quick Fix Commands

```bash
# Laravel
php artisan optimize:clear     # Clear all caches
php artisan migrate:fresh --seed  # Reset database
composer dump-autoload        # Regenerate autoloader

# Next.js
rm -rf .next                  # Clear Next.js cache
rm -rf node_modules package-lock.json  # Clean install
npm install

# Permissions
sudo chown -R $(whoami) .     # Fix ownership
chmod -R 755 storage/         # Fix Laravel storage
chmod -R 755 bootstrap/cache/ # Fix Laravel cache

# Logs
tail -f storage/logs/laravel.log      # Laravel logs
tail -f /var/log/nginx/error.log      # Nginx logs
```

### Emergency Recovery

#### Complete Reset (Development Only)
```bash
# Backend reset
php artisan migrate:fresh --seed
php artisan optimize:clear
composer install

# Frontend reset
rm -rf node_modules package-lock.json .next
npm install
npm run build

# Database reset
DROP DATABASE hltkkq_foundation;
CREATE DATABASE hltkkq_foundation;
php artisan migrate:fresh --seed
```

---

## Getting Help

1. **Check this troubleshooting guide first**
2. **Search existing issues in repository**
3. **Check Laravel and Next.js documentation**
4. **Create detailed issue report with:**
   - Error message
   - Steps to reproduce
   - Environment details
   - Screenshots if applicable

---

> **🔧 Remember: Most issues have simple solutions. Don't hesitate to ask for help when needed!** 