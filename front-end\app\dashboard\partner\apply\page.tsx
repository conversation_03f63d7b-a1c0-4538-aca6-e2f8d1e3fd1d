"use client"

import React, { useState, useEffect } from 'react'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { apiClient } from '@/lib/api'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { ArrowLeft, Upload, FileText, User, GraduationCap, Calendar, Award } from "lucide-react"
import Link from "next/link"

interface Student {
  id: number
  full_name: string
  age: number
  grade_level: string
  date_of_birth: string
  parent_name: string
  parent_phone: string
  parent_email: string
  home_address: string
}

interface Scholarship {
  id: number
  title: string
  description: string
  category: 'primary' | 'secondary'
  amount: number
  application_deadline: string
  requirements: string[]
}

interface ApplicationFormData {
  student_id: string
  student_full_name: string
  student_age: string
  student_class: string
  student_date_of_birth: string
  parent_name: string
  parent_phone: string
  parent_email: string
  home_address: string
  reason_for_scholarship: string
  grade_level: string
  academic_year: string
}

export default function PartnerScholarshipApplication() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const [scholarship, setScholarship] = useState<Scholarship | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [files, setFiles] = useState<{[key: string]: File | null}>({})

  const [formData, setFormData] = useState<ApplicationFormData>({
    student_id: '',
    student_full_name: '',
    student_age: '',
    student_class: '',
    student_date_of_birth: '',
    parent_name: '',
    parent_phone: '',
    parent_email: '',
    home_address: '',
    reason_for_scholarship: '',
    grade_level: '',
    academic_year: '2024/2025'
  })

  useEffect(() => {
    const scholarshipId = searchParams.get('scholarship_id')
    const studentId = searchParams.get('student_id')

    if (!scholarshipId) {
      toast({
        title: "Error",
        description: "No scholarship selected",
        variant: "destructive"
      })
      router.push('/dashboard/partner')
      return
    }

    fetchData(scholarshipId, studentId)
  }, [searchParams])

  const fetchData = async (scholarshipId: string, studentId?: string | null) => {
    try {
      setLoading(true)

      // Fetch scholarship details
      const scholarshipResponse = await apiClient.getPartnerScholarshipDetails(scholarshipId)
      if (scholarshipResponse.success) {
        setScholarship(scholarshipResponse.data)
      }

      // Fetch partner students
      const studentsResponse = await apiClient.getPartnerStudents()
      if (studentsResponse.success) {
        const studentsData = studentsResponse.data?.data || studentsResponse.data || []
        setStudents(Array.isArray(studentsData) ? studentsData : [])

        // Pre-select student if provided
        if (studentId) {
          const student = studentsData.find((s: Student) => s.id.toString() === studentId)
          if (student) {
            setSelectedStudent(student)
            populateFormWithStudent(student)
          }
        }
      }

      setLoading(false)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to load application data",
        variant: "destructive"
      })
      setLoading(false)
    }
  }

  const populateFormWithStudent = (student: Student) => {
    setFormData({
      ...formData,
      student_id: student.id.toString(),
      student_full_name: student.full_name,
      student_age: student.age.toString(),
      student_date_of_birth: student.date_of_birth,
      parent_name: student.parent_name,
      parent_phone: student.parent_phone,
      parent_email: student.parent_email,
      home_address: student.home_address,
      grade_level: student.grade_level
    })
  }

  const handleStudentSelect = (studentId: string) => {
    const student = students.find(s => s.id.toString() === studentId)
    if (student) {
      setSelectedStudent(student)
      populateFormWithStudent(student)
    }
  }

  const handleInputChange = (field: keyof ApplicationFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFileChange = (fieldName: string, file: File | null) => {
    setFiles(prev => ({
      ...prev,
      [fieldName]: file
    }))
  }

  const validateForm = (): boolean => {
    const required = [
      'student_id', 'student_full_name', 'student_age', 'student_class',
      'student_date_of_birth', 'parent_name', 'parent_phone', 'parent_email',
      'home_address', 'reason_for_scholarship', 'grade_level'
    ]

    for (const field of required) {
      if (!formData[field as keyof ApplicationFormData]?.trim()) {
        toast({
          title: "Validation Error",
          description: `Please fill in ${field.replace('_', ' ')}`,
          variant: "destructive"
        })
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !scholarship) return

    try {
      setSubmitting(true)

      const applicationData = new FormData()
      
      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        applicationData.append(key, value)
      })

      // Add files
      Object.entries(files).forEach(([key, file]) => {
        if (file) {
          applicationData.append(key, file)
        }
      })

      const response = await apiClient.applyForStudentScholarship(scholarship.id.toString(), applicationData)

      if (response.success) {
        toast({
          title: "Success",
          description: "Scholarship application submitted successfully",
        })
        router.push('/dashboard/partner/applications')
      } else {
        throw new Error(response.message || 'Failed to submit application')
      }

    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to submit application",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading application form...</p>
        </div>
      </div>
    )
  }

  if (!scholarship) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Scholarship Not Found</h3>
            <p className="text-gray-600 mb-4">The requested scholarship could not be found.</p>
            <Button asChild>
              <Link href="/dashboard/partner">Back to Dashboard</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/dashboard/partner">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </Button>
          
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Apply for Scholarship</h1>
                <h2 className="text-xl text-blue-600 mb-2">{scholarship.title}</h2>
                <p className="text-gray-600 mb-4">{scholarship.description}</p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">
                    <GraduationCap className="h-3 w-3 mr-1" />
                    {scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'}
                  </Badge>
                  <Badge variant="outline">
                    <Award className="h-3 w-3 mr-1" />
                    ₦{scholarship.amount?.toLocaleString()}
                  </Badge>
                  <Badge variant="outline">
                    <Calendar className="h-3 w-3 mr-1" />
                    Deadline: {new Date(scholarship.application_deadline).toLocaleDateString()}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Application Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Student Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Select Student
              </CardTitle>
              <CardDescription>
                Choose the student you want to apply for this scholarship
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="student_select">Student *</Label>
                  <Select value={formData.student_id} onValueChange={handleStudentSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a student" />
                    </SelectTrigger>
                    <SelectContent>
                      {students.map((student) => (
                        <SelectItem key={student.id} value={student.id.toString()}>
                          {student.full_name} - Grade {student.grade_level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {students.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-gray-500 mb-2">No students found</p>
                    <Button asChild variant="outline">
                      <Link href="/dashboard/partner/students/add">Add New Student</Link>
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Student Information Form - Only show when student is selected */}
          {selectedStudent && (
            <Card>
              <CardHeader>
                <CardTitle>Student Information</CardTitle>
                <CardDescription>
                  Review and update student information for this application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="student_full_name">Full Name *</Label>
                    <Input
                      id="student_full_name"
                      value={formData.student_full_name}
                      onChange={(e) => handleInputChange('student_full_name', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="student_age">Age *</Label>
                    <Input
                      id="student_age"
                      type="number"
                      value={formData.student_age}
                      onChange={(e) => handleInputChange('student_age', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="student_class">Current Class *</Label>
                    <Input
                      id="student_class"
                      value={formData.student_class}
                      onChange={(e) => handleInputChange('student_class', e.target.value)}
                      placeholder="e.g., Primary 3, JSS 2"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="student_date_of_birth">Date of Birth *</Label>
                    <Input
                      id="student_date_of_birth"
                      type="date"
                      value={formData.student_date_of_birth}
                      onChange={(e) => handleInputChange('student_date_of_birth', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="parent_name">Parent/Guardian Name *</Label>
                    <Input
                      id="parent_name"
                      value={formData.parent_name}
                      onChange={(e) => handleInputChange('parent_name', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="parent_phone">Parent/Guardian Phone *</Label>
                    <Input
                      id="parent_phone"
                      value={formData.parent_phone}
                      onChange={(e) => handleInputChange('parent_phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="parent_email">Parent/Guardian Email *</Label>
                  <Input
                    id="parent_email"
                    type="email"
                    value={formData.parent_email}
                    onChange={(e) => handleInputChange('parent_email', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="home_address">Home Address *</Label>
                  <Textarea
                    id="home_address"
                    value={formData.home_address}
                    onChange={(e) => handleInputChange('home_address', e.target.value)}
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="reason_for_scholarship">Reason for Scholarship *</Label>
                  <Textarea
                    id="reason_for_scholarship"
                    value={formData.reason_for_scholarship}
                    onChange={(e) => handleInputChange('reason_for_scholarship', e.target.value)}
                    rows={4}
                    placeholder="Please explain why this student deserves this scholarship..."
                    required
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Submit Button */}
          {selectedStudent && (
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Submit Application
                  </>
                )}
              </Button>
            </div>
          )}
        </form>
      </div>
    </div>
  )
}
