<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use App\Models\ScholarshipCategory;
use App\Http\Requests\ScholarshipApplicationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PublicScholarshipController extends Controller
{
    /**
     * Get all active scholarships for public viewing
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 12);
            $category = $request->get('category');
            $search = $request->get('search');
            $sortBy = $request->get('sort_by', 'application_deadline');
            $sortOrder = $request->get('sort_order', 'asc');

            $query = Scholarship::where('status', 'active')
                ->orWhere('status', 'open')
                ->where('application_deadline', '>', now())
                ->with([
                    'fields' => function($query) {
                        $query->where('is_active', true)->orderBy('field_order');
                    }
                ])
                ->withCount(['applications as current_applicants']);

            // Filter by category
            if ($category && $category !== 'all') {
                $query->where('category', $category);
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('eligibility_criteria', 'like', "%{$search}%");
                });
            }

            // Sorting - prioritize featured scholarships and earliest deadlines
            $query->orderBy('is_featured', 'desc');
            if (in_array($sortBy, ['title', 'amount', 'application_deadline', 'created_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('application_deadline', 'asc');
            }

            $scholarships = $query->paginate($perPage);

            // Transform scholarship data with complete information
            $scholarships->getCollection()->transform(function ($scholarship) {
                return $this->transformScholarshipData($scholarship);
            });

            // Get categories for filtering
            $categories = ScholarshipCategory::active()->ordered()->get(['name', 'slug', 'description']);

            return response()->json([
                'success' => true,
                'message' => 'Scholarships retrieved successfully',
                'data' => $scholarships,
                'categories' => $categories
            ], 200, [
                'Cache-Control' => 'public, max-age=300', // 5 minutes cache
                'Vary' => 'Accept-Encoding'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }

    /**
     * Get a specific scholarship for public viewing
     */
    public function show($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where(function($query) {
                    $query->where('status', 'active')->orWhere('status', 'open');
                })
                ->where('application_deadline', '>', now())
                ->with([
                    'fields' => function($query) {
                        $query->where('is_active', true)->orderBy('field_order');
                    }
                ])
                ->withCount('applications as current_applicants')
                ->findOrFail($id);

            // Check if maximum applicants reached
            if ($scholarship->max_applicants && $scholarship->current_applicants >= $scholarship->max_applicants) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship has reached its maximum number of applicants'
                ], 410);
            }

            return response()->json([
                'success' => true,
                'message' => 'Scholarship retrieved successfully',
                'data' => $this->transformScholarshipData($scholarship, true)
            ], 200, [
                'Cache-Control' => 'public, max-age=300',
                'Vary' => 'Accept-Encoding'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found or no longer available',
                'error' => config('app.debug') ? $e->getMessage() : 'Not found'
            ], 404);
        }
    }

    /**
     * Apply for a scholarship
     */
    public function apply(ScholarshipApplicationRequest $request, $id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->findOrFail($id);

            // Check if application deadline has passed
            if ($scholarship->application_deadline <= now()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship application deadline has passed'
                ], 410);
            }

            // Check if user already applied
            $existingApplication = ScholarshipApplication::where('scholarship_id', $id)
                ->where('user_id', Auth::id())
                ->first();

            if ($existingApplication) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already applied for this scholarship'
                ], 409);
            }

            // Check if maximum applicants reached
            if ($scholarship->max_applicants && $scholarship->current_applicants >= $scholarship->max_applicants) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship has reached its maximum number of applicants'
                ], 410);
            }

            // Validation is handled by ScholarshipApplicationRequest

            // Generate unique application ID
            $applicationId = 'APP-' . strtoupper(Str::random(8)) . '-' . date('Y');
            
            // Ensure unique application ID
            while (ScholarshipApplication::where('application_id', $applicationId)->exists()) {
                $applicationId = 'APP-' . strtoupper(Str::random(8)) . '-' . date('Y');
            }

            // Prepare form data (excluding files)
            $formData = $request->except(['_token']);
            $uploadedFiles = [];

            // Handle file uploads
            foreach ($scholarship->fields as $field) {
                if ($field->field_type === 'file' && $request->hasFile($field->field_name)) {
                    $file = $request->file($field->field_name);
                    $uploadedFile = $this->handleFileUpload($file, $field->field_name, $applicationId);
                    
                    if ($uploadedFile) {
                        $uploadedFiles[] = $uploadedFile;
                        $formData[$field->field_name] = $uploadedFile['file_path'];
                    }
                }
            }

            // Create scholarship application
            $application = ScholarshipApplication::create([
                'application_id' => $applicationId,
                'scholarship_id' => $id,
                'user_id' => Auth::id(),
                'form_data' => $formData,
                'uploaded_files' => array_column($uploadedFiles, 'file_path'),
                'status' => 'pending',
                'submitted_at' => now()
            ]);

            // Create file records
            foreach ($uploadedFiles as $fileData) {
                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => $fileData['field_name'],
                    'original_name' => $fileData['original_name'],
                    'file_path' => $fileData['file_path'],
                    'file_size' => $fileData['file_size'],
                    'file_type' => $fileData['file_type'],
                    'upload_date' => now()
                ]);
            }

            // Update scholarship current applicants count
            $scholarship->increment('current_applicants');

            return response()->json([
                'success' => true,
                'message' => 'Application submitted successfully',
                'data' => [
                    'application_id' => $applicationId,
                    'scholarship_title' => $scholarship->title,
                    'submitted_at' => $application->submitted_at,
                    'status' => $application->status
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit application',
                'error' => $e->getMessage()
            ], 500);
        }
    }



    /**
     * Handle file upload
     */
    private function handleFileUpload($file, string $fieldName, string $applicationId): ?array
    {
        try {
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = $fieldName . '_' . $applicationId . '_' . time() . '.' . $extension;
            $filePath = 'scholarship-applications/' . date('Y/m') . '/' . $fileName;

            // Store the file
            $storedPath = $file->storeAs('scholarship-applications/' . date('Y/m'), $fileName, 'public');

            if ($storedPath) {
                return [
                    'field_name' => $fieldName,
                    'original_name' => $originalName,
                    'file_path' => $storedPath,
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getMimeType()
                ];
            }

            return null;
        } catch (\Exception $e) {
            \Log::error('File upload failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Transform scholarship data for frontend consumption
     */
    private function transformScholarshipData($scholarship, $includeDetailedFields = false): array
    {
        $categoryInstructions = $this->getCategoryInstructions($scholarship->category);

        $data = [
            // Basic scholarship info
            'id' => $scholarship->id,
            'title' => $scholarship->title,
            'slug' => $scholarship->slug,
            'description' => $scholarship->description,
            'eligibility_criteria' => $scholarship->eligibility_criteria,
            'amount' => $scholarship->amount,
            'application_deadline' => $scholarship->application_deadline,
            'application_start_date' => $scholarship->application_start_date,
            'status' => $scholarship->status,
            'is_featured' => $scholarship->is_featured,
            'max_applicants' => $scholarship->max_applicants,
            'current_applicants' => $scholarship->current_applicants,
            'contact_email' => $scholarship->contact_email,
            'image' => $scholarship->image,

            // Category-specific data
            'category' => $scholarship->category,
            'category_instructions' => $categoryInstructions,

            // Application metadata
            'spots_remaining' => $scholarship->max_applicants ?
                ($scholarship->max_applicants - $scholarship->current_applicants) : null,
            'days_remaining' => now()->diffInDays($scholarship->application_deadline, false),
            'is_application_open' => $scholarship->application_deadline > now() &&
                (!$scholarship->max_applicants || $scholarship->current_applicants < $scholarship->max_applicants),

            // Required documents
            'documents_required' => $scholarship->documents_required ?? [],

            // Custom fields for dynamic form generation
            'custom_fields' => $this->transformCustomFields($scholarship->fields ?? collect(), $includeDetailedFields),

            // Form metadata
            'form_metadata' => [
                'category' => $scholarship->category,
                'validation_rules' => $this->getValidationRules($scholarship->category),
                'field_constraints' => $this->getFieldConstraints($scholarship->fields ?? collect())
            ]
        ];

        // Add detailed application instructions for detail view
        if ($includeDetailedFields) {
            $data['application_instructions'] = $scholarship->application_instructions;
            $data['terms_conditions'] = $scholarship->terms_conditions;
            $data['requirements'] = $scholarship->requirements;
        }

        return $data;
    }

    /**
     * Get category-specific instructions
     */
    private function getCategoryInstructions(string $category): array
    {
        $instructions = [
            'primary' => [
                'title' => 'Primary School Scholarship',
                'age_range' => 'Ages 6-12 (Grades 1-6)',
                'filled_by' => 'Parent or Guardian',
                'instruction' => 'This form should be filled by the parent or guardian on behalf of the student',
                'required_info' => [
                    'Student information (filled by parent/guardian)',
                    'School account details for fee payment',
                    'Parent/guardian contact information',
                    'Family financial information'
                ]
            ],
            'secondary' => [
                'title' => 'Secondary School Scholarship',
                'age_range' => 'Ages 13-18 (JSS1-SS3)',
                'filled_by' => 'Student with Principal Assistance',
                'instruction' => 'This form should be filled by the student with assistance from school principal/financial officer',
                'required_info' => [
                    'Student personal information',
                    'School principal/financial officer account details',
                    'Academic performance records',
                    'School endorsement'
                ]
            ],
            'university' => [
                'title' => 'University Scholarship',
                'age_range' => 'Ages 18+ (Undergraduate/Graduate)',
                'filled_by' => 'Student',
                'instruction' => 'This form should be filled by the student using their matriculation number',
                'required_info' => [
                    'Student personal information',
                    'Matriculation number (no account field required)',
                    'Academic transcripts',
                    'Personal statement'
                ]
            ]
        ];

        return $instructions[$category] ?? [
            'title' => 'General Scholarship',
            'filled_by' => 'Applicant',
            'instruction' => 'Please fill out this form completely and accurately',
            'required_info' => []
        ];
    }

    /**
     * Transform custom fields for frontend consumption
     */
    private function transformCustomFields($fields, bool $includeDetailedInfo = false): array
    {
        return $fields->map(function ($field) use ($includeDetailedInfo) {
            $fieldData = [
                'field_name' => $field->field_name,
                'field_label' => $field->field_label,
                'field_type' => $field->field_type,
                'is_required' => $field->is_required,
                'display_order' => $field->field_order ?? 0
            ];

            if ($field->field_options) {
                $fieldData['field_options'] = $field->field_options;
            }

            if ($includeDetailedInfo) {
                $fieldData['help_text'] = $field->help_text ?? null;
                $fieldData['validation_rules'] = $this->getFieldValidationRules($field);
            }

            return $fieldData;
        })->toArray();
    }

    /**
     * Get validation rules for category
     */
    private function getValidationRules(string $category): array
    {
        $baseRules = [
            'required_fields' => ['student_name', 'student_age'],
            'file_types' => ['jpeg', 'png', 'jpg', 'pdf'],
            'max_file_size' => '2MB'
        ];

        $categoryRules = [
            'primary' => [
                'age_range' => [6, 12],
                'required_fields' => ['student_name', 'student_age', 'parent_name', 'school_account'],
                'special_requirements' => ['Parent/guardian must fill the form']
            ],
            'secondary' => [
                'age_range' => [13, 18],
                'required_fields' => ['student_name', 'student_age', 'principal_account'],
                'special_requirements' => ['Principal endorsement required']
            ],
            'university' => [
                'age_range' => [18, 35],
                'required_fields' => ['student_name', 'student_age', 'matric_number'],
                'special_requirements' => ['Valid matriculation number required']
            ]
        ];

        return array_merge($baseRules, $categoryRules[$category] ?? []);
    }

    /**
     * Get field constraints for form validation
     */
    private function getFieldConstraints($fields): array
    {
        $constraints = [];

        foreach ($fields as $field) {
            $fieldConstraints = [
                'required' => $field->is_required,
                'type' => $field->field_type
            ];

            // Add type-specific constraints
            switch ($field->field_type) {
                case 'file':
                    $fieldConstraints['accept'] = '.jpg,.jpeg,.png,.pdf';
                    $fieldConstraints['max_size'] = '2MB';
                    break;
                case 'email':
                    $fieldConstraints['pattern'] = 'email';
                    break;
                case 'tel':
                    $fieldConstraints['pattern'] = 'phone';
                    break;
                case 'number':
                    $fieldConstraints['min'] = 0;
                    break;
            }

            if ($field->field_options) {
                $fieldConstraints['options'] = $field->field_options;
            }

            $constraints[$field->field_name] = $fieldConstraints;
        }

        return $constraints;
    }

    /**
     * Get validation rules for specific field
     */
    private function getFieldValidationRules($field): array
    {
        $rules = [];

        if ($field->is_required) {
            $rules[] = 'required';
        }

        switch ($field->field_type) {
            case 'email':
                $rules[] = 'email';
                break;
            case 'number':
                $rules[] = 'numeric';
                break;
            case 'file':
                $rules[] = 'file';
                $rules[] = 'mimes:jpeg,png,jpg,pdf';
                $rules[] = 'max:2048';
                break;
            case 'date':
                $rules[] = 'date';
                break;
        }

        return $rules;
    }


}
