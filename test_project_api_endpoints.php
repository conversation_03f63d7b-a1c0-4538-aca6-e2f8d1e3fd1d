<?php

echo "=== Testing Project Scholarship API Endpoints ===\n\n";

$baseUrl = 'http://127.0.0.1:8000/api/v1';

// Test 1: Statistics endpoint
echo "1. Testing GET {$baseUrl}/project-scholarships/statistics\n";
$ch1 = curl_init();
curl_setopt($ch1, CURLOPT_URL, $baseUrl . '/project-scholarships/statistics');
curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch1, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch1, CURLOPT_TIMEOUT, 10);

$response1 = curl_exec($ch1);
$httpCode1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
curl_close($ch1);

echo "HTTP Status: $httpCode1\n";
if ($httpCode1 === 200) {
    $data1 = json_decode($response1, true);
    if ($data1 && $data1['success']) {
        echo "✓ Statistics API working\n";
        echo "✓ Approved by category:\n";
        foreach ($data1['data']['approved_by_category'] as $category => $count) {
            echo "  - $category: $count students\n";
        }
        echo "✓ Total approved: " . $data1['data']['totals']['total_approved'] . "\n";
        echo "✓ Active scholarships: " . $data1['data']['totals']['active_scholarships'] . "\n";
        echo "✓ Total amount awarded: ₦" . number_format($data1['data']['totals']['total_amount_awarded']) . "\n";
    } else {
        echo "✗ API Error: " . ($data1['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode1\n";
    echo "Response: $response1\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Portfolio endpoint
echo "2. Testing GET {$baseUrl}/project-scholarships/portfolio\n";
$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $baseUrl . '/project-scholarships/portfolio');
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch2, CURLOPT_TIMEOUT, 10);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "HTTP Status: $httpCode2\n";
if ($httpCode2 === 200) {
    $data2 = json_decode($response2, true);
    if ($data2 && $data2['success']) {
        echo "✓ Portfolio API working\n";
        echo "✓ Portfolio categories:\n";
        foreach ($data2['data'] as $portfolio) {
            echo "  - " . $portfolio['title'] . "\n";
            echo "    Approved: " . $portfolio['approved_applications'] . " applications\n";
            echo "    Active: " . $portfolio['active_scholarships'] . " scholarships\n";
            echo "    Amount: ₦" . number_format($portfolio['total_amount']) . "\n";
        }
    } else {
        echo "✗ API Error: " . ($data2['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode2\n";
    echo "Response: $response2\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Scholarships by status (Active)
echo "3. Testing GET {$baseUrl}/project-scholarships/by-status?status=active\n";
$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, $baseUrl . '/project-scholarships/by-status?status=active');
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch3, CURLOPT_TIMEOUT, 10);

$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "HTTP Status: $httpCode3\n";
if ($httpCode3 === 200) {
    $data3 = json_decode($response3, true);
    if ($data3 && $data3['success']) {
        echo "✓ Active scholarships API working\n";
        echo "✓ Found " . count($data3['data']) . " active scholarships\n";
        foreach ($data3['data'] as $scholarship) {
            echo "  - " . $scholarship['title'] . " (" . $scholarship['category'] . ")\n";
            echo "    Amount: ₦" . number_format($scholarship['amount']) . "\n";
            echo "    Approved: " . $scholarship['approved_applications'] . " applications\n";
            if ($scholarship['days_remaining'] !== null) {
                $days = abs($scholarship['days_remaining']);
                echo "    Days remaining: " . $days . "\n";
            }
        }
    } else {
        echo "✗ API Error: " . ($data3['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode3\n";
    echo "Response: $response3\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: Scholarships by status (Completed)
echo "4. Testing GET {$baseUrl}/project-scholarships/by-status?status=completed\n";
$ch4 = curl_init();
curl_setopt($ch4, CURLOPT_URL, $baseUrl . '/project-scholarships/by-status?status=completed');
curl_setopt($ch4, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch4, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch4, CURLOPT_TIMEOUT, 10);

$response4 = curl_exec($ch4);
$httpCode4 = curl_getinfo($ch4, CURLINFO_HTTP_CODE);
curl_close($ch4);

echo "HTTP Status: $httpCode4\n";
if ($httpCode4 === 200) {
    $data4 = json_decode($response4, true);
    if ($data4 && $data4['success']) {
        echo "✓ Completed scholarships API working\n";
        echo "✓ Found " . count($data4['data']) . " completed scholarships\n";
        if (!empty($data4['data'])) {
            foreach (array_slice($data4['data'], 0, 3) as $scholarship) {
                echo "  - " . $scholarship['title'] . " (" . $scholarship['category'] . ")\n";
                echo "    Amount: ₦" . number_format($scholarship['amount']) . "\n";
                echo "    Approved: " . $scholarship['approved_applications'] . " applications\n";
            }
        }
    } else {
        echo "✗ API Error: " . ($data4['message'] ?? 'Unknown') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode4\n";
    echo "Response: $response4\n";
}

echo "\n=== API Testing Complete ===\n";
echo "All endpoints are ready for frontend integration\n";
