<?php

// Simple database check without <PERSON><PERSON> bootstrap
$host = 'localhost';
$dbname = 'api_ngo_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Database connection successful!\n";
    echo "================================\n\n";
    
    // Check for users with the specific email
    $email = '<EMAIL>';
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, role, partner_organization_id, status, created_at FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Users with email '$email':\n";
    echo "==========================\n";
    
    if (empty($users)) {
        echo "No users found with this email.\n\n";
    } else {
        foreach ($users as $user) {
            echo "ID: {$user['id']}\n";
            echo "Name: {$user['first_name']} {$user['last_name']}\n";
            echo "Email: {$user['email']}\n";
            echo "Role: {$user['role']}\n";
            echo "Partner Org ID: {$user['partner_organization_id']}\n";
            echo "Status: {$user['status']}\n";
            echo "Created: {$user['created_at']}\n";
            echo "---\n";
        }
    }
    
    // Check all partner organization users
    echo "\nAll partner organization users:\n";
    echo "===============================\n";
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, role, partner_organization_id FROM users WHERE role = 'partner_organization'");
    $stmt->execute();
    $partnerUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($partnerUsers)) {
        echo "No partner organization users found.\n";
    } else {
        foreach ($partnerUsers as $user) {
            echo "ID: {$user['id']}, Email: {$user['email']}, Name: {$user['first_name']} {$user['last_name']}, Partner Org: {$user['partner_organization_id']}\n";
        }
    }
    
    echo "\nTotal partner users: " . count($partnerUsers) . "\n";
    
    // Check all users with similar emails
    echo "\nUsers with emails containing 'principal':\n";
    echo "=========================================\n";
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, email, role FROM users WHERE email LIKE '%principal%'");
    $stmt->execute();
    $principalUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($principalUsers as $user) {
        echo "ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}\n";
    }
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}
