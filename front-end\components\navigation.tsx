"use client"

import { useState, useEffect, useRef } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { Heart, Menu, ChevronDown, Users, GraduationCap, BookOpen, Target, MessageSquare, TrendingUp, ExternalLink, X } from "lucide-react"

import { useSettings } from "@/hooks/useSettings"

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const pathname = usePathname()
  const { settings, loading: settingsLoading } = useSettings()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Authentication is now handled by Laravel - no client-side auth check needed

  // Handle scroll effect for navigation bar
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10
      setScrolled(isScrolled)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Handle dropdown interactions with delay
  const handleDropdownEnter = (dropdownName: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setActiveDropdown(dropdownName)
  }

  const handleDropdownLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveDropdown(null)
    }, 150) // Small delay for smooth UX
  }

  // Navigation is always rendered since we removed Next.js dashboards

  const aboutItems = [
    { href: "/about", label: "About Us", icon: Heart, description: "Learn about our mission" },
    { href: "/team", label: "Our Team", icon: Users, description: "Meet our dedicated team" },
  ]

  const programItems = [
    { href: "/projects", label: "Projects", icon: Target, description: "Our community projects" },
    { href: "/scholarships", label: "Scholarships", icon: GraduationCap, description: "Educational support" },
    { href: "/learn-with-us", label: "Learn With Us", icon: BookOpen, description: "Educational programs" },
  ]

  const engagementItems = [
    { href: "/contact", label: "Contact", icon: MessageSquare, description: "Get in touch" },
    { href: "/blog", label: "Blog", icon: BookOpen, description: "Latest updates" },
  ]

  const isActiveDropdown = (items: typeof aboutItems) => {
    return items.some(item => pathname === item.href)
  }

  // Get app name and logo from settings or use defaults
  const appName = settings?.app_name || 'Laravel NGO'
  const appLogo = settings?.app_logo
  
  // Debug logging
  console.log('Navigation - Settings:', settings)
  console.log('Navigation - App Logo:', appLogo)

  return (
    <header className={`fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ${
      scrolled 
        ? "bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5" 
        : "bg-white/95 backdrop-blur-md border-b border-gray-200/30"
    } dark:bg-gray-950/95 dark:supports-[backdrop-filter]:bg-gray-950/80`}>
      <div className="container flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Logo with enhanced hover effect */}
        <Link 
          href="/" 
          className="group flex items-center gap-3 hover:scale-105 transition-all duration-300 ease-out"
        >
          {appLogo ? (
            <div className="relative overflow-hidden rounded-full">
              <img 
                src={appLogo} 
                alt={`${appName} Logo`}
                className="h-9 w-9 object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
          ) : (
            <div className="relative h-9 w-9 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg group-hover:shadow-xl transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />
              <Heart className="absolute inset-0 m-auto h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110" />
            </div>
          )}
          <span className="text-xl font-bold text-gray-900 dark:text-white transition-colors duration-300">
            <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
              {appName}
            </span>
          </span>
        </Link>

        {/* Desktop Navigation with enhanced effects */}
        <nav className="hidden lg:flex items-center space-x-1">
          {/* Home */}
          <Link
            href="/"
            className={`group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${
              pathname === "/" 
                ? "text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30" 
                : "text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"
            }`}
          >
            <span className="relative z-10">Home</span>
            {pathname === "/" && (
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl" />
            )}
          </Link>

          {/* About Dropdown with enhanced interaction */}
          <div 
            className="relative"
            onMouseEnter={() => handleDropdownEnter('about')}
            onMouseLeave={handleDropdownLeave}
          >
            <DropdownMenu open={activeDropdown === 'about'} onOpenChange={(open) => setActiveDropdown(open ? 'about' : null)}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${
                    isActiveDropdown(aboutItems) || activeDropdown === 'about'
                      ? "text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30"
                      : "text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"
                  }`}
                >
                  <span className="relative z-10">About</span>
                  <ChevronDown className={`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${
                    activeDropdown === 'about' ? 'rotate-180' : ''
                  }`} />
                  {(isActiveDropdown(aboutItems) || activeDropdown === 'about') && (
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start" 
                className="w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl"
                sideOffset={8}
              >
                {aboutItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <DropdownMenuItem key={item.href} asChild className="rounded-xl p-0">
                      <Link 
                        href={item.href} 
                        className="group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                          <Icon className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{item.label}</div>
                          <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Programs Dropdown */}
          <div 
            className="relative"
            onMouseEnter={() => handleDropdownEnter('programs')}
            onMouseLeave={handleDropdownLeave}
          >
            <DropdownMenu open={activeDropdown === 'programs'} onOpenChange={(open) => setActiveDropdown(open ? 'programs' : null)}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${
                    isActiveDropdown(programItems) || activeDropdown === 'programs'
                      ? "text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30"
                      : "text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"
                  }`}
                >
                  <span className="relative z-10">Programs</span>
                  <ChevronDown className={`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${
                    activeDropdown === 'programs' ? 'rotate-180' : ''
                  }`} />
                  {(isActiveDropdown(programItems) || activeDropdown === 'programs') && (
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start" 
                className="w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl"
                sideOffset={8}
              >
                {programItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <DropdownMenuItem key={item.href} asChild className="rounded-xl p-0">
                      <Link 
                        href={item.href} 
                        className="group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                          <Icon className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{item.label}</div>
                          <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Impact */}
          <Link
            href="/impact"
            className={`group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${
              pathname === "/impact" 
                ? "text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30" 
                : "text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"
            }`}
          >
            <span className="relative z-10">Impact</span>
            {pathname === "/impact" && (
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl" />
            )}
          </Link>

          {/* Get Involved Dropdown */}
          <div 
            className="relative"
            onMouseEnter={() => handleDropdownEnter('involved')}
            onMouseLeave={handleDropdownLeave}
          >
            <DropdownMenu open={activeDropdown === 'involved'} onOpenChange={(open) => setActiveDropdown(open ? 'involved' : null)}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${
                    isActiveDropdown(engagementItems) || activeDropdown === 'involved'
                      ? "text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30"
                      : "text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"
                  }`}
                >
                  <span className="relative z-10">Get Involved</span>
                  <ChevronDown className={`ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${
                    activeDropdown === 'involved' ? 'rotate-180' : ''
                  }`} />
                  {(isActiveDropdown(engagementItems) || activeDropdown === 'involved') && (
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start" 
                className="w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl"
                sideOffset={8}
              >
                {engagementItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <DropdownMenuItem key={item.href} asChild className="rounded-xl p-0">
                      <Link 
                        href={item.href} 
                        className="group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                          <Icon className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{item.label}</div>
                          <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </nav>

        {/* Desktop CTA with enhanced styling */}
        {isClient && (
          <div className="hidden lg:flex items-center gap-3" suppressHydrationWarning>
            {/* Show different buttons based on authentication status */}
            {isUserAuthenticated ? (
              <>
                <Link href="/donate">
                  <Button
                    className="group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105"
                    suppressHydrationWarning
                  >
                    <span className="relative z-10 font-medium">Donate Now</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500" />
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button
                    variant="outline"
                    className="group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20"
                    suppressHydrationWarning
                  >
                    <span className="relative z-10 font-medium">Login</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300" />
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button
                    className="group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105"
                    suppressHydrationWarning
                  >
                    <span className="relative z-10 font-medium">Register</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500" />
                  </Button>
                </Link>
              </>
            )}
          </div>
        )}

        {/* Enhanced Mobile Navigation */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild className="lg:hidden">
            <Button 
              variant="ghost" 
              size="icon" 
              className="group relative overflow-hidden transition-all duration-300 hover:scale-110 focus:scale-110 rounded-xl hover:bg-green-50"
            >
              <div className={`absolute inset-0 transition-all duration-300 ${isOpen ? 'rotate-180 scale-75' : ''}`}>
                {isOpen ? (
                  <X className="absolute inset-0 m-auto h-5 w-5 text-gray-600" />
                ) : (
                  <Menu className="absolute inset-0 m-auto h-5 w-5 text-gray-600" />
                )}
              </div>
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent 
            side="right" 
            className="w-[320px] sm:w-[400px] z-[100] p-0 bg-white/95 backdrop-blur-xl border-l border-gray-200/50"
          >
            <SheetPrimitive.Title className="sr-only">Mobile Navigation Menu</SheetPrimitive.Title>
            <div className="flex flex-col h-full">
              {/* Enhanced Header */}
              <div className="p-6 border-b border-gray-200/50">
                <div className="flex items-center gap-3">
                  {appLogo ? (
                    <div className="relative overflow-hidden rounded-full">
                      <img 
                        src={appLogo} 
                        alt={`${appName} Logo`}
                        className="h-8 w-auto object-contain"
                      />
                    </div>
                  ) : (
                    <div className="h-8 w-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg">
                      <Heart className="h-5 w-5 text-white" />
                    </div>
                  )}
                  <span className="text-lg font-bold text-gray-900">
                    <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                      {appName}
                    </span>
                  </span>
                </div>
              </div>

              {/* Enhanced Navigation Links */}
              <div className="flex-1 p-6 space-y-6 overflow-y-auto">
                <Link
                  href="/"
                  className={`block text-lg font-medium transition-all duration-200 p-3 rounded-xl ${
                    pathname === "/" 
                      ? "text-green-600 bg-green-50" 
                      : "text-gray-600 hover:text-green-600 hover:bg-green-50/50"
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  Home
                </Link>

                {/* About Section */}
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider">About</h3>
                  {aboutItems.map((item) => {
                    const Icon = item.icon
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${
                          pathname === item.href 
                            ? "text-green-600 bg-green-50" 
                            : "text-gray-600 hover:text-green-600 hover:bg-green-50/50"
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        <Icon className="h-5 w-5" />
                        {item.label}
                      </Link>
                    )
                  })}
                </div>

                {/* Programs Section */}
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider">Programs</h3>
                  {programItems.map((item) => {
                    const Icon = item.icon
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${
                          pathname === item.href 
                            ? "text-green-600 bg-green-50" 
                            : "text-gray-600 hover:text-green-600 hover:bg-green-50/50"
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        <Icon className="h-5 w-5" />
                        {item.label}
                      </Link>
                    )
                  })}
                </div>

                <Link
                  href="/impact"
                  className={`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${
                    pathname === "/impact" 
                      ? "text-green-600 bg-green-50" 
                      : "text-gray-600 hover:text-green-600 hover:bg-green-50/50"
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  <TrendingUp className="h-5 w-5" />
                  Impact
                </Link>

                {/* Get Involved Section */}
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider">Get Involved</h3>
                  {engagementItems.map((item) => {
                    const Icon = item.icon
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={`flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${
                          pathname === item.href 
                            ? "text-green-600 bg-green-50" 
                            : "text-gray-600 hover:text-green-600 hover:bg-green-50/50"
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        <Icon className="h-5 w-5" />
                        {item.label}
                      </Link>
                    )
                  })}
                </div>

                {/* Mobile Auth Buttons */}
                {!isUserAuthenticated && (
                  <div className="pt-4 space-y-3">
                    <Link href="/auth/login" onClick={() => setIsOpen(false)}>
                      <Button
                        variant="outline"
                        className="w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3"
                      >
                        Login
                      </Button>
                    </Link>
                    <Link href="/auth/register" onClick={() => setIsOpen(false)}>
                      <Button
                        className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg"
                      >
                        Register
                      </Button>
                    </Link>
                  </div>
                )}
              </div>

              {/* Enhanced Footer CTA */}
              <div className="p-6 border-t border-gray-200/50">
                <Link href="/donate" onClick={() => setIsOpen(false)}>
                  <Button className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg">
                    Donate Now
                  </Button>
                </Link>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
}
