@extends('layouts.admin')

@section('title', 'Volunteers Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Volunteers Management</h1>
        <div>
            <a href="{{ route('admin.volunteers.applications') }}" class="btn btn-warning me-2">
                <i class="fas fa-clock me-2"></i>Pending Applications
            </a>
            <a href="{{ route('admin.volunteers.opportunities.index') }}" class="btn btn-info">
                <i class="fas fa-plus me-2"></i>Volunteer Opportunities
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Volunteers</div>
                            <div class="h4">{{ $volunteers->total() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-hands-helping fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Volunteers</div>
                            <div class="h4">{{ $volunteers->where('application_status', 'approved')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Pending Applications</div>
                            <div class="h4">{{ $volunteers->where('application_status', 'pending')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Hours</div>
                            <div class="h4">{{ number_format($volunteers->sum('hours_logged')) }}</div>
                        </div>
                        <div>
                            <i class="fas fa-stopwatch fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Volunteers Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Volunteers</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search volunteers..." id="searchVolunteers">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterSkills">
                        <option value="">All Skills</option>
                        <option value="teaching">Teaching</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="construction">Construction</option>
                        <option value="technology">Technology</option>
                        <option value="administration">Administration</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Volunteer</th>
                            <th>Skills & Interests</th>
                            <th>Status</th>
                            <th>Hours Logged</th>
                            <th>Availability</th>
                            <th>Applied Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($volunteers as $volunteer)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($volunteer->user && $volunteer->user->profile_picture)
                                        <img src="{{ $volunteer->user->profile_picture }}" alt="{{ $volunteer->user->first_name }}" class="rounded-circle me-3" width="50" height="50">
                                    @else
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            {{ $volunteer->user ? strtoupper(substr($volunteer->user->first_name, 0, 1)) : 'V' }}
                                        </div>
                                    @endif
                                    <div>
                                        <div class="fw-bold">
                                            {{ $volunteer->user ? $volunteer->user->first_name . ' ' . $volunteer->user->last_name : 'Unknown User' }}
                                        </div>
                                        @if($volunteer->user)
                                            <small class="text-muted">{{ $volunteer->user->email }}</small><br>
                                            @if($volunteer->user->phone_number)
                                                <small class="text-muted">{{ $volunteer->user->phone_number }}</small>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    @if($volunteer->skills && is_array($volunteer->skills))
                                        <div class="mb-1">
                                            <strong>Skills:</strong>
                                            @foreach(array_slice($volunteer->skills, 0, 3) as $skill)
                                                <span class="badge bg-primary me-1">{{ $skill }}</span>
                                            @endforeach
                                            @if(count($volunteer->skills) > 3)
                                                <span class="text-muted">+{{ count($volunteer->skills) - 3 }} more</span>
                                            @endif
                                        </div>
                                    @endif
                                    @if($volunteer->interests && is_array($volunteer->interests))
                                        <div>
                                            <strong>Interests:</strong>
                                            @foreach(array_slice($volunteer->interests, 0, 2) as $interest)
                                                <span class="badge bg-info me-1">{{ $interest }}</span>
                                            @endforeach
                                            @if(count($volunteer->interests) > 2)
                                                <span class="text-muted">+{{ count($volunteer->interests) - 2 }} more</span>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $volunteer->application_status === 'approved' ? 'success' : 
                                    ($volunteer->application_status === 'rejected' ? 'danger' : 'warning') 
                                }}">
                                    {{ ucfirst($volunteer->application_status) }}
                                </span>
                                @if($volunteer->approved_at)
                                    <br><small class="text-muted">{{ $volunteer->approved_at->format('M d, Y') }}</small>
                                @endif
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="text-{{ $volunteer->hours_logged > 100 ? 'success' : ($volunteer->hours_logged > 50 ? 'info' : 'muted') }}">
                                        {{ number_format($volunteer->hours_logged, 1) }}h
                                    </strong>
                                    @if($volunteer->hours_logged > 0)
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-success" style="width: {{ min(100, ($volunteer->hours_logged / 200) * 100) }}%"></div>
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($volunteer->availability && is_array($volunteer->availability))
                                    @if(isset($volunteer->availability['weekdays']) && is_array($volunteer->availability['weekdays']))
                                        <div class="small">
                                            <strong>Weekdays:</strong> {{ implode(', ', array_slice($volunteer->availability['weekdays'], 0, 3)) }}
                                            @if(count($volunteer->availability['weekdays']) > 3)
                                                <span class="text-muted">+{{ count($volunteer->availability['weekdays']) - 3 }}</span>
                                            @endif
                                        </div>
                                    @endif
                                    @if(isset($volunteer->availability['weekends']))
                                        <div class="small">
                                            <strong>Weekends:</strong> 
                                            <span class="badge bg-{{ $volunteer->availability['weekends'] ? 'success' : 'secondary' }}">
                                                {{ $volunteer->availability['weekends'] ? 'Available' : 'Not Available' }}
                                            </span>
                                        </div>
                                    @endif
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </td>
                            <td>
                                {{ $volunteer->applied_at ? $volunteer->applied_at->format('M d, Y') : 'Unknown' }}
                                @if($volunteer->applied_at)
                                    <br><small class="text-muted">{{ $volunteer->applied_at->diffForHumans() }}</small>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.volunteers.show', $volunteer->id) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($volunteer->application_status === 'pending')
                                        <button class="btn btn-sm btn-outline-success" onclick="approveVolunteer({{ $volunteer->id }})" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="rejectVolunteer({{ $volunteer->id }})" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-sm btn-outline-info" onclick="sendMessage({{ $volunteer->user ? $volunteer->user->id : 0 }})" title="Send Message">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-hands-helping fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No volunteers found</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $volunteers->firstItem() }} to {{ $volunteers->lastItem() }} of {{ $volunteers->total() }} results
                </div>
                {{ $volunteers->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Approve Volunteer Modal -->
<div class="modal fade" id="approveVolunteerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Volunteer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to approve this volunteer application?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmApproveVolunteer">Approve</button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Volunteer Modal -->
<div class="modal fade" id="rejectVolunteerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Volunteer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject this volunteer application?</p>
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label">Reason for rejection (optional):</label>
                    <textarea class="form-control" id="rejectionReason" rows="3" placeholder="Provide a reason for rejection..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRejectVolunteer">Reject</button>
            </div>
        </div>
    </div>
</div>

<script>
function approveVolunteer(volunteerId) {
    const modal = new bootstrap.Modal(document.getElementById('approveVolunteerModal'));
    modal.show();
    
    document.getElementById('confirmApproveVolunteer').onclick = function() {
        console.log('Approve volunteer:', volunteerId);
        modal.hide();
        // Add AJAX call to approve volunteer
        // After success, refresh the page or update the row
    };
}

function rejectVolunteer(volunteerId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectVolunteerModal'));
    modal.show();
    
    document.getElementById('confirmRejectVolunteer').onclick = function() {
        const reason = document.getElementById('rejectionReason').value;
        console.log('Reject volunteer:', volunteerId, 'Reason:', reason);
        modal.hide();
        // Add AJAX call to reject volunteer
    };
}

function sendMessage(userId) {
    console.log('Send message to user:', userId);
    // Implement message sending functionality
}

// Search functionality
document.getElementById('searchVolunteers').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterSkills').addEventListener('change', function() {
    console.log('Filter skills:', this.value);
});
</script>
@endsection 