# 🌟 Halimakq Foundation - Complete Developer Documentation

> **Transforming Lives Through Technology** - Complete development guide for the HLTKKQ Foundation NGO platform

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Laravel](https://img.shields.io/badge/Laravel-12.x-red.svg)
![Next.js](https://img.shields.io/badge/Next.js-15.x-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)
![PHP](https://img.shields.io/badge/PHP-8.2+-purple.svg)

## 📋 Table of Contents

- [🚀 Quick Start](#-quick-start)
- [🏗️ Architecture Overview](#%EF%B8%8F-architecture-overview)
- [🔧 Backend Documentation](#-backend-documentation)
- [🎨 Frontend Documentation](#-frontend-documentation)
- [🔗 API Integration](#-api-integration)
- [🛠️ Development Setup](#%EF%B8%8F-development-setup)
- [📦 Component Library](#-component-library)
- [🚀 Deployment Guide](#-deployment-guide)
- [🧪 Testing](#-testing)
- [📖 Detailed Guides](#-detailed-guides)

---

## 🚀 Quick Start

### Prerequisites
- **PHP 8.2+** with Laravel 12
- **Node.js 18+** with npm/yarn
- **MySQL 8.0+** database
- **Composer** for PHP dependencies
- **Git** for version control

### 30-Second Setup
```bash
# 1. Clone and install backend
git clone [repository-url]
cd laravel-api-ngo
composer install
php artisan migrate:fresh --seed

# 2. Install and run frontend
cd front-end
npm install
npm run dev

# 3. Start backend server
cd ..
php artisan serve

# ✅ Access: Frontend (http://localhost:3000) | Backend (http://localhost:8000)
```

---

## 🏗️ Architecture Overview

### System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    HLTKKQ Foundation Platform               │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Next.js 15)          │  Backend (Laravel 12)    │
│  ├── Pages & Components         │  ├── API Controllers     │
│  ├── Hooks & State Management   │  ├── Models & Migrations │
│  ├── UI Components (shadcn/ui)  │  ├── Authentication      │
│  └── TypeScript Types           │  └── Database Seeders    │
├─────────────────────────────────────────────────────────────┤
│                    Database Layer (MySQL)                   │
│  ├── Users & Roles             │  ├── Blog & Content       │
│  ├── Donations & Campaigns     │  ├── Events & Programs    │
│  └── Volunteers & Scholarships │  └── Settings & Analytics │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | Next.js 15 + TypeScript | Modern React framework with SSR |
| **UI Components** | shadcn/ui + Tailwind CSS | Consistent, accessible design system |
| **Backend** | Laravel 12 + PHP 8.2+ | Robust API with authentication |
| **Database** | MySQL 8.0+ | Reliable data persistence |
| **Authentication** | Laravel Sanctum | Token-based API authentication |
| **File Storage** | Laravel Storage | Image/document management |
| **State Management** | Zustand + React Hooks | Lightweight state management |

---

## 🔧 Backend Documentation

### 📁 Project Structure

```
app/
├── Http/Controllers/Api/          # API Controllers
│   ├── AuthController.php         # Authentication endpoints
│   ├── UserController.php         # User profile management
│   ├── BlogController.php         # Blog content management
│   ├── DonationController.php     # Donation processing
│   ├── EventController.php        # Event management
│   ├── VolunteerController.php    # Volunteer operations
│   └── Admin/                     # Admin-only controllers
├── Models/                        # Eloquent models
├── Middleware/                    # Custom middleware
└── Providers/                     # Service providers

database/
├── migrations/                    # Database schema
├── seeders/                       # Sample data
└── factories/                     # Model factories

routes/
├── api.php                        # API route definitions
└── web.php                        # Web route definitions
```

### 🔐 Authentication System

**Laravel Sanctum Implementation**

```php
// Login endpoint example
POST /api/v1/login
{
    "email": "<EMAIL>",
    "password": "password"
}

// Response
{
    "success": true,
    "data": {
        "user": { /* user data */ },
        "access_token": "your-token-here"
    }
}
```

### 📊 Database Schema

#### Core Models
- **User**: Main user accounts with roles (admin, volunteer, donor, user)
- **Volunteer**: Volunteer applications and hour tracking
- **Program**: NGO programs and projects
- **Event**: Event management and registrations
- **BlogPost**: Content management system
- **Donation**: Donation tracking with campaigns
- **Scholarship**: Scholarship applications and management

#### User Types & Roles
```typescript
type UserType = "student" | "partner" | "volunteer" | "admin"
```

### 📡 API Endpoints

#### Public Endpoints
```http
GET    /api/v1/blog/posts              # Blog posts
GET    /api/v1/events                  # Events list
GET    /api/v1/programs                # Programs
POST   /api/v1/contact                 # Contact form
POST   /api/v1/newsletter/subscribe    # Newsletter subscription
```

#### Authenticated Endpoints
```http
GET    /api/v1/profile                 # User profile
PUT    /api/v1/profile                 # Update profile
POST   /api/v1/volunteer/apply         # Volunteer application
POST   /api/v1/donations               # Make donation
GET    /api/v1/events/my-registrations # User's event registrations
```

#### Admin Endpoints
```http
GET    /api/v1/admin/dashboard         # Admin dashboard data
GET    /api/v1/admin/users             # User management
POST   /api/v1/admin/blog/posts        # Create blog post
PUT    /api/v1/admin/events/{id}       # Update event
```

---

## 🎨 Frontend Documentation

### 📁 Project Structure

```
front-end/
├── app/                           # Next.js App Router
│   ├── auth/                      # Authentication pages
│   │   ├── login/page.tsx
│   │   └── register/page.tsx
│   ├── dashboard/                 # User dashboards
│   │   ├── admin/
│   │   ├── student/
│   │   ├── volunteer/
│   │   └── partner/
│   ├── blog/                      # Blog pages
│   ├── scholarships/              # Scholarship pages
│   └── page.tsx                   # Homepage
├── components/                    # Reusable components
│   ├── ui/                        # shadcn/ui components
│   ├── dashboard/                 # Dashboard-specific components
│   ├── navigation.tsx             # Main navigation
│   └── footer.tsx                 # Site footer
├── hooks/                         # Custom React hooks
│   ├── use-auth.ts                # Authentication hook
│   ├── use-settings.ts            # Settings management
│   └── useAuthGuard.ts            # Route protection
├── lib/                           # Utilities and configurations
│   ├── api.ts                     # API client
│   ├── utils.ts                   # Helper functions
│   └── types.ts                   # TypeScript definitions
└── styles/                        # Global styles
```

### 🎯 Component Architecture

#### Component Patterns
```typescript
// Standard component structure
interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // ... other props
}

export function ComponentName({ className, children, ...props }: ComponentProps) {
  return (
    <div className={cn("default-classes", className)} {...props}>
      {children}
    </div>
  );
}
```

#### UI Component Library (shadcn/ui)
- **Button**: `<Button variant="default" size="md">Click me</Button>`
- **Input**: `<Input type="email" placeholder="Enter email" />`
- **Card**: `<Card><CardHeader><CardTitle>Title</CardTitle></CardHeader></Card>`
- **Dialog**: Modal dialogs and overlays
- **Sheet**: Slide-out panels for mobile navigation
- **Dropdown**: Context menus and dropdowns

### 🔗 State Management

#### Authentication State (Zustand)
```typescript
// hooks/use-auth.ts
interface AuthState {
  user: User | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

// Usage in components
const { user, login, logout } = useAuth();
```

### 📱 Responsive Design

#### Breakpoint System
```css
/* Tailwind CSS responsive classes */
sm:   640px   /* Mobile landscape */
md:   768px   /* Tablet portrait */
lg:   1024px  /* Tablet landscape / Small desktop */
xl:   1280px  /* Desktop */
2xl:  1536px  /* Large desktop */
```

#### Mobile-First Approach
```jsx
<div className="text-sm md:text-base lg:text-lg xl:text-xl">
  Responsive text sizing
</div>

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Responsive grid layout */}
</div>
```

---

## 🔗 API Integration

### 🛠️ API Client Configuration

```typescript
// lib/api.ts
class ApiClient {
  private baseURL = process.env.NEXT_PUBLIC_API_URL;
  
  async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}/api/v1${endpoint}`;
    const token = localStorage.getItem('authToken');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };
    
    const response = await fetch(url, config);
    const data = await response.json();
    
    // Handle 401 Unauthorized
    if (response.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/auth/login';
    }
    
    return data;
  }
}

export const apiClient = new ApiClient();
```

### 🔄 Data Fetching Patterns

#### Standard API Call
```typescript
const [data, setData] = useState(null);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiClient.getData();
      if (response.success) {
        setData(response.data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  fetchData();
}, []);
```

#### Form Submission
```typescript
const handleSubmit = async (formData: FormData) => {
  setSubmitting(true);
  try {
    const response = await apiClient.post('/endpoint', formData);
    if (response.success) {
      toast.success('Success message');
      router.push('/success-page');
    } else {
      setErrors(response.errors || {});
    }
  } catch (error) {
    toast.error('Something went wrong');
  } finally {
    setSubmitting(false);
  }
};
```

---

## 🛠️ Development Setup

### 🚦 Environment Configuration

#### Backend (.env)
```env
# Application
APP_NAME="HLTKKQ Foundation"
APP_ENV=local
APP_KEY=base64:your-app-key
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=

# Authentication
SANCTUM_STATEFUL_DOMAINS=localhost:3000

# File Storage
FILESYSTEM_DISK=public

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
```

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

### 🔄 Development Workflow

#### Daily Development Commands
```bash
# Backend
php artisan serve                    # Start Laravel server
php artisan migrate:fresh --seed     # Reset database with sample data
php artisan queue:work               # Process background jobs
php artisan storage:link             # Link storage directory

# Frontend
npm run dev                          # Start Next.js development server
npm run build                        # Build for production
npm run lint                         # Run ESLint

# Database
php artisan migrate                  # Run new migrations
php artisan db:seed                  # Run seeders
php artisan tinker                   # Laravel REPL
```

#### Common Tasks

**Creating New API Endpoint**
```bash
# 1. Create controller
php artisan make:controller Api/NewController

# 2. Add routes in routes/api.php
Route::get('/new-endpoint', [NewController::class, 'index']);

# 3. Test endpoint
curl http://localhost:8000/api/v1/new-endpoint
```

**Creating New Page**
```bash
# Create new page
touch front-end/app/new-page/page.tsx

# Add to navigation (if needed)
# Edit front-end/components/navigation.tsx
```

---

## 📦 Component Library

### 🎨 Design System

#### Color Palette
```css
/* Primary Colors */
--primary: #22c55e      /* Green-500 */
--primary-dark: #16a34a /* Green-600 */
--primary-light: #86efac /* Green-300 */

/* Secondary Colors */
--secondary: #f59e0b     /* Amber-500 */
--secondary-dark: #d97706 /* Amber-600 */

/* Status Colors */
--success: #10b981      /* Emerald-500 */
--error: #ef4444        /* Red-500 */
--warning: #f59e0b      /* Amber-500 */
--info: #3b82f6         /* Blue-500 */
```

#### Typography Scale
```css
/* Headings */
.text-xs     { font-size: 0.75rem; }    /* 12px */
.text-sm     { font-size: 0.875rem; }   /* 14px */
.text-base   { font-size: 1rem; }       /* 16px */
.text-lg     { font-size: 1.125rem; }   /* 18px */
.text-xl     { font-size: 1.25rem; }    /* 20px */
.text-2xl    { font-size: 1.5rem; }     /* 24px */
.text-3xl    { font-size: 1.875rem; }   /* 30px */
```

### 🧩 Component Examples

#### Custom Card Component
```typescript
// components/ui/custom-card.tsx
interface CustomCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function CustomCard({ title, description, children, className }: CustomCardProps) {
  return (
    <Card className={cn("hover:shadow-lg transition-shadow", className)}>
      <CardHeader>
        <CardTitle className="text-green-600">{title}</CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
```

#### Loading States
```typescript
// components/ui/loading-spinner.tsx
export function LoadingSpinner({ size = "md" }: { size?: "sm" | "md" | "lg" }) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  };
  
  return (
    <div className={cn("animate-spin rounded-full border-2 border-gray-300 border-t-green-600", sizeClasses[size])} />
  );
}
```

---

## 🚀 Deployment Guide

### 📦 Production Build

#### Backend Deployment
```bash
# 1. Install dependencies
composer install --no-dev --optimize-autoloader

# 2. Environment setup
cp .env.example .env.production
php artisan key:generate

# 3. Database setup
php artisan migrate --force
php artisan db:seed --force

# 4. Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 5. Set permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

#### Frontend Deployment
```bash
# 1. Install dependencies
npm ci

# 2. Build for production
npm run build

# 3. Start production server
npm start

# OR export static files
npm run export
```

### 🌐 Environment Variables

#### Production Backend (.env.production)
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-production-db-host
DB_PORT=3306
DB_DATABASE=production_database
DB_USERNAME=production_user
DB_PASSWORD=secure-password

SANCTUM_STATEFUL_DOMAINS=your-frontend-domain.com
SESSION_DOMAIN=.your-domain.com
```

#### Production Frontend (.env.production)
```env
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

---

## 🧪 Testing

### 🔬 Backend Testing

#### PHPUnit Tests
```php
// tests/Feature/AuthTest.php
public function test_user_can_login_with_valid_credentials()
{
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
    ]);

    $response = $this->postJson('/api/v1/login', [
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'data' => [
                     'user',
                     'access_token'
                 ]
             ]);
}
```

#### API Testing Commands
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --filter AuthTest

# Generate test coverage
php artisan test --coverage
```

### 🧪 Frontend Testing

#### Component Testing
```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### 🔍 Manual Testing Checklist

#### Authentication Flow
- [ ] User registration works for all user types
- [ ] Login/logout functionality
- [ ] Password reset flow
- [ ] Email verification

#### User Dashboards
- [ ] Student dashboard displays correctly
- [ ] Volunteer dashboard shows opportunities
- [ ] Partner dashboard has collaboration tools
- [ ] Admin dashboard shows analytics

#### API Endpoints
- [ ] All public endpoints return correct data
- [ ] Authentication middleware works
- [ ] Admin-only endpoints are protected
- [ ] Error handling is consistent

---

## 📖 Detailed Guides

### 📝 Detailed Documentation Files

For comprehensive information on specific topics, refer to these detailed guides:

- [**BACKEND_API_GUIDE.md**](./BACKEND_API_GUIDE.md) - Complete backend API documentation
- [**FRONTEND_COMPONENT_GUIDE.md**](./FRONTEND_COMPONENT_GUIDE.md) - Frontend components and patterns
- [**DATABASE_SCHEMA_GUIDE.md**](./DATABASE_SCHEMA_GUIDE.md) - Database structure and relationships
- [**AUTHENTICATION_GUIDE.md**](./AUTHENTICATION_GUIDE.md) - Authentication implementation details
- [**DEPLOYMENT_GUIDE.md**](./DEPLOYMENT_GUIDE.md) - Production deployment instructions
- [**TROUBLESHOOTING_GUIDE.md**](./TROUBLESHOOTING_GUIDE.md) - Common issues and solutions

### 🎯 NGO-Specific Features

#### User Types & Workflows
1. **Students**: Apply for scholarships, access educational resources
2. **Partners**: Collaborate on projects, share resources
3. **Volunteers**: Log hours, apply for opportunities, track progress
4. **Admins**: Manage all platform aspects, view analytics

#### Donation System
- Campaign-based donations
- Payment processing integration
- Receipt generation
- Donor analytics

#### Event Management
- Public event listings
- Registration system
- Attendance tracking
- Event analytics

#### Blog & Content
- Multi-category blog system
- Comment moderation
- SEO optimization
- Content analytics

---

## 🚀 Getting Help

### 📞 Support Channels

- **Documentation Issues**: Create GitHub issue
- **Feature Requests**: Use GitHub discussions
- **Technical Support**: Contact development team
- **Emergency Issues**: Contact project maintainer

### 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests
5. Submit a pull request

### 📚 Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Components](https://ui.shadcn.com)

---

## 📋 Quick Reference

### Most Used Commands
```bash
# Start development servers
php artisan serve && npm --prefix front-end run dev

# Reset database with fresh data
php artisan migrate:fresh --seed

# Clear all caches
php artisan optimize:clear

# Build frontend for production
cd front-end && npm run build
```

### Important File Locations
- **API Routes**: `routes/api.php`
- **Frontend Pages**: `front-end/app/`
- **Components**: `front-end/components/`
- **API Client**: `front-end/lib/api.ts`
- **Database Migrations**: `database/migrations/`
- **Seeders**: `database/seeders/`

---

> **🌟 Remember**: This platform serves the noble mission of educational empowerment in Nigeria. Always prioritize user experience and the NGO's goals in your development decisions!

**Happy Coding! 🚀** 