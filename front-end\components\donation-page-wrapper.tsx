"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { DonationForm } from "./donation-form"

export function DonationPageWrapper() {
  const searchParams = useSearchParams()
  const [alertMessage, setAlertMessage] = useState<{ type: 'error' | 'success', message: string } | null>(null)

  useEffect(() => {
    const error = searchParams.get('error')
    const success = searchParams.get('success')

    if (error === 'payment_failed') {
      setAlertMessage({
        type: 'error',
        message: 'Payment was not completed successfully. Please try again.'
      })
    } else if (success) {
      setAlertMessage({
        type: 'success',
        message: 'Thank you for your donation! Your payment has been processed successfully.'
      })
    }

    // Clear URL parameters after showing message
    if (error || success) {
      const timer = setTimeout(() => {
        window.history.replaceState({}, '', '/donate')
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [searchParams])

  return (
    <div className="space-y-6">
      {alertMessage && (
        <div className="container mx-auto px-4">
          <Alert className={`max-w-2xl mx-auto ${
            alertMessage.type === 'error' 
              ? 'border-red-200 bg-red-50' 
              : 'border-green-200 bg-green-50'
          }`}>
            {alertMessage.type === 'error' ? (
              <AlertCircle className="h-4 w-4 text-red-600" />
            ) : (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            )}
            <AlertDescription className={
              alertMessage.type === 'error' ? 'text-red-600' : 'text-green-600'
            }>
              {alertMessage.message}
            </AlertDescription>
          </Alert>
        </div>
      )}
      
      <DonationForm />
    </div>
  )
}
