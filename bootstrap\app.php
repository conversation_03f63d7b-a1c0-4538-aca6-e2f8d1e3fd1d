<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Add CORS middleware to API routes
        $middleware->api(prepend: [
            \Illuminate\Http\Middleware\HandleCors::class,
        ]);

        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'admin.auth' => \App\Http\Middleware\AdminAuthMiddleware::class,
            'partner.auth' => \App\Http\Middleware\PartnerOrganizationMiddleware::class,
            'student.auth' => \App\Http\Middleware\StudentMiddleware::class,
            'role.auth' => \App\Http\Middleware\RoleBasedApiAuth::class,
            'data.ownership' => \App\Http\Middleware\DataOwnershipAuth::class,
            'api.security' => \App\Http\Middleware\ApiSecurityMiddleware::class,
            'blocked.ip' => \App\Http\Middleware\BlockedIpMiddleware::class,
            'scholarship.category' => \App\Http\Middleware\ScholarshipCategoryAuth::class,
        ]);

        // Configure authentication redirects
        $middleware->redirectGuestsTo(function () {
            return route('admin.login');
        });
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
