<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            // Bank transfer specific fields
            $table->string('sender_name')->nullable()->after('payment_metadata');
            $table->string('sender_bank')->nullable()->after('sender_name');
            $table->string('receipt_file_path')->nullable()->after('sender_bank');
            $table->boolean('admin_approved')->default(false)->after('receipt_file_path');
            $table->timestamp('admin_approved_at')->nullable()->after('admin_approved');
            $table->unsignedBigInteger('approved_by')->nullable()->after('admin_approved_at');
            $table->text('admin_notes')->nullable()->after('approved_by');

            // Add foreign key for approved_by
            $table->foreign('approved_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('donations', function (Blueprint $table) {
            $table->dropForeign(['approved_by']);
            $table->dropColumn([
                'sender_name',
                'sender_bank',
                'receipt_file_path',
                'admin_approved',
                'admin_approved_at',
                'approved_by',
                'admin_notes'
            ]);
        });
    }
};
