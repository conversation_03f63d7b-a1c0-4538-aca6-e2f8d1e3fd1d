(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[953],{1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1366:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return i}});let s=r(8229),a=r(1264),n=r(3063),l=s._(r(1193));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=n.Image},1976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,c=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let n=a(t)||a(s);return l[e][n]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,c,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2157:(e,t,r)=>{Promise.resolve().then(r.bind(r,2715))},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2715:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(5155),a=r(6766),n=r(6874),l=r.n(n),i=r(8145),c=r(7168),d=r(8482),o=r(5695),u=r(2115),m=r(1154),x=r(7550),h=r(1007),p=r(9074),f=r(4186),g=r(2657);let v=(0,r(9946).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var y=r(1976),j=r(1366);function b(e){let{params:t}=e,r=(0,u.use)(t),[n,b]=(0,u.useState)(null),[N,w]=(0,u.useState)(!0),[k,A]=(0,u.useState)(null),_=async()=>{try{let e;w(!0),A(null);let t=await fetch("".concat("http://localhost:8000/api/v1","/blog/posts/").concat(r.slug));if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let s=await t.text();try{e=JSON.parse(s)}catch(e){throw console.error("Response was not valid JSON:",s.substring(0,200)),Error("Server returned invalid response")}e.success?b(e.post):A("Post not found")}catch(e){console.error("Error fetching post:",e),A("Failed to load post. Please check if the server is running.")}finally{w(!1)}};(0,u.useEffect)(()=>{_()},[r.slug]);let C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return N?(0,s.jsx)("main",{className:"flex-1 py-12 md:py-20 px-4 md:px-6",children:(0,s.jsx)("div",{className:"container mx-auto max-w-4xl",children:(0,s.jsxs)("div",{className:"flex items-center justify-center py-20",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 animate-spin text-green-600 mr-2"}),(0,s.jsx)("span",{className:"text-lg text-gray-600",children:"Loading article..."})]})})}):k&&!n?(0,s.jsx)("main",{className:"flex-1 py-12 md:py-20 px-4 md:px-6",children:(0,s.jsx)("div",{className:"container mx-auto max-w-4xl",children:(0,s.jsxs)("div",{className:"text-center bg-red-50 border border-red-200 rounded-lg p-8",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("svg",{className:"h-12 w-12 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h3",{className:"text-2xl font-semibold text-red-900 mb-2",children:"Unable to Load Article"}),(0,s.jsx)("p",{className:"text-red-700 mb-6",children:k}),(0,s.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,s.jsx)(c.$,{onClick:()=>{A(null),_()},className:"bg-red-600 hover:bg-red-700 text-white rounded-full px-6 py-2",children:"Try Again"}),(0,s.jsx)(l(),{href:"/blog",children:(0,s.jsx)(c.$,{variant:"outline",className:"rounded-full px-6 py-2",children:"Back to Blog"})})]})]})})}):(n||(0,o.notFound)(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("section",{className:"relative py-20 bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsxs)("div",{className:"container relative px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(l(),{href:"/blog",children:(0,s.jsxs)(c.$,{variant:"outline",className:"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white rounded-full",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back to Blog"]})})}),(0,s.jsxs)("div",{className:"text-center",children:[n.category&&(0,s.jsx)(i.E,{className:"bg-amber-500 hover:bg-amber-600 text-black font-semibold px-4 py-2 rounded-full text-sm mb-4",children:n.category.name}),(0,s.jsx)("h1",{className:"text-3xl md:text-5xl font-bold mb-6 leading-tight",children:n.title}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-6 text-green-100",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:n.author.avatar?(0,s.jsx)(a.default,{src:n.author.avatar,alt:n.author.name,width:40,height:40,className:"rounded-full"}):(0,s.jsx)(h.A,{className:"h-5 w-5 text-green-600"})}),(0,s.jsx)("span",{className:"font-medium",children:n.author.name})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:C(n.published_at)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:n.reading_time||"5 min read"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:[n.views," views"]})]})]})]})]})]}),n.featured_image_url&&(0,s.jsx)("section",{className:"py-8 bg-white dark:bg-gray-800",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"relative aspect-video rounded-2xl overflow-hidden shadow-2xl",children:(0,s.jsx)(a.default,{src:(e=>{if(e.featured_image_url)return e.featured_image_url;let t=["https://images.unsplash.com/photo-1529070538774-1843cb3265df","https://images.unsplash.com/photo-1593113598332-cd288d649433","https://images.unsplash.com/photo-1559027615-cd4628902d4a","https://images.unsplash.com/photo-1454165804606-c3d57bc86b40","https://images.unsplash.com/photo-1517048676732-d65bc937f952","https://images.unsplash.com/photo-1488521787991-ed7bbaae773c","https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca","https://images.unsplash.com/photo-1469571486292-0ba58a3f068b","https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a","https://images.unsplash.com/photo-1484712401471-05c7215830eb"],r=e.id%t.length;return t[r]+"?w=1200&h=600&fit=crop&crop=center"})(n),alt:n.title,fill:!0,className:"object-cover",sizes:"(max-width: 1024px) 100vw, 1024px"})})})}),(0,s.jsx)("section",{className:"py-12 bg-white dark:bg-gray-800",children:(0,s.jsx)("div",{className:"container px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-1",children:(0,s.jsxs)("div",{className:"sticky top-8 space-y-6",children:[(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-3",children:"Share this article"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(c.$,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,s.jsx)(v,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,s.jsxs)(c.$,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Like"]})]})]})}),(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-3",children:"About the Author"}),(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:n.author.avatar?(0,s.jsx)(a.default,{src:n.author.avatar,alt:n.author.name,width:48,height:48,className:"rounded-full"}):(0,s.jsx)(h.A,{className:"h-6 w-6 text-green-600"})}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:n.author.name})})]}),n.author.bio&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:n.author.bio})]})}),n.tags&&n.tags.length>0&&(0,s.jsx)(d.Zp,{children:(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-3",children:"Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:n.tags.map(e=>(0,s.jsx)(i.E,{variant:"outline",className:"text-xs",children:e},e))})]})})]})}),(0,s.jsxs)("div",{className:"lg:col-span-3 order-1 lg:order-2",children:[(0,s.jsxs)("article",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8",children:[(0,s.jsx)("div",{className:"prose prose-lg dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed",dangerouslySetInnerHTML:{__html:n.content.replace(/\n/g,"<br>").replace(/\n\n/g,"</p><p>")}}),(0,s.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Published on ",C(n.published_at)]}),void 0!==n.comments_count&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:[n.comments_count," comments"]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Like"]}),(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(v,{className:"h-4 w-4 mr-2"}),"Share"]})]})]})})]}),(0,s.jsxs)("div",{className:"mt-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"Related Articles"}),(0,s.jsx)("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:(0,s.jsx)("p",{children:"Related articles will be loaded here"})})]})]})]})})})]}))}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>n});var s=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return s.useCallback(n(...e),e)}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(1469),a=r.n(s)},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155),a=r(2115),n=r(9708),l=r(2085),i=r(3999);let c=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,...o}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(c({variant:a,size:l,className:r})),ref:t,suppressHydrationWarning:!0,...o})});d.displayName="Button"},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8145:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(5155);r(2115);var a=r(2085),n=r(3999);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(l({variant:r}),t),...a})}},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>i});var s=r(5155),a=r(2115),n=r(3999);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l});var s=r(2115),a=r(6101),n=r(5155),l=s.forwardRef((e,t)=>{let{children:r,...a}=e,l=s.Children.toArray(r),c=l.find(d);if(c){let e=c.props.children,r=l.map(t=>t!==c?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(i,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,n.jsx)(i,{...a,ref:t,children:r})});l.displayName="Slot";var i=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:t?(0,a.t)(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});i.displayName="SlotClone";var c=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});function d(e){return s.isValidElement(e)&&e.type===c}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,874,63,441,684,358],()=>t(2157)),_N_E=e.O()}]);