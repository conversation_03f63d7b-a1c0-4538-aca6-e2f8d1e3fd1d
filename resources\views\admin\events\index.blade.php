@extends('layouts.admin')

@section('title', 'Events Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Events Management</h1>
        <a href="{{ route('admin.events.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Event
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Events</div>
                            <div class="h4">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Upcoming Events</div>
                            <div class="h4">{{ $stats['upcoming'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Published Events</div>
                            <div class="h4">{{ $stats['published'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Draft Events</div>
                            <div class="h4">{{ $stats['draft'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Events</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search events..." id="searchEvents">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterType">
                        <option value="">All Types</option>
                        <option value="fundraising">Fundraising</option>
                        <option value="volunteer">Volunteer</option>
                        <option value="awareness">Awareness</option>
                        <option value="training">Training</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="ongoing">Ongoing</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="month" class="form-control" id="filterMonth" value="{{ now()->format('Y-m') }}">
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Event</th>
                            <th>Type</th>
                            <th>Date & Time</th>
                            <th>Location</th>
                            <th>Capacity</th>
                            <th>Registrations</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($events as $event)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($event->featured_image)
                                        <img src="{{ $event->featured_image }}" alt="{{ $event->title }}" class="rounded me-3" width="50" height="50" style="object-fit: cover;">
                                    @else
                                        <div class="bg-info rounded d-flex align-items-center justify-content-center me-3 text-white" style="width: 50px; height: 50px;">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <div class="fw-bold">{{ $event->title }}</div>
                                        <small class="text-muted">{{ Str::limit($event->description, 60) }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $event->event_type === 'fundraising' ? 'success' : 
                                    ($event->event_type === 'volunteer' ? 'primary' : 
                                    ($event->event_type === 'awareness' ? 'info' : 
                                    ($event->event_type === 'training' ? 'warning' : 'secondary')))
                                }}">
                                    {{ ucfirst($event->event_type) }}
                                </span>
                            </td>
                            <td>
                                <div class="small">
                                    <strong>{{ $event->start_datetime->format('M d, Y') }}</strong><br>
                                    {{ $event->start_datetime->format('g:i A') }}
                                    @if($event->end_datetime)
                                        - {{ $event->end_datetime->format('g:i A') }}
                                    @endif
                                </div>
                                @if($event->start_datetime->isToday())
                                    <span class="badge bg-danger">Today</span>
                                @elseif($event->start_datetime->isTomorrow())
                                    <span class="badge bg-warning">Tomorrow</span>
                                @elseif($event->start_datetime->isFuture())
                                    <span class="badge bg-success">{{ $event->start_datetime->diffForHumans() }}</span>
                                @else
                                    <span class="badge bg-secondary">Past</span>
                                @endif
                            </td>
                            <td>
                                @if($event->venue)
                                    <div class="small">
                                        <strong>{{ $event->venue }}</strong>
                                        @if($event->address)
                                            <br><span class="text-muted">{{ Str::limit($event->address, 40) }}</span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-muted">Online</span>
                                @endif
                            </td>
                            <td>
                                @if($event->capacity)
                                    <div class="text-center">
                                        <strong>{{ number_format($event->capacity) }}</strong>
                                    </div>
                                @else
                                    <span class="text-muted">Unlimited</span>
                                @endif
                            </td>
                            <td>
                                @php
                                    $registrations = $event->registrations ? $event->registrations->count() : 0;
                                    $percentage = $event->capacity ? ($registrations / $event->capacity) * 100 : 0;
                                @endphp
                                <div class="text-center">
                                    <strong class="text-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}">
                                        {{ $registrations }}
                                    </strong>
                                    @if($event->capacity)
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}" 
                                                 style="width: {{ min(100, $percentage) }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ round($percentage) }}%</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($event->start_datetime->isFuture())
                                    <span class="badge bg-success">Upcoming</span>
                                @elseif($event->start_datetime->isToday() || ($event->end_datetime && $event->end_datetime->isFuture()))
                                    <span class="badge bg-warning">Ongoing</span>
                                @else
                                    <span class="badge bg-secondary">Completed</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('event.show', $event->id) }}" target="_blank" class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="{{ route('admin.events.show', $event->id) }}" class="btn btn-sm btn-outline-primary" title="Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.events.registrations', $event->id) }}" class="btn btn-sm btn-outline-success" title="Registrations">
                                        <i class="fas fa-users"></i>
                                    </a>
                                    <a href="{{ route('admin.events.edit', $event->id) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger delete-event-btn"
                                            data-event-id="{{ $event->id }}"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No events found</p>
                                <a href="{{ route('admin.events.create') }}" class="btn btn-primary">Create Your First Event</a>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $events->firstItem() }} to {{ $events->lastItem() }} of {{ $events->total() }} results
                </div>
                {{ $events->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteEventModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this event? This action cannot be undone and will cancel all registrations.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteEvent">Delete Event</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete event functionality
    const deleteEventButtons = document.querySelectorAll('.delete-event-btn');
    deleteEventButtons.forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.getAttribute('data-event-id');
            deleteEvent(eventId);
        });
    });
});

function deleteEvent(eventId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteEventModal'));
    modal.show();

    document.getElementById('confirmDeleteEvent').addEventListener('click', function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/events/${eventId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();

        modal.hide();
    };
}

// Search functionality
document.getElementById('searchEvents').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterType').addEventListener('change', function() {
    console.log('Filter type:', this.value);
});

document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterMonth').addEventListener('change', function() {
    console.log('Filter month:', this.value);
});
</script>
@endsection 