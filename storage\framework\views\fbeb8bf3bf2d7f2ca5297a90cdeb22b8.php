
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => '',
    'value' => '',
    'icon' => '',
    'color' => 'blue',
    'trend' => null,
    'trendDirection' => 'up'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => '',
    'value' => '',
    'icon' => '',
    'color' => 'blue',
    'trend' => null,
    'trendDirection' => 'up'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between">
        <div>
            <p class="text-sm font-medium text-gray-600"><?php echo e($title); ?></p>
            <p class="text-3xl font-bold text-<?php echo e($color); ?>-600"><?php echo e($value); ?></p>
            <?php if($trend): ?>
                <div class="flex items-center mt-2">
                    <?php if($trendDirection === 'up'): ?>
                        <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                        <span class="text-sm text-green-600">+<?php echo e($trend); ?></span>
                    <?php else: ?>
                        <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                        <span class="text-sm text-red-600">-<?php echo e($trend); ?></span>
                    <?php endif; ?>
                    <span class="text-sm text-gray-500 ml-1">from last month</span>
                </div>
            <?php endif; ?>
        </div>
        <?php if($icon): ?>
            <div class="p-3 bg-<?php echo e($color); ?>-100 rounded-full">
                <i class="fas fa-<?php echo e($icon); ?> text-<?php echo e($color); ?>-600 text-2xl"></i>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/components/dashboard/stat-card.blade.php ENDPATH**/ ?>