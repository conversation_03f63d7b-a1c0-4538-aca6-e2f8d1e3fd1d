{{-- Dashboard Card Component --}}
@props([
    'title' => '',
    'subtitle' => '',
    'icon' => '',
    'iconColor' => 'blue',
    'href' => null,
    'badge' => null,
    'badgeColor' => 'blue'
])

<div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 {{ $href ? 'cursor-pointer' : '' }}"
     @if($href) onclick="window.location.href='{{ $href }}'" @endif>
    <div class="p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                @if($icon)
                    <div class="p-3 bg-{{ $iconColor }}-100 rounded-full">
                        <i class="fas fa-{{ $icon }} text-{{ $iconColor }}-600 text-xl"></i>
                    </div>
                @endif
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">{{ $title }}</h3>
                    @if($subtitle)
                        <p class="text-sm text-gray-600">{{ $subtitle }}</p>
                    @endif
                </div>
            </div>
            @if($badge)
                <span class="px-3 py-1 text-xs font-medium bg-{{ $badgeColor }}-100 text-{{ $badgeColor }}-800 rounded-full">
                    {{ $badge }}
                </span>
            @endif
        </div>
        
        @if($slot->isNotEmpty())
            <div class="mt-4">
                {{ $slot }}
            </div>
        @endif
        
        @if($href)
            <div class="mt-4">
                <span class="text-{{ $iconColor }}-600 hover:text-{{ $iconColor }}-800 font-medium">
                    View Details →
                </span>
            </div>
        @endif
    </div>
</div>
