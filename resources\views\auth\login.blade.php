@extends('auth.layout')

@section('title', 'Login')
@section('header-title', 'Welcome Back')
@section('header-subtitle', 'Sign in to your HALIMAKQ FOUNDATION account')

@section('content')
    @if ($errors->any())
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                <h6 class="text-red-800 font-medium">Login Failed</h6>
            </div>
            <ul class="text-red-700 text-sm space-y-1">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if (session('success'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                <span class="text-green-800 text-sm">{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if (session('error'))
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                <span class="text-red-800 text-sm">{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <form method="POST" action="{{ route('login') }}" class="space-y-6">
        @csrf

        <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address <span class="text-red-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <input type="email"
                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('email') border-red-300 @enderror"
                       id="email" name="email" value="{{ old('email') }}" required autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                Password <span class="text-red-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password"
                       class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('password') border-red-300 @enderror"
                       id="password" name="password" required
                       placeholder="Enter your password">
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" id="togglePassword">
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="toggleIcon"></i>
                </button>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                       type="checkbox" id="remember" name="remember"
                       {{ old('remember') ? 'checked' : '' }}>
                <label class="ml-2 block text-sm text-gray-700" for="remember">
                    Remember me
                </label>
            </div>
            <div>
                <a href="{{ route('password.request') }}" class="text-sm text-green-600 hover:text-green-500 transition-colors duration-200">
                    Forgot password?
                </a>
            </div>
        </div>

        <div>
            <button type="submit" class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <i class="fas fa-sign-in-alt mr-2"></i>Sign In
            </button>
        </div>

        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">or</span>
            </div>
        </div>

        <div>
            <a href="{{ route('register') }}" class="w-full border border-green-600 text-green-600 hover:bg-green-50 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-user-plus mr-2"></i>Create New Account
            </a>
        </div>

        <div class="text-center pt-4 border-t border-gray-200">
            <p class="text-gray-600 text-sm mb-2">Need help?</p>
            <a href="mailto:support@{{ strtolower(str_replace(' ', '', $appName)) }}.org" class="text-green-600 hover:text-green-500 text-sm transition-colors duration-200">Contact Support</a>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        if (type === 'text') {
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    });
});
</script>
@endpush
