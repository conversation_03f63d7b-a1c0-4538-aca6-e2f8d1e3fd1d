@extends('auth.layout')

@section('title', 'Login')
@section('header-title', 'Welcome Back')
@section('header-subtitle', 'Sign in to your HALIMAKQ NGO account')

@section('content')
    @if ($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Login Failed</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if (session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ session('error') }}
        </div>
    @endif

    <form method="POST" action="{{ route('login') }}">
        @csrf
        
        <div class="mb-3">
            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-envelope text-muted"></i>
                </span>
                <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email') }}" required autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input type="password" class="form-control border-start-0 @error('password') is-invalid @enderror" 
                       id="password" name="password" required
                       placeholder="Enter your password">
                <button type="button" class="btn btn-outline-secondary border-start-0" id="togglePassword">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember" 
                           {{ old('remember') ? 'checked' : '' }}>
                    <label class="form-check-label" for="remember">
                        Remember me
                    </label>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <a href="{{ route('password.request') }}" class="text-decoration-none">
                    Forgot password?
                </a>
            </div>
        </div>

        <div class="d-grid mb-3">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </button>
        </div>

        <div class="text-center mb-3">
            <span class="text-muted">or</span>
        </div>

        <div class="d-grid">
            <a href="{{ route('register') }}" class="btn btn-outline-primary">
                <i class="fas fa-user-plus me-2"></i>Create New Account
            </a>
        </div>

        <div class="auth-links">
            <p class="text-muted mb-2">Need help?</p>
            <a href="mailto:<EMAIL>">Contact Support</a>
        </div>
    </form>
@endsection

@push('styles')
<style>
    .input-group-text {
        border-color: #e5e7eb;
    }
    
    .form-control.border-start-0 {
        border-left: none !important;
    }
    
    .form-control.border-start-0:focus {
        border-left: none !important;
        box-shadow: none;
    }
    
    .input-group:focus-within .input-group-text {
        border-color: var(--primary-green);
        background-color: rgba(5, 150, 105, 0.05);
    }
    
    .input-group:focus-within .form-control {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
    
    .btn-outline-secondary {
        border-color: #e5e7eb;
        color: #6b7280;
    }
    
    .btn-outline-secondary:hover {
        background-color: #f9fafb;
        border-color: #d1d5db;
        color: #374151;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        if (type === 'text') {
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    });
});
</script>
@endpush
