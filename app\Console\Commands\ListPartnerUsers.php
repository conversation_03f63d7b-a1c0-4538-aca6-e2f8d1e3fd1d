<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class ListPartnerUsers extends Command
{
    protected $signature = 'users:list-partners';
    protected $description = 'List all partner organization users';

    public function handle()
    {
        $users = User::where('role', 'partner_organization')->get();
        
        $this->info('Partner Organization Users:');
        $this->info('========================');
        
        foreach ($users as $user) {
            $this->line("ID: {$user->id}");
            $this->line("Name: {$user->first_name} {$user->last_name}");
            $this->line("Email: {$user->email}");
            $this->line("Partner Org ID: {$user->partner_organization_id}");
            $this->line("Status: {$user->status}");
            $this->line("---");
        }
        
        $this->info("Total: " . $users->count());
    }
}
