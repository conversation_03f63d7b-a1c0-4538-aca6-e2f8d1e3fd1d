import { useState } from 'react';
import { useAuth } from './use-auth';
import axios from 'axios';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

export const useSessionAuth = () => {
  const { setUser, setLoading, setCsrfToken, logout } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const login = async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      setError(null);

      // Get CSRF token first
      const csrfResponse = await axios.get('/sanctum/csrf-cookie');
      const csrfToken = csrfResponse.headers['x-csrf-token'];
      setCsrfToken(csrfToken);

      // Attempt login
      const response = await axios.post('/api/auth/login', credentials, {
        headers: {
          'X-CSRF-TOKEN': csrfToken,
        },
        withCredentials: true,
      });

      setUser(response.data.user);
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setLoading(true);
      setError(null);

      // Get CSRF token first
      const csrfResponse = await axios.get('/sanctum/csrf-cookie');
      const csrfToken = csrfResponse.headers['x-csrf-token'];
      setCsrfToken(csrfToken);

      // Attempt registration
      const response = await axios.post('/api/auth/register', data, {
        headers: {
          'X-CSRF-TOKEN': csrfToken,
        },
        withCredentials: true,
      });

      setUser(response.data.user);
      return response.data;
    } catch (err: any) {
      setError(err.response?.data?.message || 'Registration failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logoutUser = async () => {
    try {
      setLoading(true);
      await axios.post('/api/auth/logout', {}, {
        headers: {
          'X-CSRF-TOKEN': useAuth.getState().csrfToken,
        },
        withCredentials: true,
      });
      logout();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Logout failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const checkAuth = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/user/profile', {
        withCredentials: true,
      });
      setUser(response.data);
      return response.data;
    } catch (err) {
      logout();
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    login,
    register,
    logout: logoutUser,
    checkAuth,
    error,
  };
}; 