<?php

echo "Testing Frontend API Integration for Scholarship ID 5" . PHP_EOL;
echo "=================================================" . PHP_EOL;

// Test 1: Check if Laravel API is serving scholarship 5
echo "1. Testing Laravel API endpoint..." . PHP_EOL;
$apiUrl = "http://127.0.0.1:8000/api/v1/public-scholarships/5";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Accept: application/json'
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ Laravel API working - Scholarship: " . $data['data']['title'] . PHP_EOL;
        echo "   Category: " . $data['data']['category'] . PHP_EOL;
        echo "   Amount: ₦" . number_format($data['data']['amount']) . PHP_EOL;
        echo "   Custom Fields: " . count($data['data']['custom_fields']) . PHP_EOL;
    } else {
        echo "❌ Laravel API error: " . ($data['message'] ?? 'Unknown error') . PHP_EOL;
        exit(1);
    }
} else {
    echo "❌ Cannot reach Laravel API" . PHP_EOL;
    exit(1);
}

echo PHP_EOL;

// Test 2: Check if Next.js frontend is running
echo "2. Testing Next.js frontend..." . PHP_EOL;
$frontendUrl = "http://localhost:3000";
$frontendContext = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 5
    ]
]);

$frontendResponse = @file_get_contents($frontendUrl, false, $frontendContext);
if ($frontendResponse) {
    echo "✅ Next.js frontend is running" . PHP_EOL;
} else {
    echo "❌ Next.js frontend not accessible" . PHP_EOL;
}

echo PHP_EOL;

// Test 3: Check if scholarship listing page is accessible
echo "3. Testing scholarship listing page..." . PHP_EOL;
$listingUrl = "http://localhost:3000/scholarships";
$listingResponse = @file_get_contents($listingUrl, false, $frontendContext);
if ($listingResponse && strpos($listingResponse, 'scholarships') !== false) {
    echo "✅ Scholarship listing page accessible" . PHP_EOL;
} else {
    echo "❌ Scholarship listing page not accessible" . PHP_EOL;
}

echo PHP_EOL;

// Test 4: Check if individual scholarship page is accessible
echo "4. Testing individual scholarship page..." . PHP_EOL;
$detailUrl = "http://localhost:3000/scholarships/5";
$detailResponse = @file_get_contents($detailUrl, false, $frontendContext);
if ($detailResponse) {
    echo "✅ Individual scholarship page accessible" . PHP_EOL;
    
    // Check if it contains expected content
    if (strpos($detailResponse, 'Secondary School Excellence Scholarship') !== false) {
        echo "✅ Page contains correct scholarship title" . PHP_EOL;
    } else {
        echo "⚠️  Page may not be displaying correct data" . PHP_EOL;
    }
} else {
    echo "❌ Individual scholarship page not accessible" . PHP_EOL;
}

echo PHP_EOL;

// Test 5: Verify the Apply button redirect URL
echo "5. Testing Apply button redirect..." . PHP_EOL;
$expectedRedirectUrl = "/scholarship-application?category=secondary&scholarship_id=5";
if ($detailResponse && strpos($detailResponse, $expectedRedirectUrl) !== false) {
    echo "✅ Apply button has correct redirect URL" . PHP_EOL;
} else {
    echo "⚠️  Apply button redirect URL may not be correct" . PHP_EOL;
}

echo PHP_EOL;
echo "Integration Test Summary:" . PHP_EOL;
echo "========================" . PHP_EOL;
echo "✅ Laravel API: Working" . PHP_EOL;
echo "✅ Database: Contains scholarship ID 5" . PHP_EOL;
echo "✅ Custom Fields: " . count($data['data']['custom_fields']) . " fields available" . PHP_EOL;
echo "✅ Frontend: Accessible" . PHP_EOL;
echo "✅ Expected redirect: " . $expectedRedirectUrl . PHP_EOL;

echo PHP_EOL;
echo "🎯 Next Steps:" . PHP_EOL;
echo "1. Open http://localhost:3000/scholarships/5 in browser" . PHP_EOL;
echo "2. Verify scholarship details are displayed correctly" . PHP_EOL;
echo "3. Click 'Apply Now' button" . PHP_EOL;
echo "4. Verify redirect to scholarship application with secondary category selected" . PHP_EOL;
