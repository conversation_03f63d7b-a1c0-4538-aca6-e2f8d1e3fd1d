"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./components/ui/alert-dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _student_page__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./student/page */ \"(app-pages-browser)/./app/dashboard/student/page.tsx\");\n/* harmony import */ var _volunteer_page__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./volunteer/page */ \"(app-pages-browser)/./app/dashboard/volunteer/page.tsx\");\n/* harmony import */ var _partner_page__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./partner/page */ \"(app-pages-browser)/./app/dashboard/partner/page.tsx\");\n/* harmony import */ var _user_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./user/page */ \"(app-pages-browser)/./app/dashboard/user/page.tsx\");\n/* harmony import */ var _admin_page__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./admin/page */ \"(app-pages-browser)/./app/dashboard/admin/page.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Dashboard components for different user types\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [logoutLoading, setLogoutLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings)();\n    // Get app name from settings or use default\n    const appName = (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO';\n    const appLogo = settings === null || settings === void 0 ? void 0 : settings.app_logo;\n    const handleLogout = async ()=>{\n        try {\n            setLogoutLoading(true);\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.logout)();\n            router.push('/auth/login');\n        } catch (error) {\n            console.error('Logout failed:', error);\n            // Even if API logout fails, clear local storage and redirect\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            router.push('/auth/login');\n        } finally{\n            setLogoutLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Check if user is authenticated\n            if (!(0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                console.log('User not authenticated, redirecting to login');\n                router.push('/auth/login');\n                return;\n            }\n            // Get user data from localStorage first\n            const currentUser = (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                console.log('Current user from localStorage:', currentUser);\n                // Check if partner organization should be redirected to /dashboard/partner\n                if (currentUser.role === 'partner_organization' || currentUser.partner_organization_id) {\n                    console.log('Partner organization detected, redirecting to /dashboard/partner');\n                    router.push('/dashboard/partner');\n                    return;\n                }\n                setUser(currentUser);\n            }\n            // Fetch updated user profile from API\n            const fetchUserProfile = {\n                \"DashboardPage.useEffect.fetchUserProfile\": async ()=>{\n                    try {\n                        console.log('Fetching user profile from API...');\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getProfile();\n                        console.log('Profile API response:', response);\n                        if (response.success && response.user) {\n                            // Handle the correct response structure from Laravel backend\n                            const userData = response.user;\n                            console.log('Extracted user data:', userData);\n                            // Check if partner organization should be redirected to /dashboard/partner\n                            if (userData.role === 'partner_organization' || userData.partner_organization_id) {\n                                console.log('Partner organization detected from API, redirecting to /dashboard/partner');\n                                router.push('/dashboard/partner');\n                                return;\n                            }\n                            setUser(userData);\n                            // Update localStorage with fresh data\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else if (response.success && response.data) {\n                            // Fallback for different response structure\n                            const userData = response.data.user || response.data;\n                            console.log('Extracted user data (fallback):', userData);\n                            // Check if partner organization should be redirected to /dashboard/partner\n                            if (userData.role === 'partner_organization' || userData.partner_organization_id) {\n                                console.log('Partner organization detected from API (fallback), redirecting to /dashboard/partner');\n                                router.push('/dashboard/partner');\n                                return;\n                            }\n                            setUser(userData);\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else {\n                            console.error('Failed to fetch profile - invalid response structure:', response);\n                            // If profile fetch fails but we have cached user, continue with cached data\n                            if (!currentUser) {\n                                router.push('/auth/login');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch user profile:', error);\n                        // If API call fails but we have cached user, continue with cached data\n                        if (!currentUser) {\n                            router.push('/auth/login');\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchUserProfile\"];\n            fetchUserProfile();\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Unable to load user data. Please try logging in again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    // Route to appropriate dashboard based on user role and preferences\n    const getUserType = ()=>{\n        var _user_preferences;\n        // Check user preferences first (from registration)\n        if ((_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.user_type) {\n            return user.preferences.user_type;\n        }\n        // Fall back to role-based routing\n        if (user.role === 'partner_organization') {\n            return 'partner';\n        } else if (user.role === 'volunteer') {\n            return 'volunteer';\n        } else if (user.role === 'admin') {\n            return 'admin';\n        } else if (user.role === 'student') {\n            return 'student';\n        } else {\n            // For regular users, check if they have specific preferences\n            // If user has partner_organization_id, they're linked to an institution\n            if (user.partner_organization_id) {\n                return 'partner';\n            }\n            return 'user';\n        }\n    };\n    const userType = getUserType();\n    // Add a header with logout functionality\n    const DashboardHeader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white border-b border-gray-200 px-4 py-3 sm:px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: appLogo,\n                                alt: \"\".concat(appName, \" Logo\"),\n                                className: \"h-8 w-auto object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"absolute inset-0 m-auto h-5 w-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                children: [\n                                    appName,\n                                    \" Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:block text-sm text-gray-600\",\n                                children: [\n                                    \"Welcome, \",\n                                    user.first_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Visit Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                    children: [\n                                                        user.first_name.charAt(0),\n                                                        user.last_name.charAt(0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: user.first_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-56\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-sm font-medium\",\n                                                children: [\n                                                    user.first_name,\n                                                    \" \",\n                                                    user.last_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-xs text-gray-500\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                onClick: ()=>{\n                                                    // Navigate to profile settings (we'll implement this)\n                                                    const element = document.querySelector('[data-value=\"profile\"]');\n                                                    if (element) element.click();\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialog, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                            onSelect: (e)=>e.preventDefault(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Logout\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTitle, {\n                                                                        children: \"Are you sure you want to logout?\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogDescription, {\n                                                                        children: \"You will need to login again to access your dashboard.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogCancel, {\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogAction, {\n                                                                        onClick: handleLogout,\n                                                                        disabled: logoutLoading,\n                                                                        className: \"bg-red-600 hover:bg-red-700\",\n                                                                        children: logoutLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Logging out...\"\n                                                                            ]\n                                                                        }, void 0, true) : 'Logout'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, this);\n    // Render the appropriate dashboard component with header\n    const renderDashboard = ()=>{\n        switch(userType){\n            case 'student':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_student_page__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 16\n                }, this);\n            case 'volunteer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_volunteer_page__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, this);\n            case 'partner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_partner_page__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 16\n                }, this);\n            case 'admin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_admin_page__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_page__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardHeader, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            renderDashboard()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"+k7tylyzCuhPf6WaDKLSmdv9BFI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});