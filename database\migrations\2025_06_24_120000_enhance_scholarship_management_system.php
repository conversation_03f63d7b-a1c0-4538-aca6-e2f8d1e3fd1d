<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update scholarships table with additional fields if they don't exist
        Schema::table('scholarships', function (Blueprint $table) {
            // Add created_by field to track who created the scholarship
            if (!Schema::hasColumn('scholarships', 'created_by')) {
                $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null')->after('contact_email');
            }
            
            // Add updated_by field to track who last updated the scholarship
            if (!Schema::hasColumn('scholarships', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null')->after('created_by');
            }
            
            // Add application_start_date field
            if (!Schema::hasColumn('scholarships', 'application_start_date')) {
                $table->date('application_start_date')->nullable()->after('application_deadline');
            }
            
            // Add featured field for highlighting important scholarships
            if (!Schema::hasColumn('scholarships', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('is_open');
            }
            
            // Add application instructions field
            if (!Schema::hasColumn('scholarships', 'application_instructions')) {
                $table->text('application_instructions')->nullable()->after('eligibility_criteria');
            }
            
            // Add terms and conditions field
            if (!Schema::hasColumn('scholarships', 'terms_conditions')) {
                $table->text('terms_conditions')->nullable()->after('application_instructions');
            }
            
            // Add notification settings
            if (!Schema::hasColumn('scholarships', 'notification_settings')) {
                $table->json('notification_settings')->nullable()->after('custom_fields');
            }
        });

        // Update scholarship_applications table with additional fields
        Schema::table('scholarship_applications', function (Blueprint $table) {
            // Add priority field for application ranking
            if (!Schema::hasColumn('scholarship_applications', 'priority')) {
                $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium')->after('status');
            }
            
            // Add score field for application evaluation
            if (!Schema::hasColumn('scholarship_applications', 'score')) {
                $table->decimal('score', 5, 2)->nullable()->after('priority');
            }
            
            // Add award_amount field if it doesn't exist
            if (!Schema::hasColumn('scholarship_applications', 'award_amount')) {
                $table->decimal('award_amount', 10, 2)->nullable()->after('score');
            }
            
            // Add review_notes field if it doesn't exist (renamed from admin_notes)
            if (!Schema::hasColumn('scholarship_applications', 'review_notes')) {
                $table->text('review_notes')->nullable()->after('award_amount');
            }
            
            // Add internal_notes field for admin-only notes
            if (!Schema::hasColumn('scholarship_applications', 'internal_notes')) {
                $table->text('internal_notes')->nullable()->after('review_notes');
            }
            
            // Add notification_sent field to track email notifications
            if (!Schema::hasColumn('scholarship_applications', 'notification_sent')) {
                $table->boolean('notification_sent')->default(false)->after('internal_notes');
            }
            
            // Add additional indexes for better performance
            if (!Schema::hasIndex('scholarship_applications', ['status', 'priority'])) {
                $table->index(['status', 'priority']);
            }
            
            if (!Schema::hasIndex('scholarship_applications', ['scholarship_id', 'status'])) {
                $table->index(['scholarship_id', 'status']);
            }
        });

        // Create scholarship_categories table for better category management
        if (!Schema::hasTable('scholarship_categories')) {
            Schema::create('scholarship_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name', 100)->unique();
                $table->string('slug', 120)->unique();
                $table->text('description')->nullable();
                $table->json('default_fields')->nullable(); // Default fields for this category
                $table->json('validation_rules')->nullable(); // Category-specific validation rules
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
                
                $table->index(['is_active', 'sort_order']);
            });
        }

        // Create scholarship_application_files table for better file management
        if (!Schema::hasTable('scholarship_application_files')) {
            Schema::create('scholarship_application_files', function (Blueprint $table) {
                $table->id();
                $table->foreignId('application_id')->constrained('scholarship_applications')->onDelete('cascade');
                $table->string('field_name', 100); // Which field this file belongs to
                $table->string('original_name', 255);
                $table->string('stored_name', 255);
                $table->string('file_path', 500);
                $table->string('mime_type', 100);
                $table->bigInteger('file_size'); // in bytes
                $table->string('file_hash', 64)->nullable(); // For duplicate detection
                $table->boolean('is_verified')->default(false); // Admin verification status
                $table->text('admin_notes')->nullable();
                $table->timestamps();
                
                $table->index(['application_id', 'field_name']);
                $table->index('file_hash');
            });
        }

        // Create scholarship_templates table for reusable scholarship configurations
        if (!Schema::hasTable('scholarship_templates')) {
            Schema::create('scholarship_templates', function (Blueprint $table) {
                $table->id();
                $table->string('name', 200);
                $table->string('category', 50);
                $table->text('description')->nullable();
                $table->json('default_fields'); // Template fields configuration
                $table->json('validation_rules')->nullable();
                $table->json('settings')->nullable(); // Additional template settings
                $table->boolean('is_active')->default(true);
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->timestamps();
                
                $table->index(['category', 'is_active']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop new tables
        Schema::dropIfExists('scholarship_templates');
        Schema::dropIfExists('scholarship_application_files');
        Schema::dropIfExists('scholarship_categories');
        
        // Remove added columns from scholarship_applications
        Schema::table('scholarship_applications', function (Blueprint $table) {
            $table->dropIndex(['scholarship_id', 'status']);
            $table->dropIndex(['status', 'priority']);
            $table->dropColumn([
                'priority', 'score', 'award_amount', 'review_notes', 
                'internal_notes', 'notification_sent'
            ]);
        });
        
        // Remove added columns from scholarships
        Schema::table('scholarships', function (Blueprint $table) {
            $table->dropForeign(['updated_by']);
            $table->dropForeign(['created_by']);
            $table->dropColumn([
                'created_by', 'updated_by', 'application_start_date', 
                'is_featured', 'application_instructions', 'terms_conditions',
                'notification_settings'
            ]);
        });
    }
};
