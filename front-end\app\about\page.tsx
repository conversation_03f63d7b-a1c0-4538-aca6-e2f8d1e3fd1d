"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import { Heart, Users, Target, Award, Calendar, MapPin } from "lucide-react"
import { useSettings } from "@/hooks/useSettings"

const trustees = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Founder & Chairperson",
    tenure: "2015–Present",
    quote: "Education is the light that dispels the darkness of poverty.",
    bio: "Visionary leader who started the foundation with a mission to support 12 orphans in Zaria. Under her leadership, the foundation has grown to impact thousands of lives across Nigeria.",
  },
  {
    name: "Dr. <PERSON><PERSON>",
    role: "Trustee",
    tenure: "2020–2025",
    quote: "Every child deserves access to quality education regardless of their background.",
    bio: "Professor of Education at Ahmadu Bello University with over 20 years of experience in educational development and policy formulation.",
  },
]

const coreValues = [
  {
    icon: <Heart className="h-8 w-8 text-red-500" />,
    title: "Transparency",
    description: "We maintain open and honest communication about our operations, finances, and impact.",
  },
  {
    icon: <Users className="h-8 w-8 text-blue-500" />,
    title: "Compassion",
    description: "We approach our work with empathy and understanding for the communities we serve.",
  },
  {
    icon: <Target className="h-8 w-8 text-green-500" />,
    title: "Sustainable Impact",
    description: "We focus on creating long-term solutions that empower communities to thrive independently.",
  },
]

export default function AboutPage() {
  const { settings, loading: settingsLoading } = useSettings()
  
  // Get dynamic settings or use defaults
  const appName = settings?.app_name || 'Laravel NGO Foundation'
  const siteDescription = settings?.site_description || 'Making a difference in communities through sustainable development programs, education initiatives, and empowerment projects.'

  return (
    <div className="bg-gray-50 dark:bg-gray-900">

      <main>
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">
                About {appName}
              </Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Our Story & Mission</h1>
              <p className="text-xl text-green-100 max-w-3xl mx-auto">
                {siteDescription}
              </p>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Our Journey</h2>
                <p className="text-gray-600 dark:text-gray-400 text-lg">
                  From humble beginnings to transforming thousands of lives across communities
                </p>
              </div>

              <Card className="overflow-hidden shadow-xl rounded-2xl">
                <CardContent className="p-8 md:p-12">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div className="space-y-6">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-6 w-6 text-green-600" />
                        <span className="text-lg font-semibold text-green-800 dark:text-green-200">
                          Founded in 2015
                        </span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        Founded in 2015 by Hajiya Halimatu Abdullahi Kofa, we began by supporting 12 orphans in Zaria.
                        What started as a small act of compassion has grown into a comprehensive educational support
                        system.
                      </p>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        Today, we operate across all 36 states in Nigeria, guided by our core values of Transparency,
                        Compassion, and Sustainable Impact. Our work has touched the lives of over 5,000 students and
                        continues to grow each year.
                      </p>
                      <div className="flex items-center gap-3">
                        <MapPin className="h-6 w-6 text-green-600" />
                        <span className="text-lg font-semibold text-green-800 dark:text-green-200">
                          Operating Nationwide across Nigeria
                        </span>
                      </div>
                    </div>
                    <div className="relative">
                      <div className="aspect-square rounded-2xl overflow-hidden shadow-lg">
                        <Image
                          src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400&h=400&fit=crop"
                          alt="Kofa Foundation journey"
                          width={400}
                          height={400}
                          className="object-cover w-full h-full"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Core Values */}
        <section className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Our Core Values</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                These principles guide everything we do and shape our approach to community development
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {coreValues.map((value, index) => (
                <Card
                  key={index}
                  className="text-center overflow-hidden border-green-100 dark:border-green-800 transition-all duration-300 hover:shadow-lg hover:-translate-y-2 rounded-2xl group"
                >
                  <CardContent className="p-8">
                    <div className="flex justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      {value.icon}
                    </div>
                    <h3 className="text-xl font-semibold mb-4 text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-gray-300 transition-colors duration-300">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Leadership Team */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Meet Our Leadership</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                Dedicated leaders committed to transforming lives through education
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {trustees.map((trustee, index) => (
                <Card
                  key={index}
                  className="overflow-hidden border-green-100 dark:border-green-800 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 rounded-2xl group"
                >
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div className="relative mx-auto w-32 h-32">
                        <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-700 rounded-full opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300"></div>
                        <div className="relative w-full h-full rounded-full overflow-hidden shadow-lg">
                          <div className="w-full h-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center">
                            <span className="text-3xl font-bold text-white">{trustee.name.charAt(0)}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                          {trustee.name}
                        </h3>
                        <p className="text-green-600 dark:text-green-400 font-medium">{trustee.role}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{trustee.tenure}</p>
                      </div>

                      <blockquote className="text-gray-600 dark:text-gray-400 italic border-l-4 border-green-500 pl-4 group-hover:text-gray-900 dark:group-hover:text-gray-300 transition-colors duration-300">
                        "{trustee.quote}"
                      </blockquote>

                      <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed group-hover:text-gray-900 dark:group-hover:text-gray-300 transition-colors duration-300">
                        {trustee.bio}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Registration Info */}
        <section className="py-20 bg-green-900 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Legal & Transparency</h2>
                <p className="text-green-100 text-lg">
                  We operate with full transparency and accountability to our donors and beneficiaries
                </p>
              </div>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 rounded-2xl">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Award className="h-6 w-6 text-amber-400" />
                        <h3 className="text-xl font-semibold">Registration Details</h3>
                      </div>
                      <p className="text-green-100">
                        Halimatu Abdullahi Kofa Knowledge Quest Foundation is a registered NGO with the Corporate
                        Affairs Commission.
                      </p>
                      <p className="text-sm text-green-200">CAC Registration: CAC/IT/NO 123456</p>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Heart className="h-6 w-6 text-amber-400" />
                        <h3 className="text-xl font-semibold">Fund Usage</h3>
                      </div>
                      <p className="text-green-100">
                        All funds are used strictly per Article 13 of our constitution, ensuring maximum impact for our
                        beneficiaries.
                      </p>
                      <p className="text-sm text-green-200">Financial reports available upon request</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>

    </div>
  )
}
