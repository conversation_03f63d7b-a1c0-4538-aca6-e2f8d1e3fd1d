// Test file to verify constants are working
const { API_BASE_URL, BACKEND_BASE_URL } = require('./lib/constants.ts');

console.log('API_BASE_URL:', API_BASE_URL);
console.log('BACKEND_BASE_URL:', BACKEND_BASE_URL);

// Test API connection
fetch(`${API_BASE_URL}/public-scholarships`)
  .then(response => {
    console.log('API Response Status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('API Response Success:', data.success);
    console.log('Number of scholarships:', data.data?.data?.length || 0);
  })
  .catch(error => {
    console.error('API Error:', error.message);
  });
