"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface ProjectCardProps {
  image: string
  title: string
  description: string
  location: string
}

export function ProjectCard({ image, title, description, location }: ProjectCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false)

  return (
    <Card className="overflow-hidden border-green-100 dark:border-green-800 transition-all duration-300 hover:shadow-md hover:-translate-y-1 neumorphic group rounded-2xl">
      <div className="relative h-48 w-full overflow-hidden">
        <div
          className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${imageLoaded ? "opacity-0" : "opacity-100"}`}
        />
        <Image
          src={image || "/placeholder.svg"}
          alt={title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={`object-cover transition-transform duration-500 group-hover:scale-105 ${
            imageLoaded ? "opacity-100" : "opacity-0"
          }`}
          onLoad={() => setImageLoaded(true)}
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
      <CardContent className="p-6">
        <h3 className="mb-2 text-xl font-semibold text-green-800 dark:text-green-200">{title}</h3>
        <p className="mb-4 text-muted-foreground">{description}</p>
        <div className="flex items-center text-sm text-green-600 dark:text-green-400">
          <MapPin className="mr-1 h-4 w-4" />
          {location}
        </div>
        <Link
          href="#"
          className="mt-4 inline-flex items-center text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-200"
        >
          Learn more
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="ml-1 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1"
          >
            <path d="M5 12h14" />
            <path d="m12 5 7 7-7 7" />
          </svg>
        </Link>
      </CardContent>
    </Card>
  )
}
