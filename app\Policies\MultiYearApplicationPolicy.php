<?php

namespace App\Policies;

use App\Models\User;
use App\Models\ScholarshipApplication;
use App\Models\Student;
use App\Models\Scholarship;
use Illuminate\Auth\Access\HandlesAuthorization;

class MultiYearApplicationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any multi-year applications.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization', 'user']);
    }

    /**
     * Determine whether the user can view the multi-year application.
     */
    public function view(User $user, ScholarshipApplication $application): bool
    {
        // Admins can view all applications
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view applications from their students
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own applications
        if ($user->role === 'user') {
            return $application->student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create multi-year applications.
     */
    public function create(User $user, ?Scholarship $scholarship = null): bool
    {
        if (!$scholarship) {
            return false;
        }

        // Admins can create applications for any scholarship
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create applications for primary and secondary scholarships
        if ($user->role === 'partner_organization') {
            return in_array($scholarship->category, ['primary', 'secondary']);
        }

        // Individual users can create applications for university scholarships
        if ($user->role === 'user') {
            return $scholarship->category === 'university';
        }

        return false;
    }

    /**
     * Determine whether the user can renew multi-year applications.
     */
    public function renew(User $user, ScholarshipApplication $application): bool
    {
        // Only approved applications can be renewed
        if ($application->status !== 'approved') {
            return false;
        }

        // Admins can renew any application
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can renew applications for their students
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id;
        }

        // Individual users can renew their own applications
        if ($user->role === 'user') {
            return $application->student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can view application tracking.
     */
    public function viewTracking(User $user, ScholarshipApplication $application): bool
    {
        return $this->view($user, $application);
    }

    /**
     * Determine whether the user can perform bulk renewal operations.
     */
    public function bulkRenew(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can view renewal eligibility.
     */
    public function viewRenewalEligibility(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can view multi-year analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can access student application history.
     */
    public function viewStudentHistory(User $user, Student $student): bool
    {
        // Admins can view any student's history
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view history of their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own history
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can view eligible scholarships for a student.
     */
    public function viewEligibleScholarships(User $user, Student $student): bool
    {
        return $this->viewStudentHistory($user, $student);
    }

    /**
     * Determine whether the user can export multi-year application data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can manage renewal criteria.
     */
    public function manageRenewalCriteria(User $user, ScholarshipApplication $application): bool
    {
        // Admins can manage criteria for any application
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can manage criteria for their students' applications
        if ($user->role === 'partner_organization') {
            return $application->student->school_id === $user->partner_organization_id;
        }

        return false;
    }
}
