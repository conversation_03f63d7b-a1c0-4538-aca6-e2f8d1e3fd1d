<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

/**
 * @OA\Tag(
 *     name="Scholarships",
 *     description="Scholarship management and application endpoints"
 * )
 */
class ScholarshipController extends Controller
{
    /**
     * Get all scholarships for admin (with admin middleware)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Scholarship::query();

        // Filter by category if provided
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by is_open if provided
        if ($request->has('is_open')) {
            $query->where('is_open', (bool)$request->is_open);
        }

        $scholarships = $query->with(['fields', 'applications'])
                             ->orderBy('created_at', 'desc')
                             ->get();

        return response()->json([
            'success' => true,
            'data' => $scholarships->map(function ($scholarship) {
                return [
                    'id' => $scholarship->id,
                    'title' => $scholarship->title,
                    'slug' => $scholarship->slug,
                    'category' => $scholarship->category,
                    'description' => $scholarship->description,
                    'eligibility_criteria' => $scholarship->eligibility_criteria,
                    'amount' => $scholarship->amount,
                    'application_deadline' => $scholarship->application_deadline,
                    'status' => $scholarship->status,
                    'is_open' => $scholarship->is_open,
                    'max_applicants' => $scholarship->max_applicants,
                    'current_applicants' => $scholarship->current_applicants,
                    'custom_fields' => $scholarship->custom_fields,
                    'fields_count' => $scholarship->fields->count(),
                    'applications_count' => $scholarship->applications->count(),
                    'created_at' => $scholarship->created_at,
                ];
            })
        ]);
    }

    /**
     * Get public scholarships (open scholarships)
     */
    public function publicIndex(Request $request): JsonResponse
    {
        $query = Scholarship::query()->open();

        // Filter by category if provided
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        $scholarships = $query->with(['fields' => function($query) {
                                 $query->where('is_active', true)->orderBy('field_order', 'asc');
                             }])
                             ->orderBy('application_deadline', 'asc')
                             ->get();

        return response()->json([
            'success' => true,
            'data' => $scholarships->map(function ($scholarship) {
                return [
                    'id' => $scholarship->id,
                    'title' => $scholarship->title,
                    'slug' => $scholarship->slug,
                    'category' => $scholarship->category,
                    'description' => $scholarship->description,
                    'eligibility_criteria' => $scholarship->eligibility_criteria,
                    'amount' => $scholarship->amount,
                    'application_deadline' => $scholarship->application_deadline,
                    'is_open' => $scholarship->isOpen(),
                    'spots_remaining' => $scholarship->max_applicants ? 
                        ($scholarship->max_applicants - $scholarship->current_applicants) : null,
                    'custom_fields' => $scholarship->custom_fields,
                    'fields' => $scholarship->fields->map(function ($field) {
                        return [
                            'id' => $field->id,
                            'field_name' => $field->field_name,
                            'field_label' => $field->field_label,
                            'field_type' => $field->field_type,
                            'field_options' => $field->field_options,
                            'is_required' => $field->is_required,
                            'field_order' => $field->field_order,
                        ];
                    }),
                ];
            })
        ]);
    }

    /**
     * Store a new scholarship (admin only)
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:200',
            'category' => 'required|string|in:primary,secondary,university',
            'description' => 'required|string',
            'eligibility_criteria' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'application_deadline' => 'required|date|after:today',
            'max_applicants' => 'nullable|integer|min:1',
            'contact_email' => 'nullable|email',
            'requirements' => 'nullable|array',
            'documents_required' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'is_open' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['title']);
        $data['status'] = 'draft'; // New scholarships start as draft
        $data['is_open'] = $data['is_open'] ?? false;

        $scholarship = Scholarship::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Scholarship created successfully',
            'data' => $scholarship
        ], 201);
    }

    /**
     * Get scholarship details
     */
    public function show($id): JsonResponse
    {
        $scholarship = Scholarship::with(['fields', 'applications.user'])
                                 ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'scholarship' => $scholarship,
                'is_open' => $scholarship->isOpen(),
                'spots_remaining' => $scholarship->max_applicants ? 
                    ($scholarship->max_applicants - $scholarship->current_applicants) : null,
            ]
        ]);
    }

    /**
     * Update scholarship (admin only)
     */
    public function update(Request $request, $id): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:200',
            'category' => 'string|in:primary,secondary,university',
            'description' => 'string',
            'eligibility_criteria' => 'string',
            'amount' => 'numeric|min:0',
            'application_deadline' => 'date',
            'max_applicants' => 'nullable|integer|min:1',
            'contact_email' => 'nullable|email',
            'requirements' => 'nullable|array',
            'documents_required' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'status' => 'string|in:draft,open,closed',
            'is_open' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        
        // Update slug if title changed
        if (isset($data['title'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        $scholarship->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Scholarship updated successfully',
            'data' => $scholarship->fresh()
        ]);
    }

    /**
     * Delete scholarship (admin only)
     */
    public function destroy($id): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($id);
        
        // Check if there are any applications
        if ($scholarship->applications()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete scholarship with existing applications'
            ], 400);
        }

        $scholarship->delete();

        return response()->json([
            'success' => true,
            'message' => 'Scholarship deleted successfully'
        ]);
    }

    /**
     * Close scholarship (admin only)
     */
    public function close($id): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($id);
        
        $scholarship->update([
            'is_open' => false,
            'status' => 'closed'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Scholarship closed successfully',
            'data' => $scholarship
        ]);
    }

    /**
     * Apply for a scholarship
     */
    public function apply(Request $request, $id): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($id);
        $user = Auth::user();

        // Check if scholarship is open for applications
        if (!$scholarship->isOpen()) {
            return response()->json([
                'success' => false,
                'message' => 'This scholarship is not open for applications'
            ], 400);
        }

        // Check if user has already applied
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingApplication) {
            return response()->json([
                'success' => false,
                'message' => 'You have already applied for this scholarship'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'form_data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Handle file uploads
        $uploadedFiles = [];
        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $key => $file) {
                if ($file && $file->isValid()) {
                    $filename = time() . '_' . $key . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('scholarship_applications', $filename, 'public');
                    $uploadedFiles[$key] = $path;
                }
            }
        }

        // Create application
        $application = ScholarshipApplication::create([
            'scholarship_id' => $scholarship->id,
            'user_id' => $user->id,
            'form_data' => $request->form_data,
            'uploaded_files' => $uploadedFiles,
            'status' => 'pending',
        ]);

        // Increment application count
        $scholarship->incrementApplications();

        return response()->json([
            'success' => true,
            'message' => 'Application submitted successfully',
            'data' => [
                'application_id' => $application->application_id,
                'status' => $application->status,
                'submitted_at' => $application->submitted_at,
            ]
        ], 201);
    }

    /**
     * Get user's applications
     */
    public function myApplications(): JsonResponse
    {
        $user = Auth::user();
        
        $applications = ScholarshipApplication::with('scholarship')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $applications->map(function ($application) {
                return [
                    'id' => $application->id,
                    'application_id' => $application->application_id,
                    'scholarship' => [
                        'id' => $application->scholarship->id,
                        'title' => $application->scholarship->title,
                        'category' => $application->scholarship->category,
                        'amount' => $application->scholarship->amount,
                    ],
                    'status' => $application->status,
                    'submitted_at' => $application->submitted_at,
                    'reviewed_at' => $application->reviewed_at,
                ];
            })
        ]);
    }
}