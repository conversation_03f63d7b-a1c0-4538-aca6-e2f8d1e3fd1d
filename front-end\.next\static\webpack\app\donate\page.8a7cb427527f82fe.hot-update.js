"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/donation-form.tsx":
/*!**************************************!*\
  !*** ./components/donation-form.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DonationForm: () => (/* binding */ DonationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Building2,CheckCircle2,CreditCard,Loader2,Lock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./bank-transfer-receipt */ \"(app-pages-browser)/./components/bank-transfer-receipt.tsx\");\n/* __next_internal_client_entry_do_not_use__ DonationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DonationForm() {\n    var _campaigns_find;\n    _s();\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings)();\n    const [selectedAmount, setSelectedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customAmount, setCustomAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [donationType, setDonationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"one-time\");\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [campaignsLoading, setCampaignsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        donor_name: '',\n        donor_email: '',\n        donor_phone: '',\n        amount: 0,\n        currency: 'NGN',\n        donation_type: 'one-time',\n        payment_method: 'paystack',\n        is_anonymous: false,\n        notes: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [donationId, setDonationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [termsAccepted, setTermsAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load donation campaigns\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DonationForm.useEffect\": ()=>{\n            const loadCampaigns = {\n                \"DonationForm.useEffect.loadCampaigns\": async ()=>{\n                    try {\n                        setCampaignsLoading(true);\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.getDonationCampaigns();\n                        if (response.success && response.data) {\n                            const campaignsData = Array.isArray(response.data) ? response.data : response.data.data || [];\n                            setCampaigns(campaignsData);\n                        }\n                    } catch (error) {\n                        console.error('Error loading campaigns:', error);\n                    } finally{\n                        setCampaignsLoading(false);\n                    }\n                }\n            }[\"DonationForm.useEffect.loadCampaigns\"];\n            loadCampaigns();\n        }\n    }[\"DonationForm.useEffect\"], []);\n    const handleAmountSelect = (amount)=>{\n        setSelectedAmount(amount);\n        setCustomAmount(\"\");\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleCustomAmountChange = (value)=>{\n        const amount = Number.parseInt(value) || 0;\n        setCustomAmount(value);\n        setSelectedAmount(null);\n        setFormData((prev)=>({\n                ...prev,\n                amount\n            }));\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleDonationTypeChange = (value)=>{\n        const type = value;\n        setDonationType(type);\n        setFormData((prev)=>({\n                ...prev,\n                donation_type: type,\n                frequency: type === 'recurring' ? 'monthly' : undefined\n            }));\n    };\n    const finalAmount = selectedAmount || Number.parseInt(customAmount) || 0;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!finalAmount || finalAmount < _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT) {\n            setSubmitError(\"Please enter a valid donation amount (minimum ₦\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT.toLocaleString(), \")\"));\n            return;\n        }\n        if (!formData.donor_name || !formData.donor_email) {\n            setSubmitError('Please fill in all required fields');\n            return;\n        }\n        if (!termsAccepted) {\n            setSubmitError('Please accept the terms and conditions');\n            return;\n        }\n        setIsSubmitting(true);\n        setSubmitError(null);\n        try {\n            const donationData = {\n                ...formData,\n                amount: finalAmount\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.apiClient.createDonation(donationData);\n            if (response.success && response.data) {\n                // Store donation ID for receipt upload\n                setDonationId(response.data.id);\n                if (formData.payment_method === 'paystack' && response.data.payment_url) {\n                    // Redirect to Paystack payment page\n                    window.location.href = response.data.payment_url;\n                } else {\n                    // Bank transfer success - show receipt upload\n                    setSubmitSuccess(true);\n                }\n            } else {\n                setSubmitError(response.message || 'Failed to process donation');\n            }\n        } catch (error) {\n            console.error('Donation submission error:', error);\n            setSubmitError('An error occurred while processing your donation. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (submitSuccess) {\n        // For bank transfers, show receipt upload component\n        if (formData.payment_method === 'bank_transfer' && donationId) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-blue-200 bg-blue-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Bank Transfer Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Account Number:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_account_number) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Bank Name:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: (settings === null || settings === void 0 ? void 0 : settings.donation_bank_name) || 'Loading...'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600\",\n                                                        children: \"Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: [\n                                                            \"₦\",\n                                                            finalAmount.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                                        className: \"mt-4 border-blue-200 bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                                className: \"text-blue-700\",\n                                                children: \"Please complete your bank transfer using the details above, then upload your receipt below for verification.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_bank_transfer_receipt__WEBPACK_IMPORTED_MODULE_14__.BankTransferReceipt, {\n                        donationId: donationId,\n                        onSuccess: ()=>{\n                            // Receipt uploaded successfully\n                            console.log('Receipt uploaded successfully');\n                        },\n                        onError: (error)=>{\n                            console.error('Receipt upload error:', error);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this);\n        }\n        // For other payment methods or fallback\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-green-600 mb-4\",\n                        children: \"Thank You!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Your donation has been received successfully.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Make Another Donation\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden shadow-xl neumorphic rounded-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-2xl text-center\",\n                    children: \"Complete Your Donation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-8\",\n                    children: [\n                        submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.Alert, {\n                            className: \"border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_11__.AlertDescription, {\n                                    className: \"text-red-600\",\n                                    children: submitError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: donationType,\n                            onValueChange: handleDonationTypeChange,\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"one-time\",\n                                            className: \"rounded-l-xl\",\n                                            children: \"One-Time Donation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"recurring\",\n                                            className: \"rounded-r-xl\",\n                                            children: \"Monthly Giving\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"one-time\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Select Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        15000,\n                                                        25000,\n                                                        50000,\n                                                        100000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"amount-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"amount-\".concat(amount),\n                                                                    className: \"flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        amount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-amount\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"Or enter custom amount (minimum ₦\",\n                                                        _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT.toLocaleString(),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-amount\",\n                                                    type: \"number\",\n                                                    min: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT,\n                                                    placeholder: \"Enter amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                    value: \"recurring\",\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-amber-50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-200 dark:border-amber-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-amber-800 dark:text-amber-200 mb-2\",\n                                                    children: \"Monthly Giving Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-amber-700 dark:text-amber-300\",\n                                                    children: \"Join our monthly giving program and provide sustained support for our ongoing projects. You can modify or cancel your recurring donation at any time.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-4\",\n                                                    children: \"Monthly Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                                    value: (selectedAmount === null || selectedAmount === void 0 ? void 0 : selectedAmount.toString()) || \"\",\n                                                    onValueChange: (value)=>handleAmountSelect(Number.parseInt(value)),\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                    children: [\n                                                        2000,\n                                                        5000,\n                                                        10000,\n                                                        20000\n                                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                                    value: amount.toString(),\n                                                                    id: \"monthly-\".concat(amount),\n                                                                    className: \"peer sr-only\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"monthly-\".concat(amount),\n                                                                    className: \"flex flex-col items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-950/20 hover:border-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₦\",\n                                                                                amount.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"/month\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, amount, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"custom-monthly\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"Or enter custom monthly amount (minimum ₦\",\n                                                        _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT.toLocaleString(),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    id: \"custom-monthly\",\n                                                    type: \"number\",\n                                                    min: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT,\n                                                    placeholder: \"Enter monthly amount in Naira\",\n                                                    value: customAmount,\n                                                    onChange: (e)=>handleCustomAmountChange(e.target.value),\n                                                    className: \"mt-2 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Frequency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    value: formData.frequency || 'monthly',\n                                                    onValueChange: (value)=>handleInputChange('frequency', value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"monthly\",\n                                                                    children: \"Monthly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"quarterly\",\n                                                                    children: \"Quarterly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"annually\",\n                                                                    children: \"Annually\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 13\n                                        }, this),\n                                        !campaignsLoading && campaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Support a Specific Campaign (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    onValueChange: (value)=>handleInputChange('campaign_id', value && value !== 'general' ? parseInt(value) : undefined),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            className: \"rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Choose a campaign or leave blank for general donation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: \"general\",\n                                                                    children: \"General Donation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: campaign.id.toString(),\n                                                                        children: [\n                                                                            campaign.title,\n                                                                            \" - ₦\",\n                                                                            campaign.raised_amount.toLocaleString(),\n                                                                            \" / ₦\",\n                                                                            campaign.goal_amount.toLocaleString()\n                                                                        ]\n                                                                    }, campaign.id, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Donor Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-name\",\n                                                            children: [\n                                                                \"Full Name \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 55\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-name\",\n                                                            placeholder: \"Enter your full name\",\n                                                            value: formData.donor_name,\n                                                            onChange: (e)=>handleInputChange('donor_name', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-email\",\n                                                            children: [\n                                                                \"Email Address \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-500\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 60\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-email\",\n                                                            type: \"email\",\n                                                            placeholder: \"Enter your email\",\n                                                            value: formData.donor_email,\n                                                            onChange: (e)=>handleInputChange('donor_email', e.target.value),\n                                                            className: \"mt-1 rounded-xl\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"donor-phone\",\n                                                            children: \"Phone Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"donor-phone\",\n                                                            placeholder: \"Enter your phone number\",\n                                                            value: formData.donor_phone,\n                                                            onChange: (e)=>handleInputChange('donor_phone', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"purpose\",\n                                                            children: \"Purpose (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            id: \"purpose\",\n                                                            placeholder: \"e.g., Education, Healthcare, General\",\n                                                            value: formData.purpose || '',\n                                                            onChange: (e)=>handleInputChange('purpose', e.target.value),\n                                                            className: \"mt-1 rounded-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"anonymous\",\n                                                    checked: formData.is_anonymous,\n                                                    onCheckedChange: (checked)=>handleInputChange('is_anonymous', checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"anonymous\",\n                                                    className: \"text-sm\",\n                                                    children: \"Make this donation anonymous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Payment Method\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                            value: formData.payment_method,\n                                            onValueChange: (value)=>handleInputChange('payment_method', value),\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"paystack\",\n                                                            id: \"paystack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"paystack\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Pay with Card (Paystack)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Secure online payment with debit/credit card\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 p-4 border rounded-xl hover:bg-gray-50 dark:hover:bg-gray-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                            value: \"bank_transfer\",\n                                                            id: \"bank_transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bank_transfer\",\n                                                            className: \"flex-1 cursor-pointer\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Bank Transfer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 511,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Transfer directly to our bank account\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 13\n                                        }, this),\n                                        formData.payment_method === 'bank_transfer' && !settingsLoading && settings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-950/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Bank Account Details\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_name || 'HALIMAKQ Foundation'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Account Number:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_account_number || '**********'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-700 dark:text-blue-300\",\n                                                                    children: \"Bank Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                                    children: settings.donation_bank_name || 'First Bank of Nigeria'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 dark:text-blue-400 mt-4\",\n                                                    children: \"Please use your email address as the transfer reference for easy identification.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 11\n                                }, this),\n                                finalAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 pt-6 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 dark:bg-green-950/20 p-6 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-800 dark:text-green-200 mb-4\",\n                                                    children: \"Donation Summary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Amount:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: donationType === \"recurring\" ? \"\".concat(formData.frequency || 'Monthly', \" Recurring\") : \"One-Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        formData.campaign_id && campaigns.find((c)=>c.id === formData.campaign_id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Campaign:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: (_campaigns_find = campaigns.find((c)=>c.id === formData.campaign_id)) === null || _campaigns_find === void 0 ? void 0 : _campaigns_find.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Payment Method:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: formData.payment_method === 'paystack' ? 'Card Payment' : 'Bank Transfer'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-lg font-bold text-green-600 pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"₦\",\n                                                                        finalAmount.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                    id: \"terms\",\n                                                    checked: termsAccepted,\n                                                    onCheckedChange: (checked)=>setTermsAccepted(checked === true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"terms\",\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        \"I agree to the\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Terms of Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        \"and\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-green-600 hover:underline\",\n                                                            children: \"Privacy Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"submit\",\n                                            size: \"lg\",\n                                            disabled: isSubmitting || finalAmount < _lib_constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_RULES.DONATION_MIN_AMOUNT || !termsAccepted || !formData.donor_name || !formData.donor_email,\n                                            className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-lg py-6 transition-all duration-200 hover:scale-105 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Processing...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    formData.payment_method === 'paystack' ? 'Pay Now' : 'Submit Donation',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Building2_CheckCircle2_CreditCard_Loader2_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground text-center mt-4\",\n                                    children: \"Your donation is secure and encrypted. We never store your payment information.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-form.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(DonationForm, \"8/3rt3UtNsd7/Wz7WV++9wGRHI8=\", false, function() {\n    return [\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_13__.useSettings\n    ];\n});\n_c = DonationForm;\nvar _c;\n$RefreshReg$(_c, \"DonationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/donation-form.tsx\n"));

/***/ })

});