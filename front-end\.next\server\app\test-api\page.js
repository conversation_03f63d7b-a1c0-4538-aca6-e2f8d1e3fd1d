(()=>{var e={};e.id=990,e.ids=[990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19311:(e,r,t)=>{Promise.resolve().then(t.bind(t,29723))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29723:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(60687),a=t(43210),o=t(59556);function n(){let[e,r]=(0,a.useState)({}),[t,n]=(0,a.useState)(!1),[l,i]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),p=async(e,t)=>{try{console.log(`Testing ${e}: ${t}`),console.log("Current token:",localStorage.getItem("authToken")),console.log("Current user:",localStorage.getItem("user"));let s=await o.uE.get(t);console.log(`${e} response:`,s),r(r=>({...r,[e]:{success:!0,data:s}}))}catch(t){console.error(`${e} error:`,t),console.error(`${e} error response:`,t.response?.data),r(r=>({...r,[e]:{success:!1,error:t.message,data:t.response?.data}}))}},u=async()=>{n(!0),r({}),await p("User Info","/user"),await p("Public Scholarships","/public-scholarships"),await p("Partner Scholarships Available","/partner-scholarships/available"),await p("Partner Students","/partner-students"),await p("Partner Applications","/partner-scholarships/my-applications"),await p("Partner Statistics","/partner-students/statistics"),await p("Upcoming Events","/events/upcoming"),await p("Programs","/programs"),n(!1)},g=async()=>{try{console.log("Attempting login with:",{email:"<EMAIL>",password:"password123"});let e=await o.uE.post("/login",{email:"<EMAIL>",password:"password123"});console.log("Full login response:",e),e.success&&e.data?(console.log("User data from login:",e.data.user),console.log("User role:",e.data.user.role),console.log("Access token:",e.data.access_token),localStorage.setItem("authToken",e.data.access_token),localStorage.setItem("user",JSON.stringify(e.data.user)),i(e.data.user),c(e.data.access_token),console.log("Login successful - stored in localStorage")):console.error("Login failed - no success or data in response:",e)}catch(e){console.error("Login failed with error:",e)}};return(0,s.jsxs)("div",{className:"container mx-auto p-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"API Testing Page"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Current User Info:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:l?JSON.stringify(l,null,2):"No user data found"})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Auth Token:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:d||"No token found"}),(0,s.jsx)("div",{className:"mt-2 space-x-2",children:d?(0,s.jsx)("button",{onClick:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("user"),c(null),i(null),console.log("Cleared authentication")},className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Logout"}):(0,s.jsx)("button",{onClick:g,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600",children:"Login as Partner"})})]}),(0,s.jsx)("button",{onClick:u,disabled:t,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6",children:t?"Testing...":"Test All Endpoints"}),(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(e).map(([e,r])=>(0,s.jsxs)("div",{className:"border rounded p-4",children:[(0,s.jsxs)("h3",{className:"font-semibold mb-2 flex items-center",children:[e,(0,s.jsx)("span",{className:`ml-2 px-2 py-1 rounded text-xs ${r.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.success?"SUCCESS":"ERROR"})]}),(0,s.jsx)("pre",{className:"bg-gray-50 p-3 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(r.success?r.data:r.error,null,2)})]},e))})]})}},33873:e=>{"use strict";e.exports=require("path")},35097:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["test-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47589)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-api\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-api\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-api/page",pathname:"/test-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},47589:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\test-api\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66167:(e,r,t)=>{Promise.resolve().then(t.bind(t,47589))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[555,702],()=>t(35097));module.exports=s})();