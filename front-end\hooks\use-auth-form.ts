import { useState } from 'react';
import { useSessionAuth } from './use-session-auth';
import { useRouter } from 'next/navigation';

interface AuthFormData {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  password_confirmation?: string;
}

interface UseAuthFormOptions {
  onSuccess?: () => void;
  redirectTo?: string;
  mode?: 'login' | 'register';
}

export const useAuthForm = (options: UseAuthFormOptions = {}) => {
  const {
    onSuccess,
    redirectTo = '/dashboard',
    mode = 'login',
  } = options;

  const router = useRouter();
  const { login, register, error: authError } = useSessionAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (data: AuthFormData): string | null => {
    if (!data.email || !data.password) {
      return 'Email and password are required';
    }

    if (mode === 'register') {
      if (!data.first_name || !data.last_name) {
        return 'First name and last name are required';
      }
      if (!data.password_confirmation) {
        return 'Please confirm your password';
      }
      if (data.password !== data.password_confirmation) {
        return 'Passwords do not match';
      }
      if (data.password.length < 8) {
        return 'Password must be at least 8 characters';
      }
    }

    return null;
  };

  const handleSubmit = async (data: AuthFormData) => {
    try {
      setIsSubmitting(true);
      setFormError(null);

      const validationError = validateForm(data);
      if (validationError) {
        setFormError(validationError);
        return;
      }

      if (mode === 'login') {
        await login({
          email: data.email,
          password: data.password,
        });
      } else {
        await register({
          first_name: data.first_name!,
          last_name: data.last_name!,
          email: data.email,
          password: data.password,
          password_confirmation: data.password_confirmation!,
        });
      }

      onSuccess?.();
      router.push(redirectTo);
    } catch (error: any) {
      setFormError(error.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
    error: formError || authError,
    mode,
  };
}; 