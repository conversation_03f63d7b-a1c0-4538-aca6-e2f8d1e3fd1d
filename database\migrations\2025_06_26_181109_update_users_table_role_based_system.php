<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add new role_id column
            $table->unsignedTinyInteger('role_id')->default(1)->after('role');

            // Add additional fields for different user types
            $table->string('student_id', 50)->nullable()->after('role_id');
            $table->string('matriculation_number', 50)->nullable()->after('student_id');
            $table->string('university_name', 100)->nullable()->after('matriculation_number');
            $table->string('course_of_study', 100)->nullable()->after('university_name');
            $table->year('year_of_study')->nullable()->after('course_of_study');
            $table->decimal('cgpa', 3, 2)->nullable()->after('year_of_study');

            // Partner Organization fields
            $table->string('organization_name', 100)->nullable()->after('cgpa');
            $table->string('organization_type', 50)->nullable()->after('organization_name');
            $table->string('organization_registration_number', 50)->nullable()->after('organization_type');
            $table->text('organization_address')->nullable()->after('organization_registration_number');
            $table->string('organization_phone', 20)->nullable()->after('organization_address');
            $table->string('organization_email', 100)->nullable()->after('organization_phone');
            $table->string('principal_name', 100)->nullable()->after('organization_email');
            $table->string('principal_phone', 20)->nullable()->after('principal_name');
            $table->string('principal_email', 100)->nullable()->after('principal_phone');

            // Add indexes for better performance
            $table->index('role_id');
            $table->index(['role_id', 'status']);
        });

        // Update existing users to use role_id based on their current role
        DB::statement("
            UPDATE users SET role_id = CASE
                WHEN role = 'user' THEN 1
                WHEN role = 'volunteer' THEN 1
                WHEN role = 'donor' THEN 1
                WHEN role = 'partner_organization' THEN 3
                WHEN role = 'admin' THEN 4
                ELSE 1
            END
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove the new columns
            $table->dropColumn([
                'role_id',
                'student_id',
                'matriculation_number',
                'university_name',
                'course_of_study',
                'year_of_study',
                'cgpa',
                'organization_name',
                'organization_type',
                'organization_registration_number',
                'organization_address',
                'organization_phone',
                'organization_email',
                'principal_name',
                'principal_phone',
                'principal_email'
            ]);
        });
    }
};
