# HLTKKQ Foundation - Laravel Backend API Documentation

## Overview

This document provides comprehensive documentation for the HLTKKQ Foundation Laravel backend API. The backend provides RESTful APIs to support an NGO platform including user management, donations, blog, volunteer programs, events, scholarships, and administrative functions.

## Base URL
```
Development: http://localhost:8000/api/v1
Production: https://api.hltkkqfoundation.org/api/v1
```

## Authentication

The API uses Laravel Sanctum for authentication with Bearer tokens.

### Headers Required for Authenticated Endpoints
```
Authorization: Bearer {token}
Accept: application/json
Content-Type: application/json
```

## Database Structure

### Core Models
- **User**: User accounts with roles (admin, volunteer, donor, user)
- **Volunteer**: Volunteer applications and management
- **Program**: NGO programs and projects
- **Event**: Event management and registration
- **BlogPost**: Blog content management
- **Donation**: Donation processing and tracking
- **DonationCampaign**: Fundraising campaigns
- **Scholarship**: Scholarship programs
- **ScholarshipApplication**: Scholarship applications
- **ContactMessage**: Contact form submissions
- **NewsletterSubscriber**: Newsletter management
- **TeamMember**: Team member profiles

## API Endpoints

### Authentication Endpoints

#### Public Authentication
```http
POST /api/v1/register
POST /api/v1/login
POST /api/v1/forgot-password
POST /api/v1/reset-password
GET  /api/v1/email/verify/{id}/{hash}
```

#### Authenticated
```http
POST /api/v1/logout
GET  /api/v1/user
POST /api/v1/email/verification-notification
```

### User Profile Management

```http
GET  /api/v1/profile                    # Get user profile
PUT  /api/v1/profile                    # Update profile
POST /api/v1/profile/avatar             # Upload avatar
PUT  /api/v1/profile/password           # Change password
```

### Blog Management

#### Public
```http
GET  /api/v1/blog/posts                 # List published posts
GET  /api/v1/blog/posts/{slug}          # Get single post
GET  /api/v1/blog/categories            # Get categories
GET  /api/v1/blog/posts/category/{category} # Posts by category
```

#### Authenticated
```http
POST /api/v1/blog/posts/{id}/comments   # Add comment
PUT  /api/v1/blog/comments/{id}         # Update comment
DELETE /api/v1/blog/comments/{id}       # Delete comment
```

### Programs Management

#### Public
```http
GET  /api/v1/programs                   # List active programs
GET  /api/v1/programs/featured          # Get featured programs
GET  /api/v1/programs/stats             # Get program statistics
GET  /api/v1/programs/{slug}            # Get program details
```

### Events Management

#### Public
```http
GET  /api/v1/events                     # List published events
GET  /api/v1/events/upcoming            # Get upcoming events
GET  /api/v1/events/{id}                # Get event details
```

#### Authenticated
```http
POST   /api/v1/events/{id}/register     # Register for event
DELETE /api/v1/events/{id}/unregister   # Cancel registration
GET    /api/v1/events/my-registrations  # Get user's registrations
```

### Volunteer Management

#### Public
```http
GET  /api/v1/volunteer/opportunities    # List volunteer opportunities
```

#### Authenticated
```http
POST /api/v1/volunteer/apply            # Submit application
GET  /api/v1/volunteer/application      # Get user's application
PUT  /api/v1/volunteer/application      # Update application
POST /api/v1/volunteer/hours            # Log volunteer hours
GET  /api/v1/volunteer/hours            # Get hours summary
```

### Donations Management

#### Public
```http
POST /api/v1/donations/payment/callback # Payment callback
GET  /api/v1/donations/campaigns        # List campaigns
GET  /api/v1/donations/campaigns/{id}   # Get campaign details
```

#### Authenticated
```http
POST /api/v1/donations                  # Create donation
GET  /api/v1/donations/my-donations     # Get donation history
GET  /api/v1/donations/{id}/receipt     # Get receipt
```

### Scholarships Management

#### Public
```http
GET  /api/v1/scholarships               # List open scholarships
GET  /api/v1/scholarships/{id}          # Get scholarship details
```

#### Authenticated
```http
POST /api/v1/scholarships/{id}/apply    # Apply for scholarship
GET  /api/v1/scholarships/my-applications # Get applications
PUT  /api/v1/scholarships/applications/{id} # Update application
```

### Contact & Support

#### Public
```http
POST /api/v1/contact                    # Submit contact form
GET  /api/v1/contact/faq                # Get FAQ items
```

### Newsletter Management

#### Public
```http
POST /api/v1/newsletter/subscribe       # Subscribe to newsletter
POST /api/v1/newsletter/unsubscribe     # Unsubscribe
```

### Team Management

#### Public
```http
GET  /api/v1/team                       # List team members
GET  /api/v1/team/{id}                  # Get team member details
```

### Admin Endpoints

All admin endpoints require `role:admin` middleware.

#### User Management
```http
GET    /api/v1/admin/users              # List all users
GET    /api/v1/admin/users/{id}         # Get user details
PUT    /api/v1/admin/users/{id}         # Update user
DELETE /api/v1/admin/users/{id}         # Delete user
PUT    /api/v1/admin/users/{id}/toggle-status # Toggle user status
POST   /api/v1/admin/users/{id}/assign-role   # Assign role
DELETE /api/v1/admin/users/{id}/remove-role   # Remove role
```

#### Dashboard
```http
GET  /api/v1/admin/dashboard            # Dashboard overview
GET  /api/v1/admin/dashboard/analytics  # Analytics data
```

#### Team Management (Admin)
```http
GET    /api/v1/admin/team               # List team members
POST   /api/v1/admin/team               # Add team member
PUT    /api/v1/admin/team/{id}          # Update team member
DELETE /api/v1/admin/team/{id}          # Delete team member
POST   /api/v1/admin/team/{id}/photo    # Upload photo
```

#### Newsletter Management (Admin)
```http
GET    /api/v1/admin/newsletter/subscribers # List subscribers
POST   /api/v1/admin/newsletter/send        # Send newsletter
DELETE /api/v1/admin/newsletter/subscribers/{id} # Remove subscriber
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
    "success": true,
    "data": {
        // Response data
    },
    "message": "Operation completed successfully" // Optional
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        // Validation errors (if applicable)
    }
}
```

### Paginated Response
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            // Array of items
        ],
        "first_page_url": "...",
        "from": 1,
        "last_page": 5,
        "last_page_url": "...",
        "links": [...],
        "next_page_url": "...",
        "path": "...",
        "per_page": 15,
        "prev_page_url": null,
        "to": 15,
        "total": 67
    }
}
```

## Status Codes

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

## Authentication Examples

### Registration
```bash
curl -X POST http://localhost:8000/api/v1/register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone_number": "+234800000000"
  }'
```

### Login
```bash
curl -X POST http://localhost:8000/api/v1/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Using Bearer Token
```bash
curl -X GET http://localhost:8000/api/v1/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"
```

## Database Schema

### Users Table
```sql
- id (bigint, primary key)
- first_name (varchar 50)
- last_name (varchar 50)
- email (varchar 100, unique)
- email_verified_at (timestamp, nullable)
- password (varchar 255)
- phone_number (varchar 20, nullable)
- date_of_birth (date, nullable)
- gender (enum: male, female, other, nullable)
- address (text, nullable)
- city (varchar 50, nullable)
- state (varchar 50, nullable)
- country (varchar 50, default: Nigeria)
- profile_picture (varchar 255, nullable)
- role (enum: admin, volunteer, donor, user, default: user)
- status (enum: active, inactive, suspended, default: active)
- last_login_at (timestamp, nullable)
- preferences (json, nullable)
- created_at, updated_at (timestamps)
```

### Volunteers Table
```sql
- id (bigint, primary key)
- user_id (bigint, foreign key to users)
- application_status (enum: pending, approved, rejected, default: pending)
- skills (json, nullable)
- interests (json, nullable)
- availability (json, nullable)
- experience (text, nullable)
- motivation (text, nullable)
- background_check_status (enum: pending, passed, failed, nullable)
- emergency_contact (json, nullable)
- hours_logged (decimal 8,2, default: 0.00)
- certifications (json, nullable)
- applied_at (timestamp, default: current_timestamp)
- approved_at (timestamp, nullable)
```

### Scholarships Table
```sql
- id (bigint, primary key)
- title (varchar 200)
- slug (varchar 220, unique)
- description (text)
- eligibility_criteria (text)
- amount (decimal 10,2)
- application_deadline (date)
- requirements (json, nullable)
- status (enum: draft, open, closed, default: draft)
- max_applicants (integer, nullable)
- current_applicants (integer, default: 0)
- contact_email (varchar 100, nullable)
- documents_required (json, nullable)
- created_at, updated_at (timestamps)
```

## Error Handling

### Validation Errors
```json
{
    "success": false,
    "message": "Validation error",
    "errors": {
        "email": ["The email field is required."],
        "password": ["The password must be at least 8 characters."]
    }
}
```

### Authentication Errors
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

### Authorization Errors
```json
{
    "success": false,
    "message": "This action is unauthorized."
}
```

## Rate Limiting

The API implements rate limiting:
- **Public endpoints**: 60 requests per minute per IP
- **Authenticated endpoints**: 1000 requests per minute per user
- **Admin endpoints**: 2000 requests per minute per admin

## Security Features

- **CORS**: Configured for frontend origins
- **CSRF Protection**: Enabled for web routes
- **SQL Injection Prevention**: Eloquent ORM with parameterized queries
- **XSS Protection**: Input sanitization and output escaping
- **File Upload Security**: File type validation and storage restrictions
- **Rate Limiting**: API throttling middleware
- **Password Hashing**: bcrypt encryption
- **JWT Authentication**: Sanctum token-based authentication

## Testing Credentials

For development and testing:

- **Admin**: <EMAIL> / password
- **Test User**: <EMAIL> / password
- **Volunteer**: <EMAIL> / password

## Environment Setup

Required environment variables:

```env
APP_NAME="HLTKKQ Foundation"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_foundation
DB_USERNAME=root
DB_PASSWORD=

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Payment Gateway (Paystack)
PAYSTACK_PUBLIC_KEY=pk_test_...
PAYSTACK_SECRET_KEY=sk_test_...
PAYSTACK_PAYMENT_URL=https://api.paystack.co
```

## Deployment

### Requirements
- PHP 8.2+
- MySQL 8.0+
- Redis (for caching and queues)
- Composer
- Laravel 11+

### Installation Steps
1. Clone repository
2. Run `composer install`
3. Copy `.env.example` to `.env` and configure
4. Run `php artisan key:generate`
5. Run `php artisan migrate --seed`
6. Run `php artisan serve`

## API Testing

Use tools like Postman, Insomnia, or curl to test the API endpoints. The API includes OpenAPI documentation that can be accessed at `/api/documentation` when L5-Swagger is configured.

## Support

For technical support or questions about the API, contact the development <NAME_EMAIL>. 