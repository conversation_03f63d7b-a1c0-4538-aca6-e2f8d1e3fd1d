
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => '',
    'subtitle' => '',
    'icon' => '',
    'iconColor' => 'blue',
    'href' => null,
    'badge' => null,
    'badgeColor' => 'blue'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => '',
    'subtitle' => '',
    'icon' => '',
    'iconColor' => 'blue',
    'href' => null,
    'badge' => null,
    'badgeColor' => 'blue'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 <?php echo e($href ? 'cursor-pointer' : ''); ?>"
     <?php if($href): ?> onclick="window.location.href='<?php echo e($href); ?>'" <?php endif; ?>>
    <div class="p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <?php if($icon): ?>
                    <div class="p-3 bg-<?php echo e($iconColor); ?>-100 rounded-full">
                        <i class="fas fa-<?php echo e($icon); ?> text-<?php echo e($iconColor); ?>-600 text-xl"></i>
                    </div>
                <?php endif; ?>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800"><?php echo e($title); ?></h3>
                    <?php if($subtitle): ?>
                        <p class="text-sm text-gray-600"><?php echo e($subtitle); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <?php if($badge): ?>
                <span class="px-3 py-1 text-xs font-medium bg-<?php echo e($badgeColor); ?>-100 text-<?php echo e($badgeColor); ?>-800 rounded-full">
                    <?php echo e($badge); ?>

                </span>
            <?php endif; ?>
        </div>
        
        <?php if($slot->isNotEmpty()): ?>
            <div class="mt-4">
                <?php echo e($slot); ?>

            </div>
        <?php endif; ?>
        
        <?php if($href): ?>
            <div class="mt-4">
                <span class="text-<?php echo e($iconColor); ?>-600 hover:text-<?php echo e($iconColor); ?>-800 font-medium">
                    View Details →
                </span>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/components/dashboard/card.blade.php ENDPATH**/ ?>