import { useMemo } from 'react'
import { useAuth } from './useAuth'

export type UserRole = 'admin' | 'partner_organization' | 'user' | 'student'

export interface RolePermissions {
  // Scholarship permissions
  canViewAllScholarships: boolean
  canCreateScholarships: boolean
  canEditScholarships: boolean
  canDeleteScholarships: boolean
  
  // Application permissions
  canApplyForPrimaryScholarships: boolean
  canApplyForSecondaryScholarships: boolean
  canApplyForUniversityScholarships: boolean
  canViewAllApplications: boolean
  canReviewApplications: boolean
  canApproveApplications: boolean
  
  // Student management permissions
  canManageStudents: boolean
  canViewStudentProgress: boolean
  canAdvanceStudents: boolean
  canViewStudentHistory: boolean
  
  // Dashboard access
  canAccessAdminDashboard: boolean
  canAccessPartnerDashboard: boolean
  canAccessStudentDashboard: boolean
  
  // System permissions
  canManageUsers: boolean
  canViewAnalytics: boolean
  canManageSettings: boolean
  canExportData: boolean
}

/**
 * Hook for role-based access control
 */
export function useRoleAccess() {
  const { user, isLoading } = useAuth()

  const permissions = useMemo((): RolePermissions => {
    if (!user) {
      return {
        canViewAllScholarships: false,
        canCreateScholarships: false,
        canEditScholarships: false,
        canDeleteScholarships: false,
        canApplyForPrimaryScholarships: false,
        canApplyForSecondaryScholarships: false,
        canApplyForUniversityScholarships: false,
        canViewAllApplications: false,
        canReviewApplications: false,
        canApproveApplications: false,
        canManageStudents: false,
        canViewStudentProgress: false,
        canAdvanceStudents: false,
        canViewStudentHistory: false,
        canAccessAdminDashboard: false,
        canAccessPartnerDashboard: false,
        canAccessStudentDashboard: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canExportData: false,
      }
    }

    const role = user.role as UserRole
    const isPartnerOrg = role === 'partner_organization' || !!user.partner_organization_id
    const isIndividualStudent = !user.partner_organization_id && (role === 'user' || role === 'student')

    switch (role) {
      case 'admin':
        return {
          canViewAllScholarships: true,
          canCreateScholarships: true,
          canEditScholarships: true,
          canDeleteScholarships: true,
          canApplyForPrimaryScholarships: false,
          canApplyForSecondaryScholarships: false,
          canApplyForUniversityScholarships: false,
          canViewAllApplications: true,
          canReviewApplications: true,
          canApproveApplications: true,
          canManageStudents: true,
          canViewStudentProgress: true,
          canAdvanceStudents: true,
          canViewStudentHistory: true,
          canAccessAdminDashboard: true,
          canAccessPartnerDashboard: false,
          canAccessStudentDashboard: false,
          canManageUsers: true,
          canViewAnalytics: true,
          canManageSettings: true,
          canExportData: true,
        }

      case 'partner_organization':
        return {
          canViewAllScholarships: true,
          canCreateScholarships: false,
          canEditScholarships: false,
          canDeleteScholarships: false,
          canApplyForPrimaryScholarships: true,
          canApplyForSecondaryScholarships: true,
          canApplyForUniversityScholarships: false,
          canViewAllApplications: false, // Only their own applications
          canReviewApplications: false,
          canApproveApplications: false,
          canManageStudents: true, // Their own students
          canViewStudentProgress: true,
          canAdvanceStudents: true,
          canViewStudentHistory: true,
          canAccessAdminDashboard: false,
          canAccessPartnerDashboard: true,
          canAccessStudentDashboard: false,
          canManageUsers: false,
          canViewAnalytics: false, // Limited analytics
          canManageSettings: false,
          canExportData: false, // Limited export
        }

      case 'user':
      case 'student':
        return {
          canViewAllScholarships: true,
          canCreateScholarships: false,
          canEditScholarships: false,
          canDeleteScholarships: false,
          canApplyForPrimaryScholarships: false,
          canApplyForSecondaryScholarships: false,
          canApplyForUniversityScholarships: isIndividualStudent,
          canViewAllApplications: false, // Only their own
          canReviewApplications: false,
          canApproveApplications: false,
          canManageStudents: false,
          canViewStudentProgress: false,
          canAdvanceStudents: false,
          canViewStudentHistory: false,
          canAccessAdminDashboard: false,
          canAccessPartnerDashboard: isPartnerOrg,
          canAccessStudentDashboard: isIndividualStudent,
          canManageUsers: false,
          canViewAnalytics: false,
          canManageSettings: false,
          canExportData: false,
        }

      default:
        return {
          canViewAllScholarships: false,
          canCreateScholarships: false,
          canEditScholarships: false,
          canDeleteScholarships: false,
          canApplyForPrimaryScholarships: false,
          canApplyForSecondaryScholarships: false,
          canApplyForUniversityScholarships: false,
          canViewAllApplications: false,
          canReviewApplications: false,
          canApproveApplications: false,
          canManageStudents: false,
          canViewStudentProgress: false,
          canAdvanceStudents: false,
          canViewStudentHistory: false,
          canAccessAdminDashboard: false,
          canAccessPartnerDashboard: false,
          canAccessStudentDashboard: false,
          canManageUsers: false,
          canViewAnalytics: false,
          canManageSettings: false,
          canExportData: false,
        }
    }
  }, [user])

  /**
   * Check if user has a specific permission
   */
  const hasPermission = (permission: keyof RolePermissions): boolean => {
    return permissions[permission]
  }

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissionList: (keyof RolePermissions)[]): boolean => {
    return permissionList.some(permission => permissions[permission])
  }

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissionList: (keyof RolePermissions)[]): boolean => {
    return permissionList.every(permission => permissions[permission])
  }

  /**
   * Check if user can access a specific scholarship category
   */
  const canAccessScholarshipCategory = (category: 'primary' | 'secondary' | 'university'): boolean => {
    switch (category) {
      case 'primary':
        return permissions.canApplyForPrimaryScholarships
      case 'secondary':
        return permissions.canApplyForSecondaryScholarships
      case 'university':
        return permissions.canApplyForUniversityScholarships
      default:
        return false
    }
  }

  /**
   * Get appropriate dashboard URL for user
   */
  const getDashboardUrl = (): string => {
    if (permissions.canAccessAdminDashboard) {
      return '/admin/dashboard'
    } else if (permissions.canAccessPartnerDashboard) {
      return '/dashboard/partner'
    } else if (permissions.canAccessStudentDashboard) {
      return '/dashboard/student'
    } else {
      return '/dashboard'
    }
  }

  /**
   * Get user role information
   */
  const roleInfo = useMemo(() => {
    if (!user) return null

    const role = user.role as UserRole
    const isPartnerOrg = role === 'partner_organization' || !!user.partner_organization_id
    const isIndividualStudent = !user.partner_organization_id && (role === 'user' || role === 'student')

    return {
      role,
      isAdmin: role === 'admin',
      isPartnerOrganization: isPartnerOrg,
      isIndividualStudent,
      displayName: isPartnerOrg ? 'Partner Organization' : 
                   isIndividualStudent ? 'Student' : 
                   role === 'admin' ? 'Administrator' : 'User'
    }
  }, [user])

  return {
    user,
    isLoading,
    permissions,
    roleInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessScholarshipCategory,
    getDashboardUrl,
  }
}

/**
 * Hook for checking specific permissions
 */
export function usePermission(permission: keyof RolePermissions) {
  const { hasPermission, isLoading } = useRoleAccess()
  return {
    hasPermission: hasPermission(permission),
    isLoading
  }
}

/**
 * Hook for scholarship category access
 */
export function useScholarshipCategoryAccess(category: 'primary' | 'secondary' | 'university') {
  const { canAccessScholarshipCategory, isLoading, roleInfo } = useRoleAccess()
  return {
    canAccess: canAccessScholarshipCategory(category),
    isLoading,
    roleInfo
  }
}
