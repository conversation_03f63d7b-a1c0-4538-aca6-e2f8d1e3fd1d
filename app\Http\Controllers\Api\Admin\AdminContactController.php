<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Contact Messages",
 *     description="Admin API Endpoints for Contact Message Management"
 * )
 */
class AdminContactController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/contact-messages",
     *     summary="Get all contact messages for admin",
     *     tags={"Admin Contact Messages"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Contact messages retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $priority = $request->get('priority');
            
            $query = ContactMessage::with(['assignedTo'])
                ->orderBy('created_at', 'desc');

            if ($status) {
                $query->where('status', $status);
            }

            if ($priority) {
                $query->where('priority', $priority);
            }

            $messages = $query->paginate($perPage);

            // Calculate summary statistics
            $stats = [
                'total_messages' => ContactMessage::count(),
                'unread_messages' => ContactMessage::where('status', 'unread')->count(),
                'in_progress_messages' => ContactMessage::where('status', 'in_progress')->count(),
                'resolved_messages' => ContactMessage::where('status', 'resolved')->count(),
                'high_priority_messages' => ContactMessage::where('priority', 'high')->count(),
                'messages_this_week' => ContactMessage::where('created_at', '>=', Carbon::now()->startOfWeek())->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Contact messages retrieved successfully',
                'data' => [
                    'messages' => $messages,
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve contact messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/contact-messages/{id}",
     *     summary="Get specific contact message details",
     *     tags={"Admin Contact Messages"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Contact message retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $message = ContactMessage::with(['assignedTo'])
                ->findOrFail($id);

            // Mark as read if it's unread
            if ($message->status === 'unread') {
                $message->update(['status' => 'read']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Contact message retrieved successfully',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Contact message not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/contact-messages/{id}",
     *     summary="Update contact message",
     *     tags={"Admin Contact Messages"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Contact message updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $message = ContactMessage::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'status' => 'in:unread,read,in_progress,resolved,closed',
                'priority' => 'in:low,medium,high,urgent',
                'notes' => 'nullable|string',
                'assigned_to' => 'nullable|exists:users,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            
            // Set resolved_at timestamp when status changes to resolved
            if (isset($data['status']) && $data['status'] === 'resolved' && $message->status !== 'resolved') {
                $data['resolved_at'] = now();
            }

            $message->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Contact message updated successfully',
                'data' => $message->load('assignedTo')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update contact message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/contact-messages/{id}",
     *     summary="Delete a contact message",
     *     tags={"Admin Contact Messages"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Contact message deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $message = ContactMessage::findOrFail($id);
            $message->delete();

            return response()->json([
                'success' => true,
                'message' => 'Contact message deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete contact message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resolve contact message
     */
    public function resolve(Request $request, $id): JsonResponse
    {
        try {
            $message = ContactMessage::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'resolution_notes' => 'required|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $message->update([
                'status' => 'resolved',
                'resolution_notes' => $request->resolution_notes,
                'resolved_at' => now(),
                'resolved_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Contact message resolved successfully',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to resolve contact message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign contact message to user
     */
    public function assign(Request $request, $id): JsonResponse
    {
        try {
            $message = ContactMessage::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'assigned_to' => 'required|exists:users,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $message->update([
                'assigned_to' => $request->assigned_to,
                'status' => $message->status === 'unread' ? 'in_progress' : $message->status
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Contact message assigned successfully',
                'data' => $message->load('assignedTo')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign contact message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get contact message statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_messages' => ContactMessage::count(),
                'unread_messages' => ContactMessage::where('status', 'unread')->count(),
                'in_progress_messages' => ContactMessage::where('status', 'in_progress')->count(),
                'resolved_messages' => ContactMessage::where('status', 'resolved')->count(),
                'high_priority_messages' => ContactMessage::where('priority', 'high')->count(),
                'urgent_messages' => ContactMessage::where('priority', 'urgent')->count(),
                'messages_today' => ContactMessage::whereDate('created_at', Carbon::today())->count(),
                'messages_this_week' => ContactMessage::where('created_at', '>=', Carbon::now()->startOfWeek())->count(),
                'messages_this_month' => ContactMessage::whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year)
                    ->count(),
                'average_resolution_time' => ContactMessage::whereNotNull('resolved_at')
                    ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as avg_hours')
                    ->value('avg_hours')
            ];

            return response()->json([
                'success' => true,
                'message' => 'Contact message statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
