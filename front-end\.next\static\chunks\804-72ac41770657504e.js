"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[804],{704:(e,t,r)=>{r.d(t,{B8:()=>L,UC:()=>S,bL:()=>M,l9:()=>I});var n=r(2115),a=r(5185),i=r(6081),o=r(9196),l=r(8905),d=r(3655),s=r(4315),c=r(5845),u=r(1285),f=r(5155),p="Tabs",[h,v]=(0,i.A)(p,[o.RG]),y=(0,o.RG)(),[b,k]=h(p),m=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:p="automatic",...h}=e,v=(0,s.jH)(l),[y,k]=(0,c.i)({prop:n,onChange:a,defaultProp:i});return(0,f.jsx)(b,{scope:r,baseId:(0,u.B)(),value:y,onValueChange:k,orientation:o,dir:v,activationMode:p,children:(0,f.jsx)(d.sG.div,{dir:v,"data-orientation":o,...h,ref:t})})});m.displayName=p;var w="TabsList",g=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,i=k(w,r),l=y(r);return(0,f.jsx)(o.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,f.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});g.displayName=w;var x="TabsTrigger",A=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,s=k(x,r),c=y(r),u=j(s.baseId,n),p=E(s.baseId,n),h=n===s.value;return(0,f.jsx)(o.q7,{asChild:!0,...c,focusable:!i,active:h,children:(0,f.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;h||i||!e||s.onValueChange(n)})})})});A.displayName=x;var C="TabsContent",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:i,children:o,...s}=e,c=k(C,r),u=j(c.baseId,a),p=E(c.baseId,a),h=a===c.value,v=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:i||h,children:r=>{let{present:n}=r;return(0,f.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:p,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:n&&o})}})});function j(e,t){return"".concat(e,"-trigger-").concat(t)}function E(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=C;var M=m,L=g,I=A,S=R},968:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(2115),a=r(3655),i=r(5155),o=n.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var l=o},1275:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(2115),a=r(2712);function i(e){let[t,r]=n.useState(void 0);return(0,a.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,a;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,a=t.blockSize}else n=e.offsetWidth,a=e.offsetHeight;r({width:n,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},1586:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2138:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2919:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3096:(e,t,r)=>{r.d(t,{Wx:()=>c});var n=r(2115),a=Object.defineProperty,i=(e,t,r)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,o=new Map,l=new WeakMap,d=0,s=void 0;function c(){var e;let{threshold:t,delay:r,trackVisibility:a,rootMargin:i,root:c,triggerOnce:u,skip:f,initialInView:p,fallbackInView:h,onChange:v}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[y,b]=n.useState(null),k=n.useRef(v),[m,w]=n.useState({inView:!!p,entry:void 0});k.current=v,n.useEffect(()=>{let e;if(!f&&y)return e=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:s;if(void 0===window.IntersectionObserver&&void 0!==n){let a=e.getBoundingClientRect();return t(n,{isIntersecting:n,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:a,intersectionRect:a,rootBounds:a}),()=>{}}let{id:a,observer:i,elements:c}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return"".concat(t,"_").concat("root"===t?(r=e.root)?(l.has(r)||(d+=1,l.set(r,d.toString())),l.get(r)):"0":e[t])}).toString(),r=o.get(t);if(!r){let n;let a=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var r;let i=t.isIntersecting&&n.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=i),null==(r=a.get(t.target))||r.forEach(e=>{e(i,t)})})},e);n=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:i,elements:a},o.set(t,r)}return r}(r),u=c.get(e)||[];return c.has(e)||c.set(e,u),u.push(t),i.observe(e),function(){u.splice(u.indexOf(t),1),0===u.length&&(c.delete(e),i.unobserve(e)),0===c.size&&(i.disconnect(),o.delete(a))}}(y,(t,r)=>{w({inView:t,entry:r}),k.current&&k.current(t,r),r.isIntersecting&&u&&e&&(e(),e=void 0)},{root:c,rootMargin:i,threshold:t,trackVisibility:a,delay:r},h),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,y,c,i,u,f,a,h,r]);let g=null==(e=m.entry)?void 0:e.target,x=n.useRef(void 0);y||!g||u||f||x.current===g||(x.current=g,w({inView:!!p,entry:void 0}));let A=[b,m.inView,m.entry];return A.ref=A[0],A.inView=A[1],A.entry=A[2],A}n.Component},4059:(e,t,r)=>{r.d(t,{C1:()=>O,bL:()=>P,q7:()=>q});var n=r(2115),a=r(5185),i=r(6101),o=r(6081),l=r(3655),d=r(9196),s=r(5845),c=r(4315),u=r(1275),f=r(7884),p=r(8905),h=r(5155),v="Radio",[y,b]=(0,o.A)(v),[k,m]=y(v),w=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:o,checked:d=!1,required:s,disabled:c,value:u="on",onCheck:f,form:p,...v}=e,[y,b]=n.useState(null),m=(0,i.s)(t,e=>b(e)),w=n.useRef(!1),g=!y||p||!!y.closest("form");return(0,h.jsxs)(k,{scope:r,checked:d,disabled:c,children:[(0,h.jsx)(l.sG.button,{type:"button",role:"radio","aria-checked":d,"data-state":C(d),"data-disabled":c?"":void 0,disabled:c,value:u,...v,ref:m,onClick:(0,a.m)(e.onClick,e=>{d||null==f||f(),g&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),g&&(0,h.jsx)(A,{control:y,bubbles:!w.current,name:o,value:u,checked:d,required:s,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});w.displayName=v;var g="RadioIndicator",x=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...a}=e,i=m(g,r);return(0,h.jsx)(p.C,{present:n||i.checked,children:(0,h.jsx)(l.sG.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t})})});x.displayName=g;var A=e=>{let{control:t,checked:r,bubbles:a=!0,...i}=e,o=n.useRef(null),l=(0,f.Z)(r),d=(0,u.X)(t);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==r&&t){let n=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(n)}},[l,r,a]),(0,h.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:o,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return e?"checked":"unchecked"}var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],j="RadioGroup",[E,M]=(0,o.A)(j,[d.RG,b]),L=(0,d.RG)(),I=b(),[S,z]=E(j),D=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:a,value:i,required:o=!1,disabled:u=!1,orientation:f,dir:p,loop:v=!0,onValueChange:y,...b}=e,k=L(r),m=(0,c.jH)(p),[w,g]=(0,s.i)({prop:i,defaultProp:a,onChange:y});return(0,h.jsx)(S,{scope:r,name:n,required:o,disabled:u,value:w,onValueChange:g,children:(0,h.jsx)(d.bL,{asChild:!0,...k,orientation:f,dir:m,loop:v,children:(0,h.jsx)(l.sG.div,{role:"radiogroup","aria-required":o,"aria-orientation":f,"data-disabled":u?"":void 0,dir:m,...b,ref:t})})})});D.displayName=j;var G="RadioGroupItem",V=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:o,...l}=e,s=z(G,r),c=s.disabled||o,u=L(r),f=I(r),p=n.useRef(null),v=(0,i.s)(t,p),y=s.value===l.value,b=n.useRef(!1);return n.useEffect(()=>{let e=e=>{R.includes(e.key)&&(b.current=!0)},t=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(d.q7,{asChild:!0,...u,focusable:!c,active:y,children:(0,h.jsx)(w,{disabled:c,required:s.required,checked:y,...f,...l,name:s.name,ref:v,onCheck:()=>s.onValueChange(l.value),onKeyDown:(0,a.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,a.m)(l.onFocus,()=>{var e;b.current&&(null===(e=p.current)||void 0===e||e.click())})})})});V.displayName=G;var N=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,a=I(r);return(0,h.jsx)(x,{...a,...n,ref:t})});N.displayName="RadioGroupIndicator";var P=D,q=V,O=N},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6981:(e,t,r)=>{r.d(t,{C1:()=>R,bL:()=>C});var n=r(2115),a=r(6101),i=r(6081),o=r(5185),l=r(5845),d=r(7884),s=r(1275),c=r(8905),u=r(3655),f=r(5155),p="Checkbox",[h,v]=(0,i.A)(p),[y,b]=h(p),k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:i,checked:d,defaultChecked:s,required:c,disabled:p,value:h="on",onCheckedChange:v,form:b,...k}=e,[m,w]=n.useState(null),C=(0,a.s)(t,e=>w(e)),R=n.useRef(!1),j=!m||b||!!m.closest("form"),[E=!1,M]=(0,l.i)({prop:d,defaultProp:s,onChange:v}),L=n.useRef(E);return n.useEffect(()=>{let e=null==m?void 0:m.form;if(e){let t=()=>M(L.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[m,M]),(0,f.jsxs)(y,{scope:r,state:E,disabled:p,children:[(0,f.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":x(E)?"mixed":E,"aria-required":c,"data-state":A(E),"data-disabled":p?"":void 0,disabled:p,value:h,...k,ref:C,onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(e.onClick,e=>{M(e=>!!x(e)||!e),j&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),j&&(0,f.jsx)(g,{control:m,bubbles:!R.current,name:i,value:h,checked:E,required:c,disabled:p,form:b,style:{transform:"translateX(-100%)"},defaultChecked:!x(s)&&s})]})});k.displayName=p;var m="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,i=b(m,r);return(0,f.jsx)(c.C,{present:n||x(i.state)||!0===i.state,children:(0,f.jsx)(u.sG.span,{"data-state":A(i.state),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=m;var g=e=>{let{control:t,checked:r,bubbles:a=!0,defaultChecked:i,...o}=e,l=n.useRef(null),c=(0,d.Z)(r),u=(0,s.X)(t);n.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let n=new Event("click",{bubbles:a});e.indeterminate=x(r),t.call(e,!x(r)&&r),e.dispatchEvent(n)}},[c,r,a]);let p=n.useRef(!x(r)&&r);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:p.current,...o,tabIndex:-1,ref:l,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return"indeterminate"===e}function A(e){return x(e)?"indeterminate":e?"checked":"unchecked"}var C=k,R=w},7884:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(2115);function a(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},7949:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},8564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8691:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},9428:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9929:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Sprout",[["path",{d:"M7 20h10",key:"e6iznv"}],["path",{d:"M10 20c5.5-2.5.8-6.4 3-10",key:"161w41"}],["path",{d:"M9.5 9.4c1.1.8 1.8 2.2 2.3 3.7-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2 2.8-.5 4.4 0 5.5.8z",key:"9gtqwd"}],["path",{d:"M14.1 6a7 7 0 0 0-1.1 4c1.9-.1 3.3-.6 4.3-1.4 1-1 1.6-2.3 1.7-4.6-2.7.1-4 1-4.9 2z",key:"bkxnd2"}]])}}]);