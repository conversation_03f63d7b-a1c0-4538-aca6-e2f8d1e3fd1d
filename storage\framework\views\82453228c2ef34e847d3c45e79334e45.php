

<?php $__env->startSection('title', 'Scholarship Applications'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.application-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}
.application-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.application-card.pending {
    border-left-color: #ffc107;
}
.application-card.under_review {
    border-left-color: #17a2b8;
}
.application-card.approved {
    border-left-color: #28a745;
}
.application-card.rejected {
    border-left-color: #dc3545;
}
.file-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 8px;
}
.score-input {
    width: 80px;
}
.comparison-view {
    max-height: 600px;
    overflow-y: auto;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Scholarship Applications</h1>
            <?php if(isset($scholarship)): ?>
                <p class="text-muted mb-0"><?php echo e($scholarship->title); ?></p>
            <?php endif; ?>
        </div>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="toggleView()">
                <i class="fas fa-th-list me-2"></i><span id="viewToggleText">Grid View</span>
            </button>
            <button class="btn btn-outline-success me-2" onclick="exportApplications()">
                <i class="fas fa-download me-2"></i>Export
            </button>
            <a href="<?php echo e(route('admin.scholarships.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Applications</div>
                            <div class="h4"><?php echo e($statistics['total_applications'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Pending Review</div>
                            <div class="h4"><?php echo e($statistics['pending_applications'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Under Review</div>
                            <div class="h4"><?php echo e($statistics['under_review_applications'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-search fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Approved</div>
                            <div class="h4"><?php echo e($statistics['approved_applications'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Rejected</div>
                            <div class="h4"><?php echo e($statistics['rejected_applications'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Average Score</div>
                            <div class="h4"><?php echo e(number_format($statistics['average_score'] ?? 0, 1)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-dark text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Award Amount</div>
                            <div class="h4">₦<?php echo e(number_format($statistics['total_award_amount'] ?? 0, 0)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-light text-dark mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-muted">Files Uploaded</div>
                            <div class="h4"><?php echo e($statistics['total_files'] ?? 0); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-paperclip fa-2x text-muted"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Applications by Category</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php
                            $primaryCount = $applications->filter(function($app) { return $app->scholarship->category === 'primary'; })->count();
                            $secondaryCount = $applications->filter(function($app) { return $app->scholarship->category === 'secondary'; })->count();
                            $universityCount = $applications->filter(function($app) { return $app->scholarship->category === 'university'; })->count();
                        ?>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="rounded-circle bg-info text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-child"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Primary School</div>
                                    <div class="text-muted"><?php echo e($primaryCount); ?> applications</div>
                                    <small class="text-info">Ages 5-13 (Parent/Guardian fills)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="rounded-circle bg-warning text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Secondary School</div>
                                    <div class="text-muted"><?php echo e($secondaryCount); ?> applications</div>
                                    <small class="text-warning">Ages 12-20 (Student fills)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">University</div>
                                    <div class="text-muted"><?php echo e($universityCount); ?> applications</div>
                                    <small class="text-success">Ages 16-35 (Student fills)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Management -->
    <div class="card shadow mb-4" id="applicationsCard">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Applications Management</h6>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="bulkReview()" id="bulkReviewBtn" disabled>
                    <i class="fas fa-tasks me-1"></i>Bulk Review
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="compareApplications()" id="compareBtn" disabled>
                    <i class="fas fa-balance-scale me-1"></i>Compare
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="refreshApplications()">
                    <i class="fas fa-sync me-1"></i>Refresh
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- Enhanced Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search applications..." id="searchApplications" value="<?php echo e(request('search')); ?>">
                        <button class="btn btn-outline-secondary" type="button" onclick="filterApplications()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterStatus" onchange="filterApplications()">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="under_review" <?php echo e(request('status') == 'under_review' ? 'selected' : ''); ?>>Under Review</option>
                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterCategory" onchange="filterApplications()">
                        <option value="">All Categories</option>
                        <option value="primary" <?php echo e(request('category') == 'primary' ? 'selected' : ''); ?>>Primary School</option>
                        <option value="secondary" <?php echo e(request('category') == 'secondary' ? 'selected' : ''); ?>>Secondary School</option>
                        <option value="university" <?php echo e(request('category') == 'university' ? 'selected' : ''); ?>>University</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterScholarship" onchange="filterApplications()">
                        <option value="">All Scholarships</option>
                        <?php if(isset($scholarships)): ?>
                            <?php $__currentLoopData = $scholarships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schol): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($schol->id); ?>" <?php echo e(request('scholarship_id') == $schol->id ? 'selected' : ''); ?>>
                                    <?php echo e(Str::limit($schol->title, 20)); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" id="filterDate" title="Filter by application date" value="<?php echo e(request('date')); ?>" onchange="filterApplications()">
                </div>
                <div class="col-md-1">
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()" title="Clear Filters">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Bulk Actions Bar -->
            <div class="alert alert-info d-none" id="bulkActionsBar">
                <div class="d-flex justify-content-between align-items-center">
                    <span><strong id="selectedCount">0</strong> applications selected</span>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-success" onclick="bulkApprove()">
                            <i class="fas fa-check me-1"></i>Approve Selected
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="bulkReject()">
                            <i class="fas fa-times me-1"></i>Reject Selected
                        </button>
                        <button class="btn btn-sm btn-info" onclick="bulkSetUnderReview()">
                            <i class="fas fa-search me-1"></i>Set Under Review
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>Clear Selection
                        </button>
                    </div>
                </div>
            </div>

            <!-- Table View -->
            <div class="table-responsive" id="tableView">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Applicant</th>
                            <th>Scholarship</th>
                            <th>Category</th>
                            <th>Application Date</th>
                            <th>Status</th>
                            <th>Score</th>
                            <th>Files</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="application-row <?php echo e($application->status); ?>" data-application-id="<?php echo e($application->id); ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input application-checkbox" value="<?php echo e($application->id); ?>" onchange="updateBulkActions()">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <?php echo e(strtoupper(substr($application->user->first_name ?? 'U', 0, 1) . substr($application->user->last_name ?? 'U', 0, 1))); ?>

                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo e($application->user->first_name ?? 'Unknown'); ?> <?php echo e($application->user->last_name ?? 'User'); ?></div>
                                            <small class="text-muted"><?php echo e($application->user->email ?? 'No email'); ?></small>
                                            <?php if($application->user->phone_number): ?>
                                                <br><small class="text-muted"><?php echo e($application->user->phone_number); ?></small>
                                            <?php endif; ?>
                                            <?php
                                                $formData = $application->form_data ?? [];
                                                $applicationData = $application->application_data ?? [];
                                                $allData = array_merge($formData, $applicationData);
                                            ?>
                                            <?php if($application->scholarship->category === 'primary' && isset($allData['parent_phone'])): ?>
                                                <br><small class="text-info"><i class="fas fa-phone me-1"></i>Parent: <?php echo e($allData['parent_phone']); ?></small>
                                            <?php elseif($application->scholarship->category === 'secondary' && isset($allData['parent_phone'])): ?>
                                                <br><small class="text-info"><i class="fas fa-phone me-1"></i>Parent: <?php echo e($allData['parent_phone']); ?></small>
                                            <?php elseif($application->scholarship->category === 'university' && isset($allData['matric_number'])): ?>
                                                <br><small class="text-info"><i class="fas fa-id-card me-1"></i><?php echo e($allData['matric_number'] ?? $allData['matriculation_number']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e(Str::limit($application->scholarship->title ?? 'Unknown Scholarship', 30)); ?></strong>
                                        <br><small class="text-muted">₦<?php echo e(number_format($application->scholarship->amount ?? 0, 0)); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                        $category = $application->scholarship->category ?? 'unknown';
                                        $categoryInfo = [
                                            'primary' => ['label' => 'Primary', 'class' => 'bg-info', 'age' => '5-13'],
                                            'secondary' => ['label' => 'Secondary', 'class' => 'bg-warning', 'age' => '12-20'],
                                            'university' => ['label' => 'University', 'class' => 'bg-success', 'age' => '16-35'],
                                        ];
                                        $info = $categoryInfo[$category] ?? ['label' => 'Unknown', 'class' => 'bg-secondary', 'age' => ''];
                                    ?>
                                    <span class="badge <?php echo e($info['class']); ?>"><?php echo e($info['label']); ?></span>
                                    <?php if($info['age']): ?>
                                        <br><small class="text-muted"><?php echo e($info['age']); ?> years</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo e($application->submitted_at ? $application->submitted_at->format('M d, Y') : 'N/A'); ?>

                                    <?php if($application->submitted_at): ?>
                                        <br><small class="text-muted"><?php echo e($application->submitted_at->diffForHumans()); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                        $statusClasses = [
                                            'pending' => 'bg-warning',
                                            'under_review' => 'bg-info',
                                            'approved' => 'bg-success',
                                            'rejected' => 'bg-danger'
                                        ];
                                        $statusClass = $statusClasses[$application->status] ?? 'bg-secondary';
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $application->status))); ?></span>
                                    <?php if($application->reviewed_at): ?>
                                        <br><small class="text-muted"><?php echo e($application->reviewed_at->format('M d')); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <?php if($application->score): ?>
                                            <span class="fw-bold <?php echo e($application->score >= 80 ? 'text-success' : ($application->score >= 60 ? 'text-warning' : 'text-danger')); ?>">
                                                <?php echo e(number_format($application->score, 1)); ?>/100
                                            </span>
                                            <br><small class="text-muted">
                                                <?php if($application->score >= 90): ?> Excellent
                                                <?php elseif($application->score >= 80): ?> Very Good
                                                <?php elseif($application->score >= 70): ?> Good
                                                <?php elseif($application->score >= 60): ?> Fair
                                                <?php else: ?> Poor
                                                <?php endif; ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Not scored</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <?php $fileCount = $application->files->count(); ?>
                                        <?php if($fileCount > 0): ?>
                                            <span class="badge bg-primary"><?php echo e($fileCount); ?> files</span>
                                            <?php $verifiedCount = $application->verifiedFiles->count(); ?>
                                            <?php if($verifiedCount > 0): ?>
                                                <br><small class="text-success"><?php echo e($verifiedCount); ?> verified</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">No files</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewApplication(<?php echo e($application->id); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if($application->status === 'pending' || $application->status === 'under_review'): ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="quickApprove(<?php echo e($application->id); ?>)" title="Quick Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="quickReject(<?php echo e($application->id); ?>)" title="Quick Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="More Actions">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="downloadApplication(<?php echo e($application->id); ?>)">
                                                    <i class="fas fa-download me-2"></i>Download Files
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="sendNotification(<?php echo e($application->id); ?>)">
                                                    <i class="fas fa-envelope me-2"></i>Send Notification
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="viewHistory(<?php echo e($application->id); ?>)">
                                                    <i class="fas fa-history me-2"></i>View History
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="addToComparison(<?php echo e($application->id); ?>)">
                                                    <i class="fas fa-balance-scale me-2"></i>Add to Comparison
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center py-5 text-muted">
                                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                                    <p class="mb-0">No scholarship applications found.</p>
                                    <small>Applications will appear here once students start applying.</small>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if(isset($applications) && method_exists($applications, 'links')): ?>
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing <?php echo e($applications->firstItem() ?? 0); ?> to <?php echo e($applications->lastItem() ?? 0); ?> of <?php echo e($applications->total() ?? 0); ?> applications
                    </div>
                    <?php echo e($applications->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Comprehensive Application Review Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applicationModalTitle">Application Review</h5>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2" id="applicationStatus">Loading...</span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <div class="row">
                    <!-- Left Column - Application Details -->
                    <div class="col-md-8">
                        <div id="applicationContent">
                            <!-- Content will be loaded here -->
                        </div>
                    </div>

                    <!-- Right Column - Review Panel -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Review Panel</h6>
                            </div>
                            <div class="card-body">
                                <!-- Status Management -->
                                <div class="mb-3">
                                    <label class="form-label">Application Status</label>
                                    <select class="form-select" id="reviewStatus">
                                        <option value="pending">Pending</option>
                                        <option value="under_review">Under Review</option>
                                        <option value="approved">Approved</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>

                                <!-- Scoring System -->
                                <div class="mb-3">
                                    <label class="form-label">Score (0-100)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="reviewScore" min="0" max="100" step="0.1">
                                        <span class="input-group-text">/100</span>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">
                                            90-100: Excellent | 80-89: Very Good | 70-79: Good | 60-69: Fair | <60: Poor
                                        </small>
                                    </div>
                                </div>

                                <!-- Award Amount -->
                                <div class="mb-3">
                                    <label class="form-label">Award Amount (₦)</label>
                                    <input type="number" class="form-control" id="reviewAwardAmount" min="0" step="1000">
                                    <div class="form-text">
                                        <small class="text-muted">Leave blank to use default scholarship amount</small>
                                    </div>
                                </div>

                                <!-- Review Notes -->
                                <div class="mb-3">
                                    <label class="form-label">Review Notes</label>
                                    <textarea class="form-control" id="reviewNotes" rows="4" placeholder="Add your review comments here..."></textarea>
                                </div>

                                <!-- Internal Notes -->
                                <div class="mb-3">
                                    <label class="form-label">Internal Notes</label>
                                    <textarea class="form-control" id="internalNotes" rows="3" placeholder="Internal notes (not visible to applicant)..."></textarea>
                                </div>

                                <!-- File Verification Status -->
                                <div class="mb-3">
                                    <label class="form-label">File Verification</label>
                                    <div id="fileVerificationList">
                                        <!-- File verification checkboxes will be populated here -->
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="quickApproveFromModal()">
                                        <i class="fas fa-check me-2"></i>Quick Approve
                                    </button>
                                    <button class="btn btn-danger" onclick="quickRejectFromModal()">
                                        <i class="fas fa-times me-2"></i>Quick Reject
                                    </button>
                                    <button class="btn btn-info" onclick="setUnderReviewFromModal()">
                                        <i class="fas fa-search me-2"></i>Set Under Review
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-primary" onclick="addToComparison(currentApplicationId)">
                    <i class="fas fa-balance-scale me-2"></i>Add to Comparison
                </button>
                <button type="button" class="btn btn-primary" onclick="saveReview()">
                    <i class="fas fa-save me-2"></i>Save Review
                </button>
            </div>
        </div>
    </div>
</div>

<!-- File Preview Modal -->
<div class="modal fade" id="filePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filePreviewTitle">File Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center" id="filePreviewContent">
                <!-- File preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="verifyCurrentFile()">
                    <i class="fas fa-check me-2"></i>Verify File
                </button>
                <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">
                    <i class="fas fa-download me-2"></i>Download
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Application Comparison Modal -->
<div class="modal fade" id="comparisonModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Application Comparison</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body comparison-view" id="comparisonContent">
                <!-- Comparison content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-danger" onclick="clearComparison()">
                    <i class="fas fa-trash me-2"></i>Clear Comparison
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Review Modal -->
<div class="modal fade" id="bulkReviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Review Applications</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>You have selected <strong id="bulkSelectedCount">0</strong> applications for bulk review.</p>

                <div class="mb-3">
                    <label class="form-label">Action</label>
                    <select class="form-select" id="bulkAction">
                        <option value="">Select Action</option>
                        <option value="approve">Approve All</option>
                        <option value="reject">Reject All</option>
                        <option value="under_review">Set Under Review</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Bulk Notes</label>
                    <textarea class="form-control" id="bulkNotes" rows="3" placeholder="Add notes for all selected applications..."></textarea>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action will be applied to all selected applications and cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkReview()">
                    <i class="fas fa-check me-2"></i>Execute Bulk Action
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Global variables
let currentApplicationId = null;
let selectedApplications = [];
let comparisonList = [];

// Pre-load all application data for modal display (no API calls needed)
const applicationsData = <?php echo json_encode($applicationsData, 15, 512) ?>;

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateBulkActions();
});

function initializeEventListeners() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Application viewing and management
function viewApplication(applicationId) {
    currentApplicationId = applicationId;

    // Find the application data from pre-loaded data (no API call needed)
    const application = applicationsData.find(app => app.id === applicationId);

    if (!application) {
        showError('Application data not found');
        return;
    }

    // Populate the modal with application data
    populateApplicationModal(application);

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    modal.show();
}

function populateApplicationModal(application) {
    // Update modal title and status
    document.getElementById('applicationModalTitle').textContent =
        `${application.user.first_name} ${application.user.last_name} - Application Review`;

    const statusBadge = document.getElementById('applicationStatus');
    statusBadge.textContent = application.status.replace('_', ' ').toUpperCase();
    statusBadge.className = `badge ${getStatusBadgeClass(application.status)} me-2`;

    // Populate application content
    const content = `
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>Applicant Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>Name:</strong></div>
                            <div class="col-sm-8">${application.user.first_name} ${application.user.last_name}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Email:</strong></div>
                            <div class="col-sm-8">${application.user.email}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Phone:</strong></div>
                            <div class="col-sm-8">${application.user.phone || 'Not provided'}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Applied:</strong></div>
                            <div class="col-sm-8">${new Date(application.submitted_at).toLocaleDateString()}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Scholarship Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>Title:</strong></div>
                            <div class="col-sm-8">${application.scholarship.title}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Category:</strong></div>
                            <div class="col-sm-8">
                                <span class="badge ${getCategoryBadgeClass(application.scholarship.category)}">
                                    ${application.scholarship.category.charAt(0).toUpperCase() + application.scholarship.category.slice(1)}
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Amount:</strong></div>
                            <div class="col-sm-8">₦${Number(application.scholarship.amount).toLocaleString()}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Status:</strong></div>
                            <div class="col-sm-8">
                                <span class="badge ${getStatusBadgeClass(application.status)}">
                                    ${application.status.replace('_', ' ').toUpperCase()}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-form me-2"></i>Application Form Data</h6>
            </div>
            <div class="card-body">
                ${generateFormDataHTML(application.form_data, application.application_data, application.scholarship.category)}
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-paperclip me-2"></i>Uploaded Files</h6>
                <span class="badge bg-primary">${application.files ? application.files.length : 0} files</span>
            </div>
            <div class="card-body">
                ${generateFilesHTML(application.files)}
            </div>
        </div>

        ${application.review_notes || application.internal_notes ? `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Review History</h6>
            </div>
            <div class="card-body">
                ${application.review_notes ? `<p><strong>Review Notes:</strong> ${application.review_notes}</p>` : ''}
                ${application.internal_notes ? `<p><strong>Internal Notes:</strong> ${application.internal_notes}</p>` : ''}
                ${application.reviewed_at ? `<p><small class="text-muted">Last reviewed: ${new Date(application.reviewed_at).toLocaleString()}</small></p>` : ''}
            </div>
        </div>
        ` : ''}
    `;

    document.getElementById('applicationContent').innerHTML = content;

    // Populate review panel
    populateReviewPanel(application);
}

function generateFormDataHTML(formData, applicationData, category) {
    // Combine both form_data and application_data
    const allData = { ...formData, ...applicationData };

    if (!allData || Object.keys(allData).length === 0) {
        return '<p class="text-muted">No form data available</p>';
    }

    let html = '';

    // Category-specific field organization
    if (category === 'primary') {
        html += generatePrimarySchoolDataHTML(allData);
    } else if (category === 'secondary') {
        html += generateSecondarySchoolDataHTML(allData);
    } else if (category === 'university') {
        html += generateUniversityDataHTML(allData);
    } else {
        // Fallback for unknown categories
        html += generateGenericDataHTML(allData);
    }

    return html;
}

function generatePrimarySchoolDataHTML(data) {
    let html = '<div class="row">';

    // Student Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-primary border-bottom pb-2"><i class="fas fa-child me-2"></i>Student Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Student Full Name:</strong>
                        <p class="mb-0 mt-1">${data.student_full_name || data.student_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Age:</strong>
                        <p class="mb-0 mt-1">${data.age || data.student_age || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Current Class:</strong>
                        <p class="mb-0 mt-1">${data.current_class || data.student_class || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>School Name:</strong>
                        <p class="mb-0 mt-1">${data.school_name || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Parent/Guardian Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-success border-bottom pb-2"><i class="fas fa-users me-2"></i>Parent/Guardian Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Father's Name:</strong>
                        <p class="mb-0 mt-1">${data.father_name || data.parent_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Mother's Name:</strong>
                        <p class="mb-0 mt-1">${data.mother_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Parent Phone:</strong>
                        <p class="mb-0 mt-1">${data.parent_phone || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Home Address:</strong>
                        <p class="mb-0 mt-1">${data.home_address || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // School Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-info border-bottom pb-2"><i class="fas fa-school me-2"></i>School Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Headmaster Name:</strong>
                        <p class="mb-0 mt-1">${data.headmaster_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>School Account Number:</strong>
                        <p class="mb-0 mt-1">${data.school_account_number || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Current School Fee:</strong>
                        <p class="mb-0 mt-1">₦${data.current_school_fee || data.school_fee || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Application Details Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-warning border-bottom pb-2"><i class="fas fa-file-alt me-2"></i>Application Details</h6>
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Reason for Scholarship:</strong>
                        <p class="mb-0 mt-1">${data.reason_for_scholarship || data.reason || 'Not provided'}</p>
                    </div>
                </div>
                ${data.supporting_information ? `
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Supporting Information:</strong>
                        <p class="mb-0 mt-1">${data.supporting_information}</p>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    html += '</div>';
    return html;
}

function generateSecondarySchoolDataHTML(data) {
    let html = '<div class="row">';

    // Student Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-primary border-bottom pb-2"><i class="fas fa-user-graduate me-2"></i>Student Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Student Full Name:</strong>
                        <p class="mb-0 mt-1">${data.student_full_name || data.student_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Age:</strong>
                        <p class="mb-0 mt-1">${data.age || data.student_age || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Class:</strong>
                        <p class="mb-0 mt-1">${data.class || data.student_class || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>School Name:</strong>
                        <p class="mb-0 mt-1">${data.school_name || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Parent Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-success border-bottom pb-2"><i class="fas fa-users me-2"></i>Parent Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Parent Phone:</strong>
                        <p class="mb-0 mt-1">${data.parent_phone || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Address:</strong>
                        <p class="mb-0 mt-1">${data.address || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // School Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-info border-bottom pb-2"><i class="fas fa-school me-2"></i>School Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Principal Name:</strong>
                        <p class="mb-0 mt-1">${data.principal_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Principal Account Number:</strong>
                        <p class="mb-0 mt-1">${data.principal_account_number || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>School Fee Amount:</strong>
                        <p class="mb-0 mt-1">₦${data.school_fee_amount || data.school_fee || 'Not provided'}</p>
                    </div>
                </div>
                ${data.financial_officer_name ? `
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Financial Officer Name:</strong>
                        <p class="mb-0 mt-1">${data.financial_officer_name}</p>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    // Application Details Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-warning border-bottom pb-2"><i class="fas fa-file-alt me-2"></i>Application Details</h6>
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Reason for Scholarship:</strong>
                        <p class="mb-0 mt-1">${data.reason_for_scholarship || data.reason || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    html += '</div>';
    return html;
}

function generateUniversityDataHTML(data) {
    let html = '<div class="row">';

    // Student Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-primary border-bottom pb-2"><i class="fas fa-graduation-cap me-2"></i>Student Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Full Name:</strong>
                        <p class="mb-0 mt-1">${data.full_name || data.student_name || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Age:</strong>
                        <p class="mb-0 mt-1">${data.age || data.student_age || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Email Address:</strong>
                        <p class="mb-0 mt-1">${data.email_address || data.student_email || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Phone Number:</strong>
                        <p class="mb-0 mt-1">${data.phone_number || data.student_phone || 'Not provided'}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Academic Information Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-success border-bottom pb-2"><i class="fas fa-book me-2"></i>Academic Information</h6>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Matriculation Number:</strong>
                        <p class="mb-0 mt-1">${data.matriculation_number || data.matric_number || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Course of Study:</strong>
                        <p class="mb-0 mt-1">${data.course_of_study || 'Not provided'}</p>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Current Level:</strong>
                        <p class="mb-0 mt-1">${data.current_level || 'Not provided'}</p>
                    </div>
                </div>
                ${data.current_gpa ? `
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Current GPA:</strong>
                        <p class="mb-0 mt-1">${data.current_gpa}</p>
                    </div>
                </div>
                ` : ''}
                ${data.institution_name ? `
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <strong>Institution Name:</strong>
                        <p class="mb-0 mt-1">${data.institution_name}</p>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    // Application Details Section
    html += `
        <div class="col-12 mb-4">
            <h6 class="text-warning border-bottom pb-2"><i class="fas fa-file-alt me-2"></i>Application Details</h6>
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Reason for Scholarship:</strong>
                        <p class="mb-0 mt-1">${data.reason_for_scholarship || data.reason || 'Not provided'}</p>
                    </div>
                </div>
                ${data.personal_statement ? `
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Personal Statement:</strong>
                        <p class="mb-0 mt-1">${data.personal_statement}</p>
                    </div>
                </div>
                ` : ''}
                ${data.academic_achievements ? `
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Academic Achievements:</strong>
                        <p class="mb-0 mt-1">${data.academic_achievements}</p>
                    </div>
                </div>
                ` : ''}
                ${data.financial_need_statement ? `
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Financial Need Statement:</strong>
                        <p class="mb-0 mt-1">${data.financial_need_statement}</p>
                    </div>
                </div>
                ` : ''}
                ${data.career_goals ? `
                <div class="col-12 mb-3">
                    <div class="border rounded p-3">
                        <strong>Career Goals:</strong>
                        <p class="mb-0 mt-1">${data.career_goals}</p>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    html += '</div>';
    return html;
}

function generateGenericDataHTML(data) {
    let html = '<div class="row">';
    let count = 0;

    for (const [key, value] of Object.entries(data)) {
        if (count % 2 === 0 && count > 0) {
            html += '</div><div class="row">';
        }

        html += `
            <div class="col-md-6 mb-3">
                <div class="border rounded p-3">
                    <strong>${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</strong>
                    <p class="mb-0 mt-1">${Array.isArray(value) ? value.join(', ') : value}</p>
                </div>
            </div>
        `;
        count++;
    }

    html += '</div>';
    return html;
}

function generateFilesHTML(files) {
    if (!files || files.length === 0) {
        return '<p class="text-muted">No files uploaded</p>';
    }

    let html = '<div class="row">';

    files.forEach(file => {
        const isImage = file.mime_type && file.mime_type.startsWith('image/');
        const isVerified = file.is_verified;

        html += `
            <div class="col-md-4 mb-3">
                <div class="card ${isVerified ? 'border-success' : 'border-warning'}">
                    <div class="card-body text-center">
                        ${isImage ?
                            `<img src="${file.file_url}" class="file-preview mb-2" alt="${file.original_name}">` :
                            `<i class="fas fa-file fa-3x text-muted mb-2"></i>`
                        }
                        <h6 class="card-title">${file.field_name}</h6>
                        <p class="card-text small text-muted">${file.original_name}</p>
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewFile(${file.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadFile(${file.id})">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm ${isVerified ? 'btn-success' : 'btn-outline-warning'}"
                                    onclick="toggleFileVerification(${file.id})">
                                <i class="fas ${isVerified ? 'fa-check' : 'fa-question'}"></i>
                            </button>
                        </div>
                        ${isVerified ?
                            '<small class="text-success mt-1 d-block"><i class="fas fa-check-circle"></i> Verified</small>' :
                            '<small class="text-warning mt-1 d-block"><i class="fas fa-exclamation-circle"></i> Pending Verification</small>'
                        }
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function populateReviewPanel(application) {
    // Set current values
    document.getElementById('reviewStatus').value = application.status;
    document.getElementById('reviewScore').value = application.score || '';
    document.getElementById('reviewAwardAmount').value = application.award_amount || '';
    document.getElementById('reviewNotes').value = application.review_notes || '';
    document.getElementById('internalNotes').value = application.internal_notes || '';

    // Populate file verification list
    const fileVerificationList = document.getElementById('fileVerificationList');
    if (application.files && application.files.length > 0) {
        let html = '';
        application.files.forEach(file => {
            html += `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="file_${file.id}"
                           ${file.is_verified ? 'checked' : ''}
                           onchange="toggleFileVerification(${file.id})">
                    <label class="form-check-label" for="file_${file.id}">
                        ${file.field_name} - ${file.original_name}
                    </label>
                </div>
            `;
        });
        fileVerificationList.innerHTML = html;
    } else {
        fileVerificationList.innerHTML = '<p class="text-muted small">No files to verify</p>';
    }
}

// Helper functions
function getStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'under_review': 'bg-info',
        'approved': 'bg-success',
        'rejected': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getCategoryBadgeClass(category) {
    const classes = {
        'primary': 'bg-info',
        'secondary': 'bg-warning',
        'university': 'bg-success'
    };
    return classes[category] || 'bg-secondary';
}

function showSuccess(message) {
    // You can integrate with your preferred notification system
    alert('Success: ' + message);
}

function showError(message) {
    // You can integrate with your preferred notification system
    alert('Error: ' + message);
}

// Review actions
function saveReview() {
    if (!currentApplicationId) return;

    // Create a form and submit it to the web route
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/scholarship-applications/${currentApplicationId}/review`;

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    // Add method override for PUT
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    form.appendChild(methodField);

    // Add form fields
    const fieldMappings = {
        'status': 'reviewStatus',
        'score': 'reviewScore',
        'award_amount': 'reviewAwardAmount',
        'review_notes': 'reviewNotes',
        'internal_notes': 'internalNotes'
    };

    Object.keys(fieldMappings).forEach(fieldName => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = fieldName;
        const elementId = fieldMappings[fieldName];
        const element = document.getElementById(elementId);
        input.value = element ? element.value : '';
        form.appendChild(input);
    });

    // Submit form
    document.body.appendChild(form);
    form.submit();
}

function quickApprove(applicationId) {
    if (confirm('Are you sure you want to approve this application?')) {
        updateApplicationStatus(applicationId, 'approved');
    }
}

function quickReject(applicationId) {
    if (confirm('Are you sure you want to reject this application?')) {
        updateApplicationStatus(applicationId, 'rejected');
    }
}

function quickApproveFromModal() {
    document.getElementById('reviewStatus').value = 'approved';
    saveReview();
}

function quickRejectFromModal() {
    document.getElementById('reviewStatus').value = 'rejected';
    saveReview();
}

function setUnderReviewFromModal() {
    document.getElementById('reviewStatus').value = 'under_review';
    saveReview();
}

function updateApplicationStatus(applicationId, status) {
    // Create a form and submit it to the web route
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/scholarship-applications/${applicationId}/update-status`;

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    // Add method override for PUT
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    form.appendChild(methodField);

    // Add status field
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'status';
    statusField.value = status;
    form.appendChild(statusField);

    // Submit form
    document.body.appendChild(form);
    form.submit();
}

// File management
function previewFile(fileId) {
    // Find file data from pre-loaded application data (no API call needed)
    let fileData = null;

    for (const application of applicationsData) {
        if (application.files) {
            fileData = application.files.find(file => file.id === fileId);
            if (fileData) break;
        }
    }

    if (fileData) {
        showFilePreview(fileData);
    } else {
        showError('File not found');
    }
}

function showFilePreview(file) {
    const modal = new bootstrap.Modal(document.getElementById('filePreviewModal'));
    document.getElementById('filePreviewTitle').textContent = file.original_name;

    let content = '';
    if (file.mime_type && file.mime_type.startsWith('image/')) {
        content = `<img src="${file.file_url}" class="img-fluid" alt="${file.original_name}">`;
    } else if (file.mime_type === 'application/pdf') {
        content = `<embed src="${file.file_url}" type="application/pdf" width="100%" height="500px">`;
    } else {
        content = `
            <div class="text-center py-5">
                <i class="fas fa-file fa-5x text-muted mb-3"></i>
                <h5>${file.original_name}</h5>
                <p class="text-muted">File type: ${file.mime_type}</p>
                <p class="text-muted">Size: ${(file.file_size / 1024).toFixed(2)} KB</p>
                <button class="btn btn-primary" onclick="downloadFile(${file.id})">
                    <i class="fas fa-download me-2"></i>Download File
                </button>
            </div>
        `;
    }

    document.getElementById('filePreviewContent').innerHTML = content;
    modal.show();
}

function downloadFile(fileId) {
    // Use web route instead of API route
    window.open(`/admin/scholarships/applications/files/${fileId}/download`, '_blank');
}

function toggleFileVerification(fileId) {
    // Use form submission instead of AJAX
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/scholarships/applications/files/${fileId}/verify`;

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    // Add method override for PUT
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    form.appendChild(methodField);

    document.body.appendChild(form);
    form.submit();
}

function downloadApplication(applicationId) {
    // Use web route instead of API route
    window.open(`/admin/scholarships/applications/${applicationId}/download`, '_blank');
}

function sendNotification(applicationId) {
    if (confirm('Send status notification to applicant?')) {
        // Use form submission instead of AJAX
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/scholarships/applications/${applicationId}/notify`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk operations
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.application-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.application-checkbox:checked');
    const count = checkboxes.length;

    selectedApplications = Array.from(checkboxes).map(cb => cb.value);

    // Update bulk action buttons
    const bulkReviewBtn = document.getElementById('bulkReviewBtn');
    const compareBtn = document.getElementById('compareBtn');
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    const selectedCountSpan = document.getElementById('selectedCount');

    if (bulkReviewBtn) bulkReviewBtn.disabled = count === 0;
    if (compareBtn) compareBtn.disabled = count < 2;

    if (count > 0) {
        bulkActionsBar.classList.remove('d-none');
        selectedCountSpan.textContent = count;
    } else {
        bulkActionsBar.classList.add('d-none');
    }
}

function bulkReview() {
    if (selectedApplications.length === 0) {
        showError('Please select applications to review');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('bulkReviewModal'));
    document.getElementById('bulkSelectedCount').textContent = selectedApplications.length;
    modal.show();
}

function executeBulkReview() {
    const action = document.getElementById('bulkAction').value;
    const notes = document.getElementById('bulkNotes').value;

    if (!action) {
        showError('Please select an action');
        return;
    }

    // Use form submission instead of AJAX
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/scholarships/applications/bulk-review';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    // Add application IDs
    selectedApplications.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'application_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    // Add notes
    const notesInput = document.createElement('input');
    notesInput.type = 'hidden';
    notesInput.name = 'notes';
    notesInput.value = notes;
    form.appendChild(notesInput);

    document.body.appendChild(form);
    form.submit();
}

function bulkApprove() {
    if (confirm(`Approve ${selectedApplications.length} selected applications?`)) {
        executeBulkAction('approved');
    }
}

function bulkReject() {
    if (confirm(`Reject ${selectedApplications.length} selected applications?`)) {
        executeBulkAction('rejected');
    }
}

function bulkSetUnderReview() {
    if (confirm(`Set ${selectedApplications.length} selected applications under review?`)) {
        executeBulkAction('under_review');
    }
}

function executeBulkAction(status) {
    // Use form submission instead of AJAX
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/scholarships/applications/bulk-status';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    // Add application IDs
    selectedApplications.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'application_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add status
    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

function clearSelection() {
    document.getElementById('selectAll').checked = false;
    document.querySelectorAll('.application-checkbox').forEach(cb => cb.checked = false);
    updateBulkActions();
}

// Filtering and search
function filterApplications() {
    const search = document.getElementById('searchApplications').value;
    const status = document.getElementById('filterStatus').value;
    const category = document.getElementById('filterCategory').value;
    const scholarship = document.getElementById('filterScholarship').value;
    const date = document.getElementById('filterDate').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (status) params.append('status', status);
    if (category) params.append('category', category);
    if (scholarship) params.append('scholarship_id', scholarship);
    if (date) params.append('date', date);

    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.location.href = newUrl;
}

function clearFilters() {
    document.getElementById('searchApplications').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterCategory').value = '';
    document.getElementById('filterScholarship').value = '';
    document.getElementById('filterDate').value = '';

    window.location.href = window.location.pathname;
}

function refreshApplications() {
    location.reload();
}

function exportApplications() {
    const params = new URLSearchParams(window.location.search);
    params.append('export', 'true');
    // Use web route instead of API route
    window.open('/admin/scholarships/applications/export?' + params.toString(), '_blank');
}

// Comparison functionality
function addToComparison(applicationId) {
    if (!comparisonList.includes(applicationId)) {
        comparisonList.push(applicationId);
        showSuccess('Application added to comparison');
        updateComparisonButton();
    } else {
        showError('Application already in comparison');
    }
}

function compareApplications() {
    if (selectedApplications.length < 2) {
        showError('Please select at least 2 applications to compare');
        return;
    }

    // Use selected applications for comparison
    comparisonList = [...selectedApplications];
    showComparisonModal();
}

function showComparisonModal() {
    const modal = new bootstrap.Modal(document.getElementById('comparisonModal'));

    // Get comparison data from pre-loaded data (no API call needed)
    const comparisonData = comparisonList.map(id => {
        return applicationsData.find(app => app.id === id);
    }).filter(app => app !== undefined);

    if (comparisonData.length > 0) {
        populateComparisonModal(comparisonData);
    } else {
        showError('No applications selected for comparison');
        return;
    }

    modal.show();
}

function populateComparisonModal(applications) {
    let html = '<div class="table-responsive"><table class="table table-bordered">';

    // Header
    html += '<thead><tr><th>Field</th>';
    applications.forEach(app => {
        html += `<th>${app.user.first_name} ${app.user.last_name}</th>`;
    });
    html += '</tr></thead><tbody>';

    // Comparison rows
    const fields = [
        { key: 'scholarship.title', label: 'Scholarship' },
        { key: 'scholarship.category', label: 'Category' },
        { key: 'status', label: 'Status' },
        { key: 'score', label: 'Score' },
        { key: 'submitted_at', label: 'Applied Date' },
        { key: 'user.email', label: 'Email' }
    ];

    fields.forEach(field => {
        html += `<tr><td><strong>${field.label}</strong></td>`;
        applications.forEach(app => {
            const value = getNestedValue(app, field.key);
            html += `<td>${value || 'N/A'}</td>`;
        });
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    document.getElementById('comparisonContent').innerHTML = html;
}

function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
}

function clearComparison() {
    comparisonList = [];
    updateComparisonButton();
    bootstrap.Modal.getInstance(document.getElementById('comparisonModal')).hide();
}

function updateComparisonButton() {
    const compareBtn = document.getElementById('compareBtn');
    if (compareBtn) {
        compareBtn.textContent = comparisonList.length > 0 ?
            `Compare (${comparisonList.length})` : 'Compare';
    }
}

// View toggle functionality
function toggleView() {
    // This would toggle between table and grid view
    // Implementation depends on your specific requirements
    const viewToggleText = document.getElementById('viewToggleText');
    if (viewToggleText.textContent === 'Grid View') {
        viewToggleText.textContent = 'Table View';
        // Switch to grid view
    } else {
        viewToggleText.textContent = 'Grid View';
        // Switch to table view
    }
}

function viewHistory(applicationId) {
    // Implementation for viewing application history
    showSuccess('History view feature coming soon');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/scholarships/applications.blade.php ENDPATH**/ ?>