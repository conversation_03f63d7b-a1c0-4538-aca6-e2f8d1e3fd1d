@extends('layouts.admin')

@section('title', 'Edit Donation Campaign')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Donation Campaign</h1>
        <div>
            <a href="{{ route('admin.donations.campaigns.show', $campaign->id) }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="{{ route('admin.donations.campaigns') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Campaigns
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.donations.campaigns.update', $campaign->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Campaign Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ old('title', $campaign->title) }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required>{{ old('description', $campaign->description) }}</textarea>
                        </div>

                        <!-- Goal and Category -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="goal_amount" class="form-label">Goal Amount (₦) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="goal_amount" name="goal_amount" step="0.01" min="0" value="{{ old('goal_amount', $campaign->goal_amount) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="education" {{ old('category', $campaign->category) == 'education' ? 'selected' : '' }}>Education</option>
                                        <option value="healthcare" {{ old('category', $campaign->category) == 'healthcare' ? 'selected' : '' }}>Healthcare</option>
                                        <option value="emergency" {{ old('category', $campaign->category) == 'emergency' ? 'selected' : '' }}>Emergency Relief</option>
                                        <option value="community" {{ old('category', $campaign->category) == 'community' ? 'selected' : '' }}>Community Development</option>
                                        <option value="environment" {{ old('category', $campaign->category) == 'environment' ? 'selected' : '' }}>Environment</option>
                                        <option value="other" {{ old('category', $campaign->category) == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Timeline -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ old('start_date', $campaign->start_date ? $campaign->start_date->format('Y-m-d') : '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ old('end_date', $campaign->end_date ? $campaign->end_date->format('Y-m-d') : '') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Campaign Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            @if($campaign->image)
                            <div class="mt-2">
                                <small class="text-muted">Current image:</small><br>
                                <img src="{{ asset('storage/' . $campaign->image) }}" alt="Current image" class="img-thumbnail" style="max-width: 200px;">
                            </div>
                            @endif
                            <div class="form-text">Upload a new image to replace the current one (optional)</div>
                        </div>

                        <!-- Status and Featured -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="draft" {{ old('status', $campaign->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="active" {{ old('status', $campaign->status) == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="paused" {{ old('status', $campaign->status) == 'paused' ? 'selected' : '' }}>Paused</option>
                                        <option value="completed" {{ old('status', $campaign->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" {{ old('featured', $campaign->featured) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="featured">
                                            Featured Campaign
                                        </label>
                                        <div class="form-text">Featured campaigns appear prominently on the website</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Campaign
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Current Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <span class="badge bg-{{ $campaign->status === 'active' ? 'success' : ($campaign->status === 'completed' ? 'primary' : ($campaign->status === 'paused' ? 'warning' : 'secondary')) }} p-3 mb-3" style="font-size: 1rem;">
                            {{ ucfirst($campaign->status) }}
                        </span>
                        <p class="text-muted mb-0">Current campaign status</p>
                    </div>
                </div>
            </div>

            <!-- Progress Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Progress</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4 class="text-primary">{{ number_format($campaign->progress_percentage, 1) }}%</h4>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: {{ $campaign->progress_percentage }}%"></div>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-success">₦{{ number_format($campaign->raised_amount, 0) }}</h6>
                                    <small class="text-muted">Raised</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-info">₦{{ number_format($campaign->goal_amount, 0) }}</h6>
                                <small class="text-muted">Goal</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0">{{ $campaign->created_at->format('F j, Y') }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0">{{ $campaign->updated_at->format('F j, Y g:i A') }}</p>
                    </div>
                    @if($campaign->creator)
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0">{{ $campaign->creator->first_name }} {{ $campaign->creator->last_name }}</p>
                    </div>
                    @endif
                    @if($campaign->donations)
                    <div class="mb-3">
                        <small class="text-muted">Total Donations</small>
                        <p class="mb-0">{{ $campaign->donations->count() }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Preview</h6>
                </div>
                <div class="card-body">
                    <h6 id="preview-title">{{ $campaign->title }}</h6>
                    <p class="text-muted small" id="preview-category">{{ $campaign->category ? ucfirst($campaign->category) : 'No category' }}</p>
                    <p class="text-muted small" id="preview-goal">
                        <i class="fas fa-bullseye me-1"></i>Goal: ₦<span id="preview-goal-amount">{{ number_format($campaign->goal_amount, 0) }}</span>
                    </p>
                    @if($campaign->end_date)
                    <p class="text-muted small" id="preview-deadline">
                        <i class="fas fa-calendar-times me-1"></i>Ends {{ $campaign->end_date->format('M j, Y') }}
                    </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const categoryInput = document.getElementById('category');
    const goalInput = document.getElementById('goal_amount');
    const endDateInput = document.getElementById('end_date');
    
    titleInput.addEventListener('input', function() {
        document.getElementById('preview-title').textContent = this.value || '{{ $campaign->title }}';
    });
    
    categoryInput.addEventListener('change', function() {
        const categoryText = this.options[this.selectedIndex].text;
        document.getElementById('preview-category').textContent = categoryText === 'Select Category' ? 'No category' : categoryText;
    });
    
    goalInput.addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        document.getElementById('preview-goal-amount').textContent = amount.toLocaleString();
    });
    
    if (endDateInput) {
        endDateInput.addEventListener('change', function() {
            const previewDeadline = document.getElementById('preview-deadline');
            if (this.value) {
                const date = new Date(this.value);
                if (previewDeadline) {
                    previewDeadline.innerHTML = '<i class="fas fa-calendar-times me-1"></i>Ends ' + date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                } else {
                    const newDeadline = document.createElement('p');
                    newDeadline.className = 'text-muted small';
                    newDeadline.id = 'preview-deadline';
                    newDeadline.innerHTML = '<i class="fas fa-calendar-times me-1"></i>Ends ' + date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                    document.getElementById('preview-goal').parentNode.appendChild(newDeadline);
                }
            } else if (previewDeadline) {
                previewDeadline.remove();
            }
        });
    }

    // Set minimum date to today for start and end dates
    const today = new Date().toISOString().split('T')[0];
    const startDateInput = document.getElementById('start_date');
    const endDateInputForMin = document.getElementById('end_date');
    
    startDateInput.setAttribute('min', today);
    endDateInputForMin.setAttribute('min', today);
    
    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInputForMin.setAttribute('min', this.value);
    });
});
</script>
@endsection
