@extends('auth.layout')

@section('title', 'Register')
@section('header-title', 'Join HALIMAKQ NGO')
@section('header-subtitle', 'Choose your account type to get started')

@section('content')
    @if ($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if (session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    <form method="POST" action="{{ route('register') }}" id="registrationForm">
        @csrf
        
        <!-- Role Selection -->
        <div class="mb-4">
            <label class="form-label">Select Account Type <span class="text-danger">*</span></label>
            <div class="role-selector">
                <div class="role-card" data-role="1">
                    <i class="fas fa-user"></i>
                    <h5>Normal User</h5>
                    <p>Browse content and resources</p>
                    <input type="radio" name="role_id" value="1" class="d-none" {{ old('role_id') == '1' ? 'checked' : '' }}>
                </div>
                
                <div class="role-card" data-role="2">
                    <i class="fas fa-graduation-cap"></i>
                    <h5>University Student</h5>
                    <p>Apply for university scholarships</p>
                    <input type="radio" name="role_id" value="2" class="d-none" {{ old('role_id') == '2' ? 'checked' : '' }}>
                </div>
                
                <div class="role-card" data-role="3">
                    <i class="fas fa-school"></i>
                    <h5>Partner Organization</h5>
                    <p>Manage students and applications</p>
                    <input type="radio" name="role_id" value="3" class="d-none" {{ old('role_id') == '3' ? 'checked' : '' }}>
                </div>
            </div>
            @error('role_id')
                <div class="text-danger small mt-1">{{ $message }}</div>
            @enderror
        </div>

        <!-- Basic Information (Common for all roles) -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                       id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                @error('first_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                       id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                @error('last_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="mb-3">
            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                   id="email" name="email" value="{{ old('email') }}" required>
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="phone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
            <input type="tel" class="form-control @error('phone_number') is-invalid @enderror" 
                   id="phone_number" name="phone_number" value="{{ old('phone_number') }}" required>
            @error('phone_number')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                       id="password" name="password" required>
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" required>
            </div>
        </div>

        <!-- University Student Fields -->
        <div id="student-fields" class="role-specific-fields" style="display: none;">
            <h5 class="text-success mb-3"><i class="fas fa-graduation-cap me-2"></i>Academic Information</h5>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="student_id" class="form-label">Student ID</label>
                    <input type="text" class="form-control @error('student_id') is-invalid @enderror" 
                           id="student_id" name="student_id" value="{{ old('student_id') }}">
                    @error('student_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="matriculation_number" class="form-label">Matriculation Number</label>
                    <input type="text" class="form-control @error('matriculation_number') is-invalid @enderror" 
                           id="matriculation_number" name="matriculation_number" value="{{ old('matriculation_number') }}">
                    @error('matriculation_number')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="university_name" class="form-label">University Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('university_name') is-invalid @enderror" 
                       id="university_name" name="university_name" value="{{ old('university_name') }}">
                @error('university_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="course_of_study" class="form-label">Course of Study <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('course_of_study') is-invalid @enderror" 
                           id="course_of_study" name="course_of_study" value="{{ old('course_of_study') }}">
                    @error('course_of_study')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="year_of_study" class="form-label">Year of Study</label>
                    <select class="form-select @error('year_of_study') is-invalid @enderror" 
                            id="year_of_study" name="year_of_study">
                        <option value="">Select Year</option>
                        <option value="1" {{ old('year_of_study') == '1' ? 'selected' : '' }}>1st Year</option>
                        <option value="2" {{ old('year_of_study') == '2' ? 'selected' : '' }}>2nd Year</option>
                        <option value="3" {{ old('year_of_study') == '3' ? 'selected' : '' }}>3rd Year</option>
                        <option value="4" {{ old('year_of_study') == '4' ? 'selected' : '' }}>4th Year</option>
                        <option value="5" {{ old('year_of_study') == '5' ? 'selected' : '' }}>5th Year</option>
                        <option value="6" {{ old('year_of_study') == '6' ? 'selected' : '' }}>6th Year</option>
                    </select>
                    @error('year_of_study')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="cgpa" class="form-label">CGPA (Optional)</label>
                <input type="number" step="0.01" min="0" max="5" 
                       class="form-control @error('cgpa') is-invalid @enderror" 
                       id="cgpa" name="cgpa" value="{{ old('cgpa') }}">
                @error('cgpa')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <!-- Partner Organization Fields -->
        <div id="organization-fields" class="role-specific-fields" style="display: none;">
            <h5 class="text-success mb-3"><i class="fas fa-school me-2"></i>Organization Information</h5>
            
            <div class="mb-3">
                <label for="organization_name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('organization_name') is-invalid @enderror" 
                       id="organization_name" name="organization_name" value="{{ old('organization_name') }}">
                @error('organization_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="organization_type" class="form-label">Organization Type <span class="text-danger">*</span></label>
                    <select class="form-select @error('organization_type') is-invalid @enderror" 
                            id="organization_type" name="organization_type">
                        <option value="">Select Type</option>
                        <option value="primary_school" {{ old('organization_type') == 'primary_school' ? 'selected' : '' }}>Primary School</option>
                        <option value="secondary_school" {{ old('organization_type') == 'secondary_school' ? 'selected' : '' }}>Secondary School</option>
                        <option value="college" {{ old('organization_type') == 'college' ? 'selected' : '' }}>College</option>
                        <option value="ngo" {{ old('organization_type') == 'ngo' ? 'selected' : '' }}>NGO</option>
                        <option value="other" {{ old('organization_type') == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('organization_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="organization_registration_number" class="form-label">Registration Number</label>
                    <input type="text" class="form-control @error('organization_registration_number') is-invalid @enderror" 
                           id="organization_registration_number" name="organization_registration_number" 
                           value="{{ old('organization_registration_number') }}">
                    @error('organization_registration_number')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="organization_address" class="form-label">Organization Address <span class="text-danger">*</span></label>
                <textarea class="form-control @error('organization_address') is-invalid @enderror" 
                          id="organization_address" name="organization_address" rows="3">{{ old('organization_address') }}</textarea>
                @error('organization_address')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="organization_phone" class="form-label">Organization Phone</label>
                    <input type="tel" class="form-control @error('organization_phone') is-invalid @enderror" 
                           id="organization_phone" name="organization_phone" value="{{ old('organization_phone') }}">
                    @error('organization_phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="organization_email" class="form-label">Organization Email</label>
                    <input type="email" class="form-control @error('organization_email') is-invalid @enderror" 
                           id="organization_email" name="organization_email" value="{{ old('organization_email') }}">
                    @error('organization_email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <h6 class="text-muted mb-3">Principal/Head Information</h6>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="principal_name" class="form-label">Principal/Head Name</label>
                    <input type="text" class="form-control @error('principal_name') is-invalid @enderror" 
                           id="principal_name" name="principal_name" value="{{ old('principal_name') }}">
                    @error('principal_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="principal_phone" class="form-label">Principal Phone</label>
                    <input type="tel" class="form-control @error('principal_phone') is-invalid @enderror" 
                           id="principal_phone" name="principal_phone" value="{{ old('principal_phone') }}">
                    @error('principal_phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="principal_email" class="form-label">Principal Email</label>
                <input type="email" class="form-control @error('principal_email') is-invalid @enderror" 
                       id="principal_email" name="principal_email" value="{{ old('principal_email') }}">
                @error('principal_email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <!-- Additional Common Fields -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="date_of_birth" class="form-label">Date of Birth</label>
                <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror" 
                       id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth') }}">
                @error('date_of_birth')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="gender" class="form-label">Gender</label>
                <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender">
                    <option value="">Select Gender</option>
                    <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Male</option>
                    <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Female</option>
                    <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('gender')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="mb-3">
            <label for="address" class="form-label">Address</label>
            <textarea class="form-control @error('address') is-invalid @enderror" 
                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
            @error('address')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <label for="city" class="form-label">City</label>
                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                       id="city" name="city" value="{{ old('city') }}">
                @error('city')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="state" class="form-label">State</label>
                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                       id="state" name="state" value="{{ old('state') }}">
                @error('state')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="d-grid">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>Create Account
            </button>
        </div>

        <div class="auth-links">
            <p class="text-muted mb-2">Already have an account?</p>
            <a href="{{ route('login') }}">Sign in here</a>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleCards = document.querySelectorAll('.role-card');
    const roleSpecificFields = document.querySelectorAll('.role-specific-fields');
    
    // Handle role selection
    roleCards.forEach(card => {
        card.addEventListener('click', function() {
            const roleId = this.dataset.role;
            const radioInput = this.querySelector('input[type="radio"]');
            
            // Remove active class from all cards
            roleCards.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked card
            this.classList.add('active');
            
            // Check the radio button
            radioInput.checked = true;
            
            // Hide all role-specific fields
            roleSpecificFields.forEach(field => {
                field.style.display = 'none';
            });
            
            // Show relevant fields based on role
            if (roleId === '2') {
                document.getElementById('student-fields').style.display = 'block';
            } else if (roleId === '3') {
                document.getElementById('organization-fields').style.display = 'block';
            }
        });
    });
    
    // Set initial state based on old input
    const checkedRole = document.querySelector('input[name="role_id"]:checked');
    if (checkedRole) {
        const roleCard = document.querySelector(`[data-role="${checkedRole.value}"]`);
        if (roleCard) {
            roleCard.click();
        }
    }
});
</script>
@endpush
