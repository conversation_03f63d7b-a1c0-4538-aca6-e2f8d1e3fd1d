@extends('layouts.app')

@section('title', 'Apply for Scholarship - ' . $scholarship->title)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-8 text-white">
                <h1 class="text-3xl font-bold mb-2">Scholarship Application</h1>
                <p class="text-green-100 text-lg">{{ $scholarship->title }}</p>
                <div class="mt-4 flex flex-wrap gap-4 text-sm">
                    <span class="bg-green-500 bg-opacity-30 px-3 py-1 rounded-full">
                        Category: {{ ucfirst($scholarship->category) }}
                    </span>
                    <span class="bg-green-500 bg-opacity-30 px-3 py-1 rounded-full">
                        Deadline: {{ $scholarship->application_deadline->format('M d, Y') }}
                    </span>
                    @if($scholarship->award_amount)
                    <span class="bg-green-500 bg-opacity-30 px-3 py-1 rounded-full">
                        Award: ₦{{ number_format($scholarship->award_amount) }}
                    </span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Application Form -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <form action="{{ route('scholarships.apply.store', $scholarship->id) }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-8">
                @csrf
                
                @if($errors->any())
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Primary School Application Form -->
                @if($scholarship->category === 'primary')
                    @include('scholarships.forms.primary')
                @endif

                <!-- Secondary School Application Form -->
                @if($scholarship->category === 'secondary')
                    @include('scholarships.forms.secondary')
                @endif

                <!-- University Application Form -->
                @if($scholarship->category === 'university')
                    @include('scholarships.forms.university')
                @endif

                <!-- Common Application Details -->
                <div class="border-t pt-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                        <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Application Statement
                    </h3>
                    
                    <div class="space-y-6">
                        <div>
                            <label for="reason_for_scholarship" class="block text-sm font-medium text-gray-700 mb-2">
                                Why do you need this scholarship? <span class="text-red-500">*</span>
                            </label>
                            <textarea 
                                id="reason_for_scholarship" 
                                name="reason_for_scholarship" 
                                rows="6" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reason_for_scholarship') border-red-500 @enderror"
                                placeholder="Please explain your financial situation and why you need this scholarship (minimum 50 characters)..."
                                required
                            >{{ old('reason_for_scholarship') }}</textarea>
                            @error('reason_for_scholarship')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">Minimum 50 characters, maximum 1000 characters</p>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="border-t pt-8">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Terms and Conditions</h3>
                        <div class="space-y-3 text-sm text-gray-700">
                            <p>• All information provided must be accurate and truthful</p>
                            <p>• False information may result in disqualification</p>
                            <p>• Uploaded documents must be clear and legible</p>
                            <p>• Application review may take 2-4 weeks</p>
                            <p>• Scholarship awards are subject to available funding</p>
                        </div>
                        
                        <div class="mt-6">
                            <label class="flex items-start">
                                <input 
                                    type="checkbox" 
                                    name="terms_accepted" 
                                    value="1" 
                                    class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                    required
                                >
                                <span class="ml-2 text-sm text-gray-700">
                                    I agree to the terms and conditions and confirm that all information provided is accurate <span class="text-red-500">*</span>
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-between items-center pt-8 border-t">
                    <a href="{{ route('scholarships.show', $scholarship->id) }}" 
                       class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    
                    <button 
                        type="submit" 
                        class="px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white font-semibold rounded-lg hover:from-green-700 hover:to-green-800 focus:ring-4 focus:ring-green-200 transition-all duration-200 shadow-lg"
                    >
                        Submit Application
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Section -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Need Help?</h3>
            <div class="text-sm text-blue-800 space-y-2">
                <p>• Make sure all required fields are filled out completely</p>
                <p>• Upload clear, high-quality images of your documents</p>
                <p>• Double-check your contact information for accuracy</p>
                <p>• Contact us at <a href="mailto:<EMAIL>" class="underline"><EMAIL></a> if you need assistance</p>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation and file upload preview
document.addEventListener('DOMContentLoaded', function() {
    // File upload preview
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            const preview = this.parentElement.querySelector('.file-preview');
            
            if (file && preview) {
                preview.textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                preview.classList.remove('hidden');
            }
        });
    });

    // Character count for textarea
    const textarea = document.getElementById('reason_for_scholarship');
    if (textarea) {
        const counter = document.createElement('div');
        counter.className = 'text-sm text-gray-500 mt-1';
        textarea.parentElement.appendChild(counter);
        
        function updateCounter() {
            const count = textarea.value.length;
            counter.textContent = `${count}/1000 characters`;
            
            if (count < 50) {
                counter.classList.add('text-red-500');
                counter.classList.remove('text-gray-500');
            } else {
                counter.classList.remove('text-red-500');
                counter.classList.add('text-gray-500');
            }
        }
        
        textarea.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Form submission confirmation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const termsCheckbox = document.querySelector('input[name="terms_accepted"]');
        if (!termsCheckbox.checked) {
            e.preventDefault();
            alert('Please accept the terms and conditions to continue.');
            return;
        }

        const reasonText = document.getElementById('reason_for_scholarship').value;
        if (reasonText.length < 50) {
            e.preventDefault();
            alert('Please provide at least 50 characters for your scholarship reason.');
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = 'Submitting...';
    });
});
</script>
@endsection
