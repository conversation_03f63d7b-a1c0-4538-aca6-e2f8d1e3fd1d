<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLTKKQ Foundation - Complete Developer Documentation Hub</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-green: #22c55e;
            --primary-amber: #f59e0b;
            --primary-blue: #3b82f6;
            --primary-orange: #f97316;
            --dark-bg: #1e293b;
            --light-bg: #f8fafc;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-green) 0%, #059669 50%, var(--primary-amber) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: white;
        }
        
        .doc-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        
        .doc-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
        }
        
        .doc-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }
        
        .backend-icon { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .frontend-icon { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .deployment-icon { background: linear-gradient(135deg, #f97316, #ea580c); }
        .api-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .guide-icon { background: linear-gradient(135deg, #10b981, #059669); }
        .troubleshoot-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
        
        .tech-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0.25rem;
            text-decoration: none;
            color: white;
        }
        
        .tech-laravel { background: #ff2d20; }
        .tech-nextjs { background: #000000; }
        .tech-typescript { background: #3178c6; }
        .tech-tailwind { background: #06b6d4; }
        .tech-mysql { background: #4479a1; }
        .tech-bootstrap { background: #7952b3; }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
        }
        
        .section-title {
            position: relative;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-green), var(--primary-amber));
            border-radius: 2px;
        }
        
        .quick-link {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 1rem;
            text-decoration: none;
            color: white;
            display: block;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-3px);
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #1e293b, #334155);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }
        
        .navbar-custom {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .footer-custom {
            background: var(--dark-bg);
            color: white;
            padding: 3rem 0;
        }
    </style>
</head>
<body>
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#home">
                <i class="fas fa-heart text-warning me-2"></i>
                HLTKKQ Foundation Docs
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#overview">Overview</a></li>
                    <li class="nav-item"><a class="nav-link" href="#documentation">Documentation</a></li>
                    <li class="nav-item"><a class="nav-link" href="#quick-start">Quick Start</a></li>
                    <li class="nav-item"><a class="nav-link" href="#resources">Resources</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="mb-4">
                        <h1 class="display-2 fw-bold mb-4">
                            <i class="fas fa-graduation-cap text-warning me-3"></i>
                            Complete Developer Documentation
                        </h1>
                        <p class="lead fs-4 mb-4">
                            Comprehensive documentation hub for the HLTKKQ Foundation NGO Platform
                        </p>
                        <p class="fs-5 mb-5 text-light">
                            Everything you need to understand, develop, deploy, and maintain the platform that's transforming lives through technology in Nigeria.
                        </p>
                    </div>
                    
                    <!-- Technology Stack -->
                    <div class="mb-5">
                        <h5 class="mb-3">Built With Modern Technologies</h5>
                        <div class="d-flex flex-wrap gap-2">
                            <span class="tech-badge tech-laravel">Laravel 12</span>
                            <span class="tech-badge tech-nextjs">Next.js 15</span>
                            <span class="tech-badge tech-typescript">TypeScript</span>
                            <span class="tech-badge tech-tailwind">Tailwind CSS</span>
                            <span class="tech-badge tech-mysql">MySQL 8.0+</span>
                            <span class="tech-badge tech-bootstrap">Bootstrap 5</span>
                        </div>
                    </div>
                    
                    <!-- Quick Action Buttons -->
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="#documentation" class="btn btn-warning btn-lg px-4">
                            <i class="fas fa-book me-2"></i>Browse Docs
                        </a>
                        <a href="backend-api-guide.html" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-code me-2"></i>API Reference
                        </a>
                        <a href="#quick-start" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-rocket me-2"></i>Quick Start
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stats-card text-center p-3">
                                <h3 class="mb-1">50+</h3>
                                <small>API Endpoints</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card text-center p-3">
                                <h3 class="mb-1">25+</h3>
                                <small>UI Components</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card text-center p-3">
                                <h3 class="mb-1">15+</h3>
                                <small>Database Tables</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card text-center p-3">
                                <h3 class="mb-1">4</h3>
                                <small>User Types</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Overview Section -->
    <section id="overview" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center section-title">
                <i class="fas fa-info-circle text-primary me-2"></i>
                Platform Overview
            </h2>
            
            <div class="row g-4 mb-5">
                <div class="col-lg-8 mx-auto text-center">
                    <p class="lead">
                        The HLTKKQ Foundation platform is a comprehensive NGO management system designed to support educational empowerment in Nigeria through cutting-edge technology. This documentation provides everything you need to understand, develop, and deploy the platform.
                    </p>
                </div>
            </div>

            <!-- Key Features -->
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="feature-highlight text-center h-100">
                        <i class="fas fa-graduation-cap fa-3x text-warning mb-3"></i>
                        <h5>Scholarship Management</h5>
                        <p class="small mb-0">Complete application and review system for educational funding opportunities</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-highlight text-center h-100">
                        <i class="fas fa-hands-helping fa-3x text-success mb-3"></i>
                        <h5>Volunteer Coordination</h5>
                        <p class="small mb-0">Opportunity matching and hour tracking for community volunteers</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-highlight text-center h-100">
                        <i class="fas fa-donate fa-3x text-warning mb-3"></i>
                        <h5>Donation Processing</h5>
                        <p class="small mb-0">Campaign-based fundraising with secure payment integration</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="feature-highlight text-center h-100">
                        <i class="fas fa-users-cog fa-3x text-info mb-3"></i>
                        <h5>User Management</h5>
                        <p class="small mb-0">Role-based access control with comprehensive profile management</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Documentation Section -->
    <section id="documentation" class="py-5">
        <div class="container">
            <h2 class="text-center section-title">
                <i class="fas fa-book text-success me-2"></i>
                Complete Documentation Library
            </h2>
            
            <div class="row g-4">
                <!-- Backend API Guide -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon backend-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <h5 class="card-title mb-3">Backend API Guide</h5>
                            <p class="card-text text-muted mb-4">
                                Complete Laravel 12 backend documentation with all API endpoints, authentication, database schema, and implementation details.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-danger">Laravel</span>
                                <span class="badge bg-info">API</span>
                                <span class="badge bg-warning">Auth</span>
                            </div>
                            <a href="backend-api-guide.html" class="btn btn-outline-danger">
                                <i class="fas fa-code me-2"></i>View API Docs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Frontend Development Guide -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon frontend-icon">
                                <i class="fab fa-react"></i>
                            </div>
                            <h5 class="card-title mb-3">Frontend Guide</h5>
                            <p class="card-text text-muted mb-4">
                                Next.js 15 frontend development guide covering components, state management, styling, and modern React patterns.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-dark">Next.js</span>
                                <span class="badge bg-primary">TypeScript</span>
                                <span class="badge bg-info">Tailwind</span>
                            </div>
                            <a href="frontend-guide.html" class="btn btn-outline-primary">
                                <i class="fab fa-react me-2"></i>View Frontend
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Deployment Guide -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon deployment-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h5 class="card-title mb-3">Deployment Guide</h5>
                            <p class="card-text text-muted mb-4">
                                Production deployment instructions, server configuration, SSL setup, monitoring, and security best practices.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-warning">Production</span>
                                <span class="badge bg-success">Security</span>
                                <span class="badge bg-info">Monitoring</span>
                            </div>
                            <a href="deployment-guide.html" class="btn btn-outline-warning">
                                <i class="fas fa-rocket me-2"></i>Deploy Now
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Complete Developer Guide -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon guide-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <h5 class="card-title mb-3">Complete Guide</h5>
                            <p class="card-text text-muted mb-4">
                                Master documentation covering architecture, development workflow, testing, and advanced implementation patterns.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-success">Complete</span>
                                <span class="badge bg-primary">Architecture</span>
                                <span class="badge bg-warning">Workflow</span>
                            </div>
                            <a href="complete-documentation.html" class="btn btn-outline-success">
                                <i class="fas fa-book-open me-2"></i>Full Guide
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API Reference -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon api-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <h5 class="card-title mb-3">API Reference</h5>
                            <p class="card-text text-muted mb-4">
                                Interactive API documentation with request/response examples, authentication flows, and endpoint testing tools.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-purple">REST API</span>
                                <span class="badge bg-success">Examples</span>
                                <span class="badge bg-info">Testing</span>
                            </div>
                            <a href="backend-api-guide.html#endpoints" class="btn btn-outline-purple">
                                <i class="fas fa-exchange-alt me-2"></i>API Docs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting Guide -->
                <div class="col-md-6 col-lg-4">
                    <div class="card doc-card">
                        <div class="card-body text-center p-4">
                            <div class="doc-icon troubleshoot-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <h5 class="card-title mb-3">Troubleshooting</h5>
                            <p class="card-text text-muted mb-4">
                                Common issues, solutions, debugging tips, and maintenance procedures for smooth platform operation.
                            </p>
                            <div class="mb-3">
                                <span class="badge bg-warning">Debug</span>
                                <span class="badge bg-danger">Fixes</span>
                                <span class="badge bg-info">Tips</span>
                            </div>
                            <a href="troubleshooting-guide.html" class="btn btn-outline-warning">
                                <i class="fas fa-tools me-2"></i>Get Help
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start Section -->
    <section id="quick-start" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center section-title">
                <i class="fas fa-rocket text-warning me-2"></i>
                Quick Start Guide
            </h2>
            
            <div class="row g-4">
                <div class="col-lg-8 mx-auto">
                    <div class="card border-0 shadow">
                        <div class="card-body p-5">
                            <h4 class="text-center mb-4">Get Running in 5 Minutes</h4>
                            
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-start">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">1</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-2">Clone Repository</h6>
                                            <code class="small bg-light p-2 rounded d-block">git clone [repo-url]</code>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-start">
                                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">2</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-2">Install Dependencies</h6>
                                            <code class="small bg-light p-2 rounded d-block">composer install && npm install</code>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-start">
                                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">3</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-2">Setup Database</h6>
                                            <code class="small bg-light p-2 rounded d-block">php artisan migrate:fresh --seed</code>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-start">
                                        <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">4</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-2">Start Servers</h6>
                                            <code class="small bg-light p-2 rounded d-block">php artisan serve & npm run dev</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <a href="complete-documentation.html#quick-start" class="btn btn-primary btn-lg">
                                    <i class="fas fa-play me-2"></i>Detailed Setup Guide
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources Section -->
    <section id="resources" class="py-5">
        <div class="container">
            <h2 class="text-center section-title">
                <i class="fas fa-link text-info me-2"></i>
                Additional Resources
            </h2>
            
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <a href="#" class="quick-link text-decoration-none">
                        <i class="fab fa-github fa-2x mb-2 d-block"></i>
                        <h6>GitHub Repository</h6>
                        <small>Source code and issue tracking</small>
                    </a>
                </div>
                <div class="col-md-6 col-lg-3">
                    <a href="backend-api-guide.html#testing" class="quick-link text-decoration-none">
                        <i class="fas fa-vial fa-2x mb-2 d-block"></i>
                        <h6>API Testing</h6>
                        <small>Test endpoints with examples</small>
                    </a>
                </div>
                <div class="col-md-6 col-lg-3">
                    <a href="deployment-guide.html#monitoring" class="quick-link text-decoration-none">
                        <i class="fas fa-chart-line fa-2x mb-2 d-block"></i>
                        <h6>Performance Monitoring</h6>
                        <small>Monitor your deployment</small>
                    </a>
                </div>
                <div class="col-md-6 col-lg-3">
                    <a href="troubleshooting-guide.html" class="quick-link text-decoration-none">
                        <i class="fas fa-life-ring fa-2x mb-2 d-block"></i>
                        <h6>Get Support</h6>
                        <small>Troubleshooting and help</small>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-custom">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-6">
                    <h5 class="mb-3">
                        <i class="fas fa-heart text-warning me-2"></i>
                        HLTKKQ Foundation Platform
                    </h5>
                    <p class="text-light">
                        Transforming lives through technology and education in Nigeria. This comprehensive platform supports scholarship management, volunteer coordination, donation processing, and community engagement.
                    </p>
                </div>
                <div class="col-lg-3">
                    <h6 class="mb-3">Documentation</h6>
                    <ul class="list-unstyled">
                        <li><a href="backend-api-guide.html" class="text-light text-decoration-none">Backend API</a></li>
                        <li><a href="frontend-guide.html" class="text-light text-decoration-none">Frontend Guide</a></li>
                        <li><a href="deployment-guide.html" class="text-light text-decoration-none">Deployment</a></li>
                        <li><a href="troubleshooting-guide.html" class="text-light text-decoration-none">Troubleshooting</a></li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h6 class="mb-3">Resources</h6>
                    <ul class="list-unstyled">
                        <li><a href="#quick-start" class="text-light text-decoration-none">Quick Start</a></li>
                        <li><a href="complete-documentation.html" class="text-light text-decoration-none">Complete Guide</a></li>
                        <li><a href="#" class="text-light text-decoration-none">GitHub Repository</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Support</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 text-muted">
            <div class="text-center">
                <p class="mb-0 text-muted">
                    &copy; 2024 HLTKKQ Foundation. Documentation created with ❤️ for developers.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add active class to navigation on scroll
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html> 