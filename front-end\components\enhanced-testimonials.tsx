"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Quote, Star, Play, Pause } from "lucide-react"

interface Testimonial {
  id: number
  name: string
  role: string
  location: string
  quote: string
  image: string
  rating: number
  program: string
  year: string
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>.",
    role: "Scholarship Recipient",
    location: "ABU Zaria",
    quote:
      "Thanks to Kofa Foundation, I'm now studying Medicine at ABU Zaria. My dream came true! The scholarship didn't just cover my fees, it gave me hope and showed me that someone believed in my potential.",
    image: "/placeholder.svg?height=300&width=300&text=Amina",
    rating: 5,
    program: "Tertiary Scholarship",
    year: "2023",
  },
  {
    id: 2,
    name: "Mr. <PERSON><PERSON>",
    role: "Headteacher",
    location: "GS Kofan Doka",
    quote:
      "The books donated to our school library have improved literacy rates by 40%. Our students now have access to quality educational materials that were previously unavailable. The transformation has been remarkable.",
    image: "/placeholder.svg?height=300&width=300&text=Mr.<PERSON>",
    rating: 5,
    program: "Book Donation Program",
    year: "2024",
  },
  {
    id: 3,
    name: "Fatima A.",
    role: "Primary School Student",
    location: "Kaduna State",
    quote:
      "I love my new school uniform and books. Now I can learn better and my friends think I look smart! Before, I was ashamed to go to school because my clothes were torn. Now I'm proud to be a student.",
    image: "/placeholder.svg?height=300&width=300&text=Fatima",
    rating: 5,
    program: "Back-to-School Initiative",
    year: "2024",
  },
  {
    id: 4,
    name: "Malam Ibrahim",
    role: "Community Leader",
    location: "Katsina State",
    quote:
      "The adult literacy program has empowered our community. Many parents can now help their children with homework and participate more actively in community decisions. Education truly transforms lives.",
    image: "/placeholder.svg?height=300&width=300&text=Ibrahim",
    rating: 5,
    program: "Community Literacy",
    year: "2023",
  },
  {
    id: 5,
    name: "Aisha M.",
    role: "University Graduate",
    location: "Ahmadu Bello University",
    quote:
      "From a village girl with no hope of higher education to a university graduate - Kofa Foundation made this journey possible. I'm now working as a teacher, giving back to my community.",
    image: "/placeholder.svg?height=300&width=300&text=Aisha",
    rating: 5,
    program: "Higher Education Grant",
    year: "2022",
  },
]

export function EnhancedTestimonials() {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [autoplay, setAutoplay] = useState(true)
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }))
  }

  const goToPrevious = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1))
    setTimeout(() => setIsAnimating(false), 600)
  }, [isAnimating])

  const goToNext = useCallback(() => {
    if (isAnimating) return
    setIsAnimating(true)
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1))
    setTimeout(() => setIsAnimating(false), 600)
  }, [isAnimating])

  useEffect(() => {
    if (!autoplay) return

    const interval = setInterval(goToNext, 6000)
    return () => clearInterval(interval)
  }, [autoplay, goToNext])

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 transition-colors duration-300 ${
          i < rating ? "text-amber-400 fill-current" : "text-gray-300"
        }`}
      />
    ))
  }

  const currentTestimonial = testimonials[activeIndex]

  return (
    <div className="relative max-w-6xl mx-auto">
      {/* Navigation Controls */}
      <div className="absolute top-1/2 -left-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-green-200 hover:border-green-300"
          onClick={goToPrevious}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronLeft className="h-5 w-5 text-green-600" />
          <span className="sr-only">Previous testimonial</span>
        </Button>
      </div>

      <div className="absolute top-1/2 -right-6 transform -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          className="rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-green-200 hover:border-green-300"
          onClick={goToNext}
          onMouseEnter={() => setAutoplay(false)}
          onMouseLeave={() => setAutoplay(true)}
        >
          <ChevronRight className="h-5 w-5 text-green-600" />
          <span className="sr-only">Next testimonial</span>
        </Button>
      </div>

      {/* Autoplay Control */}
      <div className="absolute top-4 right-4 z-10">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setAutoplay(!autoplay)}
          className="rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-md border-green-200 hover:border-green-300"
        >
          {autoplay ? <Pause className="h-4 w-4 text-green-600" /> : <Play className="h-4 w-4 text-green-600" />}
          <span className="sr-only">{autoplay ? "Pause" : "Play"} autoplay</span>
        </Button>
      </div>

      {/* Main Testimonial Card */}
      <Card className="overflow-hidden shadow-2xl border-green-200 dark:border-green-800 rounded-3xl bg-gradient-to-br from-white to-green-50 dark:from-gray-900 dark:to-green-950">
        <CardContent className="p-0">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-0">
            {/* Image Section */}
            <div className="md:col-span-2 relative">
              <div className="relative aspect-square md:aspect-auto md:h-full overflow-hidden">
                <div
                  className={`absolute inset-0 bg-gradient-to-br from-green-200 to-green-300 dark:from-green-800 dark:to-green-900 animate-pulse ${
                    imagesLoaded[currentTestimonial.id] ? "opacity-0" : "opacity-100"
                  }`}
                />
                <Image
                  src={currentTestimonial.image || "/placeholder.svg"}
                  alt={currentTestimonial.name}
                  fill
                  sizes="(max-width: 768px) 100vw, 40vw"
                  className={`object-cover transition-all duration-700 ${
                    imagesLoaded[currentTestimonial.id] ? "opacity-100 scale-100" : "opacity-0 scale-105"
                  }`}
                  onLoad={() => handleImageLoad(currentTestimonial.id)}
                />

                {/* Overlay with gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

                {/* Person Info Overlay */}
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <div className="space-y-2">
                    <div className="inline-block bg-green-600/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium">
                      {currentTestimonial.program}
                    </div>
                    <div className="font-bold text-xl">{currentTestimonial.name}</div>
                    <div className="text-sm opacity-90">{currentTestimonial.role}</div>
                    <div className="text-xs opacity-80 flex items-center gap-1">
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      {currentTestimonial.location}
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      {currentTestimonial.year}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="md:col-span-3 flex flex-col justify-center p-8 md:p-12">
              <div className="space-y-6">
                {/* Quote Icon */}
                <Quote className="h-12 w-12 text-green-600/30" />

                {/* Testimonial Text */}
                <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 italic leading-relaxed font-medium">
                  "{currentTestimonial.quote}"
                </blockquote>

                {/* Rating */}
                <div className="flex items-center gap-2">
                  {renderStars(currentTestimonial.rating)}
                  <span className="text-sm text-gray-500 ml-2">{currentTestimonial.rating}/5 stars</span>
                </div>

                {/* Attribution */}
                <div className="pt-4 border-t border-green-200 dark:border-green-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-green-800 dark:text-green-200">{currentTestimonial.name}</div>
                      <div className="text-sm text-green-600 dark:text-green-400">
                        {currentTestimonial.role} • {currentTestimonial.location}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-700 dark:text-green-300">
                        {currentTestimonial.program}
                      </div>
                      <div className="text-xs text-gray-500">Class of {currentTestimonial.year}</div>
                    </div>
                  </div>
                </div>

                {/* Navigation Dots */}
                <div className="flex items-center gap-2 pt-4">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        index === activeIndex
                          ? "w-8 bg-green-600"
                          : "w-2 bg-green-200 dark:bg-green-800 hover:bg-green-300 dark:hover:bg-green-700"
                      }`}
                      onClick={() => {
                        setActiveIndex(index)
                        setAutoplay(false)
                        setTimeout(() => setAutoplay(true), 3000)
                      }}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Testimonial Counter */}
      <div className="text-center mt-6">
        <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900 px-4 py-2 rounded-full">
          <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-green-800 dark:text-green-200">
            {activeIndex + 1} of {testimonials.length} stories
          </span>
        </div>
      </div>
    </div>
  )
}
