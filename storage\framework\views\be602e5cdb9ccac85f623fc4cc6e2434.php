

<?php $__env->startSection('title', 'Volunteers Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Volunteers Management</h1>
        <div>
            <a href="<?php echo e(route('admin.volunteers.applications')); ?>" class="btn btn-warning me-2">
                <i class="fas fa-clock me-2"></i>Pending Applications
            </a>
            <a href="<?php echo e(route('admin.volunteers.opportunities')); ?>" class="btn btn-info">
                <i class="fas fa-plus me-2"></i>Volunteer Opportunities
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Volunteers</div>
                            <div class="h4"><?php echo e($volunteers->total()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-hands-helping fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Volunteers</div>
                            <div class="h4"><?php echo e($volunteers->where('application_status', 'approved')->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Pending Applications</div>
                            <div class="h4"><?php echo e($volunteers->where('application_status', 'pending')->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Hours</div>
                            <div class="h4"><?php echo e(number_format($volunteers->sum('hours_logged'))); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-stopwatch fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Volunteers Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Volunteers</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search volunteers..." id="searchVolunteers">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterSkills">
                        <option value="">All Skills</option>
                        <option value="teaching">Teaching</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="construction">Construction</option>
                        <option value="technology">Technology</option>
                        <option value="administration">Administration</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Volunteer</th>
                            <th>Skills & Interests</th>
                            <th>Status</th>
                            <th>Hours Logged</th>
                            <th>Availability</th>
                            <th>Applied Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $volunteers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $volunteer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($volunteer->user && $volunteer->user->profile_picture): ?>
                                        <img src="<?php echo e($volunteer->user->profile_picture); ?>" alt="<?php echo e($volunteer->user->first_name); ?>" class="rounded-circle me-3" width="50" height="50">
                                    <?php else: ?>
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            <?php echo e($volunteer->user ? strtoupper(substr($volunteer->user->first_name, 0, 1)) : 'V'); ?>

                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-bold">
                                            <?php echo e($volunteer->user ? $volunteer->user->first_name . ' ' . $volunteer->user->last_name : 'Unknown User'); ?>

                                        </div>
                                        <?php if($volunteer->user): ?>
                                            <small class="text-muted"><?php echo e($volunteer->user->email); ?></small><br>
                                            <?php if($volunteer->user->phone_number): ?>
                                                <small class="text-muted"><?php echo e($volunteer->user->phone_number); ?></small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <?php if($volunteer->skills && is_array($volunteer->skills)): ?>
                                        <div class="mb-1">
                                            <strong>Skills:</strong>
                                            <?php $__currentLoopData = array_slice($volunteer->skills, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge bg-primary me-1"><?php echo e($skill); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(count($volunteer->skills) > 3): ?>
                                                <span class="text-muted">+<?php echo e(count($volunteer->skills) - 3); ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($volunteer->interests && is_array($volunteer->interests)): ?>
                                        <div>
                                            <strong>Interests:</strong>
                                            <?php $__currentLoopData = array_slice($volunteer->interests, 0, 2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $interest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge bg-info me-1"><?php echo e($interest); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(count($volunteer->interests) > 2): ?>
                                                <span class="text-muted">+<?php echo e(count($volunteer->interests) - 2); ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($volunteer->application_status === 'approved' ? 'success' : 
                                    ($volunteer->application_status === 'rejected' ? 'danger' : 'warning')); ?>">
                                    <?php echo e(ucfirst($volunteer->application_status)); ?>

                                </span>
                                <?php if($volunteer->approved_at): ?>
                                    <br><small class="text-muted"><?php echo e($volunteer->approved_at->format('M d, Y')); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="text-<?php echo e($volunteer->hours_logged > 100 ? 'success' : ($volunteer->hours_logged > 50 ? 'info' : 'muted')); ?>">
                                        <?php echo e(number_format($volunteer->hours_logged, 1)); ?>h
                                    </strong>
                                    <?php if($volunteer->hours_logged > 0): ?>
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-success" style="width: <?php echo e(min(100, ($volunteer->hours_logged / 200) * 100)); ?>%"></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if($volunteer->availability && is_array($volunteer->availability)): ?>
                                    <?php if(isset($volunteer->availability['weekdays']) && is_array($volunteer->availability['weekdays'])): ?>
                                        <div class="small">
                                            <strong>Weekdays:</strong> <?php echo e(implode(', ', array_slice($volunteer->availability['weekdays'], 0, 3))); ?>

                                            <?php if(count($volunteer->availability['weekdays']) > 3): ?>
                                                <span class="text-muted">+<?php echo e(count($volunteer->availability['weekdays']) - 3); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(isset($volunteer->availability['weekends'])): ?>
                                        <div class="small">
                                            <strong>Weekends:</strong> 
                                            <span class="badge bg-<?php echo e($volunteer->availability['weekends'] ? 'success' : 'secondary'); ?>">
                                                <?php echo e($volunteer->availability['weekends'] ? 'Available' : 'Not Available'); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo e($volunteer->applied_at ? $volunteer->applied_at->format('M d, Y') : 'Unknown'); ?>

                                <?php if($volunteer->applied_at): ?>
                                    <br><small class="text-muted"><?php echo e($volunteer->applied_at->diffForHumans()); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.volunteers.show', $volunteer->id)); ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if($volunteer->application_status === 'pending'): ?>
                                        <button class="btn btn-sm btn-outline-success" onclick="approveVolunteer(<?php echo e($volunteer->id); ?>)" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="rejectVolunteer(<?php echo e($volunteer->id); ?>)" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-sm btn-outline-info" onclick="sendMessage(<?php echo e($volunteer->user ? $volunteer->user->id : 0); ?>)" title="Send Message">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-hands-helping fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No volunteers found</p>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing <?php echo e($volunteers->firstItem()); ?> to <?php echo e($volunteers->lastItem()); ?> of <?php echo e($volunteers->total()); ?> results
                </div>
                <?php echo e($volunteers->links()); ?>

            </div>
        </div>
    </div>
</div>

<!-- Approve Volunteer Modal -->
<div class="modal fade" id="approveVolunteerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approve Volunteer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to approve this volunteer application?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmApproveVolunteer">Approve</button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Volunteer Modal -->
<div class="modal fade" id="rejectVolunteerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Volunteer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject this volunteer application?</p>
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label">Reason for rejection (optional):</label>
                    <textarea class="form-control" id="rejectionReason" rows="3" placeholder="Provide a reason for rejection..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRejectVolunteer">Reject</button>
            </div>
        </div>
    </div>
</div>

<script>
function approveVolunteer(volunteerId) {
    const modal = new bootstrap.Modal(document.getElementById('approveVolunteerModal'));
    modal.show();
    
    document.getElementById('confirmApproveVolunteer').onclick = function() {
        console.log('Approve volunteer:', volunteerId);
        modal.hide();
        // Add AJAX call to approve volunteer
        // After success, refresh the page or update the row
    };
}

function rejectVolunteer(volunteerId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectVolunteerModal'));
    modal.show();
    
    document.getElementById('confirmRejectVolunteer').onclick = function() {
        const reason = document.getElementById('rejectionReason').value;
        console.log('Reject volunteer:', volunteerId, 'Reason:', reason);
        modal.hide();
        // Add AJAX call to reject volunteer
    };
}

function sendMessage(userId) {
    console.log('Send message to user:', userId);
    // Implement message sending functionality
}

// Search functionality
document.getElementById('searchVolunteers').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterSkills').addEventListener('change', function() {
    console.log('Filter skills:', this.value);
});
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/volunteers/index.blade.php ENDPATH**/ ?>