<?php $__env->startSection('title', 'Partner Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:p-8 p-4 pt-20 lg:pt-8">
            <!-- Welcome Header -->
            <div class="relative bg-gradient-to-br from-green-600 to-green-800 text-white rounded-2xl shadow-xl p-8 mb-8 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
                    <div class="absolute top-1/2 left-1/2 w-40 h-40 bg-white/20 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
                </div>

                <div class="relative z-10 flex flex-col lg:flex-row items-start lg:items-center justify-between">
                    <div class="flex-1 mb-6 lg:mb-0">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mr-4 backdrop-blur-sm">
                                <i class="fas fa-handshake text-white text-2xl"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl lg:text-4xl font-bold text-white mb-1">
                                    Welcome, <?php echo e(Auth::user()->organization_name ?? Auth::user()->first_name); ?>!
                                </h1>
                                <p class="text-green-100 text-lg">Empowering students through partnership</p>
                            </div>
                        </div>
                        <p class="text-green-50 max-w-2xl leading-relaxed">
                            Manage your students, track scholarship applications, and expand educational opportunities in your community.
                        </p>
                        <?php if(Auth::user()->organization_type): ?>
                            <div class="mt-4 inline-flex items-center bg-white/15 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                                <i class="fas fa-building text-green-200 mr-2"></i>
                                <span class="text-white font-medium"><?php echo e(ucfirst(str_replace('_', ' ', Auth::user()->organization_type))); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="lg:ml-8">
                        <div class="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-xl min-w-[200px]">
                            <div class="text-center">
                                <div class="text-green-100 text-sm mb-2">Total Students</div>
                                <div class="text-4xl font-bold text-white mb-2"><?php echo e(Auth::user()->partnerStudents()->count()); ?></div>
                                <div class="text-green-200 text-xs">
                                    <i class="fas fa-users mr-1"></i>Active Partnerships
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Students Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-green-600 mb-2"><?php echo e(Auth::user()->partnerStudents()->count()); ?></div>
                    <div class="text-sm text-gray-600">Total Students</div>
                </div>

                <!-- Active Applications Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-file-alt text-amber-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-amber-600 mb-2"><?php echo e(Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) { $q->whereIn('status', ['pending', 'under_review']); })->count()); ?></div>
                    <div class="text-sm text-gray-600">Active Applications</div>
                </div>

                <!-- Approved Applications Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-check-circle text-blue-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?php echo e(Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) { $q->where('status', 'approved'); })->count()); ?></div>
                    <div class="text-sm text-gray-600">Approved Applications</div>
                </div>

                <!-- Available Scholarships Card -->
                <div class="text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-graduation-cap text-purple-600"></i>
                    </div>
                    <div class="text-3xl font-bold text-purple-600 mb-2"><?php echo e(\App\Models\Scholarship::where('status', 'active')->whereIn('category', ['primary', 'secondary'])->count()); ?></div>
                    <div class="text-sm text-gray-600">Available Scholarships</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Manage Students Action -->
                <a href="/partner/students" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-users h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">Manage Students</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Add and manage your students for scholarship applications.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            Manage Students
                        </div>
                    </div>
                </a>

                <!-- Apply for Scholarships Action -->
                <a href="<?php echo e(route('partner.scholarships.index')); ?>" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-graduation-cap h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">Apply for Scholarships</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Browse and apply for scholarships on behalf of your students.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            Browse Scholarships
                        </div>
                    </div>
                <!-- Track Applications Action -->
                <a href="/partner/applications" class="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 rounded-2xl overflow-hidden bg-white block">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <i class="fas fa-chart-bar h-5 w-5 text-green-600"></i>
                            <h3 class="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">Track Applications</h3>
                        </div>
                        <p class="text-gray-600 mb-4 group-hover:text-gray-900 transition-colors duration-300">
                            Monitor application status and download certificates.
                        </p>
                        <div class="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-2 px-4 text-center transition-colors">
                            View Applications
                        </div>
                    </div>
                </a>
            </div>

        <!-- Recent Students and Applications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
            <!-- Recent Students -->
            <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Recent Students</h2>
                    </div>
                    <a href="/partner/students" class="text-green-600 hover:text-green-700 font-medium text-sm transition-colors duration-200 flex items-center">
                        View All <i class="fas fa-arrow-right ml-1 text-xs"></i>
                    </a>
                </div>
                <?php
                    $recentStudents = Auth::user()->partnerStudents()->latest()->take(5)->get();
                ?>
                <?php if($recentStudents->count() > 0): ?>
                    <div class="space-y-3">
                        <?php $__currentLoopData = $recentStudents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-green-50 rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-800"><?php echo e($student->name); ?></h4>
                                        <p class="text-sm text-gray-600">Class <?php echo e($student->class); ?> • <?php echo e($student->gender); ?></p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="px-3 py-1 text-xs font-medium rounded-full
                                        <?php if($student->status === 'active'): ?> bg-green-100 text-green-700 border border-green-200
                                        <?php else: ?> bg-gray-100 text-gray-700 border border-gray-200
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($student->status)); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user-plus text-green-500 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">No students yet</h3>
                        <p class="text-gray-600 mb-4">Start building your student network</p>
                        <a href="/partner/students" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            <i class="fas fa-plus mr-2"></i>Add Your First Student
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Organization Information -->
            <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                        <i class="fas fa-building text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800">Organization Profile</h2>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl">
                        <div class="flex items-center">
                            <i class="fas fa-building text-blue-500 mr-3"></i>
                            <span class="text-gray-700 font-medium">Organization Name</span>
                        </div>
                        <span class="font-semibold text-gray-800"><?php echo e(Auth::user()->organization_name ?? 'Not specified'); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-green-50 rounded-xl">
                        <div class="flex items-center">
                            <i class="fas fa-tag text-green-500 mr-3"></i>
                            <span class="text-gray-700 font-medium">Type</span>
                        </div>
                        <span class="font-semibold text-gray-800"><?php echo e(Auth::user()->organization_type ? ucfirst(str_replace('_', ' ', Auth::user()->organization_type)) : 'Not specified'); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-purple-50 rounded-xl">
                        <div class="flex items-center">
                            <i class="fas fa-user-tie text-purple-500 mr-3"></i>
                            <span class="text-gray-700 font-medium">Principal</span>
                        </div>
                        <span class="font-semibold text-gray-800"><?php echo e(Auth::user()->principal_name ?? 'Not specified'); ?></span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-orange-50 rounded-xl">
                        <div class="flex items-center">
                            <i class="fas fa-phone text-orange-500 mr-3"></i>
                            <span class="text-gray-700 font-medium">Contact</span>
                        </div>
                        <span class="font-semibold text-gray-800"><?php echo e(Auth::user()->phone_number ?? 'Not specified'); ?></span>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t border-gray-100">
                    <a href="/partner/profile" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-edit mr-2"></i>Update Information
                    </a>
                </div>
            </div>
        </div>

            <!-- Student Management Tools -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                        <i class="fas fa-tools text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800">Student Management Tools</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
                    <a href="/partner/students/create" class="group p-6 border-2 border-gray-200 rounded-2xl hover:border-green-300 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 block">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-user-plus text-white text-lg"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2 group-hover:text-green-600 transition-colors duration-200">Add New Student</h3>
                        <p class="text-sm text-gray-600 leading-relaxed">Register new students to your organization and start their scholarship journey</p>
                    </a>

                    <a href="/partner/students/import" class="group p-6 border-2 border-gray-200 rounded-2xl hover:border-blue-300 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 block">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-file-import text-white text-lg"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-200">Bulk Import</h3>
                        <p class="text-sm text-gray-600 leading-relaxed">Import multiple students from CSV file for efficient management</p>
                    </a>

                    <a href="/partner/reports" class="group p-6 border-2 border-gray-200 rounded-2xl hover:border-purple-300 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 block">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-file-export text-white text-lg"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2 group-hover:text-purple-600 transition-colors duration-200">Export Reports</h3>
                        <p class="text-sm text-gray-600 leading-relaxed">Generate comprehensive student and application reports</p>
                    </a>
                </div>
            </div>

            <!-- Guidelines -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-2xl p-6 shadow-lg">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                            <i class="fas fa-info-circle text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h3 class="text-xl font-bold text-green-800 mb-3">Partner Organization Guidelines</h3>
                        <div class="text-green-700 space-y-2">
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-600 mr-3 mt-1 flex-shrink-0"></i>
                                <p>Add students to your organization to manage their scholarship applications</p>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-600 mr-3 mt-1 flex-shrink-0"></i>
                                <p>You can apply for Primary and Secondary scholarships on behalf of your students</p>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-600 mr-3 mt-1 flex-shrink-0"></i>
                                <p>Track application progress and download approval letters for successful applications</p>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-600 mr-3 mt-1 flex-shrink-0"></i>
                                <p>Keep student information updated for better application processing</p>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-600 mr-3 mt-1 flex-shrink-0"></i>
                                <p>Use bulk import feature to add multiple students efficiently</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', openMobileMenu);
    }

    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', closeMobileMenu);
    }

    // Add loading states to action buttons
    const actionButtons = document.querySelectorAll('a[href*="/partner/"]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon && !icon.classList.contains('fa-spinner')) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';
                setTimeout(() => {
                    icon.className = originalClass;
                }, 2000);
            }
        });
    });

    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.group');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/dashboards/partner/index.blade.php ENDPATH**/ ?>