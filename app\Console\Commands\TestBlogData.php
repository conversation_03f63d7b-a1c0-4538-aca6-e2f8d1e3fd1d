<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\User;

class TestBlogData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:blog-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check blog data counts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Blog Data Counts:');
        $this->info('Blog Posts: ' . BlogPost::count());
        $this->info('Blog Categories: ' . BlogCategory::count());
        $this->info('Blog Comments: ' . BlogComment::count());
        $this->info('Users: ' . User::count());

        // Let's also create some test data if none exists
        if (BlogCategory::count() === 0) {
            $this->info('Creating test blog category...');
            BlogCategory::create([
                'name' => 'General',
                'slug' => 'general',
                'description' => 'General blog posts',
                'color' => '#563d7c',
                'is_active' => true,
                'sort_order' => 0
            ]);
            $this->info('Test category created!');
        }

        if (BlogPost::count() === 0 && User::count() > 0) {
            $this->info('Creating test blog post...');
            $firstUser = User::first();
            $category = BlogCategory::first();
            
            BlogPost::create([
                'title' => 'Welcome to Our Blog',
                'slug' => 'welcome-to-our-blog',
                'content' => 'This is a test blog post to demonstrate the admin panel functionality.',
                'excerpt' => 'This is a test blog post...',
                'status' => 'published',
                'author_id' => $firstUser->id,
                'category_id' => $category ? $category->id : null,
                'views_count' => 25,
                'published_at' => now()
            ]);
            $this->info('Test blog post created!');
        }

        return 0;
    }
}
