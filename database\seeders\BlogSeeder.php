<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogCategory;
use App\Models\BlogPost;
use App\Models\BlogComment;
use App\Models\User;
use Illuminate\Support\Str;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create blog categories
        $categories = [
            [
                'name' => 'Impact Stories',
                'description' => 'Stories showcasing the impact of our work in communities',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Volunteer Spotlights',
                'description' => 'Highlighting the amazing work of our volunteers',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'News & Updates',
                'description' => 'Latest news and updates from HLTKKQ Foundation',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Educational Resources',
                'description' => 'Resources and guides for education and learning',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Community Events',
                'description' => 'Information about upcoming and past community events',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Scholarship Updates',
                'description' => 'Updates about our scholarship programs',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $categoryData) {
            BlogCategory::create($categoryData);
        }

        // Get admin users for authorship
        $adminUsers = User::where('role', 'admin')->orWhere('email', 'like', '%admin%')->get();
        if ($adminUsers->isEmpty()) {
            // Create a default admin user if none exists
            $adminUser = User::create([
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
                'status' => 'active',
            ]);
            $adminUsers = collect([$adminUser]);
        }

        // Create blog posts
        $posts = [
            [
                'title' => 'Empowering Communities Through Education: Our 2024 Impact Report',
                'excerpt' => 'Discover how HLTKKQ Foundation has transformed lives through education initiatives across Nigeria in 2024.',
                'content' => $this->generateLongContent('education', 'Our education programs have reached over 10,000 students across Nigeria this year...'),
                'category_id' => 1, // Impact Stories
                'status' => 'published',
                'views_count' => 2845,
                'likes_count' => 156,
                'published_at' => now()->subDays(3),
                'seo_title' => 'HLTKKQ Foundation 2024 Impact Report - Education Programs',
                'seo_description' => 'Read about our educational impact reaching 10,000+ students across Nigeria.',
                'tags' => ['education', 'impact', '2024', 'nigeria', 'students'],
            ],
            [
                'title' => 'Meet Sarah: From Scholarship Recipient to Community Leader',
                'excerpt' => 'Sarah\'s journey from receiving our scholarship to becoming a teacher and community advocate.',
                'content' => $this->generateLongContent('volunteer', 'Sarah received our scholarship in 2020 when she was struggling to continue her education...'),
                'category_id' => 2, // Volunteer Spotlights
                'status' => 'published',
                'views_count' => 1924,
                'likes_count' => 89,
                'published_at' => now()->subDays(7),
                'seo_title' => 'Sarah\'s Success Story - HLTKKQ Foundation Scholarship Impact',
                'seo_description' => 'How our scholarship program transformed Sarah from student to community leader.',
                'tags' => ['scholarship', 'success-story', 'education', 'community'],
            ],
            [
                'title' => 'New Partnership with Local Schools Announced',
                'excerpt' => 'We\'re excited to announce partnerships with 15 new schools across Lagos State.',
                'content' => $this->generateLongContent('partnership', 'HLTKKQ Foundation is proud to announce new partnerships with 15 schools...'),
                'category_id' => 3, // News & Updates
                'status' => 'published',
                'views_count' => 1456,
                'likes_count' => 67,
                'published_at' => now()->subDays(10),
                'seo_title' => 'HLTKKQ Foundation Partners with 15 New Schools in Lagos',
                'seo_description' => 'Expanding our reach through new school partnerships in Lagos State.',
                'tags' => ['partnership', 'schools', 'lagos', 'expansion'],
            ],
            [
                'title' => 'Volunteer Training Workshop: Building Capacity for Impact',
                'excerpt' => 'Last weekend\'s volunteer training workshop equipped 50 volunteers with new skills.',
                'content' => $this->generateLongContent('training', 'Our quarterly volunteer training workshop was a huge success...'),
                'category_id' => 2, // Volunteer Spotlights
                'status' => 'published',
                'views_count' => 892,
                'likes_count' => 45,
                'published_at' => now()->subDays(14),
                'seo_title' => 'Volunteer Training Workshop - HLTKKQ Foundation',
                'seo_description' => '50 volunteers gained new skills at our capacity building workshop.',
                'tags' => ['volunteer', 'training', 'capacity-building', 'workshop'],
            ],
            [
                'title' => 'Digital Learning Resources Now Available',
                'excerpt' => 'Access our new collection of digital learning materials designed for Nigerian students.',
                'content' => $this->generateLongContent('digital', 'In response to the growing need for accessible education...'),
                'category_id' => 4, // Educational Resources
                'status' => 'published',
                'views_count' => 2156,
                'likes_count' => 134,
                'published_at' => now()->subDays(21),
                'seo_title' => 'Free Digital Learning Resources - HLTKKQ Foundation',
                'seo_description' => 'Access free digital learning materials for Nigerian students.',
                'tags' => ['digital-learning', 'resources', 'education', 'technology'],
            ],
            [
                'title' => 'Upcoming Community Fair: Education and Opportunities',
                'excerpt' => 'Join us for our annual community fair featuring educational exhibitions and scholarship information.',
                'content' => $this->generateLongContent('event', 'Mark your calendars for our annual community fair...'),
                'category_id' => 5, // Community Events
                'status' => 'published',
                'views_count' => 743,
                'likes_count' => 32,
                'published_at' => now()->subDays(2),
                'seo_title' => 'HLTKKQ Community Fair 2024 - Education & Opportunities',
                'seo_description' => 'Join our annual community fair with educational exhibitions and scholarship info.',
                'tags' => ['community-fair', 'event', 'scholarships', 'education'],
            ],
            [
                'title' => '2024 Scholarship Application Now Open',
                'excerpt' => 'Applications for our 2024 scholarship program are now open. Don\'t miss this opportunity!',
                'content' => $this->generateLongContent('scholarship', 'We are thrilled to announce that applications for our 2024 scholarship program...'),
                'category_id' => 6, // Scholarship Updates
                'status' => 'published',
                'views_count' => 3247,
                'likes_count' => 287,
                'published_at' => now()->subDays(5),
                'seo_title' => '2024 Scholarship Applications Open - HLTKKQ Foundation',
                'seo_description' => 'Apply now for HLTKKQ Foundation scholarships. Applications closing soon.',
                'tags' => ['scholarship', '2024', 'application', 'education', 'opportunity'],
            ],
            [
                'title' => 'How Technology is Transforming Rural Education',
                'excerpt' => 'Exploring the role of technology in bringing quality education to rural communities.',
                'content' => $this->generateLongContent('technology', 'Technology has the power to bridge educational gaps in rural areas...'),
                'category_id' => 4, // Educational Resources
                'status' => 'draft',
                'views_count' => 0,
                'likes_count' => 0,
                'published_at' => null,
                'seo_title' => 'Technology in Rural Education - HLTKKQ Foundation',
                'seo_description' => 'How technology is transforming education in rural Nigerian communities.',
                'tags' => ['technology', 'rural-education', 'innovation', 'digital-divide'],
            ],
        ];

        $createdPosts = [];
        foreach ($posts as $index => $postData) {
            $postData['author_id'] = $adminUsers->random()->id;
            $createdPosts[] = BlogPost::create($postData);
        }

        // Create regular users for comments
        $users = User::where('role', 'user')->limit(10)->get();
        if ($users->count() < 5) {
            for ($i = 0; $i < 5; $i++) {
                $users[] = User::create([
                    'first_name' => 'User',
                    'last_name' => 'Test' . ($i + 1),
                    'email' => 'user' . ($i + 1) . '@example.com',
                    'password' => bcrypt('password'),
                    'role' => 'user',
                    'email_verified_at' => now(),
                    'status' => 'active',
                ]);
            }
        }

        // Create blog comments
        $comments = [
            'This is such an inspiring story! Thank you for sharing.',
            'Great work! I\'m so proud to be part of this community.',
            'How can I get involved as a volunteer?',
            'This scholarship program changed my life. Thank you HLTKKQ!',
            'Excellent initiative. We need more organizations like this.',
            'I attended the workshop and it was fantastic!',
            'When will the next training session be held?',
            'The digital resources are very helpful for my studies.',
            'Thank you for making education accessible to everyone.',
            'I\'m interested in applying for the scholarship.',
            'This partnership will benefit so many students.',
            'Amazing impact! Keep up the great work.',
            'I love reading these success stories.',
            'How can our school partner with HLTKKQ?',
            'The community fair sounds exciting!',
        ];

        foreach ($createdPosts as $post) {
            if ($post->status === 'published') {
                // Create 2-5 comments per published post
                $commentCount = rand(2, 5);
                for ($i = 0; $i < $commentCount; $i++) {
                    BlogComment::create([
                        'post_id' => $post->id,
                        'user_id' => $users->random()->id,
                        'content' => $comments[array_rand($comments)],
                        'status' => ['approved', 'approved', 'approved', 'pending', 'rejected'][rand(0, 4)], // More approved than others
                        'created_at' => now()->subDays(rand(1, 30)),
                    ]);
                }
            }
        }

        $this->command->info('Blog seeder completed successfully!');
        $this->command->info('Created: ' . count($categories) . ' categories, ' . count($posts) . ' posts, and multiple comments');
    }

    private function generateLongContent($type, $intro)
    {
        $paragraphs = [
            'education' => [
                $intro,
                'Through our comprehensive education programs, we have established learning centers in 25 communities across Nigeria. These centers provide after-school tutoring, computer literacy classes, and mentorship programs that help students excel in their studies.',
                'Our partnership with local teachers has been instrumental in delivering quality education. We provide training, resources, and ongoing support to ensure that every child receives the attention they deserve. The impact has been remarkable - test scores have improved by 40% in participating schools.',
                'One of our most successful initiatives has been the mobile library program. Our specially equipped vehicles visit remote areas, bringing books, educational materials, and interactive learning experiences directly to children who might otherwise have limited access to quality educational resources.',
                'Looking ahead, we are expanding our digital learning platform to reach even more students. With features like video lessons, interactive quizzes, and virtual mentoring, we are making quality education accessible regardless of geographical barriers.',
            ],
            'volunteer' => [
                $intro,
                'What started as a personal struggle became a mission to help others. After completing her degree with our scholarship support, Sarah returned to her community with a vision to make education accessible to every child.',
                'She established a learning center in her village, where she teaches primary school children and provides computer literacy training. Her center has become a beacon of hope for families who had given up on their children\'s educational future.',
                'Sarah\'s impact extends beyond teaching. She has organized community meetings to discuss the importance of education, especially for girl children. Her advocacy has resulted in a 60% increase in school enrollment in her area.',
                'Today, Sarah mentors other scholarship recipients and serves on our youth advisory board. Her story exemplifies how investment in one person\'s education can transform an entire community.',
            ],
            'partnership' => [
                $intro,
                'These partnerships represent a significant milestone in our mission to make quality education accessible to all Nigerian children. Each school will receive customized support based on their specific needs and challenges.',
                'Our partnership package includes teacher training programs, learning materials, technology infrastructure, and ongoing mentorship support. We believe in working closely with school administrators to ensure sustainable impact.',
                'The selection process involved comprehensive assessments of each school\'s needs, community engagement levels, and commitment to educational excellence. Priority was given to schools serving underserved communities.',
                'We expect these partnerships to directly benefit over 5,000 students in the first year alone. Regular monitoring and evaluation will ensure that our interventions are making the intended impact.',
            ],
            'training' => [
                $intro,
                'The workshop covered essential topics including child psychology, modern teaching methodologies, community engagement strategies, and effective communication techniques. Participants left with practical tools they can immediately apply in their volunteer work.',
                'Interactive sessions allowed volunteers to share experiences and learn from each other. Many highlighted this peer-to-peer learning as one of the most valuable aspects of the training.',
                'Special focus was given to understanding the challenges faced by children from disadvantaged backgrounds. Volunteers learned how to provide emotional support alongside academic assistance.',
                'The feedback has been overwhelmingly positive, with 95% of participants rating the workshop as excellent. Plans are already underway for the next training session in three months.',
            ],
            'digital' => [
                $intro,
                'Our digital library includes video lessons, interactive exercises, downloadable worksheets, and assessment tools covering subjects from primary to secondary school levels. All content is aligned with the Nigerian educational curriculum.',
                'The platform is designed to work on various devices, including smartphones, tablets, and computers. This ensures that students can access learning materials regardless of the technology available to them.',
                'Teachers can also benefit from our resources section, which includes lesson plans, teaching guides, and professional development materials. We believe that supporting teachers is crucial to improving educational outcomes.',
                'Since the launch, we have recorded over 50,000 downloads and positive feedback from students, teachers, and parents across the country. The platform continues to grow with new content added monthly.',
            ],
            'event' => [
                $intro,
                'The fair will feature educational exhibitions, scholarship information sessions, career guidance workshops, and cultural performances. Local schools, universities, and training institutions will showcase their programs.',
                'Special attention will be given to highlighting opportunities for young people, including vocational training, entrepreneurship support, and scholarship programs. Our goal is to ensure every attendee leaves with actionable information about their future.',
                'Community leaders, successful alumni, and industry professionals will be available to provide mentorship and guidance. This networking opportunity often proves as valuable as the formal presentations.',
                'Entry is completely free, and refreshments will be provided throughout the day. We encourage families to attend together, as education is a community responsibility that requires everyone\'s participation.',
            ],
            'scholarship' => [
                $intro,
                'This year, we are offering 100 full scholarships covering tuition, books, accommodation, and living allowances. Additional partial scholarships will be available for students who demonstrate exceptional potential but may not qualify for full support.',
                'The application process has been streamlined to ensure accessibility. Students can apply online or visit any of our community centers for assistance with their applications. We are committed to ensuring that lack of technology does not become a barrier.',
                'Selection criteria include academic performance, financial need, community involvement, and demonstrated leadership potential. We are particularly committed to supporting female students and those from rural communities.',
                'Application deadline is December 31st, 2024. Selected candidates will be notified by February 2025, with scholarships commencing in the following academic year. Don\'t miss this life-changing opportunity!',
            ],
            'technology' => [
                $intro,
                'Digital learning platforms, solar-powered computer labs, and satellite internet connections are making it possible for students in remote areas to access the same quality education as their urban counterparts.',
                'Our pilot program in five rural communities has shown remarkable results. Students who previously had no access to computers are now creating presentations, conducting online research, and collaborating with peers from other schools.',
                'Teacher training is a crucial component of our technology integration strategy. We provide comprehensive training on digital tools, online safety, and blended learning methodologies to ensure effective implementation.',
                'The challenges are real - from unreliable power supply to limited internet connectivity. However, innovative solutions like offline learning systems and renewable energy are helping us overcome these obstacles.',
            ],
        ];

        $content = $paragraphs[$type] ?? $paragraphs['education'];
        return implode("\n\n", $content);
    }
}
