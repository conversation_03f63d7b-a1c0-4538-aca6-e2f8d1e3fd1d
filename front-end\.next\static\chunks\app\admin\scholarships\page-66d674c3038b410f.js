(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[458],{1546:(e,t,s)=>{Promise.resolve().then(s.bind(s,1678))},1678:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L});var a=s(5155),r=s(2115),i=s(8482),l=s(7168),n=s(8145),d=s(4964),o=s(9852),c=s(2714),u=s(9474),p=s(5784),m=s(5040),f=s(6785),x=s(7949),h=s(7265),g=s(2010),y=s(7580),v=s(4616),j=s(5868),b=s(9074),N=s(2657),w=s(3717);let S=0,C=new Map,A=e=>{if(C.has(e))return;let t=setTimeout(()=>{C.delete(e),R({type:"REMOVE_TOAST",toastId:e})},1e6);C.set(e,t)},_=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?A(s):e.toasts.forEach(e=>{A(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},T=[],E={toasts:[]};function R(e){E=_(E,e),T.forEach(e=>{e(E)})}function k(e){let{...t}=e,s=(S=(S+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>R({type:"DISMISS_TOAST",toastId:s});return R({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>R({type:"UPDATE_TOAST",toast:{...e,id:s}})}}var O=s(1886),D=s(5452),F=s(4416),U=s(3999);let J=D.bL,z=D.l9,I=D.ZL;D.bm;let P=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D.hJ,{ref:t,className:(0,U.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});P.displayName=D.hJ.displayName;let q=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(I,{children:[(0,a.jsx)(P,{}),(0,a.jsxs)(D.UC,{ref:t,className:(0,U.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...i,children:[r,(0,a.jsxs)(D.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(F.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});q.displayName=D.UC.displayName;let M=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,U.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};M.displayName="DialogHeader";let V=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D.hE,{ref:t,className:(0,U.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});V.displayName=D.hE.displayName;let Z=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D.VY,{ref:t,className:(0,U.cn)("text-sm text-muted-foreground",s),...r})});Z.displayName=D.VY.displayName;let W={primary:{icon:m.A,title:"Primary School",color:"bg-blue-500"},secondary:{icon:f.A,title:"Secondary School",color:"bg-green-500"},university:{icon:x.A,title:"University",color:"bg-purple-500"}};function L(){let[e,t]=(0,r.useState)([]),[s,f]=(0,r.useState)(!0),[x,S]=(0,r.useState)("all"),[C,A]=(0,r.useState)(!1),[_,D]=(0,r.useState)(!1),[F,U]=(0,r.useState)(null),[I,P]=(0,r.useState)({title:"",category:"primary",description:"",eligibility_criteria:"",amount:"",application_deadline:"",max_applicants:""}),{toast:L}=function(){let[e,t]=r.useState(E);return r.useEffect(()=>(T.push(t),()=>{let e=T.indexOf(t);e>-1&&T.splice(e,1)}),[e]),{...e,toast:k,dismiss:e=>R({type:"DISMISS_TOAST",toastId:e})}}();(0,r.useEffect)(()=>{$()},[]);let $=async()=>{try{let e=await O.uE.request("/admin/scholarships");e.success&&e.data&&t(e.data)}catch(e){console.error("Error fetching scholarships:",e),L({title:"Error",description:"Failed to load scholarships",variant:"destructive"})}finally{f(!1)}},B=async e=>{e.preventDefault();try{(await O.uE.request("/admin/scholarships",{method:"POST",body:JSON.stringify(I)})).success&&(L({title:"Success",description:"Scholarship created successfully"}),A(!1),H(),$())}catch(e){L({title:"Error",description:"Failed to create scholarship",variant:"destructive"})}},X=async(e,t)=>{try{let s="open"===t?"close":"open";(await O.uE.request("/admin/scholarships/".concat(e,"/").concat(s),{method:"POST"})).success&&(L({title:"Success",description:"Scholarship ".concat(s,"ed successfully")}),$())}catch(e){L({title:"Error",description:"Failed to update scholarship status",variant:"destructive"})}},H=()=>{P({title:"",category:"primary",description:"",eligibility_criteria:"",amount:"",application_deadline:"",max_applicants:""})},Y="all"===x?e:e.filter(e=>e.category===x),G={total:e.length,open:e.filter(e=>"open"===e.status).length,closed:e.filter(e=>"closed"===e.status).length,applications:e.reduce((e,t)=>e+t.current_applicants,0)};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Scholarship Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage scholarships, applications, and dynamic form fields"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Scholarships"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:G.total})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Open"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:G.open})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Closed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:G.closed})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Applications"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:G.applications})]}),(0,a.jsx)(y.A,{className:"h-8 w-8 text-purple-500"})]})})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)(d.tU,{value:x,onValueChange:S,children:(0,a.jsxs)(d.j7,{children:[(0,a.jsx)(d.Xi,{value:"all",children:"All"}),(0,a.jsx)(d.Xi,{value:"primary",children:"Primary"}),(0,a.jsx)(d.Xi,{value:"secondary",children:"Secondary"}),(0,a.jsx)(d.Xi,{value:"university",children:"University"})]})}),(0,a.jsxs)(J,{open:C,onOpenChange:A,children:[(0,a.jsx)(z,{asChild:!0,children:(0,a.jsxs)(l.$,{children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Create Scholarship"]})}),(0,a.jsxs)(q,{className:"max-w-2xl",children:[(0,a.jsxs)(M,{children:[(0,a.jsx)(V,{children:"Create New Scholarship"}),(0,a.jsx)(Z,{children:"Create a new scholarship with custom form fields for applications."})]}),(0,a.jsxs)("form",{onSubmit:B,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"title",children:"Title"}),(0,a.jsx)(o.p,{id:"title",value:I.title,onChange:e=>P(t=>({...t,title:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(p.l6,{value:I.category,onValueChange:e=>P(t=>({...t,category:e})),children:[(0,a.jsx)(p.bq,{children:(0,a.jsx)(p.yv,{})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"primary",children:"Primary School"}),(0,a.jsx)(p.eb,{value:"secondary",children:"Secondary School"}),(0,a.jsx)(p.eb,{value:"university",children:"University"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)(u.T,{id:"description",value:I.description,onChange:e=>P(t=>({...t,description:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"eligibility",children:"Eligibility Criteria"}),(0,a.jsx)(u.T,{id:"eligibility",value:I.eligibility_criteria,onChange:e=>P(t=>({...t,eligibility_criteria:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"amount",children:"Amount (₦)"}),(0,a.jsx)(o.p,{id:"amount",type:"number",value:I.amount,onChange:e=>P(t=>({...t,amount:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"deadline",children:"Application Deadline"}),(0,a.jsx)(o.p,{id:"deadline",type:"date",value:I.application_deadline,onChange:e=>P(t=>({...t,application_deadline:e.target.value})),required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"max_applicants",children:"Maximum Applicants"}),(0,a.jsx)(o.p,{id:"max_applicants",type:"number",value:I.max_applicants,onChange:e=>P(t=>({...t,max_applicants:e.target.value}))})]}),(0,a.jsx)(l.$,{type:"submit",className:"w-full",children:"Create Scholarship"})]})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Y.map(e=>(0,a.jsxs)(i.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[r.createElement(W[e.category].icon,{className:"h-5 w-5"}),(0,a.jsx)(i.ZB,{className:"text-lg leading-tight",children:e.title})]}),(0,a.jsx)(n.E,{variant:"open"===e.status?"default":"secondary",className:"open"===e.status?"bg-green-500":"bg-red-500",children:e.status})]}),(0,a.jsx)(i.BT,{className:"line-clamp-2",children:e.description})]}),(0,a.jsxs)(i.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["₦",e.amount.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Due: ",new Date(e.application_deadline).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[e.current_applicants,"/",e.max_applicants||"∞"," applicants"]})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,a.jsx)(l.$,{variant:"open"===e.status?"destructive":"default",size:"sm",className:"flex-1",onClick:()=>X(e.id,e.status),children:"open"===e.status?"Close":"Open"})]})]})]},e.id))}),0===Y.length&&!s&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No scholarships found"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"all"===x?"Create your first scholarship to get started.":"No scholarships in the ".concat(x," category.")}),(0,a.jsxs)(l.$,{onClick:()=>A(!0),children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Create Scholarship"]})]})]})})}},2714:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(5155),r=s(2115),i=s(968),l=s(2085),n=s(3999);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),s),...r})});o.displayName=i.b.displayName},3999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},4964:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var a=s(5155),r=s(2115),i=s(704),l=s(3999);let n=i.bL,d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.B8,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});d.displayName=i.B8.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});o.displayName=i.l9.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=i.UC.displayName},5784:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>u});var a=s(5155),r=s(2115),i=s(8715),l=s(6474),n=s(7863),d=s(5196),o=s(3999);let c=i.bL;i.YJ;let u=i.WT,p=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(i.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...n,children:[r,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=i.l9.displayName;let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});m.displayName=i.PP.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});f.displayName=i.wn.displayName;let x=r.forwardRef((e,t)=>{let{className:s,children:r,position:l="popper",...n}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...n,children:[(0,a.jsx)(m,{}),(0,a.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(f,{})]})})});x.displayName=i.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=i.JU.displayName;let h=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(i.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:r})]})});h.displayName=i.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=i.wv.displayName},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155),r=s(2115),i=s(9708),l=s(2085),n=s(3999);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:o=!1,...c}=e,u=o?i.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(d({variant:r,size:l,className:s})),ref:t,suppressHydrationWarning:!0,...c})});o.displayName="Button"},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(5155);s(2115);var r=s(2085),i=s(3999);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:s}),t),...r})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>n});var a=s(5155),r=s(2115),i=s(3999);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});l.displayName="Card";let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...r})});n.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...r})});c.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},9474:(e,t,s)=>{"use strict";s.d(t,{T:()=>l});var a=s(5155),r=s(2115),i=s(3999);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...r})});l.displayName="Textarea"},9852:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var a=s(5155),r=s(2115),i=s(3999);let l=r.forwardRef((e,t)=>{let{className:s,type:r,...l}=e;return(0,a.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,suppressHydrationWarning:!0,...l})});l.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[598,514,114,609,558,886,441,684,358],()=>t(1546)),_N_E=e.O()}]);