"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   APPLICATION_STATUS: () => (/* binding */ APPLICATION_STATUS),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   BACKEND_BASE_URL: () => (/* binding */ BACKEND_BASE_URL),\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   DONATION_TYPES: () => (/* binding */ DONATION_TYPES),\n/* harmony export */   ENV: () => (/* binding */ ENV),\n/* harmony export */   FEATURES: () => (/* binding */ FEATURES),\n/* harmony export */   FILE_UPLOAD: () => (/* binding */ FILE_UPLOAD),\n/* harmony export */   LAYOUT_CONFIG: () => (/* binding */ LAYOUT_CONFIG),\n/* harmony export */   PAGINATION: () => (/* binding */ PAGINATION),\n/* harmony export */   PAYMENT_METHODS: () => (/* binding */ PAYMENT_METHODS),\n/* harmony export */   SCHOLARSHIP_CATEGORIES: () => (/* binding */ SCHOLARSHIP_CATEGORIES),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_CONFIG: () => (/* binding */ THEME_CONFIG),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   VALIDATION_RULES: () => (/* binding */ VALIDATION_RULES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   validateDonationAmount: () => (/* binding */ validateDonationAmount)\n/* harmony export */ });\n/**\n * Application Constants\n * Centralized configuration for the NGO platform frontend\n */ // =============================================================================\n// API CONFIGURATION\n// =============================================================================\n/**\n * Base API URL for the Laravel backend\n * Change this URL to switch between development, staging, and production\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n/**\n * Laravel backend base URL (without /api/v1)\n * Used for authentication redirects and file uploads\n */ const BACKEND_BASE_URL = \"http://localhost:8000\" || 0;\n/**\n * API endpoints configuration\n */ const API_ENDPOINTS = {\n    // Authentication\n    AUTH: {\n        LOGIN: '/login',\n        REGISTER: '/register',\n        LOGOUT: '/logout',\n        PROFILE: '/profile',\n        REFRESH: '/refresh-token'\n    },\n    // Public endpoints\n    PUBLIC: {\n        SCHOLARSHIPS: '/public-scholarships',\n        BLOG_POSTS: '/blog/posts',\n        EVENTS: '/events',\n        PROGRAMS: '/programs',\n        TEAM: '/team',\n        SETTINGS: '/settings',\n        CONTACT: '/contact',\n        NEWSLETTER: '/newsletter/subscribe',\n        DONATIONS: '/donations',\n        DONATION_CAMPAIGNS: '/donations/campaigns',\n        DONATION_RECEIPT_UPLOAD: '/donations/{id}/upload-receipt',\n        DONATION_VERIFY: '/donations/verify/{reference}'\n    },\n    // Protected endpoints\n    PROTECTED: {\n        DASHBOARD: '/dashboard',\n        APPLICATIONS: '/scholarship-applications',\n        MY_DONATIONS: '/donations/my-donations',\n        PROFILE: '/profile'\n    }\n};\n// =============================================================================\n// APPLICATION INFORMATION\n// =============================================================================\n/**\n * Application name and branding\n */ const APP_CONFIG = {\n    NAME: 'HALIMAKQ Foundation',\n    SHORT_NAME: 'HALIMAKQ',\n    TAGLINE: 'Empowering Communities Through Education',\n    DESCRIPTION: 'A non-governmental organization dedicated to providing educational opportunities and community development programs.',\n    VERSION: '1.0.0'\n};\n/**\n * Contact information\n */ const CONTACT_INFO = {\n    EMAIL: '<EMAIL>',\n    PHONE: '+234 ************',\n    ADDRESS: 'Lagos, Nigeria',\n    WEBSITE: 'https://halimakqfoundation.org'\n};\n/**\n * Social media links\n */ const SOCIAL_LINKS = {\n    FACEBOOK: 'https://facebook.com/halimakqfoundation',\n    TWITTER: 'https://twitter.com/halimakqfoundation',\n    INSTAGRAM: 'https://instagram.com/halimakqfoundation',\n    LINKEDIN: 'https://linkedin.com/company/halimakqfoundation',\n    YOUTUBE: 'https://youtube.com/@halimakqfoundation'\n};\n// =============================================================================\n// UI CONFIGURATION\n// =============================================================================\n/**\n * Theme and styling constants\n */ const THEME_CONFIG = {\n    COLORS: {\n        PRIMARY: '#059669',\n        PRIMARY_DARK: '#047857',\n        SECONDARY: '#3B82F6',\n        ACCENT: '#8B5CF6',\n        SUCCESS: '#10B981',\n        WARNING: '#F59E0B',\n        ERROR: '#EF4444'\n    },\n    GRADIENTS: {\n        PRIMARY: 'from-blue-50 via-indigo-50 to-purple-50',\n        HEADER: 'from-blue-600 via-indigo-600 to-purple-600',\n        ADMIN: 'from-green-600 to-green-700'\n    }\n};\n/**\n * Layout configuration\n */ const LAYOUT_CONFIG = {\n    CONTAINER_MAX_WIDTH: 'max-w-7xl',\n    SECTION_PADDING: 'py-16',\n    CARD_SHADOW: 'shadow-lg',\n    BORDER_RADIUS: 'rounded-lg'\n};\n// =============================================================================\n// BUSINESS LOGIC CONSTANTS\n// =============================================================================\n/**\n * Scholarship categories\n */ const SCHOLARSHIP_CATEGORIES = {\n    PRIMARY: 'primary',\n    SECONDARY: 'secondary',\n    UNIVERSITY: 'university'\n};\n/**\n * User roles\n */ const USER_ROLES = {\n    ADMIN: 'admin',\n    STUDENT: 'student',\n    PARTNER: 'partner',\n    NORMAL: 'normal'\n};\n/**\n * Application statuses\n */ const APPLICATION_STATUS = {\n    PENDING: 'pending',\n    UNDER_REVIEW: 'under_review',\n    APPROVED: 'approved',\n    REJECTED: 'rejected',\n    COMPLETED: 'completed'\n};\n/**\n * Payment methods\n */ const PAYMENT_METHODS = {\n    PAYSTACK: 'paystack',\n    BANK_TRANSFER: 'bank_transfer'\n};\n/**\n * Donation types\n */ const DONATION_TYPES = {\n    ONE_TIME: 'one-time',\n    RECURRING: 'recurring'\n};\n// =============================================================================\n// VALIDATION CONSTANTS\n// =============================================================================\n/**\n * Form validation rules\n */ const VALIDATION_RULES = {\n    EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    PHONE_REGEX: /^(\\+234|0)[789][01]\\d{8}$/,\n    PASSWORD_MIN_LENGTH: 8,\n    NAME_MIN_LENGTH: 2,\n    NAME_MAX_LENGTH: 50,\n    MESSAGE_MAX_LENGTH: 1000,\n    // Donation validation\n    DONATION_MIN_AMOUNT: 15000,\n    DONATION_MAX_AMOUNT: ********\n};\n/**\n * File upload constraints\n */ const FILE_UPLOAD = {\n    MAX_SIZE: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'image/jpeg',\n        'image/png',\n        'image/webp',\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.jpg',\n        '.jpeg',\n        '.png',\n        '.webp',\n        '.pdf'\n    ]\n};\n// =============================================================================\n// PAGINATION AND LIMITS\n// =============================================================================\n/**\n * Pagination configuration\n */ const PAGINATION = {\n    DEFAULT_PAGE_SIZE: 10,\n    MAX_PAGE_SIZE: 50,\n    BLOG_POSTS_PER_PAGE: 6,\n    SCHOLARSHIPS_PER_PAGE: 9,\n    EVENTS_PER_PAGE: 8\n};\n// =============================================================================\n// CACHE AND PERFORMANCE\n// =============================================================================\n/**\n * Cache configuration\n */ const CACHE_CONFIG = {\n    DEFAULT_STALE_TIME: 5 * 60 * 1000,\n    DEFAULT_CACHE_TIME: 10 * 60 * 1000,\n    STATIC_DATA_CACHE_TIME: 60 * 60 * 1000\n};\n// =============================================================================\n// ENVIRONMENT HELPERS\n// =============================================================================\n/**\n * Environment detection\n */ const ENV = {\n    IS_DEVELOPMENT: \"development\" === 'development',\n    IS_PRODUCTION: \"development\" === 'production',\n    IS_TEST: \"development\" === 'test'\n};\n/**\n * Feature flags\n */ const FEATURES = {\n    ENABLE_ANALYTICS: \"false\" === 'true',\n    ENABLE_CHAT_SUPPORT: \"false\" === 'true',\n    ENABLE_NOTIFICATIONS: \"false\" === 'true'\n};\n// =============================================================================\n// EXPORT ALL CONSTANTS\n// =============================================================================\n/**\n * Default export with all constants grouped\n */ const CONSTANTS = {\n    API_BASE_URL,\n    BACKEND_BASE_URL,\n    API_ENDPOINTS,\n    APP_CONFIG,\n    CONTACT_INFO,\n    SOCIAL_LINKS,\n    THEME_CONFIG,\n    LAYOUT_CONFIG,\n    SCHOLARSHIP_CATEGORIES,\n    USER_ROLES,\n    APPLICATION_STATUS,\n    PAYMENT_METHODS,\n    DONATION_TYPES,\n    VALIDATION_RULES,\n    FILE_UPLOAD,\n    PAGINATION,\n    CACHE_CONFIG,\n    ENV,\n    FEATURES\n};\n// =============================================================================\n// UTILITY FUNCTIONS\n// =============================================================================\n/**\n * Format currency amount in Nigerian Naira\n */ const formatCurrency = (amount)=>{\n    return \"₦\".concat(amount.toLocaleString('en-NG'));\n};\n/**\n * Validate donation amount\n */ const validateDonationAmount = (amount)=>{\n    if (!amount || amount < VALIDATION_RULES.DONATION_MIN_AMOUNT) {\n        return {\n            isValid: false,\n            error: \"Minimum donation amount is \".concat(formatCurrency(VALIDATION_RULES.DONATION_MIN_AMOUNT))\n        };\n    }\n    if (amount > VALIDATION_RULES.DONATION_MAX_AMOUNT) {\n        return {\n            isValid: false,\n            error: \"Maximum donation amount is \".concat(formatCurrency(VALIDATION_RULES.DONATION_MAX_AMOUNT))\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CONSTANTS);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/constants.ts\n"));

/***/ })

});