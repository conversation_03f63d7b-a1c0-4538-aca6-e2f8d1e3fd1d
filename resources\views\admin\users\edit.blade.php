@extends('layouts.admin')

@section('title', 'Edit User')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit User</h1>
        <div>
            <a href="{{ route('admin.users.show', $id) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.users.update', $id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="John" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="Doe" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" value="+************">
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select" id="role_id" name="role_id" required onchange="toggleRoleFields()">
                                        <option value="1" {{ old('role_id', $user->role_id ?? 1) == 1 ? 'selected' : '' }}>Normal User</option>
                                        <option value="2" {{ old('role_id', $user->role_id ?? 1) == 2 ? 'selected' : '' }}>University Student</option>
                                        <option value="3" {{ old('role_id', $user->role_id ?? 1) == 3 ? 'selected' : '' }}>Partner Organization</option>
                                        <option value="4" {{ old('role_id', $user->role_id ?? 1) == 4 ? 'selected' : '' }}>Administrator</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" selected>Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="1990-01-01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" selected>Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3">123 Sample Street, Sample Area</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city" value="Lagos">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state" value="Lagos">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" value="Nigeria">
                                </div>
                            </div>
                        </div>

                        <!-- University Student Fields -->
                        <div id="student-fields" style="display: none;">
                            <hr>
                            <h6 class="text-primary mb-3">University Student Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="university_name" class="form-label">University Name</label>
                                        <input type="text" class="form-control" id="university_name" name="university_name" value="{{ old('university_name', $user->university_name ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="course_of_study" class="form-label">Course of Study</label>
                                        <input type="text" class="form-control" id="course_of_study" name="course_of_study" value="{{ old('course_of_study', $user->course_of_study ?? '') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="year_of_study" class="form-label">Year of Study</label>
                                        <select class="form-select" id="year_of_study" name="year_of_study">
                                            <option value="">Select Year</option>
                                            @for($i = 1; $i <= 7; $i++)
                                                <option value="{{ $i }}" {{ old('year_of_study', $user->year_of_study ?? '') == $i ? 'selected' : '' }}>{{ $i }}{{ $i == 1 ? 'st' : ($i == 2 ? 'nd' : ($i == 3 ? 'rd' : 'th')) }} Year</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="matriculation_number" class="form-label">Matriculation Number</label>
                                        <input type="text" class="form-control" id="matriculation_number" name="matriculation_number" value="{{ old('matriculation_number', $user->matriculation_number ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="cgpa" class="form-label">CGPA</label>
                                        <input type="number" class="form-control" id="cgpa" name="cgpa" step="0.01" min="0" max="5" value="{{ old('cgpa', $user->cgpa ?? '') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Partner Organization Fields -->
                        <div id="partner-fields" style="display: none;">
                            <hr>
                            <h6 class="text-primary mb-3">Partner Organization Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_name" class="form-label">Organization Name</label>
                                        <input type="text" class="form-control" id="organization_name" name="organization_name" value="{{ old('organization_name', $user->organization_name ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_type" class="form-label">Organization Type</label>
                                        <select class="form-select" id="organization_type" name="organization_type">
                                            <option value="">Select Type</option>
                                            <option value="primary_school" {{ old('organization_type', $user->organization_type ?? '') == 'primary_school' ? 'selected' : '' }}>Primary School</option>
                                            <option value="secondary_school" {{ old('organization_type', $user->organization_type ?? '') == 'secondary_school' ? 'selected' : '' }}>Secondary School</option>
                                            <option value="university" {{ old('organization_type', $user->organization_type ?? '') == 'university' ? 'selected' : '' }}>University</option>
                                            <option value="ngo" {{ old('organization_type', $user->organization_type ?? '') == 'ngo' ? 'selected' : '' }}>NGO</option>
                                            <option value="government" {{ old('organization_type', $user->organization_type ?? '') == 'government' ? 'selected' : '' }}>Government</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="organization_address" class="form-label">Organization Address</label>
                                <textarea class="form-control" id="organization_address" name="organization_address" rows="2">{{ old('organization_address', $user->organization_address ?? '') }}</textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_phone" class="form-label">Organization Phone</label>
                                        <input type="tel" class="form-control" id="organization_phone" name="organization_phone" value="{{ old('organization_phone', $user->organization_phone ?? '') }}">
                                    </div>
                                </div>
                            </div>
                            <h6 class="text-secondary mb-3">Principal/Contact Person Information</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_name" class="form-label">Principal Name</label>
                                        <input type="text" class="form-control" id="principal_name" name="principal_name" value="{{ old('principal_name', $user->principal_name ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_email" class="form-label">Principal Email</label>
                                        <input type="email" class="form-control" id="principal_email" name="principal_email" value="{{ old('principal_email', $user->principal_email ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="principal_phone" class="form-label">Principal Phone</label>
                                        <input type="tel" class="form-control" id="principal_phone" name="principal_phone" value="{{ old('principal_phone', $user->principal_phone ?? '') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Password Change -->
                        <hr>
                        <h6 class="text-primary mb-3">Change Password (Optional)</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="password" name="password">
                                    <small class="text-muted">Leave blank to keep current password</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </button>
                            <div>
                                <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update User
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Profile Picture -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Picture</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="profilePreview" src="{{ asset('img/default-avatar.png') }}" alt="Profile Preview" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*" onchange="previewImage(this)">
                    </div>
                    <small class="text-muted">Leave empty to keep current photo</small>
                </div>
            </div>

            <!-- User Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-primary">5</h4>
                                <p class="text-muted mb-0">Donations</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">₦25,000</h4>
                                <p class="text-muted mb-0">Total</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-info">3</h4>
                                <p class="text-muted mb-0">Events</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">24h</h4>
                                <p class="text-muted mb-0">Volunteer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="sendEmail('<EMAIL>')">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </button>
                        <button class="btn btn-outline-info" onclick="viewActivity({{ $id }})">
                            <i class="fas fa-history me-2"></i>View Activity
                        </button>
                        <button class="btn btn-outline-warning" onclick="toggleStatus({{ $id }})">
                            <i class="fas fa-toggle-on me-2"></i>Toggle Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('profilePreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function confirmDelete() {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.users.destroy", $id) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

// Toggle role-specific fields
function toggleRoleFields() {
    const roleId = document.getElementById('role_id').value;
    const studentFields = document.getElementById('student-fields');
    const partnerFields = document.getElementById('partner-fields');

    // Hide all role-specific fields first
    studentFields.style.display = 'none';
    partnerFields.style.display = 'none';

    // Show relevant fields based on role
    if (roleId === '2') { // University Student
        studentFields.style.display = 'block';
    } else if (roleId === '3') { // Partner Organization
        partnerFields.style.display = 'block';
    }
}

// Initialize role fields on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleRoleFields();
});

function sendEmail(email) {
    window.location.href = `mailto:${email}`;
}

function viewActivity(userId) {
    alert('Activity viewer not implemented yet');
}

function toggleStatus(userId) {
    if (confirm('Are you sure you want to change this user\'s status?')) {
        alert('Status toggle not implemented yet');
    }
}
</script>
@endsection 