<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>Test Login Form</h1>
    <form id="testForm" method="POST" action="/test-login">
        <input type="hidden" name="_token" value="">
        <div>
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" name="password" value="password123" required>
        </div>
        <button type="submit">Test Login</button>
    </form>

    <script>
        // Get CSRF token
        fetch('/test-auth')
            .then(response => response.json())
            .then(data => {
                console.log('Auth status:', data);
            });

        // Set CSRF token
        document.querySelector('input[name="_token"]').value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    </script>
</body>
</html>
