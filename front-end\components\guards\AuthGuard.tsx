'use client'

import { ReactNode, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Lock, User } from 'lucide-react'
import { Button } from '@/components/ui/button'

export interface AuthGuardProps {
  children: ReactNode
  requireAuth?: boolean
  requiredRole?: string | string[]
  fallbackUrl?: string
  loginUrl?: string
  showAccessDenied?: boolean
  redirectOnSuccess?: boolean
}

/**
 * General authentication guard for protecting routes
 */
export default function AuthGuard({
  children,
  requireAuth = true,
  requiredRole,
  fallbackUrl = '/dashboard',
  loginUrl = '/auth/login',
  showAccessDenied = true,
  redirectOnSuccess = false
}: AuthGuardProps) {
  const { user, isLoading } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (isLoading) return

    // Check authentication requirement
    if (requireAuth && !user) {
      // Store current URL for redirect after login
      const currentUrl = window.location.pathname + window.location.search
      localStorage.setItem('redirectAfterLogin', currentUrl)

      toast({
        title: "Authentication Required",
        description: "Please log in to access this page",
        variant: "default",
      })

      router.push(loginUrl)
      return
    }

    // Check role requirements
    if (user && requiredRole) {
      const userRole = user.role
      const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
      
      if (!requiredRoles.includes(userRole)) {
        if (!showAccessDenied) {
          router.push(fallbackUrl)
          return
        }
      }
    }

    // Handle redirect on successful authentication
    if (user && redirectOnSuccess) {
      const redirectUrl = localStorage.getItem('redirectAfterLogin')
      if (redirectUrl && redirectUrl !== window.location.pathname + window.location.search) {
        localStorage.removeItem('redirectAfterLogin')
        router.push(redirectUrl)
        return
      }
    }

  }, [user, isLoading, requireAuth, requiredRole, router, toast, fallbackUrl, loginUrl, showAccessDenied, redirectOnSuccess])

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">
            Checking authentication...
          </p>
        </div>
      </div>
    )
  }

  // Check if user is not authenticated but auth is required
  if (requireAuth && !user) {
    return null // Will redirect in useEffect
  }

  // Check role requirements
  if (user && requiredRole) {
    const userRole = user.role
    const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    
    if (!requiredRoles.includes(userRole)) {
      if (!showAccessDenied) {
        return null // Will redirect in useEffect
      }

      // Show access denied page
      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {/* Header */}
          <section className="relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-red-600 to-red-800 text-white">
            <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
              <div className="max-w-4xl mx-auto text-center space-y-4 sm:space-y-6">
                <Lock className="h-16 w-16 mx-auto mb-4 opacity-80" />
                <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                  Access Denied
                </h1>
                <p className="text-base sm:text-lg md:text-xl text-red-100 max-w-3xl mx-auto px-2">
                  You don't have permission to access this page
                </p>
              </div>
            </div>
          </section>

          {/* Access Denied Content */}
          <section className="py-12 bg-white dark:bg-gray-900">
            <div className="container mx-auto px-4">
              <div className="max-w-2xl mx-auto text-center">
                <User className="h-16 w-16 text-amber-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Insufficient Permissions
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6 text-lg">
                  Your account doesn't have the required permissions to access this page.
                </p>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    <p><strong>Your Role:</strong> {userRole}</p>
                    <p><strong>Required Role(s):</strong> {Array.isArray(requiredRole) ? requiredRole.join(', ') : requiredRole}</p>
                  </div>
                </div>
                
                <div className="flex gap-4 justify-center flex-wrap">
                  <Button 
                    onClick={() => router.push(fallbackUrl)} 
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Go to Dashboard
                  </Button>
                  
                  <Button 
                    onClick={() => window.history.back()} 
                    variant="outline"
                  >
                    Go Back
                  </Button>
                </div>

                {/* Help Section */}
                <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Need Different Access?
                  </h3>
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                    <p>• Contact your administrator to request additional permissions</p>
                    <p>• Make sure you're logged in with the correct account</p>
                    <p>• Some features may require account verification</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      )
    }
  }

  // User has access, render children
  return <>{children}</>
}

/**
 * Higher-order component for protecting routes with authentication
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<AuthGuardProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <AuthGuard {...guardProps}>
        <Component {...props} />
      </AuthGuard>
    )
  }
}

/**
 * Specific guards for different user roles
 */
export const AdminGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requiredRole'>) => (
  <AuthGuard requiredRole="admin" {...props}>
    {children}
  </AuthGuard>
)

export const PartnerGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requiredRole'>) => (
  <AuthGuard requiredRole="partner_organization" {...props}>
    {children}
  </AuthGuard>
)

export const StudentGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requiredRole'>) => (
  <AuthGuard requiredRole={["user", "student"]} {...props}>
    {children}
  </AuthGuard>
)

/**
 * Guard that only allows unauthenticated users (for login/register pages)
 */
export const GuestGuard = ({ children, redirectUrl = '/dashboard' }: { children: ReactNode, redirectUrl?: string }) => {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && user) {
      // Check for stored redirect URL
      const storedRedirect = localStorage.getItem('redirectAfterLogin')
      if (storedRedirect) {
        localStorage.removeItem('redirectAfterLogin')
        router.push(storedRedirect)
      } else {
        router.push(redirectUrl)
      }
    }
  }, [user, isLoading, router, redirectUrl])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-green-600" />
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect in useEffect
  }

  return <>{children}</>
}
