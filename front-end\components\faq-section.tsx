"use client"

import { useState } from "react"
import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

interface FAQ {
  id: string
  question: string
  answer: string
  category: "scholarships" | "backToSchool" | "higherEducation" | "literacy" | "donations" | "general"
}

const faqs: FAQ[] = [
  {
    id: "faq-1",
    question: "How can I apply for a Kofa Foundation Scholarship?",
    answer:
      "The scholarship application process is detailed on our website under the 'Scholarships' section. Applications are typically open between [Start Date] and [End Date] each year. Ensure you meet all eligibility criteria before applying.",
    category: "scholarships",
  },
  {
    id: "faq-2",
    question: "What are the eligibility criteria for Kofa Foundation support?",
    answer:
      "Eligibility criteria vary depending on the program. Generally, we look for individuals and communities demonstrating significant need and a commitment to improving their circumstances through education and empowerment. Specific criteria are listed on each program's page.",
    category: "general",
  },
  {
    id: "faq-3",
    question: "How do I apply for the Back-to-School Initiative?",
    answer:
      "Applications for the Back-to-School Initiative are accepted online through our website during the application period, which is usually between [Start Date] and [End Date]. Required documents include proof of enrollment and a statement of need.",
    category: "backToSchool",
  },
  {
    id: "faq-4",
    question: "What are the requirements for Higher Education Grants?",
    answer:
      "Higher Education Grants are awarded to exceptional students pursuing undergraduate or postgraduate studies. Requirements include a strong academic record, demonstrated leadership potential, and a clear financial need. Detailed requirements can be found on the Higher Education Grants page.",
    category: "higherEducation",
  },
  {
    id: "faq-5",
    question: "How can I enroll in the Community Literacy Program?",
    answer:
      "Enrollment in our Community Literacy Program is open to all community members. You can register at your local community center or through our website. Please check our website for program schedules and locations.",
    category: "literacy",
  },
  {
    id: "faq-6",
    question: "How are donations allocated, and how is transparency ensured?",
    answer:
      "We are committed to transparency in how we allocate donations. Funds are primarily used to support our core programs in education, community development, and empowerment initiatives. Detailed financial reports are published annually on our website, outlining how donations are utilized.",
    category: "donations",
  },
  {
    id: "faq-7",
    question: "Does Kofa Foundation offer support for vocational training?",
    answer:
      "Yes, we provide support for vocational training programs aimed at equipping individuals with practical skills for employment. Details about available programs and application processes can be found on our website.",
    category: "general",
  },
  {
    id: "faq-8",
    question: "How can I get involved with Kofa Foundation as a volunteer?",
    answer:
      "We welcome volunteers who are passionate about our mission. You can find information about volunteer opportunities and the application process on our website's 'Get Involved' section.",
    category: "general",
  },
  {
    id: "faq-9",
    question: "What geographical areas does Kofa Foundation serve?",
    answer:
      "Kofa Foundation primarily focuses on serving communities in [Specific Region/State in Nigeria]. Our programs are designed to address the unique challenges and needs of these areas.",
    category: "general",
  },
  {
    id: "faq-10",
    question: "How does Kofa Foundation measure the impact of its programs?",
    answer:
      "We employ a comprehensive monitoring and evaluation system to measure the impact of our programs. This includes tracking key indicators, conducting surveys, and gathering feedback from beneficiaries. Our impact reports are available on our website.",
    category: "general",
  },
]

export function FAQSection() {
  const [activeCategory, setActiveCategory] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")

  const filteredFAQs = faqs
    .filter((faq) => activeCategory === "all" || faq.category === activeCategory)
    .filter(
      (faq) =>
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase()),
    )

  const categories = [
    { id: "all", label: "All FAQs" },
    { id: "scholarships", label: "Scholarships" },
    { id: "backToSchool", label: "Back to School Initiative" },
    { id: "higherEducation", label: "Higher Education Grants" },
    { id: "literacy", label: "Community Literacy Program" },
    { id: "donations", label: "Donations" },
    { id: "general", label: "General" },
  ]

  return (
    <div>
      <div className="mb-8">
        <div className="relative max-w-md mx-auto mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search FAQs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>

        <div className="flex flex-wrap justify-center gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? "bg-green-600 text-white shadow-md"
                  : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800/30"
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      <div className="max-w-3xl mx-auto">
        <Accordion type="single" collapsible className="w-full space-y-4">
          {filteredFAQs.map((faq) => (
            <AccordionItem
              key={faq.id}
              value={faq.id}
              className="border border-green-100 dark:border-green-900/50 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-green-300 dark:hover:border-green-700 bg-white dark:bg-gray-900/50"
            >
              <AccordionTrigger className="px-6 py-4 text-left font-medium text-green-800 dark:text-green-200 hover:text-green-600 dark:hover:text-green-400 hover:no-underline">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="px-6 pb-4 text-muted-foreground">{faq.answer}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {filteredFAQs.length === 0 && (
          <div className="text-center py-12 bg-green-50 dark:bg-green-950/20 rounded-xl">
            <h3 className="text-xl font-semibold mb-2">No FAQs found</h3>
            <p className="text-muted-foreground">Try adjusting your search or selecting a different category.</p>
          </div>
        )}
      </div>
    </div>
  )
}
