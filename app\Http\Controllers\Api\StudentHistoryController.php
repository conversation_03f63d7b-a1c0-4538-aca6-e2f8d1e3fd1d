<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\PartnerOrganization;
use App\Services\StudentHistoryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class StudentHistoryController extends Controller
{
    protected $historyService;

    public function __construct(StudentHistoryService $historyService)
    {
        $this->historyService = $historyService;
    }

    /**
     * Get student history
     */
    public function getStudentHistory(Request $request, string $studentId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'include_progressions' => 'boolean',
                'include_applications' => 'boolean',
                'include_achievements' => 'boolean',
                'academic_year' => 'nullable|string',
                'limit' => 'nullable|integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            
            // Verify access to student
            if ($user->role === 'partner_organization') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $student = Student::where('id', $studentId)
                    ->where('school_id', $partnerOrg->id)
                    ->firstOrFail();
            } elseif ($user->role === 'student') {
                $student = Student::where('id', $studentId)
                    ->where('user_id', $user->id)
                    ->firstOrFail();
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $options = [
                'include_progressions' => $request->boolean('include_progressions', true),
                'include_applications' => $request->boolean('include_applications', true),
                'include_achievements' => $request->boolean('include_achievements', true),
                'academic_year' => $request->academic_year,
                'limit' => $request->limit,
            ];

            $history = $this->historyService->getStudentHistory($student, $options);

            return response()->json([
                'success' => true,
                'data' => $history,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch student history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get organization student history (for partner organizations)
     */
    public function getOrganizationHistory(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'academic_year' => 'nullable|string',
                'limit' => 'nullable|integer|min:1|max:200',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            
            if ($user->role !== 'partner_organization') {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Partner organization role required.'
                ], 403);
            }

            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $options = [
                'academic_year' => $request->academic_year,
                'limit' => $request->get('limit', 50),
            ];

            $history = $this->historyService->getOrganizationStudentHistory($partnerOrg, $options);

            return response()->json([
                'success' => true,
                'data' => $history,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch organization history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student summary
     */
    public function getStudentSummary(string $studentId): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Verify access to student
            if ($user->role === 'partner_organization') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $student = Student::where('id', $studentId)
                    ->where('school_id', $partnerOrg->id)
                    ->firstOrFail();
            } elseif ($user->role === 'student') {
                $student = Student::where('id', $studentId)
                    ->where('user_id', $user->id)
                    ->firstOrFail();
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $summary = $this->historyService->getStudentSummary($student);

            return response()->json([
                'success' => true,
                'data' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch student summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get organization summary
     */
    public function getOrganizationSummary(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'academic_year' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            
            if ($user->role !== 'partner_organization') {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Partner organization role required.'
                ], 403);
            }

            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
            $summary = $this->historyService->getOrganizationSummary($partnerOrg, $request->academic_year);

            return response()->json([
                'success' => true,
                'data' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch organization summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export student history to CSV
     */
    public function exportStudentHistory(Request $request, string $studentId): Response
    {
        try {
            $validator = Validator::make($request->all(), [
                'include_progressions' => 'boolean',
                'include_applications' => 'boolean',
                'include_achievements' => 'boolean',
                'academic_year' => 'nullable|string',
                'limit' => 'nullable|integer|min:1|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            
            // Verify access to student
            if ($user->role === 'partner_organization') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $student = Student::where('id', $studentId)
                    ->where('school_id', $partnerOrg->id)
                    ->firstOrFail();
            } elseif ($user->role === 'student') {
                $student = Student::where('id', $studentId)
                    ->where('user_id', $user->id)
                    ->firstOrFail();
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $options = [
                'include_progressions' => $request->boolean('include_progressions', true),
                'include_applications' => $request->boolean('include_applications', true),
                'include_achievements' => $request->boolean('include_achievements', true),
                'academic_year' => $request->academic_year,
                'limit' => $request->limit,
            ];

            $csvContent = $this->historyService->exportStudentHistory($student, $options);
            $filename = "student_history_{$student->id}_" . date('Y-m-d_H-i-s') . ".csv";

            return response($csvContent)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export student history',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
