'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen, 
  School, 
  GraduationCap, 
  Calendar, 
  Users, 
  DollarSign, 
  ArrowRight, 
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  FileText,
  User,
  Clock,
  Star
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { BACKEND_BASE_URL } from '@/lib/constants'

interface Scholarship {
  id: number
  title: string
  slug: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria?: string
  requirements?: string
  amount: number
  application_deadline: string
  status: string
  current_applicants: number
  max_applicants?: number
  is_featured?: boolean
  category_instructions?: {
    title: string
    age_range: string
    filled_by: string
    instruction: string
    required_info: string[]
  }
  custom_fields?: Array<{
    id: number
    field_name: string
    field_type: string
    is_required: boolean
    field_options?: string[]
    validation_rules?: any
  }>
  form_metadata?: {
    validation_rules: any
    field_constraints: any
    has_file_uploads: boolean
  }
  application_instructions?: string
  terms_conditions?: string
  documents_required?: string[]
}

const categoryConfig = {
  primary: {
    icon: BookOpen,
    title: "Primary School",
    color: "bg-blue-500",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200"
  },
  secondary: {
    icon: School,
    title: "Secondary School", 
    color: "bg-green-500",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200"
  },
  university: {
    icon: GraduationCap,
    title: "University",
    color: "bg-purple-500",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700",
    borderColor: "border-purple-200"
  }
}

export default function ScholarshipDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [scholarship, setScholarship] = useState<Scholarship | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      fetchScholarship(params.id as string)
    }
  }, [params.id])

  const fetchScholarship = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getPublicScholarship(id)
      
      if (response.success && response.data) {
        setScholarship(response.data)
      } else {
        throw new Error(response.message || 'Scholarship not found')
      }
    } catch (error) {
      console.error('Error fetching scholarship:', error)
      setError('Failed to load scholarship details. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const formatDeadline = (deadline: string) => {
    return new Date(deadline).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadge = (scholarship: Scholarship) => {
    if (scholarship.status === 'closed' || scholarship.status === 'inactive') {
      return <Badge variant="secondary">Closed</Badge>
    }
    
    if (scholarship.max_applicants) {
      const spotsLeft = scholarship.max_applicants - scholarship.current_applicants
      if (spotsLeft <= 5) {
        return <Badge variant="destructive">Few Spots Left</Badge>
      }
    }
    
    if (scholarship.is_featured) {
      return <Badge variant="default" className="bg-amber-500">Featured</Badge>
    }
    
    return <Badge variant="default" className="bg-green-500">Open</Badge>
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading scholarship details...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !scholarship) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Scholarship</h3>
            <p className="text-gray-600 mb-6">{error || 'Scholarship not found'}</p>
            <div className="flex gap-4 justify-center">
              <Button onClick={() => router.back()} variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
              <Button onClick={() => fetchScholarship(params.id as string)}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const config = categoryConfig[scholarship.category]
  const IconComponent = config.icon
  const spotsLeft = scholarship.max_applicants ? scholarship.max_applicants - scholarship.current_applicants : null
  const isOpen = scholarship.status === 'active' || scholarship.status === 'open'

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <section className="py-8 bg-white dark:bg-gray-900 border-b">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center gap-4 mb-6">
            <Button 
              variant="outline" 
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Scholarships
            </Button>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-8">
            <div className="flex-1">
              <div className="flex items-start gap-4 mb-4">
                <div className={`p-3 rounded-lg ${config.bgColor}`}>
                  <IconComponent className={`h-8 w-8 ${config.textColor}`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusBadge(scholarship)}
                    <Badge variant="outline" className="rounded-full">
                      {config.title}
                    </Badge>
                    {scholarship.is_featured && (
                      <Badge variant="outline" className="text-amber-600 border-amber-200">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {scholarship.title}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-lg">
                    {scholarship.description}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="lg:w-80">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Award Amount</p>
                      <p className="font-semibold text-green-600">
                        ₦{scholarship.amount.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Application Deadline</p>
                      <p className="font-semibold">
                        {formatDeadline(scholarship.application_deadline)}
                      </p>
                    </div>
                  </div>
                  
                  {spotsLeft !== null && (
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="text-sm text-gray-600">Applicants</p>
                        <p className="font-semibold">
                          {scholarship.current_applicants}/{scholarship.max_applicants}
                        </p>
                      </div>
                    </div>
                  )}

                  <hr className="my-4" />

                  <Button
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                    disabled={!isOpen || (spotsLeft !== null && spotsLeft <= 0)}
                    size="lg"
                    onClick={() => {
                      // Redirect to Laravel login page using constants
                      window.location.href = `${BACKEND_BASE_URL}/login`
                    }}
                  >
                    {!isOpen ? 'Application Closed' : (spotsLeft !== null && spotsLeft <= 0) ? 'No Spots Available' : 'Apply Now'}
                    {isOpen && (spotsLeft === null || spotsLeft > 0) && <ArrowRight className="ml-2 h-5 w-5" />}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">

              {/* Category Instructions */}
              {scholarship.category_instructions && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Application Instructions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        {scholarship.category_instructions.title}
                      </h4>
                      <p className="text-blue-800 dark:text-blue-200 text-sm mb-2">
                        {scholarship.category_instructions.age_range}
                      </p>
                      <p className="text-blue-700 dark:text-blue-300">
                        <strong>Who fills this form:</strong> {scholarship.category_instructions.filled_by}
                      </p>
                      <p className="text-blue-700 dark:text-blue-300 mt-2">
                        {scholarship.category_instructions.instruction}
                      </p>
                    </div>

                    <div>
                      <h5 className="font-semibold mb-2">Required Information:</h5>
                      <ul className="space-y-1">
                        {scholarship.category_instructions.required_info && Array.isArray(scholarship.category_instructions.required_info) && scholarship.category_instructions.required_info.map((info, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            {info}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Requirements */}
              {(scholarship.eligibility_criteria || scholarship.requirements) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      Eligibility & Requirements
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 dark:text-gray-300">
                      {scholarship.eligibility_criteria || scholarship.requirements}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Custom Fields Preview */}
              {scholarship.custom_fields && scholarship.custom_fields.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Application Form Fields
                    </CardTitle>
                    <CardDescription>
                      Preview of the information you'll need to provide
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {scholarship.custom_fields && Array.isArray(scholarship.custom_fields) && scholarship.custom_fields.map((field, index) => (
                        <div key={field.id} className="border rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">{field.field_name}</span>
                            {field.is_required && (
                              <Badge variant="outline" className="text-xs">Required</Badge>
                            )}
                          </div>
                          <p className="text-xs text-gray-600 capitalize">
                            {field.field_type.replace('_', ' ')} field
                          </p>
                          {field.field_options && field.field_options.length > 0 && (
                            <p className="text-xs text-gray-500 mt-1">
                              Options: {field.field_options.join(', ')}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Documents Required */}
              {scholarship.documents_required && Array.isArray(scholarship.documents_required) && scholarship.documents_required.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Required Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {scholarship.documents_required.map((doc, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          {doc}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Application Instructions */}
              {scholarship.application_instructions && (
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Instructions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      {scholarship.application_instructions}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Terms & Conditions */}
              {scholarship.terms_conditions && (
                <Card>
                  <CardHeader>
                    <CardTitle>Terms & Conditions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line text-sm">
                      {scholarship.terms_conditions}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Application Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Applications Open</p>
                        <p className="text-xs text-gray-600">Now accepting applications</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Application Deadline</p>
                        <p className="text-xs text-gray-600">
                          {formatDeadline(scholarship.application_deadline)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Review Period</p>
                        <p className="text-xs text-gray-600">Applications under review</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Results Announced</p>
                        <p className="text-xs text-gray-600">Winners notified</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Need Help */}
              <Card>
                <CardHeader>
                  <CardTitle>Need Help?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Have questions about this scholarship or the application process?
                  </p>
                  <Link href="/contact">
                    <Button variant="outline" className="w-full">
                      Contact Support
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
