"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/page",{

/***/ "(app-pages-browser)/./app/dashboard/partner/page.tsx":
/*!****************************************!*\
  !*** ./app/dashboard/partner/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PartnerDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BarChart3,BookOpen,Building,Calendar,CheckCircle,Clock,FileText,GraduationCap,Handshake,MapPin,Plus,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/partner/ProposalManager */ \"(app-pages-browser)/./components/dashboard/partner/ProposalManager.tsx\");\n/* harmony import */ var _components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/partner/ResourceSharing */ \"(app-pages-browser)/./components/dashboard/partner/ResourceSharing.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PartnerDashboard() {\n    var _user_preferences, _dashboardData_overview, _dashboardData_quick_stats, _dashboardData_overview1, _dashboardData_overview2;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [partnershipApplications, setPartnershipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collaborationOpportunities, setCollaborationOpportunities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [upcomingEvents, setUpcomingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [programs, setPrograms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Scholarship-related state\n    const [availableScholarships, setAvailableScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [myStudents, setMyStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [scholarshipApplications, setScholarshipApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentStats, setStudentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Dashboard overview state\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PartnerDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"PartnerDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        // Fetch user data first\n                        const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getProfile();\n                        if (userData.success) {\n                            setUser(userData.data);\n                        }\n                        // Fetch comprehensive dashboard data\n                        try {\n                            console.log('Fetching partner dashboard data...');\n                            const dashboardResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-dashboard');\n                            console.log('Dashboard response:', dashboardResponse);\n                            if (dashboardResponse.success) {\n                                setDashboardData(dashboardResponse.data);\n                                console.log('Dashboard data set:', dashboardResponse.data);\n                                // Update student stats from dashboard data\n                                if (dashboardResponse.data.overview) {\n                                    setStudentStats({\n                                        total_students: dashboardResponse.data.overview.total_students,\n                                        active_students: dashboardResponse.data.overview.total_students,\n                                        recent_enrollments: 0,\n                                        scholarship_applications: dashboardResponse.data.overview.total_applications\n                                    });\n                                    console.log('Student stats updated from dashboard data');\n                                }\n                            } else {\n                                console.log('Dashboard response not successful:', dashboardResponse);\n                            }\n                        } catch (error) {\n                            var _error_response;\n                            console.error('Error fetching dashboard data:', error);\n                            console.error('Error details:', ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                        }\n                        // Fetch partner-specific data\n                        const [eventsResponse, programsResponse] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getUpcomingEvents(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.getPrograms()\n                        ]);\n                        const eventsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(eventsResponse);\n                        const programsData = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.extractArrayData)(programsResponse);\n                        setUpcomingEvents(eventsData.slice(0, 5));\n                        setPrograms(programsData.slice(0, 3));\n                        // Fetch scholarship-related data\n                        await fetchScholarshipData();\n                        setLoading(false);\n                    } catch (error) {\n                        console.error('Error fetching dashboard data:', error);\n                        setLoading(false);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchDashboardData\"];\n            const fetchScholarshipData = {\n                \"PartnerDashboard.useEffect.fetchScholarshipData\": async ()=>{\n                    try {\n                        console.log('Starting to fetch scholarship data...');\n                        // Fetch available scholarships for partner organizations (Primary/Secondary only)\n                        try {\n                            console.log('Fetching available scholarships...');\n                            const scholarshipsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/available');\n                            console.log('Scholarships response:', scholarshipsResponse);\n                            if (scholarshipsResponse.success) {\n                                setAvailableScholarships(scholarshipsResponse.data || []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching scholarships:', error);\n                        }\n                        // Fetch partner students\n                        try {\n                            console.log('Fetching partner students...');\n                            const studentsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students');\n                            console.log('Students response:', studentsResponse);\n                            if (studentsResponse.success) {\n                                var _studentsResponse_data;\n                                // Handle both paginated and direct array responses\n                                const studentsData = ((_studentsResponse_data = studentsResponse.data) === null || _studentsResponse_data === void 0 ? void 0 : _studentsResponse_data.data) || studentsResponse.data || [];\n                                setMyStudents(Array.isArray(studentsData) ? studentsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching students:', error);\n                        }\n                        // Fetch scholarship applications submitted by this partner\n                        try {\n                            console.log('Fetching my applications...');\n                            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-scholarships/my-applications');\n                            console.log('Applications response:', applicationsResponse);\n                            if (applicationsResponse.success) {\n                                var _applicationsResponse_data;\n                                const applicationsData = ((_applicationsResponse_data = applicationsResponse.data) === null || _applicationsResponse_data === void 0 ? void 0 : _applicationsResponse_data.data) || applicationsResponse.data || [];\n                                setScholarshipApplications(Array.isArray(applicationsData) ? applicationsData : []);\n                            }\n                        } catch (error) {\n                            console.error('Error fetching applications:', error);\n                        }\n                        // Fetch student statistics\n                        try {\n                            console.log('Fetching student statistics...');\n                            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get('/partner-students/statistics');\n                            console.log('Stats response:', statsResponse);\n                            if (statsResponse.success) {\n                                setStudentStats(statsResponse.data || {});\n                            }\n                        } catch (error) {\n                            console.error('Error fetching stats:', error);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching scholarship data:', error);\n                    }\n                }\n            }[\"PartnerDashboard.useEffect.fetchScholarshipData\"];\n            fetchDashboardData();\n        }\n    }[\"PartnerDashboard.useEffect\"], []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 199,\n            columnNumber: 12\n        }, this);\n    }\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'approved':\n                return 'bg-green-500';\n            case 'rejected':\n                return 'bg-red-500';\n            case 'pending':\n                return 'bg-yellow-500';\n            case 'under_review':\n                return 'bg-blue-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const partnerInfo = (user === null || user === void 0 ? void 0 : (_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.partner_data) || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    partnerInfo.organization_name || (user === null || user === void 0 ? void 0 : user.first_name),\n                                                    \"!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Partnership Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.organization_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: [\n                                                    partnerInfo.organization_type,\n                                                    \" • \",\n                                                    partnerInfo.sector\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/opportunities\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Opportunities\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                            href: \"/partnerships/apply\",\n                                            children: \"Apply for Partnership\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"My Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview = dashboardData.overview) === null || _dashboardData_overview === void 0 ? void 0 : _dashboardData_overview.total_students) || (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Enrolled students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Available Scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_quick_stats = dashboardData.quick_stats) === null || _dashboardData_quick_stats === void 0 ? void 0 : _dashboardData_quick_stats.active_scholarships) || availableScholarships.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Open for applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview1 = dashboardData.overview) === null || _dashboardData_overview1 === void 0 ? void 0 : _dashboardData_overview1.total_applications) || scholarshipApplications.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Total applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: [\n                                                (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_overview2 = dashboardData.overview) === null || _dashboardData_overview2 === void 0 ? void 0 : _dashboardData_overview2.success_rate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Application success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    defaultValue: \"scholarships\",\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"scholarships\",\n                                    children: \"Scholarships\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"students\",\n                                    children: \"My Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"applications\",\n                                    children: \"Applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"progression\",\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"opportunities\",\n                                    children: \"Opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"proposals\",\n                                    children: \"Proposals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"resources\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"profile\",\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"scholarships\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Available Scholarships\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                children: [\n                                                                    availableScholarships.length,\n                                                                    \" Available\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        availableScholarships.slice(0, 3).map((scholarship)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: scholarship.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                                                    children: scholarship.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 355,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 355,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 inline mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                                    lineNumber: 358,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \"Deadline: \",\n                                                                                                new Date(scholarship.application_deadline).toLocaleDateString()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 358,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-3 flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: scholarship.category === 'primary' ? 'default' : 'secondary',\n                                                                                            children: scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 363,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium text-green-600\",\n                                                                                            children: [\n                                                                                                \"₦\",\n                                                                                                scholarship.amount ? Number(scholarship.amount).toLocaleString() : 'Amount varies'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 366,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 362,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarship-application?scholarship_id=\".concat(scholarship.id, \"&category=\").concat(scholarship.category),\n                                                                                    children: \"Apply for Student\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                    href: \"/scholarships/\".concat(scholarship.id),\n                                                                                    children: \"View Details\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, scholarship.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        availableScholarships.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"No scholarships available at the moment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-2\",\n                                                                    children: \"Check back later for new opportunities\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        availableScholarships.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                \"View All Scholarships (\",\n                                                                availableScholarships.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Quick Actions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Add New Student\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 416,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Register a new student to your school\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 417,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Manage Students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 428,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View and manage all your students\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 429,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Browse Scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"Explore all available scholarships\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 441,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"justify-start h-auto p-4\",\n                                                                variant: \"outline\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/analytics\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-left\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Analytics & Reports\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 452,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: \"View performance insights and statistics\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 453,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                dashboardData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        dashboardData.recent_activities && dashboardData.recent_activities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Recent Activities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-3\",\n                                                    children: dashboardData.recent_activities.slice(0, 5).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-3 p-2 rounded-lg bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: activity.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(activity.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: activity.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this),\n                                        dashboardData.upcoming_deadlines && dashboardData.upcoming_deadlines.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Upcoming Deadlines\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-3\",\n                                                    children: dashboardData.upcoming_deadlines.slice(0, 5).map((deadline, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-2 rounded-lg bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: deadline.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-600\",\n                                                                            children: deadline.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-yellow-600\",\n                                                                            children: new Date(deadline.deadline).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                deadline.days_remaining,\n                                                                                \" days left\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Recent Scholarship Applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: [\n                                                            scholarshipApplications.length,\n                                                            \" Total\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    scholarshipApplications.slice(0, 5).map((application)=>{\n                                                        var _application_student, _application_scholarship, _application_scholarship1, _application_scholarship2, _application_status, _application_status1;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'Student Name'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.category) || application.scholarship_category || 'Category'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.title) || application.scholarship_title || 'Scholarship Title'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Applied: \",\n                                                                                        new Date(application.created_at).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.amount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Amount: ₦\",\n                                                                                        Number(application.scholarship.amount).toLocaleString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                            children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: \"View\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No scholarship applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Start applying for scholarships on behalf of your students\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/scholarships\",\n                                                                    children: \"Browse Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"students\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"View All Students\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        href: \"/dashboard/partner/students/add\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add New Student\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.total_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.active_students) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Active Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.recent_enrollments) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Recent Enrollments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: (studentStats === null || studentStats === void 0 ? void 0 : studentStats.scholarship_applications) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"My Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    myStudents.slice(0, 10).map((student)=>{\n                                                        var _student_progressions_, _student_progressions;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: student.full_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Age: \",\n                                                                                        student.age || 'N/A'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 640,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Grade: \",\n                                                                                        ((_student_progressions = student.progressions) === null || _student_progressions === void 0 ? void 0 : (_student_progressions_ = _student_progressions[0]) === null || _student_progressions_ === void 0 ? void 0 : _student_progressions_.grade_level) || 'Not set'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \"Status: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            variant: \"outline\",\n                                                                                            className: \"text-xs ml-1\",\n                                                                                            children: student.status || 'Active'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        student.student_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                student.student_id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            children: \"View Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                href: \"/scholarship-application?student_id=\".concat(student.id),\n                                                                                children: \"Apply for Scholarship\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, student.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    }),\n                                                    myStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No students registered yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-4\",\n                                                                children: \"Add students to start managing their scholarship applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/students/add\",\n                                                                    children: \"Add Your First Student\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Collaboration Opportunities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        collaborationOpportunities.slice(0, 3).map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: opportunity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: opportunity.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mt-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 693,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.location\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"h-3 w-3 inline mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    opportunity.timeline\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: opportunity.type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                                children: opportunity.budget_range\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, opportunity.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/partnerships/opportunities\",\n                                                                children: \"View All Opportunities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Available Programs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        programs.map((program)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 border rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: program.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: program.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                children: program.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 722,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: \"Learn More\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, program.id, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, this)),\n                                                        programs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-center py-4\",\n                                                            children: \"No programs available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Partnership Applications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    partnershipApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: application.program\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"Submitted: \",\n                                                                                new Date(application.submitted_at).toLocaleDateString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getStatusColor(application.status),\n                                                                    children: application.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, application.id, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    partnershipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No partnership applications yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/partnerships/apply\",\n                                                                    children: \"Submit Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"opportunities\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Partnership Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Explore collaboration opportunities that align with your organization's mission\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: collaborationOpportunities.map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: opportunity.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Apply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: opportunity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Location: \",\n                                                                        opportunity.location\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Timeline: \",\n                                                                        opportunity.timeline\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 791,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Type: \",\n                                                                        opportunity.type\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 28\n                                                                        }, this),\n                                                                        \"Budget: \",\n                                                                        opportunity.budget_range\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, opportunity.id, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"proposals\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ProposalManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 802,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"resources\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_partner_ResourceSharing__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"applications\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Scholarship Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track scholarship applications submitted on behalf of your students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                scholarshipApplications.map((application)=>{\n                                                    var _application_scholarship, _application_status, _application_status1, _application_student, _application_scholarship1, _application_scholarship2, _application_scholarship3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-4 hover:shadow-sm transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: ((_application_scholarship = application.scholarship) === null || _application_scholarship === void 0 ? void 0 : _application_scholarship.title) || 'Scholarship Application'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"text-white \".concat(getStatusColor(application.status)),\n                                                                        children: ((_application_status = application.status) === null || _application_status === void 0 ? void 0 : _application_status.charAt(0).toUpperCase()) + ((_application_status1 = application.status) === null || _application_status1 === void 0 ? void 0 : _application_status1.slice(1)) || 'Pending'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Student:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 828,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_student = application.student) === null || _application_student === void 0 ? void 0 : _application_student.full_name) || application.student_name || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Amount:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 829,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" ₦\",\n                                                                            ((_application_scholarship1 = application.scholarship) === null || _application_scholarship1 === void 0 ? void 0 : _application_scholarship1.amount) ? Number(application.scholarship.amount).toLocaleString() : 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Category:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            ((_application_scholarship2 = application.scholarship) === null || _application_scholarship2 === void 0 ? void 0 : _application_scholarship2.category) || application.category || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Submitted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                                lineNumber: 831,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" \",\n                                                                            new Date(application.submitted_at || application.created_at).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            ((_application_scholarship3 = application.scholarship) === null || _application_scholarship3 === void 0 ? void 0 : _application_scholarship3.application_deadline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Deadline:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" \",\n                                                                    new Date(application.scholarship.application_deadline).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"View Details\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    application.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: \"Edit Application\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    application.status === 'approved' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓ Approved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, application.id, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                scholarshipApplications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No scholarship applications submitted yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-4\",\n                                                            children: \"Start applying for scholarships on behalf of your students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                href: \"/scholarships\",\n                                                                children: \"Browse Available Scholarships\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"progression\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Academic Progression Tracking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                            href: \"/dashboard/partner/progression\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"View All Progressions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Track and manage student academic progression through grade levels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-8 w-8 text-yellow-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pending Approval\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 891,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"Recent Progressions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                    href: \"/dashboard/partner/progression/create\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Create Progression\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BarChart3_BookOpen_Building_Calendar_CheckCircle_Clock_FileText_GraduationCap_Handshake_MapPin_Plus_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-2\",\n                                                                children: \"No progressions found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Start tracking student academic progression\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"profile\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Manage your organization's information and partnership preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_name || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 933,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.organization_type || 'Not specified'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Contact Person\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    user === null || user === void 0 ? void 0 : user.first_name,\n                                                                    \" \",\n                                                                    user === null || user === void 0 ? void 0 : user.last_name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    partnerInfo.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Website\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.website\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.sector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Sector\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.sector\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Organization Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.size\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    partnerInfo.annual_budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Annual Budget\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: partnerInfo.annual_budget\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, this),\n                                            partnerInfo.partnership_interests && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Partnership Interests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: partnerInfo.partnership_interests\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: \"Edit Organization Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\partner\\\\page.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(PartnerDashboard, \"Bk3UO5XMfRUlkQk2ciyYz20OBnE=\");\n_c = PartnerDashboard;\nvar _c;\n$RefreshReg$(_c, \"PartnerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/partner/page.tsx\n"));

/***/ })

});