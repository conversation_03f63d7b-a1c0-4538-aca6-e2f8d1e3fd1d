@extends('layouts.admin')

@section('title', 'API Documentation - Admin Panel')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">API Documentation</h1>
        <p class="page-subtitle">Complete reference for HLTKKQ Foundation API endpoints</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="testAllEndpoints()">
            <i class="fas fa-play me-2"></i>Test All Endpoints
        </button>
        <button class="btn btn-success" onclick="exportPostmanCollection()">
            <i class="fas fa-download me-2"></i>Export Postman Collection
        </button>
    </div>
</div>

<!-- API Status and Base URL -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">API Status</h5>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <span class="badge bg-success fs-6" id="apiStatus">
                            <i class="fas fa-circle me-1" style="font-size: 8px;"></i>
                            Online
                        </span>
                    </div>
                    <div>
                        <small class="text-muted">Last checked: <span id="lastChecked">--</span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Base URL</h5>
                <div class="input-group">
                    <input type="text" class="form-control" value="{{ config('app.url') }}/api/v1" readonly>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('{{ config('app.url') }}/api/v1')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Token -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Authentication Token</h5>
                <p class="text-muted">Your admin authentication token for API requests:</p>
                <div class="input-group">
                    <input type="password" class="form-control" value="{{ session('admin_token') }}" readonly id="tokenInput">
                    <button class="btn btn-outline-secondary" onclick="toggleTokenVisibility()">
                        <i class="fas fa-eye" id="tokenIcon"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('{{ session('admin_token') }}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <small class="text-muted">Include this token in the Authorization header: <code>Bearer {{ session('admin_token') }}</code></small>
            </div>
        </div>
    </div>
</div>

<!-- API Endpoints -->
@foreach($endpoints as $category => $categoryEndpoints)
<div class="card mb-4">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="fas fa-{{ $category === 'Authentication' ? 'lock' : ($category === 'Users' ? 'users' : ($category === 'Programs' ? 'project-diagram' : ($category === 'Events' ? 'calendar' : ($category === 'Blog' ? 'blog' : ($category === 'Donations' ? 'heart' : ($category === 'Volunteers' ? 'hands-helping' : ($category === 'Scholarships' ? 'graduation-cap' : 'cog'))))))) }} me-2"></i>
            {{ $category }}
        </h4>
    </div>
    <div class="card-body">
        @foreach($categoryEndpoints as $endpoint => $description)
        @php
            $method = explode(' ', $endpoint)[0];
            $url = explode(' ', $endpoint)[1];
            $methodColor = $method === 'GET' ? 'success' : ($method === 'POST' ? 'primary' : ($method === 'PUT' ? 'warning' : ($method === 'DELETE' ? 'danger' : 'secondary')));
        @endphp
        <div class="endpoint-item mb-3 p-3 border rounded">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-{{ $methodColor }} me-2">{{ $method }}</span>
                        <code class="text-dark">{{ $url }}</code>
                    </div>
                    <p class="text-muted mb-2">{{ $description }}</p>
                    
                    <!-- Request Parameters Section -->
                    @if(in_array($method, ['POST', 'PUT', 'PATCH']))
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#params-{{ md5($endpoint) }}">
                            <i class="fas fa-code me-1"></i>Request Body
                        </button>
                        <div class="collapse mt-2" id="params-{{ md5($endpoint) }}">
                            <div class="bg-light p-3 rounded">
                                <small class="text-muted">Example request body (JSON):</small>
                                <pre class="mt-2 mb-0"><code class="language-json">{{ json_encode(getExampleRequestBody($endpoint), JSON_PRETTY_PRINT) }}</code></pre>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="ms-3">
                    <button class="btn btn-sm btn-primary" onclick="testEndpoint('{{ $method }}', '{{ $url }}', '{{ $description }}')">
                        <i class="fas fa-play me-1"></i>Test
                    </button>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endforeach

<!-- API Testing Modal -->
<div class="modal fade" id="apiTestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API Endpoint Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">Method</label>
                        <input type="text" class="form-control" id="testMethod" readonly>
                    </div>
                    <div class="col-md-9">
                        <label class="form-label">URL</label>
                        <input type="text" class="form-control" id="testUrl" readonly>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <input type="text" class="form-control" id="testDescription" readonly>
                </div>
                
                <div class="mb-3" id="requestBodySection" style="display: none;">
                    <label class="form-label">Request Body (JSON)</label>
                    <textarea class="form-control" id="requestBody" rows="8" placeholder="Enter JSON request body..."></textarea>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="executeTest()">
                        <i class="fas fa-play me-2"></i>Execute Request
                    </button>
                    <button class="btn btn-secondary" onclick="clearTestResults()">
                        <i class="fas fa-trash me-2"></i>Clear Results
                    </button>
                </div>
                
                <div id="testResults" style="display: none;">
                    <h6>Response:</h6>
                    <div class="border rounded p-3 bg-light">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Status: <span id="responseStatus"></span></span>
                            <span>Time: <span id="responseTime"></span>ms</span>
                        </div>
                        <pre id="responseBody" class="mb-0"><code></code></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Response Codes Reference -->
<div class="card">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>HTTP Response Codes
        </h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-success">Success Codes</h6>
                <ul class="list-unstyled">
                    <li><code class="text-success">200</code> - OK (Request successful)</li>
                    <li><code class="text-success">201</code> - Created (Resource created)</li>
                    <li><code class="text-success">204</code> - No Content (Successful deletion)</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6 class="text-danger">Error Codes</h6>
                <ul class="list-unstyled">
                    <li><code class="text-warning">400</code> - Bad Request (Invalid data)</li>
                    <li><code class="text-warning">401</code> - Unauthorized (Invalid token)</li>
                    <li><code class="text-warning">403</code> - Forbidden (Insufficient permissions)</li>
                    <li><code class="text-danger">404</code> - Not Found (Resource not found)</li>
                    <li><code class="text-danger">422</code> - Validation Error (Invalid input)</li>
                    <li><code class="text-danger">500</code> - Server Error (Internal error)</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentTestModal = new bootstrap.Modal(document.getElementById('apiTestModal'));

// Check API status on load
function checkApiStatus() {
    const startTime = Date.now();
    fetch('/api/v1/status', {
        headers: {
            'Authorization': `Bearer ${window.apiToken}`,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        const responseTime = Date.now() - startTime;
        const statusBadge = document.getElementById('apiStatus');
        
        if (response.ok) {
            statusBadge.innerHTML = '<i class="fas fa-circle me-1" style="font-size: 8px;"></i>Online';
            statusBadge.className = 'badge bg-success fs-6';
        } else {
            statusBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Issues';
            statusBadge.className = 'badge bg-warning fs-6';
        }
        
        document.getElementById('lastChecked').textContent = new Date().toLocaleTimeString();
    })
    .catch(error => {
        const statusBadge = document.getElementById('apiStatus');
        statusBadge.innerHTML = '<i class="fas fa-times-circle me-1"></i>Offline';
        statusBadge.className = 'badge bg-danger fs-6';
        document.getElementById('lastChecked').textContent = new Date().toLocaleTimeString();
    });
}

// Toggle token visibility
function toggleTokenVisibility() {
    const tokenInput = document.getElementById('tokenInput');
    const tokenIcon = document.getElementById('tokenIcon');
    
    if (tokenInput.type === 'password') {
        tokenInput.type = 'text';
        tokenIcon.className = 'fas fa-eye-slash';
    } else {
        tokenInput.type = 'password';
        tokenIcon.className = 'fas fa-eye';
    }
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show toast notification
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.textContent = 'Copied to clipboard!';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 9999;
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
    });
}

// Test endpoint
function testEndpoint(method, url, description) {
    document.getElementById('testMethod').value = method;
    document.getElementById('testUrl').value = url;
    document.getElementById('testDescription').value = description;
    
    // Show request body section for POST, PUT, PATCH
    const requestBodySection = document.getElementById('requestBodySection');
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
        requestBodySection.style.display = 'block';
        // Pre-fill with example data
        document.getElementById('requestBody').value = JSON.stringify(getExampleRequestBodyForEndpoint(url), null, 2);
    } else {
        requestBodySection.style.display = 'none';
    }
    
    // Clear previous results
    clearTestResults();
    
    currentTestModal.show();
}

// Execute test
function executeTest() {
    const method = document.getElementById('testMethod').value;
    const url = document.getElementById('testUrl').value;
    let requestBody = document.getElementById('requestBody').value;
    
    const startTime = Date.now();
    
    const headers = {
        'Authorization': `Bearer ${window.apiToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    };
    
    const config = {
        method: method,
        headers: headers
    };
    
    if (requestBody && ['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
            JSON.parse(requestBody); // Validate JSON
            config.body = requestBody;
        } catch (e) {
            alert('Invalid JSON in request body');
            return;
        }
    }
    
    fetch(`{{ config('app.url') }}/api/v1${url}`, config)
    .then(response => {
        const responseTime = Date.now() - startTime;
        
        // Show results section
        document.getElementById('testResults').style.display = 'block';
        document.getElementById('responseStatus').textContent = `${response.status} ${response.statusText}`;
        document.getElementById('responseTime').textContent = responseTime;
        
        return response.json().then(data => ({ status: response.status, data }));
    })
    .then(({ status, data }) => {
        document.getElementById('responseBody').textContent = JSON.stringify(data, null, 2);
        
        // Apply syntax highlighting if available
        if (typeof hljs !== 'undefined') {
            hljs.highlightElement(document.getElementById('responseBody'));
        }
    })
    .catch(error => {
        document.getElementById('testResults').style.display = 'block';
        document.getElementById('responseStatus').textContent = 'Error';
        document.getElementById('responseTime').textContent = '--';
        document.getElementById('responseBody').textContent = error.message;
    });
}

// Clear test results
function clearTestResults() {
    document.getElementById('testResults').style.display = 'none';
    document.getElementById('responseStatus').textContent = '';
    document.getElementById('responseTime').textContent = '';
    document.getElementById('responseBody').textContent = '';
}

// Get example request body for endpoint
function getExampleRequestBodyForEndpoint(url) {
    const examples = {
        '/login': {
            email: '<EMAIL>',
            password: 'password'
        },
        '/register': {
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            password: 'password',
            password_confirmation: 'password'
        },
        '/contact': {
            name: 'John Doe',
            email: '<EMAIL>',
            subject: 'General Inquiry',
            message: 'Hello, I would like to know more about your programs.'
        },
        '/donations': {
            amount: 10000,
            donor_name: 'John Doe',
            donor_email: '<EMAIL>',
            donor_phone: '+2348012345678',
            purpose: 'Education Program'
        },
        '/volunteer/apply': {
            skills: 'Teaching, Project Management',
            experience: 'I have 5 years of experience in education.',
            availability: 'weekends',
            motivation: 'I want to help improve education in Nigeria.'
        }
    };
    
    return examples[url] || { message: 'Example request body' };
}

// Test all endpoints
function testAllEndpoints() {
    if (confirm('This will test all GET endpoints. Continue?')) {
        const getEndpoints = [
            '/programs',
            '/programs/featured',
            '/events',
            '/blog/posts',
            '/blog/categories',
            '/scholarships',
            '/volunteer/opportunities',
            '/contact/faq'
        ];
        
        let completed = 0;
        const total = getEndpoints.length;
        
        getEndpoints.forEach((endpoint, index) => {
            setTimeout(() => {
                fetch(`{{ config('app.url') }}/api/v1${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${window.apiToken}`,
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    console.log(`${endpoint}: ${response.status}`);
                    completed++;
                    if (completed === total) {
                        alert('All endpoint tests completed. Check console for results.');
                    }
                })
                .catch(error => {
                    console.error(`${endpoint}: Error - ${error.message}`);
                    completed++;
                    if (completed === total) {
                        alert('All endpoint tests completed. Check console for results.');
                    }
                });
            }, index * 500); // Stagger requests
        });
    }
}

// Export Postman collection
function exportPostmanCollection() {
    const collection = {
        info: {
            name: 'HLTKKQ Foundation API',
            description: 'Complete API collection for HLTKKQ Foundation',
            version: '1.0.0'
        },
        auth: {
            type: 'bearer',
            bearer: {
                token: '{{ session("admin_token") }}'
            }
        },
        variable: [
            {
                key: 'baseUrl',
                value: '{{ config("app.url") }}/api/v1'
            }
        ],
        item: []
    };
    
    // Add endpoints to collection
    @foreach($endpoints as $category => $categoryEndpoints)
        const {{ strtolower($category) }}Folder = {
            name: '{{ $category }}',
            item: []
        };
        
        @foreach($categoryEndpoints as $endpoint => $description)
        @php
            $method = explode(' ', $endpoint)[0];
            $url = explode(' ', $endpoint)[1];
        @endphp
        {{ strtolower($category) }}Folder.item.push({
            name: '{{ $description }}',
            request: {
                method: '{{ $method }}',
                header: [
                    {
                        key: 'Accept',
                        value: 'application/json'
                    },
                    {
                        key: 'Content-Type',
                        value: 'application/json'
                    }
                ],
                url: {
                    raw: '@{{baseUrl}}{{ $url }}',
                    host: ['@{{baseUrl}}'],
                    path: '{{ ltrim($url, "/") }}'.split('/')
                }
                @if(in_array($method, ['POST', 'PUT', 'PATCH']))
                ,
                body: {
                    mode: 'raw',
                    raw: JSON.stringify(getExampleRequestBodyForEndpoint('{{ $url }}'), null, 2)
                }
                @endif
            }
        });
        @endforeach
        
        collection.item.push({{ strtolower($category) }}Folder);
    @endforeach
    
    // Download collection
    const blob = new Blob([JSON.stringify(collection, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'HLTKKQ_Foundation_API.postman_collection.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    checkApiStatus();
    setInterval(checkApiStatus, 60000); // Check every minute
});
</script>
@endpush

@php
function getExampleRequestBody($endpoint) {
    $examples = [
        'POST /api/v1/login' => [
            'email' => '<EMAIL>',
            'password' => 'password'
        ],
        'POST /api/v1/register' => [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password'
        ],
        'POST /api/v1/contact' => [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'General Inquiry',
            'message' => 'Hello, I would like to know more about your programs.'
        ],
        'POST /api/v1/donations' => [
            'amount' => 10000,
            'donor_name' => 'John Doe',
            'donor_email' => '<EMAIL>',
            'donor_phone' => '+2348012345678',
            'purpose' => 'Education Program'
        ],
        'POST /api/v1/volunteer/apply' => [
            'skills' => 'Teaching, Project Management',
            'experience' => 'I have 5 years of experience in education.',
            'availability' => 'weekends',
            'motivation' => 'I want to help improve education in Nigeria.'
        ]
    ];
    
    return $examples[$endpoint] ?? ['message' => 'Example request body'];
}
@endphp 