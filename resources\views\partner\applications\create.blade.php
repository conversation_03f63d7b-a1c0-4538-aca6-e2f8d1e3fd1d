@extends('layouts.dashboard')

@section('title', 'Submit New Application')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications', 'active' => true],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Submit New Application</h1>
                            <p class="text-gray-600">Apply for a scholarship on behalf of your student</p>
                        </div>
                        <div class="flex gap-3">
                            <a href="{{ route('partner.applications.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Applications
                            </a>
                        </div>
                    </div>
                </div>

                @if($students->isEmpty())
                    <!-- No Students Message -->
                    <div class="bg-white rounded-2xl shadow-lg p-12 text-center">
                        <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Students</h3>
                        <p class="text-gray-500 mb-6">You need to add students before you can submit scholarship applications.</p>
                        <a href="{{ route('partner.students.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                            <i class="fas fa-plus mr-2"></i>Add Student
                        </a>
                    </div>
                @elseif($scholarships->isEmpty())
                    <!-- No Scholarships Message -->
                    <div class="bg-white rounded-2xl shadow-lg p-12 text-center">
                        <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Scholarships</h3>
                        <p class="text-gray-500 mb-6">There are currently no open scholarships available for application.</p>
                        <a href="{{ route('scholarships') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                            <i class="fas fa-search mr-2"></i>Browse Scholarships
                        </a>
                    </div>
                @else
                    <!-- Application Form -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Application Details</h2>
                            <p class="text-gray-600 mt-1">Fill out the application form for your student</p>
                        </div>

                        <form action="{{ route('partner.applications.store') }}" method="POST" enctype="multipart/form-data" class="p-6">
                            @csrf

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Scholarship Selection -->
                                <div class="lg:col-span-2">
                                    <label for="scholarship_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Select Scholarship <span class="text-red-500">*</span>
                                    </label>
                                    <select id="scholarship_id" 
                                            name="scholarship_id" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('scholarship_id') border-red-500 @enderror"
                                            required>
                                        <option value="">Choose a scholarship</option>
                                        @foreach($scholarships as $scholarship)
                                            <option value="{{ $scholarship->id }}" 
                                                    {{ old('scholarship_id', $selectedScholarship?->id) == $scholarship->id ? 'selected' : '' }}
                                                    data-category="{{ $scholarship->category }}"
                                                    data-amount="{{ $scholarship->amount }}"
                                                    data-deadline="{{ $scholarship->application_deadline }}">
                                                {{ $scholarship->title }} ({{ ucfirst($scholarship->category) }}) - ₦{{ number_format($scholarship->amount) }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('scholarship_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Student Selection -->
                                <div class="lg:col-span-2">
                                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Select Student <span class="text-red-500">*</span>
                                    </label>
                                    <select id="student_id" 
                                            name="student_id" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('student_id') border-red-500 @enderror"
                                            required>
                                        <option value="">Choose a student</option>
                                        @foreach($students as $student)
                                            <option value="{{ $student->id }}" {{ old('student_id') == $student->id ? 'selected' : '' }}>
                                                {{ $student->name }} ({{ $student->class }}) - Age {{ $student->age }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('student_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Guardian Information -->
                                <div class="lg:col-span-2">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">Guardian Information</h3>
                                </div>

                                <!-- Guardian Name -->
                                <div>
                                    <label for="guardian_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian/Parent Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           id="guardian_name" 
                                           name="guardian_name" 
                                           value="{{ old('guardian_name') }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('guardian_name') border-red-500 @enderror"
                                           placeholder="Enter guardian's full name"
                                           required>
                                    @error('guardian_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Guardian Phone -->
                                <div>
                                    <label for="guardian_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Phone Number <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" 
                                           id="guardian_phone" 
                                           name="guardian_phone" 
                                           value="{{ old('guardian_phone') }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('guardian_phone') border-red-500 @enderror"
                                           placeholder="e.g., +234 ************"
                                           required>
                                    @error('guardian_phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Guardian Email -->
                                <div class="lg:col-span-2">
                                    <label for="guardian_email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Email Address (Optional)
                                    </label>
                                    <input type="email" 
                                           id="guardian_email" 
                                           name="guardian_email" 
                                           value="{{ old('guardian_email') }}"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('guardian_email') border-red-500 @enderror"
                                           placeholder="<EMAIL>">
                                    @error('guardian_email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Reason for Application -->
                                <div class="lg:col-span-2">
                                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                                        Reason for Application <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="reason" 
                                              name="reason" 
                                              rows="4"
                                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('reason') border-red-500 @enderror"
                                              placeholder="Explain why this student deserves the scholarship (minimum 50 characters)"
                                              required>{{ old('reason') }}</textarea>
                                    <p class="mt-1 text-sm text-gray-500">Minimum 50 characters required</p>
                                    @error('reason')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Supporting Documents -->
                                <div class="lg:col-span-2">
                                    <label for="supporting_documents" class="block text-sm font-medium text-gray-700 mb-2">
                                        Supporting Documents (Optional)
                                    </label>
                                    <input type="file" 
                                           id="supporting_documents" 
                                           name="supporting_documents" 
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 @error('supporting_documents') border-red-500 @enderror">
                                    <p class="mt-1 text-sm text-gray-500">Accepted formats: PDF, DOC, DOCX, JPG, PNG. Maximum size: 5MB</p>
                                    @error('supporting_documents')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6 mt-6 border-t border-gray-200">
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 flex-1 sm:flex-none">
                                    <i class="fas fa-paper-plane mr-2"></i>Submit Application
                                </button>
                                <a href="{{ route('partner.applications.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-xl font-semibold transition-colors duration-300 text-center flex-1 sm:flex-none">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    if (mobileMenuBtn && mobileSidebar && mobileSidebarOverlay && mobileSidebarContent) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileSidebar.classList.remove('hidden');
            setTimeout(() => {
                mobileSidebarContent.classList.remove('-translate-x-full');
            }, 10);
        });

        mobileSidebarOverlay.addEventListener('click', function() {
            mobileSidebarContent.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileSidebar.classList.add('hidden');
            }, 300);
        });
    }

    // Character counter for reason textarea
    const reasonTextarea = document.getElementById('reason');
    if (reasonTextarea) {
        reasonTextarea.addEventListener('input', function() {
            const length = this.value.length;
            const minLength = 50;
            const parent = this.parentElement;
            let counter = parent.querySelector('.char-counter');
            
            if (!counter) {
                counter = document.createElement('p');
                counter.className = 'char-counter mt-1 text-sm';
                parent.appendChild(counter);
            }
            
            if (length < minLength) {
                counter.textContent = `${length}/${minLength} characters (${minLength - length} more needed)`;
                counter.className = 'char-counter mt-1 text-sm text-red-600';
            } else {
                counter.textContent = `${length} characters`;
                counter.className = 'char-counter mt-1 text-sm text-green-600';
            }
        });
    }
</script>
@endpush
@endsection
