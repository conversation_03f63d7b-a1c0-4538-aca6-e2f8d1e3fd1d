

<?php $__env->startSection('title', 'Blog Posts'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Blog Posts</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.blog.index')); ?>">Blog</a></li>
                        <li class="breadcrumb-item active">Posts</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-newspaper me-2"></i>All Blog Posts
                    </h6>
                </div>
                <div class="col-auto">
                    <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>New Post
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="card-body border-bottom">
            <form method="GET" action="<?php echo e(route('admin.blog.posts')); ?>" class="row g-3 align-items-center">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control form-control-sm" placeholder="Search posts..." value="<?php echo e(request('search')); ?>">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select form-select-sm">
                        <option value="">All Status</option>
                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Draft</option>
                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="archived" <?php echo e(request('status') == 'archived' ? 'selected' : ''); ?>>Archived</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-select form-select-sm">
                        <option value="">All Categories</option>
                        <?php if(isset($categories)): ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-1">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if(request('search') || request('status') || request('category')): ?>
                            <a href="<?php echo e(route('admin.blog.posts')); ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body">
            <?php if(isset($posts) && $posts->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%"></th>
                                <th width="40%">Title</th>
                                <th width="15%">Author</th>
                                <th width="15%">Category</th>
                                <th width="10%">Status</th>
                                <th width="10%">Date</th>
                                <th width="5%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php if($post->featured_image): ?>
                                            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" class="img-thumbnail" width="50" height="50" alt="Featured Image">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($post->title); ?></strong>
                                            <?php if($post->excerpt): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($post->excerpt, 80)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($post->author): ?>
                                            <div class="d-flex align-items-center">
                                                <?php if($post->author->profile_picture): ?>
                                                    <img src="<?php echo e(asset('storage/' . $post->author->profile_picture)); ?>" class="rounded-circle me-2" width="24" height="24" alt="Author">
                                                <?php else: ?>
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px;">
                                                        <i class="fas fa-user text-white" style="font-size: 10px;"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <small><?php echo e($post->author->first_name); ?> <?php echo e($post->author->last_name); ?></small>
                                            </div>
                                        <?php else: ?>
                                            <small class="text-muted">Unknown Author</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($post->category): ?>
                                            <span class="badge rounded-pill" style="background-color: <?php echo e($post->category->color ?? '#6c757d'); ?>; color: white;">
                                                <?php echo e($post->category->name); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Uncategorized</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($post->status == 'published'): ?>
                                            <span class="badge bg-success">Published</span>
                                        <?php elseif($post->status == 'draft'): ?>
                                            <span class="badge bg-warning">Draft</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Archived</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($post->published_at): ?>
                                            <small><?php echo e($post->published_at->format('M d, Y')); ?></small>
                                        <?php else: ?>
                                            <small class="text-muted"><?php echo e($post->created_at->format('M d, Y')); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="<?php echo e(route('admin.blog.posts.edit', $post->id)); ?>">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a>
                                                </li>
                                                <?php if($post->status == 'published'): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="/blog/<?php echo e($post->slug); ?>" target="_blank">
                                                            <i class="fas fa-eye me-2"></i>View Post
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <?php if($post->status != 'published'): ?>
                                                    <li>
                                                        <form action="<?php echo e(route('admin.blog.posts.status.form', $post->id)); ?>" method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('PUT'); ?>
                                                            <input type="hidden" name="status" value="published">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-check text-success me-2"></i>Publish
                                                            </button>
                                                        </form>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if($post->status != 'draft'): ?>
                                                    <li>
                                                        <form action="<?php echo e(route('admin.blog.posts.status.form', $post->id)); ?>" method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('PUT'); ?>
                                                            <input type="hidden" name="status" value="draft">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-edit text-warning me-2"></i>Move to Draft
                                                            </button>
                                                        </form>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if($post->status != 'archived'): ?>
                                                    <li>
                                                        <form action="<?php echo e(route('admin.blog.posts.status.form', $post->id)); ?>" method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('PUT'); ?>
                                                            <input type="hidden" name="status" value="archived">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-archive text-info me-2"></i>Archive
                                                            </button>
                                                        </form>
                                                    </li>
                                                <?php endif; ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form action="<?php echo e(route('admin.blog.posts.destroy.form', $post->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="small text-muted">
                            Showing <?php echo e($posts->firstItem()); ?> to <?php echo e($posts->lastItem()); ?> of <?php echo e($posts->total()); ?> posts
                        </p>
                    </div>
                    <div>
                        <?php echo e($posts->appends(request()->query())->links()); ?>

                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No blog posts found</h5>
                    <?php if(request('search') || request('status') || request('category')): ?>
                        <p class="text-muted">Try adjusting your search criteria or filters.</p>
                        <a href="<?php echo e(route('admin.blog.posts')); ?>" class="btn btn-secondary">Clear Filters</a>
                    <?php else: ?>
                        <p class="text-muted">Create your first blog post to get started!</p>
                        <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create First Post
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/blog/posts.blade.php ENDPATH**/ ?>