@extends('layouts.admin')

@section('title', 'Blog Comments')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Blog Comments</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">Blog</a></li>
                        <li class="breadcrumb-item active">Comments</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="card shadow">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comments me-2"></i>All Comments
                    </h6>
                </div>
                <div class="col-auto">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ route('admin.blog.comments') }}" class="d-flex gap-2">
                        <input type="text" name="search" class="form-control form-control-sm" placeholder="Search comments..." value="{{ request('search') }}">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                            <option value="spam" {{ request('status') == 'spam' ? 'selected' : '' }}>Spam</option>
                        </select>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        @if(request('search') || request('status'))
                            <a href="{{ route('admin.blog.comments') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i>
                            </a>
                        @endif
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($comments->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th width="15%">User</th>
                                <th width="30%">Comment</th>
                                <th width="20%">Post</th>
                                <th width="10%">Status</th>
                                <th width="15%">Date</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($comments as $comment)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($comment->user && $comment->user->profile_picture)
                                                <img src="{{ asset('storage/' . $comment->user->profile_picture) }}" class="rounded-circle me-2" width="32" height="32" alt="Avatar">
                                            @else
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                @if($comment->user)
                                                    <strong>{{ $comment->user->first_name }} {{ $comment->user->last_name }}</strong>
                                                    <br><small class="text-muted">{{ $comment->user->email }}</small>
                                                @else
                                                    <span class="text-muted">User Deleted</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="comment-content">
                                            @if(strlen($comment->content) > 100)
                                                <span class="comment-short">{{ Str::limit($comment->content, 100) }}</span>
                                                <span class="comment-full" style="display: none;">{{ $comment->content }}</span>
                                                <br><a href="#" class="text-primary small" onclick="toggleComment(this); return false;">Show more</a>
                                            @else
                                                {{ $comment->content }}
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($comment->post)
                                            <a href="/blog/{{ $comment->post->slug }}" target="_blank" class="text-decoration-none">
                                                <strong>{{ Str::limit($comment->post->title, 50) }}</strong>
                                            </a>
                                            <br><small class="text-muted">{{ $comment->post->created_at->format('M d, Y') }}</small>
                                        @else
                                            <span class="text-muted">Post Deleted</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($comment->status == 'approved')
                                            <span class="badge bg-success">Approved</span>
                                        @elseif($comment->status == 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($comment->status == 'rejected')
                                            <span class="badge bg-danger">Rejected</span>
                                        @else
                                            <span class="badge bg-dark">Spam</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $comment->created_at->format('M d, Y') }}</small>
                                        <br><small class="text-muted">{{ $comment->created_at->format('h:i A') }}</small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                @if($comment->status != 'approved')
                                                    <li>
                                                        <form action="{{ route('admin.blog.comments.status.form', $comment->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="approved">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-check text-success me-2"></i>Approve
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                @if($comment->status != 'rejected')
                                                    <li>
                                                        <form action="{{ route('admin.blog.comments.status.form', $comment->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="rejected">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-times text-danger me-2"></i>Reject
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                @if($comment->status != 'spam')
                                                    <li>
                                                        <form action="{{ route('admin.blog.comments.status.form', $comment->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="spam">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-flag text-warning me-2"></i>Mark as Spam
                                                            </button>
                                                        </form>
                                                    </li>
                                                @endif
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form action="{{ route('admin.blog.comments.destroy.form', $comment->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="small text-muted">
                            Showing {{ $comments->firstItem() }} to {{ $comments->lastItem() }} of {{ $comments->total() }} comments
                        </p>
                    </div>
                    <div>
                        {{ $comments->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No comments found</h5>
                    @if(request('search') || request('status'))
                        <p class="text-muted">Try adjusting your search criteria or filters.</p>
                        <a href="{{ route('admin.blog.comments') }}" class="btn btn-secondary">Clear Filters</a>
                    @else
                        <p class="text-muted">Comments will appear here once users start commenting on blog posts.</p>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function toggleComment(element) {
    const short = element.parentNode.querySelector('.comment-short');
    const full = element.parentNode.querySelector('.comment-full');
    
    if (full.style.display === 'none') {
        short.style.display = 'none';
        full.style.display = 'inline';
        element.textContent = 'Show less';
    } else {
        short.style.display = 'inline';
        full.style.display = 'none';
        element.textContent = 'Show more';
    }
}
</script>
@endsection 