"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/page",{

/***/ "(app-pages-browser)/./app/projects/page.tsx":
/*!*******************************!*\
  !*** ./app/projects/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Heart,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Heart,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Heart,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Heart,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API functions\nconst API_BASE_URL = \"https://halimakqfoundation.fnskills.ng/api/v1\" || 0;\nconst fetchStatistics = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/statistics\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchPortfolio = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/portfolio\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nfunction ProjectsPage() {\n    _s();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [portfolio, setPortfolio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [categoryScholarships, setCategoryScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCategoryResults, setShowCategoryResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            const loadData = {\n                \"ProjectsPage.useEffect.loadData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [statsData, portfolioData] = await Promise.all([\n                            fetchStatistics(),\n                            fetchPortfolio()\n                        ]);\n                        setStatistics(statsData);\n                        setPortfolio(portfolioData);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ProjectsPage.useEffect\"], []);\n    // Handle category click to show scholarships for that category\n    const handleCategoryClick = async (category)=>{\n        try {\n            setLoading(true);\n            setSelectedCategory(category);\n            // Fetch scholarships for the selected category using apiClient\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.apiClient.getPublicScholarships({\n                category\n            });\n            if (response.success) {\n                var _response_data;\n                setCategoryScholarships(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || []);\n                setShowCategoryResults(true);\n            } else {\n                setError(\"Failed to load \".concat(category, \" scholarships\"));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load \".concat(category, \" scholarships\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle back to main view\n    const handleBackToMain = ()=>{\n        setShowCategoryResults(false);\n        setSelectedCategory(null);\n        setCategoryScholarships([]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading projects...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        className: \"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2\",\n                                        children: \"Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                        children: \"Transforming Lives Through Education\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                        children: \"Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Scholarship Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Our comprehensive scholarship programs support students at every educational level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: portfolio.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"rounded-full capitalize\",\n                                                                children: category.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end pt-4 border-t\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"text-green-600 border-green-600 hover:bg-green-50\",\n                                                                onClick: ()=>handleCategoryClick(category.category),\n                                                                children: \"View Available Scholarships\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, category.category, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    showCategoryResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: handleBackToMain,\n                                                className: \"mb-4\",\n                                                children: \"← Back to Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tight mb-2\",\n                                                children: [\n                                                    selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : '',\n                                                    \" Scholarships\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: [\n                                                    \"Available scholarships for \",\n                                                    selectedCategory || 'selected',\n                                                    \" education level\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this) : categoryScholarships.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryScholarships.map((scholarship)=>{\n                                        var _scholarship_category, _scholarship_category1, _scholarship_amount;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    className: \"bg-green-600 text-white rounded-full\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full\",\n                                                                    children: ((_scholarship_category = scholarship.category) === null || _scholarship_category === void 0 ? void 0 : _scholarship_category.charAt(0).toUpperCase()) + ((_scholarship_category1 = scholarship.category) === null || _scholarship_category1 === void 0 ? void 0 : _scholarship_category1.slice(1))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: scholarship.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm line-clamp-3\",\n                                                            children: scholarship.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-green-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 269,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"₦\",\n                                                                        ((_scholarship_amount = scholarship.amount) === null || _scholarship_amount === void 0 ? void 0 : _scholarship_amount.toLocaleString()) || 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                            onClick: ()=>{\n                                                                const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                                window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                                            },\n                                                            children: \"Apply Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, scholarship.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"No scholarships available for \",\n                                            selectedCategory,\n                                            \" category at the moment.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-900 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight sm:text-4xl\",\n                                        children: \"Support Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-lg\",\n                                        children: \"Your contribution can help us expand our reach and create even more educational opportunities for underprivileged students across Northern Nigeria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Heart_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Donate to Projects\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold\",\n                                                children: \"Become a Partner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-green-950 dark:bg-black text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Building Tomorrow's Leaders\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-200 max-w-2xl mx-auto\",\n                                children: \"Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this transformative journey.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"LXCPPSppHbGW1RWSamHfTqQBFaU=\");\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/projects/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/dollar-sign.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DollarSign)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst DollarSign = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"DollarSign\", [\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"2\",\n            y2: \"22\",\n            key: \"7eqyqh\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\",\n            key: \"1b0p4s\"\n        }\n    ]\n]);\n //# sourceMappingURL=dollar-sign.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\n"));

/***/ })

});