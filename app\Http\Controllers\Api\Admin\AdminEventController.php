<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Events",
 *     description="Admin API Endpoints for Event Management"
 * )
 */
class AdminEventController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/events",
     *     summary="Get all events for admin",
     *     tags={"Admin Events"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Events retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $status = $request->get('status');
            $dateRange = $request->get('date_range');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = Event::with(['organizer'])->withCount('registrations');

            // Apply search filter
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('venue', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%");
                });
            }

            // Apply status filter
            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            // Apply date range filter
            if ($dateRange && $dateRange !== 'all') {
                $startDate = match($dateRange) {
                    'today' => Carbon::today(),
                    'week' => Carbon::now()->startOfWeek(),
                    'month' => Carbon::now()->startOfMonth(),
                    'upcoming' => Carbon::now(),
                    default => null
                };

                if ($startDate) {
                    if ($dateRange === 'upcoming') {
                        $query->where('start_datetime', '>=', $startDate);
                    } else {
                        $query->where('created_at', '>=', $startDate);
                    }
                }
            }

            // Apply sorting
            if (in_array($sortBy, ['created_at', 'title', 'start_datetime', 'end_datetime', 'status'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $events = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Events retrieved successfully',
                'data' => [
                    'data' => $events->items(),
                    'meta' => [
                        'total' => $events->total(),
                        'per_page' => $events->perPage(),
                        'current_page' => $events->currentPage(),
                        'last_page' => $events->lastPage(),
                        'from' => $events->firstItem(),
                        'to' => $events->lastItem(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/admin/events",
     *     summary="Create a new event",
     *     tags={"Admin Events"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "description", "start_datetime", "end_datetime", "location"},
     *             @OA\Property(property="title", type="string"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="start_datetime", type="string", format="datetime"),
     *             @OA\Property(property="end_datetime", type="string", format="datetime"),
     *             @OA\Property(property="location", type="string"),
     *             @OA\Property(property="max_participants", type="integer"),
     *             @OA\Property(property="registration_deadline", type="string", format="datetime"),
     *             @OA\Property(property="is_featured", type="boolean"),
     *             @OA\Property(property="status", type="string", enum={"draft", "published", "cancelled"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Event created successfully"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'start_datetime' => 'required|date|after:now',
                'end_datetime' => 'required|date|after:start_datetime',
                'location' => 'required|string|max:255',
                'max_participants' => 'nullable|integer|min:1',
                'registration_deadline' => 'nullable|date|before:start_datetime',
                'is_featured' => 'boolean',
                'status' => 'in:draft,published,cancelled',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $event = Event::create($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Event created successfully',
                'data' => $event
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/events/{id}",
     *     summary="Get specific event details",
     *     tags={"Admin Events"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Event retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $event = Event::with(['registrations.user'])
                ->withCount('registrations')
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Event retrieved successfully',
                'data' => $event
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/events/{id}",
     *     summary="Update an event",
     *     tags={"Admin Events"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Event updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $event = Event::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'string|max:255',
                'description' => 'string',
                'start_datetime' => 'date',
                'end_datetime' => 'date|after:start_datetime',
                'location' => 'string|max:255',
                'max_participants' => 'nullable|integer|min:1',
                'registration_deadline' => 'nullable|date',
                'is_featured' => 'boolean',
                'status' => 'in:draft,published,cancelled',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $event->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Event updated successfully',
                'data' => $event
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/events/{id}",
     *     summary="Delete an event",
     *     tags={"Admin Events"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Event deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $event = Event::findOrFail($id);
            
            // Check if event has registrations
            if ($event->registrations()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete event with existing registrations'
                ], 400);
            }

            $event->delete();

            return response()->json([
                'success' => true,
                'message' => 'Event deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get event registrations
     */
    public function registrations($id): JsonResponse
    {
        try {
            $event = Event::findOrFail($id);
            $registrations = EventRegistration::with('user')
                ->where('event_id', $id)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Event registrations retrieved successfully',
                'data' => [
                    'event' => $event,
                    'registrations' => $registrations
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve registrations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check-in participant
     */
    public function checkIn($id, $registrationId): JsonResponse
    {
        try {
            $registration = EventRegistration::where('event_id', $id)
                ->where('id', $registrationId)
                ->firstOrFail();

            $registration->update([
                'status' => 'checked_in',
                'checked_in_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Participant checked in successfully',
                'data' => $registration
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check in participant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on events
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:publish,cancel,delete,export',
                'event_ids' => 'required|array|min:1',
                'event_ids.*' => 'integer|exists:events,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $action = $request->action;
            $eventIds = $request->event_ids;

            $events = Event::whereIn('id', $eventIds)->get();

            if ($events->count() !== count($eventIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some events not found'
                ], 404);
            }

            $successCount = 0;
            $errors = [];

            foreach ($events as $event) {
                try {
                    switch ($action) {
                        case 'publish':
                            $event->update(['status' => 'published']);
                            break;
                        case 'cancel':
                            $event->update(['status' => 'cancelled']);
                            break;
                        case 'delete':
                            // Check for registrations before deletion
                            if ($event->registrations()->count() > 0) {
                                $errors[] = "Cannot delete event '{$event->title}' with existing registrations";
                                continue 2;
                            }
                            $event->delete();
                            break;
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to process event '{$event->title}': " . $e->getMessage();
                }
            }

            $message = "Successfully processed {$successCount} out of " . count($eventIds) . " events";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $successCount,
                    'total_count' => count($eventIds),
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export events data
     */
    public function exportEvents(Request $request): JsonResponse
    {
        try {
            $format = $request->get('format', 'csv');
            $status = $request->get('status');

            $query = Event::with(['organizer'])->withCount('registrations');

            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            $events = $query->get();

            // Prepare data for export
            $exportData = $events->map(function ($event) {
                return [
                    'ID' => $event->id,
                    'Title' => $event->title,
                    'Description' => strip_tags($event->description),
                    'Event Type' => $event->event_type,
                    'Start Date' => $event->start_datetime->format('Y-m-d H:i:s'),
                    'End Date' => $event->end_datetime->format('Y-m-d H:i:s'),
                    'Venue' => $event->venue,
                    'Address' => $event->address,
                    'Capacity' => $event->capacity,
                    'Registrations' => $event->registrations_count,
                    'Status' => $event->status,
                    'Organizer' => $event->organizer ? $event->organizer->first_name . ' ' . $event->organizer->last_name : '',
                    'Created At' => $event->created_at->format('Y-m-d H:i:s')
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Export data prepared successfully',
                'data' => [
                    'format' => $format,
                    'count' => $exportData->count(),
                    'data' => $exportData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get event statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_events' => Event::count(),
                'published_events' => Event::where('status', 'published')->count(),
                'upcoming_events' => Event::where('start_datetime', '>', Carbon::now())->count(),
                'past_events' => Event::where('end_datetime', '<', Carbon::now())->count(),
                'total_registrations' => EventRegistration::count(),
                'events_this_month' => Event::whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year)
                    ->count(),
                'average_attendance' => EventRegistration::where('status', 'checked_in')->count() / max(Event::count(), 1) * 100
            ];

            return response()->json([
                'success' => true,
                'message' => 'Event statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
