'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BookOpen, Target, GraduationCap, Users, DollarSign, Calendar, Plus, Edit, Eye, Power, PowerOff } from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

interface Scholarship {
  id: number
  title: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria: string
  amount: number
  application_deadline: string
  is_open: boolean
  status: string
  current_applicants: number
  max_applicants: number
}

const categoryConfig = {
  primary: {
    icon: BookOpen,
    title: "Primary School",
    color: "bg-blue-500"
  },
  secondary: {
    icon: Target,
    title: "Secondary School", 
    color: "bg-green-500"
  },
  university: {
    icon: GraduationCap,
    title: "University",
    color: "bg-purple-500"
  }
}

export default function AdminScholarshipsPage() {
  const [scholarships, setScholarships] = useState<Scholarship[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    category: 'primary',
    description: '',
    eligibility_criteria: '',
    amount: '',
    application_deadline: '',
    max_applicants: ''
  })
  
  const { toast } = useToast()

  useEffect(() => {
    fetchScholarships()
  }, [])

  const fetchScholarships = async () => {
    try {
      const response = await apiClient.request('/admin/scholarships')
      if (response.success && response.data) {
        setScholarships(response.data)
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error)
      toast({
        title: "Error",
        description: "Failed to load scholarships",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateScholarship = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await apiClient.request('/admin/scholarships', {
        method: 'POST',
        body: JSON.stringify(formData)
      })
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Scholarship created successfully"
        })
        setIsCreateDialogOpen(false)
        resetForm()
        fetchScholarships()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create scholarship",
        variant: "destructive"
      })
    }
  }

  const handleToggleStatus = async (id: number, currentStatus: string) => {
    try {
      const action = currentStatus === 'open' ? 'close' : 'open'
      const response = await apiClient.request(`/admin/scholarships/${id}/${action}`, {
        method: 'POST'
      })
      
      if (response.success) {
        toast({
          title: "Success",
          description: `Scholarship ${action}ed successfully`
        })
        fetchScholarships()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update scholarship status",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      title: '',
      category: 'primary',
      description: '',
      eligibility_criteria: '',
      amount: '',
      application_deadline: '',
      max_applicants: ''
    })
  }

  const filteredScholarships = selectedCategory === 'all' 
    ? scholarships 
    : scholarships.filter(s => s.category === selectedCategory)

  const getStats = () => {
    return {
      total: scholarships.length,
      open: scholarships.filter(s => s.status === 'open').length,
      closed: scholarships.filter(s => s.status === 'closed').length,
      applications: scholarships.reduce((sum, s) => sum + s.current_applicants, 0)
    }
  }

  const stats = getStats()

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Scholarship Management</h1>
          <p className="text-gray-600">Manage scholarships, applications, and dynamic form fields</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Scholarships</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Open</p>
                  <p className="text-2xl font-bold text-green-600">{stats.open}</p>
                </div>
                <Power className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Closed</p>
                  <p className="text-2xl font-bold text-red-600">{stats.closed}</p>
                </div>
                <PowerOff className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Applications</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.applications}</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center mb-6">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="primary">Primary</TabsTrigger>
              <TabsTrigger value="secondary">Secondary</TabsTrigger>
              <TabsTrigger value="university">University</TabsTrigger>
            </TabsList>
          </Tabs>

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Scholarship
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Scholarship</DialogTitle>
                <DialogDescription>
                  Create a new scholarship with custom form fields for applications.
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleCreateScholarship} className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="primary">Primary School</SelectItem>
                      <SelectItem value="secondary">Secondary School</SelectItem>
                      <SelectItem value="university">University</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="eligibility">Eligibility Criteria</Label>
                  <Textarea
                    id="eligibility"
                    value={formData.eligibility_criteria}
                    onChange={(e) => setFormData(prev => ({ ...prev, eligibility_criteria: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount">Amount (₦)</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={formData.amount}
                      onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="deadline">Application Deadline</Label>
                    <Input
                      id="deadline"
                      type="date"
                      value={formData.application_deadline}
                      onChange={(e) => setFormData(prev => ({ ...prev, application_deadline: e.target.value }))}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="max_applicants">Maximum Applicants</Label>
                  <Input
                    id="max_applicants"
                    type="number"
                    value={formData.max_applicants}
                    onChange={(e) => setFormData(prev => ({ ...prev, max_applicants: e.target.value }))}
                  />
                </div>
                
                <Button type="submit" className="w-full">Create Scholarship</Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Scholarships List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredScholarships.map((scholarship) => (
            <Card key={scholarship.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {React.createElement(categoryConfig[scholarship.category].icon, { 
                      className: "h-5 w-5" 
                    })}
                    <CardTitle className="text-lg leading-tight">
                      {scholarship.title}
                    </CardTitle>
                  </div>
                  <Badge 
                    variant={scholarship.status === 'open' ? "default" : "secondary"}
                    className={scholarship.status === 'open' ? "bg-green-500" : "bg-red-500"}
                  >
                    {scholarship.status}
                  </Badge>
                </div>
                <CardDescription className="line-clamp-2">
                  {scholarship.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <DollarSign className="h-4 w-4" />
                  <span>₦{scholarship.amount.toLocaleString()}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>Due: {new Date(scholarship.application_deadline).toLocaleDateString()}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="h-4 w-4" />
                  <span>{scholarship.current_applicants}/{scholarship.max_applicants || '∞'} applicants</span>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button 
                    variant={scholarship.status === 'open' ? 'destructive' : 'default'}
                    size="sm" 
                    className="flex-1"
                    onClick={() => handleToggleStatus(scholarship.id, scholarship.status)}
                  >
                    {scholarship.status === 'open' ? 'Close' : 'Open'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredScholarships.length === 0 && !loading && (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No scholarships found</h3>
            <p className="text-gray-500 mb-4">
              {selectedCategory === 'all' ? 'Create your first scholarship to get started.' : `No scholarships in the ${selectedCategory} category.`}
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Scholarship
            </Button>
          </div>
        )}
      </div>
    </div>
  )
} 