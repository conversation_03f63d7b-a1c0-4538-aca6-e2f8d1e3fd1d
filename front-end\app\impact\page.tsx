"use client"


import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  GraduationCap,
  BookOpen,
  TrendingUp,
  Award,
  Heart,
  MapPin,
  Target,
  CheckCircle,
  BarChart3,
} from "lucide-react"

const impactMetrics = [
  {
    category: "Education Access",
    metrics: [
      { label: "Students Supported", value: "1,200+", change: "+15%", icon: Users, color: "text-blue-600" },
      { label: "Schools Partnered", value: "45", change: "+8", icon: GraduationCap, color: "text-green-600" },
      { label: "Books Distributed", value: "5,000+", change: "+1,200", icon: BookOpen, color: "text-purple-600" },
      { label: "Scholarships Awarded", value: "₦12M", change: "+₦3M", icon: Award, color: "text-amber-600" },
    ],
  },
  {
    category: "Academic Performance",
    metrics: [
      { label: "Test Score Improvement", value: "40%", change: "+5%", icon: TrendingUp, color: "text-green-600" },
      { label: "Graduation Rate", value: "95%", change: "+10%", icon: CheckCircle, color: "text-blue-600" },
      { label: "University Admission", value: "85%", change: "+12%", icon: GraduationCap, color: "text-purple-600" },
      { label: "Employment Rate", value: "78%", change: "+8%", icon: Target, color: "text-amber-600" },
    ],
  },
]

const stateImpact = [
  {
    state: "Kaduna State",
    students: 650,
    schools: 25,
    projects: 8,
    investment: "₦8.5M",
    progress: 85,
    highlights: ["Largest scholarship program", "15 libraries established", "Teacher training center"],
  },
  {
    state: "Kano State",
    students: 350,
    schools: 12,
    projects: 5,
    investment: "₦4.2M",
    progress: 75,
    highlights: ["Digital learning initiative", "Adult literacy program", "Community outreach"],
  },
  {
    state: "Katsina State",
    students: 200,
    schools: 8,
    projects: 3,
    investment: "₦2.8M",
    progress: 65,
    highlights: ["Rural school support", "Mobile library program", "Vocational training"],
  },
]

const successStories = [
  {
    name: "Amina Ibrahim",
    program: "University Scholarship",
    achievement: "Medical Student at ABU Zaria",
    year: "2023",
    quote: "Kofa Foundation didn't just give me a scholarship, they gave me hope and a future.",
    impact: "Now mentoring 15 younger students",
  },
  {
    name: "Malam Bello",
    program: "Adult Literacy",
    achievement: "Community Leader & Advocate",
    year: "2022",
    quote: "Learning to read at 45 changed my life and my community's future.",
    impact: "Started 3 community schools",
  },
  {
    name: "Fatima Abdullahi",
    program: "Back-to-School Initiative",
    achievement: "Top Student in WAEC",
    year: "2024",
    quote: "The uniform and books gave me confidence to excel in my studies.",
    impact: "Inspired 50+ peers to stay in school",
  },
]

const yearlyProgress = [
  { year: "2015", students: 12, budget: "₦0.5M", schools: 1 },
  { year: "2016", students: 45, budget: "₦1.2M", schools: 3 },
  { year: "2017", students: 120, budget: "₦2.8M", schools: 8 },
  { year: "2018", students: 250, budget: "₦4.5M", schools: 15 },
  { year: "2019", students: 380, budget: "₦6.2M", schools: 22 },
  { year: "2020", students: 520, budget: "₦7.8M", schools: 28 },
  { year: "2021", students: 680, budget: "₦9.5M", schools: 32 },
  { year: "2022", students: 850, budget: "₦11.2M", schools: 38 },
  { year: "2023", students: 1050, budget: "₦13.8M", schools: 42 },
  { year: "2024", students: 1200, budget: "₦15.5M", schools: 45 },
]

export default function ImpactPage() {
  return (
    <div className="flex min-h-screen flex-col bg-pattern">

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden">
          <div className="absolute inset-0 bg-grid opacity-20"></div>
          <div className="container relative">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Our Impact</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up">
                Measuring Real Change
              </h1>
              <p
                className="text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto"
                style={{ animationDelay: "0.2s" }}
              >
                Since 2015, we've been creating measurable, lasting impact across Nigeria. See how your support
                is transforming lives and communities, helping students, the underprivileged, and those in need.
              </p>
              <div
                className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">9</div>
                  <div className="text-green-200 text-sm">Years of Impact</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">1,200+</div>
                  <div className="text-green-200 text-sm">Lives Changed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">45</div>
                  <div className="text-green-200 text-sm">Partner Schools</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">3</div>
                  <div className="text-green-200 text-sm">Regions Served</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Impact Metrics */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Impact by Numbers</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Quantifiable results that demonstrate the effectiveness of our educational programs
              </p>
            </div>

            <Tabs defaultValue="education" className="space-y-8">
              <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
                <TabsTrigger value="education">Education Access</TabsTrigger>
                <TabsTrigger value="performance">Academic Performance</TabsTrigger>
              </TabsList>

              {impactMetrics.map((category, categoryIndex) => (
                <TabsContent key={categoryIndex} value={categoryIndex === 0 ? "education" : "performance"}>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {category.metrics.map((metric, index) => (
                      <Card
                        key={index}
                        className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                      >
                        <CardContent className="p-6">
                          <div
                            className={`h-16 w-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4 ${metric.color}`}
                          >
                            <metric.icon className="h-8 w-8" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">{metric.label}</h3>
                          <p className={`text-3xl font-bold mb-2 ${metric.color}`}>{metric.value}</p>
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                            {metric.change} this year
                          </Badge>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </AnimatedSection>

        {/* State-wise Impact */}
        <AnimatedSection className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Regional Impact</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Our work spans across various regions in Nigeria, each with unique challenges and achievements in supporting students, the underprivileged, and those in need.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {stateImpact.map((state, index) => (
                <Card
                  key={index}
                  className="overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <MapPin className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-green-800 dark:text-green-200">{state.state}</h3>
                        <p className="text-muted-foreground">Investment: {state.investment}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{state.students}</div>
                        <div className="text-xs text-muted-foreground">Students</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{state.schools}</div>
                        <div className="text-xs text-muted-foreground">Schools</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{state.projects}</div>
                        <div className="text-xs text-muted-foreground">Projects</div>
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{state.progress}%</span>
                      </div>
                      <Progress value={state.progress} className="h-2" />
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">Key Highlights</h4>
                      <ul className="space-y-2">
                        {state.highlights.map((highlight, i) => (
                          <li key={i} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                            {highlight}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Success Stories */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Stories of Transformation</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Real people, real change. Meet some of the individuals whose lives have been transformed through our
                programs
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {successStories.map((story, index) => (
                <Card
                  key={index}
                  className="overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <CardContent className="p-6">
                    <div className="text-center mb-6">
                      <div className="h-20 w-20 bg-gradient-to-br from-green-500 to-green-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl font-bold text-white">{story.name.charAt(0)}</span>
                      </div>
                      <h3 className="text-xl font-bold text-green-800 dark:text-green-200">{story.name}</h3>
                      <p className="text-green-600 dark:text-green-400 font-medium">{story.achievement}</p>
                      <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                        {story.program}
                      </Badge>
                    </div>

                    <blockquote className="text-muted-foreground italic text-center mb-6 border-l-4 border-green-500 pl-4">
                      "{story.quote}"
                    </blockquote>

                    <div className="text-center">
                      <div className="text-sm font-medium text-green-600 dark:text-green-400 mb-1">Current Impact</div>
                      <div className="text-sm text-muted-foreground">{story.impact}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </AnimatedSection>



        {/* Call to Action */}
        <AnimatedSection className="py-20 bg-green-900 text-white">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Be Part of Our Impact</h2>
              <p className="text-green-100 text-lg">
                Every donation, every volunteer hour, every partnership contributes to these remarkable results. Join us
                in creating even greater impact in the years ahead.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold transition-all duration-300 hover:scale-105 rounded-full"
                >
                  <Heart className="mr-2 h-5 w-5" />
                  Support Our Mission
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="text-white border-white hover:bg-white/10 transition-all duration-300 rounded-full"
                >
                  <BarChart3 className="mr-2 h-5 w-5" />
                  View Full Report
                </Button>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </main>

      {/* Footer */}
      <footer className="bg-green-950 dark:bg-black text-white py-12">
        <div className="container">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Transparent Impact Reporting</h3>
            <p className="text-green-200 max-w-2xl mx-auto">
              We believe in complete transparency. All our impact data is verified and audited annually to ensure
              accuracy and accountability to our donors and beneficiaries.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
