"use client"

import { useState, useEffect } from 'react'
import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { apiClient } from '@/lib/api'
import {
  Users,
  GraduationCap,
  Award,
  Heart,
  MapPin,
  Target,
  CheckCircle,
  BarChart3,
  Loader2,
} from "lucide-react"

// Interface for impact statistics
interface ImpactStats {
  students_supported: number
  schools_partnered: number
  scholarships_awarded: number
  total_applications: number
  active_scholarships: number
  regional_impact: Array<{
    state: string
    schools_registered: number
    students_applied: number
  }>
}

// Format currency for display
const formatCurrency = (amount: number): string => {
  if (amount >= 1000000) {
    return `₦${(amount / 1000000).toFixed(1)}M`
  } else if (amount >= 1000) {
    return `₦${(amount / 1000).toFixed(0)}K`
  }
  return `₦${amount.toLocaleString()}`
}

export default function ImpactPage() {
  const [impactStats, setImpactStats] = useState<ImpactStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchImpactStats = async () => {
      try {
        setLoading(true)
        const response = await apiClient.request('/impact/statistics')
        if (response.success) {
          setImpactStats(response.data)
        } else {
          setError('Failed to load impact statistics')
        }
      } catch (err) {
        console.error('Error fetching impact stats:', err)
        setError('Failed to load impact statistics')
      } finally {
        setLoading(false)
      }
    }

    fetchImpactStats()
  }, [])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col bg-pattern">
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-green-600" />
            <p className="text-gray-600">Loading impact statistics...</p>
          </div>
        </main>
      </div>
    )
  }

  if (error || !impactStats) {
    return (
      <div className="flex min-h-screen flex-col bg-pattern">
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4">
            <p className="text-red-600">{error || 'Failed to load data'}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-pattern">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden">
          <div className="absolute inset-0 bg-grid opacity-20"></div>
          <div className="container relative">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Our Impact</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up">
                Measuring Real Change
              </h1>
              <p
                className="text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto"
                style={{ animationDelay: "0.2s" }}
              >
                Since 2015, we've been creating measurable, lasting impact across Nigeria. See how your support
                is transforming lives and communities, helping students, the underprivileged, and those in need.
              </p>
              <div
                className="grid grid-cols-2 md:grid-cols-3 gap-6 mt-12 animate-fade-in-up"
                style={{ animationDelay: "0.4s" }}
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{impactStats.students_supported.toLocaleString()}</div>
                  <div className="text-green-200 text-sm">Students Supported</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{impactStats.schools_partnered}</div>
                  <div className="text-green-200 text-sm">Schools Partnered</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">{formatCurrency(impactStats.scholarships_awarded)}</div>
                  <div className="text-green-200 text-sm">Scholarships Awarded</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Impact Metrics */}
        <AnimatedSection className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Impact by Numbers</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Real data showing the effectiveness of our educational programs and support initiatives
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Students Supported</h3>
                  <p className="text-3xl font-bold mb-2 text-green-600">{impactStats.students_supported.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">Students who received scholarships</p>
                </CardContent>
              </Card>

              <Card className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <GraduationCap className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Schools Partnered</h3>
                  <p className="text-3xl font-bold mb-2 text-green-600">{impactStats.schools_partnered}</p>
                  <p className="text-sm text-muted-foreground">Partner organizations registered</p>
                </CardContent>
              </Card>

              <Card className="text-center neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Scholarships Awarded</h3>
                  <p className="text-3xl font-bold mb-2 text-green-600">{formatCurrency(impactStats.scholarships_awarded)}</p>
                  <p className="text-sm text-muted-foreground">Total amount awarded to students</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </AnimatedSection>

        {/* State-wise Impact */}
        <AnimatedSection className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Regional Impact</h2>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Our reach across Nigeria, showing the states where students have applied and schools have registered
              </p>
            </div>

            {impactStats.regional_impact.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {impactStats.regional_impact.map((region, index) => (
                  <Card
                    key={index}
                    className="overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                          <MapPin className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-green-800 dark:text-green-200">{region.state}</h3>
                          <p className="text-muted-foreground">State</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{region.students_applied}</div>
                          <div className="text-xs text-muted-foreground">Students Applied</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">{region.schools_registered}</div>
                          <div className="text-xs text-muted-foreground">Schools Registered</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No regional data available yet.</p>
              </div>
            )}
          </div>
        </AnimatedSection>



        {/* Call to Action */}
        <AnimatedSection className="py-20 bg-green-900 text-white">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Be Part of Our Impact</h2>
              <p className="text-green-100 text-lg">
                Every donation, every volunteer hour, every partnership contributes to these remarkable results. Join us
                in creating even greater impact in the years ahead.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold transition-all duration-300 hover:scale-105 rounded-full"
                >
                  <Heart className="mr-2 h-5 w-5" />
                  Support Our Mission
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="text-white border-white hover:bg-white/10 transition-all duration-300 rounded-full"
                >
                  <BarChart3 className="mr-2 h-5 w-5" />
                  View Full Report
                </Button>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </main>

      {/* Footer */}
      <footer className="bg-green-950 dark:bg-black text-white py-12">
        <div className="container">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Transparent Impact Reporting</h3>
            <p className="text-green-200 max-w-2xl mx-auto">
              We believe in complete transparency. All our impact data is verified and audited annually to ensure
              accuracy and accountability to our donors and beneficiaries.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
