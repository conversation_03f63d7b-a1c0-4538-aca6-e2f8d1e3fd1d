# =============================================================================
# HALIMAKQ Foundation - Environment Configuration
# =============================================================================
# Copy this file to .env.local and update the values for your environment

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Laravel Backend API URL (with /api/v1)
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Laravel Backend Base URL (without /api/v1) - for auth redirects and file uploads
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# =============================================================================
# PRODUCTION URLS (uncomment and use for production)
# =============================================================================

# Production API URL
# NEXT_PUBLIC_API_URL=https://halimakqfoundation.fnskills.ng/api/v1

# Production Backend URL
# NEXT_PUBLIC_BACKEND_URL=https://halimakqfoundation.fnskills.ng

# =============================================================================
# STAGING URLS (uncomment and use for staging)
# =============================================================================

# Staging API URL
# NEXT_PUBLIC_API_URL=https://staging.halimakqfoundation.fnskills.ng/api/v1

# Staging Backend URL
# NEXT_PUBLIC_BACKEND_URL=https://staging.halimakqfoundation.fnskills.ng

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable Google Analytics
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Enable Chat Support
NEXT_PUBLIC_ENABLE_CHAT=false

# Enable Push Notifications
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=false

# =============================================================================
# THIRD-PARTY SERVICES
# =============================================================================

# Google Analytics ID (if analytics is enabled)
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Paystack Public Key (for donations)
# NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_xxxxxxxxxx

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Show debug information in development
NEXT_PUBLIC_DEBUG=false

# API Request timeout (in milliseconds)
NEXT_PUBLIC_API_TIMEOUT=30000
