"use client"

import { useEffect, useState } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check<PERSON>ircle2, Download, ArrowLeft, Share2, Heart } from "lucide-react"
import { apiClient } from "@/lib/api"
import { APP_CONFIG, formatCurrency } from "@/lib/constants"

interface DonationDetails {
  id: number
  reference: string
  amount: number
  currency: string
  donor_name: string
  donor_email: string
  payment_method: string
  payment_status: string
  created_at: string
  receipt_number?: string
}

export default function DonationSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [donation, setDonation] = useState<DonationDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const reference = searchParams.get('reference')

  useEffect(() => {
    if (!reference) {
      setError('No payment reference found')
      setLoading(false)
      return
    }

    const verifyPayment = async () => {
      try {
        setLoading(true)
        const response = await apiClient.verifyPayment(reference)
        
        if (response.success && response.data) {
          setDonation(response.data)
        } else {
          setError(response.message || 'Failed to verify payment')
        }
      } catch (err) {
        console.error('Payment verification error:', err)
        setError('Failed to verify payment. Please contact support.')
      } finally {
        setLoading(false)
      }
    }

    verifyPayment()
  }, [reference])

  const handleDownloadReceipt = async () => {
    if (!donation?.id) return

    try {
      // Use the API client to get the correct base URL
      const receiptUrl = `${APP_CONFIG.API_BASE_URL}/donations/${donation.id}/receipt`
      window.open(receiptUrl, '_blank')
    } catch (error) {
      console.error('Failed to download receipt:', error)
    }
  }

  const handleShare = async () => {
    const shareData = {
      title: `I just donated to ${APP_CONFIG.NAME}!`,
      text: `I made a donation of ${formatCurrency(donation?.amount || 0)} to support ${APP_CONFIG.NAME}. Join me in making a difference!`,
      url: window.location.origin + '/donate'
    }

    if (navigator.share) {
      try {
        await navigator.share(shareData)
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(`${shareData.text} ${shareData.url}`)
      alert('Donation details copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying your payment...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-xl">!</span>
            </div>
            <h3 className="text-xl font-semibold text-red-600 mb-2">Payment Verification Failed</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-y-3">
              <Button 
                onClick={() => router.push('/donate')}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                Try Again
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/contact')}
                className="w-full"
              >
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          {/* Success Card */}
          <Card className="overflow-hidden shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white text-center py-8">
              <div className="flex justify-center mb-4">
                <div className="h-16 w-16 bg-white/20 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="h-10 w-10 text-white" />
                </div>
              </div>
              <CardTitle className="text-3xl font-bold mb-2">Thank You!</CardTitle>
              <p className="text-green-100 text-lg">Your donation has been processed successfully</p>
            </CardHeader>

            <CardContent className="p-8">
              {donation && (
                <div className="space-y-6">
                  {/* Donation Details */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <Heart className="h-5 w-5 text-red-500" />
                      Donation Details
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Amount:</span>
                        <span className="font-semibold text-green-600">
                          {formatCurrency(donation.amount)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Reference:</span>
                        <span className="font-mono text-xs">{donation.reference}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Payment Method:</span>
                        <span className="capitalize">{donation.payment_method.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <span className="text-green-600 font-semibold capitalize">
                          {donation.payment_status.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date:</span>
                        <span>{new Date(donation.created_at).toLocaleDateString()}</span>
                      </div>
                      {donation.receipt_number && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Receipt #:</span>
                          <span className="font-mono text-xs">{donation.receipt_number}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Impact Message */}
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
                    <h3 className="font-semibold text-blue-800 mb-3">Your Impact</h3>
                    <p className="text-blue-700 text-sm leading-relaxed">
                      Your generous donation of {formatCurrency(donation.amount)} will help us continue our mission
                      of providing education, healthcare, and essential services to communities in need. 
                      Thank you for being part of the change!
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      onClick={handleDownloadReceipt}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Download Receipt
                    </Button>
                    
                    <Button
                      onClick={handleShare}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Share2 className="h-4 w-4" />
                      Share
                    </Button>
                    
                    <Button
                      onClick={() => router.push('/donate')}
                      className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                    >
                      <Heart className="h-4 w-4" />
                      Donate Again
                    </Button>
                  </div>

                  {/* Navigation */}
                  <div className="pt-6 border-t">
                    <Button
                      onClick={() => router.push('/')}
                      variant="ghost"
                      className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Back to Home
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Information */}
          <div className="mt-8 text-center text-sm text-gray-600">
            <p>
              A confirmation email has been sent to your email address. 
              If you have any questions, please <a href="/contact" className="text-green-600 hover:underline">contact us</a>.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
