@extends('api-docs.layout')

@section('title', 'Mobile Integration')

@section('content')
<div class="row">
    <div class="col-12">
        <h1 class="display-5 fw-bold text-primary mb-4">
            <i class="fas fa-mobile-alt me-3"></i>
            Mobile Integration
        </h1>
        <p class="lead text-muted mb-5">Complete guide for integrating the NGO Platform API with mobile applications.</p>
    </div>
</div>

<!-- Overview -->
<div class="row mb-5">
    <div class="col-12">
        <div class="api-card card">
            <div class="card-body">
                <h3 class="card-title">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    Mobile Integration Overview
                </h3>
                <p class="card-text">
                    The NGO Platform API is designed to be mobile-first, providing optimized endpoints for 
                    mobile applications with efficient data structures and minimal bandwidth usage.
                </p>
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fab fa-android fa-3x text-success mb-2"></i>
                            <h5>Android</h5>
                            <p class="text-muted">Native Android integration with Retrofit/OkHttp</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fab fa-apple fa-3x text-primary mb-2"></i>
                            <h5>iOS</h5>
                            <p class="text-muted">Swift/Objective-C with URLSession</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fab fa-react fa-3x text-info mb-2"></i>
                            <h5>React Native</h5>
                            <p class="text-muted">Cross-platform with Axios/Fetch</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- React Native Example -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>React Native Integration</h3>
            <p class="text-muted">Complete React Native service for API integration.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <h5>API Service Setup</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-javascript">// services/ApiService.js
import AsyncStorage from '@react-native-async-storage/async-storage';

class ApiService {
  constructor() {
    this.baseURL = '{{ url('/api/v1') }}';
    this.token = null;
  }

  async setAuthToken(token) {
    this.token = token;
    await AsyncStorage.setItem('auth_token', token);
  }

  async getAuthToken() {
    if (!this.token) {
      this.token = await AsyncStorage.getItem('auth_token');
    }
    return this.token;
  }

  async request(endpoint, options = {}) {
    const token = await this.getAuthToken();
    
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(email, password) {
    const response = await this.request('/login', {
      method: 'POST',
      body: { email, password },
    });

    if (response.success && response.data.token) {
      await this.setAuthToken(response.data.token);
    }

    return response;
  }

  async register(userData) {
    const response = await this.request('/register', {
      method: 'POST',
      body: userData,
    });

    if (response.success && response.data.token) {
      await this.setAuthToken(response.data.token);
    }

    return response;
  }

  async logout() {
    try {
      await this.request('/logout', { method: 'POST' });
    } finally {
      this.token = null;
      await AsyncStorage.removeItem('auth_token');
    }
  }

  // Scholarship methods
  async getPublicScholarships() {
    return this.request('/public-scholarships');
  }

  async getMyApplications() {
    return this.request('/student-scholarships/my-applications');
  }

  async applyForScholarship(scholarshipId, applicationData) {
    const formData = new FormData();
    
    Object.keys(applicationData).forEach(key => {
      if (key === 'documents' && Array.isArray(applicationData[key])) {
        applicationData[key].forEach((file, index) => {
          formData.append(`documents[${index}]`, file);
        });
      } else {
        formData.append(key, applicationData[key]);
      }
    });

    return this.request(`/student-scholarships/${scholarshipId}/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });
  }
}

export default new ApiService();</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Android Example -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>Android Integration (Java/Kotlin)</h3>
            <p class="text-muted">Android service using Retrofit for API communication.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <h5>Retrofit API Interface</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-java">// ApiInterface.java
public interface ApiInterface {
    String BASE_URL = "{{ url('/api/v1/') }}";

    @POST("login")
    Call&lt;LoginResponse&gt; login(@Body LoginRequest request);

    @POST("register")
    Call&lt;RegisterResponse&gt; register(@Body RegisterRequest request);

    @POST("logout")
    Call&lt;BaseResponse&gt; logout(@Header("Authorization") String token);

    @GET("public-scholarships")
    Call&lt;ScholarshipsResponse&gt; getPublicScholarships();

    @GET("student-scholarships/my-applications")
    Call&lt;ApplicationsResponse&gt; getMyApplications(@Header("Authorization") String token);

    @Multipart
    @POST("student-scholarships/{id}/apply")
    Call&lt;ApplicationResponse&gt; applyForScholarship(
        @Path("id") int scholarshipId,
        @Header("Authorization") String token,
        @Part("matric_number") RequestBody matricNumber,
        @Part("course_of_study") RequestBody courseOfStudy,
        @Part("personal_statement") RequestBody personalStatement,
        @Part List&lt;MultipartBody.Part&gt; documents
    );
}

// ApiClient.java
public class ApiClient {
    private static Retrofit retrofit = null;
    private static final String BASE_URL = "{{ url('/api/v1/') }}";

    public static Retrofit getClient() {
        if (retrofit == null) {
            OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();

            retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        }
        return retrofit;
    }

    public static ApiInterface getApiInterface() {
        return getClient().create(ApiInterface.class);
    }
}</code></pre>
                    </div>

                    <h5 class="mt-4">Authentication Manager</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-java">// AuthManager.java
public class AuthManager {
    private static final String PREF_NAME = "NGO_APP_PREFS";
    private static final String TOKEN_KEY = "auth_token";
    private SharedPreferences prefs;
    private ApiInterface apiInterface;

    public AuthManager(Context context) {
        prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        apiInterface = ApiClient.getApiInterface();
    }

    public void login(String email, String password, AuthCallback callback) {
        LoginRequest request = new LoginRequest(email, password);
        
        apiInterface.login(request).enqueue(new Callback&lt;LoginResponse&gt;() {
            @Override
            public void onResponse(Call&lt;LoginResponse&gt; call, Response&lt;LoginResponse&gt; response) {
                if (response.isSuccessful() && response.body().isSuccess()) {
                    String token = response.body().getData().getToken();
                    saveToken(token);
                    callback.onSuccess(response.body().getData().getUser());
                } else {
                    callback.onError("Login failed");
                }
            }

            @Override
            public void onFailure(Call&lt;LoginResponse&gt; call, Throwable t) {
                callback.onError(t.getMessage());
            }
        });
    }

    public void saveToken(String token) {
        prefs.edit().putString(TOKEN_KEY, token).apply();
    }

    public String getToken() {
        return prefs.getString(TOKEN_KEY, null);
    }

    public String getAuthHeader() {
        String token = getToken();
        return token != null ? "Bearer " + token : null;
    }

    public boolean isLoggedIn() {
        return getToken() != null;
    }

    public void logout() {
        String authHeader = getAuthHeader();
        if (authHeader != null) {
            apiInterface.logout(authHeader).enqueue(new Callback&lt;BaseResponse&gt;() {
                @Override
                public void onResponse(Call&lt;BaseResponse&gt; call, Response&lt;BaseResponse&gt; response) {
                    clearToken();
                }

                @Override
                public void onFailure(Call&lt;BaseResponse&gt; call, Throwable t) {
                    clearToken(); // Clear token even if logout request fails
                }
            });
        }
    }

    private void clearToken() {
        prefs.edit().remove(TOKEN_KEY).apply();
    }

    public interface AuthCallback {
        void onSuccess(User user);
        void onError(String error);
    }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- iOS Swift Example -->
<div class="row mb-5">
    <div class="col-12">
        <div class="endpoint-section">
            <h3>iOS Swift Integration</h3>
            <p class="text-muted">Swift implementation using URLSession and Codable.</p>
            
            <div class="api-card card">
                <div class="card-body">
                    <h5>API Service Class</h5>
                    <div class="code-block">
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <pre><code class="language-swift">// APIService.swift
import Foundation

class APIService {
    static let shared = APIService()
    private let baseURL = "{{ url('/api/v1') }}"
    private var authToken: String?
    
    private init() {
        loadAuthToken()
    }
    
    // MARK: - Token Management
    func setAuthToken(_ token: String) {
        self.authToken = token
        UserDefaults.standard.set(token, forKey: "auth_token")
    }
    
    private func loadAuthToken() {
        self.authToken = UserDefaults.standard.string(forKey: "auth_token")
    }
    
    func clearAuthToken() {
        self.authToken = nil
        UserDefaults.standard.removeObject(forKey: "auth_token")
    }
    
    // MARK: - Network Request
    private func makeRequest&lt;T: Codable&gt;(
        endpoint: String,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        responseType: T.Type,
        completion: @escaping (Result&lt;T, APIError&gt;) -&gt; Void
    ) {
        guard let url = URL(string: baseURL + endpoint) else {
            completion(.failure(.invalidURL))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        if let token = authToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = body
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(.networkError(error.localizedDescription)))
                    return
                }
                
                guard let data = data else {
                    completion(.failure(.noData))
                    return
                }
                
                do {
                    let result = try JSONDecoder().decode(responseType, from: data)
                    completion(.success(result))
                } catch {
                    completion(.failure(.decodingError(error.localizedDescription)))
                }
            }
        }.resume()
    }
    
    // MARK: - Authentication
    func login(email: String, password: String, completion: @escaping (Result&lt;LoginResponse, APIError&gt;) -&gt; Void) {
        let loginData = LoginRequest(email: email, password: password)
        
        guard let body = try? JSONEncoder().encode(loginData) else {
            completion(.failure(.encodingError))
            return
        }
        
        makeRequest(endpoint: "/login", method: .POST, body: body, responseType: LoginResponse.self) { result in
            switch result {
            case .success(let response):
                if response.success, let token = response.data?.token {
                    self.setAuthToken(token)
                }
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func register(userData: RegisterRequest, completion: @escaping (Result&lt;RegisterResponse, APIError&gt;) -&gt; Void) {
        guard let body = try? JSONEncoder().encode(userData) else {
            completion(.failure(.encodingError))
            return
        }
        
        makeRequest(endpoint: "/register", method: .POST, body: body, responseType: RegisterResponse.self) { result in
            switch result {
            case .success(let response):
                if response.success, let token = response.data?.token {
                    self.setAuthToken(token)
                }
                completion(.success(response))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func logout(completion: @escaping (Result&lt;BaseResponse, APIError&gt;) -&gt; Void) {
        makeRequest(endpoint: "/logout", method: .POST, body: nil, responseType: BaseResponse.self) { result in
            self.clearAuthToken()
            completion(result)
        }
    }
    
    // MARK: - Scholarships
    func getPublicScholarships(completion: @escaping (Result&lt;ScholarshipsResponse, APIError&gt;) -&gt; Void) {
        makeRequest(endpoint: "/public-scholarships", responseType: ScholarshipsResponse.self, completion: completion)
    }
    
    func getMyApplications(completion: @escaping (Result&lt;ApplicationsResponse, APIError&gt;) -&gt; Void) {
        makeRequest(endpoint: "/student-scholarships/my-applications", responseType: ApplicationsResponse.self, completion: completion)
    }
}

// MARK: - Supporting Types
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

enum APIError: Error {
    case invalidURL
    case noData
    case networkError(String)
    case decodingError(String)
    case encodingError
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .networkError(let message):
            return "Network error: \(message)"
        case .decodingError(let message):
            return "Decoding error: \(message)"
        case .encodingError:
            return "Encoding error"
        }
    }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Best Practices -->
<div class="row">
    <div class="col-12">
        <div class="api-card card">
            <div class="card-body">
                <h3 class="card-title">
                    <i class="fas fa-star text-warning me-2"></i>
                    Mobile Integration Best Practices
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Security</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i> Store tokens securely (Keychain/Keystore)</li>
                            <li><i class="fas fa-check text-success me-2"></i> Implement certificate pinning</li>
                            <li><i class="fas fa-check text-success me-2"></i> Use HTTPS only</li>
                            <li><i class="fas fa-check text-success me-2"></i> Validate server certificates</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Performance</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i> Implement request caching</li>
                            <li><i class="fas fa-check text-success me-2"></i> Use pagination for large datasets</li>
                            <li><i class="fas fa-check text-success me-2"></i> Compress images before upload</li>
                            <li><i class="fas fa-check text-success me-2"></i> Handle offline scenarios</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Error Handling</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i> Parse API error codes</li>
                            <li><i class="fas fa-check text-success me-2"></i> Show user-friendly messages</li>
                            <li><i class="fas fa-check text-success me-2"></i> Implement retry mechanisms</li>
                            <li><i class="fas fa-check text-success me-2"></i> Log errors for debugging</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>User Experience</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i> Show loading indicators</li>
                            <li><i class="fas fa-check text-success me-2"></i> Implement pull-to-refresh</li>
                            <li><i class="fas fa-check text-success me-2"></i> Cache frequently accessed data</li>
                            <li><i class="fas fa-check text-success me-2"></i> Handle network connectivity changes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
