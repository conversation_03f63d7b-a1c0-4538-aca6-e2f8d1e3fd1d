"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Download, RefreshCw, QrCode, User, Mail, Phone, MapPin, Calendar } from "lucide-react"
import { apiClient } from "@/lib/api"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"
import { useSettings } from "@/hooks/useSettings"

interface UserIdCardProps {
  user: {
    id: number
    short_id?: string
    first_name: string
    last_name: string
    email: string
    phone_number?: string
    role: string
    qr_code_url?: string
    profile_picture_url?: string
    address?: string
    city?: string
    state?: string
    country?: string
    created_at?: string
  }
}

interface IdCardData {
  short_id: string
  full_name: string
  email: string
  phone_number?: string
  role: string
  qr_code_url: string
  profile_picture_url?: string
  organization: string
  member_since: string
  address?: string
}

export default function UserIdCard({ user }: UserIdCardProps) {
  const [loading, setLoading] = useState(false)
  const [qrLoading, setQrLoading] = useState(false)
  const [idCardData, setIdCardData] = useState<IdCardData | null>(null)
  const cardRef = useRef<HTMLDivElement>(null)
  const { settings } = useSettings()

  // Get dynamic app name from settings
  const appName = settings?.app_name || 'Laravel NGO Foundation'

  const fetchIdCardData = async () => {
    try {
      setLoading(true)
      const response = await apiClient.getIdCard()
      if (response.success && response.data) {
        setIdCardData(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch ID card data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateNewQrCode = async () => {
    try {
      setQrLoading(true)
      const response = await apiClient.generateQrCode()
      if (response.success) {
        // Refresh ID card data to get new QR code
        await fetchIdCardData()
      }
    } catch (error) {
      console.error('Failed to generate QR code:', error)
    } finally {
      setQrLoading(false)
    }
  }

  const downloadIdCard = async () => {
    if (!cardRef.current || !idCardData) return

    try {
      setLoading(true)
      
      // Generate canvas from the ID card element
      const canvas = await html2canvas(cardRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })
      
      // Create PDF
      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })
      
      // Calculate dimensions
      const imgWidth = 85.6 // Standard ID card width in mm
      const imgHeight = 53.98 // Standard ID card height in mm
      const x = (210 - imgWidth) / 2 // Center on A4
      const y = 50
      
      pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight)
      pdf.save(`${idCardData.short_id}_${appName.replace(/\s+/g, '_')}_ID_Card.pdf`)
      
    } catch (error) {
      console.error('Failed to download ID card:', error)
    } finally {
      setLoading(false)
    }
  }

  // Auto-fetch ID card data when component mounts
  useEffect(() => {
    fetchIdCardData()
  }, [])

  const displayData = idCardData || {
    short_id: user.short_id || 'Loading...',
    full_name: `${user.first_name} ${user.last_name}`,
    email: user.email,
    phone_number: user.phone_number,
    role: user.role,
    qr_code_url: user.qr_code_url,
    profile_picture_url: user.profile_picture_url,
    organization: appName,
    member_since: user.created_at ? new Date(user.created_at).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    }) : 'Unknown',
    address: user.address ? `${user.city}, ${user.state}, ${user.country}` : null
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Your {appName} ID Card</h2>
        <div className="flex gap-2">
          <Button
            onClick={generateNewQrCode}
            disabled={qrLoading}
            variant="outline"
            size="sm"
          >
            {qrLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <QrCode className="h-4 w-4 mr-2" />
            )}
            Generate New QR
          </Button>
          <Button
            onClick={downloadIdCard}
            disabled={loading || !idCardData}
            size="sm"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Download PDF
          </Button>
        </div>
      </div>

      {/* ID Card Preview */}
      <div className="flex justify-center">
        <div
          ref={cardRef}
          className="w-[340px] h-[214px] bg-gradient-to-br from-green-600 to-green-800 rounded-lg shadow-lg overflow-hidden relative"
          style={{ aspectRatio: '85.6/53.98' }}
        >
          {/* Header */}
          <div className="bg-white/20 backdrop-blur-sm px-4 py-2">
            <h3 className="text-white font-bold text-lg">{displayData.organization}</h3>
            <p className="text-white/90 text-sm">Member ID Card</p>
          </div>

          {/* Main Content */}
          <div className="flex p-4 h-full">
            {/* Left Side - Photo and Info */}
            <div className="flex-1 space-y-3">
              {/* Photo */}
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center overflow-hidden">
                {displayData.profile_picture_url ? (
                  <img
                    src={displayData.profile_picture_url}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User className="h-8 w-8 text-gray-400" />
                )}
              </div>

              {/* User Info */}
              <div className="space-y-1">
                <h4 className="text-white font-bold text-sm">{displayData.full_name}</h4>
                <p className="text-white/90 text-xs">ID: {displayData.short_id}</p>
                <Badge variant="secondary" className="text-xs">
                  {displayData.role}
                </Badge>
              </div>

              {/* Contact Info */}
              <div className="space-y-1 text-white/80 text-xs">
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  <span className="truncate">{displayData.email}</span>
                </div>
                {displayData.phone_number && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    <span>{displayData.phone_number}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>Since {displayData.member_since}</span>
                </div>
              </div>
            </div>

            {/* Right Side - QR Code */}
            <div className="flex flex-col items-center justify-center">
              <div className="w-20 h-20 bg-white rounded p-1">
                {displayData.qr_code_url ? (
                  <img
                    src={displayData.qr_code_url}
                    alt="QR Code"
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                    <QrCode className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>
              <p className="text-white/70 text-xs mt-1">Scan for details</p>
            </div>
          </div>

          {/* Footer */}
          <div className="absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm px-4 py-1">
            <p className="text-white/70 text-xs text-center">
              {settings?.organization_address || 'foundation.org'}
            </p>
          </div>
        </div>
      </div>

      {/* ID Card Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            ID Card Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Member ID</label>
              <p className="text-lg font-mono">{displayData.short_id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p>{displayData.full_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Role</label>
              <p className="capitalize">{displayData.role}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Member Since</label>
              <p>{displayData.member_since}</p>
            </div>
            <div className="md:col-span-2">
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p>{displayData.email}</p>
            </div>
            {displayData.phone_number && (
              <div>
                <label className="text-sm font-medium text-gray-500">Phone</label>
                <p>{displayData.phone_number}</p>
              </div>
            )}
            {displayData.address && (
              <div>
                <label className="text-sm font-medium text-gray-500">Address</label>
                <p>{displayData.address}</p>
              </div>
            )}
          </div>

          <div className="border-t pt-4">
            <p className="text-sm text-gray-600">
              This QR code contains your member information and can be scanned to verify your identity 
              as a member of {appName}. Keep this ID card safe and contact support if you need 
              to regenerate your QR code.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 