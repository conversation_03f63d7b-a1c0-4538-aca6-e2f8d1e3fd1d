<?php

namespace App\Services;

use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\PartnerOrganization;
use App\Notifications\ScholarshipEligibilityNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;
use Exception;

class ScholarshipEligibilityService
{
    /**
     * Check scholarship eligibility for a student after progression
     */
    public function checkEligibilityAfterProgression(Student $student, StudentProgression $progression): array
    {
        try {
            $newGrade = $progression->to_grade;
            $category = $this->getScholarshipCategoryForGrade($newGrade);
            
            if (!$category) {
                return [
                    'eligible' => false,
                    'reason' => 'No scholarship category available for grade level',
                    'scholarships' => []
                ];
            }

            // Find available scholarships for the new grade category
            $availableScholarships = $this->getAvailableScholarships($category, $student);

            if ($availableScholarships->isEmpty()) {
                return [
                    'eligible' => false,
                    'reason' => 'No active scholarships available for this category',
                    'scholarships' => []
                ];
            }

            // Check if student has already applied for scholarships in this category this academic year
            $existingApplications = $this->getExistingApplications($student, $category, $progression->academic_year);

            $eligibleScholarships = $availableScholarships->filter(function ($scholarship) use ($existingApplications) {
                return !$existingApplications->contains('scholarship_id', $scholarship->id);
            });

            if ($eligibleScholarships->isEmpty()) {
                return [
                    'eligible' => false,
                    'reason' => 'Student has already applied for all available scholarships in this category',
                    'scholarships' => []
                ];
            }

            Log::info("Student eligible for scholarships after progression", [
                'student_id' => $student->id,
                'progression_id' => $progression->id,
                'new_grade' => $newGrade,
                'category' => $category,
                'eligible_scholarships' => $eligibleScholarships->count()
            ]);

            return [
                'eligible' => true,
                'reason' => 'Student is eligible for new scholarships',
                'category' => $category,
                'scholarships' => $eligibleScholarships->toArray(),
                'progression' => $progression
            ];

        } catch (Exception $e) {
            Log::error("Error checking scholarship eligibility", [
                'student_id' => $student->id,
                'progression_id' => $progression->id,
                'error' => $e->getMessage()
            ]);

            return [
                'eligible' => false,
                'reason' => 'Error checking eligibility: ' . $e->getMessage(),
                'scholarships' => []
            ];
        }
    }

    /**
     * Get all students eligible for scholarships in a partner organization
     */
    public function getEligibleStudentsForOrganization(PartnerOrganization $partnerOrg, string $academicYear = null): array
    {
        $academicYear = $academicYear ?? $this->getCurrentAcademicYear();

        // Get students who have completed progressions this academic year
        $completedProgressions = StudentProgression::with(['student'])
            ->whereHas('student', function ($query) use ($partnerOrg) {
                $query->where('school_id', $partnerOrg->id);
            })
            ->where('academic_year', $academicYear)
            ->where('status', 'completed')
            ->get();

        $eligibleStudents = [];

        foreach ($completedProgressions as $progression) {
            $eligibility = $this->checkEligibilityAfterProgression($progression->student, $progression);
            
            if ($eligibility['eligible']) {
                $eligibleStudents[] = [
                    'student' => $progression->student,
                    'progression' => $progression,
                    'eligibility' => $eligibility
                ];
            }
        }

        return $eligibleStudents;
    }

    /**
     * Automatically create scholarship applications for eligible students
     */
    public function autoCreateApplications(Student $student, array $scholarshipIds, array $options = []): array
    {
        $results = [
            'successful' => [],
            'failed' => []
        ];

        DB::beginTransaction();

        try {
            foreach ($scholarshipIds as $scholarshipId) {
                try {
                    $scholarship = Scholarship::findOrFail($scholarshipId);
                    
                    // Verify student is eligible for this scholarship
                    if (!$this->isStudentEligibleForScholarship($student, $scholarship)) {
                        $results['failed'][] = [
                            'scholarship_id' => $scholarshipId,
                            'error' => 'Student not eligible for this scholarship'
                        ];
                        continue;
                    }

                    // Check if application already exists
                    $existingApplication = ScholarshipApplication::where('student_id', $student->id)
                        ->where('scholarship_id', $scholarshipId)
                        ->first();

                    if ($existingApplication) {
                        $results['failed'][] = [
                            'scholarship_id' => $scholarshipId,
                            'error' => 'Application already exists'
                        ];
                        continue;
                    }

                    // Create application
                    $application = ScholarshipApplication::create([
                        'student_id' => $student->id,
                        'scholarship_id' => $scholarshipId,
                        'category' => $scholarship->category,
                        'status' => 'pending',
                        'application_data' => $this->generateAutoApplicationData($student, $scholarship),
                        'auto_generated' => true,
                        'created_at' => Carbon::now(),
                    ]);

                    $results['successful'][] = [
                        'scholarship_id' => $scholarshipId,
                        'application_id' => $application->id,
                        'scholarship_title' => $scholarship->title
                    ];

                    Log::info("Auto-created scholarship application", [
                        'student_id' => $student->id,
                        'scholarship_id' => $scholarshipId,
                        'application_id' => $application->id
                    ]);

                } catch (Exception $e) {
                    $results['failed'][] = [
                        'scholarship_id' => $scholarshipId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Failed to auto-create applications", [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }

        return $results;
    }

    /**
     * Send eligibility notifications to partner organizations
     */
    public function sendEligibilityNotifications(PartnerOrganization $partnerOrg, array $eligibleStudents): void
    {
        try {
            if (empty($eligibleStudents)) {
                return;
            }

            // Get partner organization users who should receive notifications
            $users = $partnerOrg->users()->where('role', 'partner_organization')->get();

            foreach ($users as $user) {
                Notification::send($user, new ScholarshipEligibilityNotification($eligibleStudents, $partnerOrg));
            }

            Log::info("Sent scholarship eligibility notifications", [
                'partner_org_id' => $partnerOrg->id,
                'eligible_students_count' => count($eligibleStudents),
                'notified_users_count' => $users->count()
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send eligibility notifications", [
                'partner_org_id' => $partnerOrg->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Run daily eligibility check for all partner organizations
     */
    public function runDailyEligibilityCheck(): array
    {
        $results = [
            'processed_organizations' => 0,
            'total_eligible_students' => 0,
            'notifications_sent' => 0,
            'errors' => []
        ];

        try {
            $partnerOrgs = PartnerOrganization::where('status', 'active')->get();

            foreach ($partnerOrgs as $partnerOrg) {
                try {
                    $eligibleStudents = $this->getEligibleStudentsForOrganization($partnerOrg);
                    
                    if (!empty($eligibleStudents)) {
                        $this->sendEligibilityNotifications($partnerOrg, $eligibleStudents);
                        $results['notifications_sent']++;
                        $results['total_eligible_students'] += count($eligibleStudents);
                    }

                    $results['processed_organizations']++;

                } catch (Exception $e) {
                    $results['errors'][] = [
                        'partner_org_id' => $partnerOrg->id,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info("Daily eligibility check completed", $results);

        } catch (Exception $e) {
            Log::error("Failed to run daily eligibility check", [
                'error' => $e->getMessage()
            ]);
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Get scholarship category for a grade level
     */
    private function getScholarshipCategoryForGrade(string $gradeLevel): ?string
    {
        $gradeCategories = [
            'primary_1' => 'primary',
            'primary_2' => 'primary',
            'primary_3' => 'primary',
            'primary_4' => 'primary',
            'primary_5' => 'primary',
            'primary_6' => 'primary',
            'secondary_1' => 'secondary',
            'secondary_2' => 'secondary',
            'secondary_3' => 'secondary',
            'secondary_4' => 'secondary',
            'secondary_5' => 'secondary',
            'secondary_6' => 'secondary',
            'university' => 'university',
        ];

        return $gradeCategories[$gradeLevel] ?? null;
    }

    /**
     * Get available scholarships for a category
     */
    private function getAvailableScholarships(string $category, Student $student = null)
    {
        return Scholarship::where('category', $category)
            ->where('status', 'active')
            ->where('application_deadline', '>', Carbon::now())
            ->get();
    }

    /**
     * Get existing applications for a student in a category
     */
    private function getExistingApplications(Student $student, string $category, string $academicYear)
    {
        return ScholarshipApplication::where('student_id', $student->id)
            ->where('category', $category)
            ->whereYear('created_at', '>=', (int) substr($academicYear, 0, 4))
            ->get();
    }

    /**
     * Check if student is eligible for a specific scholarship
     */
    private function isStudentEligibleForScholarship(Student $student, Scholarship $scholarship): bool
    {
        // Check category match
        $studentCategory = $this->getScholarshipCategoryForGrade($student->grade_level);
        if ($studentCategory !== $scholarship->category) {
            return false;
        }

        // Check if scholarship is active and deadline hasn't passed
        if ($scholarship->status !== 'active' || $scholarship->application_deadline <= Carbon::now()) {
            return false;
        }

        // Check if student already has an application for this scholarship
        $existingApplication = ScholarshipApplication::where('student_id', $student->id)
            ->where('scholarship_id', $scholarship->id)
            ->exists();

        return !$existingApplication;
    }

    /**
     * Generate auto application data for a student
     */
    private function generateAutoApplicationData(Student $student, Scholarship $scholarship): array
    {
        return [
            'auto_generated' => true,
            'student_name' => $student->full_name,
            'grade_level' => $student->grade_level,
            'school_name' => $student->school->name ?? 'Unknown School',
            'application_reason' => 'Automatically generated application based on grade progression',
            'generated_at' => Carbon::now()->toISOString(),
        ];
    }

    /**
     * Get current academic year
     */
    private function getCurrentAcademicYear(): string
    {
        $currentDate = Carbon::now();
        $year = $currentDate->year;
        
        // Academic year typically starts in September
        if ($currentDate->month >= 9) {
            return $year . '/' . ($year + 1);
        } else {
            return ($year - 1) . '/' . $year;
        }
    }
}
