@extends('layouts.admin')

@section('title', 'Create New Event')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Event</h1>
        <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Events
        </a>
    </div>

    <!-- Display Validation Errors -->
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.events.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Event Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Event Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="5" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Event Type and Category -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_type" class="form-label">Event Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('event_type') is-invalid @enderror" id="event_type" name="event_type" required>
                                        <option value="">Select Event Type</option>
                                        <option value="fundraising" {{ old('event_type') == 'fundraising' ? 'selected' : '' }}>Fundraising</option>
                                        <option value="volunteer" {{ old('event_type') == 'volunteer' ? 'selected' : '' }}>Volunteer Activity</option>
                                        <option value="awareness" {{ old('event_type') == 'awareness' ? 'selected' : '' }}>Awareness Campaign</option>
                                        <option value="training" {{ old('event_type') == 'training' ? 'selected' : '' }}>Training Workshop</option>
                                        <option value="other" {{ old('event_type') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('event_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                        <option value="draft" {{ old('status', 'published') == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="published" {{ old('status', 'published') == 'published' ? 'selected' : '' }}>Published</option>
                                        <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_datetime" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control @error('start_datetime') is-invalid @enderror"
                                           id="start_datetime" name="start_datetime" value="{{ old('start_datetime') }}" required>
                                    @error('start_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_datetime" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control @error('end_datetime') is-invalid @enderror"
                                           id="end_datetime" name="end_datetime" value="{{ old('end_datetime') }}" required>
                                    @error('end_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="mb-3">
                            <label for="venue" class="form-label">Venue Name</label>
                            <input type="text" class="form-control @error('venue') is-invalid @enderror"
                                   id="venue" name="venue" value="{{ old('venue') }}" placeholder="e.g., Community Center Hall">
                            @error('venue')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Full Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror"
                                      id="address" name="address" rows="3" placeholder="Complete address with landmarks">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Event Capacity and Registration -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Event Capacity</label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                           id="capacity" name="capacity" value="{{ old('capacity') }}" min="1" placeholder="Maximum number of attendees">
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registration_deadline" class="form-label">Registration Deadline</label>
                                    <input type="datetime-local" class="form-control @error('registration_deadline') is-invalid @enderror"
                                           id="registration_deadline" name="registration_deadline" value="{{ old('registration_deadline') }}">
                                    @error('registration_deadline')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Requirements and Agenda -->
                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements</label>
                            <textarea class="form-control @error('requirements') is-invalid @enderror"
                                      id="requirements" name="requirements" rows="3" placeholder="What attendees need to bring or prepare">{{ old('requirements') }}</textarea>
                            @error('requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="agenda" class="form-label">Event Agenda</label>
                            <textarea class="form-control @error('agenda') is-invalid @enderror"
                                      id="agenda" name="agenda" rows="4" placeholder="Detailed schedule and activities">{{ old('agenda') }}</textarea>
                            @error('agenda')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Event
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Featured Image Upload -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="imagePreview" src="{{ asset('img/default-event.svg') }}" alt="Event Preview" class="img-fluid rounded" style="width: 100%; height: 200px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control @error('featured_image') is-invalid @enderror"
                               id="featured_image" name="featured_image" accept="image/*" onchange="previewImage(this)">
                        @error('featured_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <small class="text-muted">Recommended size: 800x400px</small>
                </div>
            </div>

            <!-- Event Guidelines -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Use clear, descriptive event titles
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Include complete location details
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Set realistic capacity limits
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Provide detailed agenda if applicable
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Upload high-quality featured image
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Quick Preview -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Preview</h6>
                </div>
                <div class="card-body">
                    <div id="preview-content">
                        <h6 id="preview-title" class="text-muted">Event title will appear here</h6>
                        <p id="preview-type" class="small text-info">Event type</p>
                        <p id="preview-datetime" class="small text-secondary">Date & time will appear here</p>
                        <p id="preview-venue" class="small text-secondary">Venue will appear here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const typeInput = document.getElementById('event_type');
    const startInput = document.getElementById('start_datetime');
    const venueInput = document.getElementById('venue');
    
    titleInput.addEventListener('input', function() {
        document.getElementById('preview-title').textContent = this.value || 'Event title will appear here';
    });
    
    typeInput.addEventListener('change', function() {
        document.getElementById('preview-type').textContent = this.options[this.selectedIndex].text;
    });
    
    startInput.addEventListener('change', function() {
        if (this.value) {
            const date = new Date(this.value);
            document.getElementById('preview-datetime').textContent = date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
        }
    });
    
    venueInput.addEventListener('input', function() {
        document.getElementById('preview-venue').textContent = this.value || 'Venue will appear here';
    });
});
</script>
@endsection 