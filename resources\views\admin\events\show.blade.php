@extends('layouts.admin')

@section('title', 'Event Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Event Details</h1>
        <div>
            <a href="{{ route('admin.events.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Events
            </a>
            <a href="{{ route('admin.events.edit', $event->id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Event
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Event Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h4 class="text-primary">{{ $event->title }}</h4>
                        <span class="badge bg-secondary mb-3">{{ ucfirst(str_replace('_', ' ', $event->event_type)) }}</span>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted">Description</h6>
                        <p class="text-justify">{{ $event->description }}</p>
                    </div>

                    <!-- Event Schedule -->
                    <div class="mb-4">
                        <h6 class="text-muted">Schedule</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-play-circle text-success me-2"></i>
                                    <div>
                                        <small class="text-muted">Start</small>
                                        <p class="mb-0">{{ $event->start_datetime->format('F j, Y g:i A') }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-stop-circle text-danger me-2"></i>
                                    <div>
                                        <small class="text-muted">End</small>
                                        <p class="mb-0">{{ $event->end_datetime->format('F j, Y g:i A') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if($event->registration_deadline)
                        <div class="mt-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock text-warning me-2"></i>
                                <div>
                                    <small class="text-muted">Registration Deadline</small>
                                    <p class="mb-0">{{ $event->registration_deadline->format('F j, Y g:i A') }}</p>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Location -->
                    @if($event->venue || $event->address)
                    <div class="mb-4">
                        <h6 class="text-muted">Location</h6>
                        @if($event->venue)
                            <p class="mb-1"><i class="fas fa-building text-primary me-2"></i>{{ $event->venue }}</p>
                        @endif
                        @if($event->address)
                            <p class="mb-0"><i class="fas fa-map-marker-alt text-danger me-2"></i>{{ $event->address }}</p>
                        @endif
                    </div>
                    @endif

                    <!-- Requirements -->
                    @if($event->requirements && count($event->requirements) > 0)
                    <div class="mb-4">
                        <h6 class="text-muted">Requirements</h6>
                        <ul class="list-unstyled">
                            @foreach($event->requirements as $requirement)
                                <li class="mb-1"><i class="fas fa-check-circle text-success me-2"></i>{{ $requirement }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <!-- Agenda -->
                    @if($event->agenda && count($event->agenda) > 0)
                    <div class="mb-4">
                        <h6 class="text-muted">Event Agenda</h6>
                        <ul class="list-unstyled">
                            @foreach($event->agenda as $item)
                                <li class="mb-1"><i class="fas fa-calendar-check text-info me-2"></i>{{ $item }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Featured Image Card -->
            @if($event->featured_image)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Featured Image</h6>
                </div>
                <div class="card-body text-center">
                    <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}" class="img-fluid rounded" style="max-height: 400px;">
                </div>
            </div>
            @endif

            <!-- Registrations Card -->
            @if($event->registrations && $event->registrations->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Registrations</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Registered</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($event->registrations->take(10) as $registration)
                                <tr>
                                    <td>{{ $registration->user->first_name }} {{ $registration->user->last_name }}</td>
                                    <td>{{ $registration->user->email }}</td>
                                    <td>{{ $registration->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($registration->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @if($event->registrations->count() > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.events.registrations', $event->id) }}" class="btn btn-outline-primary btn-sm">
                            View All {{ $event->registrations->count() }} Registrations
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Status</h6>
                </div>
                <div class="card-body text-center">
                    <span class="badge bg-{{ $event->status === 'published' ? 'success' : ($event->status === 'cancelled' ? 'danger' : 'warning') }} p-3 mb-3" style="font-size: 1.2rem;">
                        <i class="fas fa-{{ $event->status === 'published' ? 'check' : ($event->status === 'cancelled' ? 'times' : 'clock') }} me-2"></i>
                        {{ ucfirst($event->status) }}
                    </span>
                    <p class="text-muted mb-0">Current event status</p>
                </div>
            </div>

            <!-- Capacity Card -->
            @if($event->capacity)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Capacity</h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-primary mb-2">{{ $event->registrations ? $event->registrations->count() : 0 }} / {{ $event->capacity }}</h3>
                    <p class="text-muted mb-0">Registered Participants</p>
                    <div class="progress mt-2">
                        <div class="progress-bar" role="progressbar" style="width: {{ $event->capacity > 0 ? (($event->registrations ? $event->registrations->count() : 0) / $event->capacity) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Event Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Event ID</small>
                        <p class="mb-0">#{{ $event->id }}</p>
                    </div>
                    @if($event->slug)
                    <div class="mb-3">
                        <small class="text-muted">Slug</small>
                        <p class="mb-0">{{ $event->slug }}</p>
                    </div>
                    @endif
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0">{{ $event->created_at->format('F j, Y g:i A') }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0">{{ $event->updated_at->format('F j, Y g:i A') }}</p>
                    </div>
                    @if($event->organizer)
                    <div class="mb-3">
                        <small class="text-muted">Organizer</small>
                        <p class="mb-0">{{ $event->organizer->first_name }} {{ $event->organizer->last_name }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.events.edit', $event->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Event
                        </a>
                        @if($event->registrations && $event->registrations->count() > 0)
                        <a href="{{ route('admin.events.registrations', $event->id) }}" class="btn btn-outline-info">
                            <i class="fas fa-users me-2"></i>View Registrations
                        </a>
                        @endif
                        @if($event->status !== 'published')
                        <form method="POST" action="{{ route('admin.events.update', $event->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="status" value="published">
                            <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to publish this event?')">
                                <i class="fas fa-check me-2"></i>Publish Event
                            </button>
                        </form>
                        @endif
                        @if($event->status === 'published')
                        <form method="POST" action="{{ route('admin.events.update', $event->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="status" value="cancelled">
                            <button type="submit" class="btn btn-outline-warning w-100" onclick="return confirm('Are you sure you want to cancel this event?')">
                                <i class="fas fa-times me-2"></i>Cancel Event
                            </button>
                        </form>
                        @endif
                        <form method="POST" action="{{ route('admin.events.destroy', $event->id) }}" style="display: inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this event? This action cannot be undone.')">
                                <i class="fas fa-trash me-2"></i>Delete Event
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
