<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    /**
     * Show the registration form.
     */
    public function showRegistrationForm()
    {
        return view('auth.register');
    }

    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle user registration.
     */
    public function register(Request $request)
    {
        // Debug logging
        \Log::info('Registration attempt', [
            'data' => $request->all(),
            'ip' => $request->ip()
        ]);

        $validator = $this->getRegistrationValidator($request->all());

        if ($validator->fails()) {
            \Log::warning('Registration validation failed', [
                'errors' => $validator->errors()->toArray(),
                'data' => $request->all()
            ]);
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $user = $this->createUser($request->all());
            \Log::info('User created successfully', ['user_id' => $user->id]);

            // Don't auto-login, redirect to login page instead
            \Log::info('User registration completed, redirecting to login page');

            // Redirect to login page with success message
            return redirect()->route('login')
                ->with('success', 'Account created successfully! Please login to access your dashboard.');
        } catch (\Exception $e) {
            \Log::error('Registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()
                ->with('error', 'Registration failed. Please try again.')
                ->withInput();
        }
    }

    /**
     * Handle user login.
     */
    public function login(Request $request)
    {
        // Debug logging
        \Log::info('Login attempt', [
            'email' => $request->email,
            'ip' => $request->ip()
        ]);

        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            $user = Auth::user();
            \Log::info('Login successful', [
                'user_id' => $user->id,
                'role_id' => $user->role_id,
                'dashboard_route' => $user->dashboard_route
            ]);

            // Update last login
            $user->update(['last_login_at' => now()]);

            // Redirect directly to user's dashboard (not intended route)
            \Log::info('Redirecting user to dashboard', [
                'user_id' => $user->id,
                'dashboard_route' => $user->dashboard_route
            ]);

            return redirect($user->dashboard_route)
                ->with('success', 'Welcome back, ' . $user->first_name . '!');
        }

        \Log::warning('Login failed', ['email' => $request->email]);
        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->withInput($request->only('email'));
    }

    /**
     * Handle user logout.
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Get the registration validator.
     */
    protected function getRegistrationValidator(array $data)
    {
        $rules = [
            'role_id' => ['required', 'integer', 'in:1,2,3'],
            'first_name' => ['required', 'string', 'max:50'],
            'last_name' => ['required', 'string', 'max:50'],
            'email' => ['required', 'string', 'email', 'max:100', 'unique:users'],
            'phone_number' => ['required', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Password::min(8)],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'string', 'in:male,female,other'],
            'address' => ['nullable', 'string', 'max:500'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
        ];

        // Role-specific validation rules
        if (isset($data['role_id'])) {
            switch ($data['role_id']) {
                case '2': // University Student
                    $rules = array_merge($rules, [
                        'student_id' => ['nullable', 'string', 'max:50'],
                        'matriculation_number' => ['nullable', 'string', 'max:50'],
                        'university_name' => ['required', 'string', 'max:100'],
                        'course_of_study' => ['required', 'string', 'max:100'],
                        'year_of_study' => ['nullable', 'integer', 'min:1', 'max:6'],
                        'cgpa' => ['nullable', 'numeric', 'min:0', 'max:5'],
                    ]);
                    break;

                case '3': // Partner Organization
                    $rules = array_merge($rules, [
                        'organization_name' => ['required', 'string', 'max:100'],
                        'organization_type' => ['required', 'string', 'in:primary_school,secondary_school,college,ngo,other'],
                        'organization_registration_number' => ['nullable', 'string', 'max:50'],
                        'organization_address' => ['required', 'string', 'max:500'],
                        'organization_phone' => ['nullable', 'string', 'max:20'],
                        'organization_email' => ['nullable', 'email', 'max:100'],
                        'principal_name' => ['nullable', 'string', 'max:100'],
                        'principal_phone' => ['nullable', 'string', 'max:20'],
                        'principal_email' => ['nullable', 'email', 'max:100'],
                    ]);
                    break;
            }
        }

        return Validator::make($data, $rules);
    }

    /**
     * Create a new user instance.
     */
    protected function createUser(array $data)
    {
        $userData = [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'password' => Hash::make($data['password']),
            'role_id' => $data['role_id'],
            'role' => $this->getRoleString($data['role_id']),
            'date_of_birth' => $data['date_of_birth'] ?? null,
            'gender' => $data['gender'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => 'Nigeria', // Default country
            'status' => 'active',
        ];

        // Add role-specific data
        switch ($data['role_id']) {
            case '2': // University Student
                $userData = array_merge($userData, [
                    'student_id' => $data['student_id'] ?? null,
                    'matriculation_number' => $data['matriculation_number'] ?? null,
                    'university_name' => $data['university_name'] ?? null,
                    'course_of_study' => $data['course_of_study'] ?? null,
                    'year_of_study' => $data['year_of_study'] ?? null,
                    'cgpa' => $data['cgpa'] ?? null,
                ]);
                break;

            case '3': // Partner Organization
                $userData = array_merge($userData, [
                    'organization_name' => $data['organization_name'] ?? null,
                    'organization_type' => $data['organization_type'] ?? null,
                    'organization_registration_number' => $data['organization_registration_number'] ?? null,
                    'organization_address' => $data['organization_address'] ?? null,
                    'organization_phone' => $data['organization_phone'] ?? null,
                    'organization_email' => $data['organization_email'] ?? null,
                    'principal_name' => $data['principal_name'] ?? null,
                    'principal_phone' => $data['principal_phone'] ?? null,
                    'principal_email' => $data['principal_email'] ?? null,
                ]);
                break;
        }

        $user = User::create($userData);

        // Generate short ID and QR code (these are handled automatically by model events)
        // The short_id and qr_code_url are generated in the User model's boot method

        return $user;
    }

    /**
     * Get role string from role ID.
     */
    protected function getRoleString($roleId)
    {
        return match($roleId) {
            1 => 'user',
            2 => 'user', // University students are still 'user' role in old system
            3 => 'partner_organization',
            4 => 'admin',
            default => 'user'
        };
    }
}
