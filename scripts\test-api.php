#!/usr/bin/env php
<?php

/**
 * NGO Platform Multi-Tier API Testing Script
 * 
 * This script runs comprehensive tests for the multi-tier scholarship system API
 * including role-based authorization, endpoint functionality, and data validation.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;

class ApiTestRunner
{
    private $testResults = [];
    private $totalTests = 0;
    private $passedTests = 0;
    private $failedTests = 0;

    public function __construct()
    {
        echo "\n🎓 NGO Platform Multi-Tier API Test Suite\n";
        echo "==========================================\n\n";
    }

    public function runAllTests()
    {
        $this->runTestSuite('Partner Dashboard API Tests', 'tests/Feature/Api/PartnerDashboardApiTest.php');
        $this->runTestSuite('Student Progression API Tests', 'tests/Feature/Api/StudentProgressionApiTest.php');
        $this->runTestSuite('Multi-Year Application API Tests', 'tests/Feature/Api/MultiYearApplicationApiTest.php');
        $this->runTestSuite('Role-Based Authorization Tests', 'tests/Feature/Api/RoleBasedAuthTest.php');
        
        $this->displaySummary();
    }

    private function runTestSuite($suiteName, $testFile)
    {
        echo "🧪 Running: {$suiteName}\n";
        echo str_repeat('-', 50) . "\n";

        $command = "php artisan test {$testFile} --verbose";
        $output = [];
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $this->parseTestResults($suiteName, $output, $returnCode);
        echo "\n";
    }

    private function parseTestResults($suiteName, $output, $returnCode)
    {
        $suiteResults = [
            'name' => $suiteName,
            'passed' => 0,
            'failed' => 0,
            'total' => 0,
            'status' => $returnCode === 0 ? 'PASSED' : 'FAILED',
            'details' => []
        ];

        foreach ($output as $line) {
            if (strpos($line, 'PASS') !== false) {
                $suiteResults['passed']++;
                $suiteResults['total']++;
                $this->passedTests++;
                $this->totalTests++;
                echo "  ✅ " . trim(str_replace('PASS', '', $line)) . "\n";
            } elseif (strpos($line, 'FAIL') !== false) {
                $suiteResults['failed']++;
                $suiteResults['total']++;
                $this->failedTests++;
                $this->totalTests++;
                echo "  ❌ " . trim(str_replace('FAIL', '', $line)) . "\n";
            } elseif (strpos($line, 'Tests:') !== false) {
                echo "  📊 " . trim($line) . "\n";
            }
        }

        $this->testResults[] = $suiteResults;

        $statusIcon = $returnCode === 0 ? '✅' : '❌';
        echo "\n{$statusIcon} {$suiteName}: {$suiteResults['passed']} passed, {$suiteResults['failed']} failed\n";
    }

    private function displaySummary()
    {
        echo "\n" . str_repeat('=', 60) . "\n";
        echo "📋 TEST SUMMARY\n";
        echo str_repeat('=', 60) . "\n\n";

        foreach ($this->testResults as $result) {
            $statusIcon = $result['status'] === 'PASSED' ? '✅' : '❌';
            echo "{$statusIcon} {$result['name']}: {$result['passed']}/{$result['total']} tests passed\n";
        }

        echo "\n" . str_repeat('-', 60) . "\n";
        echo "🎯 OVERALL RESULTS:\n";
        echo "   Total Tests: {$this->totalTests}\n";
        echo "   Passed: {$this->passedTests}\n";
        echo "   Failed: {$this->failedTests}\n";
        
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 1) : 0;
        echo "   Success Rate: {$successRate}%\n";

        if ($this->failedTests === 0) {
            echo "\n🎉 ALL TESTS PASSED! The Multi-Tier API is working correctly.\n";
        } else {
            echo "\n⚠️  Some tests failed. Please review the output above for details.\n";
        }

        echo "\n📚 API Documentation: /api-docs\n";
        echo "🔧 Test Coverage Areas:\n";
        echo "   • Role-based authentication and authorization\n";
        echo "   • Partner Organization dashboard functionality\n";
        echo "   • Student progression tracking and analytics\n";
        echo "   • Multi-year application management\n";
        echo "   • Category-specific access control\n";
        echo "   • Data validation and error handling\n";
        echo "\n" . str_repeat('=', 60) . "\n";
    }

    public function runSpecificTests($category)
    {
        switch (strtolower($category)) {
            case 'auth':
                $this->runTestSuite('Role-Based Authorization Tests', 'tests/Feature/Api/RoleBasedAuthTest.php');
                break;
            case 'partner':
                $this->runTestSuite('Partner Dashboard API Tests', 'tests/Feature/Api/PartnerDashboardApiTest.php');
                break;
            case 'progression':
                $this->runTestSuite('Student Progression API Tests', 'tests/Feature/Api/StudentProgressionApiTest.php');
                break;
            case 'applications':
                $this->runTestSuite('Multi-Year Application API Tests', 'tests/Feature/Api/MultiYearApplicationApiTest.php');
                break;
            default:
                echo "❌ Unknown test category: {$category}\n";
                echo "Available categories: auth, partner, progression, applications\n";
                return;
        }
        
        $this->displaySummary();
    }
}

// Main execution
if (php_sapi_name() === 'cli') {
    $testRunner = new ApiTestRunner();
    
    $category = $argv[1] ?? 'all';
    
    if ($category === 'all') {
        $testRunner->runAllTests();
    } else {
        $testRunner->runSpecificTests($category);
    }
}
