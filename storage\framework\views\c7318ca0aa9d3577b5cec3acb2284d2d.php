

<?php $__env->startSection('title', 'HLTKKQ Foundation - Transforming Lives, Building Communities'); ?>
<?php $__env->startSection('description', 'Join HLTKKQ Foundation in making a difference across Nigeria through education, healthcare, youth empowerment, and community development programs.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6 hero-content">
                <h1 class="display-4 fw-bold mb-4 font-display">
                    Transforming Lives, Building <span class="text-warning">Communities</span>
                </h1>
                <p class="lead mb-4">
                    Join us in creating lasting change across Nigeria through education, healthcare, youth empowerment, and sustainable community development programs that matter.
                </p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="<?php echo e(route('donate')); ?>" class="btn btn-warning btn-lg">
                        <i class="fas fa-heart me-2"></i>Donate Now
                    </a>
                    <a href="<?php echo e(route('volunteer')); ?>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-hands-helping me-2"></i>Volunteer With Us
                    </a>
                </div>
                
                <!-- Quick Stats -->
                <div class="row mt-5">
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="fw-bold mb-0"><?php echo e(number_format($stats['beneficiaries'])); ?>+</h3>
                            <small class="text-light">Lives Touched</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="fw-bold mb-0"><?php echo e($stats['programs']); ?>+</h3>
                            <small class="text-light">Active Programs</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="fw-bold mb-0"><?php echo e($stats['volunteers']); ?>+</h3>
                            <small class="text-light">Volunteers</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="position-relative">
                    <img src="https://images.unsplash.com/photo-1559027615-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                         alt="Community Impact" 
                         class="img-fluid rounded-3 shadow-lg">
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <button class="btn btn-light btn-lg rounded-circle p-3" onclick="playVideo()">
                            <i class="fas fa-play text-primary"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Impact Statistics -->
<section class="py-5 bg-light-custom">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="stats-card">
                    <div class="stats-number"><?php echo e(number_format($stats['beneficiaries'])); ?>+</div>
                    <h6 class="fw-semibold">Lives Transformed</h6>
                    <p class="text-muted small">Through our comprehensive programs</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="stats-card">
                    <div class="stats-number"><?php echo e($stats['programs']); ?></div>
                    <h6 class="fw-semibold">Active Programs</h6>
                    <p class="text-muted small">Across education, health & development</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="stats-card">
                    <div class="stats-number"><?php echo e($stats['volunteers']); ?>+</div>
                    <h6 class="fw-semibold">Dedicated Volunteers</h6>
                    <p class="text-muted small">Making a difference every day</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="stats-card">
                    <div class="stats-number">12+</div>
                    <h6 class="fw-semibold">States Reached</h6>
                    <p class="text-muted small">Expanding our impact nationwide</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Programs -->
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4 font-display">Our Impact Programs</h2>
                <p class="lead text-muted">Discover how we're creating sustainable change across Nigeria through targeted programs that address real community needs.</p>
            </div>
        </div>
        
        <div class="row">
            <?php $__empty_1 = true; $__currentLoopData = $featuredPrograms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="card h-100">
                    <img src="<?php echo e($program->featured_image ?? 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'); ?>" 
                         alt="<?php echo e($program->title); ?>" 
                         class="card-img-top">
                    <div class="card-body">
                        <span class="badge bg-primary mb-2"><?php echo e(ucfirst($program->status)); ?></span>
                        <h5 class="card-title"><?php echo e($program->title); ?></h5>
                        <p class="card-text text-muted"><?php echo e(Str::limit($program->description, 120)); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-secondary-custom fw-semibold">₦<?php echo e(number_format($program->budget)); ?></span>
                            <a href="<?php echo e(route('programs.show', $program->slug)); ?>" class="btn btn-outline-primary btn-sm">
                                Learn More <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12 text-center">
                <p class="text-muted">No active programs at the moment. Check back soon!</p>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo e(route('programs')); ?>" class="btn btn-primary btn-lg">
                View All Programs <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Upcoming Events -->
<?php if($upcomingEvents->count() > 0): ?>
<section class="py-5 bg-light-custom">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4 font-display">Upcoming Events</h2>
                <p class="lead text-muted">Join us at our upcoming events and be part of positive change in your community.</p>
            </div>
        </div>
        
        <div class="row">
            <?php $__currentLoopData = $upcomingEvents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary text-white rounded p-2 me-3">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div>
                                <h6 class="mb-0"><?php echo e($event->start_datetime->format('M d, Y')); ?></h6>
                                <small class="text-muted"><?php echo e($event->start_datetime->format('g:i A')); ?></small>
                            </div>
                        </div>
                        <h5 class="card-title"><?php echo e($event->title); ?></h5>
                        <p class="card-text text-muted"><?php echo e(Str::limit($event->description, 100)); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-secondary-custom fw-semibold">
                                <i class="fas fa-map-marker-alt me-1"></i><?php echo e($event->venue); ?>

                            </span>
                            <a href="<?php echo e(route('event.show', $event->id)); ?>" class="btn btn-outline-primary btn-sm">
                                Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo e(route('events')); ?>" class="btn btn-primary btn-lg">
                View All Events <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Active Campaigns -->
<?php if($activeCampaigns->count() > 0): ?>
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4 font-display">Active Campaigns</h2>
                <p class="lead text-muted">Support our current fundraising campaigns and help us reach our goals for community impact.</p>
            </div>
        </div>
        
        <div class="row">
            <?php $__currentLoopData = $activeCampaigns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campaign): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-6 mb-4 fade-in">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title"><?php echo e($campaign->title); ?></h5>
                            <span class="badge bg-secondary-custom"><?php echo e($campaign->category); ?></span>
                        </div>
                        <p class="card-text text-muted"><?php echo e(Str::limit($campaign->description, 150)); ?></p>
                        
                        <!-- Progress Bar -->
                        <?php
                            $percentage = $campaign->goal_amount > 0 ? ($campaign->current_amount / $campaign->goal_amount) * 100 : 0;
                        ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-semibold">₦<?php echo e(number_format($campaign->current_amount)); ?></span>
                                <span class="text-muted">₦<?php echo e(number_format($campaign->goal_amount)); ?></span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-secondary-custom" style="width: <?php echo e($percentage); ?>%"></div>
                            </div>
                            <small class="text-muted"><?php echo e(number_format($percentage, 1)); ?>% funded</small>
                        </div>
                        
                        <a href="<?php echo e(route('donate')); ?>?campaign=<?php echo e($campaign->id); ?>" class="btn btn-secondary w-100">
                            <i class="fas fa-heart me-2"></i>Support This Campaign
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Recent Blog Posts -->
<?php if($recentPosts->count() > 0): ?>
<section class="py-5 bg-light-custom">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4 font-display">Latest Stories</h2>
                <p class="lead text-muted">Read inspiring stories and updates from our community impact initiatives.</p>
            </div>
        </div>
        
        <div class="row">
            <?php $__currentLoopData = $recentPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="card h-100">
                    <img src="<?php echo e($post->featured_image ?? 'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'); ?>" 
                         alt="<?php echo e($post->title); ?>" 
                         class="card-img-top">
                    <div class="card-body">
                        <span class="badge bg-primary mb-2"><?php echo e($post->category->name ?? 'General'); ?></span>
                        <h5 class="card-title"><?php echo e($post->title); ?></h5>
                        <p class="card-text text-muted"><?php echo e(Str::limit($post->excerpt ?? strip_tags($post->content), 120)); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted"><?php echo e($post->published_at?->format('M d, Y')); ?></small>
                            <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="btn btn-outline-primary btn-sm">
                                Read More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo e(route('blog.index')); ?>" class="btn btn-primary btn-lg">
                Read More Stories <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action -->
<section class="py-5 text-white" style="background: linear-gradient(135deg, var(--secondary-color) 0%, #059669 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="display-5 fw-bold mb-3 font-display">Ready to Make a Difference?</h2>
                <p class="lead mb-0">Join thousands of Nigerians who are already creating positive change in their communities. Every contribution matters.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex flex-column gap-3">
                    <a href="<?php echo e(route('volunteer')); ?>" class="btn btn-light btn-lg">
                        <i class="fas fa-hands-helping me-2"></i>Become a Volunteer
                    </a>
                    <a href="<?php echo e(route('donate')); ?>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-heart me-2"></i>Make a Donation
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Signup -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h3 class="fw-bold mb-3">Stay Connected</h3>
                <p class="text-muted mb-4">Get updates on our programs, events, and impact stories delivered to your inbox.</p>
                
                <form class="row g-3 justify-content-center" id="newsletter-form">
                    <div class="col-md-6">
                        <input type="email" class="form-control form-control-lg" placeholder="Enter your email address" required>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-envelope me-2"></i>Subscribe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function playVideo() {
    // Placeholder for video modal or player
    alert('Video player would open here with foundation introduction video');
}

// Newsletter form submission
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = this.querySelector('input[type="email"]').value;
    
    // Make API call to subscribe
    fetch('/api/v1/newsletter/subscribe', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Thank you for subscribing to our newsletter!');
            this.reset();
        } else {
            alert(data.message || 'Subscription failed. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
    });
});

// Counter animation for statistics
function animateCounters() {
    const counters = document.querySelectorAll('.stats-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target.toLocaleString() + '+';
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current).toLocaleString() + '+';
            }
        }, 20);
    });
}

// Trigger counter animation when stats section is visible
const statsSection = document.querySelector('.stats-card');
if (statsSection) {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    });
    
    observer.observe(statsSection);
}
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/home.blade.php ENDPATH**/ ?>