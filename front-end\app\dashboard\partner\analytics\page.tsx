"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Users,
  Award,
  Calendar,
  Target,
  CheckCircle,
  Clock,
  XCircle,
  Download,
  Filter,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  Activity
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

// TypeScript interfaces
interface AnalyticsData {
  overview: {
    total_students: number
    total_applications: number
    success_rate: number
    total_amount_awarded: number
    active_scholarships: number
    pending_applications: number
  }
  application_trends: Array<{
    month: string
    applications: number
    approved: number
    rejected: number
  }>
  success_by_category: Array<{
    category: string
    total: number
    approved: number
    success_rate: number
  }>
  grade_distribution: Array<{
    grade: string
    students: number
    applications: number
  }>
  monthly_progression: Array<{
    month: string
    progressions: number
    completed: number
  }>
  top_performing_students: Array<{
    id: number
    name: string
    grade: string
    applications: number
    approved: number
    total_amount: number
  }>
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

export default function PartnerAnalyticsPage() {
  const { user, token } = useAuth()
  const [loading, setLoading] = useState(true)
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState('12months')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    if (!token) return

    try {
      setLoading(true)
      const response = await fetch(`${API_BASE}/api/v1/partner-analytics?period=${selectedPeriod}&category=${selectedCategory}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data')
      }

      const data = await response.json()
      if (data.success) {
        setAnalyticsData(data.data)
      } else {
        throw new Error(data.message || 'Failed to load analytics')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to load analytics data')
      // Set mock data for development
      setAnalyticsData({
        overview: {
          total_students: 245,
          total_applications: 89,
          success_rate: 67.4,
          total_amount_awarded: 2450000,
          active_scholarships: 12,
          pending_applications: 15
        },
        application_trends: [
          { month: 'Jan', applications: 12, approved: 8, rejected: 4 },
          { month: 'Feb', applications: 15, approved: 10, rejected: 5 },
          { month: 'Mar', applications: 18, approved: 12, rejected: 6 },
          { month: 'Apr', applications: 22, approved: 15, rejected: 7 },
          { month: 'May', applications: 20, approved: 14, rejected: 6 },
          { month: 'Jun', applications: 25, approved: 17, rejected: 8 }
        ],
        success_by_category: [
          { category: 'Primary', total: 35, approved: 25, success_rate: 71.4 },
          { category: 'Secondary', total: 40, approved: 26, success_rate: 65.0 },
          { category: 'University', total: 14, approved: 9, success_rate: 64.3 }
        ],
        grade_distribution: [
          { grade: 'Primary 1-3', students: 85, applications: 25 },
          { grade: 'Primary 4-6', students: 78, applications: 22 },
          { grade: 'Secondary 1-3', students: 52, applications: 28 },
          { grade: 'Secondary 4-6', students: 30, applications: 14 }
        ],
        monthly_progression: [
          { month: 'Jan', progressions: 8, completed: 6 },
          { month: 'Feb', progressions: 12, completed: 10 },
          { month: 'Mar', progressions: 15, completed: 12 },
          { month: 'Apr', progressions: 18, completed: 15 },
          { month: 'May', progressions: 14, completed: 12 },
          { month: 'Jun', progressions: 20, completed: 18 }
        ],
        top_performing_students: [
          { id: 1, name: 'John Doe', grade: 'Secondary 5', applications: 3, approved: 3, total_amount: 450000 },
          { id: 2, name: 'Jane Smith', grade: 'Primary 6', applications: 2, approved: 2, total_amount: 300000 },
          { id: 3, name: 'Mike Johnson', grade: 'Secondary 3', applications: 4, approved: 3, total_amount: 380000 }
        ]
      })
    } finally {
      setLoading(false)
    }
  }

  // Export analytics report
  const handleExportReport = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE}/api/v1/partner-analytics/export?period=${selectedPeriod}&category=${selectedCategory}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Failed to export report')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      toast.success('Report exported successfully')
    } catch (error) {
      console.error('Error exporting report:', error)
      toast.error('Failed to export report')
    }
  }

  useEffect(() => {
    if (user && token) {
      fetchAnalyticsData()
    }
  }, [user, token, selectedPeriod, selectedCategory])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">Please log in to access analytics.</p>
          <Button asChild>
            <Link href="/auth/login">Login</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Analytics & Reporting
              </h1>
              <p className="text-gray-600 mt-2">
                Comprehensive insights into your organization's performance and student outcomes
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3months">Last 3 Months</SelectItem>
                  <SelectItem value="6months">Last 6 Months</SelectItem>
                  <SelectItem value="12months">Last 12 Months</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="primary">Primary</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="university">University</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={fetchAnalyticsData} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button onClick={handleExportReport} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading analytics...</span>
          </div>
        ) : analyticsData ? (
          <div className="space-y-8">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Students</p>
                      <p className="text-2xl font-bold text-blue-600">{analyticsData.overview.total_students}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Award className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Applications</p>
                      <p className="text-2xl font-bold text-green-600">{analyticsData.overview.total_applications}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Target className="h-8 w-8 text-purple-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Success Rate</p>
                      <p className="text-2xl font-bold text-purple-600">{analyticsData.overview.success_rate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <TrendingUp className="h-8 w-8 text-yellow-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Amount Awarded</p>
                      <p className="text-2xl font-bold text-yellow-600">₦{analyticsData.overview.total_amount_awarded.toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Activity className="h-8 w-8 text-indigo-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Active Scholarships</p>
                      <p className="text-2xl font-bold text-indigo-600">{analyticsData.overview.active_scholarships}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Clock className="h-8 w-8 text-orange-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Pending</p>
                      <p className="text-2xl font-bold text-orange-600">{analyticsData.overview.pending_applications}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts Section */}
            <Tabs defaultValue="trends" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="trends">Application Trends</TabsTrigger>
                <TabsTrigger value="success">Success Analysis</TabsTrigger>
                <TabsTrigger value="distribution">Grade Distribution</TabsTrigger>
                <TabsTrigger value="progression">Progression Tracking</TabsTrigger>
              </TabsList>

              {/* Application Trends Tab */}
              <TabsContent value="trends" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <LineChartIcon className="h-5 w-5 mr-2" />
                      Application Trends Over Time
                    </CardTitle>
                    <CardDescription>
                      Track application submissions and approval rates over time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <AreaChart data={analyticsData.application_trends}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="applications" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
                        <Area type="monotone" dataKey="approved" stackId="2" stroke="#10B981" fill="#10B981" fillOpacity={0.6} />
                        <Area type="monotone" dataKey="rejected" stackId="3" stroke="#EF4444" fill="#EF4444" fillOpacity={0.6} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Success Analysis Tab */}
              <TabsContent value="success" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <PieChartIcon className="h-5 w-5 mr-2" />
                        Success Rate by Category
                      </CardTitle>
                      <CardDescription>
                        Compare success rates across scholarship categories
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={analyticsData.success_by_category}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ category, success_rate }) => `${category}: ${success_rate}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="success_rate"
                          >
                            {analyticsData.success_by_category.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <BarChart3 className="h-5 w-5 mr-2" />
                        Applications by Category
                      </CardTitle>
                      <CardDescription>
                        Total applications and approvals by category
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={analyticsData.success_by_category}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="category" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="total" fill="#3B82F6" name="Total Applications" />
                          <Bar dataKey="approved" fill="#10B981" name="Approved" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Top Performing Students */}
                <Card>
                  <CardHeader>
                    <CardTitle>Top Performing Students</CardTitle>
                    <CardDescription>
                      Students with highest scholarship success rates and amounts
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analyticsData.top_performing_students.map((student, index) => (
                        <div key={student.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-semibold">{student.name}</p>
                              <p className="text-sm text-gray-600">{student.grade}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">₦{student.total_amount.toLocaleString()}</p>
                            <p className="text-sm text-gray-600">{student.approved}/{student.applications} applications</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Grade Distribution Tab */}
              <TabsContent value="distribution" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2" />
                      Student Distribution by Grade Level
                    </CardTitle>
                    <CardDescription>
                      Overview of student enrollment and application activity by grade
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={analyticsData.grade_distribution}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="grade" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="students" fill="#3B82F6" name="Total Students" />
                        <Bar dataKey="applications" fill="#10B981" name="Applications" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Progression Tracking Tab */}
              <TabsContent value="progression" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2" />
                      Academic Progression Trends
                    </CardTitle>
                    <CardDescription>
                      Track student academic progression and completion rates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <LineChart data={analyticsData.monthly_progression}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="progressions" stroke="#3B82F6" strokeWidth={2} name="Total Progressions" />
                        <Line type="monotone" dataKey="completed" stroke="#10B981" strokeWidth={2} name="Completed" />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="text-center py-12">
            <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 mb-2">No analytics data available</p>
            <p className="text-sm text-gray-400">Data will appear once you have students and applications</p>
          </div>
        )}
      </div>
    </div>
  )
}
