<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CREATING TEST SCHOLARSHIP FIELDS ===\n\n";

// Get first few scholarships
$scholarships = App\Models\Scholarship::take(3)->get();

foreach ($scholarships as $scholarship) {
    echo "Creating fields for: {$scholarship->title} (ID: {$scholarship->id})\n";
    
    // Check if fields already exist
    $existingFields = App\Models\ScholarshipField::where('scholarship_id', $scholarship->id)->count();
    if ($existingFields > 0) {
        echo "  - Already has {$existingFields} fields, skipping...\n";
        continue;
    }
    
    // Create category-specific fields
    $fields = [];
    
    if ($scholarship->category === 'primary') {
        $fields = [
            ['field_name' => 'student_full_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'current_class', 'field_label' => 'Current Class', 'field_type' => 'text', 'is_required' => true, 'field_order' => 3],
            ['field_name' => 'parent_name', 'field_label' => 'Parent/Guardian Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 4],
            ['field_name' => 'parent_phone', 'field_label' => 'Parent Phone Number', 'field_type' => 'tel', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 6],
            ['field_name' => 'school_account', 'field_label' => 'School Account Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'student_picture', 'field_label' => 'Student Picture', 'field_type' => 'file', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'reason_for_scholarship', 'field_label' => 'Reason for Scholarship', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 9],
        ];
    } elseif ($scholarship->category === 'secondary') {
        $fields = [
            ['field_name' => 'student_full_name', 'field_label' => 'Student Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'student_age', 'field_label' => 'Student Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'current_class', 'field_label' => 'Current Class', 'field_type' => 'text', 'is_required' => true, 'field_order' => 3],
            ['field_name' => 'parent_phone', 'field_label' => 'Parent Phone Number', 'field_type' => 'tel', 'is_required' => true, 'field_order' => 4],
            ['field_name' => 'school_name', 'field_label' => 'School Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'principal_name', 'field_label' => 'Principal Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 6],
            ['field_name' => 'principal_account', 'field_label' => 'Principal Account Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'student_picture', 'field_label' => 'Student Picture', 'field_type' => 'file', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'reason_for_scholarship', 'field_label' => 'Reason for Scholarship', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 9],
        ];
    } elseif ($scholarship->category === 'university') {
        $fields = [
            ['field_name' => 'student_full_name', 'field_label' => 'Full Name', 'field_type' => 'text', 'is_required' => true, 'field_order' => 1],
            ['field_name' => 'student_age', 'field_label' => 'Age', 'field_type' => 'number', 'is_required' => true, 'field_order' => 2],
            ['field_name' => 'course_of_study', 'field_label' => 'Course of Study', 'field_type' => 'text', 'is_required' => true, 'field_order' => 3],
            ['field_name' => 'current_level', 'field_label' => 'Current Level', 'field_type' => 'text', 'is_required' => true, 'field_order' => 4],
            ['field_name' => 'matric_number', 'field_label' => 'Matriculation Number', 'field_type' => 'text', 'is_required' => true, 'field_order' => 5],
            ['field_name' => 'phone_number', 'field_label' => 'Phone Number', 'field_type' => 'tel', 'is_required' => true, 'field_order' => 6],
            ['field_name' => 'email_address', 'field_label' => 'Email Address', 'field_type' => 'email', 'is_required' => true, 'field_order' => 7],
            ['field_name' => 'student_id_card', 'field_label' => 'Student ID Card', 'field_type' => 'file', 'is_required' => true, 'field_order' => 8],
            ['field_name' => 'reason_for_scholarship', 'field_label' => 'Reason for Scholarship', 'field_type' => 'textarea', 'is_required' => true, 'field_order' => 9],
        ];
    }
    
    // Create the fields
    foreach ($fields as $fieldData) {
        $fieldData['scholarship_id'] = $scholarship->id;
        $fieldData['is_active'] = true;
        
        App\Models\ScholarshipField::create($fieldData);
    }
    
    echo "  - Created " . count($fields) . " fields\n";
}

echo "\n=== VERIFICATION ===\n";

// Verify the fields were created
$totalFields = App\Models\ScholarshipField::count();
echo "Total scholarship fields in database: {$totalFields}\n";

// Test the API response
echo "\nTesting API response with custom fields...\n";
$response = file_get_contents('http://127.0.0.1:8000/api/v1/public-scholarships');
$data = json_decode($response, true);

if ($data && $data['success'] && !empty($data['data']['data'])) {
    $scholarship = $data['data']['data'][0];
    if (isset($scholarship['custom_fields']) && !empty($scholarship['custom_fields'])) {
        echo "✓ API now includes custom fields (" . count($scholarship['custom_fields']) . " fields)\n";
        foreach ($scholarship['custom_fields'] as $field) {
            echo "  - {$field['field_label']} ({$field['field_type']})\n";
        }
    } else {
        echo "⚠ API still not including custom fields\n";
    }
} else {
    echo "✗ API request failed\n";
}

echo "\nDone!\n";

?>
