<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class ApiDocumentationController extends Controller
{
    /**
     * Display the API documentation homepage
     */
    public function index()
    {
        $apiRoutes = $this->getApiRoutes();
        $roleMatrix = $this->getRoleAccessMatrix();
        
        return view('api-docs.index', compact('apiRoutes', 'roleMatrix'));
    }

    /**
     * Display authentication documentation
     */
    public function authentication()
    {
        return view('api-docs.authentication');
    }

    /**
     * Display authorization documentation
     */
    public function authorization()
    {
        $roleMatrix = $this->getRoleAccessMatrix();
        return view('api-docs.authorization', compact('roleMatrix'));
    }

    /**
     * Display endpoints documentation
     */
    public function endpoints()
    {
        $apiRoutes = $this->getApiRoutes();
        return view('api-docs.endpoints', compact('apiRoutes'));
    }

    /**
     * Display specific endpoint documentation
     */
    public function endpoint($category)
    {
        $apiRoutes = $this->getApiRoutes();
        $categoryRoutes = $apiRoutes[$category] ?? [];
        
        return view('api-docs.endpoint', compact('category', 'categoryRoutes'));
    }

    /**
     * Display error codes documentation
     */
    public function errorCodes()
    {
        $errorCodes = $this->getErrorCodes();
        return view('api-docs.error-codes', compact('errorCodes'));
    }

    /**
     * Display mobile integration examples
     */
    public function mobileIntegration()
    {
        return view('api-docs.mobile-integration');
    }

    /**
     * Display code examples
     */
    public function codeExamples()
    {
        return view('api-docs.code-examples');
    }

    /**
     * API testing interface
     */
    public function testing()
    {
        $apiRoutes = $this->getApiRoutes();
        return view('api-docs.testing', compact('apiRoutes'));
    }

    /**
     * Handle API test requests
     */
    public function testEndpoint(Request $request)
    {
        $method = $request->input('method');
        $endpoint = $request->input('endpoint');
        $headers = json_decode($request->input('headers', '{}'), true);
        $body = json_decode($request->input('body', '{}'), true);

        // This would make an internal API call for testing
        // For security, we'll return a mock response
        return response()->json([
            'success' => true,
            'message' => 'Test endpoint functionality - replace with actual API call',
            'request' => [
                'method' => $method,
                'endpoint' => $endpoint,
                'headers' => $headers,
                'body' => $body
            ]
        ]);
    }

    /**
     * Get organized API routes
     */
    private function getApiRoutes()
    {
        return [
            'Authentication' => [
                'POST /api/v1/register' => [
                    'description' => 'Register a new user account',
                    'roles' => ['public'],
                    'parameters' => ['name', 'email', 'password', 'role'],
                    'response' => 'User object with access token'
                ],
                'POST /api/v1/login' => [
                    'description' => 'Authenticate user and get access token',
                    'roles' => ['public'],
                    'parameters' => ['email', 'password'],
                    'response' => 'User object with access token'
                ],
                'POST /api/v1/logout' => [
                    'description' => 'Logout user and revoke token',
                    'roles' => ['authenticated'],
                    'parameters' => [],
                    'response' => 'Success message'
                ],
            ],
            'Scholarships' => [
                'GET /api/v1/public-scholarships' => [
                    'description' => 'Get list of active scholarships',
                    'roles' => ['public'],
                    'parameters' => ['category', 'limit'],
                    'response' => 'Array of scholarship objects'
                ],
                'POST /api/v1/student-scholarships/{id}/apply' => [
                    'description' => 'Apply for university scholarship',
                    'roles' => ['student', 'user'],
                    'parameters' => ['scholarship_id', 'application_data'],
                    'response' => 'Application object'
                ],
                'POST /api/v1/partner-scholarships/{id}/apply' => [
                    'description' => 'Apply for primary/secondary scholarship',
                    'roles' => ['partner_organization'],
                    'parameters' => ['scholarship_id', 'student_data'],
                    'response' => 'Application object'
                ],
            ],
            'Volunteers' => [
                'GET /api/v1/volunteer/opportunities/matched' => [
                    'description' => 'Get matched volunteer opportunities',
                    'roles' => ['volunteer'],
                    'parameters' => ['skills', 'location'],
                    'response' => 'Array of opportunity objects'
                ],
                'POST /api/v1/volunteer/hours/log' => [
                    'description' => 'Log volunteer hours',
                    'roles' => ['volunteer'],
                    'parameters' => ['hours', 'activity', 'date'],
                    'response' => 'Hour log object'
                ],
            ],
            'Programs' => [
                'GET /api/v1/programs' => [
                    'description' => 'Get list of programs',
                    'roles' => ['authenticated'],
                    'parameters' => ['category', 'status'],
                    'response' => 'Array of program objects'
                ],
                'POST /api/v1/programs/{id}/enroll' => [
                    'description' => 'Enroll in a program',
                    'roles' => ['student', 'user'],
                    'parameters' => ['program_id'],
                    'response' => 'Enrollment object'
                ],
            ],
            'Events' => [
                'GET /api/v1/events' => [
                    'description' => 'Get list of events',
                    'roles' => ['authenticated'],
                    'parameters' => ['date_from', 'date_to'],
                    'response' => 'Array of event objects'
                ],
                'POST /api/v1/events/{id}/register' => [
                    'description' => 'Register for an event',
                    'roles' => ['authenticated'],
                    'parameters' => ['event_id'],
                    'response' => 'Registration object'
                ],
            ],
            'Admin' => [
                'GET /api/v1/admin/dashboard' => [
                    'description' => 'Get admin dashboard data',
                    'roles' => ['admin'],
                    'parameters' => [],
                    'response' => 'Dashboard statistics object'
                ],
                'GET /api/v1/admin/users' => [
                    'description' => 'Get list of users',
                    'roles' => ['admin'],
                    'parameters' => ['role', 'status', 'page'],
                    'response' => 'Paginated user objects'
                ],
            ],
        ];
    }

    /**
     * Get role access matrix
     */
    private function getRoleAccessMatrix()
    {
        return [
            'admin' => [
                'description' => 'Full system access',
                'permissions' => ['All endpoints', 'User management', 'System configuration'],
                'rate_limit' => '2000 requests/minute'
            ],
            'partner_organization' => [
                'description' => 'School/organization management',
                'permissions' => ['Student management', 'Primary/Secondary scholarships', 'Program collaboration'],
                'rate_limit' => '1000 requests/minute'
            ],
            'volunteer' => [
                'description' => 'Volunteer activities',
                'permissions' => ['Opportunity matching', 'Hour logging', 'Training modules'],
                'rate_limit' => '500 requests/minute'
            ],
            'student' => [
                'description' => 'Student services',
                'permissions' => ['University scholarships', 'Program enrollment', 'Educational resources'],
                'rate_limit' => '300 requests/minute'
            ],
            'user' => [
                'description' => 'General user access',
                'permissions' => ['Basic services', 'Public information', 'Profile management'],
                'rate_limit' => '300 requests/minute'
            ],
        ];
    }

    /**
     * Get error codes documentation
     */
    private function getErrorCodes()
    {
        return [
            'Authentication Errors' => [
                'AUTH_001' => 'Invalid credentials',
                'AUTH_002' => 'Token expired',
                'AUTH_003' => 'Token invalid',
                'AUTH_004' => 'Account not verified',
                'AUTH_005' => 'Account suspended',
            ],
            'Authorization Errors' => [
                'AUTHZ_001' => 'Insufficient permissions',
                'AUTHZ_002' => 'Role not authorized',
                'AUTHZ_003' => 'Resource access denied',
                'AUTHZ_004' => 'Data ownership violation',
            ],
            'Validation Errors' => [
                'VAL_001' => 'Required field missing',
                'VAL_002' => 'Invalid field format',
                'VAL_003' => 'Field value out of range',
                'VAL_004' => 'Duplicate entry',
            ],
            'Rate Limiting' => [
                'RATE_001' => 'Rate limit exceeded',
                'RATE_002' => 'Too many requests',
            ],
            'Security Errors' => [
                'SEC_001' => 'IP blocked',
                'SEC_002' => 'Suspicious activity detected',
                'SEC_003' => 'Security violation',
            ],
        ];
    }
}
