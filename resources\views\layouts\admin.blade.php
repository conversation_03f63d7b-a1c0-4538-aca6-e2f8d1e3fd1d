<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Dashboard') - HALIMAKQ Foundation</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('img/favicon.png') }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- DataTables for enhanced tables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css">
    <!-- Select2 for enhanced dropdowns -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- Toastr for notifications -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <style>
        /* Professional Admin Styles - Dark Green Theme */
        :root {
            --primary-color: #047857;
            --primary-dark: #065f46;
            --primary-light: #059669;
            --secondary-color: #064e3b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-bg: #f8fafc;
            --dark-bg: #1e293b;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --border-radius-lg: 16px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #047857 0%, #065f46 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: var(--light-bg);
            min-height: 100vh;
            color: var(--text-primary);
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            background: white;
            border-right: 1px solid var(--border-color);
            z-index: 1000;
            transition: var(--transition);
            overflow-y: auto;
        }

        .sidebar-header {
            height: var(--topbar-height);
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            background: var(--gradient-primary);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: white;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .sidebar-brand img {
            height: 40px;
            width: auto;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: var(--transition);
            border-radius: 0;
            margin: 0;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: rgba(4, 120, 87, 0.04);
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: rgba(4, 120, 87, 0.08);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        .nav-link i {
            width: 1.5rem;
            margin-right: 0.75rem;
            font-size: 1.1rem;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition);
        }

        .topbar {
            height: var(--topbar-height);
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 999;
            box-shadow: var(--shadow-sm);
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .topbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .content-wrapper {
            padding: 2rem;
        }

        /* Card Styles */
        .card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: none;
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Modern Dashboard Header */
        .dashboard-header {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            color: white;
            padding: 2rem 0;
            margin: -1.5rem -1.5rem 2rem -1.5rem;
            border-radius: 0 0 1.5rem 1.5rem;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .dashboard-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: 1rem;
            font-weight: 400;
        }

        .dashboard-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .meta-item {
            display: flex;
            align-items: center;
        }

        .meta-divider {
            opacity: 0.5;
        }

        .status-online {
            color: #10b981;
        }

        .wave-emoji {
            display: inline-block;
            animation: wave 2s infinite;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(20deg); }
            75% { transform: rotate(-10deg); }
        }

        .dashboard-actions {
            text-align: right;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        /* Modern Stats Cards */
        .stats-grid {
            margin-top: -4rem;
            position: relative;
            z-index: 10;
        }

        .stat-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease-in-out;
            overflow: hidden;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-color: #d1d5db;
        }

        .stat-card-body {
            padding: 1.25rem;
        }

        .stat-card-action {
            border: 2px dashed #e5e7eb;
            background: #f9fafb;
        }

        .stat-card-action:hover {
            border-color: #047857;
            background: #ecfdf5;
        }

        .stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .stat-content {
            flex: 1;
            min-width: 0;
        }

        .stat-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #111827;
            line-height: 1.2;
            margin-bottom: 0.25rem;
        }

        .stat-trend {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .text-purple {
            color: #8b5cf6 !important;
        }

        .text-gold {
            color: #f59e0b !important;
        }

        .text-green {
            color: #047857 !important;
        }

        .stat-card-footer {
            padding: 0.75rem 1.5rem;
            background: #f9fafb;
            border-top: 1px solid #f3f4f6;
        }

        .stat-link {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.2s;
        }

        .stat-link:hover {
            color: #374151;
        }

        /* Color variants for stat cards */
        .stat-card-primary .stat-icon-wrapper {
            background: rgba(4, 120, 87, 0.1);
            color: #047857;
        }

        .stat-card-success .stat-icon-wrapper {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .stat-card-warning .stat-icon-wrapper {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .stat-card-info .stat-icon-wrapper {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
        }

        /* Modern Button Styles */
        .btn {
            padding: 0.625rem 1.25rem;
            border-radius: 0.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-modern {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .btn-modern:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .btn-primary {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }

        .btn-outline-primary {
            color: #047857;
            border-color: #047857;
            background: white;
        }

        .btn-outline-primary:hover {
            background: #047857;
            border-color: #047857;
            color: white;
        }

        /* Modern Dropdown Styles */
        .dropdown-menu-modern {
            border: none;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-radius: 0.75rem;
            padding: 0.5rem;
            margin-top: 0.5rem;
        }

        .dropdown-menu-modern .dropdown-item {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s;
        }

        .dropdown-menu-modern .dropdown-item:hover {
            background-color: #f3f4f6;
            transform: translateX(2px);
        }

        .dropdown-menu-modern .dropdown-item:last-child {
            margin-bottom: 0;
        }

        /* Modern Analytics Card */
        .analytics-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
            overflow: hidden;
        }

        .analytics-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .analytics-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .analytics-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .analytics-heading {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .analytics-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .analytics-body {
            padding: 1.5rem;
        }

        .chart-container {
            position: relative;
            height: 350px;
        }

        /* Activity Card */
        .activity-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .activity-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .activity-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .activity-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .activity-heading {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .activity-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .btn-ghost {
            background: transparent;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .btn-ghost:hover {
            background: #f9fafb;
            color: #374151;
        }

        .activity-body {
            flex: 1;
            overflow-y: auto;
        }

        .activity-feed {
            padding: 1rem 1.5rem;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item-icon-wrapper {
            width: 2rem;
            height: 2rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .activity-default {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .activity-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .activity-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .activity-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .activity-item-content {
            flex: 1;
            min-width: 0;
        }

        .activity-item-text {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .activity-item-time {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .activity-empty {
            text-align: center;
            padding: 2rem 1rem;
        }

        .activity-empty-icon {
            font-size: 2rem;
            color: #d1d5db;
            margin-bottom: 0.5rem;
        }

        .activity-empty-text {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .activity-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #f3f4f6;
            background: #f9fafb;
        }

        .activity-link {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.2s;
        }

        .activity-link:hover {
            color: #374151;
        }

        /* Quick Actions Card */
        .quick-actions-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
            overflow: hidden;
            height: 100%;
        }

        .quick-actions-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .quick-actions-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .quick-actions-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .quick-actions-heading {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .quick-actions-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .quick-actions-body {
            padding: 1.5rem;
        }

        .quick-actions-grid {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .quick-action-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid #f3f4f6;
            border-radius: 0.75rem;
            text-decoration: none;
            transition: all 0.2s;
            background: white;
        }

        .quick-action-item:hover {
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            text-decoration: none;
        }

        .quick-action-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .quick-action-content {
            flex: 1;
            min-width: 0;
        }

        .quick-action-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.125rem;
        }

        .quick-action-subtitle {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .quick-action-arrow {
            color: #9ca3af;
            font-size: 0.875rem;
            transition: all 0.2s;
        }

        .quick-action-item:hover .quick-action-arrow {
            color: #6b7280;
            transform: translateX(2px);
        }

        /* Donations Card */
        .donations-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .donations-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .donations-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .donations-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .donations-heading {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .donations-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .donations-body {
            flex: 1;
            overflow-y: auto;
        }

        .donations-table-container {
            padding: 1rem 1.5rem;
        }

        .donation-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .donation-item:last-child {
            border-bottom: none;
        }

        .donation-avatar {
            flex-shrink: 0;
        }

        .donation-avatar-img {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            object-fit: cover;
        }

        .donation-avatar-placeholder {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 1rem;
        }

        .donation-content {
            flex: 1;
            min-width: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .donation-donor {
            flex: 1;
            min-width: 0;
        }

        .donation-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.125rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .donation-email {
            font-size: 0.75rem;
            color: #6b7280;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .donation-details {
            text-align: right;
            margin-left: 1rem;
        }

        .donation-campaign {
            font-size: 0.75rem;
            color: #374151;
            margin-bottom: 0.125rem;
        }

        .donation-date {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .donation-amount {
            text-align: right;
            margin-left: 1rem;
            flex-shrink: 0;
        }

        .donation-amount-value {
            font-size: 1rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
        }

        .donation-status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #d97706;
        }

        .status-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
        }

        .status-info {
            background: rgba(14, 165, 233, 0.1);
            color: #0284c7;
        }

        .status-secondary {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .donations-empty {
            text-align: center;
            padding: 3rem 1rem;
        }

        .donations-empty-icon {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        .donations-empty-text {
            font-size: 1rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .donations-empty-subtitle {
            font-size: 0.875rem;
            color: #9ca3af;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard-header {
                padding: 1.5rem 0;
                margin: -1rem -1rem 1.5rem -1rem;
            }

            .dashboard-title {
                font-size: 1.5rem;
            }

            .dashboard-subtitle {
                font-size: 1rem;
            }

            .dashboard-actions {
                text-align: left;
                margin-top: 1rem;
            }

            .action-buttons {
                justify-content: flex-start;
            }

            .stats-grid {
                margin-top: -2rem;
            }

            .stat-card-body {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .stat-icon-wrapper {
                align-self: center;
            }

            .donation-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .donation-details {
                text-align: left;
                margin-left: 0;
            }

            .donation-amount {
                margin-left: 0;
                text-align: left;
            }
        }

        /* Form Styles */
        .form-control, .form-select {
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
            padding: 0.5rem 0.75rem;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(4, 120, 87, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* Table Styles */
        .table {
            margin-bottom: 0;
        }

        .table th {
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 2px solid var(--border-color);
            padding: 0.75rem;
            background-color: #f8fafc;
        }

        .table td {
            vertical-align: middle;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr:hover {
            background-color: rgba(37, 99, 235, 0.02);
        }

        /* Badge Styles */
        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: var(--border-radius-sm);
        }

        /* Alert Styles */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background-color: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background-color: rgba(217, 119, 6, 0.1);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background-color: rgba(8, 145, 178, 0.1);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }

        /* Sidebar scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        /* Enhanced Data Tables */
        .dataTables_wrapper {
            padding: 0;
        }

        .dataTables_filter {
            margin-bottom: 1rem;
        }

        .dataTables_filter input {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.5rem 0.75rem;
            margin-left: 0.5rem;
        }

        .dataTables_length select {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.375rem 0.75rem;
            margin: 0 0.5rem;
        }

        .dataTables_info {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .dataTables_paginate .paginate_button {
            padding: 0.5rem 0.75rem;
            margin: 0 0.125rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-secondary);
            text-decoration: none;
        }

        .dataTables_paginate .paginate_button:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .dataTables_paginate .paginate_button.current {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Enhanced Modals */
        .modal-content {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            padding: 1.5rem;
        }

        /* Enhanced Filters */
        .filter-section {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .filter-row {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        /* Enhanced Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .btn-action {
            padding: 0.375rem 0.75rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-secondary);
            font-size: 0.875rem;
            transition: var(--transition);
        }

        .btn-action:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Enhanced Bulk Actions */
        .bulk-actions {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            display: none;
            align-items: center;
            gap: 1rem;
            box-shadow: var(--shadow-sm);
        }

        .bulk-actions.show {
            display: flex;
        }

        .bulk-actions-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .bulk-actions-buttons {
            display: flex;
            gap: 0.5rem;
            margin-left: auto;
        }

        /* Enhanced Charts */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .chart-legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .chart-legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        /* Professional Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* Professional Utilities */
        .text-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, var(--info-color), #047857);
        }

        .shadow-custom {
            box-shadow: var(--shadow-lg);
        }

        .border-radius-custom {
            border-radius: var(--border-radius-lg);
        }

        .hover-lift {
            transition: var(--transition);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .topbar {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                color: var(--text-secondary);
                font-size: 1.25rem;
                margin-right: 1rem;
                padding: 0.5rem;
                border-radius: var(--border-radius);
                transition: var(--transition);
            }

            .mobile-toggle:hover {
                color: var(--primary-color);
                background-color: rgba(4, 120, 87, 0.04);
            }

            .filter-row {
                flex-direction: column;
            }

            .filter-group {
                min-width: 100%;
            }

            .action-buttons {
                flex-direction: column;
                width: 100%;
            }

            .bulk-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .bulk-actions-buttons {
                margin-left: 0;
                justify-content: center;
            }
        }

        @media (min-width: 769px) {
            .mobile-toggle {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="{{ route('admin.dashboard') }}" class="sidebar-brand">
                <!-- <img src="{{ asset('img/logo.png') }}" alt="HALIMAKQ Foundation"> -->
                <span>HALIMAKQ</span>
            </a>
        </div>
        <nav class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i class="fas fa-users"></i>
                        Users
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.programs.index') }}" class="nav-link {{ request()->routeIs('admin.programs.*') ? 'active' : '' }}">
                        <i class="fas fa-project-diagram"></i>
                        Programs
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.events.index') }}" class="nav-link {{ request()->routeIs('admin.events.*') ? 'active' : '' }}">
                        <i class="fas fa-calendar-alt"></i>
                        Events
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.volunteers.index') }}" class="nav-link {{ request()->routeIs('admin.volunteers.*') ? 'active' : '' }}">
                        <i class="fas fa-hands-helping"></i>
                        Volunteers
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.donations.index') }}" class="nav-link {{ request()->routeIs('admin.donations.*') ? 'active' : '' }}">
                        <i class="fas fa-hand-holding-heart"></i>
                        Donations
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.blog.index') }}" class="nav-link {{ request()->routeIs('admin.blog.*') ? 'active' : '' }}">
                        <i class="fas fa-blog"></i>
                        Blog
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.newsletter.index') }}" class="nav-link {{ request()->routeIs('admin.newsletter.*') ? 'active' : '' }}">
                        <i class="fas fa-envelope"></i>
                        Newsletter
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.contact.index') }}" class="nav-link {{ request()->routeIs('admin.contact.*') ? 'active' : '' }}">
                        <i class="fas fa-envelope-open-text"></i>
                        Contact
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.scholarships.index') }}" class="nav-link {{ request()->routeIs('admin.scholarships.*') ? 'active' : '' }}">
                        <i class="fas fa-graduation-cap"></i>
                        Scholarships
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.team.index') }}" class="nav-link {{ request()->routeIs('admin.team.*') ? 'active' : '' }}">
                        <i class="fas fa-users-cog"></i>
                        Team
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.analytics.index') }}" class="nav-link {{ request()->routeIs('admin.analytics.*') ? 'active' : '' }}">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('admin.settings.index') }}" class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Topbar -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="btn btn-link d-lg-none mobile-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Admin</a></li>
                        @yield('breadcrumb')
                    </ol>
                </nav>
            </div>
            <div class="topbar-right">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="{{ auth()->user()?->profile_picture_url ?? asset('img/default-avatar.png') }}" alt="{{ auth()->user()?->full_name ?? 'User' }}" class="rounded-circle" width="32" height="32">
                        <span class="ms-2">{{ auth()->user()?->full_name ?? 'User' }}</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ route('admin.profile') }}">
                            <i class="fas fa-user me-2"></i>Profile
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('admin.settings.index') }}">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="content-wrapper">
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if(session('warning'))
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                {{ session('warning') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if(session('info'))
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                {{ session('info') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @yield('content')
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery (required for DataTables and Select2) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Professional Admin Scripts -->
    <script>
        // Configure Toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };
        }

        // Initialize DataTables with enhanced features
        function initDataTable(selector, options = {}) {
            if (typeof $ === 'undefined' || typeof $.fn.DataTable === 'undefined') {
                console.warn('DataTables not loaded');
                return null;
            }

            const defaultOptions = {
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Search records...",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            };

            return $(selector).DataTable({ ...defaultOptions, ...options });
        }

        // Initialize Select2 with enhanced features
        function initSelect2(selector, options = {}) {
            if (typeof $ === 'undefined' || typeof $.fn.select2 === 'undefined') {
                console.warn('Select2 not loaded');
                return null;
            }

            const defaultOptions = {
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Select an option...',
                allowClear: true
            };

            return $(selector).select2({ ...defaultOptions, ...options });
        }

        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // Enhanced bulk actions
        function initBulkActions() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][data-bulk]');
            const bulkActions = document.querySelector('.bulk-actions');
            const selectAllCheckbox = document.querySelector('#selectAll');

            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('input[type="checkbox"][data-bulk]:checked');
                const bulkActionsText = document.querySelector('.bulk-actions-text');

                if (checkedBoxes.length > 0) {
                    bulkActions?.classList.add('show');
                    if (bulkActionsText) {
                        bulkActionsText.textContent = `${checkedBoxes.length} item(s) selected`;
                    }
                } else {
                    bulkActions?.classList.remove('show');
                }
            }

            // Select all functionality
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActions();
                });
            }

            // Individual checkbox functionality
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActions();

                    // Update select all checkbox state
                    if (selectAllCheckbox) {
                        const checkedCount = document.querySelectorAll('input[type="checkbox"][data-bulk]:checked').length;
                        selectAllCheckbox.checked = checkedCount === checkboxes.length;
                        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
                    }
                });
            });
        }

        // Enhanced form validation
        function validateForm(form) {
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Traditional form submission helpers (no AJAX)
        function submitForm(formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.submit();
            }
        }

        // Enhanced notifications using Toastr
        function showNotification(message, type = 'success') {
            if (typeof toastr !== 'undefined') {
                switch(type) {
                    case 'success':
                        toastr.success(message);
                        break;
                    case 'error':
                        toastr.error(message);
                        break;
                    case 'warning':
                        toastr.warning(message);
                        break;
                    case 'info':
                        toastr.info(message);
                        break;
                    default:
                        toastr.success(message);
                }
            } else {
                // Fallback to console
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Loading states
        function showLoading(element) {
            element.classList.add('loading');
            const spinner = document.createElement('div');
            spinner.className = 'spinner-border spinner-border-sm me-2';
            spinner.setAttribute('role', 'status');
            element.insertBefore(spinner, element.firstChild);
        }

        function hideLoading(element) {
            element.classList.remove('loading');
            const spinner = element.querySelector('.spinner-border');
            if (spinner) {
                spinner.remove();
            }
        }

        // Professional data export functionality
        function exportTableData(tableId, filename = 'export.csv') {
            const table = document.querySelector(`#${tableId}`);
            if (!table) return;

            let csv = [];
            const rows = table.querySelectorAll('tr');

            rows.forEach(row => {
                const cols = row.querySelectorAll('td, th');
                const csvRow = [];
                cols.forEach(col => {
                    csvRow.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
                });
                csv.push(csvRow.join(','));
            });

            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // Initialize on document ready
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize bulk actions
            initBulkActions();

            // Initialize Select2 dropdowns
            if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                $('.select2').each(function() {
                    initSelect2(this);
                });
            }

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            showNotification('An unexpected error occurred. Please refresh the page and try again.', 'error');
        });

        // Show Laravel session messages as toastr notifications
        @if(session('success'))
            document.addEventListener('DOMContentLoaded', function() {
                showNotification('{{ session('success') }}', 'success');
            });
        @endif

        @if(session('error'))
            document.addEventListener('DOMContentLoaded', function() {
                showNotification('{{ session('error') }}', 'error');
            });
        @endif

        @if(session('warning'))
            document.addEventListener('DOMContentLoaded', function() {
                showNotification('{{ session('warning') }}', 'warning');
            });
        @endif

        @if(session('info'))
            document.addEventListener('DOMContentLoaded', function() {
                showNotification('{{ session('info') }}', 'info');
            });
        @endif
    </script>

    @stack('scripts')
</body>
</html>
