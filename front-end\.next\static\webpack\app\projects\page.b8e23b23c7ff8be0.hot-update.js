"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/page",{

/***/ "(app-pages-browser)/./app/projects/page.tsx":
/*!*******************************!*\
  !*** ./app/projects/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API functions\nconst API_BASE_URL = \"https://halimakqfoundation.fnskills.ng/api/v1\" || 0;\nconst fetchStatistics = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/statistics\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchPortfolio = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/portfolio\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchScholarshipsByStatus = async (status)=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/by-status?status=\").concat(status));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nfunction ProjectsPage() {\n    _s();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [portfolio, setPortfolio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeProjects, setActiveProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedProjects, setCompletedProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [plannedProjects, setPlannedProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [categoryScholarships, setCategoryScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCategoryResults, setShowCategoryResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            const loadData = {\n                \"ProjectsPage.useEffect.loadData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [statsData, portfolioData, activeData, completedData, plannedData] = await Promise.all([\n                            fetchStatistics(),\n                            fetchPortfolio(),\n                            fetchScholarshipsByStatus('active'),\n                            fetchScholarshipsByStatus('completed'),\n                            fetchScholarshipsByStatus('planned')\n                        ]);\n                        setStatistics(statsData);\n                        setPortfolio(portfolioData);\n                        setActiveProjects(activeData);\n                        setCompletedProjects(completedData);\n                        setPlannedProjects(plannedData);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ProjectsPage.useEffect\"], []);\n    // Handle category click to show scholarships for that category\n    const handleCategoryClick = async (category)=>{\n        try {\n            setLoading(true);\n            setSelectedCategory(category);\n            // Fetch scholarships for the selected category using apiClient\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.apiClient.getPublicScholarships({\n                category\n            });\n            if (response.success) {\n                var _response_data;\n                setCategoryScholarships(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || []);\n                setShowCategoryResults(true);\n            } else {\n                setError(\"Failed to load \".concat(category, \" scholarships\"));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load \".concat(category, \" scholarships\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle back to main view\n    const handleBackToMain = ()=>{\n        setShowCategoryResults(false);\n        setSelectedCategory(null);\n        setCategoryScholarships([]);\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"active\":\n                return \"bg-green-500\";\n            case \"completed\":\n                return \"bg-blue-500\";\n            case \"planned\":\n                return \"bg-amber-500\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const getAllProjects = ()=>[\n            ...activeProjects,\n            ...completedProjects,\n            ...plannedProjects\n        ];\n    const getProjectsByStatus = (status)=>{\n        switch(status){\n            case 'active':\n                return activeProjects;\n            case 'completed':\n                return completedProjects;\n            case 'planned':\n                return plannedProjects;\n            default:\n                return getAllProjects();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading projects...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        className: \"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2\",\n                                        children: \"Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                        children: \"Transforming Lives Through Education\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                        children: \"Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Active Scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_approved) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Students Supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_amount_awarded) || '₦0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Total Investment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: portfolio.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Program Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Scholarship Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Our comprehensive scholarship programs support students at every educational level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: portfolio.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full capitalize\",\n                                                                    children: category.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                category.approved_count,\n                                                                                \" approved\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-green-600\",\n                                                                            children: [\n                                                                                category.active_count,\n                                                                                \" active\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 256,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Total: \",\n                                                                        category.total_amount\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-green-600 border-green-600 hover:bg-green-50\",\n                                                                    onClick: ()=>handleCategoryClick(category.category),\n                                                                    children: \"View Available Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, category.category, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    showCategoryResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: handleBackToMain,\n                                                className: \"mb-4\",\n                                                children: \"← Back to Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tight mb-2\",\n                                                children: [\n                                                    selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : '',\n                                                    \" Scholarships\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: [\n                                                    \"Available scholarships for \",\n                                                    selectedCategory || 'selected',\n                                                    \" education level\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this) : categoryScholarships.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryScholarships.map((scholarship)=>{\n                                        var _scholarship_category, _scholarship_category1, _scholarship_amount;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    className: \"bg-green-600 text-white rounded-full\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full\",\n                                                                    children: ((_scholarship_category = scholarship.category) === null || _scholarship_category === void 0 ? void 0 : _scholarship_category.charAt(0).toUpperCase()) + ((_scholarship_category1 = scholarship.category) === null || _scholarship_category1 === void 0 ? void 0 : _scholarship_category1.slice(1))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: scholarship.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm line-clamp-3\",\n                                                            children: scholarship.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-green-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"₦\",\n                                                                        ((_scholarship_amount = scholarship.amount) === null || _scholarship_amount === void 0 ? void 0 : _scholarship_amount.toLocaleString()) || 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 334,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                            onClick: ()=>{\n                                                                const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                                window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                                            },\n                                                            children: \"Apply Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, scholarship.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"No scholarships available for \",\n                                            selectedCategory,\n                                            \" category at the moment.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-50 dark:bg-green-950/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Measuring Our Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Every project we undertake is designed to create measurable, lasting change in the communities we serve.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Primary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.primary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Secondary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.secondary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-6 w-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"University Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-amber-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.university) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-6 w-6 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Active Programs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Currently accepting applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-900 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight sm:text-4xl\",\n                                        children: \"Support Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-lg\",\n                                        children: \"Your contribution can help us expand our reach and create even more educational opportunities for underprivileged students across Northern Nigeria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Donate to Projects\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold\",\n                                                children: \"Become a Partner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-green-950 dark:bg-black text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Building Tomorrow's Leaders\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-200 max-w-2xl mx-auto\",\n                                children: \"Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this transformative journey.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"BqlvbRaLva9pRQa6x4aTllWpgEM=\");\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/projects/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   extractArrayData: () => (/* binding */ extractArrayData)\n/* harmony export */ });\nconst API_BASE_URL = \"https://halimakqfoundation.fnskills.ng/api/v1\" || 0;\n// Utility function to extract array data from paginated or direct responses\nconst extractArrayData = (response)=>{\n    if (!response || !response.data) return [];\n    // If data is already an array, return it\n    if (Array.isArray(response.data)) {\n        return response.data;\n    }\n    // If data has a data property (paginated response), return that array\n    if (response.data.data && Array.isArray(response.data.data)) {\n        return response.data.data;\n    }\n    // Default to empty array\n    return [];\n};\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL).concat(endpoint) : \"\".concat(this.baseURL, \"/api/v1\").concat(endpoint);\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Authentication is now handled by Laravel - no token needed for public API calls\n        try {\n            console.log(\"API Request: \".concat(options.method || 'GET', \" \").concat(url)) // Debug logging\n            ;\n            const response = await fetch(url, config);\n            const data = await response.json();\n            console.log(\"API Response for \".concat(endpoint, \":\"), data) // Debug logging\n            ;\n            // Authentication is handled by Laravel - no need to handle 401s here\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // HTTP method shortcuts\n    async get(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return this.request(endpoint, {\n            ...options,\n            method: 'GET'\n        });\n    }\n    async post(endpoint, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const config = {\n            ...options,\n            method: 'POST'\n        };\n        if (data) {\n            config.body = JSON.stringify(data);\n        }\n        return this.request(endpoint, config);\n    }\n    async put(endpoint, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const config = {\n            ...options,\n            method: 'PUT'\n        };\n        if (data) {\n            config.body = JSON.stringify(data);\n        }\n        return this.request(endpoint, config);\n    }\n    async patch(endpoint, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const config = {\n            ...options,\n            method: 'PATCH'\n        };\n        if (data) {\n            config.body = JSON.stringify(data);\n        }\n        return this.request(endpoint, config);\n    }\n    async delete(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return this.request(endpoint, {\n            ...options,\n            method: 'DELETE'\n        });\n    }\n    // Authentication is now handled by Laravel\n    async register(userData) {\n        var _response_data;\n        // First, register the basic user\n        const response = await this.request('/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                first_name: userData.first_name,\n                last_name: userData.last_name,\n                email: userData.email,\n                password: userData.password,\n                password_confirmation: userData.password_confirmation,\n                phone_number: userData.phone_number,\n                address: userData.address,\n                date_of_birth: userData.date_of_birth,\n                city: userData.city,\n                state: userData.state,\n                country: userData.country\n            })\n        });\n        // Store token and user data if registration successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        // If registration is successful, handle user type specific data\n        if (response.success && userData.user_type && userData.additional_data) {\n            try {\n                if (userData.user_type === 'volunteer') {\n                    // Submit volunteer application\n                    await this.applyAsVolunteer(userData.additional_data);\n                } else if (userData.user_type === 'student' || userData.user_type === 'partner') {\n                    // Store additional data in user preferences\n                    const preferences = {\n                        user_type: userData.user_type,\n                        profile_data: userData.additional_data,\n                        profile_completed: true\n                    };\n                    await this.updateUserPreferences(preferences);\n                }\n            } catch (error) {\n                console.error('Additional data submission failed:', error);\n                // Return the user registration success but note additional data failed\n                return {\n                    ...response,\n                    message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'\n                };\n            }\n        }\n        return response;\n    }\n    // Profile endpoints\n    async getProfile() {\n        return this.request('/profile');\n    }\n    async updateProfile(profileData) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    }\n    async updateUserPreferences(preferences) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify({\n                preferences\n            })\n        });\n    }\n    async uploadAvatar(file) {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL, \"/profile/avatar\") : \"\".concat(this.baseURL, \"/api/v1/profile/avatar\");\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Avatar upload failed:', error);\n            throw error;\n        }\n    }\n    async changePassword(passwordData) {\n        return this.request('/profile/password', {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    async generateQrCode() {\n        return this.request('/profile/generate-qr', {\n            method: 'POST'\n        });\n    }\n    async getIdCard() {\n        return this.request('/profile/id-card');\n    }\n    // Dashboard endpoints\n    async getDashboardSummary() {\n        return this.request('/dashboard/summary');\n    }\n    // Volunteer endpoints\n    async getVolunteerApplication() {\n        return this.request('/volunteer/application');\n    }\n    async getVolunteerHours() {\n        return this.request('/volunteer/hours');\n    }\n    async getVolunteerOpportunities() {\n        return this.request('/volunteer/opportunities');\n    }\n    async logVolunteerHours(hoursData) {\n        return this.request('/volunteer/hours', {\n            method: 'POST',\n            body: JSON.stringify(hoursData)\n        });\n    }\n    // Scholarship endpoints\n    async getMyScholarshipApplications() {\n        return this.request('/scholarships/my-applications');\n    }\n    async getScholarships() {\n        return this.request('/scholarships');\n    }\n    async applyForScholarship(scholarshipId, applicationData) {\n        return this.request(\"/scholarships/\".concat(scholarshipId, \"/apply\"), {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Event endpoints\n    async getMyEventRegistrations() {\n        return this.request('/events/my-registrations');\n    }\n    async getUpcomingEvents() {\n        return this.request('/events/upcoming');\n    }\n    // Helper method for building query strings\n    buildQueryString(params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined && value !== null && value !== '') {\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>searchParams.append(\"\".concat(key, \"[]\"), item));\n                } else {\n                    searchParams.append(key, value.toString());\n                }\n            }\n        });\n        return searchParams.toString();\n    }\n    // Admin Dashboard endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminAnalytics(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/analytics\".concat(queryString));\n    }\n    async getAdminStats(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/stats\".concat(queryString));\n    }\n    // Admin User Management\n    async getAdminUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users\".concat(queryString));\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionUsers(data) {\n        return this.request('/admin/users/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users/export\".concat(queryString));\n    }\n    // Admin Scholarship Management\n    async getScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications\".concat(queryString));\n    }\n    async reviewScholarshipApplication(applicationId, reviewData) {\n        return this.request(\"/admin/scholarship-applications/\".concat(applicationId, \"/review\"), {\n            method: 'PUT',\n            body: JSON.stringify(reviewData)\n        });\n    }\n    async bulkActionScholarshipApplications(data) {\n        return this.request('/admin/scholarship-applications/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications/export\".concat(queryString));\n    }\n    async getScholarshipStatistics() {\n        return this.request('/admin/scholarships/statistics');\n    }\n    // Admin Event Management\n    async getAdminEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events\".concat(queryString));\n    }\n    async createAdminEvent(eventData) {\n        return this.request('/admin/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async updateAdminEvent(eventId, eventData) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'PUT',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async deleteAdminEvent(eventId) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionEvents(data) {\n        return this.request('/admin/events/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events/export\".concat(queryString));\n    }\n    async getEventStatistics() {\n        return this.request('/admin/events/statistics');\n    }\n    // Admin Program Management\n    async getAdminPrograms(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/programs\".concat(queryString));\n    }\n    async createAdminProgram(programData) {\n        return this.request('/admin/programs', {\n            method: 'POST',\n            body: JSON.stringify(programData)\n        });\n    }\n    async updateAdminProgram(programId, programData) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'PUT',\n            body: JSON.stringify(programData)\n        });\n    }\n    async deleteAdminProgram(programId) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'DELETE'\n        });\n    }\n    // Admin Blog Management\n    async getAdminBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts\".concat(queryString));\n    }\n    async createAdminBlogPost(postData) {\n        return this.request('/admin/blog/posts', {\n            method: 'POST',\n            body: JSON.stringify(postData)\n        });\n    }\n    async updateAdminBlogPost(postId, postData) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'PUT',\n            body: JSON.stringify(postData)\n        });\n    }\n    async deleteAdminBlogPost(postId) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'DELETE'\n        });\n    }\n    async exportBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts/export\".concat(queryString));\n    }\n    // Donation endpoints\n    async getMyDonations() {\n        return this.request('/donations/my-donations');\n    }\n    async getDonationCampaigns() {\n        return this.request('/donations/campaigns');\n    }\n    // Blog endpoints\n    async getBlogPosts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/blog/posts?page=\".concat(page));\n    }\n    async getBlogPost(slug) {\n        return this.request(\"/blog/posts/\".concat(slug));\n    }\n    // Events endpoints\n    async getEvents() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/events?page=\".concat(page));\n    }\n    async getEvent(id) {\n        return this.request(\"/events/\".concat(id));\n    }\n    async registerForEvent(eventId, additionalInfo) {\n        return this.request(\"/events/\".concat(eventId, \"/register\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                additional_info: additionalInfo\n            })\n        });\n    }\n    // Programs endpoints\n    async getPrograms() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/programs?page=\".concat(page));\n    }\n    async getProgram(slug) {\n        return this.request(\"/programs/\".concat(slug));\n    }\n    // Additional scholarship endpoint\n    async getScholarship(id) {\n        return this.request(\"/scholarships/\".concat(id));\n    }\n    // Public scholarships endpoints (no authentication required)\n    async getPublicScholarships(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/public-scholarships\".concat(queryString));\n    }\n    async getPublicScholarship(id) {\n        return this.request(\"/public-scholarships/\".concat(id));\n    }\n    // Project scholarship endpoints for Projects page\n    async getProjectScholarshipStatistics() {\n        return this.request('/project-scholarships/statistics');\n    }\n    async getProjectScholarshipPortfolio() {\n        return this.request('/project-scholarships/portfolio');\n    }\n    async getScholarshipsByStatus() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'active';\n        return this.request(\"/project-scholarships/by-status?status=\".concat(status));\n    }\n    async submitScholarshipApplication(scholarshipId, formData) {\n        // Use the same URL construction logic as the request method\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL, \"/apply-scholarship/\").concat(scholarshipId) : \"\".concat(this.baseURL, \"/api/v1/apply-scholarship/\").concat(scholarshipId);\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            console.log(\"API Response for scholarship application:\", data);\n            if (!response.ok) {\n                throw new Error(data.message || \"HTTP error! status: \".concat(response.status));\n            }\n            return data;\n        } catch (error) {\n            console.error('Error submitting scholarship application:', error);\n            throw error;\n        }\n    }\n    async applyAsVolunteer(applicationData) {\n        return this.request('/volunteer/apply', {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Contact endpoints\n    async submitContactForm(contactData) {\n        return this.request('/contact', {\n            method: 'POST',\n            body: JSON.stringify(contactData)\n        });\n    }\n    // Newsletter endpoints\n    async subscribeToNewsletter(email) {\n        return this.request('/newsletter/subscribe', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Settings endpoint\n    async getSettings() {\n        return this.request('/settings');\n    }\n    // Partner Dashboard endpoints\n    async getPartnerDashboard() {\n        return this.request('/partner-dashboard');\n    }\n    // Partner Scholarship endpoints\n    async getPartnerAvailableScholarships(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/partner-scholarships/available\".concat(queryString));\n    }\n    async getPartnerScholarshipDetails(scholarshipId) {\n        return this.request(\"/partner-scholarships/\".concat(scholarshipId, \"/details\"));\n    }\n    async applyForStudentScholarship(scholarshipId, applicationData) {\n        return this.request(\"/partner-scholarships/\".concat(scholarshipId, \"/apply\"), {\n            method: 'POST',\n            body: applicationData\n        });\n    }\n    async getPartnerApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/partner-scholarships/my-applications\".concat(queryString));\n    }\n    async getPartnerStudents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/partner-students\".concat(queryString));\n    }\n    async addPartnerStudent(studentData) {\n        return this.request('/partner-students', {\n            method: 'POST',\n            body: studentData\n        });\n    }\n    constructor(baseURL = API_BASE_URL !== null && API_BASE_URL !== void 0 ? API_BASE_URL : \"\"){\n        this.baseURL = baseURL;\n    }\n}\n// Create and export a default instance\nconst apiClient = new ApiClient();\n// Export the class for custom instances\n\n// Export the extractArrayData utility function\n // Authentication is now handled by Laravel - helper functions removed\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});