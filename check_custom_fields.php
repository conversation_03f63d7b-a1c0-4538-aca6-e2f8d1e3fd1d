<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CHECKING CUSTOM FIELDS IN DATABASE ===\n\n";

// Check total scholarship fields
$allFields = App\Models\ScholarshipField::all();
echo "Total Scholarship Fields in DB: " . $allFields->count() . "\n\n";

if ($allFields->count() > 0) {
    echo "All Scholarship Fields:\n";
    foreach ($allFields as $field) {
        echo "  - ID: {$field->id}, Name: {$field->field_name}, Type: {$field->field_type}, Scholarship ID: {$field->scholarship_id}\n";
    }
    echo "\n";
}

// Check scholarships with custom fields
$scholarships = App\Models\Scholarship::with('fields')->get();
echo "Scholarships and their scholarship fields:\n";
foreach ($scholarships as $scholarship) {
    echo "  - {$scholarship->title} (ID: {$scholarship->id}): {$scholarship->fields->count()} scholarship fields\n";
    if ($scholarship->fields->count() > 0) {
        foreach ($scholarship->fields as $field) {
            echo "    * {$field->field_name} ({$field->field_type})\n";
        }
    }
}

echo "\n";

// Check if the relationship is working
$firstScholarship = App\Models\Scholarship::with('fields')->first();
if ($firstScholarship) {
    echo "Testing relationship for first scholarship:\n";
    echo "  - Scholarship: {$firstScholarship->title}\n";
    echo "  - Scholarship Fields: {$firstScholarship->fields->count()}\n";

    // Test the API response format
    $apiData = $firstScholarship->toArray();
    if (isset($apiData['custom_fields'])) {
        echo "  - API includes custom_fields: YES\n";
        echo "  - Custom fields count in API: " . count($apiData['custom_fields']) . "\n";
    } else {
        echo "  - API includes custom_fields: NO\n";
    }
}

?>
