<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use App\Models\Scholarship;
use App\Models\User;
use Carbon\Carbon;

class ScholarshipApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get scholarships
        $primaryScholarship = Scholarship::where('category', 'primary')->first();
        $secondaryScholarship = Scholarship::where('category', 'secondary')->first();
        $universityScholarship = Scholarship::where('category', 'university')->first();

        // Create sample users if they don't exist
        $users = [];
        for ($i = 1; $i <= 10; $i++) {
            $users[] = User::firstOrCreate([
                'email' => "testuser{$i}@example.com"
            ], [
                'first_name' => "Test{$i}",
                'last_name' => "User",
                'password' => bcrypt('password'),
                'role' => 'user',
                'status' => 'active',
                'email_verified_at' => now()
            ]);
        }

        // Create Primary School Applications
        if ($primaryScholarship) {
            for ($i = 0; $i < 3; $i++) {
                $application = ScholarshipApplication::create([
                    'scholarship_id' => $primaryScholarship->id,
                    'user_id' => $users[$i]->id,
                    'form_data' => [
                        'student_full_name' => "Primary Student " . ($i + 1),
                        'student_age' => rand(6, 12),
                        'current_class' => 'Primary ' . rand(1, 6),
                        'father_name' => "Father Name " . ($i + 1),
                        'mother_name' => "Mother Name " . ($i + 1),
                        'parent_phone' => "+234" . rand(**********, **********),
                        'home_address' => "123 Test Street, Lagos State, Nigeria",
                        'school_name' => "Primary School " . ($i + 1),
                        'headmaster_name' => "Headmaster " . ($i + 1),
                        'school_account_number' => "ACC" . rand(**********, **********),
                        'scholarship_reason' => "I need this scholarship to support my child's education. Our family is facing financial difficulties and this support would help ensure my child can continue their studies without interruption.",
                        'school_fee_amount' => rand(20000, 50000),
                        'supporting_information' => "Additional information about the student's academic performance and family situation."
                    ],
                    'application_data' => [
                        'reason_for_scholarship' => "Financial assistance needed for primary education",
                        'current_school_fee' => rand(20000, 50000)
                    ],
                    'status' => ['pending', 'under_review', 'approved'][rand(0, 2)],
                    'score' => rand(70, 95),
                    'submitted_at' => Carbon::now()->subDays(rand(1, 30)),
                    'reviewed_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 15)) : null
                ]);

                // Create sample file record
                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => 'student_picture',
                    'original_name' => "student_photo_{$i}.jpg",
                    'stored_name' => "stored_student_photo_{$i}.jpg",
                    'file_path' => "scholarship_applications/student_photo_{$i}.jpg",
                    'mime_type' => 'image/jpeg',
                    'file_size' => rand(100000, 500000),
                    'is_verified' => rand(0, 1)
                ]);
            }
        }

        // Create Secondary School Applications
        if ($secondaryScholarship) {
            for ($i = 3; $i < 6; $i++) {
                $application = ScholarshipApplication::create([
                    'scholarship_id' => $secondaryScholarship->id,
                    'user_id' => $users[$i]->id,
                    'form_data' => [
                        'student_full_name' => "Secondary Student " . ($i + 1),
                        'student_age' => rand(13, 18),
                        'current_class' => ['JSS1', 'JSS2', 'JSS3', 'SS1', 'SS2', 'SS3'][rand(0, 5)],
                        'father_name' => "Father Name " . ($i + 1),
                        'mother_name' => "Mother Name " . ($i + 1),
                        'student_phone' => "+234" . rand(**********, **********),
                        'parent_phone' => "+234" . rand(**********, **********),
                        'home_address' => "456 Secondary Street, Abuja, Nigeria",
                        'school_name' => "Secondary School " . ($i + 1),
                        'principal_name' => "Principal " . ($i + 1),
                        'school_account_number' => "SEC" . rand(**********, **********),
                        'scholarship_reason' => "I am a dedicated student seeking financial assistance to continue my secondary education. I maintain excellent grades and participate actively in school activities.",
                        'school_fee_amount' => rand(50000, 80000),
                        'academic_goals' => "I aim to excel in my studies and pursue higher education in engineering/medicine."
                    ],
                    'application_data' => [
                        'reason_for_scholarship' => "Academic excellence scholarship for secondary education",
                        'class' => ['JSS1', 'JSS2', 'JSS3', 'SS1', 'SS2', 'SS3'][rand(0, 5)]
                    ],
                    'status' => ['pending', 'under_review', 'approved'][rand(0, 2)],
                    'score' => rand(75, 98),
                    'submitted_at' => Carbon::now()->subDays(rand(1, 25)),
                    'reviewed_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 12)) : null
                ]);

                // Create sample file record
                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => 'student_picture',
                    'original_name' => "secondary_photo_{$i}.jpg",
                    'stored_name' => "stored_secondary_photo_{$i}.jpg",
                    'file_path' => "scholarship_applications/secondary_photo_{$i}.jpg",
                    'mime_type' => 'image/jpeg',
                    'file_size' => rand(100000, 500000),
                    'is_verified' => rand(0, 1)
                ]);
            }
        }

        // Create University Applications
        if ($universityScholarship) {
            for ($i = 6; $i < 9; $i++) {
                $application = ScholarshipApplication::create([
                    'scholarship_id' => $universityScholarship->id,
                    'user_id' => $users[$i]->id,
                    'form_data' => [
                        'full_name' => "University Student " . ($i + 1),
                        'age' => rand(18, 25),
                        'course_of_study' => ['Computer Science', 'Engineering', 'Medicine', 'Law', 'Business Administration'][rand(0, 4)],
                        'current_level' => ['100L', '200L', '300L', '400L', '500L'][rand(0, 4)],
                        'phone' => "+234" . rand(**********, **********),
                        'email' => "university{$i}@example.com",
                        'matriculation_number' => "UNI/" . rand(2020, 2024) . "/" . rand(1000, 9999),
                        'scholarship_reason' => "I am pursuing a degree in my chosen field and require financial assistance to complete my studies. I have maintained excellent academic performance and am committed to contributing to society.",
                        'academic_achievements' => "Dean's List for 3 consecutive semesters, President of Student Association, Research publication in undergraduate journal.",
                        'future_goals' => "I plan to pursue graduate studies and contribute to technological advancement in Nigeria."
                    ],
                    'application_data' => [
                        'reason_for_scholarship' => "Merit-based university scholarship",
                        'matric_number' => "UNI/" . rand(2020, 2024) . "/" . rand(1000, 9999),
                        'personal_statement' => "I am passionate about my field of study and committed to academic excellence.",
                        'career_goals' => "To become a leader in my field and contribute to national development."
                    ],
                    'status' => ['pending', 'under_review', 'approved'][rand(0, 2)],
                    'score' => rand(80, 100),
                    'submitted_at' => Carbon::now()->subDays(rand(1, 20)),
                    'reviewed_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 10)) : null
                ]);

                // Create sample file records
                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => 'student_id_card',
                    'original_name' => "university_id_{$i}.pdf",
                    'stored_name' => "stored_university_id_{$i}.pdf",
                    'file_path' => "scholarship_applications/university_id_{$i}.pdf",
                    'mime_type' => 'application/pdf',
                    'file_size' => rand(200000, 800000),
                    'is_verified' => rand(0, 1)
                ]);

                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => 'payment_evidence',
                    'original_name' => "payment_receipt_{$i}.pdf",
                    'stored_name' => "stored_payment_receipt_{$i}.pdf",
                    'file_path' => "scholarship_applications/payment_receipt_{$i}.pdf",
                    'mime_type' => 'application/pdf',
                    'file_size' => rand(150000, 600000),
                    'is_verified' => rand(0, 1)
                ]);
            }
        }

        $this->command->info('✅ Sample scholarship applications created successfully!');
        $this->command->info('📝 Created applications for all three categories:');
        $this->command->info('   - 3 Primary School applications');
        $this->command->info('   - 3 Secondary School applications');
        $this->command->info('   - 3 University applications');
        $this->command->info('📎 Added sample file uploads for each application');
    }
}
