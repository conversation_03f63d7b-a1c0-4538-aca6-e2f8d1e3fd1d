"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Play, ChevronLeft, ChevronRight, Pause } from "lucide-react"
import { useInView } from "react-intersection-observer"

const heroImages = [
  {
    id: 1,
    src: "/placeholder.svg?height=800&width=1200&text=Students+Receiving+Materials&bg=22c55e&color=ffffff",
    alt: "Students receiving educational materials from Kofa Foundation",
    title: "Educational Materials Distribution",
    description: "Providing essential learning resources to students across Northern Nigeria",
    category: "Materials",
    location: "Zaria, Kaduna State",
    year: "2024",
  },
  {
    id: 2,
    src: "/placeholder.svg?height=800&width=1200&text=Modern+Classroom&bg=059669&color=ffffff",
    alt: "Modern classroom environment with students learning",
    title: "Transformed Learning Spaces",
    description: "Creating conducive learning environments for quality education",
    category: "Infrastructure",
    location: "Kano State",
    year: "2023",
  },
  {
    id: 3,
    src: "/placeholder.svg?height=800&width=1200&text=Scholarship+Ceremony&bg=f59e0b&color=ffffff",
    alt: "Scholarship award ceremony with students and founder",
    title: "Scholarship Award Ceremony",
    description: "Celebrating academic excellence and supporting higher education dreams",
    category: "Scholarships",
    location: "ABU Zaria",
    year: "2024",
  },
  {
    id: 4,
    src: "/placeholder.svg?height=800&width=1200&text=Community+Outreach&bg=7c3aed&color=ffffff",
    alt: "Community outreach program in rural Northern Nigeria",
    title: "Community Literacy Programs",
    description: "Empowering communities through adult education and literacy initiatives",
    category: "Outreach",
    location: "Rural Kaduna",
    year: "2023",
  },
  {
    id: 5,
    src: "/placeholder.svg?height=800&width=1200&text=Graduation+Day&bg=dc2626&color=ffffff",
    alt: "Students celebrating graduation with certificates",
    title: "Success Stories",
    description: "Witnessing the transformation of lives through education",
    category: "Achievement",
    location: "Multiple Locations",
    year: "2024",
  },
]

const impactStats = [
  { number: "1,200+", label: "Students Supported", icon: "👨‍🎓" },
  { number: "45", label: "Schools Partnered", icon: "🏫" },
  { number: "5,000+", label: "Books Donated", icon: "📚" },
  { number: "₦12M", label: "Scholarships Awarded", icon: "💰" },
]

export function HyperRealisticHero() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})
  const [imageErrors, setImageErrors] = useState<Record<number, boolean>>({})
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovered, setIsHovered] = useState(false)
  const heroRef = useRef<HTMLElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  })

  // Handle mouse movement for parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const rect = heroRef.current.getBoundingClientRect()
        const x = (e.clientX - rect.left) / rect.width
        const y = (e.clientY - rect.top) / rect.height
        setMousePosition({ x, y })
      }
    }

    const heroElement = heroRef.current
    if (heroElement) {
      heroElement.addEventListener("mousemove", handleMouseMove)
      return () => heroElement.removeEventListener("mousemove", handleMouseMove)
    }
  }, [])

  // Auto-play carousel
  useEffect(() => {
    if (isPlaying && inView && !isHovered) {
      intervalRef.current = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
      }, 5000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, inView, isHovered])

  const handleImageLoad = (index: number) => {
    setImagesLoaded((prev) => ({ ...prev, [index]: true }))
  }

  const handleImageError = (index: number) => {
    setImageErrors((prev) => ({ ...prev, [index]: true }))
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + heroImages.length) % heroImages.length)
  }

  const currentImage = heroImages[currentImageIndex]

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen overflow-hidden bg-gradient-to-br from-green-950 via-green-900 to-green-800"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Floating Particles */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-amber-400/30 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`,
            }}
          />
        ))}

        {/* Parallax Background Layers */}
        <div
          className="absolute inset-0 bg-gradient-to-r from-green-900/50 to-transparent"
          style={{
            transform: `translate(${mousePosition.x * 10}px, ${mousePosition.y * 10}px)`,
            transition: "transform 0.3s ease-out",
          }}
        />
        <div
          className="absolute inset-0 bg-grid opacity-10"
          style={{
            transform: `translate(${mousePosition.x * -5}px, ${mousePosition.y * -5}px)`,
            transition: "transform 0.3s ease-out",
          }}
        />
      </div>

      {/* Main Content Container */}
      <div ref={ref} className="container relative h-screen flex items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
          {/* Content Section */}
          <div className="space-y-8 z-20">
            {/* Badge */}
            <div className="inline-flex items-center gap-3 rounded-full bg-white/10 backdrop-blur-md px-6 py-3 text-sm text-white border border-white/20 animate-fade-in-up">
              <div className="relative">
                <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-amber-400 rounded-full animate-ping"></div>
              </div>
              <span className="font-medium">Transforming Lives Since 2015</span>
              <div className="text-amber-400">✨</div>
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
            <h1 className="text-5xl font-bold tracking-tighter text-white sm:text-6xl md:text-7xl lg:text-8xl animate-fade-in-up leading-tight">
              Empowering Nigeria Through <br />
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
                Education & Opportunity
              </span>
            </h1>

              {/* Subheading */}
            <p
              className="text-xl text-green-100 md:text-2xl max-w-2xl animate-fade-in-up leading-relaxed"
              style={{ animationDelay: "0.2s" }}
            >
              Since 2015, we've been dedicated to providing scholarships, essential school supplies, and unwavering hope
              to students, underprivileged people, and those in need across Nigeria. Join us in forging a brighter future for all.
            </p>
            </div>

            {/* Impact Stats */}
            <div
              className="grid grid-cols-2 md:grid-cols-4 gap-4 animate-fade-in-up"
              style={{ animationDelay: "0.3s" }}
            >
              {impactStats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="text-2xl mb-1 group-hover:scale-110 transition-transform duration-300">
                    {stat.icon}
                  </div>
                  <div className="text-2xl font-bold text-amber-400">{stat.number}</div>
                  <div className="text-green-200 text-sm font-medium">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style={{ animationDelay: "0.4s" }}>
              <Link href="/donate">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-green-950 shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 hover:scale-105 rounded-full font-bold px-8 py-4 text-lg group relative overflow-hidden"
                >
                  <span className="relative z-10 flex items-center">
                    Donate Now
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </Button>
              </Link>
              <Link href="/students">
                <Button
                  size="lg"
                  variant="outline"
                  className="text-white border-2 border-white/50 hover:bg-white/10 backdrop-blur-md transition-all duration-300 rounded-full font-bold px-8 py-4 text-lg group hover:border-white/80"
                >
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  Meet Our Students
                </Button>
              </Link>
            </div>
          </div>

          {/* Image Carousel Section */}
          <div className="relative h-full flex items-center justify-center">
            <div className="relative w-full max-w-2xl">
              {/* Main Image Container */}
              <div className="relative aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl border-4 border-white/20 group">
                {/* Loading State */}
                {!imagesLoaded[currentImageIndex] && !imageErrors[currentImageIndex] && (
                  <div className="absolute inset-0 bg-green-800/50 animate-pulse flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-12 h-12 border-4 border-amber-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-white text-lg font-medium">Loading Impact Story...</p>
                    </div>
                  </div>
                )}

                {/* Error State */}
                {imageErrors[currentImageIndex] && (
                  <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-800 flex flex-col items-center justify-center text-white p-8">
                    <div className="text-6xl mb-4">📚</div>
                    <h3 className="text-2xl font-bold mb-2">Kofa Foundation</h3>
                    <p className="text-center text-lg opacity-90 leading-relaxed">{currentImage.description}</p>
                    <div className="mt-4 px-4 py-2 bg-white/20 rounded-full text-sm font-medium">
                      {currentImage.category}
                    </div>
                  </div>
                )}

                {/* Main Image */}
                {!imageErrors[currentImageIndex] && (
                  <Image
                    src={currentImage.src || "/placeholder.svg"}
                    alt={currentImage.alt}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className={`object-cover transition-all duration-700 group-hover:scale-105 ${
                      imagesLoaded[currentImageIndex] ? "opacity-100" : "opacity-0"
                    }`}
                    onLoad={() => handleImageLoad(currentImageIndex)}
                    onError={() => handleImageError(currentImageIndex)}
                    priority={currentImageIndex === 0}
                  />
                )}

                {/* Image Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="px-3 py-1 bg-amber-500 rounded-full text-xs font-bold text-green-950">
                        {currentImage.category}
                      </span>
                      <span className="text-sm opacity-80">{currentImage.location}</span>
                      <span className="text-sm opacity-80">•</span>
                      <span className="text-sm opacity-80">{currentImage.year}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-2">{currentImage.title}</h3>
                    <p className="text-sm opacity-90 leading-relaxed">{currentImage.description}</p>
                  </div>
                </div>
              </div>

              {/* Navigation Controls */}
              <div className="absolute top-1/2 -translate-y-1/2 -left-6">
                <Button
                  onClick={prevImage}
                  size="icon"
                  className="bg-white/20 backdrop-blur-md hover:bg-white/30 border border-white/30 rounded-full w-12 h-12 text-white hover:scale-110 transition-all duration-300"
                  aria-label="Previous image"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
              </div>
              <div className="absolute top-1/2 -translate-y-1/2 -right-6">
                <Button
                  onClick={nextImage}
                  size="icon"
                  className="bg-white/20 backdrop-blur-md hover:bg-white/30 border border-white/30 rounded-full w-12 h-12 text-white hover:scale-110 transition-all duration-300"
                  aria-label="Next image"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </div>

              {/* Play/Pause Control */}
              <div className="absolute top-4 right-4">
                <Button
                  onClick={() => setIsPlaying(!isPlaying)}
                  size="icon"
                  className="bg-black/50 backdrop-blur-md hover:bg-black/70 border border-white/30 rounded-full w-10 h-10 text-white"
                  aria-label={isPlaying ? "Pause slideshow" : "Play slideshow"}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
              </div>

              {/* Image Indicators */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
                {heroImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentImageIndex
                        ? "bg-amber-400 scale-125 shadow-lg shadow-amber-400/50"
                        : "bg-white/50 hover:bg-white/80"
                    }`}
                    aria-label={`View image ${index + 1}: ${heroImages[index].title}`}
                  />
                ))}
              </div>

              {/* Progress Bar */}
              <div className="absolute -bottom-12 left-0 right-0 h-1 bg-white/20 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-amber-400 to-amber-600 transition-all duration-300 ease-linear"
                  style={{
                    width: `${((currentImageIndex + 1) / heroImages.length) * 100}%`,
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="flex flex-col items-center gap-2 text-white/70">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
          </div>
          <span className="text-xs font-medium">Scroll to explore</span>
        </div>
      </div>

      {/* Background Video Simulation */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-green-900/30 to-transparent animate-pulse" />
      </div>
    </section>
  )
}
