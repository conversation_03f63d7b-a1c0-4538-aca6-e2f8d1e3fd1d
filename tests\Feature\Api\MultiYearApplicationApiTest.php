<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\ScholarshipApplication;
use App\Models\Scholarship;
use App\Models\StudentProgression;
use Laravel\Sanctum\Sanctum;

class MultiYearApplicationApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $partnerUser;
    protected $partnerOrganization;
    protected $adminUser;
    protected $studentUser;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create partner organization
        $this->partnerOrganization = PartnerOrganization::factory()->create([
            'name' => 'Test School',
            'type' => 'school',
            'status' => 'active'
        ]);

        // Create partner user
        $this->partnerUser = User::factory()->create([
            'role' => 'partner_organization',
            'partner_organization_id' => $this->partnerOrganization->id
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin'
        ]);

        // Create student user
        $this->studentUser = User::factory()->create([
            'role' => 'user'
        ]);

        // Create student
        $this->student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id,
            'user_id' => $this->studentUser->id
        ]);
    }

    /** @test */
    public function partner_can_access_student_application_history()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create scholarships and applications across multiple years
        $scholarship2023 = Scholarship::factory()->create([
            'category' => 'primary',
            'academic_year' => 2023
        ]);

        $scholarship2024 = Scholarship::factory()->create([
            'category' => 'secondary',
            'academic_year' => 2024
        ]);

        ScholarshipApplication::factory()->create([
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship2023->id,
            'status' => 'approved',
            'created_at' => now()->subYear()
        ]);

        ScholarshipApplication::factory()->create([
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship2024->id,
            'status' => 'pending'
        ]);

        $response = $this->getJson("/api/v1/multi-year-applications/student/{$this->student->id}/history");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'student',
                        'applications_by_year',
                        'summary' => [
                            'total_applications',
                            'approved_applications',
                            'years_active',
                            'success_rate'
                        ]
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals(2, $response->json('data.summary.total_applications'));
    }

    /** @test */
    public function student_can_access_own_application_history()
    {
        Sanctum::actingAs($this->studentUser);

        $scholarship = Scholarship::factory()->create([
            'category' => 'university'
        ]);

        ScholarshipApplication::factory()->create([
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'status' => 'approved'
        ]);

        $response = $this->getJson("/api/v1/multi-year-applications/student/{$this->student->id}/history");

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function partner_cannot_access_other_school_student_applications()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create student from different school
        $otherStudent = Student::factory()->create([
            'school_id' => 999 // Different school
        ]);

        $response = $this->getJson("/api/v1/multi-year-applications/student/{$otherStudent->id}/history");

        $response->assertStatus(403);
    }

    /** @test */
    public function can_get_eligible_scholarships_for_student()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create current progression for student
        StudentProgression::factory()->create([
            'student_id' => $this->student->id,
            'grade_level' => 5,
            'academic_year' => now()->year,
            'status' => 'completed',
            'is_current' => true
        ]);

        // Create scholarships for different categories
        $primaryScholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'is_open' => true,
            'application_deadline' => now()->addMonths(2)
        ]);

        $universityScholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'is_open' => true,
            'application_deadline' => now()->addMonths(2)
        ]);

        $response = $this->getJson("/api/v1/multi-year-applications/student/{$this->student->id}/eligible-scholarships");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'student',
                        'current_progression',
                        'eligible_scholarships',
                        'eligibility_criteria'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        
        // Student in grade 5 should be eligible for primary scholarships
        $eligibleScholarships = $response->json('data.eligible_scholarships');
        $this->assertGreaterThan(0, count($eligibleScholarships));
    }

    /** @test */
    public function can_create_multi_year_application()
    {
        Sanctum::actingAs($this->partnerUser);

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'is_open' => true
        ]);

        $applicationData = [
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'academic_year' => now()->year,
            'application_data' => [
                'student_name' => 'John Doe',
                'parent_name' => 'Jane Doe',
                'contact_phone' => '1234567890'
            ],
            'is_multi_year' => true,
            'renewal_criteria' => [
                'minimum_grade' => 'B',
                'attendance_rate' => 85
            ]
        ];

        $response = $this->postJson('/api/v1/multi-year-applications/create', $applicationData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'application',
                        'tracking_info'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertDatabaseHas('scholarship_applications', [
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'is_multi_year' => true
        ]);
    }

    /** @test */
    public function university_student_can_create_own_application()
    {
        Sanctum::actingAs($this->studentUser);

        // Create university student progression
        StudentProgression::factory()->create([
            'student_id' => $this->student->id,
            'grade_level' => 13, // University level
            'academic_year' => now()->year,
            'status' => 'completed',
            'is_current' => true
        ]);

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'is_open' => true
        ]);

        $applicationData = [
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'academic_year' => now()->year,
            'application_data' => [
                'student_name' => 'John Doe',
                'matriculation_number' => 'MAT123456',
                'course_of_study' => 'Computer Science'
            ]
        ];

        $response = $this->postJson('/api/v1/multi-year-applications/create', $applicationData);

        $response->assertStatus(201);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function partner_cannot_create_university_applications()
    {
        Sanctum::actingAs($this->partnerUser);

        $scholarship = Scholarship::factory()->create([
            'category' => 'university',
            'status' => 'active',
            'is_open' => true
        ]);

        $applicationData = [
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'academic_year' => now()->year,
            'application_data' => []
        ];

        $response = $this->postJson('/api/v1/multi-year-applications/create', $applicationData);

        $response->assertStatus(403);
    }

    /** @test */
    public function student_cannot_create_primary_secondary_applications()
    {
        Sanctum::actingAs($this->studentUser);

        $scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'status' => 'active',
            'is_open' => true
        ]);

        $applicationData = [
            'student_id' => $this->student->id,
            'scholarship_id' => $scholarship->id,
            'academic_year' => now()->year,
            'application_data' => []
        ];

        $response = $this->postJson('/api/v1/multi-year-applications/create', $applicationData);

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_access_multi_year_analytics()
    {
        Sanctum::actingAs($this->adminUser);

        // Create test data
        $students = Student::factory()->count(5)->create();
        $scholarship = Scholarship::factory()->create(['category' => 'primary']);

        foreach ($students as $student) {
            ScholarshipApplication::factory()->create([
                'student_id' => $student->id,
                'scholarship_id' => $scholarship->id,
                'is_multi_year' => true,
                'status' => 'approved'
            ]);
        }

        $response = $this->getJson('/api/v1/multi-year-applications/analytics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_multi_year_applications',
                        'renewal_success_rate',
                        'student_retention_rate',
                        'category_distribution',
                        'yearly_trends',
                        'performance_metrics'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function partner_can_access_analytics()
    {
        Sanctum::actingAs($this->partnerUser);

        $response = $this->getJson('/api/v1/multi-year-applications/analytics');

        $response->assertStatus(200);
        $this->assertTrue($response->json('success'));
    }

    /** @test */
    public function student_cannot_access_analytics()
    {
        Sanctum::actingAs($this->studentUser);

        $response = $this->getJson('/api/v1/multi-year-applications/analytics');

        $response->assertStatus(403);
    }

    /** @test */
    public function application_creation_validates_required_fields()
    {
        Sanctum::actingAs($this->partnerUser);

        $response = $this->postJson('/api/v1/multi-year-applications/create', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'student_id',
                    'scholarship_id',
                    'academic_year',
                    'application_data'
                ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_endpoints()
    {
        $endpoints = [
            "/api/v1/multi-year-applications/student/{$this->student->id}/history",
            "/api/v1/multi-year-applications/student/{$this->student->id}/eligible-scholarships",
            '/api/v1/multi-year-applications/analytics'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            $response->assertStatus(401);
        }

        $response = $this->postJson('/api/v1/multi-year-applications/create', []);
        $response->assertStatus(401);
    }
}
