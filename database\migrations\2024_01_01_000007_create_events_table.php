<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title', 200);
            $table->string('slug', 220)->unique();
            $table->text('description');
            $table->enum('event_type', ['fundraising', 'volunteer', 'awareness', 'training', 'other']);
            $table->timestamp('start_datetime');
            $table->timestamp('end_datetime');
            $table->string('venue', 200)->nullable();
            $table->text('address')->nullable();
            $table->integer('capacity')->nullable();
            $table->timestamp('registration_deadline')->nullable();
            $table->json('requirements')->nullable();
            $table->json('agenda')->nullable();
            $table->string('featured_image')->nullable();
            $table->enum('status', ['draft', 'published', 'cancelled'])->default('draft');
            $table->foreignId('organizer_id')->constrained('users');
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'start_datetime']);
            $table->index(['organizer_id', 'status']);
            $table->index('slug');
            $table->index(['start_datetime', 'end_datetime']);
            $table->index('event_type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('events');
    }
}; 