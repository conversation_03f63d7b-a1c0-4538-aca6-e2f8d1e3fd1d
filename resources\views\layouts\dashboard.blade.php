<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Dashboard') - {{ config('app.name', 'HALIMAKQ Foundation') }}</title>
    <meta name="description" content="@yield('description', 'Dashboard for HALIMAKQ Foundation NGO platform.')">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    @stack('styles')
</head>

<body class="bg-gray-50 font-sans antialiased">
    <!-- Dashboard Content -->
    <div id="app">
        @yield('content')
    </div>

    <!-- Scripts -->
    <script>
        // Mobile menu toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileSidebar = document.getElementById('mobile-sidebar');
            const mobileSidebarContent = document.getElementById('mobile-sidebar-content');
            const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');

            if (mobileMenuBtn && mobileSidebar) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileSidebar.classList.remove('hidden');
                    setTimeout(() => {
                        mobileSidebarContent.classList.remove('-translate-x-full');
                    }, 10);
                });
            }

            // Close mobile menu when clicking overlay
            if (mobileSidebarOverlay) {
                mobileSidebarOverlay.addEventListener('click', function() {
                    closeMobileMenu();
                });
            }

            // Function to close mobile menu
            function closeMobileMenu() {
                if (mobileSidebarContent) {
                    mobileSidebarContent.classList.add('-translate-x-full');
                    setTimeout(() => {
                        if (mobileSidebar) {
                            mobileSidebar.classList.add('hidden');
                        }
                    }, 300);
                }
            }

            // Close mobile menu on escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeMobileMenu();
                }
            });
        });

        // Show Laravel session messages
        @if(session('success'))
            document.addEventListener('DOMContentLoaded', function() {
                // You can add a toast notification library here
                alert('{{ session('success') }}');
            });
        @endif

        @if(session('error'))
            document.addEventListener('DOMContentLoaded', function() {
                alert('{{ session('error') }}');
            });
        @endif
    </script>

    @stack('scripts')
</body>
</html>
