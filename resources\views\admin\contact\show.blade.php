@extends('layouts.admin')

@section('title', 'Contact Message Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Contact Message Details</h1>
        <div>
            <a href="{{ route('admin.contact.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Messages
            </a>
            @if($message->status === 'new')
                <button class="btn btn-warning" onclick="markAsInProgress({{ $message->id }})">
                    <i class="fas fa-play me-2"></i>Mark In Progress
                </button>
            @elseif($message->status === 'in_progress')
                <button class="btn btn-success" onclick="markAsResolved({{ $message->id }})">
                    <i class="fas fa-check me-2"></i>Mark Resolved
                </button>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ $message->subject ?? 'General Inquiry' }}</h6>
                    <span class="badge bg-{{ 
                        $message->status === 'new' ? 'danger' : 
                        ($message->status === 'in_progress' ? 'warning' : 'success') 
                    }}">
                        {{ ucfirst(str_replace('_', ' ', $message->status ?? 'new')) }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary">Contact Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $message->name ?? 'John Doe' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        <a href="mailto:{{ $message->email ?? '<EMAIL>' }}">
                                            {{ $message->email ?? '<EMAIL>' }}
                                        </a>
                                    </td>
                                </tr>
                                @if($message->phone ?? '+234 ************')
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>
                                            <a href="tel:{{ $message->phone ?? '+234 ************' }}">
                                                {{ $message->phone ?? '+234 ************' }}
                                            </a>
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Message Details</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Subject:</strong></td>
                                    <td>{{ $message->subject ?? 'General Inquiry' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ 
                                            ($message->priority ?? 'medium') === 'high' ? 'danger' : 
                                            (($message->priority ?? 'medium') === 'medium' ? 'warning' : 'info') 
                                        }}">
                                            {{ ucfirst($message->priority ?? 'medium') }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Received:</strong></td>
                                    <td>
                                        {{ ($message->created_at ?? now())->format('M d, Y g:i A') }}
                                        <br><small class="text-muted">{{ ($message->created_at ?? now())->diffForHumans() }}</small>
                                    </td>
                                </tr>
                                @if($message->assigned_to ?? null)
                                    <tr>
                                        <td><strong>Assigned To:</strong></td>
                                        <td>{{ $message->assignedUser->name ?? 'Admin User' }}</td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary">Message Content</h6>
                        <div class="bg-light p-3 rounded">
                            <p>{{ $message->message ?? 'Hello, I am interested in learning more about your programs and how I can get involved as a volunteer. Could you please provide me with more information about the application process and requirements? I have experience in community outreach and education, and I believe I could contribute meaningfully to your mission. Thank you for your time and I look forward to hearing from you soon.' }}</p>
                        </div>
                    </div>

                    @if($message->attachments ?? false)
                        <div class="mb-4">
                            <h6 class="text-primary">Attachments</h6>
                            <div class="list-group">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-pdf text-danger me-2"></i>resume.pdf (245 KB)
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Reply Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Send Reply</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.contact.messages.reply', $message->id ?? 1) }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="reply_subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="reply_subject" name="subject" value="Re: {{ $message->subject ?? 'General Inquiry' }}">
                        </div>
                        <div class="mb-3">
                            <label for="reply_message" class="form-label">Message</label>
                            <textarea class="form-control" id="reply_message" name="message" rows="6" placeholder="Type your reply here..."></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="mark_resolved" name="mark_resolved" value="1">
                                <label class="form-check-label" for="mark_resolved">
                                    Mark as resolved after sending
                                </label>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane me-2"></i>Send Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="copyToClipboard('{{ $message->email ?? '<EMAIL>' }}')">
                            <i class="fas fa-copy me-2"></i>Copy Email
                        </button>
                        <a href="mailto:{{ $message->email ?? '<EMAIL>' }}" class="btn btn-outline-success">
                            <i class="fas fa-envelope me-2"></i>Email Directly
                        </a>
                        @if($message->phone ?? '+234 ************')
                            <a href="tel:{{ $message->phone ?? '+234 ************' }}" class="btn btn-outline-info">
                                <i class="fas fa-phone me-2"></i>Call
                            </a>
                        @endif
                        <button class="btn btn-outline-warning" onclick="assignToMe({{ $message->id ?? 1 }})">
                            <i class="fas fa-user me-2"></i>Assign to Me
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteMessage({{ $message->id ?? 1 }})">
                            <i class="fas fa-trash me-2"></i>Delete Message
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Message History</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Message Received</h6>
                                <p class="text-muted mb-0 small">{{ ($message->created_at ?? now())->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        @if(($message->status ?? 'new') !== 'new')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Status Updated</h6>
                                    <p class="text-muted mb-0 small">{{ ($message->updated_at ?? now())->format('M d, Y g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #e3e6f0;
}

.timeline-marker {
    position: absolute;
    left: -20px;
    top: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.timeline-content {
    margin-left: 10px;
}
</style>

<script>
function markAsInProgress(messageId) {
    if (confirm('Mark this message as in progress?')) {
        // Add AJAX call here
        console.log('Mark as in progress:', messageId);
    }
}

function markAsResolved(messageId) {
    if (confirm('Mark this message as resolved?')) {
        // Add AJAX call here
        console.log('Mark as resolved:', messageId);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Email copied to clipboard!');
    });
}

function assignToMe(messageId) {
    if (confirm('Assign this message to yourself?')) {
        // Add AJAX call here
        console.log('Assign to me:', messageId);
    }
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        // Add delete functionality here
        console.log('Delete message:', messageId);
    }
}
</script>
@endsection 