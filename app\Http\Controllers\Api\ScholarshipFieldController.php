<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ScholarshipField;
use App\Models\Scholarship;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

class ScholarshipFieldController extends Controller
{
    /**
     * Get all fields for a scholarship
     */
    public function index($scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        
        $fields = $scholarship->fields()
                            ->ordered()
                            ->get();

        return response()->json([
            'success' => true,
            'data' => $fields
        ]);
    }

    /**
     * Store a new field for a scholarship
     */
    public function store(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'field_name' => 'required|string|max:100',
            'field_label' => 'required|string|max:200',
            'field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'field_options' => 'nullable|array',
            'is_required' => 'boolean',
            'field_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['scholarship_id'] = $scholarship->id;
        
        // Auto-generate field_order if not provided
        if (!isset($data['field_order'])) {
            $maxOrder = $scholarship->fields()->max('field_order') ?? 0;
            $data['field_order'] = $maxOrder + 1;
        }

        $field = ScholarshipField::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Field created successfully',
            'data' => $field
        ], 201);
    }

    /**
     * Show a specific field
     */
    public function show($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        return response()->json([
            'success' => true,
            'data' => $field
        ]);
    }

    /**
     * Update a field
     */
    public function update(Request $request, $scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        $validator = Validator::make($request->all(), [
            'field_name' => 'string|max:100',
            'field_label' => 'string|max:200',
            'field_type' => 'string|in:text,textarea,email,tel,number,date,file,select',
            'field_options' => 'nullable|array',
            'is_required' => 'boolean',
            'field_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $field->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Field updated successfully',
            'data' => $field->fresh()
        ]);
    }

    /**
     * Delete a field
     */
    public function destroy($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        $field->delete();

        return response()->json([
            'success' => true,
            'message' => 'Field deleted successfully'
        ]);
    }

    /**
     * Reorder fields
     */
    public function reorder(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'fields' => 'required|array',
            'fields.*.id' => 'required|integer|exists:scholarship_fields,id',
            'fields.*.field_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        foreach ($request->fields as $fieldData) {
            $field = $scholarship->fields()->find($fieldData['id']);
            if ($field) {
                $field->update(['field_order' => $fieldData['field_order']]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Fields reordered successfully'
        ]);
    }

    /**
     * Toggle field active status
     */
    public function toggleActive($scholarshipId, $fieldId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);
        $field = $scholarship->fields()->findOrFail($fieldId);

        $field->update(['is_active' => !$field->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Field status updated successfully',
            'data' => $field->fresh()
        ]);
    }

    /**
     * Bulk create fields from template
     */
    public function bulkCreate(Request $request, $scholarshipId): JsonResponse
    {
        $scholarship = Scholarship::findOrFail($scholarshipId);

        $validator = Validator::make($request->all(), [
            'fields' => 'required|array|min:1',
            'fields.*.field_name' => 'required|string|max:100',
            'fields.*.field_label' => 'required|string|max:200',
            'fields.*.field_type' => 'required|string|in:text,textarea,email,tel,number,date,file,select',
            'fields.*.field_options' => 'nullable|array',
            'fields.*.is_required' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $maxOrder = $scholarship->fields()->max('field_order') ?? 0;
        $createdFields = [];

        foreach ($request->fields as $index => $fieldData) {
            $fieldData['scholarship_id'] = $scholarship->id;
            $fieldData['field_order'] = $maxOrder + $index + 1;
            $fieldData['is_active'] = true;
            
            $field = ScholarshipField::create($fieldData);
            $createdFields[] = $field;
        }

        return response()->json([
            'success' => true,
            'message' => count($createdFields) . ' fields created successfully',
            'data' => $createdFields
        ], 201);
    }
}
