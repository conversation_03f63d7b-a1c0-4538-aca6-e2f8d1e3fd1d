<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Program;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProgramPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any programs.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view programs list
    }

    /**
     * Determine whether the user can view the program.
     */
    public function view(User $user, Program $program): bool
    {
        // Public programs can be viewed by anyone
        if ($program->is_public) {
            return true;
        }

        // Admins can view all programs
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can view their programs
        if ($program->created_by === $user->id) {
            return true;
        }

        // Partner organizations can view programs they're involved in
        if ($user->role === 'partner_organization') {
            return $program->partnerOrganizations()
                ->where('partner_organization_id', $user->partner_organization_id)
                ->exists();
        }

        // Students can view programs they're enrolled in
        if (in_array($user->role, ['user', 'student'])) {
            return $program->participants()->where('user_id', $user->id)->exists();
        }

        // Volunteers can view programs they're assigned to
        if ($user->role === 'volunteer') {
            return $this->isVolunteerAssignedToProgram($user, $program);
        }

        return false;
    }

    /**
     * Determine whether the user can create programs.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization']);
    }

    /**
     * Determine whether the user can update the program.
     */
    public function update(User $user, Program $program): bool
    {
        // Admins can update any program
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can update their programs
        if ($program->created_by === $user->id) {
            return true;
        }

        // Partner organizations can update programs they lead
        if ($user->role === 'partner_organization') {
            $partnership = $program->partnerOrganizations()
                ->where('partner_organization_id', $user->partner_organization_id)
                ->first();
            
            return $partnership && $partnership->pivot->role === 'lead';
        }

        return false;
    }

    /**
     * Determine whether the user can delete the program.
     */
    public function delete(User $user, Program $program): bool
    {
        // Admins can delete any program
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can delete their programs if no participants
        if ($program->created_by === $user->id && $program->participants()->count() === 0) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can enroll in the program.
     */
    public function enroll(User $user, Program $program): bool
    {
        // Check if program is active and accepting enrollments
        if (!$program->is_active || ($program->enrollment_deadline && $program->enrollment_deadline < now())) {
            return false;
        }

        // Check if program is full
        if ($program->max_participants && $program->participants()->count() >= $program->max_participants) {
            return false;
        }

        // Check if user is already enrolled
        if ($program->participants()->where('user_id', $user->id)->exists()) {
            return false;
        }

        // Check eligibility criteria
        if (!$this->meetsEligibilityCriteria($user, $program)) {
            return false;
        }

        return in_array($user->role, ['user', 'student']) && $user->isActive();
    }

    /**
     * Determine whether the user can withdraw from the program.
     */
    public function withdraw(User $user, Program $program): bool
    {
        // Users can withdraw if they are enrolled and program allows withdrawal
        return $program->participants()->where('user_id', $user->id)->exists() &&
               $program->allows_withdrawal;
    }

    /**
     * Determine whether the user can view program participants.
     */
    public function viewParticipants(User $user, Program $program): bool
    {
        // Admins can view all participants
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can view participants
        if ($program->created_by === $user->id) {
            return true;
        }

        // Partner organizations involved in the program can view participants
        if ($user->role === 'partner_organization') {
            return $program->partnerOrganizations()
                ->where('partner_organization_id', $user->partner_organization_id)
                ->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can manage program participants.
     */
    public function manageParticipants(User $user, Program $program): bool
    {
        // Admins can manage all participants
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can manage participants
        if ($program->created_by === $user->id) {
            return true;
        }

        // Lead partner organizations can manage participants
        if ($user->role === 'partner_organization') {
            $partnership = $program->partnerOrganizations()
                ->where('partner_organization_id', $user->partner_organization_id)
                ->first();
            
            return $partnership && $partnership->pivot->role === 'lead';
        }

        return false;
    }

    /**
     * Determine whether the user can view program analytics.
     */
    public function viewAnalytics(User $user, Program $program): bool
    {
        // Admins can view all analytics
        if ($user->role === 'admin') {
            return true;
        }

        // Program creators can view analytics
        if ($program->created_by === $user->id) {
            return true;
        }

        // Partner organizations involved in the program can view analytics
        if ($user->role === 'partner_organization') {
            return $program->partnerOrganizations()
                ->where('partner_organization_id', $user->partner_organization_id)
                ->exists();
        }

        return false;
    }

    /**
     * Check if user meets program eligibility criteria
     */
    private function meetsEligibilityCriteria(User $user, Program $program): bool
    {
        // Check age requirements
        if ($program->min_age && $user->date_of_birth) {
            $age = now()->diffInYears($user->date_of_birth);
            if ($age < $program->min_age) {
                return false;
            }
        }

        if ($program->max_age && $user->date_of_birth) {
            $age = now()->diffInYears($user->date_of_birth);
            if ($age > $program->max_age) {
                return false;
            }
        }

        // Check location requirements
        if ($program->location_restrictions) {
            $allowedLocations = json_decode($program->location_restrictions, true);
            if (is_array($allowedLocations) && !in_array($user->state, $allowedLocations)) {
                return false;
            }
        }

        // Check role requirements
        if ($program->target_roles) {
            $targetRoles = json_decode($program->target_roles, true);
            if (is_array($targetRoles) && !in_array($user->role, $targetRoles)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if volunteer is assigned to the program
     */
    private function isVolunteerAssignedToProgram(User $user, Program $program): bool
    {
        // This would need to be implemented based on your volunteer assignment logic
        // For now, return false as a placeholder
        return false;
    }
}
