<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLTKKQ Foundation - Deployment & Setup Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #f97316;
            --primary-red: #ef4444;
            --dark-bg: #1e293b;
            --light-bg: #f8fafc;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-red) 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .code-block {
            background: #2d3748;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .code-block pre {
            color: #e2e8f0;
            margin: 0;
        }
        
        .deployment-step {
            background: #f8f9fa;
            border-left: 4px solid var(--primary-orange);
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        
        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .error-box {
            background: #fee2e2;
            border: 1px solid #ef4444;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .toc-sidebar {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .section-divider {
            height: 2px;
            background: linear-gradient(90deg, var(--primary-orange), var(--primary-red));
            margin: 3rem 0;
        }
        
        .server-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .server-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .checklist-item {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            margin: 0.25rem 0;
            background: white;
        }
        
        .checklist-item.completed {
            background: #f0f9ff;
            border-color: #3b82f6;
        }
    </style>
</head>
<body data-bs-spy="scroll" data-bs-target="#deploy-nav">
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="complete-documentation.html">
                <i class="fas fa-arrow-left me-2"></i>
                <i class="fas fa-heart text-warning me-2"></i>
                HLTKKQ Foundation
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#overview">Overview</a></li>
                    <li class="nav-item"><a class="nav-link" href="#requirements">Requirements</a></li>
                    <li class="nav-item"><a class="nav-link" href="#production">Production</a></li>
                    <li class="nav-item"><a class="nav-link" href="#monitoring">Monitoring</a></li>
                    <li class="nav-item"><a class="nav-link" href="#troubleshooting">Troubleshooting</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <i class="fas fa-rocket fa-4x text-white mb-4"></i>
                    <h1 class="display-3 fw-bold mb-4">Deployment & Setup Guide</h1>
                    <p class="lead mb-4">
                        Complete guide for deploying the HLTKKQ Foundation platform to production environments
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge bg-light text-dark fs-6">Production Ready</span>
                        <span class="badge bg-light text-dark fs-6">Scalable</span>
                        <span class="badge bg-light text-dark fs-6">Secure</span>
                        <span class="badge bg-light text-dark fs-6">Monitored</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="toc-sidebar p-3">
                    <h6 class="text-muted text-uppercase fw-bold mb-3">Deployment Guide</h6>
                    <nav id="deploy-nav" class="nav nav-pills flex-column">
                        <a class="nav-link" href="#overview">Overview</a>
                        <a class="nav-link" href="#requirements">Server Requirements</a>
                        <a class="nav-link" href="#environment">Environment Setup</a>
                        <a class="nav-link" href="#backend-deploy">Backend Deployment</a>
                        <a class="nav-link" href="#frontend-deploy">Frontend Deployment</a>
                        <a class="nav-link" href="#database">Database Setup</a>
                        <a class="nav-link" href="#ssl">SSL Configuration</a>
                        <a class="nav-link" href="#monitoring">Monitoring</a>
                        <a class="nav-link" href="#backup">Backup Strategy</a>
                        <a class="nav-link" href="#troubleshooting">Troubleshooting</a>
                    </nav>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                
                <!-- Overview -->
                <section id="overview" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-info-circle text-primary me-3"></i>
                        Deployment Overview
                    </h2>
                    
                    <div class="row g-4 mb-4">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="text-primary mb-3">Production Architecture</h4>
                                    <p class="lead">
                                        The HLTKKQ Foundation platform can be deployed using various strategies, from simple shared hosting to advanced cloud infrastructure with load balancing and auto-scaling.
                                    </p>
                                    <ul class="list-unstyled mt-3">
                                        <li><i class="fas fa-check text-success me-2"></i>Multi-environment support (dev, staging, production)</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Containerized deployment with Docker</li>
                                        <li><i class="fas fa-check text-success me-2"></i>CI/CD pipeline integration</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Database migrations and backups</li>
                                        <li><i class="fas fa-check text-success me-2"></i>SSL/TLS encryption</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Performance monitoring and logging</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h5>
                                </div>
                                <div class="card-body">
                                    <div class="warning-box">
                                        <p class="mb-2"><strong>Security First:</strong></p>
                                        <ul class="small mb-0">
                                            <li>Always use HTTPS in production</li>
                                            <li>Set strong database passwords</li>
                                            <li>Configure proper file permissions</li>
                                            <li>Enable firewall protection</li>
                                            <li>Regular security updates</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deployment Options -->
                    <h4 class="mb-4">Deployment Options</h4>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="card server-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-cloud fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">Cloud Hosting</h5>
                                    <p class="card-text text-muted">AWS, Google Cloud, DigitalOcean with auto-scaling and load balancing</p>
                                    <span class="badge bg-primary">Recommended</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card server-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-server fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">VPS Hosting</h5>
                                    <p class="card-text text-muted">Virtual private servers with full control and custom configuration</p>
                                    <span class="badge bg-success">Good</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card server-card h-100 text-center border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <i class="fas fa-home fa-3x text-warning mb-3"></i>
                                    <h5 class="card-title">Shared Hosting</h5>
                                    <p class="card-text text-muted">Budget-friendly option with some limitations on configuration</p>
                                    <span class="badge bg-warning">Basic</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Server Requirements -->
                <section id="requirements" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-clipboard-check text-success me-3"></i>
                        Server Requirements
                    </h2>

                    <div class="row g-4">
                        <div class="col-lg-6">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>Backend Requirements</h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-success">Minimum Specifications</h6>
                                    <ul class="list-unstyled mb-3">
                                        <li><i class="fas fa-microchip text-primary me-2"></i><strong>CPU:</strong> 2 cores (2.4 GHz+)</li>
                                        <li><i class="fas fa-memory text-info me-2"></i><strong>RAM:</strong> 4 GB minimum</li>
                                        <li><i class="fas fa-hdd text-warning me-2"></i><strong>Storage:</strong> 20 GB SSD</li>
                                        <li><i class="fas fa-network-wired text-secondary me-2"></i><strong>Bandwidth:</strong> 100 Mbps</li>
                                    </ul>
                                    
                                    <h6 class="text-success">Software Requirements</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fab fa-php text-purple me-2"></i>PHP 8.2+ with extensions</li>
                                        <li><i class="fas fa-database text-info me-2"></i>MySQL 8.0+ or MariaDB 10.6+</li>
                                        <li><i class="fas fa-server text-secondary me-2"></i>Apache 2.4+ or Nginx 1.18+</li>
                                        <li><i class="fas fa-shield-alt text-success me-2"></i>SSL/TLS certificate</li>
                                        <li><i class="fas fa-cog text-warning me-2"></i>Composer (latest)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-desktop me-2"></i>Frontend Requirements</h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-primary">Minimum Specifications</h6>
                                    <ul class="list-unstyled mb-3">
                                        <li><i class="fas fa-microchip text-primary me-2"></i><strong>CPU:</strong> 1 core (2.0 GHz+)</li>
                                        <li><i class="fas fa-memory text-info me-2"></i><strong>RAM:</strong> 2 GB minimum</li>
                                        <li><i class="fas fa-hdd text-warning me-2"></i><strong>Storage:</strong> 10 GB SSD</li>
                                        <li><i class="fas fa-network-wired text-secondary me-2"></i><strong>Bandwidth:</strong> 50 Mbps</li>
                                    </ul>
                                    
                                    <h6 class="text-primary">Software Requirements</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fab fa-node-js text-success me-2"></i>Node.js 18.17+ LTS</li>
                                        <li><i class="fas fa-cube text-warning me-2"></i>npm 9+ or yarn 1.22+</li>
                                        <li><i class="fas fa-server text-secondary me-2"></i>PM2 for process management</li>
                                        <li><i class="fas fa-shield-alt text-success me-2"></i>Reverse proxy (Nginx)</li>
                                        <li><i class="fas fa-code-branch text-info me-2"></i>Git for deployments</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PHP Extensions -->
                    <div class="mt-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fab fa-php me-2"></i>Required PHP Extensions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>BCMath
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>Ctype
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>Fileinfo
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>JSON
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>Mbstring
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>OpenSSL
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>PDO
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>Tokenizer
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>XML
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>Zip
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>GD (for image processing)
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="checklist-item">
                                            <i class="fas fa-check text-success me-2"></i>cURL
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Environment Setup -->
                <section id="environment" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-cogs text-warning me-3"></i>
                        Environment Setup
                    </h2>

                    <!-- Production Environment Variables -->
                    <div class="deployment-step">
                        <h4><i class="fas fa-step-forward text-orange me-2"></i>Step 1: Environment Configuration</h4>
                        <p>Configure your production environment variables for optimal security and performance.</p>
                        
                        <h6>Backend .env Configuration</h6>
                        <div class="code-block">
                            <pre><code># Application
APP_NAME="HLTKKQ Foundation"
APP_ENV=production
APP_KEY=base64:your-generated-app-key
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hltkkq_production
DB_USERNAME=your_db_user
DB_PASSWORD=strong_password_here

# Cache & Session
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="HLTKKQ Foundation"

# AWS S3 (for file storage)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-bucket-name

# Security
SANCTUM_STATEFUL_DOMAINS=your-frontend-domain.com
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com</code></pre>
                        </div>
                        
                        <h6>Frontend Environment Variables</h6>
                        <div class="code-block">
                            <pre><code># .env.production
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_STORAGE_URL=https://api.your-domain.com/storage
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id</code></pre>
                        </div>
                    </div>

                    <!-- SSL Configuration -->
                    <div class="deployment-step">
                        <h4><i class="fas fa-step-forward text-orange me-2"></i>Step 2: SSL Certificate Setup</h4>
                        <p>Secure your application with SSL/TLS certificates using Let's Encrypt or commercial certificates.</p>
                        
                        <div class="row g-4">
                            <div class="col-md-6">
                                <h6>Let's Encrypt (Free)</h6>
                                <div class="code-block">
                                    <pre><code># Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d your-domain.com -d api.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet</code></pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Commercial Certificate</h6>
                                <div class="code-block">
                                    <pre><code># Generate CSR
openssl req -new -newkey rsa:2048 -nodes \
  -keyout your-domain.key \
  -out your-domain.csr

# Install certificate in web server
# Configure SSL settings in Nginx/Apache</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Hardening -->
                    <div class="deployment-step">
                        <h4><i class="fas fa-step-forward text-orange me-2"></i>Step 3: Security Hardening</h4>
                        <p>Implement security best practices to protect your application.</p>
                        
                        <div class="warning-box">
                            <h6><i class="fas fa-shield-alt text-warning me-2"></i>Security Checklist</h6>
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Enable firewall (UFW/iptables)
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Disable root SSH access
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Use SSH key authentication
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Regular security updates
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Secure file permissions
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="checklist-item">
                                        <i class="fas fa-square-check text-success me-2"></i>Database security
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Backend Deployment -->
                <section id="backend-deploy" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-server text-danger me-3"></i>
                        Backend Deployment
                    </h2>

                    <div class="deployment-step">
                        <h4><i class="fas fa-upload text-orange me-2"></i>Laravel Backend Deployment</h4>
                        <p>Deploy the Laravel backend API to your production server.</p>
                        
                        <h6>1. Upload Code to Server</h6>
                        <div class="code-block">
                            <pre><code># Clone repository
git clone https://github.com/your-repo/laravel-api-ngo.git
cd laravel-api-ngo

# Or upload via FTP/SFTP to your web directory
# /var/www/html/api (for subdomain setup)
# /public_html/api (for shared hosting)</code></pre>
                        </div>
                        
                        <h6>2. Install Dependencies</h6>
                        <div class="code-block">
                            <pre><code># Install Composer dependencies
composer install --optimize-autoloader --no-dev

# Set file permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache</code></pre>
                        </div>
                        
                        <h6>3. Environment Configuration</h6>
                        <div class="code-block">
                            <pre><code># Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Edit .env with production settings
nano .env</code></pre>
                        </div>
                        
                        <h6>4. Database Setup</h6>
                        <div class="code-block">
                            <pre><code># Run migrations
php artisan migrate --force

# Seed database (optional)
php artisan db:seed --force

# Create storage link
php artisan storage:link</code></pre>
                        </div>
                        
                        <h6>5. Cache Optimization</h6>
                        <div class="code-block">
                            <pre><code># Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Cache events
php artisan event:cache</code></pre>
                        </div>
                    </div>

                    <!-- Web Server Configuration -->
                    <div class="card mt-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-server me-2"></i>Web Server Configuration</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <h6>Nginx Configuration</h6>
                                    <div class="code-block">
                                        <pre><code>server {
    listen 80;
    server_name api.your-domain.com;
    root /var/www/html/api/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}</code></pre>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Apache Configuration</h6>
                                    <div class="code-block">
                                        <pre><code>&lt;VirtualHost *:80&gt;
    ServerName api.your-domain.com
    DocumentRoot /var/www/html/api/public

    &lt;Directory /var/www/html/api/public&gt;
        AllowOverride All
        Require all granted
    &lt;/Directory&gt;

    ErrorLog ${APACHE_LOG_DIR}/api_error.log
    CustomLog ${APACHE_LOG_DIR}/api_access.log combined
&lt;/VirtualHost&gt;</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="section-divider"></div>

                <!-- Frontend Deployment -->
                <section id="frontend-deploy" class="py-5">
                    <h2 class="display-5 mb-4">
                        <i class="fas fa-desktop text-info me-3"></i>
                        Frontend Deployment
                    </h2>

                    <div class="deployment-step">
                        <h4><i class="fas fa-upload text-orange me-2"></i>Next.js Frontend Deployment</h4>
                        <p>Deploy the Next.js frontend application with optimized production build.</p>
                        
                        <h6>1. Prepare for Deployment</h6>
                        <div class="code-block">
                            <pre><code>cd front-end

# Install dependencies
npm install

# Create production environment file
cp .env.example .env.production

# Edit environment variables
nano .env.production</code></pre>
                        </div>
                        
                        <h6>2. Build for Production</h6>
                        <div class="code-block">
                            <pre><code># Build optimized production bundle
npm run build

# Test production build locally
npm run start</code></pre>
                        </div>
                        
                        <h6>3. Deploy with PM2</h6>
                        <div class="code-block">
                            <pre><code># Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'hltkkq-frontend',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/html/frontend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup</code></pre>
                        </div>
                        
                        <h6>4. Nginx Reverse Proxy</h6>
                        <div class="code-block">
                            <pre><code>server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Continue with monitoring, backup, and troubleshooting sections... -->

            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html> 