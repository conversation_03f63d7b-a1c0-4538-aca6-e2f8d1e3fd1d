"use client"

import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { notFound } from "next/navigation";
import { useEffect, useState, use } from "react";
import { Calendar, User, Clock, ArrowLeft, Share2, Heart, MessageCircle, Loader2, Eye } from "lucide-react";

// Type definitions
interface BlogPost {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image_url?: string;
  reading_time?: string;
  views: number;
  author: {
    id: number;
    name: string;
    avatar?: string;
    bio?: string;
  };
  category?: {
    id: number;
    name: string;
    slug: string;
    color?: string;
  };
  tags: string[];
  published_at: string;
  comments_count?: number;
}

interface BlogComment {
  id: number;
  content: string;
  user: {
    id: number;
    name: string;
    avatar?: string;
  };
  created_at: string;
  replies?: BlogComment[];
}

interface BlogPostResponse {
  post: BlogPost;
  comments?: BlogComment[];
  success: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "";

export default function BlogPostPage({ params }: { params: Promise<{ slug: string }> }) {
  // Use React.use() to unwrap the params Promise
  const resolvedParams = use(params);
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch blog post
  const fetchPost = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`${API_BASE_URL}/blog/posts/${resolvedParams.slug}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const text = await response.text();
      let data: BlogPostResponse;
      
      try {
        data = JSON.parse(text);
      } catch (parseError) {
        console.error('Response was not valid JSON:', text.substring(0, 200));
        throw new Error('Server returned invalid response');
      }

      if (data.success) {
        setPost(data.post);
      } else {
        setError('Post not found');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      setError('Failed to load post. Please check if the server is running.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPost();
  }, [resolvedParams.slug]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get reading time or default
  const getReadingTime = (post: BlogPost) => {
    return post.reading_time || '5 min read';
  };

  // Get fallback image
  const getPostImage = (post: BlogPost) => {
    if (post.featured_image_url) {
      return post.featured_image_url;
    }
    
    // Return unique placeholder based on post ID
    const placeholders = [
      'https://images.unsplash.com/photo-1529070538774-1843cb3265df', // Impact/community
      'https://images.unsplash.com/photo-1593113598332-cd288d649433', // Education
      'https://images.unsplash.com/photo-**********-cd4628902d4a', // Healthcare
      'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40', // Environment
      'https://images.unsplash.com/photo-1517048676732-d65bc937f952', // Community work
      'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c', // Volunteering
      'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca', // Charity
      'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b', // Nature/environment
      'https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a', // Help/support
      'https://images.unsplash.com/photo-1484712401471-05c7215830eb' // Community building
    ];

    // Use post ID to get consistent but different images
    const index = post.id % placeholders.length;
    return placeholders[index] + '?w=1200&h=600&fit=crop&crop=center';
  };

  if (loading) {
    return (
      <main className="flex-1 py-12 md:py-20 px-4 md:px-6">
        <div className="container mx-auto max-w-4xl">
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-green-600 mr-2" />
            <span className="text-lg text-gray-600">Loading article...</span>
          </div>
        </div>
      </main>
    );
  }

  if (error && !post) {
    return (
      <main className="flex-1 py-12 md:py-20 px-4 md:px-6">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center bg-red-50 border border-red-200 rounded-lg p-8">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="h-12 w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-red-900 mb-2">Unable to Load Article</h3>
            <p className="text-red-700 mb-6">
              {error}
            </p>
            <div className="flex gap-4 justify-center">
              <Button 
                onClick={() => {
                  setError(null)
                  fetchPost()
                }}
                className="bg-red-600 hover:bg-red-700 text-white rounded-full px-6 py-2"
              >
                Try Again
              </Button>
              <Link href="/blog">
                <Button 
                  variant="outline"
                  className="rounded-full px-6 py-2"
                >
                  Back to Blog
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (!post) {
    notFound();
  }

  return (
    <>
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container relative px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          <div className="mb-6">
            <Link href="/blog">
              <Button variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white rounded-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Button>
            </Link>
          </div>
          
          <div className="text-center">
            {post.category && (
              <Badge className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-4 py-2 rounded-full text-sm mb-4">
                {post.category.name}
              </Badge>
            )}
            
            <h1 className="text-3xl md:text-5xl font-bold mb-6 leading-tight">
              {post.title}
            </h1>
            
            <div className="flex flex-wrap items-center justify-center gap-6 text-green-100">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  {post.author.avatar ? (
                    <Image
                      src={post.author.avatar}
                      alt={post.author.name}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  ) : (
                    <User className="h-5 w-5 text-green-600" />
                  )}
                </div>
                <span className="font-medium">{post.author.name}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(post.published_at)}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{getReadingTime(post)}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>{post.views} views</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      {post.featured_image_url && (
        <section className="py-8 bg-white dark:bg-gray-800">
          <div className="container px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
            <div className="relative aspect-video rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={getPostImage(post)}
                alt={post.title}
                fill
                className="object-cover"
                sizes="(max-width: 1024px) 100vw, 1024px"
              />
            </div>
          </div>
        </section>
      )}

      {/* Article Content */}
      <section className="py-12 bg-white dark:bg-gray-800">
        <div className="container px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <div className="sticky top-8 space-y-6">
                {/* Share Buttons */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Share this article</h3>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Heart className="h-4 w-4 mr-2" />
                        Like
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Author Bio */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-3">About the Author</h3>
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        {post.author.avatar ? (
                          <Image
                            src={post.author.avatar}
                            alt={post.author.name}
                            width={48}
                            height={48}
                            className="rounded-full"
                          />
                        ) : (
                          <User className="h-6 w-6 text-green-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{post.author.name}</p>
                      </div>
                    </div>
                    {post.author.bio && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">{post.author.bio}</p>
                    )}
                  </CardContent>
                </Card>

                {/* Tags */}
                {post.tags && post.tags.length > 0 && (
                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {post.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 order-1 lg:order-2">
              <article className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
                {/* Article Body */}
                <div 
                  className="prose prose-lg dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed"
                  dangerouslySetInnerHTML={{ 
                    __html: post.content.replace(/\n/g, '<br>').replace(/\n\n/g, '</p><p>') 
                  }}
                />

                {/* Article Footer */}
                <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex flex-wrap items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Published on {formatDate(post.published_at)}
                      </span>
                      {post.comments_count !== undefined && (
                        <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                          <MessageCircle className="h-4 w-4" />
                          <span>{post.comments_count} comments</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Heart className="h-4 w-4 mr-2" />
                        Like
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              </article>

              {/* Related Articles */}
              <div className="mt-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Related Articles</h2>
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <p>Related articles will be loaded here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}