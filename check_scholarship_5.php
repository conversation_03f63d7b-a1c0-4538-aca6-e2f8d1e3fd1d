<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Checking scholarship ID 5:" . PHP_EOL;

$scholarship = App\Models\Scholarship::find(5);

if ($scholarship) {
    echo "✅ Found scholarship ID 5:" . PHP_EOL;
    echo "Title: " . $scholarship->title . PHP_EOL;
    echo "Category: " . $scholarship->category . PHP_EOL;
    echo "Status: " . $scholarship->status . PHP_EOL;
    echo "Amount: ₦" . number_format($scholarship->amount) . PHP_EOL;
    echo "Deadline: " . $scholarship->application_deadline . PHP_EOL;
    echo "Description: " . substr($scholarship->description, 0, 100) . "..." . PHP_EOL;
    
    // Check if it has custom fields
    $customFields = $scholarship->fields;
    echo "Custom Fields: " . $customFields->count() . PHP_EOL;
    
    if ($customFields->count() > 0) {
        echo "Custom Fields List:" . PHP_EOL;
        foreach ($customFields as $field) {
            echo "  - " . $field->field_label . " (" . $field->field_type . ")" . PHP_EOL;
        }
    }
} else {
    echo "❌ Scholarship with ID 5 not found" . PHP_EOL;
    echo "Available scholarships:" . PHP_EOL;
    
    $scholarships = App\Models\Scholarship::all(['id', 'title', 'category']);
    foreach ($scholarships as $s) {
        echo "ID: " . $s->id . " - " . $s->title . " (" . $s->category . ")" . PHP_EOL;
    }
}

echo PHP_EOL . "Testing API endpoint for scholarship 5:" . PHP_EOL;

// Test the API endpoint
$url = "http://127.0.0.1:8000/api/v1/public-scholarships/5";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Accept: application/json'
    ]
]);

$response = @file_get_contents($url, false, $context);

if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ API endpoint working for scholarship 5" . PHP_EOL;
        echo "API Title: " . $data['data']['title'] . PHP_EOL;
        echo "API Category: " . $data['data']['category'] . PHP_EOL;
    } else {
        echo "❌ API returned error: " . ($data['message'] ?? 'Unknown error') . PHP_EOL;
    }
} else {
    echo "❌ Could not reach API endpoint" . PHP_EOL;
}
