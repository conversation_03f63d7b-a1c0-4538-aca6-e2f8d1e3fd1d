<?php

namespace Database\Factories;

use App\Models\PartnerOrganization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PartnerOrganization>
 */
class PartnerOrganizationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PartnerOrganization::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' School',
            'type' => $this->faker->randomElement(['school', 'university', 'ngo', 'government']),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
            'website' => $this->faker->optional()->url(),
            'description' => $this->faker->optional()->paragraph(),
            'contact_person' => $this->faker->name(),
            'contact_person_title' => $this->faker->jobTitle(),
            'contact_person_email' => $this->faker->safeEmail(),
            'contact_person_phone' => $this->faker->phoneNumber(),
            'established_year' => $this->faker->optional()->year(),
            'student_capacity' => $this->faker->optional()->numberBetween(100, 5000),
            'current_students' => $this->faker->optional()->numberBetween(50, 3000),
            'partnership_start_date' => $this->faker->optional()->date(),
            'partnership_status' => $this->faker->randomElement(['active', 'pending', 'inactive']),
            'logo' => $this->faker->optional()->imageUrl(200, 200, 'business'),
            'documents' => $this->faker->optional()->randomElement([
                json_encode(['registration_certificate.pdf', 'tax_certificate.pdf']),
                json_encode(['license.pdf']),
                null
            ]),
            'notes' => $this->faker->optional()->paragraph(),
            'is_verified' => $this->faker->boolean(80),
            'verification_date' => $this->faker->optional()->dateTime(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the partner organization is a school.
     */
    public function school(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'school',
            'name' => $this->faker->company() . ' Primary School',
        ]);
    }

    /**
     * Indicate that the partner organization is a university.
     */
    public function university(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'university',
            'name' => $this->faker->company() . ' University',
        ]);
    }

    /**
     * Indicate that the partner organization is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
            'verification_date' => now(),
            'partnership_status' => 'active',
        ]);
    }

    /**
     * Indicate that the partner organization is pending verification.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
            'verification_date' => null,
            'partnership_status' => 'pending',
        ]);
    }
}
