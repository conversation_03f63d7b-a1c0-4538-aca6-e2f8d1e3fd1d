@extends('layouts.admin')

@section('title', 'Edit Donation')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Donation</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.donations.index') }}">Donations</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.donations.show', $donation->id) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="{{ route('admin.donations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Donation #{{ $donation->id }}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.donations.update', $donation->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_status" class="font-weight-bold">Payment Status <span class="text-danger">*</span></label>
                                    <select class="form-control @error('payment_status') is-invalid @enderror" id="payment_status" name="payment_status" required>
                                        <option value="">Select Status</option>
                                        <option value="pending" {{ old('payment_status', $donation->payment_status) === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="completed" {{ old('payment_status', $donation->payment_status) === 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="failed" {{ old('payment_status', $donation->payment_status) === 'failed' ? 'selected' : '' }}>Failed</option>
                                        <option value="refunded" {{ old('payment_status', $donation->payment_status) === 'refunded' ? 'selected' : '' }}>Refunded</option>
                                    </select>
                                    @error('payment_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Amount (Read Only)</label>
                                    <input type="text" class="form-control" value="₦{{ number_format($donation->amount, 2) }}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes" class="font-weight-bold">Admin Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="4" placeholder="Add any administrative notes about this donation...">{{ old('notes', $donation->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">These notes are for internal use only and will not be visible to the donor.</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Donation
                            </button>
                            <a href="{{ route('admin.donations.show', $donation->id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Donation Information (Read Only) -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Donation Information</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Donation ID:</label>
                        <p class="text-muted">#{{ $donation->id }}</p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Donor:</label>
                        <p class="text-muted">
                            @if($donation->donor)
                                {{ $donation->donor->first_name }} {{ $donation->donor->last_name }}
                            @else
                                {{ $donation->is_anonymous ? 'Anonymous' : 'N/A' }}
                            @endif
                        </p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Type:</label>
                        <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $donation->donation_type)) }}</p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Payment Method:</label>
                        <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $donation->payment_method)) }}</p>
                    </div>
                    @if($donation->purpose)
                    <div class="form-group">
                        <label class="font-weight-bold">Purpose:</label>
                        <p class="text-muted">{{ $donation->purpose }}</p>
                    </div>
                    @endif
                    <div class="form-group">
                        <label class="font-weight-bold">Created:</label>
                        <p class="text-muted">{{ $donation->created_at->format('M d, Y h:i A') }}</p>
                    </div>
                </div>
            </div>

            @if($donation->campaign)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="font-weight-bold">Campaign:</label>
                        <p class="text-muted">{{ $donation->campaign->title }}</p>
                    </div>
                    <div class="form-group">
                        <label class="font-weight-bold">Goal:</label>
                        <p class="text-muted">₦{{ number_format($donation->campaign->goal_amount, 2) }}</p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
