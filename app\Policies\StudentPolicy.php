<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Student;
use App\Models\PartnerOrganization;
use Illuminate\Auth\Access\HandlesAuthorization;

class StudentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any students.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'partner_organization', 'user']);
    }

    /**
     * Determine whether the user can view the student.
     */
    public function view(User $user, Student $student): bool
    {
        // Admins can view all students
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can view their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can only view their own student profile
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create students.
     */
    public function create(User $user): bool
    {
        // Admins can create students for any organization
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create students for their organization
        if ($user->role === 'partner_organization') {
            return true;
        }

        // Individual users can create their own student profile
        if ($user->role === 'user') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update the student.
     */
    public function update(User $user, Student $student): bool
    {
        // Admins can update all students
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can update their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can update their own student profile
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the student.
     */
    public function delete(User $user, Student $student): bool
    {
        // Only admins can delete students
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can delete their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        return false;
    }

    /**
     * Determine whether the user can view student academic history.
     */
    public function viewAcademicHistory(User $user, Student $student): bool
    {
        return $this->view($user, $student);
    }

    /**
     * Determine whether the user can view student application history.
     */
    public function viewApplicationHistory(User $user, Student $student): bool
    {
        return $this->view($user, $student);
    }

    /**
     * Determine whether the user can manage student progressions.
     */
    public function manageProgressions(User $user, Student $student): bool
    {
        // Admins can manage progressions for all students
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can manage progressions for their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users cannot manage their own progressions
        return false;
    }

    /**
     * Determine whether the user can create applications for the student.
     */
    public function createApplications(User $user, Student $student): bool
    {
        // Admins can create applications for any student
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can create applications for their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can create applications for themselves
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can view student statistics.
     */
    public function viewStatistics(User $user, Student $student): bool
    {
        return $this->view($user, $student);
    }

    /**
     * Determine whether the user can export student data.
     */
    public function exportData(User $user, Student $student): bool
    {
        // Admins can export any student's data
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can export their students' data
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can export their own data
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can enroll the student in programs.
     */
    public function enrollInPrograms(User $user, Student $student): bool
    {
        // Admins can enroll any student
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can enroll their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual users can enroll themselves
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can view student eligibility for scholarships.
     */
    public function viewScholarshipEligibility(User $user, Student $student): bool
    {
        return $this->view($user, $student);
    }

    /**
     * Determine whether the user can manage student documents.
     */
    public function manageDocuments(User $user, Student $student): bool
    {
        return $this->update($user, $student);
    }

    /**
     * Determine whether the user can view student performance metrics.
     */
    public function viewPerformanceMetrics(User $user, Student $student): bool
    {
        return $this->view($user, $student);
    }

    /**
     * Determine whether the user can assign the student to a partner organization.
     */
    public function assignToOrganization(User $user, Student $student): bool
    {
        // Only admins can assign students to organizations
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can transfer the student between organizations.
     */
    public function transferStudent(User $user, Student $student): bool
    {
        // Only admins can transfer students
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can archive/deactivate the student.
     */
    public function archive(User $user, Student $student): bool
    {
        // Admins can archive any student
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can archive their students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        return false;
    }
}
