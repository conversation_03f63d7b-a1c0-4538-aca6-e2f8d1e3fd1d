"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/donation-page-wrapper.tsx":
/*!**********************************************!*\
  !*** ./components/donation-page-wrapper.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DonationPageWrapper: () => (/* binding */ DonationPageWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _donation_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./donation-form */ \"(app-pages-browser)/./components/donation-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ DonationPageWrapper auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DonationPageWrapper() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [alertMessage, setAlertMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DonationPageWrapper.useEffect\": ()=>{\n            const error = searchParams.get('error');\n            const success = searchParams.get('success');\n            if (error === 'payment_failed') {\n                setAlertMessage({\n                    type: 'error',\n                    message: 'Payment was not completed successfully. Please try again.'\n                });\n            } else if (error === 'payment_cancelled') {\n                setAlertMessage({\n                    type: 'error',\n                    message: 'Payment was cancelled. You can try again when ready.'\n                });\n            } else if (error === 'invalid_amount') {\n                setAlertMessage({\n                    type: 'error',\n                    message: 'Invalid donation amount. Please ensure the amount meets our minimum requirement.'\n                });\n            } else if (error) {\n                setAlertMessage({\n                    type: 'error',\n                    message: 'An error occurred during payment processing. Please try again or contact support.'\n                });\n            } else if (success) {\n                setAlertMessage({\n                    type: 'success',\n                    message: 'Thank you for your donation! Your payment has been processed successfully.'\n                });\n            }\n            // Clear URL parameters after showing message\n            if (error || success) {\n                const timer = setTimeout({\n                    \"DonationPageWrapper.useEffect.timer\": ()=>{\n                        window.history.replaceState({}, '', '/donate');\n                    }\n                }[\"DonationPageWrapper.useEffect.timer\"], 100);\n                return ({\n                    \"DonationPageWrapper.useEffect\": ()=>clearTimeout(timer)\n                })[\"DonationPageWrapper.useEffect\"];\n            }\n        }\n    }[\"DonationPageWrapper.useEffect\"], [\n        searchParams\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            alertMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                    className: \"max-w-2xl mx-auto \".concat(alertMessage.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'),\n                    children: [\n                        alertMessage.type === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                            className: alertMessage.type === 'error' ? 'text-red-600' : 'text-green-600',\n                            children: alertMessage.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_donation_form__WEBPACK_IMPORTED_MODULE_4__.DonationForm, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-page-wrapper.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(DonationPageWrapper, \"mxzIl3BpuZhULGKKvd00YW7rwzk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DonationPageWrapper;\nvar _c;\n$RefreshReg$(_c, \"DonationPageWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/donation-page-wrapper.tsx\n"));

/***/ })

});