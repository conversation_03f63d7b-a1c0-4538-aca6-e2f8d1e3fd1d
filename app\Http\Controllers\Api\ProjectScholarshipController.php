<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProjectScholarshipController extends Controller
{
    /**
     * Get scholarship statistics for the Projects page
     */
    public function getStatistics(): JsonResponse
    {
        try {
            // Get approved applications count by category
            $approvedByCategory = ScholarshipApplication::select('scholarships.category')
                ->selectRaw('COUNT(*) as count')
                ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                ->where('scholarship_applications.status', 'approved')
                ->groupBy('scholarships.category')
                ->pluck('count', 'category')
                ->toArray();

            // Get total statistics
            $totalApproved = array_sum($approvedByCategory);
            $totalScholarships = Scholarship::count();
            $activeScholarships = Scholarship::where('status', 'open')
                ->where('application_deadline', '>', now())
                ->count();

            // Calculate total scholarship amount awarded
            $totalAmountAwarded = ScholarshipApplication::where('status', 'approved')
                ->whereNotNull('award_amount')
                ->sum('award_amount');

            // If award_amount is null, use scholarship amount
            if ($totalAmountAwarded == 0) {
                $totalAmountAwarded = ScholarshipApplication::select('scholarships.amount')
                    ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->where('scholarship_applications.status', 'approved')
                    ->sum('scholarships.amount');
            }

            return response()->json([
                'success' => true,
                'message' => 'Statistics retrieved successfully',
                'data' => [
                    'approved_by_category' => [
                        'primary' => $approvedByCategory['primary'] ?? 0,
                        'secondary' => $approvedByCategory['secondary'] ?? 0,
                        'university' => $approvedByCategory['university'] ?? 0,
                    ],
                    'totals' => [
                        'total_approved' => $totalApproved,
                        'total_scholarships' => $totalScholarships,
                        'active_scholarships' => $activeScholarships,
                        'total_amount_awarded' => $totalAmountAwarded,
                    ],
                    'impact_metrics' => [
                        'primary_students_helped' => $approvedByCategory['primary'] ?? 0,
                        'secondary_students_helped' => $approvedByCategory['secondary'] ?? 0,
                        'university_students_helped' => $approvedByCategory['university'] ?? 0,
                        'total_students_helped' => $totalApproved,
                    ]
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship portfolio data grouped by category
     */
    public function getPortfolio(): JsonResponse
    {
        try {
            $categories = ['primary', 'secondary', 'university'];
            $portfolio = [];

            foreach ($categories as $category) {
                // Get scholarships in this category
                $scholarships = Scholarship::where('category', $category)->get();
                
                // Count approved applications for this category
                $approvedCount = ScholarshipApplication::join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->where('scholarships.category', $category)
                    ->where('scholarship_applications.status', 'approved')
                    ->count();

                // Get active scholarships count
                $activeCount = Scholarship::where('category', $category)
                    ->where('status', 'open')
                    ->where('application_deadline', '>', now())
                    ->count();

                // Calculate total amount for this category
                $totalAmount = ScholarshipApplication::join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
                    ->where('scholarships.category', $category)
                    ->where('scholarship_applications.status', 'approved')
                    ->sum('scholarships.amount');

                $portfolio[] = [
                    'category' => $category,
                    'title' => ucfirst($category) . ' Students Scholarship 2025',
                    'approved_applications' => $approvedCount,
                    'active_scholarships' => $activeCount,
                    'total_amount' => $totalAmount,
                    'description' => $this->getCategoryDescription($category, $approvedCount, $activeCount),
                    'scholarships_available' => $scholarships->count(),
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Portfolio data retrieved successfully',
                'data' => $portfolio
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve portfolio data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarships by status for tabs
     */
    public function getScholarshipsByStatus(Request $request): JsonResponse
    {
        try {
            $status = $request->get('status', 'active');
            $query = Scholarship::with(['applications' => function($q) {
                $q->where('status', 'approved');
            }]);

            switch ($status) {
                case 'active':
                    $query->where('status', 'open')
                          ->where('application_deadline', '>', now());
                    break;
                    
                case 'completed':
                    $query->where(function($q) {
                        $q->where('status', 'closed')
                          ->orWhere('application_deadline', '<=', now());
                    });
                    break;
                    
                case 'planned':
                    $query->where('status', 'planned')
                          ->orWhere('application_start_date', '>', now());
                    break;
                    
                default:
                    $query->where('status', 'open');
            }

            $scholarships = $query->orderBy('application_deadline', $status === 'completed' ? 'desc' : 'asc')
                                 ->get()
                                 ->map(function ($scholarship) {
                                     return [
                                         'id' => $scholarship->id,
                                         'title' => $scholarship->title,
                                         'category' => $scholarship->category,
                                         'amount' => $scholarship->amount,
                                         'status' => $scholarship->status,
                                         'application_deadline' => $scholarship->application_deadline,
                                         'application_start_date' => $scholarship->application_start_date,
                                         'approved_applications' => $scholarship->applications->count(),
                                         'max_applicants' => $scholarship->max_applicants,
                                         'description' => $scholarship->description,
                                         'is_featured' => $scholarship->is_featured,
                                         'days_remaining' => $scholarship->application_deadline ? 
                                             Carbon::parse($scholarship->application_deadline)->diffInDays(now(), false) : null,
                                     ];
                                 });

            return response()->json([
                'success' => true,
                'message' => 'Scholarships retrieved successfully',
                'data' => $scholarships
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get category-specific description
     */
    private function getCategoryDescription(string $category, int $approvedCount, int $activeCount): string
    {
        $descriptions = [
            'primary' => "Supporting primary school students (grades 1-6) with educational scholarships. Currently {$approvedCount} students have been approved for scholarships, with {$activeCount} active scholarship programs available.",
            'secondary' => "Empowering secondary school students (JSS1-SS3) to achieve academic excellence. We have approved {$approvedCount} scholarship applications and have {$activeCount} active programs running.",
            'university' => "Providing higher education opportunities for university students. {$approvedCount} students have received approved scholarships, with {$activeCount} university scholarship programs currently active."
        ];

        return $descriptions[$category] ?? "Scholarship program for {$category} students.";
    }
}
