<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Web\AdminController;
use App\Models\User;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use Illuminate\Http\Request;

class TestApiEndpoints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:api-endpoints';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all AJAX API endpoints for admin blog functionality';

    private $controller;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Admin API Controller Methods...');
        $this->newLine();

        // Setup
        $this->controller = new AdminController();
        
        $errors = [];

        // Test Blog Dashboard API
        $this->info('🔍 Testing Blog Dashboard Controller...');
        $errors = array_merge($errors, $this->testBlogDashboardController());

        // Test Blog Posts APIs
        $this->info('🔍 Testing Blog Posts Controllers...');
        $errors = array_merge($errors, $this->testBlogPostsControllers());

        // Test Blog Categories APIs
        $this->info('🔍 Testing Blog Categories Controllers...');
        $errors = array_merge($errors, $this->testBlogCategoriesControllers());

        // Test Blog Comments APIs
        $this->info('🔍 Testing Blog Comments Controllers...');
        $errors = array_merge($errors, $this->testBlogCommentsControllers());

        // Summary
        $this->newLine();
        if (empty($errors)) {
            $this->info('✅ All API controller methods are working correctly!');
        } else {
            $this->error('❌ Found issues with API controller methods:');
            foreach ($errors as $error) {
                $this->line('  - ' . $error);
            }
        }

        return empty($errors) ? 0 : 1;
    }

    private function testBlogDashboardController()
    {
        $errors = [];

        try {
            $response = $this->controller->getBlogDashboard();
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getContent(), true);
                if (isset($data['data']['stats']) && isset($data['data']['recent_posts'])) {
                    $this->line('  ✅ Blog Dashboard Controller: Success');
                } else {
                    $errors[] = 'Blog Dashboard Controller: Missing required data structure';
                }
            } else {
                $errors[] = 'Blog Dashboard Controller: HTTP ' . $response->getStatusCode();
            }
        } catch (\Exception $e) {
            $errors[] = 'Blog Dashboard Controller: ' . $e->getMessage();
        }

        return $errors;
    }

    private function testBlogPostsControllers()
    {
        $errors = [];

        try {
            // Test get posts
            $request = new Request();
            $response = $this->controller->getBlogPosts($request);
            if ($response->getStatusCode() === 200) {
                $this->line('  ✅ Get Blog Posts Controller: Success');
            } else {
                $errors[] = 'Get Blog Posts Controller: HTTP ' . $response->getStatusCode();
            }

            // Create test post for other operations
            $testPost = $this->createTestPost();
            if ($testPost) {
                // Test delete post
                $response = $this->controller->deleteBlogPost($testPost->id);
                if ($response->getStatusCode() === 200) {
                    $this->line('  ✅ Delete Blog Post Controller: Success');
                } else {
                    $errors[] = 'Delete Blog Post Controller: HTTP ' . $response->getStatusCode();
                }

                // Create another test post for toggle status
                $testPost2 = $this->createTestPost();
                if ($testPost2) {
                    $request = new Request(['status' => 'published']);
                    $response = $this->controller->toggleBlogPostStatus($request, $testPost2->id);
                    if ($response->getStatusCode() === 200) {
                        $this->line('  ✅ Toggle Post Status Controller: Success');
                    } else {
                        $errors[] = 'Toggle Post Status Controller: HTTP ' . $response->getStatusCode();
                    }
                    
                    $testPost2->delete();
                }
            }
        } catch (\Exception $e) {
            $errors[] = 'Blog Posts Controllers: ' . $e->getMessage();
        }

        return $errors;
    }

    private function testBlogCategoriesControllers()
    {
        $errors = [];

        try {
            // Test get categories
            $response = $this->controller->getBlogCategories();
            if ($response->getStatusCode() === 200) {
                $this->line('  ✅ Get Blog Categories Controller: Success');
            } else {
                $errors[] = 'Get Blog Categories Controller: HTTP ' . $response->getStatusCode();
            }

            // Test create category
            $request = new Request([
                'name' => 'Test API Category ' . time(),
                'description' => 'Test description',
                'color' => '#ff5722',
                'is_active' => true,
                'sort_order' => 1
            ]);
            
            $response = $this->controller->storeBlogCategory($request);
            if ($response->getStatusCode() === 201) {
                $this->line('  ✅ Create Blog Category Controller: Success');
                
                $data = json_decode($response->getContent(), true);
                $categoryId = $data['data']['id'];
                
                // Test update category
                $updateRequest = new Request([
                    'name' => 'Updated API Category',
                    'description' => 'Updated description',
                    'color' => '#2196f3',
                    'is_active' => false,
                    'sort_order' => 2
                ]);
                
                $response = $this->controller->updateBlogCategory($updateRequest, $categoryId);
                if ($response->getStatusCode() === 200) {
                    $this->line('  ✅ Update Blog Category Controller: Success');
                } else {
                    $errors[] = 'Update Blog Category Controller: HTTP ' . $response->getStatusCode();
                }
                
                // Test delete category
                $response = $this->controller->deleteBlogCategory($categoryId);
                if ($response->getStatusCode() === 200) {
                    $this->line('  ✅ Delete Blog Category Controller: Success');
                } else {
                    $errors[] = 'Delete Blog Category Controller: HTTP ' . $response->getStatusCode();
                }
            } else {
                $errors[] = 'Create Blog Category Controller: HTTP ' . $response->getStatusCode();
            }
        } catch (\Exception $e) {
            $errors[] = 'Blog Categories Controllers: ' . $e->getMessage();
        }

        return $errors;
    }

    private function testBlogCommentsControllers()
    {
        $errors = [];

        try {
            // Test get comments
            $request = new Request();
            $response = $this->controller->getBlogComments($request);
            if ($response->getStatusCode() === 200) {
                $this->line('  ✅ Get Blog Comments Controller: Success');
            } else {
                $errors[] = 'Get Blog Comments Controller: HTTP ' . $response->getStatusCode();
            }

            // Create test comment
            $testComment = $this->createTestComment();
            if ($testComment) {
                // Test update comment status
                $request = new Request(['status' => 'approved']);
                $response = $this->controller->updateBlogCommentStatus($request, $testComment->id);
                if ($response->getStatusCode() === 200) {
                    $this->line('  ✅ Update Comment Status Controller: Success');
                } else {
                    $errors[] = 'Update Comment Status Controller: HTTP ' . $response->getStatusCode();
                }

                // Test delete comment
                $response = $this->controller->deleteBlogComment($testComment->id);
                if ($response->getStatusCode() === 200) {
                    $this->line('  ✅ Delete Blog Comment Controller: Success');
                } else {
                    $errors[] = 'Delete Blog Comment Controller: HTTP ' . $response->getStatusCode();
                }
            }
        } catch (\Exception $e) {
            $errors[] = 'Blog Comments Controllers: ' . $e->getMessage();
        }

        return $errors;
    }

    private function createTestPost()
    {
        try {
            $user = User::where('role', 'admin')->first();
            if (!$user) {
                $user = User::factory()->create(['role' => 'admin']);
            }
            
            $category = BlogCategory::first() ?: BlogCategory::create([
                'name' => 'Test Category',
                'slug' => 'test-category',
                'is_active' => true
            ]);

            return BlogPost::create([
                'title' => 'Test API Post ' . time(),
                'slug' => 'test-api-post-' . time(),
                'content' => 'Test content for API testing',
                'author_id' => $user->id,
                'category_id' => $category->id,
                'status' => 'draft'
            ]);
        } catch (\Exception $e) {
            $this->error('Failed to create test post: ' . $e->getMessage());
            return null;
        }
    }

    private function createTestComment()
    {
        try {
            $user = User::first();
            if (!$user) {
                $user = User::factory()->create();
            }
            
            $post = $this->createTestPost();
            if (!$post) return null;

            return BlogComment::create([
                'post_id' => $post->id,
                'user_id' => $user->id,
                'content' => 'Test API comment ' . time(),
                'status' => 'pending'
            ]);
        } catch (\Exception $e) {
            $this->error('Failed to create test comment: ' . $e->getMessage());
            return null;
        }
    }
}
