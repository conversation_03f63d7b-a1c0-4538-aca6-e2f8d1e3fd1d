"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/page",{

/***/ "(app-pages-browser)/./app/projects/page.tsx":
/*!*******************************!*\
  !*** ./app/projects/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,CheckCircle,DollarSign,Heart,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API functions\nconst API_BASE_URL = \"https://halimakqfoundation.fnskills.ng/api/v1\" || 0;\nconst fetchStatistics = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/statistics\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchPortfolio = async ()=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/portfolio\"));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nconst fetchScholarshipsByStatus = async (status)=>{\n    const response = await fetch(\"\".concat(API_BASE_URL, \"/project-scholarships/by-status?status=\").concat(status));\n    const result = await response.json();\n    if (!result.success) throw new Error(result.message);\n    return result.data;\n};\nfunction ProjectsPage() {\n    _s();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [portfolio, setPortfolio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [categoryScholarships, setCategoryScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCategoryResults, setShowCategoryResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            const loadData = {\n                \"ProjectsPage.useEffect.loadData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [statsData, portfolioData, activeData, completedData, plannedData] = await Promise.all([\n                            fetchStatistics(),\n                            fetchPortfolio(),\n                            fetchScholarshipsByStatus('active'),\n                            fetchScholarshipsByStatus('completed'),\n                            fetchScholarshipsByStatus('planned')\n                        ]);\n                        setStatistics(statsData);\n                        setPortfolio(portfolioData);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load data');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ProjectsPage.useEffect\"], []);\n    // Handle category click to show scholarships for that category\n    const handleCategoryClick = async (category)=>{\n        try {\n            setLoading(true);\n            setSelectedCategory(category);\n            // Fetch scholarships for the selected category using apiClient\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.apiClient.getPublicScholarships({\n                category\n            });\n            if (response.success) {\n                var _response_data;\n                setCategoryScholarships(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || []);\n                setShowCategoryResults(true);\n            } else {\n                setError(\"Failed to load \".concat(category, \" scholarships\"));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load \".concat(category, \" scholarships\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle back to main view\n    const handleBackToMain = ()=>{\n        setShowCategoryResults(false);\n        setSelectedCategory(null);\n        setCategoryScholarships([]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading projects...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        className: \"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2\",\n                                        children: \"Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                        children: \"Transforming Lives Through Education\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                        children: \"Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Active Scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_approved) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Students Supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.total_amount_awarded) || '₦0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Total Investment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: portfolio.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Program Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Scholarship Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Our comprehensive scholarship programs support students at every educational level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: portfolio.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full capitalize\",\n                                                                    children: category.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                category.approved_count,\n                                                                                \" approved\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-green-600\",\n                                                                            children: [\n                                                                                category.active_count,\n                                                                                \" active\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: category.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between pt-4 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Total: \",\n                                                                        category.total_amount\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-green-600 border-green-600 hover:bg-green-50\",\n                                                                    onClick: ()=>handleCategoryClick(category.category),\n                                                                    children: \"View Available Scholarships\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, category.category, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    showCategoryResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: handleBackToMain,\n                                                className: \"mb-4\",\n                                                children: \"← Back to Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tight mb-2\",\n                                                children: [\n                                                    selectedCategory ? selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1) : '',\n                                                    \" Scholarships\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: [\n                                                    \"Available scholarships for \",\n                                                    selectedCategory || 'selected',\n                                                    \" education level\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, this) : categoryScholarships.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryScholarships.map((scholarship)=>{\n                                        var _scholarship_category, _scholarship_category1, _scholarship_amount;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    className: \"bg-green-600 text-white rounded-full\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"rounded-full\",\n                                                                    children: ((_scholarship_category = scholarship.category) === null || _scholarship_category === void 0 ? void 0 : _scholarship_category.charAt(0).toUpperCase()) + ((_scholarship_category1 = scholarship.category) === null || _scholarship_category1 === void 0 ? void 0 : _scholarship_category1.slice(1))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-xl text-green-800 dark:text-green-200\",\n                                                            children: scholarship.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-sm line-clamp-3\",\n                                                            children: scholarship.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-green-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"₦\",\n                                                                        ((_scholarship_amount = scholarship.amount) === null || _scholarship_amount === void 0 ? void 0 : _scholarship_amount.toLocaleString()) || 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        scholarship.application_deadline ? new Date(scholarship.application_deadline).toLocaleDateString() : 'N/A'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                            onClick: ()=>{\n                                                                const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                                window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                                            },\n                                                            children: \"Apply Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, scholarship.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            \"No scholarships available for \",\n                                            selectedCategory,\n                                            \" category at the moment.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-50 dark:bg-green-950/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Measuring Our Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto\",\n                                            children: \"Every project we undertake is designed to create measurable, lasting change in the communities we serve.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Primary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.primary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Secondary Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.secondary) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-6 w-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"University Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-amber-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.approved_by_category.university) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Approved scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"text-center border-green-100 dark:border-green-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-12 w-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-6 w-6 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-1\",\n                                                        children: \"Active Programs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-600 mb-1\",\n                                                        children: (statistics === null || statistics === void 0 ? void 0 : statistics.totals.active_scholarships) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-xs\",\n                                                        children: \"Currently accepting applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-green-900 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight sm:text-4xl\",\n                                        children: \"Support Our Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-lg\",\n                                        children: \"Your contribution can help us expand our reach and create even more educational opportunities for underprivileged students across Northern Nigeria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_CheckCircle_DollarSign_Heart_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Donate to Projects\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold\",\n                                                children: \"Become a Partner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-green-950 dark:bg-black text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Building Tomorrow's Leaders\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-200 max-w-2xl mx-auto\",\n                                children: \"Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this transformative journey.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\projects\\\\page.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsPage, \"LXCPPSppHbGW1RWSamHfTqQBFaU=\");\n_c = ProjectsPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/projects/page.tsx\n"));

/***/ })

});