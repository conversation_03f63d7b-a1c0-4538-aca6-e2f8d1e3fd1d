@extends('api-docs.layout')

@section('title', 'API Overview')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-4 fw-bold text-primary">NGO Platform API</h1>
                <p class="lead text-muted">Comprehensive REST API for NGO management, scholarships, and volunteer coordination</p>
            </div>
            <div class="text-end">
                <span class="badge bg-success fs-6">v1.0</span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Start -->
<div class="row mb-5">
    <div class="col-12">
        <div class="api-card card">
            <div class="card-body">
                <h3 class="card-title">
                    <i class="fas fa-rocket text-primary me-2"></i>
                    Quick Start
                </h3>
                <p class="card-text">Get started with the NGO Platform API in minutes.</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>1. Authentication</h5>
                        <div class="code-block">
                            <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <pre><code class="language-bash">curl -X POST {{ url('/api/v1/login') }} \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'</code></pre>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>2. Make API Calls</h5>
                        <div class="code-block">
                            <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyCode(this)">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <pre><code class="language-bash">curl -X GET {{ url('/api/v1/user') }} \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Features -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-star text-warning me-2"></i>
            Key Features
        </h2>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="api-card card h-100">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Role-Based Security</h5>
                <p class="card-text">Advanced authorization with role-based access control, rate limiting, and security monitoring.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="api-card card h-100">
            <div class="card-body text-center">
                <i class="fas fa-graduation-cap fa-3x text-success mb-3"></i>
                <h5 class="card-title">Scholarship Management</h5>
                <p class="card-text">Multi-tier scholarship system supporting Primary, Secondary, and University applications.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="api-card card h-100">
            <div class="card-body text-center">
                <i class="fas fa-hands-helping fa-3x text-info mb-3"></i>
                <h5 class="card-title">Volunteer Coordination</h5>
                <p class="card-text">Skill matching, hour tracking, and training module management for volunteers.</p>
            </div>
        </div>
    </div>
</div>

<!-- Role Access Matrix -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-users text-info me-2"></i>
            User Roles & Access
        </h2>
        
        <div class="api-card card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Role</th>
                                <th>Description</th>
                                <th>Rate Limit</th>
                                <th>Key Permissions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($roleMatrix as $role => $details)
                            <tr>
                                <td>
                                    <span class="role-badge role-{{ str_replace('_', '-', $role) }}">{{ $role }}</span>
                                </td>
                                <td>{{ $details['description'] }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ $details['rate_limit'] }}</span>
                                </td>
                                <td>
                                    @foreach(array_slice($details['permissions'], 0, 2) as $permission)
                                        <span class="badge bg-light text-dark me-1">{{ $permission }}</span>
                                    @endforeach
                                    @if(count($details['permissions']) > 2)
                                        <span class="text-muted">+{{ count($details['permissions']) - 2 }} more</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Categories -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-sitemap text-secondary me-2"></i>
            API Categories
        </h2>
    </div>
    
    @foreach($apiRoutes as $category => $routes)
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="api-card card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    @switch($category)
                        @case('Authentication')
                            <i class="fas fa-key text-primary me-2"></i>
                            @break
                        @case('Scholarships')
                            <i class="fas fa-graduation-cap text-success me-2"></i>
                            @break
                        @case('Volunteers')
                            <i class="fas fa-hands-helping text-info me-2"></i>
                            @break
                        @case('Programs')
                            <i class="fas fa-project-diagram text-warning me-2"></i>
                            @break
                        @case('Events')
                            <i class="fas fa-calendar text-danger me-2"></i>
                            @break
                        @case('Admin')
                            <i class="fas fa-cog text-secondary me-2"></i>
                            @break
                        @default
                            <i class="fas fa-folder text-muted me-2"></i>
                    @endswitch
                    {{ $category }}
                </h5>
                <p class="card-text text-muted">{{ count($routes) }} endpoints available</p>
                
                <div class="mb-3">
                    @foreach(array_slice($routes, 0, 3) as $endpoint => $details)
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">{{ explode(' ', $endpoint)[1] ?? $endpoint }}</small>
                            <span class="method-badge method-{{ strtolower(explode(' ', $endpoint)[0] ?? 'get') }}">
                                {{ explode(' ', $endpoint)[0] ?? 'GET' }}
                            </span>
                        </div>
                    @endforeach
                    @if(count($routes) > 3)
                        <small class="text-muted">+{{ count($routes) - 3 }} more endpoints</small>
                    @endif
                </div>
                
                <a href="{{ route('api-docs.endpoint', strtolower($category)) }}" class="btn btn-outline-primary btn-sm">
                    View Details <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    @endforeach
</div>

<!-- Base URL and Standards -->
<div class="row">
    <div class="col-md-6">
        <div class="api-card card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-link text-primary me-2"></i>
                    Base URL
                </h5>
                <div class="code-block">
                    <code>{{ url('/api/v1') }}</code>
                </div>
                <p class="mt-2 mb-0 text-muted">All API endpoints are prefixed with this base URL.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="api-card card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    Standards
                </h5>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i> RESTful API design</li>
                    <li><i class="fas fa-check text-success me-2"></i> JSON request/response</li>
                    <li><i class="fas fa-check text-success me-2"></i> Bearer token authentication</li>
                    <li><i class="fas fa-check text-success me-2"></i> Consistent error handling</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
