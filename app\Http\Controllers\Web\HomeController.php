<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Models\Event;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\TeamMember;
use App\Models\Scholarship;
use App\Models\DonationCampaign;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the main landing page
     */
    public function index()
    {
        $featuredPrograms = Program::where('status', 'active')
            ->take(3)
            ->get();
            
        $upcomingEvents = Event::where('status', 'published')
            ->where('start_datetime', '>', now())
            ->orderBy('start_datetime', 'asc')
            ->take(3)
            ->get();
            
        $recentPosts = BlogPost::where('status', 'published')
            ->with('category')
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();
            
        $activeCampaigns = DonationCampaign::where('status', 'active')
            ->where('end_date', '>', now())
            ->take(2)
            ->get();

        $stats = [
            'programs' => Program::where('status', 'active')->count(),
            'events' => Event::where('status', 'published')->count(),
            'volunteers' => \App\Models\User::where('role', 'volunteer')->count(),
            'beneficiaries' => 1500 // This could be calculated from programs
        ];

        return view('home', compact(
            'featuredPrograms', 
            'upcomingEvents', 
            'recentPosts', 
            'activeCampaigns',
            'stats'
        ));
    }

    /**
     * Show about page
     */
    public function about()
    {
        $teamMembers = TeamMember::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->get();
            
        return view('about', compact('teamMembers'));
    }

    /**
     * Show programs page
     */
    public function programs()
    {
        $programs = Program::where('status', 'active')
            ->orderBy('created_at', 'desc')
            ->paginate(9);
            
        return view('programs', compact('programs'));
    }

    /**
     * Show single program
     */
    public function program($slug)
    {
        $program = Program::where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();
            
        return view('program', compact('program'));
    }

    /**
     * Show events page
     */
    public function events()
    {
        $events = Event::where('status', 'published')
            ->where('start_datetime', '>', now())
            ->orderBy('start_datetime', 'asc')
            ->paginate(9);
            
        return view('events', compact('events'));
    }

    /**
     * Show single event
     */
    public function event($id)
    {
        $event = Event::where('id', $id)
            ->where('status', 'published')
            ->firstOrFail();
            
        return view('event', compact('event'));
    }

    /**
     * Show blog page
     */
    public function blog(Request $request)
    {
        $query = BlogPost::where('status', 'published')
            ->with(['category', 'author']);

        if ($request->has('category')) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'LIKE', "%{$search}%")
                  ->orWhere('excerpt', 'LIKE', "%{$search}%")
                  ->orWhere('content', 'LIKE', "%{$search}%");
            });
        }

        $posts = $query->orderBy('published_at', 'desc')->paginate(9);
        $categories = BlogCategory::all();
        
        return view('blog', compact('posts', 'categories'));
    }

    /**
     * Show single blog post
     */
    public function blogPost($slug)
    {
        $post = BlogPost::where('slug', $slug)
            ->where('status', 'published')
            ->with(['category', 'author'])
            ->firstOrFail();
            
        // Increment view count
        $post->increment('views_count');
        
        $relatedPosts = BlogPost::where('status', 'published')
            ->where('id', '!=', $post->id)
            ->where('category_id', $post->category_id)
            ->take(3)
            ->get();
            
        return view('blog-post', compact('post', 'relatedPosts'));
    }

    /**
     * Show team page
     */
    public function team()
    {
        $teamMembers = TeamMember::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->get();
            
        return view('team', compact('teamMembers'));
    }

    /**
     * Show contact page
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Show donate page
     */
    public function donate()
    {
        $campaigns = DonationCampaign::where('status', 'active')
            ->where('end_date', '>', now())
            ->get();
            
        return view('donate', compact('campaigns'));
    }

    /**
     * Show volunteer page
     */
    public function volunteer()
    {
        return view('volunteer');
    }

    /**
     * Show scholarships page
     */
    public function scholarships()
    {
        $scholarships = Scholarship::where('status', 'open')
            ->where('application_deadline', '>', now())
            ->orderBy('application_deadline', 'asc')
            ->paginate(9);
            
        return view('scholarships', compact('scholarships'));
    }

    /**
     * Show single scholarship
     */
    public function scholarship($slug)
    {
        // Create a mock scholarship object since we don't have real models yet
        $scholarship = (object) [
            'title' => 'HLTKKQ Excellence Scholarship',
            'slug' => $slug,
            'description' => 'A prestigious scholarship program for outstanding students who demonstrate academic excellence and commitment to community service.',
            'amount' => 500000,
            'status' => 'open',
            'eligibility_criteria' => 'Must be a Nigerian citizen, aged 18-25, with excellent academic record (minimum 3.5 GPA), and demonstrated financial need.',
            'requirements' => [
                'Completed application form',
                'Academic transcripts',
                'Two recommendation letters',
                'Personal statement (500 words)',
                'Proof of income',
                'Community service certificate'
            ],
            'application_deadline' => now()->addDays(30),
            'max_applicants' => 50,
            'created_at' => now()->subDays(10),
            'updated_at' => now()
        ];
            
        return view('scholarship', compact('scholarship'));
    }
} 