"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Users, ArrowRight } from "lucide-react"

interface Event {
  id: number
  title: string
  description: string
  date: string
  time: string
  location: string
  image: string
  category: "community" | "fundraising" | "education" | "health" | "environment"
  attendees: number
  featured?: boolean
}

const events: Event[] = [
  {
    id: 1,
    title: "Back-to-School Initiative Launch",
    description: "Kickstarting the academic year with essential school supplies for underprivileged students.",
    date: "2024-06-15",
    time: "10:00 AM - 12:00 PM",
    location: "Kaduna North Local Government, Kaduna",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 150,
    featured: true,
  },
  {
    id: 2,
    title: "Scholarship Award Ceremony",
    description: "Recognizing and awarding scholarships to deserving students to support their educational pursuits.",
    date: "2024-07-22",
    time: "2:00 PM - 4:00 PM",
    location: "Katsina State Secretariat, Katsina",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 200,
  },
  {
    id: 3,
    title: "Community Literacy Program",
    description: "Empowering adults and youths with essential literacy skills for personal and community development.",
    date: "2024-08-05",
    time: "10:00 AM - 1:00 PM",
    location: "Kano Municipal Library, Kano",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 80,
  },
  {
    id: 4,
    title: "Educational Materials Distribution",
    description: "Providing textbooks, notebooks, and other learning resources to schools in underserved areas.",
    date: "2024-06-28",
    time: "9:00 AM - 11:00 AM",
    location: "Zaria Local Education Authority, Kaduna",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 100,
  },
  {
    id: 5,
    title: "Teacher Training Workshop",
    description: "Enhancing the skills and knowledge of teachers through professional development workshops.",
    date: "2024-07-10",
    time: "10:00 AM - 3:00 PM",
    location: "Katsina Teachers Institute, Katsina",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 60,
  },
  {
    id: 6,
    title: "Parent-Teacher Conference",
    description: "Facilitating communication and collaboration between parents and teachers for student success.",
    date: "2024-08-15",
    time: "1:00 PM - 4:00 PM",
    location: "Kano State College of Education, Kano",
    image: "/placeholder.svg?height=400&width=600",
    category: "education",
    attendees: 120,
  },
]

export function EventsSection() {
  const [activeCategory, setActiveCategory] = useState<string>("all")
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }))
  }

  const filteredEvents = activeCategory === "all" ? events : events.filter((event) => event.category === activeCategory)

  const categories = [
    { id: "all", label: "All Events" },
    { id: "community", label: "Community" },
    { id: "fundraising", label: "Fundraising" },
    { id: "education", label: "Education" },
    { id: "health", label: "Health" },
    { id: "environment", label: "Environment" },
  ]

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "long", day: "numeric" }
    return new Date(dateString).toLocaleDateString("en-US", options)
  }

  const featuredEvent = events.find((event) => event.featured)
  const regularEvents = filteredEvents.filter((event) => !event.featured || activeCategory !== "all")

  return (
    <div>
      <div className="flex flex-wrap justify-center gap-2 mb-8">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeCategory === category.id
                ? "bg-green-600 text-white shadow-md"
                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800/30"
            }`}
          >
            {category.label}
          </button>
        ))}
      </div>

      {/* Featured Event */}
      {featuredEvent && activeCategory === "all" && (
        <Card className="mb-12 overflow-hidden border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-700 transition-all duration-300 hover:shadow-lg group rounded-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="relative h-64 md:h-auto overflow-hidden">
              <div
                className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${
                  imagesLoaded[featuredEvent.id] ? "opacity-0" : "opacity-100"
                }`}
              />
              <Image
                src={featuredEvent.image || "/placeholder.svg"}
                alt={featuredEvent.title}
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
                className={`object-cover transition-all duration-500 group-hover:scale-105 ${
                  imagesLoaded[featuredEvent.id] ? "opacity-100" : "opacity-0"
                }`}
                onLoad={() => handleImageLoad(featuredEvent.id)}
                priority
              />
              <div className="absolute top-4 left-4">
                <Badge className="bg-amber-500 hover:bg-amber-600 text-white rounded-full px-3 py-1">
                  Featured Event
                </Badge>
              </div>
            </div>
            <CardContent className="p-6 flex flex-col justify-center">
              <Badge className="w-fit mb-2 bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800 capitalize rounded-full">
                {featuredEvent.category}
              </Badge>
              <h3 className="text-2xl font-bold mb-2 text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                {featuredEvent.title}
              </h3>
              <p className="text-muted-foreground mb-6">{featuredEvent.description}</p>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 text-green-600" />
                  <span>{formatDate(featuredEvent.date)}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 text-green-600" />
                  <span>{featuredEvent.time}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 text-green-600" />
                  <span>{featuredEvent.location}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="h-4 w-4 text-green-600" />
                  <span>{featuredEvent.attendees} Attendees</span>
                </div>
              </div>
              <Button className="w-fit bg-green-600 hover:bg-green-700 transition-all duration-300 hover:scale-105 rounded-full">
                Register Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </div>
        </Card>
      )}

      {/* Regular Events */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {regularEvents.map((event) => (
          <Card
            key={event.id}
            className="overflow-hidden border-green-100 dark:border-green-900/50 hover:border-green-300 dark:hover:border-green-700 transition-all duration-300 hover:shadow-md hover:-translate-y-1 group rounded-xl"
          >
            <div className="relative h-48 overflow-hidden">
              <div
                className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${
                  imagesLoaded[event.id] ? "opacity-0" : "opacity-100"
                }`}
              />
              <Image
                src={event.image || "/placeholder.svg"}
                alt={event.title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className={`object-cover transition-all duration-500 group-hover:scale-105 ${
                  imagesLoaded[event.id] ? "opacity-100" : "opacity-0"
                }`}
                onLoad={() => handleImageLoad(event.id)}
              />
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-4 left-4">
                <Badge className="bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/80 dark:text-green-200 dark:hover:bg-green-800/80 capitalize rounded-full">
                  {event.category}
                </Badge>
              </div>
            </div>
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-2 text-green-800 dark:text-green-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                {event.title}
              </h3>
              <p className="text-muted-foreground text-sm mb-4 line-clamp-2">{event.description}</p>
              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 text-green-600" />
                  <span>{formatDate(event.date)}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 text-green-600" />
                  <span>{event.time}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 text-green-600" />
                  <span className="truncate">{event.location}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="px-6 pb-6 pt-0 flex justify-between items-center">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Users className="h-4 w-4 text-green-600" />
                <span>{event.attendees} Attendees</span>
              </div>
              <Button variant="outline" size="sm" className="rounded-full hover:bg-green-50 dark:hover:bg-green-950/30">
                Register
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold mb-2">No events found</h3>
          <p className="text-muted-foreground">Try selecting a different category.</p>
        </div>
      )}
    </div>
  )
}
