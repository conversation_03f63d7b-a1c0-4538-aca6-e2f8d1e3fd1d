<?php $__env->startSection('title', 'Donation Campaigns'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Donation Campaigns</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.donations.index')); ?>">Donations</a></li>
                        <li class="breadcrumb-item active">Campaigns</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('admin.donations.campaigns.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Campaign
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?status=active">Active</a></li>
                            <li><a class="dropdown-item" href="?status=completed">Completed</a></li>
                            <li><a class="dropdown-item" href="?status=paused">Paused</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?">All Campaigns</a></li>
                        </ul>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportCampaigns()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Campaigns</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(method_exists($campaigns, 'total') ? $campaigns->total() : $campaigns->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Campaigns</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($campaigns->where('status', 'active')->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Raised</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₦<?php echo e(number_format($campaigns->sum('total_raised'), 2)); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Goal Achievement</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format(($campaigns->sum('total_raised') / max($campaigns->sum('goal_amount'), 1)) * 100, 1)); ?>%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-target fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Donation Campaigns</h6>
        </div>
        <div class="card-body">
            <?php if($campaigns->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" id="campaignsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Campaign</th>
                                <th>Goal Amount</th>
                                <th>Raised</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>End Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $campaigns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campaign): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($campaign->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $campaign->image)); ?>" alt="<?php echo e($campaign->title); ?>" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-primary rounded d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                <i class="fas fa-bullhorn text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($campaign->title); ?></h6>
                                            <small class="text-muted"><?php echo e(Str::limit($campaign->description, 50)); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>₦<?php echo e(number_format($campaign->goal_amount, 2)); ?></td>
                                <td>₦<?php echo e(number_format($campaign->total_raised ?? 0, 2)); ?></td>
                                <td>
                                    <?php
                                        $progress = ($campaign->total_raised / max($campaign->goal_amount, 1)) * 100;
                                    ?>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo e(min($progress, 100)); ?>%" aria-valuenow="<?php echo e($progress); ?>" aria-valuemin="0" aria-valuemax="100">
                                            <?php echo e(number_format($progress, 1)); ?>%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php switch($campaign->status):
                                        case ('active'): ?>
                                            <span class="badge bg-success">Active</span>
                                            <?php break; ?>
                                        <?php case ('completed'): ?>
                                            <span class="badge bg-primary">Completed</span>
                                            <?php break; ?>
                                        <?php case ('paused'): ?>
                                            <span class="badge bg-warning">Paused</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($campaign->status)); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td><?php echo e($campaign->end_date ? $campaign->end_date->format('M d, Y') : 'No end date'); ?></td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="viewCampaign(<?php echo e($campaign->id); ?>)">
                                                <i class="fas fa-eye me-2"></i>View Details
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-chart-bar me-2"></i>Analytics
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <?php if($campaign->status === 'active'): ?>
                                                <li><a class="dropdown-item text-warning" href="#" onclick="pauseCampaign(<?php echo e($campaign->id); ?>)">
                                                    <i class="fas fa-pause me-2"></i>Pause
                                                </a></li>
                                            <?php elseif($campaign->status === 'paused'): ?>
                                                <li><a class="dropdown-item text-success" href="#" onclick="resumeCampaign(<?php echo e($campaign->id); ?>)">
                                                    <i class="fas fa-play me-2"></i>Resume
                                                </a></li>
                                            <?php endif; ?>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteCampaign(<?php echo e($campaign->id); ?>)">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if(method_exists($campaigns, 'links')): ?>
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="text-muted">
                            Showing <?php echo e($campaigns->firstItem()); ?> to <?php echo e($campaigns->lastItem()); ?> of <?php echo e($campaigns->total()); ?> campaigns
                        </p>
                    </div>
                    <div>
                        <?php echo e($campaigns->links()); ?>

                    </div>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No campaigns found</h5>
                    <p class="text-muted">Create your first donation campaign to get started.</p>
                    <a href="<?php echo e(route('admin.donations.campaigns.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Campaign
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function viewCampaign(campaignId) {
    // Implement view campaign functionality
    console.log('View campaign:', campaignId);
}

function pauseCampaign(campaignId) {
    if (confirm('Are you sure you want to pause this campaign?')) {
        // Implement pause campaign functionality
        console.log('Pause campaign:', campaignId);
    }
}

function resumeCampaign(campaignId) {
    if (confirm('Are you sure you want to resume this campaign?')) {
        // Implement resume campaign functionality
        console.log('Resume campaign:', campaignId);
    }
}

function deleteCampaign(campaignId) {
    if (confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
        // Implement delete campaign functionality
        console.log('Delete campaign:', campaignId);
    }
}

function exportCampaigns() {
    // Implement export functionality
    console.log('Export campaigns');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/donations/campaigns.blade.php ENDPATH**/ ?>