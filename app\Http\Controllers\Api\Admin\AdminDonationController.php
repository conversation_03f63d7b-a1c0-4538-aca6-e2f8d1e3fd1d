<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Donation;
use App\Models\DonationCampaign;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Donations",
 *     description="Admin API Endpoints for Donation Management"
 * )
 */
class AdminDonationController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/donations",
     *     summary="Get all donations for admin",
     *     tags={"Admin Donations"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Donations retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            
            $query = Donation::with(['user', 'campaign'])
                ->orderBy('created_at', 'desc');

            if ($status) {
                $query->where('payment_status', $status);
            }

            $donations = $query->paginate($perPage);

            // Calculate summary statistics
            $stats = [
                'total_donations' => Donation::where('payment_status', 'completed')->sum('amount'),
                'total_count' => Donation::where('payment_status', 'completed')->count(),
                'pending_donations' => Donation::where('payment_status', 'pending')->sum('amount'),
                'pending_count' => Donation::where('payment_status', 'pending')->count(),
                'this_month' => Donation::where('payment_status', 'completed')
                    ->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year)
                    ->sum('amount')
            ];

            return response()->json([
                'success' => true,
                'message' => 'Donations retrieved successfully',
                'data' => [
                    'donations' => $donations,
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve donations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/donations/{id}",
     *     summary="Get specific donation details",
     *     tags={"Admin Donations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Donation retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $donation = Donation::with(['user', 'campaign'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Donation retrieved successfully',
                'data' => $donation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Donation not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/donations/{id}",
     *     summary="Update donation status",
     *     tags={"Admin Donations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="payment_status", type="string", enum={"pending", "completed", "failed", "refunded"}),
     *             @OA\Property(property="notes", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Donation updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $donation = Donation::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'payment_status' => 'in:pending,completed,failed,refunded',
                'notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $donation->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Donation updated successfully',
                'data' => $donation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update donation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get donation receipt
     */
    public function receipt($id): JsonResponse
    {
        try {
            $donation = Donation::with(['user', 'campaign'])
                ->where('payment_status', 'completed')
                ->findOrFail($id);

            // Generate receipt data
            $receiptData = [
                'donation_id' => $donation->id,
                'receipt_number' => 'RCP-' . str_pad($donation->id, 6, '0', STR_PAD_LEFT),
                'date' => $donation->created_at->format('Y-m-d'),
                'donor' => [
                    'name' => $donation->donor_name ?: ($donation->user ? $donation->user->full_name : 'Anonymous'),
                    'email' => $donation->donor_email ?: ($donation->user ? $donation->user->email : null)
                ],
                'amount' => $donation->amount,
                'currency' => $donation->currency ?? 'USD',
                'campaign' => $donation->campaign ? $donation->campaign->title : 'General Donation',
                'payment_method' => $donation->payment_method,
                'transaction_id' => $donation->transaction_id
            ];

            return response()->json([
                'success' => true,
                'message' => 'Receipt generated successfully',
                'data' => $receiptData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate receipt',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get donation campaigns
     */
    public function campaigns(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $campaigns = DonationCampaign::withSum('donations', 'amount')
                ->withCount('donations')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Campaigns retrieved successfully',
                'data' => $campaigns
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve campaigns',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create donation campaign
     */
    public function storeCampaign(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'goal_amount' => 'required|numeric|min:1',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'is_active' => 'boolean',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $campaign = DonationCampaign::create($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Campaign created successfully',
                'data' => $campaign
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create campaign',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update donation campaign
     */
    public function updateCampaign(Request $request, $id): JsonResponse
    {
        try {
            $campaign = DonationCampaign::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'string|max:255',
                'description' => 'string',
                'goal_amount' => 'numeric|min:1',
                'start_date' => 'date',
                'end_date' => 'date|after:start_date',
                'is_active' => 'boolean',
                'image' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $campaign->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Campaign updated successfully',
                'data' => $campaign
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update campaign',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete donation campaign
     */
    public function destroyCampaign($id): JsonResponse
    {
        try {
            $campaign = DonationCampaign::findOrFail($id);
            
            // Check if campaign has donations
            if ($campaign->donations()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete campaign with existing donations'
                ], 400);
            }

            $campaign->delete();

            return response()->json([
                'success' => true,
                'message' => 'Campaign deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete campaign',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
