"use client"

import { useEffect, useState } from "react"
import { apiClient, extractArrayData } from "@/lib/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UserIdCard from "@/components/ui/user-id-card"
import ProfileSettings from "@/components/ui/profile-settings"
import { 
  User, 
  Heart, 
  Calendar, 
  Gift, 
  BookOpen, 
  Users,
  TrendingUp,
  Target,
  MessageSquare,
  Award,
  HandHeart,
  GraduationCap,
  Building,
  CreditCard,
  Settings
} from "lucide-react"
import Link from "next/link"

interface UserType {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  preferences?: any
  short_id?: string
  qr_code_url?: string
  profile_picture_url?: string
  date_of_birth?: string
  gender?: string
  address?: string
  city?: string
  state?: string
  country?: string
  role: string
  status: string
  created_at?: string
}

// Removed UserDashboardProps as Next.js pages don't take props

export default function UserDashboard() {
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)
  const [donations, setDonations] = useState([])
  const [eventRegistrations, setEventRegistrations] = useState([])
  const [upcomingEvents, setUpcomingEvents] = useState([])
  const [programs, setPrograms] = useState([])
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)

  const handleUserUpdate = (updatedUser: any) => {
    setCurrentUser(prev => ({ ...prev, ...updatedUser }))
  }

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch user-specific data
        const [
          userResponse,
          donationsResponse,
          eventsResponse,
          registrationsResponse,
          programsResponse,
          campaignsResponse
        ] = await Promise.all([
          apiClient.getProfile(),
          apiClient.getMyDonations(),
          apiClient.getUpcomingEvents(),
          apiClient.getMyEventRegistrations(),
          apiClient.getPrograms(),
          apiClient.getDonationCampaigns()
        ])

        if (userResponse.success) {
          setCurrentUser(userResponse.data)
        }

        if (donationsResponse.success) {
          setDonations(extractArrayData(donationsResponse))
        }

        if (eventsResponse.success) {
          const eventsData = extractArrayData(eventsResponse)
          setUpcomingEvents(eventsData.slice(0, 5))
        }

        if (registrationsResponse.success) {
          setEventRegistrations(extractArrayData(registrationsResponse))
        }

        if (programsResponse.success) {
          const programsData = extractArrayData(programsResponse)
          setPrograms(programsData.slice(0, 3))
        }

        if (campaignsResponse.success) {
          const campaignsData = extractArrayData(campaignsResponse)
          setCampaigns(campaignsData.slice(0, 3))
        }

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const totalDonated = donations.reduce((sum: number, donation: any) => sum + donation.amount, 0)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load user data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header - Mobile Responsive */}
        <div className="bg-white rounded-none sm:rounded-lg shadow-sm p-4 sm:p-6 mx-0 sm:mx-6 mt-0 sm:mt-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="h-12 w-12 sm:h-16 sm:w-16 bg-green-600 rounded-full flex items-center justify-center">
                {currentUser.profile_picture_url ? (
                  <img
                    src={currentUser.profile_picture_url}
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover"
                  />
                ) : (
                  <User className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                )}
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
                  Welcome back, {currentUser.first_name}!
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Community Member Dashboard</p>
                <p className="text-xs sm:text-sm text-green-600">
                  Thank you for being part of our mission to empower communities
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                <Link href="/donate">
                  <Heart className="h-4 w-4 mr-2" />
                  Make a Donation
                </Link>
              </Button>
              <Button size="sm" asChild className="w-full sm:w-auto">
                <Link href="/about">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards - Mobile Responsive Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-4 sm:px-6 mt-4 sm:mt-6">
          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Donated</CardTitle>
              <Gift className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold">₦{totalDonated.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Your contribution
              </p>
            </CardContent>
          </Card>
          
          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Donations</CardTitle>
              <Heart className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {donations.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Times donated
              </p>
            </CardContent>
          </Card>

          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Events</CardTitle>
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">
                {eventRegistrations.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Events registered
              </p>
            </CardContent>
          </Card>

          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Impact</CardTitle>
              <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold text-purple-600">
                {Math.floor(totalDonated / 1000)}
              </div>
              <p className="text-xs text-muted-foreground">
                Lives impacted
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Get Involved Section - Mobile Responsive */}
        <div className="px-4 sm:px-6 mt-4 sm:mt-6">
          <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-green-700 text-lg sm:text-xl">
                <HandHeart className="h-5 w-5 mr-2" />
                Get More Involved
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Discover ways to make a bigger impact in your community
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                  <GraduationCap className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-medium mb-2 text-sm sm:text-base">Become a Student</h4>
                  <p className="text-xs sm:text-sm text-gray-600 mb-3">Apply for scholarships and access educational resources</p>
                  <Button size="sm" variant="outline" asChild className="w-full">
                    <Link href="/auth/register?type=student">Apply Now</Link>
                  </Button>
                </div>
                <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                  <Users className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-medium mb-2 text-sm sm:text-base">Volunteer With Us</h4>
                  <p className="text-xs sm:text-sm text-gray-600 mb-3">Contribute your time and skills to support our mission</p>
                  <Button size="sm" variant="outline" asChild className="w-full">
                    <Link href="/auth/register?type=volunteer">Join Us</Link>
                  </Button>
                </div>
                <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                  <Building className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2" />
                  <h4 className="font-medium mb-2 text-sm sm:text-base">Partner Organization</h4>
                  <p className="text-xs sm:text-sm text-gray-600 mb-3">Collaborate with us to expand educational opportunities</p>
                  <Button size="sm" variant="outline" asChild className="w-full">
                    <Link href="/auth/register?type=partner">Partner</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content - Mobile Responsive Tabs */}
        <div className="px-4 sm:px-6 mt-4 sm:mt-6 pb-6">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6 gap-1">
              <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
              <TabsTrigger value="donations" className="text-xs sm:text-sm">Donations</TabsTrigger>
              <TabsTrigger value="events" className="text-xs sm:text-sm">Events</TabsTrigger>
              <TabsTrigger value="programs" className="text-xs sm:text-sm">Programs</TabsTrigger>
              <TabsTrigger value="id-card" className="text-xs sm:text-sm">ID Card</TabsTrigger>
              <TabsTrigger value="profile" className="text-xs sm:text-sm">
                <Settings className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                <span className="hidden sm:inline">Profile</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Recent Donations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-base sm:text-lg">
                      <Heart className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                      Recent Donations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {donations.slice(0, 3).map((donation: any) => (
                      <div key={donation.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm sm:text-base">₦{donation.amount.toLocaleString()}</p>
                          <p className="text-xs sm:text-sm text-gray-600 truncate">
                            {donation.donation_type || 'General'} • {new Date(donation.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge variant="secondary" className="text-xs">{donation.currency}</Badge>
                      </div>
                    ))}
                    {donations.length === 0 && (
                      <div className="text-center py-8">
                        <Heart className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4 text-sm sm:text-base">No donations yet</p>
                        <Button asChild size="sm" className="w-full sm:w-auto">
                          <Link href="/donate">Make Your First Donation</Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Active Campaigns */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-base sm:text-lg">
                      <Target className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                      Active Campaigns
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {campaigns.map((campaign: any) => (
                      <div key={campaign.id} className="p-3 border rounded-lg">
                        <h4 className="font-medium text-sm sm:text-base">{campaign.title}</h4>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2">{campaign.description}</p>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 space-y-2 sm:space-y-0">
                          <span className="text-xs sm:text-sm text-green-600">
                            Goal: ₦{campaign.goal?.toLocaleString()}
                          </span>
                          <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                            <Link href={`/donate?campaign=${campaign.id}`}>Donate</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                    {campaigns.length === 0 && (
                      <p className="text-gray-500 text-center py-4 text-sm">No active campaigns</p>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Programs - Mobile Responsive */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-base sm:text-lg">
                    <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Our Programs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {programs.map((program: any) => (
                      <div key={program.id} className="p-4 border rounded-lg">
                        <h4 className="font-medium text-sm sm:text-base">{program.title}</h4>
                        <p className="text-xs sm:text-sm text-gray-600 mt-2 line-clamp-3">{program.description}</p>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-3 space-y-2 sm:space-y-0">
                          <Badge variant="outline" className="text-xs w-fit">{program.status}</Badge>
                          <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                            <Link href={`/projects/${program.slug}`}>Learn More</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                    {programs.length === 0 && (
                      <div className="col-span-full text-center py-8">
                        <BookOpen className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 text-sm sm:text-base">No programs available</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="donations" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">My Donation History</CardTitle>
                  <CardDescription className="text-sm sm:text-base">
                    Track your contributions and see your impact
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {donations.map((donation: any) => (
                      <div key={donation.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-sm sm:text-base">₦{donation.amount.toLocaleString()}</h4>
                          <Badge variant="secondary" className="text-xs">{donation.payment_status}</Badge>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600">
                          <p>Type: {donation.donation_type || 'General'}</p>
                          <p>Date: {new Date(donation.created_at).toLocaleDateString()}</p>
                          <p>Currency: {donation.currency}</p>
                          <p>Method: {donation.payment_method || 'Online'}</p>
                        </div>
                        <div className="mt-3">
                          <Button variant="outline" size="sm" className="w-full sm:w-auto">View Receipt</Button>
                        </div>
                      </div>
                    ))}
                    {donations.length === 0 && (
                      <div className="text-center py-8">
                        <Gift className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4 text-sm sm:text-base">No donations recorded</p>
                        <Button asChild className="w-full sm:w-auto">
                          <Link href="/donate">Make Your First Donation</Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">Community Events</CardTitle>
                  <CardDescription className="text-sm sm:text-base">
                    Join events and connect with your community
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingEvents.map((event: any) => (
                      <div key={event.id} className="border rounded-lg p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 space-y-2 sm:space-y-0">
                          <h4 className="font-semibold text-sm sm:text-base">{event.title}</h4>
                          <Button variant="outline" size="sm" className="w-full sm:w-auto">Register</Button>
                        </div>
                        <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                          <p>Date: {new Date(event.start_datetime).toLocaleDateString()}</p>
                          <p>Type: {event.event_type}</p>
                          {event.location && <p>Location: {event.location}</p>}
                        </div>
                      </div>
                    ))}
                    {upcomingEvents.length === 0 && (
                      <p className="text-gray-500 text-center py-4 text-sm">No upcoming events</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="programs" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">Our Impact Programs</CardTitle>
                  <CardDescription className="text-sm sm:text-base">
                    Learn about our initiatives and how you can contribute
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {programs.map((program: any) => (
                      <div key={program.id} className="border rounded-lg p-4">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 space-y-2 sm:space-y-0">
                          <h4 className="font-semibold text-sm sm:text-base">{program.title}</h4>
                          <Badge variant="outline" className="text-xs w-fit">{program.status}</Badge>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 mb-3">{program.description}</p>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                          <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                            <Link href={`/projects/${program.slug}`}>Learn More</Link>
                          </Button>
                          <Button size="sm" asChild className="w-full sm:w-auto">
                            <Link href={`/donate?program=${program.id}`}>Support This Program</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                    {programs.length === 0 && (
                      <div className="text-center py-8">
                        <BookOpen className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4 text-sm sm:text-base">No programs available</p>
                        <Button asChild className="w-full sm:w-auto">
                          <Link href="/projects">Explore Programs</Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="id-card" className="space-y-6">
              <UserIdCard user={currentUser} />
            </TabsContent>

            <TabsContent value="profile" className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm">
                <ProfileSettings user={currentUser} onUserUpdate={handleUserUpdate} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
} 