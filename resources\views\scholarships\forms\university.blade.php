<!-- University Application Form -->
<div class="space-y-8">
    <!-- Personal Information -->
    <div>
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Personal Information
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Full Name <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="full_name"
                    name="full_name"
                    value="{{ old('full_name', $user->name ?? '') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('full_name') border-red-500 @enderror"
                    required
                >
                @error('full_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    value="{{ old('email', $user->email ?? '') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('email') border-red-500 @enderror"
                    required
                >
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                </label>
                <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value="{{ old('phone') }}"
                    placeholder="e.g., 08012345678"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('phone') border-red-500 @enderror"
                    required
                >
                @error('phone')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth <span class="text-red-500">*</span>
                </label>
                <input
                    type="date"
                    id="date_of_birth"
                    name="date_of_birth"
                    value="{{ old('date_of_birth') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('date_of_birth') border-red-500 @enderror"
                    required
                >
                @error('date_of_birth')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="md:col-span-2">
                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                    Home Address <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="address"
                    name="address"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('address') border-red-500 @enderror"
                    required
                >{{ old('address') }}</textarea>
                @error('address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- Academic Information -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            Academic Information
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="institution" class="block text-sm font-medium text-gray-700 mb-2">
                    University/Institution <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="institution"
                    name="institution"
                    value="{{ old('institution') }}"
                    placeholder="e.g., University of Lagos"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('institution') border-red-500 @enderror"
                    required
                >
                @error('institution')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="matriculation_number" class="block text-sm font-medium text-gray-700 mb-2">
                    Matriculation Number <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="matriculation_number"
                    name="matriculation_number"
                    value="{{ old('matriculation_number') }}"
                    placeholder="e.g., 180405001"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('matriculation_number') border-red-500 @enderror"
                    required
                >
                @error('matriculation_number')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="field_of_study" class="block text-sm font-medium text-gray-700 mb-2">
                    Field of Study/Course <span class="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="field_of_study"
                    name="field_of_study"
                    value="{{ old('field_of_study') }}"
                    placeholder="e.g., Computer Science"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('field_of_study') border-red-500 @enderror"
                    required
                >
                @error('field_of_study')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="academic_level" class="block text-sm font-medium text-gray-700 mb-2">
                    Academic Level <span class="text-red-500">*</span>
                </label>
                <select
                    id="academic_level"
                    name="academic_level"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('academic_level') border-red-500 @enderror"
                    required
                >
                    <option value="">Select Level</option>
                    <option value="undergraduate" {{ old('academic_level') === 'undergraduate' ? 'selected' : '' }}>Undergraduate</option>
                    <option value="postgraduate" {{ old('academic_level') === 'postgraduate' ? 'selected' : '' }}>Postgraduate</option>
                    <option value="masters" {{ old('academic_level') === 'masters' ? 'selected' : '' }}>Masters</option>
                    <option value="phd" {{ old('academic_level') === 'phd' ? 'selected' : '' }}>PhD</option>
                </select>
                @error('academic_level')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="current_year" class="block text-sm font-medium text-gray-700 mb-2">
                    Current Year of Study <span class="text-red-500">*</span>
                </label>
                <select
                    id="current_year"
                    name="current_year"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('current_year') border-red-500 @enderror"
                    required
                >
                    <option value="">Select Year</option>
                    <option value="100 Level" {{ old('current_year') === '100 Level' ? 'selected' : '' }}>100 Level</option>
                    <option value="200 Level" {{ old('current_year') === '200 Level' ? 'selected' : '' }}>200 Level</option>
                    <option value="300 Level" {{ old('current_year') === '300 Level' ? 'selected' : '' }}>300 Level</option>
                    <option value="400 Level" {{ old('current_year') === '400 Level' ? 'selected' : '' }}>400 Level</option>
                    <option value="500 Level" {{ old('current_year') === '500 Level' ? 'selected' : '' }}>500 Level</option>
                    <option value="600 Level" {{ old('current_year') === '600 Level' ? 'selected' : '' }}>600 Level</option>
                    <option value="Masters Year 1" {{ old('current_year') === 'Masters Year 1' ? 'selected' : '' }}>Masters Year 1</option>
                    <option value="Masters Year 2" {{ old('current_year') === 'Masters Year 2' ? 'selected' : '' }}>Masters Year 2</option>
                    <option value="PhD Year 1" {{ old('current_year') === 'PhD Year 1' ? 'selected' : '' }}>PhD Year 1</option>
                    <option value="PhD Year 2" {{ old('current_year') === 'PhD Year 2' ? 'selected' : '' }}>PhD Year 2</option>
                    <option value="PhD Year 3+" {{ old('current_year') === 'PhD Year 3+' ? 'selected' : '' }}>PhD Year 3+</option>
                </select>
                @error('current_year')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="current_gpa" class="block text-sm font-medium text-gray-700 mb-2">
                    Current GPA/CGPA (Optional)
                </label>
                <input
                    type="number"
                    id="current_gpa"
                    name="current_gpa"
                    value="{{ old('current_gpa') }}"
                    min="0"
                    max="5"
                    step="0.01"
                    placeholder="e.g., 4.50"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('current_gpa') border-red-500 @enderror"
                >
                @error('current_gpa')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="expected_graduation" class="block text-sm font-medium text-gray-700 mb-2">
                    Expected Graduation Date (Optional)
                </label>
                <input
                    type="date"
                    id="expected_graduation"
                    name="expected_graduation"
                    value="{{ old('expected_graduation') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('expected_graduation') border-red-500 @enderror"
                >
                @error('expected_graduation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <!-- Application Essays -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Application Essays
        </h3>

        <div class="space-y-6">
            <div>
                <label for="career_goals" class="block text-sm font-medium text-gray-700 mb-2">
                    Career Goals and Aspirations <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="career_goals"
                    name="career_goals"
                    rows="5"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('career_goals') border-red-500 @enderror"
                    placeholder="Describe your career goals, what you want to achieve after graduation, and how this scholarship will help you reach your goals..."
                    required
                >{{ old('career_goals') }}</textarea>
                @error('career_goals')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Minimum 50 characters, maximum 1000 characters</p>
            </div>

            <div>
                <label for="academic_achievements" class="block text-sm font-medium text-gray-700 mb-2">
                    Academic Achievements (Optional)
                </label>
                <textarea
                    id="academic_achievements"
                    name="academic_achievements"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('academic_achievements') border-red-500 @enderror"
                    placeholder="List your academic achievements, awards, honors, publications, research work, etc..."
                >{{ old('academic_achievements') }}</textarea>
                @error('academic_achievements')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="extracurricular_activities" class="block text-sm font-medium text-gray-700 mb-2">
                    Extracurricular Activities (Optional)
                </label>
                <textarea
                    id="extracurricular_activities"
                    name="extracurricular_activities"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('extracurricular_activities') border-red-500 @enderror"
                    placeholder="Sports, clubs, volunteer work, leadership roles, community service, etc..."
                >{{ old('extracurricular_activities') }}</textarea>
                @error('extracurricular_activities')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="financial_need_explanation" class="block text-sm font-medium text-gray-700 mb-2">
                    Financial Need Statement <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="financial_need_explanation"
                    name="financial_need_explanation"
                    rows="5"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('financial_need_explanation') border-red-500 @enderror"
                    placeholder="Explain your financial situation, family background, and why you need this scholarship to continue your education..."
                    required
                >{{ old('financial_need_explanation') }}</textarea>
                @error('financial_need_explanation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Minimum 50 characters, maximum 1000 characters</p>
            </div>

            <div>
                <label for="how_scholarship_helps" class="block text-sm font-medium text-gray-700 mb-2">
                    How This Scholarship Will Help You <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="how_scholarship_helps"
                    name="how_scholarship_helps"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('how_scholarship_helps') border-red-500 @enderror"
                    placeholder="Explain how receiving this scholarship will impact your education and future goals..."
                    required
                >{{ old('how_scholarship_helps') }}</textarea>
                @error('how_scholarship_helps')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Minimum 50 characters, maximum 1000 characters</p>
            </div>
        </div>
    </div>

    <!-- References -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            References
        </h3>

        <div class="space-y-8">
            <!-- Reference 1 (Required) -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Reference 1 (Required)</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="reference1_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="reference1_name"
                            name="reference1_name"
                            value="{{ old('reference1_name') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference1_name') border-red-500 @enderror"
                            required
                        >
                        @error('reference1_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference1_position" class="block text-sm font-medium text-gray-700 mb-2">
                            Position/Title <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="reference1_position"
                            name="reference1_position"
                            value="{{ old('reference1_position') }}"
                            placeholder="e.g., Professor, Lecturer, HOD"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference1_position') border-red-500 @enderror"
                            required
                        >
                        @error('reference1_position')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference1_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="email"
                            id="reference1_email"
                            name="reference1_email"
                            value="{{ old('reference1_email') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference1_email') border-red-500 @enderror"
                            required
                        >
                        @error('reference1_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference1_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="tel"
                            id="reference1_phone"
                            name="reference1_phone"
                            value="{{ old('reference1_phone') }}"
                            placeholder="e.g., 08012345678"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference1_phone') border-red-500 @enderror"
                            required
                        >
                        @error('reference1_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Reference 2 (Optional) -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Reference 2 (Optional)</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="reference2_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Name
                        </label>
                        <input
                            type="text"
                            id="reference2_name"
                            name="reference2_name"
                            value="{{ old('reference2_name') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference2_name') border-red-500 @enderror"
                        >
                        @error('reference2_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference2_position" class="block text-sm font-medium text-gray-700 mb-2">
                            Position/Title
                        </label>
                        <input
                            type="text"
                            id="reference2_position"
                            name="reference2_position"
                            value="{{ old('reference2_position') }}"
                            placeholder="e.g., Professor, Lecturer, HOD"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference2_position') border-red-500 @enderror"
                        >
                        @error('reference2_position')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference2_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input
                            type="email"
                            id="reference2_email"
                            name="reference2_email"
                            value="{{ old('reference2_email') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference2_email') border-red-500 @enderror"
                        >
                        @error('reference2_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reference2_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input
                            type="tel"
                            id="reference2_phone"
                            name="reference2_phone"
                            value="{{ old('reference2_phone') }}"
                            placeholder="e.g., 08012345678"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('reference2_phone') border-red-500 @enderror"
                        >
                        @error('reference2_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Uploads -->
    <div class="border-t pt-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Required Documents
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="student_photo" class="block text-sm font-medium text-gray-700 mb-2">
                    Student Photo <span class="text-red-500">*</span>
                </label>
                <input
                    type="file"
                    id="student_photo"
                    name="student_photo"
                    accept="image/jpeg,image/png,image/jpg"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_photo') border-red-500 @enderror"
                    required
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('student_photo')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="student_id_card" class="block text-sm font-medium text-gray-700 mb-2">
                    Student ID Card <span class="text-red-500">*</span>
                </label>
                <input
                    type="file"
                    id="student_id_card"
                    name="student_id_card"
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('student_id_card') border-red-500 @enderror"
                    required
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('student_id_card')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="academic_transcript" class="block text-sm font-medium text-gray-700 mb-2">
                    Academic Transcript <span class="text-red-500">*</span>
                </label>
                <input
                    type="file"
                    id="academic_transcript"
                    name="academic_transcript"
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('academic_transcript') border-red-500 @enderror"
                    required
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('academic_transcript')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="recommendation_letter" class="block text-sm font-medium text-gray-700 mb-2">
                    Recommendation Letter (Optional)
                </label>
                <input
                    type="file"
                    id="recommendation_letter"
                    name="recommendation_letter"
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('recommendation_letter') border-red-500 @enderror"
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 2MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('recommendation_letter')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="md:col-span-2">
                <label for="additional_documents" class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Documents (Optional)
                </label>
                <input
                    type="file"
                    id="additional_documents"
                    name="additional_documents"
                    accept="image/jpeg,image/png,image/jpg,application/pdf"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('additional_documents') border-red-500 @enderror"
                >
                <p class="mt-1 text-xs text-gray-500">JPG, PNG, PDF only. Max 5MB</p>
                <div class="file-preview hidden mt-2 text-sm text-green-600"></div>
                @error('additional_documents')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>
</div>