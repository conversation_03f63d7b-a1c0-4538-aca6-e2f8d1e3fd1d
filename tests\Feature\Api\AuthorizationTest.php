<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Volunteer;
use App\Models\Event;
use App\Models\Program;
use App\Models\ScholarshipApplication;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AuthorizationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test admin can access all endpoints
     */
    public function test_admin_can_access_all_endpoints()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $token = $admin->createToken('test-token')->plainTextToken;

        $endpoints = [
            '/api/v1/admin/dashboard',
            '/api/v1/admin/users',
            '/api/v1/admin/scholarships'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            // Should not be 403 (forbidden)
            $this->assertNotEquals(403, $response->getStatusCode(), 
                "Admin should have access to {$endpoint}");
        }
    }

    /**
     * Test partner organization role restrictions
     */
    public function test_partner_organization_role_restrictions()
    {
        $partner = User::factory()->create(['role' => 'partner_organization']);
        $token = $partner->createToken('test-token')->plainTextToken;

        // Should have access to partner-specific endpoints
        $allowedEndpoints = [
            '/api/v1/partner-scholarships/available',
        ];

        foreach ($allowedEndpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            $this->assertNotEquals(403, $response->getStatusCode(),
                "Partner organization should have access to {$endpoint}");
        }

        // Should not have access to admin endpoints
        $forbiddenEndpoints = [
            '/api/v1/admin/dashboard',
            '/api/v1/admin/users'
        ];

        foreach ($forbiddenEndpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            $response->assertStatus(403);
        }
    }

    /**
     * Test student role restrictions
     */
    public function test_student_role_restrictions()
    {
        $student = User::factory()->create(['role' => 'student']);
        $token = $student->createToken('test-token')->plainTextToken;

        // Should have access to student-specific endpoints
        $allowedEndpoints = [
            '/api/v1/student-scholarships/available',
            '/api/v1/programs'
        ];

        foreach ($allowedEndpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            $this->assertNotEquals(403, $response->getStatusCode(),
                "Student should have access to {$endpoint}");
        }

        // Should not have access to admin or partner endpoints
        $forbiddenEndpoints = [
            '/api/v1/admin/dashboard',
            '/api/v1/partner-scholarships/available'
        ];

        foreach ($forbiddenEndpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            $response->assertStatus(403);
        }
    }

    /**
     * Test volunteer role restrictions
     */
    public function test_volunteer_role_restrictions()
    {
        $volunteer = User::factory()->create(['role' => 'volunteer']);
        $token = $volunteer->createToken('test-token')->plainTextToken;

        // Should have access to volunteer-specific endpoints
        $allowedEndpoints = [
            '/api/v1/volunteer/opportunities/matched',
        ];

        foreach ($allowedEndpoints as $endpoint) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json'
            ])->getJson($endpoint);

            $this->assertNotEquals(403, $response->getStatusCode(),
                "Volunteer should have access to {$endpoint}");
        }
    }

    /**
     * Test data ownership validation
     */
    public function test_data_ownership_validation()
    {
        $user1 = User::factory()->create(['role' => 'student']);
        $user2 = User::factory()->create(['role' => 'student']);
        
        $token1 = $user1->createToken('test-token')->plainTextToken;
        $token2 = $user2->createToken('test-token')->plainTextToken;

        // Create a scholarship application for user1
        $application = ScholarshipApplication::factory()->create([
            'user_id' => $user1->id
        ]);

        // User1 should be able to access their own application
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token1,
            'Accept' => 'application/json'
        ])->getJson("/api/v1/student-scholarships/applications/{$application->id}");

        $this->assertNotEquals(403, $response->getStatusCode());

        // User2 should not be able to access user1's application
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token2,
            'Accept' => 'application/json'
        ])->getJson("/api/v1/student-scholarships/applications/{$application->id}");

        $response->assertStatus(403);
    }

    /**
     * Test policy-based authorization for volunteers
     */
    public function test_volunteer_policy_authorization()
    {
        $volunteer = User::factory()->create(['role' => 'volunteer']);
        $otherUser = User::factory()->create(['role' => 'student']);
        
        $volunteerToken = $volunteer->createToken('test-token')->plainTextToken;
        $otherToken = $otherUser->createToken('test-token')->plainTextToken;

        // Create volunteer record
        $volunteerRecord = Volunteer::factory()->create([
            'user_id' => $volunteer->id
        ]);

        // Volunteer should be able to access their own data
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $volunteerToken,
            'Accept' => 'application/json'
        ])->getJson('/api/v1/volunteer/hours');

        $this->assertNotEquals(403, $response->getStatusCode());

        // Other user should not be able to access volunteer data
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $otherToken,
            'Accept' => 'application/json'
        ])->getJson('/api/v1/volunteer/hours');

        $response->assertStatus(403);
    }

    /**
     * Test event access based on visibility
     */
    public function test_event_access_based_on_visibility()
    {
        $user = User::factory()->create(['role' => 'student']);
        $token = $user->createToken('test-token')->plainTextToken;

        // Create public and private events
        $publicEvent = Event::factory()->create([
            'is_public' => true,
            'status' => 'active'
        ]);

        $privateEvent = Event::factory()->create([
            'is_public' => false,
            'status' => 'active'
        ]);

        // User should be able to view public events
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->getJson("/api/v1/events/{$publicEvent->id}");

        $this->assertNotEquals(403, $response->getStatusCode());

        // User should not be able to view private events (unless specifically invited)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->getJson("/api/v1/events/{$privateEvent->id}");

        // This might return 403 or 404 depending on implementation
        $this->assertContains($response->getStatusCode(), [403, 404]);
    }

    /**
     * Test program enrollment authorization
     */
    public function test_program_enrollment_authorization()
    {
        $student = User::factory()->create(['role' => 'student']);
        $volunteer = User::factory()->create(['role' => 'volunteer']);
        
        $studentToken = $student->createToken('test-token')->plainTextToken;
        $volunteerToken = $volunteer->createToken('test-token')->plainTextToken;

        $program = Program::factory()->create([
            'is_active' => true,
            'target_roles' => json_encode(['student', 'user'])
        ]);

        // Student should be able to enroll
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $studentToken,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/programs/{$program->id}/enroll");

        $this->assertNotEquals(403, $response->getStatusCode());

        // Volunteer should not be able to enroll (not in target roles)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $volunteerToken,
            'Accept' => 'application/json'
        ])->postJson("/api/v1/programs/{$program->id}/enroll");

        $response->assertStatus(403);
    }

    /**
     * Test middleware chain execution
     */
    public function test_middleware_chain_execution()
    {
        $user = User::factory()->create(['role' => 'student']);
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->getJson('/api/v1/user');

        // Check that security headers are applied
        $response->assertHeader('X-Content-Type-Options', 'nosniff')
                ->assertHeader('X-API-Version', 'v1');

        // Check that rate limit headers are present
        $this->assertTrue($response->headers->has('X-RateLimit-Limit'));
    }
}
