"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, CheckCircle2, AlertCircle, Loader2, FileText } from "lucide-react"
import { apiClient } from "@/lib/api"
import { FILE_UPLOAD } from "@/lib/constants"

interface BankTransferReceiptProps {
  donationId: number
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function BankTransferReceipt({ donationId, onSuccess, onError }: BankTransferReceiptProps) {
  const [formData, setFormData] = useState({
    sender_name: '',
    sender_bank: '',
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!FILE_UPLOAD.ALLOWED_TYPES.includes(file.type)) {
      setUploadError('Please select a valid file (JPG, PNG, or PDF)')
      return
    }

    // Validate file size
    if (file.size > FILE_UPLOAD.MAX_SIZE) {
      setUploadError(`File size must be less than ${FILE_UPLOAD.MAX_SIZE / (1024 * 1024)}MB`)
      return
    }

    setSelectedFile(file)
    setUploadError(null)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedFile) {
      setUploadError('Please select a receipt file')
      return
    }

    if (!formData.sender_name || !formData.sender_bank) {
      setUploadError('Please fill in all required fields')
      return
    }

    setIsUploading(true)
    setUploadError(null)

    try {
      const uploadFormData = new FormData()
      uploadFormData.append('receipt', selectedFile)
      uploadFormData.append('sender_name', formData.sender_name)
      uploadFormData.append('sender_bank', formData.sender_bank)

      const response = await apiClient.uploadDonationReceipt(donationId, uploadFormData)

      if (response.success) {
        setUploadSuccess(true)
        onSuccess?.()
      } else {
        setUploadError(response.message || 'Failed to upload receipt')
        onError?.(response.message || 'Failed to upload receipt')
      }
    } catch (error) {
      console.error('Receipt upload error:', error)
      const errorMessage = 'Failed to upload receipt. Please try again.'
      setUploadError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  if (uploadSuccess) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-6 text-center">
          <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-700 mb-2">Receipt Uploaded Successfully!</h3>
          <p className="text-green-600 text-sm mb-4">
            Your receipt has been uploaded and is now pending admin verification. 
            You will receive an email confirmation once your donation is approved.
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-green-600 hover:bg-green-700"
          >
            Back to Home
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Bank Transfer Receipt
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Please upload your bank transfer receipt and provide the required details for verification.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {uploadError && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-600">
                {uploadError}
              </AlertDescription>
            </Alert>
          )}

          {/* Sender Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sender-name">Sender Name <span className="text-red-500">*</span></Label>
              <Input
                id="sender-name"
                placeholder="Name on the bank account"
                value={formData.sender_name}
                onChange={(e) => handleInputChange('sender_name', e.target.value)}
                className="mt-1"
                required
              />
            </div>
            <div>
              <Label htmlFor="sender-bank">Sender Bank <span className="text-red-500">*</span></Label>
              <Input
                id="sender-bank"
                placeholder="Bank used for transfer"
                value={formData.sender_bank}
                onChange={(e) => handleInputChange('sender_bank', e.target.value)}
                className="mt-1"
                required
              />
            </div>
          </div>

          {/* File Upload */}
          <div>
            <Label>Receipt File <span className="text-red-500">*</span></Label>
            <div
              className={`mt-2 border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive
                  ? 'border-green-400 bg-green-50'
                  : selectedFile
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                accept=".jpg,.jpeg,.png,.pdf"
                onChange={handleFileChange}
                className="hidden"
                id="receipt-upload"
              />
              
              {selectedFile ? (
                <div className="space-y-2">
                  <FileText className="h-8 w-8 text-green-500 mx-auto" />
                  <p className="text-sm font-medium text-green-700">{selectedFile.name}</p>
                  <p className="text-xs text-green-600">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById('receipt-upload')?.click()}
                  >
                    Change File
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-sm text-gray-600">
                      <label htmlFor="receipt-upload" className="cursor-pointer text-green-600 hover:text-green-500">
                        Click to upload
                      </label>{' '}
                      or drag and drop
                    </p>
                    <p className="text-xs text-gray-500">JPG, PNG or PDF (max 5MB)</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isUploading || !selectedFile || !formData.sender_name || !formData.sender_bank}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading Receipt...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Receipt
              </>
            )}
          </Button>

          <p className="text-xs text-muted-foreground text-center">
            Your receipt will be reviewed by our admin team. You will receive an email confirmation once verified.
          </p>
        </form>
      </CardContent>
    </Card>
  )
}
