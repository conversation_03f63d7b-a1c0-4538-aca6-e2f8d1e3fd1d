{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' 'inline-speculation-rules' https: chrome-extension: chrome-extension://9bb310a9-2a7c-4c5f-8b3f-efa4c117c7d2/; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: blob: https:; font-src 'self' data: https:; connect-src 'self' http://localhost:8000 http://127.0.0.1:8000 https:; frame-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/blog/[slug]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/scholarships/[id]", "regex": "^/scholarships/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/scholarships/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/admin/scholarships", "regex": "^/admin/scholarships(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/scholarships(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/register", "regex": "^/auth/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/register(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/demo-scholarship", "regex": "^/demo\\-scholarship(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo\\-scholarship(?:/)?$"}, {"page": "/donate", "regex": "^/donate(?:/)?$", "routeKeys": {}, "namedRegex": "^/donate(?:/)?$"}, {"page": "/impact", "regex": "^/impact(?:/)?$", "routeKeys": {}, "namedRegex": "^/impact(?:/)?$"}, {"page": "/learn-with-us", "regex": "^/learn\\-with\\-us(?:/)?$", "routeKeys": {}, "namedRegex": "^/learn\\-with\\-us(?:/)?$"}, {"page": "/projects", "regex": "^/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/projects(?:/)?$"}, {"page": "/scholarships", "regex": "^/scholarships(?:/)?$", "routeKeys": {}, "namedRegex": "^/scholarships(?:/)?$"}, {"page": "/team", "regex": "^/team(?:/)?$", "routeKeys": {}, "namedRegex": "^/team(?:/)?$"}, {"page": "/test-api", "regex": "^/test\\-api(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-api(?:/)?$"}, {"page": "/test-images", "regex": "^/test\\-images(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-images(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}