<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create partner_organizations table (only if it doesn't exist)
        if (!Schema::hasTable('partner_organizations')) {
            Schema::create('partner_organizations', function (Blueprint $table) {
            $table->id();
            $table->string('organization_id', 20)->unique(); // HLTKKQ-ORG-0001
            $table->string('name', 200);
            $table->string('slug', 220)->unique();
            $table->enum('type', ['primary_school', 'secondary_school', 'university', 'ngo', 'government', 'private_sector', 'other'])->default('primary_school');
            $table->text('description')->nullable();
            
            // Contact Information
            $table->string('email', 100);
            $table->string('phone', 20);
            $table->string('alternative_phone', 20)->nullable();
            $table->text('address');
            $table->string('city', 100);
            $table->string('state', 100);
            $table->string('country', 100)->default('Nigeria');
            $table->string('postal_code', 20)->nullable();
            
            // Institution Specific Information
            $table->string('registration_number', 100)->nullable(); // Government registration number
            $table->string('license_number', 100)->nullable(); // Educational license number
            $table->date('established_date')->nullable();
            $table->integer('student_capacity')->nullable();
            $table->integer('current_enrollment')->default(0);
            
            // Educational Levels Offered (for schools)
            $table->json('education_levels')->nullable(); // ['primary', 'secondary', 'university']
            $table->json('grade_levels')->nullable(); // ['primary_1', 'primary_2', ..., 'secondary_6']
            
            // Contact Person Information
            $table->string('contact_person_name', 200);
            $table->string('contact_person_title', 100); // Principal, Director, etc.
            $table->string('contact_person_phone', 20);
            $table->string('contact_person_email', 100);
            
            // Principal/Head Information (for schools)
            $table->string('principal_name', 200)->nullable();
            $table->string('principal_phone', 20)->nullable();
            $table->string('principal_email', 100)->nullable();
            $table->string('principal_qualification', 200)->nullable();
            $table->integer('principal_experience_years')->nullable();
            
            // Verification and Status
            $table->enum('verification_status', ['pending', 'verified', 'rejected', 'suspended'])->default('pending');
            $table->timestamp('verified_at')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('verification_notes')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended', 'closed'])->default('active');
            
            // Partnership Information
            $table->date('partnership_start_date')->nullable();
            $table->date('partnership_end_date')->nullable();
            $table->enum('partnership_status', ['active', 'inactive', 'terminated', 'pending'])->default('pending');
            $table->text('partnership_terms')->nullable();
            
            // Documents and Credentials
            $table->json('documents')->nullable(); // Store document file paths
            $table->json('credentials')->nullable(); // Store credential information
            $table->json('certifications')->nullable(); // Educational certifications
            
            // Performance Metrics
            $table->decimal('success_rate', 5, 2)->default(0.00); // Scholarship success rate
            $table->integer('total_applications')->default(0);
            $table->integer('approved_applications')->default(0);
            $table->decimal('average_student_performance', 5, 2)->nullable();
            
            // Additional Information
            $table->json('facilities')->nullable(); // School facilities
            $table->json('programs_offered')->nullable(); // Academic programs
            $table->json('extracurricular_activities')->nullable();
            $table->text('mission_statement')->nullable();
            $table->text('vision_statement')->nullable();
            $table->json('additional_info')->nullable();
            
            // Tracking
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('last_activity_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['organization_id']);
            $table->index(['type', 'status']);
            $table->index(['verification_status', 'partnership_status'], 'partner_org_status_idx');
            $table->index(['city', 'state']);
            $table->index(['created_at', 'status']);
            });
        }

        // Add partner_organization_id to users table for linking institutional accounts
        Schema::table('users', function (Blueprint $table) {
            // First, update the role enum to include partner_organization
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'volunteer', 'donor', 'user', 'partner_organization') DEFAULT 'user'");

            if (!Schema::hasColumn('users', 'partner_organization_id')) {
                $table->foreignId('partner_organization_id')->nullable()->constrained('partner_organizations')->onDelete('set null')->after('role');
            }

            // Add institutional_role for users representing organizations
            if (!Schema::hasColumn('users', 'institutional_role')) {
                $table->string('institutional_role', 100)->nullable()->after('partner_organization_id'); // principal, admin, teacher, etc.
            }

            // Add verification status for institutional users
            if (!Schema::hasColumn('users', 'institutional_verification_status')) {
                $table->enum('institutional_verification_status', ['pending', 'verified', 'rejected'])->nullable()->after('institutional_role');
            }
        });

        // Create partner_organization_users pivot table for multiple users per organization (only if it doesn't exist)
        if (!Schema::hasTable('partner_organization_users')) {
            Schema::create('partner_organization_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('partner_organization_id')->constrained('partner_organizations')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('role', 100); // principal, admin, teacher, staff
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->json('permissions')->nullable(); // Specific permissions for this user
            $table->text('notes')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['partner_organization_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->unique(['partner_organization_id', 'user_id', 'role'], 'partner_org_user_unique'); // Prevent duplicate roles per user per org
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partner_organization_users');
        
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'institutional_verification_status')) {
                $table->dropColumn('institutional_verification_status');
            }
            if (Schema::hasColumn('users', 'institutional_role')) {
                $table->dropColumn('institutional_role');
            }
            if (Schema::hasColumn('users', 'partner_organization_id')) {
                $table->dropColumn('partner_organization_id');
            }
        });
        
        Schema::dropIfExists('partner_organizations');
    }
};
