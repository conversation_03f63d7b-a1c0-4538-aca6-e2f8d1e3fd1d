

<?php $__env->startSection('title', 'Donations Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Donations Management</h1>
        <div>
            <a href="<?php echo e(route('admin.donations.campaigns')); ?>" class="btn btn-info me-2">
                <i class="fas fa-bullhorn me-2"></i>Campaigns
            </a>
            <a href="<?php echo e(route('admin.donations.analytics')); ?>" class="btn btn-success">
                <i class="fas fa-chart-line me-2"></i>Analytics
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Donations</div>
                            <div class="h4">₦<?php echo e(number_format($donations->sum('amount'), 2)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-hand-holding-heart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Donors</div>
                            <div class="h4"><?php echo e($donations->where('donor_id', '!=', null)->unique('donor_id')->count()); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">This Month</div>
                            <div class="h4">₦<?php echo e(number_format($donations->filter(function($donation) { return $donation->created_at->month === now()->month; })->sum('amount'), 0)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Avg. Donation</div>
                            <div class="h4">₦<?php echo e(number_format($donations->avg('amount'), 0)); ?></div>
                        </div>
                        <div>
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Donations Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Donations</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search donations..." id="searchDonations">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                        <option value="refunded">Refunded</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterType">
                        <option value="">All Types</option>
                        <option value="one-time">One-time</option>
                        <option value="recurring">Recurring</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" id="filterDate" title="Filter by date">
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterAmount">
                        <option value="">All Amounts</option>
                        <option value="0-1000">₦0 - ₦1,000</option>
                        <option value="1000-10000">₦1,000 - ₦10,000</option>
                        <option value="10000-50000">₦10,000 - ₦50,000</option>
                        <option value="50000+">₦50,000+</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Donor</th>
                            <th>Amount</th>
                            <th>Purpose</th>
                            <th>Type</th>
                            <th>Payment Method</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $donations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($donation->donor && $donation->donor->profile_picture): ?>
                                        <img src="<?php echo e($donation->donor->profile_picture); ?>" alt="<?php echo e($donation->donor->first_name); ?>" class="rounded-circle me-3" width="40" height="40">
                                    <?php else: ?>
                                        <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <?php echo e($donation->donor ? strtoupper(substr($donation->donor->first_name, 0, 1)) : strtoupper(substr($donation->donor_name ?: 'A', 0, 1))); ?>

                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <?php if($donation->is_anonymous): ?>
                                            <div class="fw-bold text-muted">Anonymous Donor</div>
                                            <small class="text-muted">Identity hidden</small>
                                        <?php else: ?>
                                            <div class="fw-bold">
                                                <?php echo e($donation->donor ? $donation->donor->first_name . ' ' . $donation->donor->last_name : $donation->donor_name); ?>

                                            </div>
                                            <small class="text-muted"><?php echo e($donation->donor ? $donation->donor->email : $donation->donor_email); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="text-success h5">₦<?php echo e(number_format($donation->amount, 2)); ?></strong>
                                    <?php if($donation->currency !== 'NGN'): ?>
                                        <br><small class="text-muted"><?php echo e($donation->currency); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if($donation->purpose): ?>
                                    <span class="badge bg-primary"><?php echo e($donation->purpose); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">General</span>
                                <?php endif; ?>
                                <?php if($donation->campaign): ?>
                                    <br><small class="text-muted"><?php echo e($donation->campaign->name); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($donation->donation_type === 'recurring' ? 'info' : 'secondary'); ?>">
                                    <?php echo e(ucfirst($donation->donation_type)); ?>

                                </span>
                                <?php if($donation->donation_type === 'recurring' && $donation->frequency): ?>
                                    <br><small class="text-muted"><?php echo e(ucfirst($donation->frequency)); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($donation->payment_method): ?>
                                    <div class="small">
                                        <strong><?php echo e(ucfirst($donation->payment_method)); ?></strong>
                                        <?php if($donation->payment_provider): ?>
                                            <br><span class="text-muted">via <?php echo e(ucfirst($donation->payment_provider)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Not specified</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($donation->payment_status === 'completed' ? 'success' : 
                                    ($donation->payment_status === 'pending' ? 'warning' : 
                                    ($donation->payment_status === 'failed' ? 'danger' : 'info'))); ?>">
                                    <?php echo e(ucfirst($donation->payment_status)); ?>

                                </span>
                                <?php if($donation->receipt_number): ?>
                                    <br><small class="text-muted">Receipt: <?php echo e($donation->receipt_number); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo e($donation->created_at->format('M d, Y')); ?>

                                <br><small class="text-muted"><?php echo e($donation->created_at->format('g:i A')); ?></small>
                                <br><small class="text-muted"><?php echo e($donation->created_at->diffForHumans()); ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewDonation(<?php echo e($donation->id); ?>)" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if($donation->receipt_number): ?>
                                        <a href="<?php echo e(route('admin.donations.receipt', $donation->id)); ?>" class="btn btn-sm btn-outline-success" title="Download Receipt" target="_blank">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-success" onclick="generateReceipt(<?php echo e($donation->id); ?>)" title="Generate Receipt">
                                            <i class="fas fa-receipt"></i>
                                        </button>
                                    <?php endif; ?>
                                    <?php if($donation->payment_status === 'completed'): ?>
                                        <button class="btn btn-sm btn-outline-warning" onclick="refundDonation(<?php echo e($donation->id); ?>)" title="Refund">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-sm btn-outline-info" onclick="sendThankYou(<?php echo e($donation->id); ?>)" title="Send Thank You">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-hand-holding-heart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No donations found</p>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing <?php echo e($donations->firstItem()); ?> to <?php echo e($donations->lastItem()); ?> of <?php echo e($donations->total()); ?> results
                </div>
                <?php echo e($donations->links()); ?>

            </div>
        </div>
    </div>
</div>

<!-- Donation Details Modal -->
<div class="modal fade" id="donationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Donation Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="donationDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Refund Confirmation Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Refund Donation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to refund this donation?</p>
                <div class="mb-3">
                    <label for="refundReason" class="form-label">Reason for refund:</label>
                    <textarea class="form-control" id="refundReason" rows="3" placeholder="Provide a reason for the refund..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmRefund">Process Refund</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewDonation(donationId) {
    const modal = new bootstrap.Modal(document.getElementById('donationDetailsModal'));
    
    // Mock donation details - replace with actual AJAX call
    const detailsContent = `
        <div class="row">
            <div class="col-md-6">
                <h6>Donation Information</h6>
                <p><strong>Amount:</strong> ₦5,000.00</p>
                <p><strong>Type:</strong> One-time</p>
                <p><strong>Purpose:</strong> Education</p>
                <p><strong>Transaction ID:</strong> TXN123456789</p>
            </div>
            <div class="col-md-6">
                <h6>Donor Information</h6>
                <p><strong>Name:</strong> John Doe</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +234 ************</p>
                <p><strong>Date:</strong> Dec 15, 2024</p>
            </div>
        </div>
    `;
    
    document.getElementById('donationDetailsContent').innerHTML = detailsContent;
    modal.show();
}

function generateReceipt(donationId) {
    console.log('Generate receipt for donation:', donationId);
    // Add AJAX call to generate receipt
}

function refundDonation(donationId) {
    const modal = new bootstrap.Modal(document.getElementById('refundModal'));
    modal.show();
    
    document.getElementById('confirmRefund').onclick = function() {
        const reason = document.getElementById('refundReason').value;
        console.log('Refund donation:', donationId, 'Reason:', reason);
        modal.hide();
        // Add AJAX call to process refund
    };
}

function sendThankYou(donationId) {
    console.log('Send thank you for donation:', donationId);
    // Add AJAX call to send thank you email
}

// Search functionality
document.getElementById('searchDonations').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterType').addEventListener('change', function() {
    console.log('Filter type:', this.value);
});

document.getElementById('filterDate').addEventListener('change', function() {
    console.log('Filter date:', this.value);
});

document.getElementById('filterAmount').addEventListener('change', function() {
    console.log('Filter amount:', this.value);
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/donations/index.blade.php ENDPATH**/ ?>