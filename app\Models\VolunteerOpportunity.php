<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteerOpportunity extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category',
        'required_skills',
        'time_commitment',
        'location',
        'application_deadline',
        'start_date',
        'end_date',
        'max_volunteers',
        'is_active',
        'is_featured',
        'created_by'
    ];

    protected $casts = [
        'required_skills' => 'array',
        'application_deadline' => 'datetime',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'is_featured' => 'boolean'
    ];

    /**
     * Get the user who created this opportunity
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get applications for this opportunity
     */
    public function applications()
    {
        return $this->hasMany(VolunteerOpportunityApplication::class, 'opportunity_id');
    }

    /**
     * Scope for active opportunities
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured opportunities
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for opportunities with open applications
     */
    public function scopeOpenForApplications($query)
    {
        return $query->where('application_deadline', '>', now());
    }

    /**
     * Check if applications are still open
     */
    public function isOpenForApplications()
    {
        return $this->application_deadline > now();
    }

    /**
     * Get number of available spots
     */
    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_volunteers) {
            return null;
        }

        $acceptedApplications = $this->applications()
            ->where('status', 'accepted')
            ->count();

        return max(0, $this->max_volunteers - $acceptedApplications);
    }

    /**
     * Check if opportunity is full
     */
    public function isFull()
    {
        if (!$this->max_volunteers) {
            return false;
        }

        return $this->available_spots <= 0;
    }

    /**
     * Get category badge color
     */
    public function getCategoryColorAttribute()
    {
        $colors = [
            'education' => 'blue',
            'health' => 'green',
            'environment' => 'emerald',
            'community' => 'purple',
            'events' => 'orange',
            'administration' => 'gray',
            'fundraising' => 'red'
        ];

        return $colors[$this->category] ?? 'gray';
    }

    /**
     * Get time commitment badge
     */
    public function getTimeCommitmentBadgeAttribute()
    {
        $badges = [
            'one-time' => 'One Time',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'flexible' => 'Flexible',
            'ongoing' => 'Ongoing'
        ];

        return $badges[$this->time_commitment] ?? $this->time_commitment;
    }
}
