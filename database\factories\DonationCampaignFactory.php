<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\DonationCampaign;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DonationCampaign>
 */
class DonationCampaignFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DonationCampaign::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'slug' => $this->faker->slug(),
            'description' => $this->faker->paragraphs(3, true),
            'goal_amount' => $this->faker->numberBetween(50000, 500000),
            'raised_amount' => $this->faker->numberBetween(0, 25000),
            'currency' => 'NGN',
            'start_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'end_date' => $this->faker->dateTimeBetween('now', '+6 months'),
            'status' => $this->faker->randomElement(['draft', 'active', 'completed', 'cancelled']),
            'impact_description' => $this->faker->paragraph(),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the campaign is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'start_date' => now()->subDays(1),
            'end_date' => now()->addDays(30),
        ]);
    }

    /**
     * Indicate that the campaign is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'end_date' => now()->subDays(1),
        ]);
    }

    /**
     * Indicate that the campaign has reached its goal.
     */
    public function goalReached(): static
    {
        return $this->state(function (array $attributes) {
            $goalAmount = $attributes['goal_amount'] ?? 100000;
            return [
                'raised_amount' => $goalAmount,
            ];
        });
    }
}
