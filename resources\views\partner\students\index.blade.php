@extends('layouts.dashboard')

@section('title', 'Manage Students')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner/students')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner/students')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Manage Students</h1>
                            <p class="text-green-100 text-sm lg:text-base">Add, edit, and manage your organization's students</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="{{ route('partner.students.create') }}" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-plus mr-2"></i>
                                Add New Student
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Total Students</p>
                                <p class="text-xl font-bold text-green-600">{{ $stats['total_students'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-check text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Active</p>
                                <p class="text-xl font-bold text-blue-600">{{ $stats['active_students'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Graduated</p>
                                <p class="text-xl font-bold text-amber-600">{{ $stats['graduated_students'] }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Applications</p>
                                <p class="text-xl font-bold text-purple-600">{{ $stats['applications_submitted'] }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    <input type="text" placeholder="Search students..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="flex gap-3">
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="graduated">Graduated</option>
                                </select>
                                <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="">All Classes</option>
                                    <option value="1">Class 1</option>
                                    <option value="2">Class 2</option>
                                    <option value="3">Class 3</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Students Table -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4 lg:mb-0">Students List</h2>

                                <!-- Bulk Actions -->
                                <div class="flex flex-col sm:flex-row gap-3">
                                    <form id="bulk-action-form" action="{{ route('partner.students.bulk-action') }}" method="POST" class="flex gap-3">
                                        @csrf
                                        <select name="action" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent" required>
                                            <option value="">Bulk Actions</option>
                                            <option value="activate">Activate Selected</option>
                                            <option value="deactivate">Deactivate Selected</option>
                                            <option value="graduate">Graduate Selected</option>
                                            <option value="delete">Delete Selected</option>
                                        </select>
                                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-300">
                                            Apply
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                    @if($students->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-left">
                                            <input type="checkbox" id="select-all" class="rounded border-gray-300">
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Contact</th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($students as $student)
                                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                                            <td class="px-6 py-4">
                                                <input type="checkbox" name="student_ids[]" value="{{ $student->id }}" class="student-checkbox rounded border-gray-300">
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    @if($student->photo)
                                                        <img src="{{ Storage::url($student->photo) }}" alt="{{ $student->name }}" class="w-10 h-10 rounded-full mr-3 object-cover">
                                                    @else
                                                        <div class="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                                                            <i class="fas fa-user text-gray-600"></i>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ $student->name }}</div>
                                                        <div class="text-sm text-gray-500">{{ $student->gender }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ $student->class }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ $student->age }} years</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                <div>{{ $student->parent_contact }}</div>
                                                @if($student->parent_name)
                                                    <div class="text-xs text-gray-500">{{ $student->parent_name }}</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    {{ $student->status === 'active' ? 'bg-green-100 text-green-800' : 
                                                       ($student->status === 'graduated' ? 'bg-blue-100 text-blue-800' : 
                                                        ($student->status === 'transferred' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                                                    {{ ucfirst($student->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('partner.students.show', $student) }}" class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('partner.students.edit', $student) }}" class="text-green-600 hover:text-green-900 transition-colors duration-200">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('partner.students.destroy', $student) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this student?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="px-6 py-4 border-t border-gray-200">
                            {{ $students->links() }}
                        </div>
                    @else
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-users text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No students found</h3>
                            <p class="text-gray-500 mb-6">Get started by adding your first student.</p>
                            <a href="{{ route('partner.students.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                <i class="fas fa-plus mr-2"></i>Add First Student
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.student-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Bulk action form submission
    document.getElementById('bulk-action-form').addEventListener('submit', function(e) {
        const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one student.');
            return;
        }

        const action = this.querySelector('select[name="action"]').value;
        if (!action) {
            e.preventDefault();
            alert('Please select an action.');
            return;
        }

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${selectedCheckboxes.length} selected students?`)) {
                e.preventDefault();
                return;
            }
        }

        // Add selected student IDs to form
        selectedCheckboxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'student_ids[]';
            input.value = checkbox.value;
            this.appendChild(input);
        });
    });
</script>
@endpush
@endsection
