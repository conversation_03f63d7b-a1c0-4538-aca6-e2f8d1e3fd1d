@extends('layouts.admin')

@section('title', 'Volunteer Opportunity Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Volunteer Opportunity Details</h1>
        <div>
            <a href="{{ route('admin.volunteers.opportunities.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Opportunities
            </a>
            <a href="{{ route('admin.volunteers.opportunities.edit', $opportunity->id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Opportunity
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Opportunity Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h4 class="text-primary">{{ $opportunity->title }}</h4>
                        <span class="badge bg-secondary mb-3">{{ ucfirst(str_replace('_', ' ', $opportunity->category)) }}</span>
                        @if($opportunity->is_featured)
                            <span class="badge bg-warning mb-3">Featured</span>
                        @endif
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted">Description</h6>
                        <p class="text-justify">{{ $opportunity->description }}</p>
                    </div>

                    @if($opportunity->required_skills && count($opportunity->required_skills) > 0)
                    <div class="mb-4">
                        <h6 class="text-muted">Required Skills</h6>
                        <div class="d-flex flex-wrap gap-2">
                            @foreach($opportunity->required_skills as $skill)
                                <span class="badge bg-info">{{ $skill }}</span>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <div class="mb-4">
                        <h6 class="text-muted">Time Commitment</h6>
                        <p><i class="fas fa-clock text-primary me-2"></i>{{ ucfirst(str_replace('_', ' ', $opportunity->time_commitment)) }}</p>
                    </div>

                    @if($opportunity->location)
                    <div class="mb-4">
                        <h6 class="text-muted">Location</h6>
                        <p><i class="fas fa-map-marker-alt text-danger me-2"></i>{{ $opportunity->location }}</p>
                    </div>
                    @endif

                    <!-- Timeline -->
                    <div class="mb-4">
                        <h6 class="text-muted">Timeline</h6>
                        <div class="row">
                            @if($opportunity->start_date)
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-success me-2"></i>
                                    <div>
                                        <small class="text-muted">Start Date</small>
                                        <p class="mb-0">{{ $opportunity->start_date->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @if($opportunity->end_date)
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-stop-circle text-danger me-2"></i>
                                    <div>
                                        <small class="text-muted">End Date</small>
                                        <p class="mb-0">{{ $opportunity->end_date->format('F j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                        <div class="mt-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-times text-warning me-2"></i>
                                <div>
                                    <small class="text-muted">Application Deadline</small>
                                    <p class="mb-0">{{ $opportunity->application_deadline->format('F j, Y') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Applications Card -->
            @if($opportunity->applications && $opportunity->applications->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Applications</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Applied</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($opportunity->applications->take(10) as $application)
                                <tr>
                                    <td>{{ $application->user->first_name }} {{ $application->user->last_name }}</td>
                                    <td>{{ $application->user->email }}</td>
                                    <td>{{ $application->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $application->status === 'approved' ? 'success' : ($application->status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @if($opportunity->applications->count() > 10)
                    <div class="text-center mt-3">
                        <a href="{{ route('admin.volunteers.applications') }}?opportunity={{ $opportunity->id }}" class="btn btn-outline-primary btn-sm">
                            View All {{ $opportunity->applications->count() }} Applications
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Status</h6>
                </div>
                <div class="card-body text-center">
                    <span class="badge bg-{{ $opportunity->is_active ? 'success' : 'danger' }} p-3 mb-3" style="font-size: 1.2rem;">
                        <i class="fas fa-{{ $opportunity->is_active ? 'check' : 'times' }} me-2"></i>
                        {{ $opportunity->is_active ? 'Active' : 'Inactive' }}
                    </span>
                    <p class="text-muted mb-0">Current opportunity status</p>
                </div>
            </div>

            <!-- Capacity Card -->
            @if($opportunity->max_volunteers)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Volunteer Capacity</h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-primary mb-2">{{ $opportunity->applications ? $opportunity->applications->where('status', 'approved')->count() : 0 }} / {{ $opportunity->max_volunteers }}</h3>
                    <p class="text-muted mb-0">Approved Volunteers</p>
                    <div class="progress mt-2">
                        <div class="progress-bar" role="progressbar" style="width: {{ $opportunity->max_volunteers > 0 ? (($opportunity->applications ? $opportunity->applications->where('status', 'approved')->count() : 0) / $opportunity->max_volunteers) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Opportunity Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Opportunity Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Opportunity ID</small>
                        <p class="mb-0">#{{ $opportunity->id }}</p>
                    </div>
                    @if($opportunity->slug)
                    <div class="mb-3">
                        <small class="text-muted">Slug</small>
                        <p class="mb-0">{{ $opportunity->slug }}</p>
                    </div>
                    @endif
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0">{{ $opportunity->created_at->format('F j, Y g:i A') }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0">{{ $opportunity->updated_at->format('F j, Y g:i A') }}</p>
                    </div>
                    @if($opportunity->creator)
                    <div class="mb-3">
                        <small class="text-muted">Created By</small>
                        <p class="mb-0">{{ $opportunity->creator->first_name }} {{ $opportunity->creator->last_name }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.volunteers.opportunities.edit', $opportunity->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Opportunity
                        </a>
                        @if($opportunity->applications && $opportunity->applications->count() > 0)
                        <a href="{{ route('admin.volunteers.applications') }}?opportunity={{ $opportunity->id }}" class="btn btn-outline-info">
                            <i class="fas fa-users me-2"></i>View Applications
                        </a>
                        @endif
                        @if(!$opportunity->is_active)
                        <form method="POST" action="{{ route('admin.volunteers.opportunities.update', $opportunity->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="1">
                            <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to activate this opportunity?')">
                                <i class="fas fa-play me-2"></i>Activate Opportunity
                            </button>
                        </form>
                        @endif
                        @if($opportunity->is_active)
                        <form method="POST" action="{{ route('admin.volunteers.opportunities.update', $opportunity->id) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="0">
                            <button type="submit" class="btn btn-outline-warning w-100" onclick="return confirm('Are you sure you want to deactivate this opportunity?')">
                                <i class="fas fa-pause me-2"></i>Deactivate Opportunity
                            </button>
                        </form>
                        @endif
                        <form method="POST" action="{{ route('admin.volunteers.opportunities.destroy', $opportunity->id) }}" style="display: inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this opportunity? This action cannot be undone.')">
                                <i class="fas fa-trash me-2"></i>Delete Opportunity
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
