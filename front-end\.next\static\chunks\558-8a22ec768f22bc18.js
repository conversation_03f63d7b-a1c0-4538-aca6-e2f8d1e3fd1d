"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{704:(e,t,n)=>{n.d(t,{B8:()=>M,UC:()=>E,bL:()=>I,l9:()=>N});var r=n(2115),a=n(5185),o=n(6081),l=n(9196),i=n(8905),s=n(3655),d=n(4315),c=n(5845),u=n(1285),p=n(5155),f="Tabs",[v,g]=(0,o.A)(f,[l.RG]),h=(0,l.RG)(),[y,m]=v(f),b=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:a,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:f="automatic",...v}=e,g=(0,d.jH)(i),[h,m]=(0,c.i)({prop:r,onChange:a,defaultProp:o});return(0,p.jsx)(y,{scope:n,baseId:(0,u.B)(),value:h,onValueChange:m,orientation:l,dir:g,activationMode:f,children:(0,p.jsx)(s.sG.div,{dir:g,"data-orientation":l,...v,ref:t})})});b.displayName=f;var x="TabsList",D=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...a}=e,o=m(x,n),i=h(n);return(0,p.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:r,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});D.displayName=x;var w="TabsTrigger",C=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,d=m(w,n),c=h(n),u=k(d.baseId,r),f=A(d.baseId,r),v=r===d.value;return(0,p.jsx)(l.q7,{asChild:!0,...c,focusable:!o,active:v,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;v||o||!e||d.onValueChange(r)})})})});C.displayName=w;var j="TabsContent",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,forceMount:o,children:l,...d}=e,c=m(j,n),u=k(c.baseId,a),f=A(c.baseId,a),v=a===c.value,g=r.useRef(v);return r.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||v,children:n=>{let{present:r}=n;return(0,p.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:r&&l})}})});function k(e,t){return"".concat(e,"-trigger-").concat(t)}function A(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=j;var I=b,M=D,N=C,E=R},968:(e,t,n)=>{n.d(t,{b:()=>i});var r=n(2115),a=n(3655),o=n(5155),l=r.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},2010:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},2657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>et,VY:()=>er,ZL:()=>$,bL:()=>Y,bm:()=>ea,hE:()=>en,hJ:()=>ee,l9:()=>Q});var r=n(2115),a=n(5185),o=n(6101),l=n(6081),i=n(1285),s=n(5845),d=n(9178),c=n(7900),u=n(4378),p=n(8905),f=n(3655),v=n(2293),g=n(3795),h=n(8168),y=n(9708),m=n(5155),b="Dialog",[x,D]=(0,l.A)(b),[w,C]=x(b),j=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[p=!1,f]=(0,s.i)({prop:a,defaultProp:o,onChange:l});return(0,m.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};j.displayName=b;var R="DialogTrigger",k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(R,n),i=(0,o.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...r,ref:i,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})});k.displayName=R;var A="DialogPortal",[I,M]=x(A,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,l=C(A,t);return(0,m.jsx)(I,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};N.displayName=A;var E="DialogOverlay",F=r.forwardRef((e,t)=>{let n=M(E,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=C(E,e.__scopeDialog);return o.modal?(0,m.jsx)(p.C,{present:r||o.open,children:(0,m.jsx)(O,{...a,ref:t})}):null});F.displayName=E;var O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(E,n);return(0,m.jsx)(g.A,{as:y.DX,allowPinchZoom:!0,shards:[a.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":H(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",P=r.forwardRef((e,t)=>{let n=M(_,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=C(_,e.__scopeDialog);return(0,m.jsx)(p.C,{present:r||o.open,children:o.modal?(0,m.jsx)(G,{...a,ref:t}):(0,m.jsx)(T,{...a,ref:t})})});P.displayName=_;var G=r.forwardRef((e,t)=>{let n=C(_,e.__scopeDialog),l=r.useRef(null),i=(0,o.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,h.Eq)(e)},[]),(0,m.jsx)(q,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=r.forwardRef((e,t)=>{let n=C(_,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,m.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let i=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=C(_,n),p=r.useRef(null),f=(0,o.s)(t,p);return(0,v.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(X,{titleId:u.titleId}),(0,m.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(B,n);return(0,m.jsx)(f.sG.h2,{id:a.titleId,...r,ref:t})});L.displayName=B;var V="DialogDescription",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(V,n);return(0,m.jsx)(f.sG.p,{id:a.descriptionId,...r,ref:t})});K.displayName=V;var W="DialogClose",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(W,n);return(0,m.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}z.displayName=W;var S="DialogTitleWarning",[U,Z]=(0,l.q)(S,{contentName:_,titleName:B,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=Z(S),a="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(a)},[a,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,a=Z("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},Y=j,Q=k,$=N,ee=F,et=P,en=L,er=K,ea=z},6785:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7265:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])}}]);