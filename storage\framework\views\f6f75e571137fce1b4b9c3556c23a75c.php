

<?php $__env->startSection('title', 'User Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">User Details</h1>
        <div>
            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
            <a href="<?php echo e(route('admin.users.edit', $user->id)); ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit User
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Picture Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Picture</h6>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo e($user->profile_picture ?? asset('img/default-avatar.png')); ?>" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <h4><?php echo e($user->first_name ?? 'User'); ?> <?php echo e($user->last_name ?? 'Name'); ?></h4>
                    <p class="text-muted"><?php echo e($user->role_name ?? 'Normal User'); ?></p>
                    <div class="row text-center">
                        <div class="col">
                            <span class="badge bg-<?php echo e($user->status === 'active' ? 'success' : 'danger'); ?> p-2">
                                <i class="fas fa-circle me-1" style="font-size: 8px;"></i>
                                <?php echo e(ucfirst($user->status ?? 'active')); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="sendEmail('<?php echo e($user->email); ?>')">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </button>
                        <button class="btn btn-outline-info" onclick="viewActivity(<?php echo e($user->id); ?>)">
                            <i class="fas fa-history me-2"></i>View Activity
                        </button>
                        <?php if($user->status === 'active'): ?>
                        <form method="POST" action="<?php echo e(route('admin.users.bulk-action')); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="action" value="deactivate">
                            <input type="hidden" name="selected_users[]" value="<?php echo e($user->id); ?>">
                            <button type="submit" class="btn btn-outline-warning" onclick="return confirm('Are you sure you want to deactivate this user?')">
                                <i class="fas fa-ban me-2"></i>Deactivate User
                            </button>
                        </form>
                        <?php else: ?>
                        <form method="POST" action="<?php echo e(route('admin.users.bulk-action')); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="action" value="activate">
                            <input type="hidden" name="selected_users[]" value="<?php echo e($user->id); ?>">
                            <button type="submit" class="btn btn-outline-success" onclick="return confirm('Are you sure you want to activate this user?')">
                                <i class="fas fa-check me-2"></i>Activate User
                            </button>
                        </form>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('admin.users.bulk-action')); ?>" style="display: inline;">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="selected_users[]" value="<?php echo e($user->id); ?>">
                            <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Personal Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">First Name</label>
                                <p class="form-control-plaintext"><?php echo e($user->first_name ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Last Name</label>
                                <p class="form-control-plaintext"><?php echo e($user->last_name ?? 'N/A'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Email Address</label>
                                <p class="form-control-plaintext"><?php echo e($user->email ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Phone Number</label>
                                <p class="form-control-plaintext"><?php echo e($user->phone_number ?? 'N/A'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Date of Birth</label>
                                <p class="form-control-plaintext"><?php echo e($user->date_of_birth ? date('F j, Y', strtotime($user->date_of_birth)) : 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Gender</label>
                                <p class="form-control-plaintext"><?php echo e(ucfirst($user->gender ?? 'N/A')); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Address</label>
                        <p class="form-control-plaintext"><?php echo e($user->address ?? 'N/A'); ?></p>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">City</label>
                                <p class="form-control-plaintext"><?php echo e($user->city ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">State</label>
                                <p class="form-control-plaintext"><?php echo e($user->state ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Country</label>
                                <p class="form-control-plaintext"><?php echo e($user->country ?? 'Nigeria'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Account Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Role</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-<?php echo e($user->role_id === 4 ? 'danger' : ($user->role_id === 3 ? 'info' : ($user->role_id === 2 ? 'success' : 'secondary'))); ?> p-2">
                                        <?php echo e($user->role_name ?? 'Normal User'); ?>

                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-<?php echo e($user->status === 'active' ? 'success' : 'danger'); ?> p-2">
                                        <?php echo e(ucfirst($user->status ?? 'active')); ?>

                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Email Verified</label>
                                <p class="form-control-plaintext">
                                    <?php if($user->email_verified_at): ?>
                                        <span class="badge bg-success p-2">
                                            <i class="fas fa-check me-1"></i>Verified
                                        </span>
                                        <small class="text-muted d-block"><?php echo e(date('F j, Y', strtotime($user->email_verified_at))); ?></small>
                                    <?php else: ?>
                                        <span class="badge bg-warning p-2">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Not Verified
                                        </span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Last Login</label>
                                <p class="form-control-plaintext"><?php echo e($user->last_login_at ? date('F j, Y g:i A', strtotime($user->last_login_at)) : 'Never'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Member Since</label>
                                <p class="form-control-plaintext"><?php echo e(date('F j, Y', strtotime($user->created_at))); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Last Updated</label>
                                <p class="form-control-plaintext"><?php echo e(date('F j, Y g:i A', strtotime($user->updated_at))); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role-Specific Information -->
            <?php if($user->role_id == 2): ?>
            <!-- University Student Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">University Student Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">University Name</label>
                                <p class="form-control-plaintext"><?php echo e($user->university_name ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Course of Study</label>
                                <p class="form-control-plaintext"><?php echo e($user->course_of_study ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Year of Study</label>
                                <p class="form-control-plaintext">
                                    <?php if($user->year_of_study): ?>
                                        <?php echo e($user->year_of_study); ?><?php echo e($user->year_of_study == 1 ? 'st' : ($user->year_of_study == 2 ? 'nd' : ($user->year_of_study == 3 ? 'rd' : 'th'))); ?> Year
                                    <?php else: ?>
                                        Not specified
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Matriculation Number</label>
                                <p class="form-control-plaintext"><?php echo e($user->matriculation_number ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">CGPA</label>
                                <p class="form-control-plaintext"><?php echo e($user->cgpa ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php elseif($user->role_id == 3): ?>
            <!-- Partner Organization Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Partner Organization Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Name</label>
                                <p class="form-control-plaintext"><?php echo e($user->organization_name ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Type</label>
                                <p class="form-control-plaintext">
                                    <?php if($user->organization_type): ?>
                                        <?php echo e(ucwords(str_replace('_', ' ', $user->organization_type))); ?>

                                    <?php else: ?>
                                        Not specified
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Address</label>
                                <p class="form-control-plaintext"><?php echo e($user->organization_address ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Phone</label>
                                <p class="form-control-plaintext"><?php echo e($user->organization_phone ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <h6 class="text-secondary mb-3">Principal/Contact Person Information</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Principal Name</label>
                                <p class="form-control-plaintext"><?php echo e($user->principal_name ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Principal Email</label>
                                <p class="form-control-plaintext"><?php echo e($user->principal_email ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Principal Phone</label>
                                <p class="form-control-plaintext"><?php echo e($user->principal_phone ?? 'Not specified'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Activity Summary Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Activity Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo e($user->donations->count()); ?></h4>
                                <p class="text-muted mb-0">Donations</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">₦<?php echo e(number_format($user->donations->sum('amount'))); ?></h4>
                                <p class="text-muted mb-0">Total Donated</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-info"><?php echo e($user->eventRegistrations->where('attendance_status', 'attended')->count()); ?></h4>
                                <p class="text-muted mb-0">Events Attended</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning"><?php echo e($user->volunteer ? $user->volunteer->hours_logged : 0); ?></h4>
                            <p class="text-muted mb-0">Volunteer Hours</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendEmail(email) {
    window.location.href = `mailto:${email}`;
}

function viewActivity(userId) {
    alert('Activity viewer not implemented yet');
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/users/show.blade.php ENDPATH**/ ?>