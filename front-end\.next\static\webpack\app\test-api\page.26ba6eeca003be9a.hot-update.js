"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-api/page",{

/***/ "(app-pages-browser)/./app/test-api/page.tsx":
/*!*******************************!*\
  !*** ./app/test-api/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestApiPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestApiPage() {\n    _s();\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestApiPage.useEffect\": ()=>{\n            // Get current user info and token\n            const userStr = localStorage.getItem('user');\n            const authToken = localStorage.getItem('authToken');\n            if (userStr) {\n                setUser(JSON.parse(userStr));\n            }\n            setToken(authToken);\n        }\n    }[\"TestApiPage.useEffect\"], []);\n    const testEndpoint = async (name, endpoint)=>{\n        try {\n            console.log(\"Testing \".concat(name, \": \").concat(endpoint));\n            console.log('Current token:', localStorage.getItem('authToken'));\n            console.log('Current user:', localStorage.getItem('user'));\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.get(endpoint);\n            console.log(\"\".concat(name, \" response:\"), response);\n            setResults((prev)=>({\n                    ...prev,\n                    [name]: {\n                        success: true,\n                        data: response\n                    }\n                }));\n        } catch (error) {\n            var _error_response;\n            console.error(\"\".concat(name, \" error:\"), error);\n            console.error(\"\".concat(name, \" error response:\"), (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            setResults((prev)=>{\n                var _error_response;\n                return {\n                    ...prev,\n                    [name]: {\n                        success: false,\n                        error: error.message,\n                        data: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data\n                    }\n                };\n            });\n        }\n    };\n    const testAllEndpoints = async ()=>{\n        setLoading(true);\n        setResults({});\n        // Test basic endpoints first\n        await testEndpoint('User Info', '/user');\n        await testEndpoint('Public Scholarships', '/public-scholarships');\n        // Test partner-specific endpoints\n        await testEndpoint('Partner Scholarships Available', '/partner-scholarships/available');\n        await testEndpoint('Partner Students', '/partner-students');\n        await testEndpoint('Partner Applications', '/partner-scholarships/my-applications');\n        await testEndpoint('Partner Statistics', '/partner-students/statistics');\n        // Test other endpoints\n        await testEndpoint('Upcoming Events', '/events/upcoming');\n        await testEndpoint('Programs', '/programs');\n        setLoading(false);\n    };\n    const loginAsPartner = async ()=>{\n        try {\n            console.log('Attempting login with:', {\n                email: '<EMAIL>',\n                password: 'password123'\n            });\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiClient.post('/login', {\n                email: '<EMAIL>',\n                password: 'password123'\n            });\n            console.log('Full login response:', response);\n            if (response.success && response.data) {\n                console.log('User data from login:', response.data.user);\n                console.log('User role:', response.data.user.role);\n                console.log('Access token:', response.data.access_token);\n                localStorage.setItem('authToken', response.data.access_token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n                setUser(response.data.user);\n                setToken(response.data.access_token);\n                console.log('Login successful - stored in localStorage');\n            } else {\n                console.error('Login failed - no success or data in response:', response);\n            }\n        } catch (error) {\n            console.error('Login failed with error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"API Testing Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Current User Info:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                        children: user ? JSON.stringify(user, null, 2) : 'No user data found'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Auth Token:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                        children: token || 'No token found'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 space-x-2\",\n                        children: !token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loginAsPartner,\n                            className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n                            children: \"Login as Partner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('authToken');\n                                localStorage.removeItem('user');\n                                setToken(null);\n                                setUser(null);\n                                console.log('Cleared authentication');\n                            },\n                            className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\",\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: testAllEndpoints,\n                disabled: loading,\n                className: \"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6\",\n                children: loading ? 'Testing...' : 'Test All Endpoints'\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: Object.entries(results).map((param)=>{\n                    let [name, result] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border rounded p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2 flex items-center\",\n                                children: [\n                                    name,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 px-2 py-1 rounded text-xs \".concat(result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                        children: result.success ? 'SUCCESS' : 'ERROR'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-50 p-3 rounded text-xs overflow-auto max-h-40\",\n                                children: JSON.stringify(result.success ? result.data : result.error, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, name, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\test-api\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(TestApiPage, \"Vq27T3aEj78rl+Wb7CVXeJCOiMs=\");\n_c = TestApiPage;\nvar _c;\n$RefreshReg$(_c, \"TestApiPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-api/page.tsx\n"));

/***/ })

});