"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarships/page",{

/***/ "(app-pages-browser)/./app/scholarships/page.tsx":
/*!***********************************!*\
  !*** ./app/scholarships/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_animated_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/animated-section */ \"(app-pages-browser)/./components/animated-section.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BookOpen,Filter,GraduationCap,School,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst categoryIcons = {\n    primary: _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    secondary: _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    university: _barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n};\nconst categoryColors = {\n    primary: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-green-100 text-green-800 border-green-200',\n    university: 'bg-purple-100 text-purple-800 border-purple-200'\n};\nconst categoryDescriptions = {\n    primary: 'For students in grades 1-6',\n    secondary: 'For students in grades 7-12',\n    university: 'For undergraduate and graduate students'\n};\nfunction ScholarshipsPage() {\n    _s();\n    const [scholarships, setScholarships] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScholarshipsPage.useEffect\": ()=>{\n            fetchScholarships();\n        }\n    }[\"ScholarshipsPage.useEffect\"], []);\n    const fetchScholarships = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.apiClient.get('/public-scholarships');\n            if (response.success) {\n                var _response_data;\n                // Handle paginated response structure\n                const scholarshipsData = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n                setScholarships(Array.isArray(scholarshipsData) ? scholarshipsData : []);\n            } else {\n                setError(response.message || 'Failed to fetch scholarships');\n            }\n        } catch (err) {\n            console.error('Error fetching scholarships:', err);\n            setError('Failed to load scholarships. Please try again later.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredScholarships = (Array.isArray(scholarships) ? scholarships : []).filter((scholarship)=>{\n        const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) || scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || scholarship.category === selectedCategory;\n        const matchesStatus = selectedStatus === 'all' || scholarship.status === selectedStatus;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-NG', {\n            style: 'currency',\n            currency: 'NGN',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const isDeadlinePassed = (deadline)=>{\n        return new Date(deadline) < new Date();\n    };\n    const getDaysUntilDeadline = (deadline)=>{\n        const deadlineDate = new Date(deadline);\n        const today = new Date();\n        const diffTime = deadlineDate.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen flex-col bg-pattern\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-muted-foreground\",\n                                children: \"Loading scholarships...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen flex-col bg-pattern\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                children: \"Error Loading Scholarships\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: fetchScholarships,\n                                className: \"bg-green-600 hover:bg-green-700\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col bg-pattern\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"flex-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-grid opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        className: \"bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2\",\n                                        children: \"Scholarships\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl animate-fade-in-up\",\n                                        children: \"Educational Scholarships\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-green-100 animate-fade-in-up max-w-3xl mx-auto\",\n                                        style: {\n                                            animationDelay: \"0.2s\"\n                                        },\n                                        children: \"Empowering students through accessible education funding opportunities across Nigeria\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 animate-fade-in-up\",\n                                        style: {\n                                            animationDelay: \"0.4s\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: filteredScholarships.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Available Scholarships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: \"₦15M+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Total Funding\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-amber-400\",\n                                                        children: \"1,200+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-200 text-sm\",\n                                                        children: \"Students Helped\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n                    className: \"py-12 bg-green-50 dark:bg-green-950/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold tracking-tight mb-4\",\n                                        children: \"Find Your Perfect Scholarship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground max-w-2xl mx-auto\",\n                                        children: \"Use our filters to discover scholarships that match your educational level and goals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4 items-center justify-center max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                    placeholder: \"Search scholarships...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pl-10 neumorphic border-green-200 focus:border-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                        className: \"w-[180px] neumorphic border-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"primary\",\n                                                                children: \"Primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"secondary\",\n                                                                children: \"Secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"university\",\n                                                                children: \"University\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                value: selectedStatus,\n                                                onValueChange: setSelectedStatus,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                        className: \"w-[180px] neumorphic border-green-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                            placeholder: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"active\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: \"closed\",\n                                                                children: \"Closed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n                    className: \"py-20 bg-background\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: filteredScholarships.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-semibold text-gray-900 mb-2\",\n                                    children: \"No Scholarships Found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all' ? 'Try adjusting your search criteria or filters.' : 'No scholarships are currently available.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this),\n                                (searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>{\n                                        setSearchTerm('');\n                                        setSelectedCategory('all');\n                                        setSelectedStatus('all');\n                                    },\n                                    variant: \"outline\",\n                                    className: \"border-green-200 text-green-700 hover:bg-green-50\",\n                                    children: \"Clear Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-4\",\n                                            children: \"Available Scholarships\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground text-lg max-w-2xl mx-auto\",\n                                            children: \"Discover opportunities that match your educational goals and aspirations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full text-lg px-4 py-2\",\n                                                children: [\n                                                    filteredScholarships.length,\n                                                    \" \",\n                                                    filteredScholarships.length === 1 ? 'Scholarship' : 'Scholarships',\n                                                    \" Available\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: filteredScholarships.map((scholarship)=>{\n                                        const IconComponent = categoryIcons[scholarship.category];\n                                        const isExpired = isDeadlinePassed(scholarship.application_deadline);\n                                        const daysLeft = getDaysUntilDeadline(scholarship.application_deadline);\n                                        const spotsLeft = scholarship.max_applicants ? scholarship.max_applicants - scholarship.current_applicants : null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"overflow-hidden neumorphic rounded-2xl border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 hover:-translate-y-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-6 w-6 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-green-800 dark:text-green-200 mb-1\",\n                                                                        children: scholarship.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        className: \"\".concat(categoryColors[scholarship.category], \" text-xs font-medium rounded-full\"),\n                                                                        children: scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: formatCurrency(scholarship.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                                        children: daysLeft > 0 ? daysLeft : 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Days Left\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                                        children: spotsLeft !== null ? spotsLeft > 0 ? spotsLeft : 0 : '∞'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Spots Left\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Application Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: isExpired ? 'text-red-600' : 'text-green-600',\n                                                                        children: isExpired ? 'Closed' : 'Open'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-gray-200 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full transition-all duration-300 \".concat(isExpired ? 'bg-red-500' : 'bg-green-500'),\n                                                                    style: {\n                                                                        width: isExpired ? '100%' : \"\".concat(Math.max(10, 100 - daysLeft / 30 * 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    daysLeft <= 7 && daysLeft > 0 && !isExpired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-orange-800\",\n                                                                    children: [\n                                                                        \"Only \",\n                                                                        daysLeft,\n                                                                        \" \",\n                                                                        daysLeft === 1 ? 'day' : 'days',\n                                                                        \" left to apply!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/scholarships/\".concat(scholarship.id),\n                                                                className: \"flex-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full border-green-200 text-green-700 hover:bg-green-50\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                className: \"w-full bg-green-600 hover:bg-green-700 flex-1\",\n                                                                disabled: isExpired || spotsLeft !== null && spotsLeft <= 0,\n                                                                onClick: ()=>{\n                                                                    const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                                    window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                                                },\n                                                                children: isExpired ? 'Expired' : spotsLeft !== null && spotsLeft <= 0 ? 'Full' : 'Apply Now'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, scholarship.id, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n                    className: \"py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-3xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold tracking-tight sm:text-4xl mb-6\",\n                                    children: \"Ready to Start Your Educational Journey?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-green-100 mb-8\",\n                                    children: \"Don't miss out on these life-changing opportunities. Apply today and take the first step towards your dreams.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold\",\n                                            onClick: ()=>{\n                                                const laravelBaseUrl = \"https://halimakqfoundation.fnskills.ng\" || 0;\n                                                window.location.href = \"\".concat(laravelBaseUrl, \"/login\");\n                                            },\n                                            children: [\n                                                \"Start Application\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BookOpen_Filter_GraduationCap_School_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/contact\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold\",\n                                                children: \"Need Help?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipsPage, \"YvQkAWMCXm3rtbCxFeAeBL20wDk=\");\n_c = ScholarshipsPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zY2hvbGFyc2hpcHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNmO0FBQ21DO0FBQ2lDO0FBQ2pEO0FBQ0Y7QUFDdUY7QUFDdkY7QUFDeUQ7QUFDakU7QUFtQnJDLE1BQU1zQixnQkFBZ0I7SUFDcEJDLFNBQVNmLCtJQUFRQTtJQUNqQmdCLFdBQVdmLCtJQUFNQTtJQUNqQmdCLFlBQVlmLCtJQUFhQTtBQUMzQjtBQUVBLE1BQU1nQixpQkFBaUI7SUFDckJILFNBQVM7SUFDVEMsV0FBVztJQUNYQyxZQUFZO0FBQ2Q7QUFFQSxNQUFNRSx1QkFBdUI7SUFDM0JKLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxZQUFZO0FBQ2Q7QUFFZSxTQUFTRzs7SUFDdEIsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRzlCLCtDQUFRQSxDQUFnQixFQUFFO0lBQ2xFLE1BQU0sQ0FBQytCLFNBQVNDLFdBQVcsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2lDLE9BQU9DLFNBQVMsR0FBR2xDLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNtQyxZQUFZQyxjQUFjLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNxQyxrQkFBa0JDLG9CQUFvQixHQUFHdEMsK0NBQVFBLENBQVM7SUFDakUsTUFBTSxDQUFDdUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3hDLCtDQUFRQSxDQUFTO0lBRTdEQyxnREFBU0E7c0NBQUM7WUFDUndDO1FBQ0Y7cUNBQUcsRUFBRTtJQUVMLE1BQU1BLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0ZULFdBQVc7WUFDWCxNQUFNVSxXQUFXLE1BQU1yQiwrQ0FBU0EsQ0FBQ3NCLEdBQUcsQ0FBQztZQUVyQyxJQUFJRCxTQUFTRSxPQUFPLEVBQUU7b0JBRUtGO2dCQUR6QixzQ0FBc0M7Z0JBQ3RDLE1BQU1HLG1CQUFtQkgsRUFBQUEsaUJBQUFBLFNBQVNJLElBQUksY0FBYkoscUNBQUFBLGVBQWVJLElBQUksS0FBSUosU0FBU0ksSUFBSSxJQUFJLEVBQUU7Z0JBQ25FaEIsZ0JBQWdCaUIsTUFBTUMsT0FBTyxDQUFDSCxvQkFBb0JBLG1CQUFtQixFQUFFO1lBQ3pFLE9BQU87Z0JBQ0xYLFNBQVNRLFNBQVNPLE9BQU8sSUFBSTtZQUMvQjtRQUNGLEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRbEIsS0FBSyxDQUFDLGdDQUFnQ2lCO1lBQzlDaEIsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNb0IsdUJBQXVCLENBQUNMLE1BQU1DLE9BQU8sQ0FBQ25CLGdCQUFnQkEsZUFBZSxFQUFFLEVBQUV3QixNQUFNLENBQUNDLENBQUFBO1FBQ3BGLE1BQU1DLGdCQUFnQkQsWUFBWUUsS0FBSyxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3ZCLFdBQVdzQixXQUFXLE9BQ2hFSCxZQUFZSyxXQUFXLENBQUNGLFdBQVcsR0FBR0MsUUFBUSxDQUFDdkIsV0FBV3NCLFdBQVc7UUFDMUYsTUFBTUcsa0JBQWtCdkIscUJBQXFCLFNBQVNpQixZQUFZTyxRQUFRLEtBQUt4QjtRQUMvRSxNQUFNeUIsZ0JBQWdCdkIsbUJBQW1CLFNBQVNlLFlBQVlTLE1BQU0sS0FBS3hCO1FBRXpFLE9BQU9nQixpQkFBaUJLLG1CQUFtQkU7SUFDN0M7SUFFQSxNQUFNRSxpQkFBaUIsQ0FBQ0M7UUFDdEIsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNwQ0MsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLHVCQUF1QjtZQUN2QkMsdUJBQXVCO1FBQ3pCLEdBQUdDLE1BQU0sQ0FBQ1A7SUFDWjtJQUVBLE1BQU1RLGFBQWEsQ0FBQ0M7UUFDbEIsT0FBTyxJQUFJQyxLQUFLRCxZQUFZRSxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3REQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsS0FBSztRQUNQO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEIsT0FBTyxJQUFJTixLQUFLTSxZQUFZLElBQUlOO0lBQ2xDO0lBRUEsTUFBTU8sdUJBQXVCLENBQUNEO1FBQzVCLE1BQU1FLGVBQWUsSUFBSVIsS0FBS007UUFDOUIsTUFBTUcsUUFBUSxJQUFJVDtRQUNsQixNQUFNVSxXQUFXRixhQUFhRyxPQUFPLEtBQUtGLE1BQU1FLE9BQU87UUFDdkQsTUFBTUMsV0FBV0MsS0FBS0MsSUFBSSxDQUFDSixXQUFZLFFBQU8sS0FBSyxLQUFLLEVBQUM7UUFDekQsT0FBT0U7SUFDVDtJQUVBLElBQUl4RCxTQUFTO1FBQ1gscUJBQ0UsOERBQUMyRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDQztnQkFBS0QsV0FBVTswQkFDZCw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdEQ7SUFFQSxJQUFJMUQsT0FBTztRQUNULHFCQUNFLDhEQUFDeUQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUtELFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUM3RSwrSUFBV0E7Z0NBQUM2RSxXQUFVOzs7Ozs7MENBQ3ZCLDhEQUFDRztnQ0FBR0gsV0FBVTswQ0FBMEI7Ozs7OzswQ0FDeEMsOERBQUNFO2dDQUFFRixXQUFVOzBDQUE4QjFEOzs7Ozs7MENBQzNDLDhEQUFDM0IseURBQU1BO2dDQUFDeUYsU0FBU3REO2dDQUFtQmtELFdBQVU7MENBQWtDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFRNUY7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0M7WUFBS0QsV0FBVTs7OEJBRWQsOERBQUNLO29CQUFRTCxXQUFVOztzQ0FDakIsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNwRix1REFBS0E7d0NBQUNvRixXQUFVO2tEQUFvRTs7Ozs7O2tEQUNyRiw4REFBQ007d0NBQUdOLFdBQVU7a0RBQStFOzs7Ozs7a0RBRzdGLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVnZCLE9BQU87NENBQUU4QixnQkFBZ0I7d0NBQU87a0RBQ2pDOzs7Ozs7a0RBR0QsOERBQUNSO3dDQUNDQyxXQUFVO3dDQUNWdkIsT0FBTzs0Q0FBRThCLGdCQUFnQjt3Q0FBTzs7MERBRWhDLDhEQUFDUjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUFxQ3ZDLHFCQUFxQitDLE1BQU07Ozs7OztrRUFDL0UsOERBQUNUO3dEQUFJQyxXQUFVO2tFQUF5Qjs7Ozs7Ozs7Ozs7OzBEQUUxQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0M7Ozs7OztrRUFDbkQsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF5Qjs7Ozs7Ozs7Ozs7OzBEQUUxQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0M7Ozs7OztrRUFDbkQsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF5Qjs7Ozs7Ozs7Ozs7OzBEQUUxQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0M7Ozs7OztrRUFDbkQsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUWxELDhEQUFDeEYseUVBQWVBO29CQUFDd0YsV0FBVTs4QkFDekIsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FBR0gsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUEwQzs7Ozs7Ozs7Ozs7OzBDQUl6RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDOUUsK0lBQU1BO29EQUFDOEUsV0FBVTs7Ozs7OzhEQUNsQiw4REFBQzVFLHVEQUFLQTtvREFDSnFGLGFBQVk7b0RBQ1pDLE9BQU9sRTtvREFDUG1FLFVBQVUsQ0FBQ0MsSUFBTW5FLGNBQWNtRSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0RBQzdDVixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrREFJaEIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzNFLHlEQUFNQTtnREFBQ3FGLE9BQU9oRTtnREFBa0JvRSxlQUFlbkU7O2tFQUM5Qyw4REFBQ25CLGdFQUFhQTt3REFBQ3dFLFdBQVU7OzBFQUN2Qiw4REFBQy9FLCtJQUFNQTtnRUFBQytFLFdBQVU7Ozs7OzswRUFDbEIsOERBQUN2RSw4REFBV0E7Z0VBQUNnRixhQUFZOzs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDbkYsZ0VBQWFBOzswRUFDWiw4REFBQ0MsNkRBQVVBO2dFQUFDbUYsT0FBTTswRUFBTTs7Ozs7OzBFQUN4Qiw4REFBQ25GLDZEQUFVQTtnRUFBQ21GLE9BQU07MEVBQVU7Ozs7OzswRUFDNUIsOERBQUNuRiw2REFBVUE7Z0VBQUNtRixPQUFNOzBFQUFZOzs7Ozs7MEVBQzlCLDhEQUFDbkYsNkRBQVVBO2dFQUFDbUYsT0FBTTswRUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUduQyw4REFBQ3JGLHlEQUFNQTtnREFBQ3FGLE9BQU85RDtnREFBZ0JrRSxlQUFlakU7O2tFQUM1Qyw4REFBQ3JCLGdFQUFhQTt3REFBQ3dFLFdBQVU7a0VBQ3ZCLDRFQUFDdkUsOERBQVdBOzREQUFDZ0YsYUFBWTs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDbkYsZ0VBQWFBOzswRUFDWiw4REFBQ0MsNkRBQVVBO2dFQUFDbUYsT0FBTTswRUFBTTs7Ozs7OzBFQUN4Qiw4REFBQ25GLDZEQUFVQTtnRUFBQ21GLE9BQU07MEVBQVM7Ozs7OzswRUFDM0IsOERBQUNuRiw2REFBVUE7Z0VBQUNtRixPQUFNOzBFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFTdkMsOERBQUNsRyx5RUFBZUE7b0JBQUN3RixXQUFVOzhCQUN6Qiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1p2QyxxQkFBcUIrQyxNQUFNLEtBQUssa0JBQy9CLDhEQUFDVDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNuRiwrSUFBUUE7b0NBQUNtRixXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDZTtvQ0FBR2YsV0FBVTs4Q0FBNEM7Ozs7Ozs4Q0FDMUQsOERBQUNFO29DQUFFRixXQUFVOzhDQUNWeEQsY0FBY0UscUJBQXFCLFNBQVNFLG1CQUFtQixRQUM1RCxtREFDQTs7Ozs7O2dDQUVKSixDQUFBQSxjQUFjRSxxQkFBcUIsU0FBU0UsbUJBQW1CLEtBQUksbUJBQ25FLDhEQUFDakMseURBQU1BO29DQUNMeUYsU0FBUzt3Q0FDUDNELGNBQWM7d0NBQ2RFLG9CQUFvQjt3Q0FDcEJFLGtCQUFrQjtvQ0FDcEI7b0NBQ0FtRSxTQUFRO29DQUNSaEIsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7aURBTUw7OzhDQUNFLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFHSCxXQUFVO3NEQUFxRDs7Ozs7O3NEQUNuRSw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQWtEOzs7Ozs7c0RBRy9ELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3BGLHVEQUFLQTtnREFBQ29GLFdBQVU7O29EQUNkdkMscUJBQXFCK0MsTUFBTTtvREFBQztvREFBRS9DLHFCQUFxQitDLE1BQU0sS0FBSyxJQUFJLGdCQUFnQjtvREFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUsxRyw4REFBQ1Q7b0NBQUlDLFdBQVU7OENBQ1p2QyxxQkFBcUJ3RCxHQUFHLENBQUMsQ0FBQ3REO3dDQUN6QixNQUFNdUQsZ0JBQWdCdkYsYUFBYSxDQUFDZ0MsWUFBWU8sUUFBUSxDQUFDO3dDQUN6RCxNQUFNaUQsWUFBWTlCLGlCQUFpQjFCLFlBQVl5RCxvQkFBb0I7d0NBQ25FLE1BQU1DLFdBQVc5QixxQkFBcUI1QixZQUFZeUQsb0JBQW9CO3dDQUN0RSxNQUFNRSxZQUFZM0QsWUFBWTRELGNBQWMsR0FBRzVELFlBQVk0RCxjQUFjLEdBQUc1RCxZQUFZNkQsa0JBQWtCLEdBQUc7d0NBRTdHLHFCQUNFLDhEQUFDL0cscURBQUlBOzRDQUVIdUYsV0FBVTtzREFFViw0RUFBQ3RGLDREQUFXQTtnREFBQ3NGLFdBQVU7O2tFQUNyQiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ2tCO29FQUFjbEIsV0FBVTs7Ozs7Ozs7Ozs7MEVBRTNCLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNlO3dFQUFHZixXQUFVO2tGQUE2RHJDLFlBQVlFLEtBQUs7Ozs7OztrRkFDNUYsOERBQUNqRCx1REFBS0E7d0VBQUNvRixXQUFXLEdBQXdDLE9BQXJDakUsY0FBYyxDQUFDNEIsWUFBWU8sUUFBUSxDQUFDLEVBQUM7a0ZBQ3ZEUCxZQUFZTyxRQUFRLENBQUN1RCxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLL0QsWUFBWU8sUUFBUSxDQUFDeUQsS0FBSyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS2pGLDhEQUFDNUI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUFxQzNCLGVBQWVWLFlBQVlXLE1BQU07Ozs7OztrRkFDckYsOERBQUN5Qjt3RUFBSUMsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7OzswRUFFakQsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQW9DcUIsV0FBVyxJQUFJQSxXQUFXOzs7Ozs7a0ZBQzdFLDhEQUFDdEI7d0VBQUlDLFdBQVU7a0ZBQWdDOzs7Ozs7Ozs7Ozs7MEVBRWpELDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUFzQ3NCLGNBQWMsT0FBUUEsWUFBWSxJQUFJQSxZQUFZLElBQUs7Ozs7OztrRkFDNUcsOERBQUN2Qjt3RUFBSUMsV0FBVTtrRkFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFJbkQsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDNEI7a0ZBQUs7Ozs7OztrRkFDTiw4REFBQ0E7d0VBQUs1QixXQUFXbUIsWUFBWSxpQkFBaUI7a0ZBQzNDQSxZQUFZLFdBQVc7Ozs7Ozs7Ozs7OzswRUFHNUIsOERBQUNwQjtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0Q7b0VBQ0NDLFdBQVcsc0NBQWdGLE9BQTFDbUIsWUFBWSxlQUFlO29FQUM1RTFDLE9BQU87d0VBQUVvRCxPQUFPVixZQUFZLFNBQVMsR0FBNkMsT0FBMUN0QixLQUFLaUMsR0FBRyxDQUFDLElBQUksTUFBTSxXQUFZLEtBQU0sTUFBSztvRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0RBSzFGVCxZQUFZLEtBQUtBLFdBQVcsS0FBSyxDQUFDRiwyQkFDakMsOERBQUNwQjt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDN0UsK0lBQVdBO29FQUFDNkUsV0FBVTs7Ozs7OzhFQUN2Qiw4REFBQzRCO29FQUFLNUIsV0FBVTs7d0VBQXNDO3dFQUM5Q3FCO3dFQUFTO3dFQUFFQSxhQUFhLElBQUksUUFBUTt3RUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU16RCw4REFBQ3RCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3pGLGtEQUFJQTtnRUFBQ3dILE1BQU0saUJBQWdDLE9BQWZwRSxZQUFZcUUsRUFBRTtnRUFBSWhDLFdBQVU7MEVBQ3ZELDRFQUFDckYseURBQU1BO29FQUFDcUcsU0FBUTtvRUFBVWhCLFdBQVU7OEVBQTJEOzs7Ozs7Ozs7OzswRUFJakcsOERBQUNyRix5REFBTUE7Z0VBQ0xxRixXQUFVO2dFQUNWaUMsVUFBVWQsYUFBY0csY0FBYyxRQUFRQSxhQUFhO2dFQUMzRGxCLFNBQVM7b0VBQ1AsTUFBTThCLGlCQUFpQkMsd0NBQW9DLElBQUksQ0FBdUI7b0VBQ3RGRyxPQUFPQyxRQUFRLENBQUNSLElBQUksR0FBRyxHQUFrQixPQUFmRyxnQkFBZTtnRUFDM0M7MEVBRUNmLFlBQVksWUFBWSxjQUFlLFFBQVFHLGFBQWEsSUFBSyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBdkU1RTNELFlBQVlxRSxFQUFFOzs7OztvQ0E2RXpCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUVIsOERBQUN4SCx5RUFBZUE7b0JBQUN3RixXQUFVOzhCQUN6Qiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUdILFdBQVU7OENBQXFEOzs7Ozs7OENBR25FLDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBOEI7Ozs7Ozs4Q0FHM0MsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3JGLHlEQUFNQTs0Q0FDTDZILE1BQUs7NENBQ0x4QyxXQUFVOzRDQUNWSSxTQUFTO2dEQUNQLE1BQU04QixpQkFBaUJDLHdDQUFvQyxJQUFJLENBQXVCO2dEQUN0RkcsT0FBT0MsUUFBUSxDQUFDUixJQUFJLEdBQUcsR0FBa0IsT0FBZkcsZ0JBQWU7NENBQzNDOztnREFDRDs4REFFQyw4REFBQ2xILCtJQUFVQTtvREFBQ2dGLFdBQVU7Ozs7Ozs7Ozs7OztzREFFeEIsOERBQUN6RixrREFBSUE7NENBQUN3SCxNQUFLO3NEQUNULDRFQUFDcEgseURBQU1BO2dEQUFDNkgsTUFBSztnREFBS3hCLFNBQVE7Z0RBQVVoQixXQUFVOzBEQUE2Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXN0o7R0EzV3dCL0Q7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGxhcmF2ZWwtYXBpLW5nb1xcZnJvbnQtZW5kXFxhcHBcXHNjaG9sYXJzaGlwc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEFuaW1hdGVkU2VjdGlvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvYW5pbWF0ZWQtc2VjdGlvblwiXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JhZGdlXCJcbmltcG9ydCB7IEJvb2tPcGVuLCBTY2hvb2wsIEdyYWR1YXRpb25DYXAsIENhbGVuZGFyLCBVc2VycywgRG9sbGFyU2lnbiwgQXJyb3dSaWdodCwgRmlsdGVyLCBTZWFyY2gsIEFsZXJ0Q2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXG5pbXBvcnQgeyBhcGlDbGllbnQgfSBmcm9tICdAL2xpYi9hcGknXG5cbmludGVyZmFjZSBTY2hvbGFyc2hpcCB7XG4gIGlkOiBudW1iZXJcbiAgdGl0bGU6IHN0cmluZ1xuICBzbHVnOiBzdHJpbmdcbiAgY2F0ZWdvcnk6ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ3VuaXZlcnNpdHknXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgZWxpZ2liaWxpdHlfY3JpdGVyaWE/OiBzdHJpbmdcbiAgcmVxdWlyZW1lbnRzPzogc3RyaW5nXG4gIGFtb3VudDogbnVtYmVyXG4gIGFwcGxpY2F0aW9uX2RlYWRsaW5lOiBzdHJpbmdcbiAgc3RhdHVzOiBzdHJpbmdcbiAgY3VycmVudF9hcHBsaWNhbnRzOiBudW1iZXJcbiAgbWF4X2FwcGxpY2FudHM/OiBudW1iZXJcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5jb25zdCBjYXRlZ29yeUljb25zID0ge1xuICBwcmltYXJ5OiBCb29rT3BlbixcbiAgc2Vjb25kYXJ5OiBTY2hvb2wsXG4gIHVuaXZlcnNpdHk6IEdyYWR1YXRpb25DYXBcbn1cblxuY29uc3QgY2F0ZWdvcnlDb2xvcnMgPSB7XG4gIHByaW1hcnk6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIGJvcmRlci1ibHVlLTIwMCcsXG4gIHNlY29uZGFyeTogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBib3JkZXItZ3JlZW4tMjAwJyxcbiAgdW5pdmVyc2l0eTogJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwIGJvcmRlci1wdXJwbGUtMjAwJ1xufVxuXG5jb25zdCBjYXRlZ29yeURlc2NyaXB0aW9ucyA9IHtcbiAgcHJpbWFyeTogJ0ZvciBzdHVkZW50cyBpbiBncmFkZXMgMS02JyxcbiAgc2Vjb25kYXJ5OiAnRm9yIHN0dWRlbnRzIGluIGdyYWRlcyA3LTEyJyxcbiAgdW5pdmVyc2l0eTogJ0ZvciB1bmRlcmdyYWR1YXRlIGFuZCBncmFkdWF0ZSBzdHVkZW50cydcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2Nob2xhcnNoaXBzUGFnZSgpIHtcbiAgY29uc3QgW3NjaG9sYXJzaGlwcywgc2V0U2Nob2xhcnNoaXBzXSA9IHVzZVN0YXRlPFNjaG9sYXJzaGlwW10+KFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbc2VsZWN0ZWRDYXRlZ29yeSwgc2V0U2VsZWN0ZWRDYXRlZ29yeV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdhbGwnKVxuICBjb25zdCBbc2VsZWN0ZWRTdGF0dXMsIHNldFNlbGVjdGVkU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFNjaG9sYXJzaGlwcygpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGZldGNoU2Nob2xhcnNoaXBzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoJy9wdWJsaWMtc2Nob2xhcnNoaXBzJylcbiAgICAgIFxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gSGFuZGxlIHBhZ2luYXRlZCByZXNwb25zZSBzdHJ1Y3R1cmVcbiAgICAgICAgY29uc3Qgc2Nob2xhcnNoaXBzRGF0YSA9IHJlc3BvbnNlLmRhdGE/LmRhdGEgfHwgcmVzcG9uc2UuZGF0YSB8fCBbXVxuICAgICAgICBzZXRTY2hvbGFyc2hpcHMoQXJyYXkuaXNBcnJheShzY2hvbGFyc2hpcHNEYXRhKSA/IHNjaG9sYXJzaGlwc0RhdGEgOiBbXSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBmZXRjaCBzY2hvbGFyc2hpcHMnKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc2Nob2xhcnNoaXBzOicsIGVycilcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBzY2hvbGFyc2hpcHMuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBmaWx0ZXJlZFNjaG9sYXJzaGlwcyA9IChBcnJheS5pc0FycmF5KHNjaG9sYXJzaGlwcykgPyBzY2hvbGFyc2hpcHMgOiBbXSkuZmlsdGVyKHNjaG9sYXJzaGlwID0+IHtcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID0gc2Nob2xhcnNoaXAudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgc2Nob2xhcnNoaXAuZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpXG4gICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ2FsbCcgfHwgc2Nob2xhcnNoaXAuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnlcbiAgICBjb25zdCBtYXRjaGVzU3RhdHVzID0gc2VsZWN0ZWRTdGF0dXMgPT09ICdhbGwnIHx8IHNjaG9sYXJzaGlwLnN0YXR1cyA9PT0gc2VsZWN0ZWRTdGF0dXNcbiAgICBcbiAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzQ2F0ZWdvcnkgJiYgbWF0Y2hlc1N0YXR1c1xuICB9KVxuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tTkcnLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnTkdOJyxcbiAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMCxcbiAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMCxcbiAgICB9KS5mb3JtYXQoYW1vdW50KVxuICB9XG5cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJ1xuICAgIH0pXG4gIH1cblxuICBjb25zdCBpc0RlYWRsaW5lUGFzc2VkID0gKGRlYWRsaW5lOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGVhZGxpbmUpIDwgbmV3IERhdGUoKVxuICB9XG5cbiAgY29uc3QgZ2V0RGF5c1VudGlsRGVhZGxpbmUgPSAoZGVhZGxpbmU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGRlYWRsaW5lRGF0ZSA9IG5ldyBEYXRlKGRlYWRsaW5lKVxuICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IGRpZmZUaW1lID0gZGVhZGxpbmVEYXRlLmdldFRpbWUoKSAtIHRvZGF5LmdldFRpbWUoKVxuICAgIGNvbnN0IGRpZmZEYXlzID0gTWF0aC5jZWlsKGRpZmZUaW1lIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKVxuICAgIHJldHVybiBkaWZmRGF5c1xuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBiZy1wYXR0ZXJuXCI+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHB5LTIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItZ3JlZW4tNjAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nIHNjaG9sYXJzaGlwcy4uLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBiZy1wYXR0ZXJuXCI+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHB5LTIwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1yZWQtNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItMlwiPkVycm9yIExvYWRpbmcgU2Nob2xhcnNoaXBzPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTZcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2ZldGNoU2Nob2xhcnNoaXBzfSBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgVHJ5IEFnYWluXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBiZy1wYXR0ZXJuXCI+XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicmVsYXRpdmUgcHktMjAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmVlbi02MDAgdG8tZ3JlZW4tODAwIHRleHQtd2hpdGUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyaWQgb3BhY2l0eS0yMFwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHRleHQtY2VudGVyIHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNzAwIGhvdmVyOmJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBweC00IHB5LTJcIj5TY2hvbGFyc2hpcHM8L0JhZGdlPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0IHNtOnRleHQtNXhsIG1kOnRleHQtNnhsIGFuaW1hdGUtZmFkZS1pbi11cFwiPlxuICAgICAgICAgICAgICAgIEVkdWNhdGlvbmFsIFNjaG9sYXJzaGlwc1xuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmVlbi0xMDAgYW5pbWF0ZS1mYWRlLWluLXVwIG1heC13LTN4bCBteC1hdXRvXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogXCIwLjJzXCIgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEVtcG93ZXJpbmcgc3R1ZGVudHMgdGhyb3VnaCBhY2Nlc3NpYmxlIGVkdWNhdGlvbiBmdW5kaW5nIG9wcG9ydHVuaXRpZXMgYWNyb3NzIE5pZ2VyaWFcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNiBtdC0xMiBhbmltYXRlLWZhZGUtaW4tdXBcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBcIjAuNHNcIiB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1hbWJlci00MDBcIj57ZmlsdGVyZWRTY2hvbGFyc2hpcHMubGVuZ3RofTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTIwMCB0ZXh0LXNtXCI+QXZhaWxhYmxlIFNjaG9sYXJzaGlwczwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYW1iZXItNDAwXCI+MzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTIwMCB0ZXh0LXNtXCI+Q2F0ZWdvcmllczwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYW1iZXItNDAwXCI+4oKmMTVNKzwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTIwMCB0ZXh0LXNtXCI+VG90YWwgRnVuZGluZzwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYW1iZXItNDAwXCI+MSwyMDArPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMjAwIHRleHQtc21cIj5TdHVkZW50cyBIZWxwZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIHsvKiBGaWx0ZXJzIFNlY3Rpb24gKi99XG4gICAgICAgIDxBbmltYXRlZFNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTIgYmctZ3JlZW4tNTAgZGFyazpiZy1ncmVlbi05NTAvMjBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdHJhY2tpbmctdGlnaHQgbWItNFwiPkZpbmQgWW91ciBQZXJmZWN0IFNjaG9sYXJzaGlwPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgVXNlIG91ciBmaWx0ZXJzIHRvIGRpc2NvdmVyIHNjaG9sYXJzaGlwcyB0aGF0IG1hdGNoIHlvdXIgZWR1Y2F0aW9uYWwgbGV2ZWwgYW5kIGdvYWxzXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGdhcC00IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtYXgtdy1tZFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTQwMCBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBzY2hvbGFyc2hpcHMuLi5cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgbmV1bW9ycGhpYyBib3JkZXItZ3JlZW4tMjAwIGZvY3VzOmJvcmRlci1ncmVlbi00MDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NlbGVjdGVkQ2F0ZWdvcnl9IG9uVmFsdWVDaGFuZ2U9e3NldFNlbGVjdGVkQ2F0ZWdvcnl9PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1bMTgwcHhdIG5ldW1vcnBoaWMgYm9yZGVyLWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIkNhdGVnb3J5XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImFsbFwiPkFsbCBDYXRlZ29yaWVzPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cInByaW1hcnlcIj5QcmltYXJ5PC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cInNlY29uZGFyeVwiPlNlY29uZGFyeTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJ1bml2ZXJzaXR5XCI+VW5pdmVyc2l0eTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtzZWxlY3RlZFN0YXR1c30gb25WYWx1ZUNoYW5nZT17c2V0U2VsZWN0ZWRTdGF0dXN9PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1bMTgwcHhdIG5ldW1vcnBoaWMgYm9yZGVyLWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTdGF0dXNcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsXCI+QWxsIFN0YXR1czwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJhY3RpdmVcIj5BY3RpdmU8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiY2xvc2VkXCI+Q2xvc2VkPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0FuaW1hdGVkU2VjdGlvbj5cblxuICAgICAgICB7LyogU2Nob2xhcnNoaXBzIEdyaWQgKi99XG4gICAgICAgIDxBbmltYXRlZFNlY3Rpb24gY2xhc3NOYW1lPVwicHktMjAgYmctYmFja2dyb3VuZFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgICAgICB7ZmlsdGVyZWRTY2hvbGFyc2hpcHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gU2Nob2xhcnNoaXBzIEZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWFyY2hUZXJtIHx8IHNlbGVjdGVkQ2F0ZWdvcnkgIT09ICdhbGwnIHx8IHNlbGVjdGVkU3RhdHVzICE9PSAnYWxsJ1xuICAgICAgICAgICAgICAgICAgICA/ICdUcnkgYWRqdXN0aW5nIHlvdXIgc2VhcmNoIGNyaXRlcmlhIG9yIGZpbHRlcnMuJ1xuICAgICAgICAgICAgICAgICAgICA6ICdObyBzY2hvbGFyc2hpcHMgYXJlIGN1cnJlbnRseSBhdmFpbGFibGUuJ31cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgeyhzZWFyY2hUZXJtIHx8IHNlbGVjdGVkQ2F0ZWdvcnkgIT09ICdhbGwnIHx8IHNlbGVjdGVkU3RhdHVzICE9PSAnYWxsJykgJiYgKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0U2VhcmNoVGVybSgnJylcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZENhdGVnb3J5KCdhbGwnKVxuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkU3RhdHVzKCdhbGwnKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmVlbi0yMDAgdGV4dC1ncmVlbi03MDAgaG92ZXI6YmctZ3JlZW4tNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBDbGVhciBGaWx0ZXJzXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCBzbTp0ZXh0LTR4bCBtYi00XCI+QXZhaWxhYmxlIFNjaG9sYXJzaGlwczwvaDI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1sZyBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICBEaXNjb3ZlciBvcHBvcnR1bml0aWVzIHRoYXQgbWF0Y2ggeW91ciBlZHVjYXRpb25hbCBnb2FscyBhbmQgYXNwaXJhdGlvbnNcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIGRhcms6YmctZ3JlZW4tOTAwIGRhcms6dGV4dC1ncmVlbi0yMDAgcm91bmRlZC1mdWxsIHRleHQtbGcgcHgtNCBweS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkU2Nob2xhcnNoaXBzLmxlbmd0aH0ge2ZpbHRlcmVkU2Nob2xhcnNoaXBzLmxlbmd0aCA9PT0gMSA/ICdTY2hvbGFyc2hpcCcgOiAnU2Nob2xhcnNoaXBzJ30gQXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAgICB7ZmlsdGVyZWRTY2hvbGFyc2hpcHMubWFwKChzY2hvbGFyc2hpcCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IGNhdGVnb3J5SWNvbnNbc2Nob2xhcnNoaXAuY2F0ZWdvcnldXG4gICAgICAgICAgICAgICAgICBjb25zdCBpc0V4cGlyZWQgPSBpc0RlYWRsaW5lUGFzc2VkKHNjaG9sYXJzaGlwLmFwcGxpY2F0aW9uX2RlYWRsaW5lKVxuICAgICAgICAgICAgICAgICAgY29uc3QgZGF5c0xlZnQgPSBnZXREYXlzVW50aWxEZWFkbGluZShzY2hvbGFyc2hpcC5hcHBsaWNhdGlvbl9kZWFkbGluZSlcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHNwb3RzTGVmdCA9IHNjaG9sYXJzaGlwLm1heF9hcHBsaWNhbnRzID8gc2Nob2xhcnNoaXAubWF4X2FwcGxpY2FudHMgLSBzY2hvbGFyc2hpcC5jdXJyZW50X2FwcGxpY2FudHMgOiBudWxsXG5cbiAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtzY2hvbGFyc2hpcC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW4gbmV1bW9ycGhpYyByb3VuZGVkLTJ4bCBib3JkZXItZ3JlZW4tMTAwIGRhcms6Ym9yZGVyLWdyZWVuLTgwMCBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOi10cmFuc2xhdGUteS0xXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1ncmVlbi0xMDAgZGFyazpiZy1ncmVlbi05MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25Db21wb25lbnQgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwIG1iLTFcIj57c2Nob2xhcnNoaXAudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPXtgJHtjYXRlZ29yeUNvbG9yc1tzY2hvbGFyc2hpcC5jYXRlZ29yeV19IHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1mdWxsYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2Nob2xhcnNoaXAuY2F0ZWdvcnkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBzY2hvbGFyc2hpcC5jYXRlZ29yeS5zbGljZSgxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57Zm9ybWF0Q3VycmVuY3koc2Nob2xhcnNoaXAuYW1vdW50KX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QW1vdW50PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPntkYXlzTGVmdCA+IDAgPyBkYXlzTGVmdCA6IDB9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkRheXMgTGVmdDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMFwiPntzcG90c0xlZnQgIT09IG51bGwgPyAoc3BvdHNMZWZ0ID4gMCA/IHNwb3RzTGVmdCA6IDApIDogJ+KInid9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlNwb3RzIExlZnQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QXBwbGljYXRpb24gU3RhdHVzPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17aXNFeHBpcmVkID8gJ3RleHQtcmVkLTYwMCcgOiAndGV4dC1ncmVlbi02MDAnfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc0V4cGlyZWQgPyAnQ2xvc2VkJyA6ICdPcGVuJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMiBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke2lzRXhwaXJlZCA/ICdiZy1yZWQtNTAwJyA6ICdiZy1ncmVlbi01MDAnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogaXNFeHBpcmVkID8gJzEwMCUnIDogYCR7TWF0aC5tYXgoMTAsIDEwMCAtIChkYXlzTGVmdCAvIDMwKSAqIDEwMCl9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZGF5c0xlZnQgPD0gNyAmJiBkYXlzTGVmdCA+IDAgJiYgIWlzRXhwaXJlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctb3JhbmdlLTUwIGJvcmRlciBib3JkZXItb3JhbmdlLTIwMCByb3VuZGVkLWxnIHAtMyBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1vcmFuZ2UtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1vcmFuZ2UtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE9ubHkge2RheXNMZWZ0fSB7ZGF5c0xlZnQgPT09IDEgPyAnZGF5JyA6ICdkYXlzJ30gbGVmdCB0byBhcHBseSFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj17YC9zY2hvbGFyc2hpcHMvJHtzY2hvbGFyc2hpcC5pZH1gfSBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci1ncmVlbi0yMDAgdGV4dC1ncmVlbi03MDAgaG92ZXI6YmctZ3JlZW4tNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBmbGV4LTFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0V4cGlyZWQgfHwgKHNwb3RzTGVmdCAhPT0gbnVsbCAmJiBzcG90c0xlZnQgPD0gMCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbGFyYXZlbEJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYCR7bGFyYXZlbEJhc2VVcmx9L2xvZ2luYFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNFeHBpcmVkID8gJ0V4cGlyZWQnIDogKHNwb3RzTGVmdCAhPT0gbnVsbCAmJiBzcG90c0xlZnQgPD0gMCkgPyAnRnVsbCcgOiAnQXBwbHkgTm93J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9BbmltYXRlZFNlY3Rpb24+XG5cbiAgICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgICA8QW5pbWF0ZWRTZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JlZW4tNjAwIHRvLWdyZWVuLTgwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCBzbTp0ZXh0LTR4bCBtYi02XCI+XG4gICAgICAgICAgICAgICAgUmVhZHkgdG8gU3RhcnQgWW91ciBFZHVjYXRpb25hbCBKb3VybmV5P1xuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JlZW4tMTAwIG1iLThcIj5cbiAgICAgICAgICAgICAgICBEb24ndCBtaXNzIG91dCBvbiB0aGVzZSBsaWZlLWNoYW5naW5nIG9wcG9ydHVuaXRpZXMuIEFwcGx5IHRvZGF5IGFuZCB0YWtlIHRoZSBmaXJzdCBzdGVwIHRvd2FyZHMgeW91ciBkcmVhbXMuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWFtYmVyLTUwMCBob3ZlcjpiZy1hbWJlci02MDAgdGV4dC1ncmVlbi05NTAgZm9udC1zZW1pYm9sZFwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxhcmF2ZWxCYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwMDAnXG4gICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYCR7bGFyYXZlbEJhc2VVcmx9L2xvZ2luYFxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBTdGFydCBBcHBsaWNhdGlvblxuICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwibWwtMiBoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2NvbnRhY3RcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gc2l6ZT1cImxnXCIgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJib3JkZXItMiBib3JkZXItZ3JlZW4tNDAwIHRleHQtZ3JlZW4tNDAwIGhvdmVyOmJnLWdyZWVuLTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgTmVlZCBIZWxwP1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0FuaW1hdGVkU2VjdGlvbj5cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsIkFuaW1hdGVkU2VjdGlvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkJ1dHRvbiIsIkJhZGdlIiwiQm9va09wZW4iLCJTY2hvb2wiLCJHcmFkdWF0aW9uQ2FwIiwiQXJyb3dSaWdodCIsIkZpbHRlciIsIlNlYXJjaCIsIkFsZXJ0Q2lyY2xlIiwiSW5wdXQiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsImFwaUNsaWVudCIsImNhdGVnb3J5SWNvbnMiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwidW5pdmVyc2l0eSIsImNhdGVnb3J5Q29sb3JzIiwiY2F0ZWdvcnlEZXNjcmlwdGlvbnMiLCJTY2hvbGFyc2hpcHNQYWdlIiwic2Nob2xhcnNoaXBzIiwic2V0U2Nob2xhcnNoaXBzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsInNlbGVjdGVkU3RhdHVzIiwic2V0U2VsZWN0ZWRTdGF0dXMiLCJmZXRjaFNjaG9sYXJzaGlwcyIsInJlc3BvbnNlIiwiZ2V0Iiwic3VjY2VzcyIsInNjaG9sYXJzaGlwc0RhdGEiLCJkYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwibWVzc2FnZSIsImVyciIsImNvbnNvbGUiLCJmaWx0ZXJlZFNjaG9sYXJzaGlwcyIsImZpbHRlciIsInNjaG9sYXJzaGlwIiwibWF0Y2hlc1NlYXJjaCIsInRpdGxlIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImRlc2NyaXB0aW9uIiwibWF0Y2hlc0NhdGVnb3J5IiwiY2F0ZWdvcnkiLCJtYXRjaGVzU3RhdHVzIiwic3RhdHVzIiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsIm1pbmltdW1GcmFjdGlvbkRpZ2l0cyIsIm1heGltdW1GcmFjdGlvbkRpZ2l0cyIsImZvcm1hdCIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImlzRGVhZGxpbmVQYXNzZWQiLCJkZWFkbGluZSIsImdldERheXNVbnRpbERlYWRsaW5lIiwiZGVhZGxpbmVEYXRlIiwidG9kYXkiLCJkaWZmVGltZSIsImdldFRpbWUiLCJkaWZmRGF5cyIsIk1hdGgiLCJjZWlsIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiIsInAiLCJoMiIsIm9uQ2xpY2siLCJzZWN0aW9uIiwiaDEiLCJhbmltYXRpb25EZWxheSIsImxlbmd0aCIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvblZhbHVlQ2hhbmdlIiwiaDMiLCJ2YXJpYW50IiwibWFwIiwiSWNvbkNvbXBvbmVudCIsImlzRXhwaXJlZCIsImFwcGxpY2F0aW9uX2RlYWRsaW5lIiwiZGF5c0xlZnQiLCJzcG90c0xlZnQiLCJtYXhfYXBwbGljYW50cyIsImN1cnJlbnRfYXBwbGljYW50cyIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJzcGFuIiwid2lkdGgiLCJtYXgiLCJocmVmIiwiaWQiLCJkaXNhYmxlZCIsImxhcmF2ZWxCYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsIndpbmRvdyIsImxvY2F0aW9uIiwic2l6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/scholarships/page.tsx\n"));

/***/ })

});