<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Donation;
use App\Models\User;
use App\Models\DonationCampaign;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Donation>
 */
class DonationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Donation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'donor_id' => null, // Anonymous donations by default
            'donor_name' => $this->faker->name(),
            'donor_email' => $this->faker->safeEmail(),
            'donor_phone' => $this->faker->phoneNumber(),
            'amount' => $this->faker->numberBetween(1000, 100000),
            'currency' => 'NGN',
            'donation_type' => $this->faker->randomElement(['one-time', 'recurring']),
            'frequency' => function (array $attributes) {
                return $attributes['donation_type'] === 'recurring' 
                    ? $this->faker->randomElement(['monthly', 'quarterly', 'annually'])
                    : null;
            },
            'purpose' => $this->faker->sentence(),
            'campaign_id' => null,
            'payment_method' => $this->faker->randomElement(['paystack', 'bank_transfer']),
            'payment_status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'refunded']),
            'transaction_reference' => 'TXN_' . strtoupper($this->faker->bothify('??########')),
            'is_anonymous' => $this->faker->boolean(30), // 30% chance of being anonymous
            'notes' => $this->faker->optional()->paragraph(),
        ];
    }

    /**
     * Indicate that the donation is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'completed',
        ]);
    }

    /**
     * Indicate that the donation is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the donation is for a specific campaign.
     */
    public function forCampaign(DonationCampaign $campaign = null): static
    {
        return $this->state(fn (array $attributes) => [
            'campaign_id' => $campaign ? $campaign->id : DonationCampaign::factory(),
        ]);
    }

    /**
     * Indicate that the donation is from a registered user.
     */
    public function fromUser(User $user = null): static
    {
        return $this->state(fn (array $attributes) => [
            'donor_id' => $user ? $user->id : User::factory(),
            'is_anonymous' => false,
        ]);
    }

    /**
     * Indicate that the donation is anonymous.
     */
    public function anonymous(): static
    {
        return $this->state(fn (array $attributes) => [
            'donor_id' => null,
            'is_anonymous' => true,
        ]);
    }

    /**
     * Indicate that the donation is recurring.
     */
    public function recurring(string $frequency = 'monthly'): static
    {
        return $this->state(fn (array $attributes) => [
            'donation_type' => 'recurring',
            'frequency' => $frequency,
        ]);
    }

    /**
     * Indicate that the donation uses Paystack.
     */
    public function paystack(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'paystack',
        ]);
    }

    /**
     * Indicate that the donation uses bank transfer.
     */
    public function bankTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'bank_transfer',
        ]);
    }
}
