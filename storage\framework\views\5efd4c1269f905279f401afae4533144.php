<?php $__env->startSection('title', 'Available Scholarships - Partner Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">All Scholarships</h1>
                            <p class="text-green-100 text-sm lg:text-base">Browse all available scholarships - Primary, Secondary, and University categories</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="<?php echo e(route('partner.students.index')); ?>" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-users mr-2"></i>
                                Manage Students
                            </a>
                            <a href="<?php echo e(route('partner.applications.index')); ?>" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-file-alt mr-2"></i>
                                View Applications
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Total</p>
                                <p class="text-xl font-bold text-gray-900"><?php echo e($stats['total_scholarships']); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Open</p>
                                <p class="text-xl font-bold text-green-600"><?php echo e($stats['open_scholarships']); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-times-circle text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Closed</p>
                                <p class="text-xl font-bold text-red-600"><?php echo e($stats['closed_scholarships']); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-child text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Primary</p>
                                <p class="text-xl font-bold text-blue-600"><?php echo e($stats['primary_scholarships']); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-graduate text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Secondary</p>
                                <p class="text-xl font-bold text-purple-600"><?php echo e($stats['secondary_scholarships']); ?></p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-university text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">University</p>
                                <p class="text-xl font-bold text-indigo-600"><?php echo e($stats['university_scholarships']); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <form method="GET" action="<?php echo e(route('partner.scholarships.index')); ?>" class="flex flex-col lg:flex-row gap-4">
                            <div class="flex-1">
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Scholarships</label>
                                <input type="text"
                                       id="search"
                                       name="search"
                                       value="<?php echo e(request('search')); ?>"
                                       placeholder="Search by title, description, or category..."
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div class="lg:w-48">
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select id="category"
                                        name="category"
                                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="">All Categories</option>
                                    <option value="primary" <?php echo e(request('category') == 'primary' ? 'selected' : ''); ?>>Primary School</option>
                                    <option value="secondary" <?php echo e(request('category') == 'secondary' ? 'selected' : ''); ?>>Secondary School</option>
                                    <option value="university" <?php echo e(request('category') == 'university' ? 'selected' : ''); ?>>University</option>
                                </select>
                            </div>
                            <div class="lg:w-48">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="status"
                                        name="status"
                                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="">All Status</option>
                                    <option value="open" <?php echo e(request('status') == 'open' ? 'selected' : ''); ?>>Open</option>
                                    <option value="closed" <?php echo e(request('status') == 'closed' ? 'selected' : ''); ?>>Closed</option>
                                </select>
                            </div>
                            <div class="lg:w-32 flex items-end">
                                <button type="submit" class="w-full px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-search mr-2"></i>
                                    Search
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Scholarships Grid -->
                    <?php if($scholarships->count() > 0): ?>
                        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                            <?php $__currentLoopData = $scholarships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scholarship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isOpen = $scholarship->status == 'open' && $scholarship->is_open && $scholarship->application_deadline > now();
                                    $categoryConfig = [
                                        'primary' => ['color' => 'blue', 'icon' => 'fa-child', 'label' => 'Primary School'],
                                        'secondary' => ['color' => 'purple', 'icon' => 'fa-user-graduate', 'label' => 'Secondary School'],
                                        'university' => ['color' => 'indigo', 'icon' => 'fa-university', 'label' => 'University']
                                    ];
                                    $config = $categoryConfig[$scholarship->category] ?? $categoryConfig['primary'];
                                ?>
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
                                    <div class="p-6">
                                        <!-- Category Badge and Status -->
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-<?php echo e($config['color']); ?>-100 text-<?php echo e($config['color']); ?>-800">
                                                <i class="fas <?php echo e($config['icon']); ?> mr-1"></i>
                                                <?php echo e($config['label']); ?>

                                            </span>
                                            <div class="flex items-center space-x-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                    <?php echo e($isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                    <i class="fas <?php echo e($isOpen ? 'fa-check-circle' : 'fa-times-circle'); ?> mr-1"></i>
                                                    <?php echo e($isOpen ? 'Open' : 'Closed'); ?>

                                                </span>
                                            </div>
                                        </div>

                                        <!-- Title and Description -->
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($scholarship->title); ?></h3>
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e(Str::limit($scholarship->description, 120)); ?></p>

                                        <!-- Amount and Details -->
                                        <div class="space-y-3 mb-4">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Amount:</span>
                                                <span class="text-lg font-bold text-green-600">₦<?php echo e(number_format($scholarship->amount, 0)); ?></span>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Deadline:</span>
                                                <span class="text-sm font-medium <?php echo e($isOpen ? 'text-gray-900' : 'text-red-600'); ?>">
                                                    <?php echo e($scholarship->application_deadline->format('M d, Y')); ?>

                                                </span>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Applicants:</span>
                                                <span class="text-sm font-medium text-gray-900">
                                                    <?php echo e($scholarship->current_applicants ?? 0); ?>/<?php echo e($scholarship->max_applicants ?? '∞'); ?>

                                                </span>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex gap-3">
                                            <a href="<?php echo e(route('partner.scholarships.show', $scholarship->id)); ?>"
                                               class="flex-1 text-center px-4 py-2 border border-green-600 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-300">
                                                View Details
                                            </a>
                                            <?php if($isOpen && in_array($scholarship->category, ['primary', 'secondary'])): ?>
                                                <a href="<?php echo e(route('partner.scholarships.create', ['scholarship_id' => $scholarship->id])); ?>"
                                                   class="flex-1 text-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    Apply Now
                                                </a>
                                            <?php elseif($scholarship->category == 'university'): ?>
                                                <span class="flex-1 text-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed text-xs">
                                                    University Only
                                                </span>
                                            <?php else: ?>
                                                <span class="flex-1 text-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed">
                                                    <?php echo e($isOpen ? 'Closed' : 'Deadline Passed'); ?>

                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Pagination -->
                        <div class="flex justify-center">
                            <?php echo e($scholarships->appends(request()->query())->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Scholarships Found</h3>
                            <p class="text-gray-600 mb-6">No scholarships match your current search criteria. Try adjusting your filters or search terms.</p>
                            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                <a href="<?php echo e(route('partner.scholarships.index')); ?>" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-refresh mr-2"></i>
                                    Clear Filters
                                </a>
                                <button onclick="document.getElementById('search').value=''; document.getElementById('category').value=''; document.getElementById('status').value=''; document.querySelector('form').submit();"
                                        class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-times mr-2"></i>
                                    Reset Search
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    mobileMenuBtn.addEventListener('click', openMobileMenu);
    mobileSidebarOverlay.addEventListener('click', closeMobileMenu);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/scholarships/index.blade.php ENDPATH**/ ?>