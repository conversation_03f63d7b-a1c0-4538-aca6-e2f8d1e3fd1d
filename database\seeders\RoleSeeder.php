<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $userRole = Role::firstOrCreate(['name' => 'user']);
        $volunteerRole = Role::firstOrCreate(['name' => 'volunteer']);
        $donorRole = Role::firstOrCreate(['name' => 'donor']);

        // Create permissions
        $permissions = [
            'manage-users',
            'manage-programs',
            'manage-events',
            'manage-volunteers',
            'manage-donations',
            'manage-blog',
            'manage-newsletter',
            'manage-contact',
            'manage-scholarships',
            'manage-team',
            'view-analytics',
            'manage-settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign all permissions to admin role
        $adminRole->givePermissionTo(Permission::all());

        // Assign roles to existing users
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            $adminUser->assignRole('admin');
        }

        $testUser = User::where('email', '<EMAIL>')->first();
        if ($testUser) {
            $testUser->assignRole('user');
        }

        $volunteerUser = User::where('email', '<EMAIL>')->first();
        if ($volunteerUser) {
            $volunteerUser->assignRole('volunteer');
        }

        echo "Roles and permissions seeded successfully!\n";
    }
}
