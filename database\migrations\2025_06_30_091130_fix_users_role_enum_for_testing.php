<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Expand the role enum to include 'user' for testing compatibility
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('normal', 'student', 'partner', 'admin', 'user') DEFAULT 'normal'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'user' from enum
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('normal', 'student', 'partner', 'admin') DEFAULT 'normal'");
    }
};
