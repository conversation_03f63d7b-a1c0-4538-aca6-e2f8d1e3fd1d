<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class BlogPost extends Model
{
    use HasFactory, HasSlug;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'author_id',
        'category_id',
        'status',
        'views_count',
        'likes_count',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'tags',
        'published_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'views_count' => 'integer',
        'likes_count' => 'integer',
        'seo_keywords' => 'array',
        'tags' => 'array',
        'published_at' => 'datetime',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the author that wrote the post.
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get the category that owns the post.
     */
    public function category()
    {
        return $this->belongsTo(BlogCategory::class, 'category_id');
    }

    /**
     * Get the comments for the post.
     */
    public function comments()
    {
        return $this->hasMany(BlogComment::class, 'post_id');
    }

    /**
     * Get the approved comments for the post.
     */
    public function approvedComments()
    {
        return $this->hasMany(BlogComment::class, 'post_id')->approved();
    }

    /**
     * Check if post is published.
     */
    public function isPublished(): bool
    {
        return $this->status === 'published' && $this->published_at <= now();
    }

    /**
     * Check if post is draft.
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Scope a query to only include published posts.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope a query to only include draft posts.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope a query to search posts by title or content.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($query) use ($term) {
            $query->where('title', 'like', "%{$term}%")
                  ->orWhere('content', 'like', "%{$term}%")
                  ->orWhere('excerpt', 'like', "%{$term}%");
        });
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to filter by author.
     */
    public function scopeByAuthor($query, $authorId)
    {
        return $query->where('author_id', $authorId);
    }

    /**
     * Scope a query to filter by tags.
     */
    public function scopeWithTags($query, array $tags)
    {
        return $query->whereJsonContains('tags', $tags);
    }

    /**
     * Increment views count.
     */
    public function incrementViews(): void
    {
        $this->increment('views_count');
    }

    /**
     * Increment likes count.
     */
    public function incrementLikes(): void
    {
        $this->increment('likes_count');
    }

    /**
     * Publish the post.
     */
    public function publish(): void
    {
        $this->update([
            'status' => 'published',
            'published_at' => now(),
        ]);
    }

    /**
     * Unpublish the post.
     */
    public function unpublish(): void
    {
        $this->update([
            'status' => 'draft',
        ]);
    }

    /**
     * Get the featured image URL attribute.
     */
    public function getFeaturedImageUrlAttribute(): ?string
    {
        if ($this->featured_image) {
            return asset('storage/' . $this->featured_image);
        }

        // Return unique placeholder based on post ID
        $placeholders = [
            'https://images.unsplash.com/photo-1529070538774-1843cb3265df', // Impact/community
            'https://images.unsplash.com/photo-1593113598332-cd288d649433', // Education
            'https://images.unsplash.com/photo-**********-cd4628902d4a', // Healthcare
            'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40', // Environment
            'https://images.unsplash.com/photo-1517048676732-d65bc937f952', // Community work
            'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c', // Volunteering
            'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca', // Charity
            'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b', // Nature/environment
            'https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a', // Help/support
            'https://images.unsplash.com/photo-1484712401471-05c7215830eb' // Community building
        ];

        // Use post ID to get consistent but different images
        $index = $this->id % count($placeholders);
        return $placeholders[$index] . '?w=800&h=500&fit=crop&crop=center';
    }

    /**
     * Get reading time in minutes.
     */
    public function getReadingTimeAttribute(): string
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $minutes = max(1, ceil($wordCount / 200)); // Average reading speed: 200 words per minute
        return $minutes . ' min read';
    }
}
