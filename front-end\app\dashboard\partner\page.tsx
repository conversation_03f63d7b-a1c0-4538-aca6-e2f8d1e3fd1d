"use client"

import { useEffect, useState } from "react"
import { apiClient, extractArrayData } from "@/lib/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Building,
  Users,
  Calendar,
  FileText,
  Award,
  Target,
  Handshake,
  Mail,
  Phone,
  Globe,
  MapPin,
  Plus,
  CheckCircle,
  Clock,
  GraduationCap,
  School,
  BookOpen,
  UserPlus,
  TrendingUp,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import ProposalManager from "@/components/dashboard/partner/ProposalManager"
import ResourceSharing from "@/components/dashboard/partner/ResourceSharing"

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  preferences?: {
    partner_data?: {
      organization_name?: string
    }
  }
}

interface Event {
  id: number
  title: string
  date: string
  status: string
}

interface Program {
  id: number
  name: string
  status: string
}

export default function PartnerDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [partnershipApplications, setPartnershipApplications] = useState<any[]>([])
  const [collaborationOpportunities, setCollaborationOpportunities] = useState<any[]>([])
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([])
  const [programs, setPrograms] = useState<Program[]>([])
  const [loading, setLoading] = useState(true)

  // Scholarship-related state
  const [availableScholarships, setAvailableScholarships] = useState<any[]>([])
  const [myStudents, setMyStudents] = useState<any[]>([])
  const [scholarshipApplications, setScholarshipApplications] = useState<any[]>([])
  const [studentStats, setStudentStats] = useState<any>(null)

  // Dashboard overview state
  const [dashboardData, setDashboardData] = useState<any>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch user data first
        const userData = await apiClient.getProfile()
        if (userData.success) {
          setUser(userData.data)
        }

        // Fetch comprehensive dashboard data
        try {
          console.log('Fetching partner dashboard data...')
          const dashboardResponse = await apiClient.get('/partner-dashboard')
          console.log('Dashboard response:', dashboardResponse)
          if (dashboardResponse.success) {
            setDashboardData(dashboardResponse.data)
            console.log('Dashboard data set:', dashboardResponse.data)
            // Update student stats from dashboard data
            if (dashboardResponse.data.overview) {
              setStudentStats({
                total_students: dashboardResponse.data.overview.total_students,
                active_students: dashboardResponse.data.overview.total_students, // Assuming all are active
                recent_enrollments: 0, // Will be updated from recent activities
                scholarship_applications: dashboardResponse.data.overview.total_applications
              })
              console.log('Student stats updated from dashboard data')
            }
          } else {
            console.log('Dashboard response not successful:', dashboardResponse)
          }
        } catch (error: any) {
          console.error('Error fetching dashboard data:', error)
          console.error('Error details:', error.response?.data || error.message)
        }

        // Fetch partner-specific data
        const [eventsResponse, programsResponse] = await Promise.all([
          apiClient.getUpcomingEvents(),
          apiClient.getPrograms()
        ])

        const eventsData = extractArrayData(eventsResponse)
        const programsData = extractArrayData(programsResponse)

        setUpcomingEvents(eventsData.slice(0, 5))
        setPrograms(programsData.slice(0, 3))

        // Fetch scholarship-related data
        await fetchScholarshipData()

        setLoading(false)
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        setLoading(false)
      }
    }

    const fetchScholarshipData = async () => {
      try {
        console.log('Starting to fetch scholarship data...')

        // Fetch available scholarships for partner organizations (Primary/Secondary only)
        try {
          console.log('Fetching available scholarships...')
          const scholarshipsResponse = await apiClient.getPartnerAvailableScholarships()
          console.log('Scholarships response:', scholarshipsResponse)
          if (scholarshipsResponse.success) {
            setAvailableScholarships(scholarshipsResponse.data || [])
          }
        } catch (error) {
          console.error('Error fetching scholarships:', error)
        }

        // Fetch partner students
        try {
          console.log('Fetching partner students...')
          const studentsResponse = await apiClient.getPartnerStudents()
          console.log('Students response:', studentsResponse)
          if (studentsResponse.success) {
            // Handle both paginated and direct array responses
            const studentsData = studentsResponse.data?.data || studentsResponse.data || []
            setMyStudents(Array.isArray(studentsData) ? studentsData : [])
          }
        } catch (error) {
          console.error('Error fetching students:', error)
        }

        // Fetch scholarship applications submitted by this partner
        try {
          console.log('Fetching my applications...')
          const applicationsResponse = await apiClient.getPartnerApplications()
          console.log('Applications response:', applicationsResponse)
          if (applicationsResponse.success) {
            const applicationsData = applicationsResponse.data?.data || applicationsResponse.data || []
            setScholarshipApplications(Array.isArray(applicationsData) ? applicationsData : [])
          }
        } catch (error) {
          console.error('Error fetching applications:', error)
        }

        // Fetch student statistics
        try {
          console.log('Fetching student statistics...')
          const statsResponse = await apiClient.get('/partner-students/statistics')
          console.log('Stats response:', statsResponse)
          if (statsResponse.success) {
            setStudentStats(statsResponse.data || {})
          }
        } catch (error) {
          console.error('Error fetching stats:', error)
        }

      } catch (error) {
        console.error('Error fetching scholarship data:', error)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading || !user) {
    return <div>Loading...</div>
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-500'
      case 'rejected': return 'bg-red-500'
      case 'pending': return 'bg-yellow-500'
      case 'under_review': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  const partnerInfo = (user?.preferences?.partner_data || {}) as {
    organization_name?: string
    organization_type?: string
    sector?: string
    [key: string]: any
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
                <Building className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome, {partnerInfo.organization_name || user?.first_name}!
                </h1>
                <p className="text-gray-600">Partnership Dashboard</p>
                {partnerInfo.organization_type && (
                  <p className="text-sm text-blue-600">
                    {partnerInfo.organization_type} • {partnerInfo.sector}
                  </p>
                )}
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/partnerships/opportunities">
                  <Plus className="h-4 w-4 mr-2" />
                  View Opportunities
                </Link>
              </Button>
              <Button asChild>
                <Link href="/partnerships/apply">
                  Apply for Partnership
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">My Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {dashboardData?.overview?.total_students || studentStats?.total_students || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Enrolled students
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available Scholarships</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {dashboardData?.quick_stats?.active_scholarships || availableScholarships.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Open for applications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {dashboardData?.overview?.total_applications || scholarshipApplications.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Total applications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {dashboardData?.overview?.success_rate || 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Application success rate
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="scholarships" className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="scholarships">Scholarships</TabsTrigger>
            <TabsTrigger value="students">My Students</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="progression">Progression</TabsTrigger>
            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
            <TabsTrigger value="proposals">Proposals</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          {/* Scholarship Management Tab */}
          <TabsContent value="scholarships" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Available Scholarships */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Award className="h-5 w-5 mr-2" />
                      Available Scholarships
                    </div>
                    <Badge variant="secondary">{availableScholarships.length} Available</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {availableScholarships.slice(0, 3).map((scholarship: any) => (
                    <div key={scholarship.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-lg">{scholarship.title}</h4>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">{scholarship.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span><GraduationCap className="h-3 w-3 inline mr-1" />
                              {scholarship.category === 'primary' ? 'Primary (Grades 1-6)' : 'Secondary (Grades 7-12)'}
                            </span>
                            <span><Calendar className="h-3 w-3 inline mr-1" />
                              Deadline: {new Date(scholarship.application_deadline).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="mt-3 flex items-center gap-2">
                            <Badge variant={scholarship.category === 'primary' ? 'default' : 'secondary'}>
                              {scholarship.category.charAt(0).toUpperCase() + scholarship.category.slice(1)}
                            </Badge>
                            <span className="text-sm font-medium text-green-600">
                              ₦{scholarship.amount ? Number(scholarship.amount).toLocaleString() : 'Amount varies'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 flex gap-2">
                        <Button size="sm" asChild>
                          <Link href={`/dashboard/partner/apply?scholarship_id=${scholarship.id}&category=${scholarship.category}`}>
                            Apply for Student
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/scholarships/${scholarship.id}`}>
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                  {availableScholarships.length === 0 && (
                    <div className="text-center py-8">
                      <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No scholarships available at the moment</p>
                      <p className="text-xs text-gray-400 mt-2">Check back later for new opportunities</p>
                    </div>
                  )}
                  {availableScholarships.length > 3 && (
                    <Button variant="outline" className="w-full">
                      View All Scholarships ({availableScholarships.length})
                    </Button>
                  )}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserPlus className="h-5 w-5 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    <Button className="justify-start h-auto p-4" variant="outline" asChild>
                      <Link href="/dashboard/partner/students/add">
                        <div className="flex items-center">
                          <UserPlus className="h-5 w-5 mr-3" />
                          <div className="text-left">
                            <div className="font-medium">Add New Student</div>
                            <div className="text-sm text-gray-500">Register a new student to your school</div>
                          </div>
                        </div>
                      </Link>
                    </Button>

                    <Button className="justify-start h-auto p-4" variant="outline" asChild>
                      <Link href="/dashboard/partner/students">
                        <div className="flex items-center">
                          <Users className="h-5 w-5 mr-3" />
                          <div className="text-left">
                            <div className="font-medium">Manage Students</div>
                            <div className="text-sm text-gray-500">View and manage all your students</div>
                          </div>
                        </div>
                      </Link>
                    </Button>

                    <Button className="justify-start h-auto p-4" variant="outline" asChild>
                      <Link href="/scholarships">
                        <div className="flex items-center">
                          <BookOpen className="h-5 w-5 mr-3" />
                          <div className="text-left">
                            <div className="font-medium">Browse Scholarships</div>
                            <div className="text-sm text-gray-500">Explore all available scholarships</div>
                          </div>
                        </div>
                      </Link>
                    </Button>

                    <Button className="justify-start h-auto p-4" variant="outline" asChild>
                      <Link href="/dashboard/partner/analytics">
                        <div className="flex items-center">
                          <BarChart3 className="h-5 w-5 mr-3" />
                          <div className="text-left">
                            <div className="font-medium">Analytics & Reports</div>
                            <div className="text-sm text-gray-500">View performance insights and statistics</div>
                          </div>
                        </div>
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Dashboard Overview Cards */}
            {dashboardData && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Activities */}
                {dashboardData.recent_activities && dashboardData.recent_activities.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Clock className="h-5 w-5 mr-2" />
                        Recent Activities
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {dashboardData.recent_activities.slice(0, 5).map((activity: any, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-2 rounded-lg bg-gray-50">
                          <div className="flex-1">
                            <p className="text-sm font-medium">{activity.description}</p>
                            <p className="text-xs text-gray-500">{new Date(activity.created_at).toLocaleDateString()}</p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {activity.type}
                          </Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {/* Upcoming Deadlines */}
                {dashboardData.upcoming_deadlines && dashboardData.upcoming_deadlines.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Calendar className="h-5 w-5 mr-2" />
                        Upcoming Deadlines
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {dashboardData.upcoming_deadlines.slice(0, 5).map((deadline: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-yellow-50">
                          <div>
                            <p className="text-sm font-medium">{deadline.title}</p>
                            <p className="text-xs text-gray-600">{deadline.description}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-yellow-600">
                              {new Date(deadline.deadline).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-gray-500">{deadline.days_remaining} days left</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Recent Applications */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Recent Scholarship Applications
                  </div>
                  <Badge variant="outline">{scholarshipApplications.length} Total</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {scholarshipApplications.slice(0, 5).map((application: any) => (
                    <div key={application.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">
                            {application.student?.full_name || application.student_name || 'Student Name'}
                          </p>
                          <Badge variant="outline" className="text-xs">
                            {application.scholarship?.category || application.scholarship_category || 'Category'}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">
                          {application.scholarship?.title || application.scholarship_title || 'Scholarship Title'}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                          <span>Applied: {new Date(application.created_at).toLocaleDateString()}</span>
                          {application.scholarship?.amount && (
                            <span>Amount: ₦{Number(application.scholarship.amount).toLocaleString()}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={`text-white ${getStatusColor(application.status)}`}>
                          {application.status?.charAt(0).toUpperCase() + application.status?.slice(1) || 'Pending'}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                  {scholarshipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No scholarship applications yet</p>
                      <p className="text-xs text-gray-400 mb-4">Start applying for scholarships on behalf of your students</p>
                      <Button asChild>
                        <Link href="/scholarships">Browse Scholarships</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Students Management Tab */}
          <TabsContent value="students" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Student Management</h2>
              <div className="flex space-x-2">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/partner/students">
                    <Users className="h-4 w-4 mr-2" />
                    View All Students
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/dashboard/partner/students/add">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add New Student
                  </Link>
                </Button>
              </div>
            </div>

            {/* Student Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{studentStats?.total_students || 0}</div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{studentStats?.active_students || 0}</div>
                  <p className="text-sm text-gray-600">Active Students</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{studentStats?.recent_enrollments || 0}</div>
                  <p className="text-sm text-gray-600">Recent Enrollments</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-orange-600">{studentStats?.scholarship_applications || 0}</div>
                  <p className="text-sm text-gray-600">Applications</p>
                </CardContent>
              </Card>
            </div>

            {/* Students List */}
            <Card>
              <CardHeader>
                <CardTitle>My Students</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {myStudents.slice(0, 10).map((student: any) => (
                    <div key={student.id} className="flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow">
                      <div className="flex-1">
                        <p className="font-medium">{student.full_name}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>Age: {student.age || 'N/A'}</span>
                          <span>Grade: {student.progressions?.[0]?.grade_level || 'Not set'}</span>
                          <span>Status: <Badge variant="outline" className="text-xs ml-1">
                            {student.status || 'Active'}
                          </Badge></span>
                        </div>
                        {student.student_id && (
                          <p className="text-xs text-gray-500 mt-1">ID: {student.student_id}</p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          View Profile
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/partner/apply?student_id=${student.id}`}>
                            Apply for Scholarship
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                  {myStudents.length === 0 && (
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No students registered yet</p>
                      <p className="text-xs text-gray-400 mb-4">Add students to start managing their scholarship applications</p>
                      <Button asChild>
                        <Link href="/dashboard/partner/students/add">Add Your First Student</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Collaboration Opportunities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Handshake className="h-5 w-5 mr-2" />
                    Collaboration Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {collaborationOpportunities.slice(0, 3).map((opportunity: any) => (
                    <div key={opportunity.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{opportunity.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{opportunity.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span><MapPin className="h-3 w-3 inline mr-1" />{opportunity.location}</span>
                        <span><Clock className="h-3 w-3 inline mr-1" />{opportunity.timeline}</span>
                      </div>
                      <div className="mt-2">
                        <Badge variant="secondary">{opportunity.type}</Badge>
                        <span className="text-xs text-gray-500 ml-2">{opportunity.budget_range}</span>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/partnerships/opportunities">View All Opportunities</Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Current Programs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Available Programs
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {programs.map((program: any) => (
                    <div key={program.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{program.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{program.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline">{program.category}</Badge>
                        <Button variant="outline" size="sm">Learn More</Button>
                      </div>
                    </div>
                  ))}
                  {programs.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No programs available</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Partnership Applications Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Partnership Applications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {partnershipApplications.map((application: any) => (
                    <div key={application.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{application.program}</p>
                        <p className="text-sm text-gray-600">
                          Submitted: {new Date(application.submitted_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge className={getStatusColor(application.status)}>
                        {application.status}
                      </Badge>
                    </div>
                  ))}
                  {partnershipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <Handshake className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No partnership applications yet</p>
                      <Button asChild>
                        <Link href="/partnerships/apply">Submit Application</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="opportunities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Partnership Opportunities</CardTitle>
                <CardDescription>
                  Explore collaboration opportunities that align with your organization's mission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {collaborationOpportunities.map((opportunity: any) => (
                    <div key={opportunity.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{opportunity.title}</h4>
                        <Button variant="outline" size="sm">Apply</Button>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{opportunity.description}</p>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <p><MapPin className="h-4 w-4 inline mr-1" />Location: {opportunity.location}</p>
                        <p><Clock className="h-4 w-4 inline mr-1" />Timeline: {opportunity.timeline}</p>
                        <p><Target className="h-4 w-4 inline mr-1" />Type: {opportunity.type}</p>
                        <p><Award className="h-4 w-4 inline mr-1" />Budget: {opportunity.budget_range}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="proposals" className="space-y-6">
            <ProposalManager />
          </TabsContent>

          <TabsContent value="resources" className="space-y-6">
            <ResourceSharing />
          </TabsContent>

          <TabsContent value="applications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Scholarship Applications</CardTitle>
                <CardDescription>
                  Track scholarship applications submitted on behalf of your students
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {scholarshipApplications.map((application: any) => (
                    <div key={application.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{application.scholarship?.title || 'Scholarship Application'}</h4>
                        <Badge className={`text-white ${getStatusColor(application.status)}`}>
                          {application.status?.charAt(0).toUpperCase() + application.status?.slice(1) || 'Pending'}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <p><strong>Student:</strong> {application.student?.full_name || application.student_name || 'N/A'}</p>
                        <p><strong>Amount:</strong> ₦{application.scholarship?.amount ? Number(application.scholarship.amount).toLocaleString() : 'N/A'}</p>
                        <p><strong>Category:</strong> {application.scholarship?.category || application.category || 'N/A'}</p>
                        <p><strong>Submitted:</strong> {new Date(application.submitted_at || application.created_at).toLocaleDateString()}</p>
                      </div>
                      {application.scholarship?.application_deadline && (
                        <p className="text-xs text-gray-500 mt-2">
                          <strong>Deadline:</strong> {new Date(application.scholarship.application_deadline).toLocaleDateString()}
                        </p>
                      )}
                      <div className="mt-3 flex space-x-2">
                        <Button variant="outline" size="sm">View Details</Button>
                        {application.status === 'pending' && (
                          <Button variant="outline" size="sm">Edit Application</Button>
                        )}
                        {application.status === 'approved' && (
                          <Badge variant="secondary" className="ml-2">✓ Approved</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {scholarshipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No scholarship applications submitted yet</p>
                      <p className="text-xs text-gray-400 mb-4">Start applying for scholarships on behalf of your students</p>
                      <Button asChild>
                        <Link href="/scholarships">Browse Available Scholarships</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Academic Progression Tab */}
          <TabsContent value="progression" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Academic Progression Tracking</span>
                  <Button asChild>
                    <Link href="/dashboard/partner/progression">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View All Progressions
                    </Link>
                  </Button>
                </CardTitle>
                <CardDescription>
                  Track and manage student academic progression through grade levels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-blue-600">0</p>
                    <p className="text-sm text-gray-600">Total Progressions</p>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-yellow-600">0</p>
                    <p className="text-sm text-gray-600">Pending Approval</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-green-600">0</p>
                    <p className="text-sm text-gray-600">Completed</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Recent Progressions</h3>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/dashboard/partner/progression/create">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Progression
                      </Link>
                    </Button>
                  </div>

                  <div className="text-center py-8 text-gray-500">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="mb-2">No progressions found</p>
                    <p className="text-sm">Start tracking student academic progression</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Organization Profile</CardTitle>
                <CardDescription>
                  Manage your organization's information and partnership preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Organization Name</label>
                    <p className="text-sm text-gray-600">{partnerInfo.organization_name || 'Not specified'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Organization Type</label>
                    <p className="text-sm text-gray-600">{partnerInfo.organization_type || 'Not specified'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Contact Person</label>
                    <p className="text-sm text-gray-600">{user?.first_name} {user?.last_name}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-gray-600">{user?.email}</p>
                  </div>
                  {partnerInfo.website && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Website</label>
                      <p className="text-sm text-gray-600">{partnerInfo.website}</p>
                    </div>
                  )}
                  {partnerInfo.sector && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Sector</label>
                      <p className="text-sm text-gray-600">{partnerInfo.sector}</p>
                    </div>
                  )}
                  {partnerInfo.size && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Organization Size</label>
                      <p className="text-sm text-gray-600">{partnerInfo.size}</p>
                    </div>
                  )}
                  {partnerInfo.annual_budget && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Annual Budget</label>
                      <p className="text-sm text-gray-600">{partnerInfo.annual_budget}</p>
                    </div>
                  )}
                </div>
                
                {partnerInfo.partnership_interests && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Partnership Interests</label>
                    <p className="text-sm text-gray-600">{partnerInfo.partnership_interests}</p>
                  </div>
                )}
                
                <Button variant="outline">Edit Organization Profile</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}