
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'userType' => 'user',
    'menuItems' => []
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'userType' => 'user',
    'menuItems' => []
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white shadow-lg h-full">
    <!-- User Profile Section -->
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-lg">
                    <?php echo e(strtoupper(substr(Auth::user()->first_name, 0, 1))); ?><?php echo e(strtoupper(substr(Auth::user()->last_name, 0, 1))); ?>

                </span>
            </div>
            <div class="ml-4">
                <h3 class="font-semibold text-gray-800"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></h3>
                <p class="text-sm text-gray-600 capitalize"><?php echo e(str_replace('_', ' ', Auth::user()->role)); ?></p>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="p-4">
        <ul class="space-y-2">
            <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                    <a href="<?php echo e($item['url']); ?>"
                       <?php if(isset($item['external']) && $item['external']): ?> target="_blank" rel="noopener noreferrer" <?php endif; ?>
                       class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo e((isset($item['active']) && $item['active']) || (!isset($item['external']) && request()->is(trim($item['url'], '/'))) ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-500' : ''); ?>">
                        <i class="fas fa-<?php echo e($item['icon']); ?> mr-3 text-lg"></i>
                        <span class="font-medium"><?php echo e($item['label']); ?></span>
                        <?php if(isset($item['external']) && $item['external']): ?>
                            <i class="fas fa-external-link-alt ml-auto text-xs text-gray-400"></i>
                        <?php endif; ?>
                        <?php if(isset($item['badge'])): ?>
                            <span class="ml-auto px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                <?php echo e($item['badge']); ?>

                            </span>
                        <?php endif; ?>
                    </a>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </nav>

    <!-- Quick Actions -->
    <div class="p-4 border-t border-gray-200 mt-auto">
        <div class="space-y-2">
            <a href="<?php echo e(route('profile.edit') ?? '#'); ?>" 
               class="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                <i class="fas fa-cog mr-2"></i>
                Settings
            </a>
            <form method="POST" action="<?php echo e(route('logout')); ?>">
                <?php echo csrf_field(); ?>
                <button type="submit" 
                        class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:text-red-800 transition-colors">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Logout
                </button>
            </form>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/components/dashboard/sidebar.blade.php ENDPATH**/ ?>