<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\BlogCategory;
use App\Models\TeamMember;
use App\Models\Scholarship;
use App\Models\Program;
use App\Models\Event;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create default admin user
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone_number' => '+234800000000',
            'role' => 'admin',
            'status' => 'active',
            'gender' => 'other',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
        ]);

        // Create test user
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone_number' => '+234800000001',
            'role' => 'normal',
            'status' => 'active',
            'gender' => 'male',
            'date_of_birth' => '1990-01-01',
            'city' => 'Abuja',
            'state' => 'FCT',
            'country' => 'Nigeria',
        ]);

        // Create university student user
        $universityStudent = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'first_name' => 'John',
            'last_name' => 'Student',
            'password' => Hash::make('password'),
            'role' => 'student',
            'status' => 'active',
            'email_verified_at' => now(),
            'phone_number' => '+2348012345678',
            'gender' => 'male',
            'date_of_birth' => '2000-01-01',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria'
        ]);

        // Create partner organization user
        $partnerUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'first_name' => 'Partner',
            'last_name' => 'Organization',
            'password' => Hash::make('password'),
            'role' => 'partner',
            'status' => 'active',
            'email_verified_at' => now(),
            'phone_number' => '+2348012345679',
            'gender' => 'other',
            'city' => 'Abuja',
            'state' => 'FCT',
            'country' => 'Nigeria'
        ]);

        // Create volunteer user
        $volunteer = User::create([
            'first_name' => 'Jane',
            'last_name' => 'Volunteer',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone_number' => '+234800000002',
            'role' => 'normal',
            'status' => 'active',
            'gender' => 'female',
            'date_of_birth' => '1995-05-15',
            'city' => 'Port Harcourt',
            'state' => 'Rivers',
            'country' => 'Nigeria',
        ]);

        // Create blog categories
        $categories = [
            ['name' => 'Community Impact', 'slug' => 'community-impact', 'description' => 'Stories about our community impact'],
            ['name' => 'Education', 'slug' => 'education', 'description' => 'Educational programs and initiatives'],
            ['name' => 'Healthcare', 'slug' => 'healthcare', 'description' => 'Healthcare outreach and support'],
            ['name' => 'Youth Empowerment', 'slug' => 'youth-empowerment', 'description' => 'Youth development programs'],
            ['name' => 'Volunteer Stories', 'slug' => 'volunteer-stories', 'description' => 'Stories from our volunteers'],
        ];

        foreach ($categories as $category) {
            BlogCategory::create($category);
        }

        // Create team members
        $teamMembers = [
            [
                'name' => 'Dr. Adebayo Ogundimu',
                'slug' => 'dr-adebayo-ogundimu',
                'position' => 'Executive Director',
                'department' => 'Leadership',
                'bio' => 'Dr. Ogundimu has over 20 years of experience in non-profit management and community development.',
                'email' => '<EMAIL>',
                'sort_order' => 1,
                'is_active' => true,
                'qualifications' => json_encode(['PhD in Public Administration', 'MBA in Non-Profit Management']),
                'achievements' => json_encode(['Founded 3 successful NGOs', 'Recipient of Community Service Award 2020']),
            ],
            [
                'name' => 'Mrs. Folake Adeyemi',
                'slug' => 'mrs-folake-adeyemi',
                'position' => 'Program Director',
                'department' => 'Programs',
                'bio' => 'Mrs. Adeyemi leads our educational and community outreach programs.',
                'email' => '<EMAIL>',
                'sort_order' => 2,
                'is_active' => true,
                'qualifications' => json_encode(['MSc in Development Studies', 'Certificate in Project Management']),
                'achievements' => json_encode(['Managed over 50 successful programs', 'Trained 200+ volunteers']),
            ],
        ];

        foreach ($teamMembers as $member) {
            TeamMember::create($member);
        }

        // Create scholarships
        $scholarships = [
            [
                'title' => 'HLTKKQ Excellence Scholarship',
                'slug' => 'hltkkq-excellence-scholarship',
                'description' => 'Supporting outstanding students from disadvantaged backgrounds.',
                'eligibility_criteria' => 'Must be a Nigerian citizen, demonstrate academic excellence, and show financial need.',
                'amount' => 500000.00,
                'application_deadline' => now()->addMonths(3),
                'requirements' => json_encode(['Academic transcripts', 'Letter of recommendation', 'Personal statement', 'Financial need documentation']),
                'status' => 'open',
                'max_applicants' => 50,
                'current_applicants' => 0,
                'contact_email' => '<EMAIL>',
                'documents_required' => json_encode(['transcript', 'recommendation_letter', 'personal_statement', 'financial_documents']),
            ],
            [
                'title' => 'Women in STEM Scholarship',
                'slug' => 'women-in-stem-scholarship',
                'description' => 'Empowering young women to pursue careers in Science, Technology, Engineering, and Mathematics.',
                'eligibility_criteria' => 'Female students enrolled in STEM programs at accredited universities.',
                'amount' => 750000.00,
                'application_deadline' => now()->addMonths(2),
                'requirements' => json_encode(['Proof of enrollment in STEM program', 'Research proposal', 'Academic transcripts']),
                'status' => 'open',
                'max_applicants' => 25,
                'current_applicants' => 0,
                'contact_email' => '<EMAIL>',
                'documents_required' => json_encode(['enrollment_proof', 'research_proposal', 'transcript']),
            ],
        ];

        foreach ($scholarships as $scholarship) {
            Scholarship::create($scholarship);
        }

        // Create programs
        $programs = [
            [
                'title' => 'Community Literacy Program',
                'slug' => 'community-literacy-program',
                'description' => 'Improving literacy rates in underserved communities through free tutoring and educational resources.',
                'objectives' => json_encode(['Increase literacy rates by 50%', 'Train 100 community teachers', 'Establish 10 learning centers']),
                'target_beneficiaries' => 'Children and adults in rural communities',
                'budget' => 2000000.00,
                'status' => 'active',
                'location' => 'Lagos, Ogun, and Oyo States',
                'start_date' => now()->subMonths(2),
                'end_date' => now()->addYear(),
                'created_by' => $admin->id,
                'impact_metrics' => json_encode(['people_reached' => 500, 'communities_served' => 15, 'learning_centers' => 8]),
            ],
            [
                'title' => 'Youth Skill Development Initiative',
                'slug' => 'youth-skill-development-initiative',
                'description' => 'Providing vocational training and entrepreneurship skills to young people.',
                'objectives' => json_encode(['Train 200 youth in various skills', 'Support 50 business startups', 'Create 300 job opportunities']),
                'target_beneficiaries' => 'Youth aged 18-35',
                'budget' => 5000000.00,
                'status' => 'active',
                'location' => 'Nationwide',
                'start_date' => now()->subMonth(),
                'end_date' => now()->addMonths(18),
                'created_by' => $admin->id,
                'impact_metrics' => json_encode(['people_reached' => 200, 'businesses_started' => 30, 'jobs_created' => 150]),
            ],
        ];

        foreach ($programs as $program) {
            Program::create($program);
        }

        // Create events
        $events = [
            [
                'title' => 'Annual Fundraising Gala',
                'slug' => 'annual-fundraising-gala-2024',
                'description' => 'Join us for an evening of celebration and fundraising for our community programs.',
                'event_type' => 'fundraising',
                'start_datetime' => now()->addWeeks(3),
                'end_datetime' => now()->addWeeks(3)->addHours(4),
                'venue' => 'Eko Hotel & Suites, Lagos',
                'capacity' => 500,
                'registration_deadline' => now()->addWeeks(2),
                'status' => 'published',
                'organizer_id' => $admin->id,
                'requirements' => json_encode(['Formal attire required', 'Valid ID for entry']),
                'agenda' => json_encode(['7:00 PM - Cocktail reception', '8:00 PM - Dinner and presentations', '10:00 PM - Entertainment']),
            ],
            [
                'title' => 'Volunteer Training Workshop',
                'slug' => 'volunteer-training-workshop-march-2024',
                'description' => 'Comprehensive training for new volunteers on our programs and procedures.',
                'event_type' => 'training',
                'start_datetime' => now()->addWeek(),
                'end_datetime' => now()->addWeek()->addHours(6),
                'venue' => 'HLTKKQ Foundation Office, Abuja',
                'capacity' => 50,
                'registration_deadline' => now()->addDays(3),
                'status' => 'published',
                'organizer_id' => $admin->id,
                'requirements' => json_encode(['Must be an approved volunteer', 'Bring valid ID']),
                'agenda' => json_encode(['9:00 AM - Welcome and introductions', '10:00 AM - Foundation overview', '2:00 PM - Hands-on training']),
            ],
        ];

        foreach ($events as $event) {
            Event::create($event);
        }

        // Run additional seeders for multi-tier scholarship system
        $this->call([
            PartnerOrganizationSeeder::class,
            StudentSeeder::class,
        ]);

        echo "Database seeded successfully!\n";
        echo "Admin credentials: <EMAIL> / password\n";
        echo "Test user credentials: <EMAIL> / password\n";
        echo "Volunteer credentials: <EMAIL> / password\n";
        echo "Partner Organization accounts created with password: password123\n";
    }
}
