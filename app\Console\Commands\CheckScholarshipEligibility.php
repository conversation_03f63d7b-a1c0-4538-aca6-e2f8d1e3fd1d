<?php

namespace App\Console\Commands;

use App\Services\ScholarshipEligibilityService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckScholarshipEligibility extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'scholarships:check-eligibility 
                            {--partner-org= : Check eligibility for specific partner organization ID}
                            {--academic-year= : Check for specific academic year}
                            {--send-notifications : Send notifications to eligible organizations}';

    /**
     * The console command description.
     */
    protected $description = 'Check scholarship eligibility for students who have completed grade progressions';

    protected $eligibilityService;

    /**
     * Create a new command instance.
     */
    public function __construct(ScholarshipEligibilityService $eligibilityService)
    {
        parent::__construct();
        $this->eligibilityService = $eligibilityService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting scholarship eligibility check...');

        try {
            if ($this->option('partner-org')) {
                return $this->checkSpecificOrganization();
            } else {
                return $this->checkAllOrganizations();
            }
        } catch (\Exception $e) {
            $this->error('Error during eligibility check: ' . $e->getMessage());
            Log::error('Scholarship eligibility check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Check eligibility for a specific organization
     */
    private function checkSpecificOrganization(): int
    {
        $partnerOrgId = $this->option('partner-org');
        $academicYear = $this->option('academic-year');

        $this->info("Checking eligibility for partner organization ID: {$partnerOrgId}");

        try {
            $partnerOrg = \App\Models\PartnerOrganization::findOrFail($partnerOrgId);
            
            $eligibleStudents = $this->eligibilityService->getEligibleStudentsForOrganization(
                $partnerOrg, 
                $academicYear
            );

            $this->displayResults($partnerOrg->name, $eligibleStudents);

            if ($this->option('send-notifications') && !empty($eligibleStudents)) {
                $this->eligibilityService->sendEligibilityNotifications($partnerOrg, $eligibleStudents);
                $this->info('✓ Notifications sent to organization users');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to check organization {$partnerOrgId}: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Check eligibility for all organizations
     */
    private function checkAllOrganizations(): int
    {
        $this->info('Running eligibility check for all partner organizations...');

        $results = $this->eligibilityService->runDailyEligibilityCheck();

        $this->info('✓ Eligibility check completed');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Organizations Processed', $results['processed_organizations']],
                ['Total Eligible Students', $results['total_eligible_students']],
                ['Notifications Sent', $results['notifications_sent']],
                ['Errors', count($results['errors'])],
            ]
        );

        if (!empty($results['errors'])) {
            $this->warn('Errors encountered:');
            foreach ($results['errors'] as $error) {
                if (is_array($error)) {
                    $this->error("Partner Org {$error['partner_org_id']}: {$error['error']}");
                } else {
                    $this->error($error);
                }
            }
        }

        return empty($results['errors']) ? 0 : 1;
    }

    /**
     * Display results for a single organization
     */
    private function displayResults(string $organizationName, array $eligibleStudents): void
    {
        $this->info("Results for {$organizationName}:");
        
        if (empty($eligibleStudents)) {
            $this->warn('No eligible students found');
            return;
        }

        $this->info("Found " . count($eligibleStudents) . " eligible student(s):");

        $tableData = [];
        foreach ($eligibleStudents as $eligibleStudent) {
            $student = $eligibleStudent['student'];
            $progression = $eligibleStudent['progression'];
            $eligibility = $eligibleStudent['eligibility'];

            $tableData[] = [
                $student->full_name,
                $this->formatGrade($progression->from_grade),
                $this->formatGrade($progression->to_grade),
                $eligibility['category'],
                count($eligibility['scholarships']),
            ];
        }

        $this->table(
            ['Student Name', 'From Grade', 'To Grade', 'Category', 'Available Scholarships'],
            $tableData
        );
    }

    /**
     * Format grade level for display
     */
    private function formatGrade(string $grade): string
    {
        $gradeMap = [
            'primary_1' => 'Primary 1',
            'primary_2' => 'Primary 2',
            'primary_3' => 'Primary 3',
            'primary_4' => 'Primary 4',
            'primary_5' => 'Primary 5',
            'primary_6' => 'Primary 6',
            'secondary_1' => 'Secondary 1',
            'secondary_2' => 'Secondary 2',
            'secondary_3' => 'Secondary 3',
            'secondary_4' => 'Secondary 4',
            'secondary_5' => 'Secondary 5',
            'secondary_6' => 'Secondary 6',
            'university' => 'University',
        ];

        return $gradeMap[$grade] ?? ucfirst(str_replace('_', ' ', $grade));
    }
}
