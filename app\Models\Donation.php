<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Donation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'donor_id',
        'donor_email',
        'donor_name',
        'donor_phone',
        'amount',
        'currency',
        'donation_type',
        'frequency',
        'purpose',
        'campaign_id',
        'payment_method',
        'payment_provider',
        'transaction_reference',
        'payment_status',
        'is_anonymous',
        'receipt_number',
        'receipt_issued_at',
        'notes',
        'payment_metadata',
        'thank_you_sent',
        'thank_you_sent_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'is_anonymous' => 'boolean',
        'receipt_issued_at' => 'datetime',
        'payment_metadata' => 'array',
        'thank_you_sent' => 'boolean',
        'thank_you_sent_at' => 'datetime',
    ];

    /**
     * Get the donor (user) for this donation.
     */
    public function donor()
    {
        return $this->belongsTo(User::class, 'donor_id');
    }

    /**
     * Get the campaign for this donation.
     */
    public function campaign()
    {
        return $this->belongsTo(DonationCampaign::class, 'campaign_id');
    }

    /**
     * Check if donation is completed.
     */
    public function isCompleted(): bool
    {
        return $this->payment_status === 'completed';
    }

    /**
     * Check if donation is pending.
     */
    public function isPending(): bool
    {
        return $this->payment_status === 'pending';
    }

    /**
     * Check if donation failed.
     */
    public function isFailed(): bool
    {
        return $this->payment_status === 'failed';
    }

    /**
     * Check if donation is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->payment_status === 'refunded';
    }

    /**
     * Check if donation is recurring.
     */
    public function isRecurring(): bool
    {
        return $this->donation_type === 'recurring';
    }

    /**
     * Generate and assign receipt number.
     */
    public function generateReceiptNumber(): string
    {
        $receiptNumber = 'RCP-' . now()->format('Y') . '-' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
        $this->update([
            'receipt_number' => $receiptNumber,
            'receipt_issued_at' => now(),
        ]);
        return $receiptNumber;
    }

    /**
     * Mark donation as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'payment_status' => 'completed',
        ]);

        if (!$this->receipt_number) {
            $this->generateReceiptNumber();
        }
    }

    /**
     * Scope a query to only include completed donations.
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    /**
     * Scope a query to only include pending donations.
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope a query to only include failed donations.
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', 'failed');
    }

    /**
     * Scope a query to only include recurring donations.
     */
    public function scopeRecurring($query)
    {
        return $query->where('donation_type', 'recurring');
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope a query to filter by amount range.
     */
    public function scopeInAmountRange($query, $minAmount, $maxAmount)
    {
        return $query->whereBetween('amount', [$minAmount, $maxAmount]);
    }
}
