'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  Trash2, 
  CheckCircle, 
  AlertCircle,
  Clock,
  Image,
  File
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface ScholarshipApplication {
  id: number;
  scholarship: {
    title: string;
    amount: number;
  };
  status: string;
  submitted_at: string;
  documents?: {
    [key: string]: any;
  };
}

interface DocumentUpload {
  file: File | null;
  uploading: boolean;
  progress: number;
}

export default function DocumentManager() {
  const [applications, setApplications] = useState<ScholarshipApplication[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<ScholarshipApplication | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploads, setUploads] = useState<{ [key: string]: DocumentUpload }>({});

  const documentTypes = [
    { key: 'payment_receipt', label: 'Payment Receipt', required: true },
    { key: 'school_id_card', label: 'School ID Card', required: true },
    { key: 'academic_transcript', label: 'Academic Transcript', required: false },
    { key: 'recommendation_letter', label: 'Recommendation Letter', required: false }
  ];

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/scholarships/my-applications');
      if (response.success) {
        setApplications(response.data.data || []);
        if (response.data.data?.length > 0) {
          setSelectedApplication(response.data.data[0]);
        }
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
      toast.error('Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (documentType: string, file: File | null) => {
    setUploads(prev => ({
      ...prev,
      [documentType]: {
        file,
        uploading: false,
        progress: 0
      }
    }));
  };

  const handleUpload = async (documentType: string) => {
    if (!selectedApplication || !uploads[documentType]?.file) {
      toast.error('Please select a file to upload');
      return;
    }

    try {
      setUploads(prev => ({
        ...prev,
        [documentType]: {
          ...prev[documentType],
          uploading: true,
          progress: 0
        }
      }));

      const formData = new FormData();
      formData.append(documentType, uploads[documentType].file!);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploads(prev => ({
          ...prev,
          [documentType]: {
            ...prev[documentType],
            progress: Math.min(prev[documentType].progress + 10, 90)
          }
        }));
      }, 200);

      const response = await apiClient.post(
        `/scholarships/applications/${selectedApplication.id}/upload-documents`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      clearInterval(progressInterval);

      if (response.success) {
        setUploads(prev => ({
          ...prev,
          [documentType]: {
            ...prev[documentType],
            uploading: false,
            progress: 100
          }
        }));

        toast.success('Document uploaded successfully');
        
        // Refresh application data
        const updatedResponse = await apiClient.get(
          `/scholarships/applications/${selectedApplication.id}/documents`
        );
        
        if (updatedResponse.success) {
          setSelectedApplication(updatedResponse.data);
          // Update in applications list
          setApplications(prev => prev.map(app => 
            app.id === selectedApplication.id 
              ? { ...app, documents: updatedResponse.data.documents }
              : app
          ));
        }

        // Clear upload state after a delay
        setTimeout(() => {
          setUploads(prev => ({
            ...prev,
            [documentType]: {
              file: null,
              uploading: false,
              progress: 0
            }
          }));
        }, 2000);
      } else {
        throw new Error(response.message || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Failed to upload document:', error);
      toast.error(error.response?.data?.message || 'Failed to upload document');
      
      setUploads(prev => ({
        ...prev,
        [documentType]: {
          ...prev[documentType],
          uploading: false,
          progress: 0
        }
      }));
    }
  };

  const handleDownload = (documentUrl: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = documentUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <Image className="h-5 w-5 text-blue-500" />;
      default:
        return <File className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const hasDocument = (documentType: string) => {
    return selectedApplication?.documents?.[documentType];
  };

  const getDocumentUrl = (documentType: string) => {
    const doc = selectedApplication?.documents?.[documentType];
    return doc?.url || doc;
  };

  const getDocumentName = (documentType: string) => {
    const doc = selectedApplication?.documents?.[documentType];
    return doc?.name || `${documentType}.pdf`;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading applications...</p>
        </CardContent>
      </Card>
    );
  }

  if (applications.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No scholarship applications found</p>
          <p className="text-sm text-gray-400 mt-2">
            Apply for scholarships to manage your documents here
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Application Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Document Management</CardTitle>
          <CardDescription>
            Upload and manage documents for your scholarship applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label>Select Application</Label>
            <div className="grid gap-3">
              {applications.map((application) => (
                <div
                  key={application.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedApplication?.id === application.id
                      ? 'border-primary bg-primary/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedApplication(application)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{application.scholarship.title}</h4>
                      <p className="text-sm text-gray-600">
                        Amount: ₦{application.scholarship.amount.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        Submitted: {new Date(application.submitted_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge className={getStatusColor(application.status)}>
                      {application.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Upload/Management */}
      {selectedApplication && (
        <Card>
          <CardHeader>
            <CardTitle>Documents for {selectedApplication.scholarship.title}</CardTitle>
            <CardDescription>
              Upload required documents for your scholarship application
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {documentTypes.map((docType) => (
                <div key={docType.key} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="font-medium flex items-center">
                        {docType.label}
                        {docType.required && (
                          <span className="text-red-500 ml-1">*</span>
                        )}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {docType.required ? 'Required document' : 'Optional document'}
                      </p>
                    </div>
                    {hasDocument(docType.key) ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : docType.required ? (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>

                  {hasDocument(docType.key) ? (
                    // Document exists - show download/view options
                    <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                      <div className="flex items-center space-x-3">
                        {getFileIcon(getDocumentName(docType.key))}
                        <div>
                          <p className="font-medium">{getDocumentName(docType.key)}</p>
                          <p className="text-sm text-gray-500">Document uploaded</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(getDocumentUrl(docType.key), '_blank')}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(
                            getDocumentUrl(docType.key),
                            getDocumentName(docType.key)
                          )}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // No document - show upload interface
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <Input
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => handleFileSelect(docType.key, e.target.files?.[0] || null)}
                          disabled={uploads[docType.key]?.uploading}
                          className="flex-1"
                        />
                        <Button
                          onClick={() => handleUpload(docType.key)}
                          disabled={!uploads[docType.key]?.file || uploads[docType.key]?.uploading}
                          size="sm"
                        >
                          {uploads[docType.key]?.uploading ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Upload
                            </>
                          )}
                        </Button>
                      </div>

                      {uploads[docType.key]?.uploading && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Uploading...</span>
                            <span>{uploads[docType.key].progress}%</span>
                          </div>
                          <Progress value={uploads[docType.key].progress} />
                        </div>
                      )}

                      {uploads[docType.key]?.progress === 100 && !uploads[docType.key]?.uploading && (
                        <div className="flex items-center space-x-2 text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          <span className="text-sm">Upload completed successfully!</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Upload Guidelines */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Upload Guidelines</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Accepted formats: PDF, JPG, JPEG, PNG</li>
                <li>• Maximum file size: 5MB per document</li>
                <li>• Ensure documents are clear and readable</li>
                <li>• Required documents must be uploaded before application review</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
