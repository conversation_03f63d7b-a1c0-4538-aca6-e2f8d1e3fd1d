

<?php $__env->startSection('title', 'Blog Categories'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Blog Categories</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.blog.index')); ?>">Blog</a></li>
                        <li class="breadcrumb-item active">Categories</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Validation Errors:</strong>
            <ul class="mb-0">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tags me-2"></i>Categories List
                    </h6>
                </div>
                <div class="card-body">
                    <?php if(isset($categories) && count($categories) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Posts</th>
                                        <th>Color</th>
                                        <th>Status</th>
                                        <th>Sort Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($category['name']); ?></strong>
                                                <br><small class="text-muted"><?php echo e($category['slug']); ?></small>
                                            </td>
                                            <td><?php echo e($category['description'] ?? 'No description'); ?></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e($category['posts_count']); ?> posts</span>
                                            </td>
                                            <td>
                                                <div style="width: 20px; height: 20px; background-color: <?php echo e($category['color']); ?>; border-radius: 3px; display: inline-block;"></div>
                                                <small class="text-muted ms-1"><?php echo e($category['color']); ?></small>
                                            </td>
                                            <td>
                                                <?php if($category['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($category['sort_order']); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editCategoryModal<?php echo e($category['id']); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if($category['posts_count'] == 0): ?>
                                                    <form action="<?php echo e(route('admin.blog.categories.destroy.form', $category['id'])); ?>" method="POST" style="display: inline-block;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-danger" disabled title="Cannot delete category with posts">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>

                                        <!-- Edit Category Modal -->
                                        <div class="modal fade" id="editCategoryModal<?php echo e($category['id']); ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <form action="<?php echo e(route('admin.blog.categories.update.form', $category['id'])); ?>" method="POST">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PUT'); ?>
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Edit Category</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="form-group mb-3">
                                                                <label for="edit_name_<?php echo e($category['id']); ?>" class="form-label">Name <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="edit_name_<?php echo e($category['id']); ?>" name="name" value="<?php echo e($category['name']); ?>" required>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_description_<?php echo e($category['id']); ?>" class="form-label">Description</label>
                                                                <textarea class="form-control" id="edit_description_<?php echo e($category['id']); ?>" name="description" rows="3"><?php echo e($category['description']); ?></textarea>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_color_<?php echo e($category['id']); ?>" class="form-label">Color</label>
                                                                <input type="color" class="form-control form-control-color" id="edit_color_<?php echo e($category['id']); ?>" name="color" value="<?php echo e($category['color']); ?>">
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="edit_sort_order_<?php echo e($category['id']); ?>" class="form-label">Sort Order</label>
                                                                <input type="number" class="form-control" id="edit_sort_order_<?php echo e($category['id']); ?>" name="sort_order" value="<?php echo e($category['sort_order']); ?>" min="0">
                                                            </div>
                                                            <div class="form-check">
                                                                <input type="hidden" name="is_active" value="0">
                                                                <input class="form-check-input" type="checkbox" id="edit_is_active_<?php echo e($category['id']); ?>" name="is_active" value="1" <?php echo e($category['is_active'] ? 'checked' : ''); ?>>
                                                                <label class="form-check-label" for="edit_is_active_<?php echo e($category['id']); ?>">
                                                                    Active
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-primary">Update Category</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No categories found. Create your first category!</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus me-2"></i>Add New Category
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.blog.categories.store.form')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form-group mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo e(old('description')); ?></textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label for="color" class="form-label">Color</label>
                            <input type="color" class="form-control form-control-color" id="color" name="color" value="<?php echo e(old('color', '#6c757d')); ?>">
                        </div>
                        <div class="form-group mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', 0)); ?>" min="0">
                        </div>
                        <div class="form-check mb-3">
                            <input type="hidden" name="is_active" value="0">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/blog/categories.blade.php ENDPATH**/ ?>