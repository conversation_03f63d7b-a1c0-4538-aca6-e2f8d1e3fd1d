@import "tailwindcss";

/* Custom styles for the NGO platform */
body {
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
}

/* Dashboard specific styles */
.dashboard-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dashboard-stat-card {
    text-align: center;
    background-color: white;
    border: 1px solid #dcfce7;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.dashboard-stat-card:hover {
    transform: translateY(-0.5rem);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dashboard-button {
    color: white;
    background-color: #16a34a;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.dashboard-button:hover {
    background-color: #15803d;
}

.dashboard-sidebar {
    background-color: white;
    height: 100%;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.sidebar-menu-item {
    color: #374151;
    border-radius: 0.5rem;
    align-items: center;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease;
    display: flex;
}

.sidebar-menu-item:hover {
    background-color: #f3f4f6;
}

.sidebar-menu-item.active {
    color: #1d4ed8;
    background-color: #eff6ff;
    border-right: 4px solid #2563eb;
}

.gradient-bg {
    background: linear-gradient(to bottom right, #eff6ff, #e0e7ff, #f3e8ff);
}

.gradient-header {
    background: linear-gradient(90deg, #16a34a, #15803d);
}
