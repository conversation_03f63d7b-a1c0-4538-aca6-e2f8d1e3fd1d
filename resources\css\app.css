@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the NGO platform */
@layer base {
    body {
        font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }
}

@layer components {
    /* Dashboard specific styles */
    .dashboard-card {
        @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
    }

    .dashboard-stat-card {
        @apply text-center p-6 border border-green-100 hover:shadow-lg transition-all duration-300 rounded-2xl bg-white group hover:-translate-y-2;
    }

    .dashboard-button {
        @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
    }

    .dashboard-sidebar {
        @apply bg-white shadow-lg h-full;
    }

    .sidebar-menu-item {
        @apply flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors;
    }

    .sidebar-menu-item.active {
        @apply bg-blue-50 text-blue-700 border-r-4 border-blue-500;
    }
}

@layer utilities {
    .gradient-bg {
        @apply bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50;
    }

    .gradient-header {
        @apply bg-gradient-to-r from-green-600 to-green-700;
    }
}
