<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            // Add student_id for linking to students table (for institutional applications)
            if (!Schema::hasColumn('scholarship_applications', 'student_id')) {
                $table->foreignId('student_id')->nullable()->constrained('students')->onDelete('cascade')->after('user_id');
            }
            
            // Add school_id for institutional applications (Primary/Secondary)
            if (!Schema::hasColumn('scholarship_applications', 'school_id')) {
                $table->foreignId('school_id')->nullable()->constrained('partner_organizations')->onDelete('cascade')->after('student_id');
            }
            
            // Add grade_level for tracking which grade the application is for
            if (!Schema::hasColumn('scholarship_applications', 'grade_level')) {
                $table->enum('grade_level', [
                    'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                    'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                    'university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6'
                ])->nullable()->after('school_id');
            }
            
            // Add academic_year for tracking which academic year the application is for
            if (!Schema::hasColumn('scholarship_applications', 'academic_year')) {
                $table->string('academic_year', 10)->nullable()->after('grade_level'); // 2024/2025
            }
            
            // Add application_type to distinguish between individual and institutional applications
            if (!Schema::hasColumn('scholarship_applications', 'application_type')) {
                $table->enum('application_type', ['individual', 'institutional'])->default('individual')->after('academic_year');
            }
            
            // Add progression_from_grade to track if this is a progression application
            if (!Schema::hasColumn('scholarship_applications', 'progression_from_grade')) {
                $table->enum('progression_from_grade', [
                    'primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6',
                    'secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6',
                    'university_1', 'university_2', 'university_3', 'university_4', 'university_5', 'university_6'
                ])->nullable()->after('application_type');
            }
            
            // Add is_progression_application flag
            if (!Schema::hasColumn('scholarship_applications', 'is_progression_application')) {
                $table->boolean('is_progression_application')->default(false)->after('progression_from_grade');
            }
            
            // Add guardian information for primary/secondary applications
            if (!Schema::hasColumn('scholarship_applications', 'guardian_name')) {
                $table->string('guardian_name', 200)->nullable()->after('is_progression_application');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'guardian_phone')) {
                $table->string('guardian_phone', 20)->nullable()->after('guardian_name');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'guardian_email')) {
                $table->string('guardian_email', 100)->nullable()->after('guardian_phone');
            }
            
            // Add principal/school contact information for secondary applications
            if (!Schema::hasColumn('scholarship_applications', 'principal_name')) {
                $table->string('principal_name', 200)->nullable()->after('guardian_email');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'principal_phone')) {
                $table->string('principal_phone', 20)->nullable()->after('principal_name');
            }
            
            if (!Schema::hasColumn('scholarship_applications', 'principal_email')) {
                $table->string('principal_email', 100)->nullable()->after('principal_phone');
            }
            
            // Add matriculation number for university applications
            if (!Schema::hasColumn('scholarship_applications', 'matriculation_number')) {
                $table->string('matriculation_number', 50)->nullable()->after('principal_email');
            }
            
            // Add university/institution name for university applications
            if (!Schema::hasColumn('scholarship_applications', 'institution_name')) {
                $table->string('institution_name', 200)->nullable()->after('matriculation_number');
            }
            
            // Add course of study for university applications
            if (!Schema::hasColumn('scholarship_applications', 'course_of_study')) {
                $table->string('course_of_study', 200)->nullable()->after('institution_name');
            }
            
            // Add year of study for university applications
            if (!Schema::hasColumn('scholarship_applications', 'year_of_study')) {
                $table->integer('year_of_study')->nullable()->after('course_of_study');
            }
            
            // Add expected_graduation_year
            if (!Schema::hasColumn('scholarship_applications', 'expected_graduation_year')) {
                $table->string('expected_graduation_year', 10)->nullable()->after('year_of_study');
            }
            
            // Add previous_scholarship_id for tracking scholarship renewals/progressions
            if (!Schema::hasColumn('scholarship_applications', 'previous_scholarship_id')) {
                $table->foreignId('previous_scholarship_id')->nullable()->constrained('scholarship_applications')->onDelete('set null')->after('expected_graduation_year');
            }
            
            // Add renewal_count for tracking how many times this scholarship has been renewed
            if (!Schema::hasColumn('scholarship_applications', 'renewal_count')) {
                $table->integer('renewal_count')->default(0)->after('previous_scholarship_id');
            }
            
            // Indexes will be added separately if needed
            // Note: Some indexes may already exist from previous migrations
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scholarship_applications', function (Blueprint $table) {
            // Drop indexes
            try {
                $table->dropIndex(['student_id', 'academic_year']);
                $table->dropIndex(['school_id', 'grade_level']);
                $table->dropIndex(['application_type', 'status']);
                $table->dropIndex('app_progression_grade_idx');
                $table->dropIndex(['academic_year', 'grade_level']);
            } catch (Exception $e) {
                // Indexes might not exist
            }
            
            // Drop columns
            $columnsToRemove = [
                'renewal_count',
                'previous_scholarship_id',
                'expected_graduation_year',
                'year_of_study',
                'course_of_study',
                'institution_name',
                'matriculation_number',
                'principal_email',
                'principal_phone',
                'principal_name',
                'guardian_email',
                'guardian_phone',
                'guardian_name',
                'is_progression_application',
                'progression_from_grade',
                'application_type',
                'academic_year',
                'grade_level',
                'school_id',
                'student_id',
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('scholarship_applications', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
