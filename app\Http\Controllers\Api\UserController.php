<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

/**
 * @OA\Tag(
 *     name="User Profile",
 *     description="User profile management endpoints"
 * )
 */
class UserController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/profile",
     *     summary="Get authenticated user profile",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="User profile retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="short_id", type="string"),
     *                 @OA\Property(property="first_name", type="string"),
     *                 @OA\Property(property="last_name", type="string"),
     *                 @OA\Property(property="full_name", type="string"),
     *                 @OA\Property(property="email", type="string"),
     *                 @OA\Property(property="phone_number", type="string"),
     *                 @OA\Property(property="date_of_birth", type="string"),
     *                 @OA\Property(property="gender", type="string"),
     *                 @OA\Property(property="address", type="string"),
     *                 @OA\Property(property="city", type="string"),
     *                 @OA\Property(property="state", type="string"),
     *                 @OA\Property(property="country", type="string"),
     *                 @OA\Property(property="profile_picture_url", type="string"),
     *                 @OA\Property(property="qr_code_url", type="string"),
     *                 @OA\Property(property="role", type="string")
     *             ),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     )
     * )
     */
    public function profile()
    {
        $user = auth()->user();

        return response()->json([
            'user' => [
                'id' => $user->id,
                'short_id' => $user->short_id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'full_name' => $user->full_name,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'date_of_birth' => $user->date_of_birth?->format('Y-m-d'),
                'gender' => $user->gender,
                'address' => $user->address,
                'city' => $user->city,
                'state' => $user->state,
                'country' => $user->country,
                'profile_picture_url' => $user->profile_picture_url,
                'qr_code_url' => $user->qr_code_url,
                'role' => $user->role,
                'status' => $user->status,
                'email_verified_at' => $user->email_verified_at?->format('Y-m-d H:i:s'),
                'last_login_at' => $user->last_login_at?->format('Y-m-d H:i:s'),
                'preferences' => $user->preferences ? json_decode($user->preferences, true) : null,
                'created_at' => $user->created_at->format('Y-m-d H:i:s'),
                'roles' => $user->roles->pluck('name'),
                'permissions' => $user->getAllPermissions()->pluck('name')
            ],
            'success' => true
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile",
     *     summary="Update user profile",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe"),
     *             @OA\Property(property="phone_number", type="string", example="+234123456789"),
     *             @OA\Property(property="date_of_birth", type="string", format="date", example="1990-01-15"),
     *             @OA\Property(property="gender", type="string", enum={"male", "female", "other"}, example="male"),
     *             @OA\Property(property="address", type="string", example="123 Main Street"),
     *             @OA\Property(property="city", type="string", example="Lagos"),
     *             @OA\Property(property="state", type="string", example="Lagos State"),
     *             @OA\Property(property="country", type="string", example="Nigeria"),
     *             @OA\Property(property="preferences", type="object", example={"newsletter": true, "notifications": true})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Profile updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Profile updated successfully"),
     *             @OA\Property(property="user", type="object"),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'preferences' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $updateData = $request->only([
            'first_name', 'last_name', 'phone_number', 'date_of_birth',
            'gender', 'address', 'city', 'state', 'country'
        ]);

        if ($request->has('preferences')) {
            $updateData['preferences'] = json_encode($request->preferences);
        }

        $user->update($updateData);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'full_name' => $user->full_name,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'date_of_birth' => $user->date_of_birth?->format('Y-m-d'),
                'gender' => $user->gender,
                'address' => $user->address,
                'city' => $user->city,
                'state' => $user->state,
                'country' => $user->country,
                'profile_picture_url' => $user->profile_picture_url,
                'preferences' => $user->preferences ? json_decode($user->preferences, true) : null
            ],
            'success' => true
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/profile/avatar",
     *     summary="Upload user avatar",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="avatar",
     *                     type="string",
     *                     format="binary",
     *                     description="Avatar image file (JPEG, PNG, JPG, GIF, max 2MB)"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Avatar uploaded successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Avatar uploaded successfully"),
     *             @OA\Property(property="avatar_url", type="string", example="https://example.com/storage/avatars/user_123.jpg"),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function uploadAvatar(Request $request)
    {
        \Log::info('Avatar upload started', [
            'user_id' => auth()->id(),
            'user_email' => auth()->user()->email,
            'request_data' => $request->all(),
            'has_file' => $request->hasFile('avatar')
        ]);

        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048' // 2MB Max
        ]);

        if ($validator->fails()) {
            \Log::warning('Avatar upload validation failed', [
                'user_id' => auth()->id(),
                'errors' => $validator->errors()->toArray()
            ]);
            
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        try {
            $user = auth()->user();
            $image = $request->file('avatar');
            
            \Log::info('Processing avatar upload', [
                'user_id' => $user->id,
                'original_name' => $image->getClientOriginalName(),
                'file_size' => $image->getSize(),
                'mime_type' => $image->getMimeType()
            ]);

            // Delete old avatar if exists
            if ($user->profile_picture && Storage::disk('public')->exists($user->profile_picture)) {
                \Log::info('Deleting old avatar', ['old_path' => $user->profile_picture]);
                Storage::disk('public')->delete($user->profile_picture);
            }

            $filename = 'user_' . $user->id . '_' . time() . '.jpg';
            
            // Create ImageManager instance with GD driver
            $manager = new ImageManager(new Driver());
            
            // Process image with new Intervention Image 3.x syntax
            $processedImage = $manager->read($image->getRealPath())
                                     ->cover(300, 300) // Square avatar, crop to fit
                                     ->toJpeg(85); // 85% quality

            $path = 'avatars/' . $filename;
            Storage::disk('public')->put($path, $processedImage);

            // Generate full URL for profile picture
            $avatarUrl = Storage::disk('public')->url($path);
            
            \Log::info('Avatar processed and saved', [
                'user_id' => $user->id,
                'filename' => $filename,
                'path' => $path,
                'avatar_url' => $avatarUrl
            ]);
            
            $user->update([
                'profile_picture' => $path,
                'profile_picture_url' => $avatarUrl
            ]);

            \Log::info('Avatar upload completed successfully', [
                'user_id' => $user->id,
                'final_avatar_url' => $user->fresh()->profile_picture_url
            ]);

            return response()->json([
                'message' => 'Avatar uploaded successfully',
                'avatar_url' => $user->profile_picture_url,
                'success' => true
            ]);

        } catch (\Exception $e) {
            \Log::error('Avatar upload failed', [
                'user_id' => auth()->id(),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to upload avatar: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/profile/password",
     *     summary="Change user password",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"current_password", "new_password", "new_password_confirmation"},
     *             @OA\Property(property="current_password", type="string", example="current123"),
     *             @OA\Property(property="new_password", type="string", example="newpassword123"),
     *             @OA\Property(property="new_password_confirmation", type="string", example="newpassword123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Password changed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Password changed successfully"),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error or incorrect current password"
     *     )
     * )
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $user = auth()->user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'message' => 'Current password is incorrect',
                'errors' => [
                    'current_password' => ['The current password is incorrect.']
                ],
                'success' => false
            ], 422);
        }

        // Check if new password is different from current
        if (Hash::check($request->new_password, $user->password)) {
            return response()->json([
                'message' => 'New password must be different from current password',
                'errors' => [
                    'new_password' => ['New password must be different from current password.']
                ],
                'success' => false
            ], 422);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'message' => 'Password changed successfully',
            'success' => true
        ]);
    }

    /**
     * Get user activity summary
     */
    public function activitySummary()
    {
        $user = auth()->user();

        $summary = [
            'donations' => [
                'total_count' => $user->donations()->count(),
                'total_amount' => $user->donations()->where('payment_status', 'completed')->sum('amount'),
                'recent_donations' => $user->donations()
                                         ->where('payment_status', 'completed')
                                         ->orderBy('created_at', 'desc')
                                         ->limit(5)
                                         ->get(['id', 'amount', 'currency', 'donation_type', 'created_at'])
            ],
            'volunteer_hours' => 0,
            'events_attended' => 0,
            'blog_comments' => $user->blogComments()->where('status', 'approved')->count(),
            'scholarships_applied' => 0
        ];

        // Get volunteer data if user is a volunteer
        if ($user->isVolunteer() && $user->volunteer) {
            $summary['volunteer_hours'] = $user->volunteer->hours_logged;
            $summary['events_attended'] = $user->eventRegistrations()
                                               ->where('attendance_status', 'attended')
                                               ->count();
        }

        // Get scholarship applications count
        $summary['scholarships_applied'] = $user->scholarshipApplications()->count();

        return response()->json([
            'summary' => $summary,
            'success' => true
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/profile/generate-qr",
     *     summary="Generate or regenerate user QR code",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="QR code generated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="QR code generated successfully"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="qr_code_url", type="string"),
     *                 @OA\Property(property="short_id", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function generateQrCode()
    {
        $user = auth()->user();
        
        // Generate new QR code
        $user->qr_code_url = $user->generateQrCodeUrl();
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'QR code generated successfully',
            'data' => [
                'qr_code_url' => $user->qr_code_url,
                'short_id' => $user->short_id
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/profile/id-card",
     *     summary="Get user ID card data",
     *     tags={"User Profile"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="ID card data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="short_id", type="string"),
     *                 @OA\Property(property="full_name", type="string"),
     *                 @OA\Property(property="email", type="string"),
     *                 @OA\Property(property="phone_number", type="string"),
     *                 @OA\Property(property="role", type="string"),
     *                 @OA\Property(property="qr_code_url", type="string"),
     *                 @OA\Property(property="profile_picture_url", type="string"),
     *                 @OA\Property(property="organization", type="string", example="HLTKKQ Foundation"),
     *                 @OA\Property(property="member_since", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function getIdCard()
    {
        $user = auth()->user();

        return response()->json([
            'success' => true,
            'data' => [
                'short_id' => $user->short_id,
                'full_name' => $user->full_name,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'role' => ucfirst($user->role),
                'qr_code_url' => $user->qr_code_url,
                'profile_picture_url' => $user->profile_picture_url,
                'organization' => 'HLTKKQ Foundation',
                'member_since' => $user->created_at->format('F Y'),
                'address' => $user->address ? "{$user->city}, {$user->state}, {$user->country}" : null
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/settings",
     *     summary="Get public application settings",
     *     tags={"Settings"},
     *     @OA\Response(
     *         response=200,
     *         description="Public settings retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="app_name", type="string", example="Laravel NGO"),
     *                 @OA\Property(property="app_logo", type="string", example="http://localhost:8000/storage/logos/logo.png"),
     *                 @OA\Property(property="site_description", type="string"),
     *                 @OA\Property(property="contact_email", type="string"),
     *                 @OA\Property(property="contact_phone", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function getPublicSettings()
    {
        $settings = \App\Models\Setting::getPublicSettings();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
