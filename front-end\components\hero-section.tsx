"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowRight, Play } from "lucide-react"
import { useInView } from "react-intersection-observer"

const heroImages = [
  {
    src: "/placeholder.svg?height=600&width=400&text=Students+in+Classroom&bg=22c55e&color=ffffff",
    fallback: "/placeholder.svg?height=600&width=400&text=📚+Education&bg=16a34a&color=ffffff",
    alt: "Students in classroom receiving quality education",
    caption: "Empowering minds through quality education",
    icon: "📚",
  },
  {
    src: "/placeholder.svg?height=600&width=400&text=Scholarship+Ceremony&bg=f59e0b&color=ffffff",
    fallback: "/placeholder.svg?height=600&width=400&text=🎓+Scholarships&bg=d97706&color=ffffff",
    alt: "Scholarship award ceremony with students and founder",
    caption: "Celebrating academic achievements",
    icon: "🎓",
  },
  {
    src: "/placeholder.svg?height=600&width=400&text=Community+Outreach&bg=059669&color=ffffff",
    fallback: "/placeholder.svg?height=600&width=400&text=🤝+Community&bg=047857&color=ffffff",
    alt: "Community literacy program in rural Nigeria",
    caption: "Building stronger communities together",
    icon: "🤝",
  },
  {
    src: "/placeholder.svg?height=600&width=400&text=School+Supplies&bg=7c3aed&color=ffffff",
    fallback: "/placeholder.svg?height=600&width=400&text=📖+Supplies&bg=6d28d9&color=ffffff",
    alt: "Distribution of school supplies and textbooks",
    caption: "Providing essential learning materials",
    icon: "📖",
  },
]

export function HeroSection() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({})
  const [allImagesLoaded, setAllImagesLoaded] = useState(false)
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  })
  const [imageErrors, setImageErrors] = useState<Record<number, boolean>>({})

  const handleImageLoad = (index: number) => {
    setImagesLoaded((prev) => {
      const newState = { ...prev, [index]: true }
      const loadedCount = Object.values(newState).filter(Boolean).length
      if (loadedCount === heroImages.length) {
        setAllImagesLoaded(true)
      }
      return newState
    })
  }

  const handleImageError = (index: number) => {
    setImageErrors((prev) => ({ ...prev, [index]: true }))
    handleImageLoad(index) // Still count as loaded to proceed with carousel
  }

  // Auto-rotate images only when all are loaded
  useEffect(() => {
    if (!inView || !allImagesLoaded) return

    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [inView, allImagesLoaded])

  return (
    <section
      ref={ref}
      className="relative min-h-[90vh] overflow-hidden bg-gradient-to-br from-green-900 via-green-800 to-green-950 py-20"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid opacity-20"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-amber-400/20 rounded-full blur-xl animate-float"></div>
      <div
        className="absolute bottom-32 right-16 w-32 h-32 bg-green-300/20 rounded-full blur-2xl animate-float"
        style={{ animationDelay: "2s" }}
      ></div>
      <div
        className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg animate-float"
        style={{ animationDelay: "1s" }}
      ></div>

      <div className="container relative h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[70vh]">
          {/* Content Section */}
          <div className="space-y-8 z-10">
            <div className="inline-flex items-center gap-2 rounded-full bg-green-800/70 px-4 py-2 text-sm text-green-100 backdrop-blur-sm animate-fade-in-up border border-green-700/50">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
              Transforming Lives Since 2015
            </div>

            <h1 className="text-4xl font-bold tracking-tighter text-white sm:text-5xl md:text-6xl lg:text-7xl animate-fade-in-up leading-tight">
              Empowering Nigeria Through{" "}
              <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
                Education
              </span>
            </h1>

            <p
              className="text-lg text-green-100 md:text-xl max-w-xl animate-fade-in-up leading-relaxed"
              style={{ animationDelay: "0.2s" }}
            >
              Providing scholarships, school supplies, and hope to students, underprivileged people, and those in need since 2015. Together, we're building a brighter future for Nigeria.
            </p>

            {/* Impact Stats Preview */}
            <div className="grid grid-cols-2 gap-4 animate-fade-in-up" style={{ animationDelay: "0.3s" }}>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-2xl font-bold text-amber-400">1,200+</div>
                <div className="text-green-200 text-sm">Students Supported</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="text-2xl font-bold text-amber-400">₦12M</div>
                <div className="text-green-200 text-sm">In Scholarships</div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 animate-fade-in-up" style={{ animationDelay: "0.4s" }}>
              <Link href="/donate">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-green-950 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 rounded-full font-semibold px-8 py-4 text-lg group"
                >
                  Donate Now
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </Link>
              <Link href="/students">
                <Button
                  size="lg"
                  variant="outline"
                  className="text-white border-white/50 hover:bg-white/10 backdrop-blur-sm transition-all duration-300 rounded-full font-semibold px-8 py-4 text-lg group border-2"
                >
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  Meet Our Students
                </Button>
              </Link>
            </div>
          </div>

          {/* Images Section */}
          <div className="relative h-full flex items-center justify-center">
            <div className="grid grid-cols-2 gap-4 max-w-lg">
              {heroImages.map((image, index) => (
                <div
                  key={index}
                  className={`relative overflow-hidden rounded-2xl border-4 shadow-2xl transition-all duration-700 cursor-pointer group ${
                    index === currentImageIndex
                      ? "scale-110 z-10 border-amber-500/70 shadow-amber-500/20"
                      : "scale-100 opacity-80 hover:opacity-100 border-white/20 hover:border-white/40"
                  }`}
                  style={{
                    height: index % 2 === 0 ? "280px" : "220px",
                    transform: `scale(${index === currentImageIndex ? 1.1 : 1}) ${
                      index === currentImageIndex
                        ? "translateY(0)"
                        : index % 2 === 0
                          ? "translateY(-10px)"
                          : "translateY(10px)"
                    }`,
                  }}
                  onClick={() => setCurrentImageIndex(index)}
                >
                  {/* Loading State */}
                  {!imagesLoaded[index] && !imageErrors[index] && (
                    <div className="absolute inset-0 bg-green-800/50 animate-pulse flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-8 h-8 border-2 border-amber-400 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                        <p className="text-white text-xs">Loading...</p>
                      </div>
                    </div>
                  )}

                  {/* Fallback Placeholder for Error State */}
                  {imageErrors[index] && (
                    <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-800 flex flex-col items-center justify-center text-white">
                      <div className="text-4xl mb-2">{image.icon}</div>
                      <div className="text-center px-4">
                        <h4 className="font-semibold text-sm mb-1">Kofa Foundation</h4>
                        <p className="text-xs opacity-90 leading-tight">{image.caption}</p>
                      </div>
                    </div>
                  )}

                  {/* Main Image */}
                  {!imageErrors[index] && (
                    <Image
                      src={image.src || "/placeholder.svg"}
                      alt={image.alt}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className={`object-cover transition-all duration-700 group-hover:scale-110 ${
                        imagesLoaded[index] ? "opacity-100" : "opacity-0"
                      }`}
                      onLoad={() => handleImageLoad(index)}
                      onError={() => handleImageError(index)}
                      priority={index < 2}
                    />
                  )}

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                    <p className="text-white text-sm font-medium leading-tight">{image.caption}</p>
                  </div>

                  {/* Active Indicator */}
                  {index === currentImageIndex && (
                    <div className="absolute top-3 right-3 w-3 h-3 bg-amber-400 rounded-full animate-pulse shadow-lg"></div>
                  )}

                  {/* Educational Badge */}
                  <div className="absolute top-3 left-3 bg-green-600/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs text-white font-medium">
                    Education
                  </div>
                </div>
              ))}
            </div>

            {/* Image Navigation Dots */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImageIndex ? "bg-amber-400 scale-125" : "bg-white/50 hover:bg-white/80"
                  }`}
                  aria-label={`View image ${index + 1}: ${heroImages[index].caption}`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
