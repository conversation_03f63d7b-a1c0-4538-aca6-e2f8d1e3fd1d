(()=>{var e={};e.id=316,e.ids=[316],e.modules={137:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var a=r(65239),t=r(48088),l=r(88170),i=r.n(l),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d={children:["",{children:["scholarships",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81967)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/scholarships/[id]/page",pathname:"/scholarships/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1910:(e,s,r)=>{Promise.resolve().then(r.bind(r,66837))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7430:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},10022:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38862:(e,s,r)=>{Promise.resolve().then(r.bind(r,81967))},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n});var a=r(60687),t=r(43210),l=r(96241);let i=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59821:(e,s,r)=>{"use strict";r.d(s,{E:()=>n});var a=r(60687);r(43210);var t=r(24224),l=r(96241);let i=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...r}){return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:s}),e),...r})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66837:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>C});var a=r(60687),t=r(43210),l=r(16189),i=r(85814),n=r.n(i),c=r(55192),d=r(24934),o=r(59821),x=r(82080),p=r(7430),m=r(27351),h=r(93613),u=r(28559),g=r(64398),j=r(23928),f=r(40228),y=r(41312),v=r(70334),N=r(58869),b=r(5336),w=r(10022),A=r(48730),k=r(59556);let _={primary:{icon:x.A,title:"Primary School",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700",borderColor:"border-blue-200"},secondary:{icon:p.A,title:"Secondary School",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700",borderColor:"border-green-200"},university:{icon:m.A,title:"University",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700",borderColor:"border-purple-200"}};function C(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),[r,i]=(0,t.useState)(null),[x,p]=(0,t.useState)(!0),[m,C]=(0,t.useState)(null),q=async e=>{try{p(!0),C(null);let s=await k.uE.getPublicScholarship(e);if(s.success&&s.data)i(s.data);else throw Error(s.message||"Scholarship not found")}catch(e){console.error("Error fetching scholarship:",e),C("Failed to load scholarship details. Please try again later.")}finally{p(!1)}},R=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});if(x)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading scholarship details..."})]})})});if(m||!r)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Scholarship"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:m||"Scholarship not found"}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsxs)(d.$,{onClick:()=>s.back(),variant:"outline",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Go Back"]}),(0,a.jsx)(d.$,{onClick:()=>q(e.id),children:"Try Again"})]})]})})});let P=_[r.category],Z=P.icon,M=r.max_applicants?r.max_applicants-r.current_applicants:null,S="active"===r.status||"open"===r.status;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("section",{className:"py-8 bg-white dark:bg-gray-900 border-b",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>s.back(),className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Back to Scholarships"]})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[(0,a.jsx)("div",{className:`p-3 rounded-lg ${P.bgColor}`,children:(0,a.jsx)(Z,{className:`h-8 w-8 ${P.textColor}`})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:["closed"===r.status||"inactive"===r.status?(0,a.jsx)(o.E,{variant:"secondary",children:"Closed"}):r.max_applicants&&r.max_applicants-r.current_applicants<=5?(0,a.jsx)(o.E,{variant:"destructive",children:"Few Spots Left"}):r.is_featured?(0,a.jsx)(o.E,{variant:"default",className:"bg-amber-500",children:"Featured"}):(0,a.jsx)(o.E,{variant:"default",className:"bg-green-500",children:"Open"}),(0,a.jsx)(o.E,{variant:"outline",className:"rounded-full",children:P.title}),r.is_featured&&(0,a.jsxs)(o.E,{variant:"outline",className:"text-amber-600 border-amber-200",children:[(0,a.jsx)(g.A,{className:"h-3 w-3 mr-1"}),"Featured"]})]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:r.title}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-lg",children:r.description})]})]})}),(0,a.jsx)("div",{className:"lg:w-80",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:"text-lg",children:"Quick Info"})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Award Amount"}),(0,a.jsxs)("p",{className:"font-semibold text-green-600",children:["₦",r.amount.toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Application Deadline"}),(0,a.jsx)("p",{className:"font-semibold",children:R(r.application_deadline)})]})]}),null!==M&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Applicants"}),(0,a.jsxs)("p",{className:"font-semibold",children:[r.current_applicants,"/",r.max_applicants]})]})]}),(0,a.jsx)("hr",{className:"my-4"}),(0,a.jsxs)(d.$,{className:"w-full bg-green-600 hover:bg-green-700 text-white",disabled:!S||null!==M&&M<=0,size:"lg",onClick:()=>{window.location.href=`http://localhost:8000/scholarship-application?scholarship_id=${r.id}&category=${r.category}`},children:[S?null!==M&&M<=0?"No Spots Available":"Apply Now":"Application Closed",S&&(null===M||M>0)&&(0,a.jsx)(v.A,{className:"ml-2 h-5 w-5"})]})]})]})})]})]})}),(0,a.jsx)("section",{className:"py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[r.category_instructions&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Application Instructions"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:r.category_instructions.title}),(0,a.jsx)("p",{className:"text-blue-800 dark:text-blue-200 text-sm mb-2",children:r.category_instructions.age_range}),(0,a.jsxs)("p",{className:"text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("strong",{children:"Who fills this form:"})," ",r.category_instructions.filled_by]}),(0,a.jsx)("p",{className:"text-blue-700 dark:text-blue-300 mt-2",children:r.category_instructions.instruction})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-semibold mb-2",children:"Required Information:"}),(0,a.jsx)("ul",{className:"space-y-1",children:r.category_instructions.required_info&&Array.isArray(r.category_instructions.required_info)&&r.category_instructions.required_info.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),e]},s))})]})]})]}),(r.eligibility_criteria||r.requirements)&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"Eligibility & Requirements"]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:r.eligibility_criteria||r.requirements})})]}),r.custom_fields&&r.custom_fields.length>0&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),"Application Form Fields"]}),(0,a.jsx)(c.BT,{children:"Preview of the information you'll need to provide"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.custom_fields&&Array.isArray(r.custom_fields)&&r.custom_fields.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:e.field_name}),e.is_required&&(0,a.jsx)(o.E,{variant:"outline",className:"text-xs",children:"Required"})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-600 capitalize",children:[e.field_type.replace("_"," ")," field"]}),e.field_options&&e.field_options.length>0&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Options: ",e.field_options.join(", ")]})]},e.id))})})]}),r.documents_required&&Array.isArray(r.documents_required)&&r.documents_required.length>0&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),"Required Documents"]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("ul",{className:"space-y-2",children:r.documents_required.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),e]},s))})})]}),r.application_instructions&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Additional Instructions"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-line",children:r.application_instructions})})]}),r.terms_conditions&&(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Terms & Conditions"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-line text-sm",children:r.terms_conditions})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-5 w-5"}),"Timeline"]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:"Applications Open"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Now accepting applications"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-amber-500 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:"Application Deadline"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:R(r.application_deadline)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:"Review Period"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Applications under review"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:"Results Announced"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Winners notified"})]})]})]})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Need Help?"})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Have questions about this scholarship or the application process?"}),(0,a.jsx)(n(),{href:"/contact",children:(0,a.jsx)(d.$,{variant:"outline",className:"w-full",children:"Contact Support"})})]})]})]})]})})})]})}},70334:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},81967:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\page.tsx","default")},93613:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[555,702],()=>r(137));module.exports=a})();