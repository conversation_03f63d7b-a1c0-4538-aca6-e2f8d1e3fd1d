<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\ScholarshipApplication;
use App\Models\Student;
use App\Models\Volunteer;
use App\Models\Event;
use App\Models\Program;
use App\Models\Donation;

class DataOwnershipAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $model
     * @param  string  $parameter
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $model = null, string $parameter = 'id')
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
                'error_code' => 'AUTH_REQUIRED'
            ], 401);
        }

        $user = Auth::user();

        // Admin users have access to all data
        if ($user->role === 'admin') {
            return $next($request);
        }

        // If no model specified, just check authentication
        if (!$model) {
            return $next($request);
        }

        $resourceId = $request->route($parameter);
        if (!$resourceId) {
            return $next($request);
        }

        // Check ownership based on model type
        if (!$this->hasDataAccess($user, $model, $resourceId)) {
            $this->logUnauthorizedAccess($request, $user, $model, $resourceId);
            return response()->json([
                'success' => false,
                'message' => 'Access denied. You can only access your own data.',
                'error' => 'Forbidden',
                'error_code' => 'DATA_ACCESS_DENIED'
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if user has access to specific data
     */
    private function hasDataAccess($user, string $model, $resourceId): bool
    {
        switch ($model) {
            case 'scholarship_application':
                return $this->checkScholarshipApplicationAccess($user, $resourceId);
            
            case 'student':
                return $this->checkStudentAccess($user, $resourceId);
            
            case 'volunteer':
                return $this->checkVolunteerAccess($user, $resourceId);
            
            case 'event':
                return $this->checkEventAccess($user, $resourceId);
            
            case 'program':
                return $this->checkProgramAccess($user, $resourceId);
            
            case 'donation':
                return $this->checkDonationAccess($user, $resourceId);
            
            default:
                return false;
        }
    }

    /**
     * Check scholarship application access
     */
    private function checkScholarshipApplicationAccess($user, $applicationId): bool
    {
        $application = ScholarshipApplication::find($applicationId);
        if (!$application) {
            return false;
        }

        // Individual students can access their own applications
        if ($user->role === 'user' && $application->user_id === $user->id) {
            return true;
        }

        // Partner organizations can access applications from their students
        if ($user->role === 'partner_organization') {
            // Check if application is from a student associated with this partner
            if ($application->student && $application->student->school_id === $user->partner_organization_id) {
                return true;
            }
            // Check if application was created by this partner organization
            if ($application->created_by === $user->id) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check student access
     */
    private function checkStudentAccess($user, $studentId): bool
    {
        $student = Student::find($studentId);
        if (!$student) {
            return false;
        }

        // Individual users can access their own student profile
        if ($user->role === 'user' && $student->user_id === $user->id) {
            return true;
        }

        // Partner organizations can access their students
        if ($user->role === 'partner_organization' && $student->school_id === $user->partner_organization_id) {
            return true;
        }

        return false;
    }

    /**
     * Check volunteer access
     */
    private function checkVolunteerAccess($user, $volunteerId): bool
    {
        $volunteer = Volunteer::find($volunteerId);
        if (!$volunteer) {
            return false;
        }

        // Users can access their own volunteer profile
        return $volunteer->user_id === $user->id;
    }

    /**
     * Check event access
     */
    private function checkEventAccess($user, $eventId): bool
    {
        $event = Event::find($eventId);
        if (!$event) {
            return false;
        }

        // Event organizers can access their events
        if ($event->organizer_id === $user->id) {
            return true;
        }

        // Users can access events they're registered for
        if ($user->registeredEvents()->where('event_id', $eventId)->exists()) {
            return true;
        }

        // Partner organizations can access events they created or are involved in
        if ($user->role === 'partner_organization' && $event->created_by === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Check program access
     */
    private function checkProgramAccess($user, $programId): bool
    {
        $program = Program::find($programId);
        if (!$program) {
            return false;
        }

        // Program creators can access their programs
        if ($program->created_by === $user->id) {
            return true;
        }

        // Partner organizations can access programs they're involved in
        if ($user->role === 'partner_organization') {
            // Check if user's organization is associated with the program
            return $program->partnerOrganizations()->where('partner_organization_id', $user->partner_organization_id)->exists();
        }

        return false;
    }

    /**
     * Check donation access
     */
    private function checkDonationAccess($user, $donationId): bool
    {
        $donation = Donation::find($donationId);
        if (!$donation) {
            return false;
        }

        // Users can access their own donations
        return $donation->user_id === $user->id;
    }

    /**
     * Log unauthorized access attempts
     */
    private function logUnauthorizedAccess(Request $request, $user, string $model, $resourceId): void
    {
        Log::channel('security')->warning('Unauthorized data access attempt', [
            'user_id' => $user->id,
            'user_role' => $user->role,
            'user_email' => $user->email,
            'model' => $model,
            'resource_id' => $resourceId,
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
