# Laravel API Integration Guide

## Overview

This document outlines the integration between the Next.js frontend and Laravel backend API for the HLTKKQ Foundation project, including support for different user types: Students, Partners, and Volunteers.

## Configuration

### Environment Variables

The following environment variables are configured in `.env.local`:

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

Make sure your Laravel backend is running on `http://localhost:8000` for local development.

## User Types and Registration Flow

The registration system supports three different user types, each with specific data collection and handling:

### 1. **Student Registration**
- **Purpose**: Apply for scholarships and access educational resources
- **Fields Collected**:
  - Basic user info (name, email, phone, address, DOB)
  - Educational Institution
  - Course of Study
  - Year of Study
  - Student ID Number (optional)
- **Backend Handling**: 
  - Creates user with role 'user'
  - Stores additional data in user preferences
  - Enables scholarship application features

### 2. **Partner Organization Registration**
- **Purpose**: Collaborate to expand educational opportunities
- **Fields Collected**:
  - Basic user info (name, email, phone, address, DOB)
  - Organization Name
  - Organization Type (NGO, Corporate, Government, etc.)
  - Your Position
  - Organization Website (optional)
- **Backend Handling**:
  - Creates user with role 'user'
  - Stores additional data in user preferences
  - Flags for partnership program access

### 3. **Volunteer Registration**
- **Purpose**: Contribute time and skills to support the mission
- **Fields Collected**:
  - Basic user info (name, email, phone, address, DOB)
  - Skills & Expertise
  - Availability
  - Previous Volunteer Experience (optional)
  - Motivation for volunteering
- **Backend Handling**:
  - Creates user with role 'user'
  - Updates user role to 'volunteer'
  - Creates volunteer application record
  - Sets application status to 'pending'

## API Integration

### Authentication

The application uses Laravel Sanctum for authentication with Bearer tokens.

#### Registration Flow
- **Endpoint**: `POST /api/v1/register`
- **Implementation**: `front-end/app/auth/register/page.tsx`
- **API Client**: `apiClient.register()`

#### Multi-step Registration Process

1. **User Type Selection**: User selects Student, Partner, or Volunteer
2. **Form Collection**: Displays relevant fields based on user type
3. **Registration Submission**: 
   - Creates basic user account
   - Handles type-specific data appropriately
   - Provides user feedback based on type

#### Login
- **Endpoint**: `POST /api/v1/login`
- **Implementation**: `front-end/app/auth/login/page.tsx`
- **API Client**: `apiClient.login()`

#### Response Structure
Registration and login return the following structure:
```json
{
  "success": true,
  "message": "Registration/Login successful",
  "data": {
    "user": {...},
    "access_token": "token_here",
    "token_type": "Bearer"
  }
}
```

### API Client

A centralized API client has been created at `front-end/lib/api.ts` that provides:

- **Automatic authentication**: Adds Bearer token to requests
- **Error handling**: Handles 401 responses and redirects to login
- **Type-safe methods** for all API endpoints
- **Helper functions** for authentication state management
- **Multi-step registration** handling for different user types

#### Key Methods

```typescript
// Authentication
apiClient.login(email, password)
apiClient.register({
  first_name, last_name, email, password, password_confirmation,
  phone_number, address, date_of_birth,
  user_type: 'student' | 'partner' | 'volunteer',
  additional_data: {...} // Type-specific data
})
apiClient.logout()
apiClient.getUser()

// Profile Management
apiClient.getProfile()
apiClient.updateProfile(profileData)
apiClient.updateUserPreferences(preferences)

// Type-specific Applications
apiClient.applyAsVolunteer(applicationData)
apiClient.applyForScholarship(scholarshipId, applicationData)
```

### Data Storage Strategy

#### Students and Partners
- **User Record**: Standard user with role 'user'
- **Additional Data**: Stored in `preferences` JSON field
  ```json
  {
    "user_type": "student",
    "profile_data": {
      "institution": "University of Lagos",
      "course": "Computer Science",
      "year_of_study": "3",
      "student_id": "STU/2024/001"
    },
    "profile_completed": true
  }
  ```

#### Volunteers
- **User Record**: Role updated to 'volunteer'
- **Volunteer Record**: Separate table with application details
- **Application Status**: 'pending', 'approved', or 'rejected'

### Token and State Management

Tokens and user data are stored in localStorage:
- `authToken`: The authentication token
- `user`: The user data object
- `userType`: The selected user type during registration

#### Helper Functions

```typescript
import { isAuthenticated, getCurrentUser, logout } from '@/lib/api'

// Check if user is logged in
if (isAuthenticated()) {
  // User is authenticated
}

// Get current user data
const user = getCurrentUser()

// Get user type from storage
const userType = localStorage.getItem('userType')

// Logout user
await logout()
```

## Testing the Integration

### Prerequisites

1. **Laravel Backend**: Make sure your Laravel backend is running on `http://localhost:8000`
2. **Database**: Ensure your Laravel database is set up and migrated
3. **CORS**: Verify CORS is configured to allow requests from `http://localhost:3000`

### Testing Different User Types

#### Testing Student Registration
1. Navigate to `http://localhost:3000/auth/register`
2. Select "Student" user type
3. Fill out all required fields including:
   - Basic personal information
   - Educational institution
   - Course of study
   - Year of study
4. Submit and verify in Laravel admin that:
   - User is created with role 'user'
   - Preferences contain student data

#### Testing Partner Registration
1. Select "Partner Organization" user type
2. Fill out organization details
3. Verify user preferences contain organization data

#### Testing Volunteer Registration
1. Select "Volunteer" user type
2. Fill out volunteer-specific fields
3. Verify in Laravel admin that:
   - User role is updated to 'volunteer'
   - Volunteer record is created
   - Application status is 'pending'

### Testing Steps

1. **Start the servers**:
   ```bash
   # Laravel backend (in laravel-api-ngo directory)
   php artisan serve
   
   # Next.js frontend (in front-end directory)
   npm run dev
   ```

2. **Test Each User Type**:
   - Complete registration for each type
   - Verify data storage in Laravel database
   - Test login with created accounts

3. **Verify Token Storage**:
   - Open browser dev tools → Application → Local Storage
   - Check for `authToken`, `user`, and `userType` entries

### Backend Verification

After registration, verify in your Laravel admin panel:

```sql
-- Check user creation
SELECT * FROM users WHERE email = '<EMAIL>';

-- Check volunteer applications (for volunteer type)
SELECT * FROM volunteers WHERE user_id = [user_id];

-- Check user preferences (for student/partner types)
SELECT preferences FROM users WHERE id = [user_id];
```

## Troubleshooting

### Common Issues

1. **User Type Data Not Saved**:
   - Check that the preferences field is properly configured in Laravel
   - Verify the profile update endpoint is working

2. **Volunteer Application Failed**:
   - Ensure volunteer table exists and is properly migrated
   - Check the volunteer application endpoint permissions

3. **TypeScript Errors**:
   - Ensure user type is selected before form submission
   - Verify type definitions match between frontend and backend

## Security Considerations

1. **Data Validation**: All user input is validated on both client and server
2. **Type Safety**: TypeScript ensures type consistency across the application
3. **Role-based Access**: Different user types have appropriate access levels
4. **Data Isolation**: User type specific data is properly encapsulated

## Next Steps

With the multi-user type registration complete, you can now:

1. **Implement role-based routing** in Next.js
2. **Create user type specific dashboards**
3. **Add profile completion flows**
4. **Implement scholarship, partnership, and volunteer workflows**
5. **Add proper form validation** with better UX

## File Structure

```
front-end/
├── app/
│   └── auth/
│       ├── login/page.tsx          # Login page with API integration
│       └── register/page.tsx       # Multi-user type registration
├── lib/
│   └── api.ts                      # Enhanced API client with user types
├── .env.local                      # Environment configuration
└── INTEGRATION_GUIDE.md            # This guide
``` 