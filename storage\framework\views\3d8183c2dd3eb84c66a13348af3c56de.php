<?php $__env->startSection('title', 'Student Details - ' . $student->name); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <?php if (isset($component)) { $__componentOriginal060abe2a9b4511e378911474e77b046d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal060abe2a9b4511e378911474e77b046d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard.sidebar','data' => ['userType' => 'partner','menuItems' => [
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userType' => 'partner','menuItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students', 'active' => true],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/scholarships'],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $attributes = $__attributesOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__attributesOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal060abe2a9b4511e378911474e77b046d)): ?>
<?php $component = $__componentOriginal060abe2a9b4511e378911474e77b046d; ?>
<?php unset($__componentOriginal060abe2a9b4511e378911474e77b046d); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2"><?php echo e($student->name); ?></h1>
                            <p class="text-gray-600">Student Details and Application History</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="<?php echo e(route('partner.students.edit', $student)); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300 text-center">
                                <i class="fas fa-edit mr-2"></i>Edit Student
                            </a>
                            <a href="<?php echo e(route('partner.students.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300 text-center">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Students
                            </a>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Student Information Card -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 text-center border-b border-gray-200">
                                <?php if($student->photo): ?>
                                    <img src="<?php echo e(Storage::url($student->photo)); ?>" alt="<?php echo e($student->name); ?>" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover border-4 border-green-100">
                                <?php else: ?>
                                    <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center border-4 border-green-100">
                                        <i class="fas fa-user text-gray-400 text-2xl"></i>
                                    </div>
                                <?php endif; ?>
                                <h2 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($student->name); ?></h2>
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                    <?php echo e($student->status === 'active' ? 'bg-green-100 text-green-800' : 
                                       ($student->status === 'graduated' ? 'bg-blue-100 text-blue-800' : 
                                        ($student->status === 'transferred' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'))); ?>">
                                    <?php echo e(ucfirst($student->status)); ?>

                                </span>
                            </div>
                            
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap text-green-600 w-5"></i>
                                        <span class="ml-3 text-gray-700"><strong>Class:</strong> <?php echo e($student->class); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-birthday-cake text-green-600 w-5"></i>
                                        <span class="ml-3 text-gray-700"><strong>Age:</strong> <?php echo e($student->age); ?> years</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-venus-mars text-green-600 w-5"></i>
                                        <span class="ml-3 text-gray-700"><strong>Gender:</strong> <?php echo e(ucfirst($student->gender)); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-phone text-green-600 w-5"></i>
                                        <span class="ml-3 text-gray-700"><strong>Parent Contact:</strong> <?php echo e($student->parent_contact); ?></span>
                                    </div>
                                    <?php if($student->parent_name): ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-user-friends text-green-600 w-5"></i>
                                            <span class="ml-3 text-gray-700"><strong>Parent Name:</strong> <?php echo e($student->parent_name); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($student->address): ?>
                                        <div class="flex items-start">
                                            <i class="fas fa-map-marker-alt text-green-600 w-5 mt-1"></i>
                                            <span class="ml-3 text-gray-700"><strong>Address:</strong> <?php echo e($student->address); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar text-green-600 w-5"></i>
                                        <span class="ml-3 text-gray-700"><strong>Added:</strong> <?php echo e($student->created_at->format('M d, Y')); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Application History -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-xl font-semibold text-gray-900">Application History</h3>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                        <?php echo e($applications->count()); ?> Applications
                                    </span>
                                </div>
                            </div>

                            <?php if($applications->count() > 0): ?>
                                <div class="divide-y divide-gray-200">
                                    <?php $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <div class="flex items-center mb-2">
                                                        <h4 class="text-lg font-medium text-gray-900 mr-3">
                                                            <?php echo e($application->scholarship->title ?? 'Scholarship Application'); ?>

                                                        </h4>
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                            <?php echo e($application->status === 'approved' ? 'bg-green-100 text-green-800' : 
                                                               ($application->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                                                ($application->status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'))); ?>">
                                                            <?php echo e(ucfirst($application->status)); ?>

                                                        </span>
                                                    </div>
                                                    
                                                    <div class="text-sm text-gray-600 space-y-1">
                                                        <p><strong>Category:</strong> <?php echo e(ucfirst($application->category)); ?></p>
                                                        <p><strong>Applied:</strong> <?php echo e($application->created_at->format('M d, Y')); ?></p>
                                                        <?php if($application->reviewed_at): ?>
                                                            <p><strong>Reviewed:</strong> <?php echo e($application->reviewed_at->format('M d, Y')); ?></p>
                                                        <?php endif; ?>
                                                        <?php if($application->reviewer_notes): ?>
                                                            <p><strong>Notes:</strong> <?php echo e($application->reviewer_notes); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                
                                                <div class="ml-4">
                                                    <a href="<?php echo e(route('partner.applications.show', $application)); ?>" class="text-green-600 hover:text-green-800 font-medium text-sm">
                                                        View Details <i class="fas fa-arrow-right ml-1"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="p-12 text-center">
                                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Applications Yet</h3>
                                    <p class="text-gray-500 mb-6">This student hasn't applied for any scholarships yet.</p>
                                    <a href="/scholarships" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300">
                                        <i class="fas fa-plus mr-2"></i>Apply for Scholarship
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-6 bg-white rounded-2xl shadow-lg overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-xl font-semibold text-gray-900">Quick Actions</h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <a href="/scholarships" class="flex items-center p-4 border border-green-200 rounded-xl hover:bg-green-50 transition-colors duration-200">
                                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                            <i class="fas fa-plus text-green-600"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Apply for Scholarship</div>
                                            <div class="text-sm text-gray-500">Submit new application</div>
                                        </div>
                                    </a>
                                    
                                    <a href="<?php echo e(route('partner.students.edit', $student)); ?>" class="flex items-center p-4 border border-blue-200 rounded-xl hover:bg-blue-50 transition-colors duration-200">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                            <i class="fas fa-edit text-blue-600"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Edit Information</div>
                                            <div class="text-sm text-gray-500">Update student details</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/partner/students/show.blade.php ENDPATH**/ ?>