# 🎨 HLTKKQ Foundation - Frontend Development Guide

## Overview

This guide covers the Next.js 15 frontend application for the HLTKKQ Foundation NGO platform, built with React 19, TypeScript, and modern web technologies.

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5.x
- **Styling**: Tailwind CSS 3.4+
- **UI Components**: shadcn/ui
- **State Management**: Zustand
- **Icons**: Lucide React
- **HTTP Client**: Fetch API with custom wrapper

## Project Structure

```
front-end/
├── app/                           # Next.js App Router
│   ├── auth/
│   │   ├── login/page.tsx         # Login page
│   │   └── register/page.tsx      # Registration page
│   ├── dashboard/
│   │   ├── admin/page.tsx         # Admin dashboard
│   │   ├── student/page.tsx       # Student dashboard
│   │   ├── volunteer/page.tsx     # Volunteer dashboard
│   │   └── partner/page.tsx       # Partner dashboard
│   ├── blog/
│   │   ├── page.tsx               # Blog listing
│   │   └── [slug]/page.tsx        # Individual post
│   ├── scholarships/page.tsx      # Scholarships
│   ├── donate/page.tsx            # Donation page
│   ├── layout.tsx                 # Root layout
│   └── page.tsx                   # Homepage
├── components/
│   ├── ui/                        # shadcn/ui components
│   ├── dashboard/                 # Dashboard components
│   ├── navigation.tsx             # Main navigation
│   └── footer.tsx                 # Site footer
├── hooks/                         # Custom React hooks
├── lib/                           # Utilities and APIs
└── styles/                        # Global styles
```

## Getting Started

### Installation
```bash
cd front-end
npm install
```

### Environment Setup
```env
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

### Development
```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run start      # Start production server
npm run lint       # Run ESLint
```

## Component Architecture

### Page Component Pattern
```typescript
// app/example/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { apiClient } from '@/lib/api'

export default function ExamplePage() {
  const { user } = useAuth()
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Fetch data logic
  }, [])

  if (loading) return <div>Loading...</div>

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page content */}
    </div>
  )
}
```

### Component Structure
```typescript
interface ComponentProps {
  className?: string
  children?: React.ReactNode
  variant?: 'default' | 'secondary'
}

export function Component({ 
  className, 
  children, 
  variant = 'default' 
}: ComponentProps) {
  return (
    <div className={cn('base-classes', className)}>
      {children}
    </div>
  )
}
```

## State Management

### Authentication Hook
```typescript
// hooks/use-auth.ts
export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      loading: false,

      login: async (credentials) => {
        const response = await apiClient.login(credentials)
        set({ user: response.data.user, token: response.data.access_token })
      },

      logout: () => {
        set({ user: null, token: null })
        localStorage.removeItem('authToken')
      }
    }),
    { name: 'auth-storage' }
  )
)
```

## API Integration

### API Client Setup
```typescript
// lib/api.ts
class ApiClient {
  private baseURL = process.env.NEXT_PUBLIC_API_URL

  async request<T>(endpoint: string, options: RequestInit = {}) {
    const token = localStorage.getItem('authToken')
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    const response = await fetch(`${this.baseURL}/api/v1${endpoint}`, config)
    return response.json()
  }
}

export const apiClient = new ApiClient()
```

## UI Components

### Using shadcn/ui
```typescript
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function ExampleForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Form</CardTitle>
      </CardHeader>
      <CardContent>
        <Input placeholder="Enter text" />
        <Button>Submit</Button>
      </CardContent>
    </Card>
  )
}
```

## Responsive Design

### Tailwind Breakpoints
```css
sm: 640px    /* Mobile landscape */
md: 768px    /* Tablet portrait */
lg: 1024px   /* Tablet landscape */
xl: 1280px   /* Desktop */
2xl: 1536px  /* Large desktop */
```

### Responsive Grid Example
```typescript
export function ResponsiveGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Grid items */}
    </div>
  )
}
```

## User Dashboard System

### Dashboard Router
```typescript
// app/dashboard/page.tsx
export default function DashboardPage() {
  const { user } = useAuth()

  // Route to specific dashboard based on user type
  if (user?.role === 'admin') return <AdminDashboard />
  if (user?.preferences?.user_type === 'student') return <StudentDashboard />
  if (user?.preferences?.user_type === 'volunteer') return <VolunteerDashboard />
  if (user?.preferences?.user_type === 'partner') return <PartnerDashboard />
  
  return <DefaultDashboard />
}
```

### Student Dashboard Features
- Scholarship applications
- Event registrations
- Educational resources
- Application status tracking

### Volunteer Dashboard Features
- Volunteer opportunities
- Hour logging
- Training modules
- Impact metrics

### Partner Dashboard Features
- Collaboration tools
- Resource sharing
- Project proposals
- Analytics

### Admin Dashboard Features
- User management
- Content management
- Analytics
- System settings

## Form Handling

### Form Pattern with Validation
```typescript
export function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [errors, setErrors] = useState({})
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    
    try {
      const response = await apiClient.request('/contact', {
        method: 'POST',
        body: JSON.stringify(formData)
      })
      
      if (response.success) {
        // Success handling
      } else {
        setErrors(response.errors || {})
      }
    } catch (error) {
      // Error handling
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  )
}
```

## Navigation System

### Main Navigation
```typescript
// components/navigation.tsx
export function Navigation() {
  const { user } = useAuth()
  
  return (
    <nav className="bg-white shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Logo />
          <DesktopMenu />
          <MobileMenu />
          {user ? <UserMenu /> : <AuthButtons />}
        </div>
      </div>
    </nav>
  )
}
```

## Testing

### Component Testing
```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })
})
```

## Performance Optimization

### Image Optimization
```typescript
import Image from 'next/image'

export function OptimizedImage() {
  return (
    <Image
      src="/hero-image.jpg"
      alt="Description"
      width={800}
      height={600}
      priority
      className="rounded-lg"
    />
  )
}
```

### Code Splitting
```typescript
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div>Loading...</div>
})
```

## SEO and Metadata

### Page Metadata
```typescript
// app/about/page.tsx
export const metadata = {
  title: 'About Us - HLTKKQ Foundation',
  description: 'Learn about our mission to transform lives through education'
}
```

## Deployment

### Build and Deploy
```bash
npm run build
npm start
```

### Environment Variables
```env
# Production
NEXT_PUBLIC_API_URL=https://api.hltkkqfoundation.org
NEXT_PUBLIC_APP_NAME="HLTKKQ Foundation"
```

## Best Practices

1. **Use TypeScript** for all components and functions
2. **Follow React Hooks** patterns and best practices
3. **Implement proper error boundaries** for error handling
4. **Use semantic HTML** elements for accessibility
5. **Test components** with React Testing Library
6. **Optimize images** and implement lazy loading
7. **Handle loading states** appropriately
8. **Implement responsive design** mobile-first

---

> **🎨 Building beautiful, accessible interfaces that serve the mission of educational empowerment in Nigeria!** 