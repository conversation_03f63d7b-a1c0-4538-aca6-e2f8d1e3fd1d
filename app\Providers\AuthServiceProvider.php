<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\ScholarshipApplication;
use App\Models\StudentProgression;
use App\Models\Student;
use App\Models\PartnerOrganization;
use App\Models\Volunteer;
use App\Models\Event;
use App\Models\Program;
use App\Models\Donation;
use App\Policies\ScholarshipApplicationPolicy;
use App\Policies\StudentProgressionPolicy;
use App\Policies\MultiYearApplicationPolicy;
use App\Policies\PartnerOrganizationPolicy;
use App\Policies\StudentPolicy;
use App\Policies\VolunteerPolicy;
use App\Policies\EventPolicy;
use App\Policies\ProgramPolicy;
use App\Policies\DonationPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        ScholarshipApplication::class => ScholarshipApplicationPolicy::class,
        StudentProgression::class => StudentProgressionPolicy::class,
        Student::class => StudentPolicy::class,
        PartnerOrganization::class => PartnerOrganizationPolicy::class,
        Volunteer::class => VolunteerPolicy::class,
        Event::class => EventPolicy::class,
        Program::class => ProgramPolicy::class,
        Donation::class => DonationPolicy::class,
        // Multi-year applications use the same model as regular applications
        // but with different policy methods for multi-year specific operations
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define additional gates for role-based permissions
        Gate::define('access-admin-panel', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('access-partner-dashboard', function ($user) {
            return $user->role === 'partner_organization';
        });

        Gate::define('access-student-dashboard', function ($user) {
            return $user->role === 'user';
        });

        Gate::define('manage-scholarships', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-partner-students', function ($user) {
            return $user->role === 'partner_organization';
        });

        Gate::define('apply-university-scholarships', function ($user) {
            return $user->role === 'user';
        });

        Gate::define('review-applications', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('view-analytics', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('export-data', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('bulk-operations', function ($user) {
            return $user->role === 'admin';
        });

        // Category-specific gates
        Gate::define('access-primary-scholarships', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('access-secondary-scholarships', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('access-university-scholarships', function ($user) {
            return in_array($user->role, ['admin', 'user']);
        });

        // Student data access gates
        Gate::define('access-student-data', function ($user, $student) {
            if ($user->role === 'admin') {
                return true;
            }

            if ($user->role === 'partner_organization') {
                return $student->school_id === $user->partner_organization_id;
            }

            if ($user->role === 'user') {
                return $student->user_id === $user->id;
            }

            return false;
        });

        // Progression management gates
        Gate::define('manage-progressions', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('approve-progressions', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        // Multi-year application gates
        Gate::define('manage-multi-year-applications', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization', 'user']);
        });

        Gate::define('view-multi-year-analytics', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        // Student management gates
        Gate::define('manage-students', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('view-student-analytics', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('transfer-students', function ($user) {
            return $user->role === 'admin';
        });

        // Partner organization management gates
        Gate::define('manage-partner-organizations', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('view-partner-analytics', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('access-institutional-dashboard', function ($user) {
            return $user->role === 'partner_organization';
        });

        // Multi-year application specific gates
        Gate::define('renew-applications', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization', 'user']);
        });

        Gate::define('bulk-renew-applications', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        Gate::define('manage-renewal-criteria', function ($user) {
            return in_array($user->role, ['admin', 'partner_organization']);
        });

        // Advanced authorization gates
        Gate::define('access-system-analytics', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-system-settings', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('audit-system-activities', function ($user) {
            return $user->role === 'admin';
        });
    }
}
