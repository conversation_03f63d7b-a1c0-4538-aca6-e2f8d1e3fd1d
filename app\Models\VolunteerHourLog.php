<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VolunteerHourLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'volunteer_id',
        'activity_date',
        'hours',
        'activity_description',
        'activity_type',
        'supervisor_name',
        'supervisor_contact',
        'notes',
        'status',
        'verified_by',
        'verified_at'
    ];

    protected $casts = [
        'activity_date' => 'date',
        'hours' => 'decimal:2',
        'verified_at' => 'datetime'
    ];

    /**
     * Get the volunteer that owns this hour log
     */
    public function volunteer()
    {
        return $this->belongsTo(Volunteer::class);
    }

    /**
     * Get the user who verified this log
     */
    public function verifiedBy()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope for pending logs
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved logs
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected logs
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for specific activity type
     */
    public function scopeActivityType($query, $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Check if log is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if log is approved
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if log is rejected
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Approve hour log
     */
    public function approve($verifiedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'verified_at' => now(),
            'verified_by' => $verifiedBy
        ]);
    }

    /**
     * Reject hour log
     */
    public function reject($verifiedBy = null)
    {
        $this->update([
            'status' => 'rejected',
            'verified_at' => now(),
            'verified_by' => $verifiedBy
        ]);
    }

    /**
     * Get activity type label
     */
    public function getActivityTypeLabelAttribute()
    {
        $types = [
            'event_support' => 'Event Support',
            'administrative' => 'Administrative',
            'training' => 'Training',
            'outreach' => 'Community Outreach',
            'fundraising' => 'Fundraising',
            'other' => 'Other'
        ];

        return $types[$this->activity_type] ?? $this->activity_type;
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return 'yellow';
            case 'approved':
                return 'green';
            case 'rejected':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Get formatted hours
     */
    public function getFormattedHoursAttribute()
    {
        return number_format($this->hours, 1) . ' hour' . ($this->hours != 1 ? 's' : '');
    }

    /**
     * Get days since logged
     */
    public function getDaysSinceLoggedAttribute()
    {
        return $this->created_at->diffInDays(now());
    }
}
