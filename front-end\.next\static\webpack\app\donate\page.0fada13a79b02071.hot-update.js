"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/donate/page",{

/***/ "(app-pages-browser)/./components/donation-tiers.tsx":
/*!***************************************!*\
  !*** ./components/donation-tiers.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DonationTiers: () => (/* binding */ DonationTiers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Droplets,GraduationCap,Shield,Sprout!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Droplets,GraduationCap,Shield,Sprout!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Droplets,GraduationCap,Shield,Sprout!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Droplets,GraduationCap,Shield,Sprout!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sprout.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Droplets,GraduationCap,Shield,Sprout!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* __next_internal_client_entry_do_not_use__ DonationTiers auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst donationTiers = [\n    {\n        amount: 15000,\n        title: \"Supporter\",\n        description: \"Provides clean water for 3 families for a month\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-8 w-8 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n            lineNumber: 13,\n            columnNumber: 11\n        }, undefined),\n        benefits: [\n            \"Monthly newsletter\",\n            \"Impact updates\"\n        ]\n    },\n    {\n        amount: 25000,\n        title: \"Advocate\",\n        description: \"Sponsors school supplies for 8 children\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-8 w-8 text-green-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n            lineNumber: 20,\n            columnNumber: 11\n        }, undefined),\n        benefits: [\n            \"Monthly newsletter\",\n            \"Impact updates\",\n            \"Quarterly reports\"\n        ],\n        popular: true\n    },\n    {\n        amount: 75000,\n        title: \"Champion\",\n        description: \"Funds healthcare for 15 families\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-8 w-8 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n            lineNumber: 28,\n            columnNumber: 11\n        }, undefined),\n        benefits: [\n            \"Monthly newsletter\",\n            \"Impact updates\",\n            \"Quarterly reports\",\n            \"Annual event invitation\"\n        ]\n    },\n    {\n        amount: 150000,\n        title: \"Hero\",\n        description: \"Supports agricultural training for 30 farmers\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-8 w-8 text-amber-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n            lineNumber: 35,\n            columnNumber: 11\n        }, undefined),\n        benefits: [\n            \"Monthly newsletter\",\n            \"Impact updates\",\n            \"Quarterly reports\",\n            \"Annual event invitation\",\n            \"Direct project updates\"\n        ]\n    }\n];\nfunction DonationTiers(param) {\n    let { selectedAmount = null, onAmountSelect } = param;\n    _s();\n    const [internalSelectedAmount, setInternalSelectedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentSelectedAmount = selectedAmount !== undefined ? selectedAmount : internalSelectedAmount;\n    const handleAmountSelect = (amount)=>{\n        if (onAmountSelect) {\n            onAmountSelect(amount);\n        } else {\n            setInternalSelectedAmount(amount);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n        children: donationTiers.map((tier, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer neumorphic rounded-2xl \".concat(currentSelectedAmount === tier.amount ? \"ring-2 ring-green-500\" : \"\", \" \").concat(tier.popular ? \"border-green-500\" : \"\"),\n                onClick: ()=>handleAmountSelect(tier.amount),\n                children: [\n                    tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                        className: \"absolute top-4 right-4 bg-green-500 hover:bg-green-600 rounded-full\",\n                        children: \"Most Popular\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"text-center pb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: tier.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl\",\n                                children: tier.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: [\n                                    \"₦\",\n                                    tier.amount.toLocaleString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground text-center\",\n                                children: tier.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-sm\",\n                                        children: \"Benefits:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: tier.benefits.map((benefit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Droplets_GraduationCap_Shield_Sprout_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-500 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    benefit\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\donation-tiers.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(DonationTiers, \"hbhiviD6XkC0Ls5H36UgUELJD6A=\");\n_c = DonationTiers;\nvar _c;\n$RefreshReg$(_c, \"DonationTiers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/donation-tiers.tsx\n"));

/***/ })

});