/**
 * Helper functions for working with constants
 * Provides utility functions to work with the centralized constants
 */

import {
  API_BASE_URL,
  BACKEND_BASE_URL,
  API_ENDPOINTS,
  APP_CONFIG,
  THEME_CONFIG,
  VALIDATION_RULES,
  SCHOLARSHIP_CATEGORIES,
  USER_ROLES,
  APPLICATION_STATUS,
  PAYMENT_METHODS,
  ENV
} from '../constants'

// =============================================================================
// API HELPERS
// =============================================================================

/**
 * Build full API URL for a given endpoint
 */
export const buildApiUrl = (endpoint: string): string => {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${API_BASE_URL}${cleanEndpoint}`
}

/**
 * Build backend URL for authentication redirects
 */
export const buildBackendUrl = (path: string): string => {
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${BACKEND_BASE_URL}${cleanPath}`
}

/**
 * Get API endpoint by category and name
 */
export const getApiEndpoint = (category: keyof typeof API_ENDPOINTS, endpoint: string): string => {
  const endpoints = API_ENDPOINTS[category] as Record<string, string>
  return endpoints[endpoint] || endpoint
}

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  return VALIDATION_RULES.EMAIL_REGEX.test(email)
}

/**
 * Validate Nigerian phone number
 */
export const isValidPhone = (phone: string): boolean => {
  return VALIDATION_RULES.PHONE_REGEX.test(phone)
}

/**
 * Validate password strength
 */
export const isValidPassword = (password: string): boolean => {
  return password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH
}

/**
 * Validate name length
 */
export const isValidName = (name: string): boolean => {
  return name.length >= VALIDATION_RULES.NAME_MIN_LENGTH && 
         name.length <= VALIDATION_RULES.NAME_MAX_LENGTH
}

/**
 * Validate message length
 */
export const isValidMessage = (message: string): boolean => {
  return message.length <= VALIDATION_RULES.MESSAGE_MAX_LENGTH
}

// =============================================================================
// BUSINESS LOGIC HELPERS
// =============================================================================

/**
 * Get scholarship category display name
 */
export const getScholarshipCategoryName = (category: string): string => {
  switch (category) {
    case SCHOLARSHIP_CATEGORIES.PRIMARY:
      return 'Primary Education'
    case SCHOLARSHIP_CATEGORIES.SECONDARY:
      return 'Secondary Education'
    case SCHOLARSHIP_CATEGORIES.UNIVERSITY:
      return 'University Education'
    default:
      return 'Unknown Category'
  }
}

/**
 * Get user role display name
 */
export const getUserRoleDisplayName = (role: string): string => {
  switch (role) {
    case USER_ROLES.ADMIN:
      return 'Administrator'
    case USER_ROLES.STUDENT:
      return 'Student'
    case USER_ROLES.PARTNER:
      return 'Partner Organization'
    case USER_ROLES.NORMAL:
      return 'Normal User'
    default:
      return 'Unknown Role'
  }
}

/**
 * Get application status display name and color
 */
export const getApplicationStatusInfo = (status: string): { name: string; color: string; bgColor: string } => {
  switch (status) {
    case APPLICATION_STATUS.PENDING:
      return { 
        name: 'Pending Review', 
        color: 'text-yellow-600', 
        bgColor: 'bg-yellow-100' 
      }
    case APPLICATION_STATUS.UNDER_REVIEW:
      return { 
        name: 'Under Review', 
        color: 'text-blue-600', 
        bgColor: 'bg-blue-100' 
      }
    case APPLICATION_STATUS.APPROVED:
      return { 
        name: 'Approved', 
        color: 'text-green-600', 
        bgColor: 'bg-green-100' 
      }
    case APPLICATION_STATUS.REJECTED:
      return { 
        name: 'Rejected', 
        color: 'text-red-600', 
        bgColor: 'bg-red-100' 
      }
    case APPLICATION_STATUS.COMPLETED:
      return { 
        name: 'Completed', 
        color: 'text-purple-600', 
        bgColor: 'bg-purple-100' 
      }
    default:
      return { 
        name: 'Unknown Status', 
        color: 'text-gray-600', 
        bgColor: 'bg-gray-100' 
      }
  }
}

/**
 * Get payment method display name
 */
export const getPaymentMethodName = (method: string): string => {
  switch (method) {
    case PAYMENT_METHODS.PAYSTACK:
      return 'Card Payment (Paystack)'
    case PAYMENT_METHODS.BANK_TRANSFER:
      return 'Bank Transfer'
    default:
      return 'Unknown Payment Method'
  }
}

// =============================================================================
// THEME HELPERS
// =============================================================================

/**
 * Get theme color by name
 */
export const getThemeColor = (colorName: keyof typeof THEME_CONFIG.COLORS): string => {
  return THEME_CONFIG.COLORS[colorName]
}

/**
 * Get gradient class by name
 */
export const getGradientClass = (gradientName: keyof typeof THEME_CONFIG.GRADIENTS): string => {
  return THEME_CONFIG.GRADIENTS[gradientName]
}

/**
 * Build gradient background style
 */
export const buildGradientStyle = (gradientName: keyof typeof THEME_CONFIG.GRADIENTS): React.CSSProperties => {
  const gradient = THEME_CONFIG.GRADIENTS[gradientName]
  return {
    background: `linear-gradient(to right, ${gradient.replace('from-', '').replace('via-', '').replace('to-', '')})`
  }
}

// =============================================================================
// APP INFO HELPERS
// =============================================================================

/**
 * Get app title with optional suffix
 */
export const getAppTitle = (suffix?: string): string => {
  return suffix ? `${APP_CONFIG.NAME} - ${suffix}` : APP_CONFIG.NAME
}

/**
 * Get app meta description
 */
export const getAppDescription = (): string => {
  return APP_CONFIG.DESCRIPTION
}

/**
 * Get app version
 */
export const getAppVersion = (): string => {
  return APP_CONFIG.VERSION
}

// =============================================================================
// ENVIRONMENT HELPERS
// =============================================================================

/**
 * Check if running in development
 */
export const isDevelopment = (): boolean => {
  return ENV.IS_DEVELOPMENT
}

/**
 * Check if running in production
 */
export const isProduction = (): boolean => {
  return ENV.IS_PRODUCTION
}

/**
 * Get environment name
 */
export const getEnvironmentName = (): string => {
  if (ENV.IS_DEVELOPMENT) return 'Development'
  if (ENV.IS_PRODUCTION) return 'Production'
  if (ENV.IS_TEST) return 'Test'
  return 'Unknown'
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Format currency (Nigerian Naira)
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength).trim() + '...'
}

/**
 * Generate page title for SEO
 */
export const generatePageTitle = (pageTitle: string): string => {
  return `${pageTitle} | ${APP_CONFIG.NAME}`
}

/**
 * Generate meta description for SEO
 */
export const generateMetaDescription = (description?: string): string => {
  return description || APP_CONFIG.DESCRIPTION
}
