@extends('layouts.admin')

@section('title', 'Edit Event')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Event</h1>
        <div>
            <a href="{{ route('admin.events.show', $event->id) }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="{{ route('admin.events.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Events
            </a>
        </div>
    </div>

    <!-- Display Validation Errors -->
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.events.update', $event->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Event Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ old('title', $event->title) }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Event Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required>{{ old('description', $event->description) }}</textarea>
                        </div>

                        <!-- Event Type and Status -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_type" class="form-label">Event Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="event_type" name="event_type" required>
                                        <option value="fundraising" {{ old('event_type', $event->event_type) == 'fundraising' ? 'selected' : '' }}>Fundraising</option>
                                        <option value="volunteer" {{ old('event_type', $event->event_type) == 'volunteer' ? 'selected' : '' }}>Volunteer</option>
                                        <option value="awareness" {{ old('event_type', $event->event_type) == 'awareness' ? 'selected' : '' }}>Awareness</option>
                                        <option value="training" {{ old('event_type', $event->event_type) == 'training' ? 'selected' : '' }}>Training</option>
                                        <option value="other" {{ old('event_type', $event->event_type) == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="draft" {{ old('status', $event->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="published" {{ old('status', $event->status) == 'published' ? 'selected' : '' }}>Published</option>
                                        <option value="cancelled" {{ old('status', $event->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_datetime" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" value="{{ old('start_datetime', $event->start_datetime ? $event->start_datetime->format('Y-m-d\TH:i') : '') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_datetime" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="end_datetime" name="end_datetime" value="{{ old('end_datetime', $event->end_datetime ? $event->end_datetime->format('Y-m-d\TH:i') : '') }}" required>
                                </div>
                            </div>
                        </div>

                        <!-- Location -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="venue" class="form-label">Venue</label>
                                    <input type="text" class="form-control" id="venue" name="venue" value="{{ old('venue', $event->venue) }}" placeholder="Event venue name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">Capacity</label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" value="{{ old('capacity', $event->capacity) }}" min="1" placeholder="Maximum participants">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2" placeholder="Full address of the event location">{{ old('address', $event->address) }}</textarea>
                        </div>

                        <!-- Registration Deadline -->
                        <div class="mb-3">
                            <label for="registration_deadline" class="form-label">Registration Deadline</label>
                            <input type="datetime-local" class="form-control" id="registration_deadline" name="registration_deadline" value="{{ old('registration_deadline', $event->registration_deadline ? $event->registration_deadline->format('Y-m-d\TH:i') : '') }}">
                            <div class="form-text">Leave empty if no registration deadline</div>
                        </div>

                        <!-- Requirements and Agenda -->
                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements</label>
                            <textarea class="form-control" id="requirements" name="requirements" rows="3" placeholder="What attendees need to bring or prepare (one per line)">{{ old('requirements', is_array($event->requirements) ? implode("\n", $event->requirements) : '') }}</textarea>
                            <div class="form-text">Enter each requirement on a new line</div>
                        </div>

                        <div class="mb-3">
                            <label for="agenda" class="form-label">Event Agenda</label>
                            <textarea class="form-control" id="agenda" name="agenda" rows="4" placeholder="Detailed schedule and activities (one per line)">{{ old('agenda', is_array($event->agenda) ? implode("\n", $event->agenda) : '') }}</textarea>
                            <div class="form-text">Enter each agenda item on a new line</div>
                        </div>

                        <!-- Featured Image -->
                        <div class="mb-3">
                            <label for="featured_image" class="form-label">Featured Image</label>
                            <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                            <div class="form-text">Upload a new image to replace the current one. Leave empty to keep current image.</div>
                            @if($event->featured_image)
                                <div class="mt-2">
                                    <small class="text-muted">Current image: {{ basename($event->featured_image) }}</small>
                                </div>
                            @endif
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Event
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Event Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <span class="badge bg-{{ $event->status === 'published' ? 'success' : ($event->status === 'cancelled' ? 'danger' : 'warning') }} p-3 mb-3" style="font-size: 1rem;">
                            {{ ucfirst($event->status) }}
                        </span>
                        <p class="text-muted mb-0">Current event status</p>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <p class="mb-0">{{ $event->created_at->format('F j, Y') }}</p>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <p class="mb-0">{{ $event->updated_at->format('F j, Y g:i A') }}</p>
                    </div>
                    @if($event->organizer)
                    <div class="mb-3">
                        <small class="text-muted">Organizer</small>
                        <p class="mb-0">{{ $event->organizer->first_name }} {{ $event->organizer->last_name }}</p>
                    </div>
                    @endif
                    @if($event->capacity)
                    <div class="mb-3">
                        <small class="text-muted">Capacity</small>
                        <p class="mb-0">{{ $event->capacity }} participants</p>
                    </div>
                    @endif
                    @if($event->registrations)
                    <div class="mb-3">
                        <small class="text-muted">Registrations</small>
                        <p class="mb-0">{{ $event->registrations->count() }} registered</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Event Preview</h6>
                </div>
                <div class="card-body">
                    @if($event->featured_image)
                    <div class="text-center mb-3">
                        <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}" class="img-fluid rounded" style="max-height: 150px;">
                    </div>
                    @endif
                    <h6 id="preview-title">{{ $event->title }}</h6>
                    <p class="text-muted small" id="preview-type">{{ ucfirst(str_replace('_', ' ', $event->event_type)) }}</p>
                    <p class="text-muted small" id="preview-datetime">
                        <i class="fas fa-calendar me-1"></i>
                        {{ $event->start_datetime ? $event->start_datetime->format('M j, Y g:i A') : 'Date not set' }}
                    </p>
                    @if($event->venue)
                    <p class="text-muted small" id="preview-venue">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ $event->venue }}
                    </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const typeInput = document.getElementById('event_type');
    const startInput = document.getElementById('start_datetime');
    const venueInput = document.getElementById('venue');
    
    titleInput.addEventListener('input', function() {
        document.getElementById('preview-title').textContent = this.value || '{{ $event->title }}';
    });
    
    typeInput.addEventListener('change', function() {
        document.getElementById('preview-type').textContent = this.options[this.selectedIndex].text;
    });
    
    startInput.addEventListener('change', function() {
        if (this.value) {
            const date = new Date(this.value);
            document.getElementById('preview-datetime').innerHTML = '<i class="fas fa-calendar me-1"></i>' + date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit'
            });
        }
    });
    
    if (venueInput) {
        venueInput.addEventListener('input', function() {
            const previewVenue = document.getElementById('preview-venue');
            if (this.value) {
                if (previewVenue) {
                    previewVenue.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i>' + this.value;
                } else {
                    const newVenue = document.createElement('p');
                    newVenue.className = 'text-muted small';
                    newVenue.id = 'preview-venue';
                    newVenue.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i>' + this.value;
                    document.getElementById('preview-datetime').parentNode.appendChild(newVenue);
                }
            } else if (previewVenue) {
                previewVenue.remove();
            }
        });
    }
});
</script>
@endsection
