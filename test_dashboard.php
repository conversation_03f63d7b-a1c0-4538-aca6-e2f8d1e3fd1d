<?php

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/admin/dashboard');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Error: $error\n";
echo "Response length: " . strlen($response) . "\n";

// Check for error keywords
if (preg_match('/\b(error|exception|fatal)\b/i', $response)) {
    echo "❌ Contains error keywords\n";
    
    // Find and show the error context
    if (preg_match('/\b(error|exception|fatal)\b.{0,100}/i', $response, $matches)) {
        echo "Error context: " . $matches[0] . "\n";
    }
} else {
    echo "✅ No error keywords found\n";
}

// Show first 500 characters
echo "\nFirst 500 characters:\n";
echo substr($response, 0, 500) . "\n";
