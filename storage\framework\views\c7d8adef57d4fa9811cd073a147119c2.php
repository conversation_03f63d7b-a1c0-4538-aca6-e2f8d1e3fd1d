<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title'); ?> - <?php echo e($appName); ?></title>

    <!-- Tailwind CSS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">


    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-inter bg-gradient-to-br from-green-50 via-green-50 to-green-100 min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8 text-center">
                <?php if($appLogo): ?>
                    <div class="mb-4">
                        <img src="<?php echo e($appLogo); ?>" alt="<?php echo e($appName); ?>" class="h-12 w-auto mx-auto">
                    </div>
                <?php endif; ?>
                <h1 class="text-2xl font-semibold mb-2"><?php echo $__env->yieldContent('header-title'); ?></h1>
                <p class="text-green-100 text-sm"><?php echo $__env->yieldContent('header-subtitle'); ?></p>
            </div>

            <!-- Body -->
            <div class="p-6 lg:p-8">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-6">
            <p class="text-gray-600 text-sm">© <?php echo e(date('Y')); ?> <?php echo e($appName); ?>. All rights reserved.</p>
        </div>
    </div>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/auth/layout.blade.php ENDPATH**/ ?>