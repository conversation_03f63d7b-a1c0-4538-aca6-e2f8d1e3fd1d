

<?php $__env->startSection('title', 'Admin Profile'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Admin Profile</h1>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Picture Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Picture</h6>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo e($user->profile_picture ?? asset('img/default-avatar.png')); ?>" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <h4><?php echo e($user->first_name ?? 'Admin'); ?> <?php echo e($user->last_name ?? 'User'); ?></h4>
                    <p class="text-muted"><?php echo e(ucfirst($user->role ?? 'admin')); ?></p>
                    <button class="btn btn-primary btn-sm">Change Picture</button>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Account Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Account Details</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.profile.update')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo e(old('first_name', $user->first_name ?? '')); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo e(old('last_name', $user->last_name ?? '')); ?>" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email', $user->email ?? '')); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone_number" class="form-label">Phone Number (Optional)</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" value="<?php echo e(old('phone_number', $user->phone_number ?? '')); ?>">
                        </div>
                        <button type="submit" class="btn btn-primary">Update Profile</button>
                    </form>
                </div>
            </div>

            <!-- Activity Log (Placeholder) -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">Logged in - <?php echo e(now()->toFormattedDateString()); ?></li>
                        <li class="list-group-item">Viewed User Management - <?php echo e(now()->subMinutes(30)->toFormattedDateString()); ?></li>
                        <li class="list-group-item">Updated a blog post - <?php echo e(now()->subHours(2)->toFormattedDateString()); ?></li>
                    </ul>
                    <a href="#" class="btn btn-link btn-sm mt-2">View All Activity</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Add any page-specific JavaScript here
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/profile/index.blade.php ENDPATH**/ ?>