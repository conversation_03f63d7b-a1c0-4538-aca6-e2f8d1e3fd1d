<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Blog",
 *     description="Blog post and comment management endpoints"
 * )
 */
class BlogController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/blog/posts",
     *     summary="Get all published blog posts",
     *     tags={"Blog"},
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by category slug",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search in title and content",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of posts per page",
     *         required=false,
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blog posts retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="posts", type="object",
     *                 @OA\Property(property="data", type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="title", type="string"),
     *                         @OA\Property(property="slug", type="string"),
     *                         @OA\Property(property="excerpt", type="string"),
     *                         @OA\Property(property="featured_image_url", type="string"),
     *                         @OA\Property(property="author", type="object"),
     *                         @OA\Property(property="category", type="object"),
     *                         @OA\Property(property="published_at", type="string"),
     *                         @OA\Property(property="tags", type="array")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $category = $request->get('category');
        $search = $request->get('search');

        $query = BlogPost::with(['author', 'category'])
                        ->where('status', 'published')
                        ->where('published_at', '<=', now());

        // Filter by category
        if ($category) {
            $query->whereHas('category', function($q) use ($category) {
                $q->where('slug', $category);
            });
        }

        // Search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('tags', 'like', "%{$search}%");
            });
        }

        $posts = $query->orderBy('published_at', 'desc')->paginate($perPage);

        return response()->json([
            'posts' => [
                'data' => $posts->map(function($post) {
                    return [
                        'id' => $post->id,
                        'title' => $post->title,
                        'slug' => $post->slug,
                        'excerpt' => $post->excerpt,
                        'featured_image_url' => $post->featured_image_url,
                        'reading_time' => $post->reading_time,
                        'author' => [
                            'id' => $post->author->id,
                            'name' => $post->author->full_name,
                            'avatar' => $post->author->profile_picture_url
                        ],
                        'category' => $post->category ? [
                            'id' => $post->category->id,
                            'name' => $post->category->name,
                            'slug' => $post->category->slug,
                            'color' => $post->category->color
                        ] : null,
                        'tags' => is_array($post->tags) ? $post->tags : (is_string($post->tags) ? explode(',', $post->tags) : []),
                        'published_at' => $post->published_at->format('Y-m-d H:i:s'),
                        'comments_count' => $post->approved_comments_count ?? 0
                    ];
                }),
                'current_page' => $posts->currentPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
                'last_page' => $posts->lastPage()
            ],
            'success' => true
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/blog/posts/{slug}",
     *     summary="Get specific blog post by slug",
     *     tags={"Blog"},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         description="Blog post slug",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blog post retrieved successfully"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Blog post not found"
     *     )
     * )
     */
    public function show($slug)
    {
        $post = BlogPost::with(['author', 'category', 'comments' => function($query) {
                           $query->where('status', 'approved')
                                 ->whereNull('parent_id')
                                 ->with(['replies' => function($q) {
                                     $q->where('status', 'approved');
                                 }]);
                       }])
                       ->where('slug', $slug)
                       ->where('status', 'published')
                       ->where('published_at', '<=', now())
                       ->first();

        if (!$post) {
            return response()->json([
                'message' => 'Blog post not found',
                'success' => false
            ], 404);
        }

        // Increment view count
        $post->increment('views_count');

        return response()->json([
            'post' => [
                'id' => $post->id,
                'title' => $post->title,
                'slug' => $post->slug,
                'content' => $post->content,
                'excerpt' => $post->excerpt,
                'featured_image_url' => $post->featured_image_url,
                'reading_time' => $post->reading_time,
                'views' => $post->views_count,
                'author' => [
                    'id' => $post->author->id,
                    'name' => $post->author->full_name,
                    'avatar' => $post->author->profile_picture_url,
                    'bio' => $post->author->bio ?? null
                ],
                'category' => $post->category ? [
                    'id' => $post->category->id,
                    'name' => $post->category->name,
                    'slug' => $post->category->slug,
                    'color' => $post->category->color
                ] : null,
                'tags' => is_array($post->tags) ? $post->tags : (is_string($post->tags) ? explode(',', $post->tags) : []),
                'published_at' => $post->published_at->format('Y-m-d H:i:s'),
                'meta_title' => $post->meta_title,
                'meta_description' => $post->meta_description,
                'comments' => $post->comments->map(function($comment) {
                    return [
                        'id' => $comment->id,
                        'content' => $comment->content,
                        'author_name' => $comment->author_name,
                        'author_email' => $comment->author_email,
                        'created_at' => $comment->created_at->format('Y-m-d H:i:s'),
                        'replies' => $comment->replies->map(function($reply) {
                            return [
                                'id' => $reply->id,
                                'content' => $reply->content,
                                'author_name' => $reply->author_name,
                                'author_email' => $reply->author_email,
                                'created_at' => $reply->created_at->format('Y-m-d H:i:s')
                            ];
                        })
                    ];
                })
            ],
            'success' => true
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/blog/categories",
     *     summary="Get all blog categories",
     *     tags={"Blog"},
     *     @OA\Response(
     *         response=200,
     *         description="Categories retrieved successfully"
     *     )
     * )
     */
    public function categories()
    {
        $categories = BlogCategory::where('is_active', true)
                                 ->withCount(['posts' => function($query) {
                                     $query->where('status', 'published');
                                 }])
                                 ->orderBy('name')
                                 ->get();

        return response()->json([
            'categories' => $categories->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'color' => $category->color,
                    'posts_count' => $category->posts_count
                ];
            }),
            'success' => true
        ]);
    }

    /**
     * Get posts by category
     */
    public function byCategory($category, Request $request)
    {
        $blogCategory = BlogCategory::where('slug', $category)
                                   ->where('is_active', true)
                                   ->first();

        if (!$blogCategory) {
            return response()->json([
                'message' => 'Category not found',
                'success' => false
            ], 404);
        }

        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');

        $query = BlogPost::with(['author', 'category'])
                        ->where('category_id', $blogCategory->id)
                        ->where('status', 'published')
                        ->where('published_at', '<=', now());

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        $posts = $query->orderBy('published_at', 'desc')->paginate($perPage);

        return response()->json([
            'category' => [
                'id' => $blogCategory->id,
                'name' => $blogCategory->name,
                'slug' => $blogCategory->slug,
                'description' => $blogCategory->description,
                'color' => $blogCategory->color
            ],
            'posts' => [
                'data' => $posts->map(function($post) {
                    return [
                        'id' => $post->id,
                        'title' => $post->title,
                        'slug' => $post->slug,
                        'excerpt' => $post->excerpt,
                        'featured_image_url' => $post->featured_image_url,
                        'reading_time' => $post->reading_time,
                        'author' => [
                            'id' => $post->author->id,
                            'name' => $post->author->full_name,
                            'avatar' => $post->author->profile_picture_url
                        ],
                        'tags' => is_array($post->tags) ? $post->tags : (is_string($post->tags) ? explode(',', $post->tags) : []),
                        'published_at' => $post->published_at->format('Y-m-d H:i:s'),
                        'comments_count' => $post->approved_comments_count ?? 0
                    ];
                }),
                'current_page' => $posts->currentPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
                'last_page' => $posts->lastPage()
            ],
            'success' => true
        ]);
    }

    /**
     * Store a comment on a blog post (Authenticated users only)
     */
    public function storeComment(Request $request, $id)
    {
        $post = BlogPost::where('id', $id)
                       ->where('status', 'published')
                       ->first();

        if (!$post) {
            return response()->json([
                'message' => 'Blog post not found',
                'success' => false
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:blog_comments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $user = auth()->user();

        $comment = BlogComment::create([
            'post_id' => $id,
            'user_id' => $user->id,
            'parent_id' => $request->parent_id,
            'content' => $request->content,
            'author_name' => $user->full_name,
            'author_email' => $user->email,
            'status' => 'pending', // Auto-approve for authenticated users or keep pending for moderation
            'ip_address' => $request->ip()
        ]);

        return response()->json([
            'message' => 'Comment submitted successfully and is pending approval',
            'comment' => [
                'id' => $comment->id,
                'content' => $comment->content,
                'author_name' => $comment->author_name,
                'status' => $comment->status,
                'created_at' => $comment->created_at->format('Y-m-d H:i:s')
            ],
            'success' => true
        ], 201);
    }

    /**
     * Update a comment (Author only)
     */
    public function updateComment(Request $request, $id)
    {
        $comment = BlogComment::where('id', $id)
                             ->where('user_id', auth()->id())
                             ->first();

        if (!$comment) {
            return response()->json([
                'message' => 'Comment not found or you do not have permission to edit it',
                'success' => false
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $comment->update([
            'content' => $request->content,
            'status' => 'pending' // Reset to pending when edited
        ]);

        return response()->json([
            'message' => 'Comment updated successfully',
            'comment' => $comment,
            'success' => true
        ]);
    }

    /**
     * Delete a comment (Author only)
     */
    public function deleteComment($id)
    {
        $comment = BlogComment::where('id', $id)
                             ->where('user_id', auth()->id())
                             ->first();

        if (!$comment) {
            return response()->json([
                'message' => 'Comment not found or you do not have permission to delete it',
                'success' => false
            ], 404);
        }

        // Delete replies first
        BlogComment::where('parent_id', $id)->delete();
        
        // Delete the comment
        $comment->delete();

        return response()->json([
            'message' => 'Comment deleted successfully',
            'success' => true
        ]);
    }

    /**
     * Get comments for a specific post
     */
    public function getPostComments($id)
    {
        $post = BlogPost::findOrFail($id);
        $comments = $post->comments()
                        ->where('status', 'approved')
                        ->whereNull('parent_id')
                        ->with(['user', 'replies' => function($query) {
                            $query->where('status', 'approved');
                        }])
                        ->orderBy('created_at', 'desc')
                        ->get();

        return response()->json([
            'comments' => $comments->map(function($comment) {
                return [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'user' => [
                        'id' => $comment->user->id,
                        'name' => $comment->user->full_name,
                        'avatar' => $comment->user->profile_picture_url
                    ],
                    'created_at' => $comment->created_at->format('Y-m-d H:i:s'),
                    'replies' => $comment->replies->map(function($reply) {
                        return [
                            'id' => $reply->id,
                            'content' => $reply->content,
                            'user' => [
                                'id' => $reply->user->id,
                                'name' => $reply->user->full_name,
                                'avatar' => $reply->user->profile_picture_url
                            ],
                            'created_at' => $reply->created_at->format('Y-m-d H:i:s')
                        ];
                    })
                ];
            }),
            'success' => true
        ]);
    }

    // ========================================
    // ADMIN BLOG MANAGEMENT METHODS
    // ========================================

    /**
     * Admin blog dashboard
     */
    public function adminIndex()
    {
        $stats = [
            'total_posts' => BlogPost::count(),
            'published_posts' => BlogPost::where('status', 'published')->count(),
            'draft_posts' => BlogPost::where('status', 'draft')->count(),
            'total_views' => BlogPost::sum('views_count'),
            'total_categories' => BlogCategory::count(),
            'total_comments' => BlogComment::count()
        ];

        $recentPosts = BlogPost::with(['author', 'category'])
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get()
                              ->map(function($post) {
                                  return [
                                      'id' => $post->id,
                                      'title' => $post->title,
                                      'slug' => $post->slug,
                                      'status' => $post->status,
                                      'author' => $post->author ? $post->author->full_name : 'Unknown',
                                      'category' => $post->category ? $post->category->name : null,
                                      'views' => $post->views_count,
                                      'created_at' => $post->created_at->format('Y-m-d H:i:s')
                                  ];
                              });

        $categories = BlogCategory::withCount('posts')->get();

        $recentComments = BlogComment::with(['user', 'post'])
                                   ->orderBy('created_at', 'desc')
                                   ->limit(5)
                                   ->get()
                                   ->map(function($comment) {
                                       return [
                                           'id' => $comment->id,
                                           'content' => \Str::limit($comment->content, 100),
                                           'user' => $comment->user ? $comment->user->full_name : 'Unknown',
                                           'post' => $comment->post ? $comment->post->title : 'Unknown',
                                           'status' => $comment->status,
                                           'created_at' => $comment->created_at->format('Y-m-d H:i:s')
                                       ];
                                   });

        return response()->json([
            'data' => [
                'stats' => $stats,
                'recent_posts' => $recentPosts,
                'categories' => $categories,
                'recent_comments' => $recentComments
            ],
            'success' => true
        ]);
    }

    /**
     * Admin posts listing (includes all statuses)
     */
    public function adminPosts(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $status = $request->get('status');
        $category = $request->get('category');
        $search = $request->get('search');

        $query = BlogPost::with(['author', 'category']);

        // Filter by status (admin can see all statuses)
        if ($status) {
            $query->where('status', $status);
        }

        // Filter by category
        if ($category) {
            $query->whereHas('category', function($q) use ($category) {
                $q->where('slug', $category);
            });
        }

        // Search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        $posts = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Calculate stats
        $stats = [
            'total_posts' => BlogPost::count(),
            'published_posts' => BlogPost::where('status', 'published')->count(),
            'draft_posts' => BlogPost::where('status', 'draft')->count(),
            'total_views' => BlogPost::sum('views_count')
        ];

        return response()->json([
            'data' => [
                'posts' => $posts->map(function($post) {
                    return [
                        'id' => $post->id,
                        'title' => $post->title,
                        'slug' => $post->slug,
                        'excerpt' => $post->excerpt,
                        'featured_image_url' => $post->featured_image_url,
                        'status' => $post->status,
                        'views' => $post->views_count,
                        'author' => [
                            'id' => $post->author->id,
                            'name' => $post->author->full_name,
                            'avatar' => $post->author->profile_picture_url
                        ],
                        'category' => $post->category ? [
                            'id' => $post->category->id,
                            'name' => $post->category->name,
                            'slug' => $post->category->slug
                        ] : null,
                        'published_at' => $post->published_at ? $post->published_at->format('Y-m-d H:i:s') : null,
                        'created_at' => $post->created_at->format('Y-m-d H:i:s')
                    ];
                }),
                'pagination' => [
                    'current_page' => $posts->currentPage(),
                    'per_page' => $posts->perPage(),
                    'total' => $posts->total(),
                    'last_page' => $posts->lastPage()
                ],
                'stats' => $stats
            ],
            'success' => true
        ]);
    }

    /**
     * Get admin stats
     */
    public function adminStats()
    {
        $stats = [
            'total_posts' => BlogPost::count(),
            'published_posts' => BlogPost::where('status', 'published')->count(),
            'draft_posts' => BlogPost::where('status', 'draft')->count(),
            'archived_posts' => BlogPost::where('status', 'archived')->count(),
            'total_views' => BlogPost::sum('views_count'),
            'total_categories' => BlogCategory::count(),
            'total_comments' => BlogComment::count(),
            'approved_comments' => BlogComment::where('status', 'approved')->count(),
            'pending_comments' => BlogComment::where('status', 'pending')->count()
        ];

        return response()->json([
            'data' => $stats,
            'success' => true
        ]);
    }

    /**
     * Toggle post status
     */
    public function togglePostStatus($id)
    {
        $post = BlogPost::findOrFail($id);
        
        if ($post->status === 'published') {
            $post->update(['status' => 'draft']);
            $message = 'Post unpublished successfully';
        } else {
            $post->update([
                'status' => 'published',
                'published_at' => now()
            ]);
            $message = 'Post published successfully';
        }

        return response()->json([
            'data' => [
                'id' => $post->id,
                'status' => $post->status,
                'published_at' => $post->published_at ? $post->published_at->format('Y-m-d H:i:s') : null
            ],
            'message' => $message,
            'success' => true
        ]);
    }

    /**
     * Delete post (admin only)
     */
    public function deletePost($id)
    {
        $post = BlogPost::findOrFail($id);
        $title = $post->title;

        // Delete associated image
        if ($post->featured_image && \Storage::disk('public')->exists($post->featured_image)) {
            \Storage::disk('public')->delete($post->featured_image);
        }

        // Delete associated comments
        $post->comments()->delete();

        $post->delete();

        return response()->json([
            'message' => "Post '{$title}' deleted successfully",
            'success' => true
        ]);
    }
}
