@extends('layouts.app')

@section('title', $post->title . ' | HLTKKQ Foundation Blog')

@section('content')
<div class="container my-5">
    <!-- Article Header -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold mb-3">{{ $post->title }}</h1>
                <div class="text-muted mb-4">
                    <span><i class="fas fa-calendar-alt me-2"></i>{{ $post->published_at ? $post->published_at->format('F j, Y') : $post->created_at->format('F j, Y') }}</span>
                    <span class="mx-3">|</span>
                    <span><i class="fas fa-user me-2"></i>{{ $post->author->first_name ?? 'HLTKKQ Foundation' }} {{ $post->author->last_name ?? '' }}</span>
                    @if($post->category)
                        <span class="mx-3">|</span>
                        <span><i class="fas fa-tag me-2"></i>{{ $post->category->name ?? 'General' }}</span>
                    @endif
                </div>
                @if($post->excerpt)
                    <p class="lead text-muted">{{ $post->excerpt }}</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Featured Image -->
    @if($post->featured_image)
        <div class="row justify-content-center mb-5">
            <div class="col-lg-10">
                <div class="card border-0 shadow-sm">
                    <img src="{{ asset('storage/' . $post->featured_image) }}" 
                         class="card-img-top rounded" 
                         alt="{{ $post->title }}" 
                         style="height: 400px; object-fit: cover;">
                </div>
            </div>
        </div>
    @endif

    <!-- Article Content -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="content-wrapper">
                <div class="article-content" style="line-height: 1.8; font-size: 1.1rem;">
                    {!! nl2br(e($post->content)) !!}
                </div>
                
                <!-- Article Tags -->
                @if($post->seo_keywords)
                    <div class="tags mt-5 pt-4 border-top">
                        <h5 class="mb-3">Tags:</h5>
                        @foreach(json_decode($post->seo_keywords, true) ?? [] as $tag)
                            <span class="badge bg-primary me-2 mb-2 px-3 py-2" style="font-size: 0.9rem;">{{ trim($tag) }}</span>
                        @endforeach
                    </div>
                @endif
                
                <!-- Share Section -->
                <div class="share-section mt-5 pt-4 border-top">
                    <h5 class="mb-3">Share this article:</h5>
                    <div class="d-flex gap-3">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                           target="_blank" 
                           class="btn btn-outline-primary">
                            <i class="fab fa-facebook-f me-2"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($post->title) }}" 
                           target="_blank" 
                           class="btn btn-outline-info">
                            <i class="fab fa-twitter me-2"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" 
                           target="_blank" 
                           class="btn btn-outline-primary">
                            <i class="fab fa-linkedin-in me-2"></i>LinkedIn
                        </a>
                        <a href="https://wa.me/?text={{ urlencode($post->title . ' - ' . request()->url()) }}" 
                           target="_blank" 
                           class="btn btn-outline-success">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Posts -->
    @if($relatedPosts && $relatedPosts->count() > 0)
        <div class="row justify-content-center mt-5 pt-5">
            <div class="col-lg-10">
                <h3 class="text-center mb-5">Related Articles</h3>
                <div class="row">
                    @foreach($relatedPosts as $related)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                @if($related->featured_image)
                                    <img src="{{ asset('storage/' . $related->featured_image) }}" 
                                         class="card-img-top" 
                                         alt="{{ $related->title }}"
                                         style="height: 200px; object-fit: cover;">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-newspaper fa-3x text-muted"></i>
                                    </div>
                                @endif
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ Str::limit($related->title, 60) }}</h5>
                                    @if($related->excerpt)
                                        <p class="card-text text-muted flex-grow-1">{{ Str::limit($related->excerpt, 100) }}</p>
                                    @endif
                                    <div class="mt-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ $related->published_at ? $related->published_at->format('M j, Y') : $related->created_at->format('M j, Y') }}
                                        </small>
                                        <div class="mt-2">
                                            <a href="{{ route('blog.show', $related->slug) }}" class="btn btn-outline-primary btn-sm">
                                                Read More <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Back to Blog -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-8 text-center">
            <a href="{{ route('blog.index') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Blog
            </a>
        </div>
    </div>
</div>

<style>
.article-content h1, .article-content h2, .article-content h3, 
.article-content h4, .article-content h5, .article-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content ul, .article-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content li {
    margin-bottom: 0.5rem;
}

.article-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #555;
}

.article-content img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
@endsection 