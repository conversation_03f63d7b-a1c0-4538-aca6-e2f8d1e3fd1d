@extends('layouts.admin')

@section('title', 'Edit Educational Resource')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Edit Educational Resource</h1>
                    <p class="text-gray-600">Update learning material information</p>
                </div>
                <a href="{{ route('admin.educational-resources.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white font-semibold rounded-lg shadow-md hover:bg-gray-700 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Resources
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <form action="{{ route('admin.educational-resources.update', $resource->id) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                        <input type="text" name="title" id="title" value="{{ old('title', $resource->title) }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('title') border-red-500 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                        <input type="text" name="category" id="category" value="{{ old('category', $resource->category) }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('category') border-red-500 @enderror">
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                    <textarea name="description" id="description" rows="4" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('description') border-red-500 @enderror">{{ old('description', $resource->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Resource Type and Settings -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type *</label>
                        <select name="type" id="type" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('type') border-red-500 @enderror">
                            <option value="">Select Type</option>
                            <option value="study_guide" {{ old('type', $resource->type) === 'study_guide' ? 'selected' : '' }}>Study Guide</option>
                            <option value="video_course" {{ old('type', $resource->type) === 'video_course' ? 'selected' : '' }}>Video Course</option>
                            <option value="document" {{ old('type', $resource->type) === 'document' ? 'selected' : '' }}>Document</option>
                            <option value="external_link" {{ old('type', $resource->type) === 'external_link' ? 'selected' : '' }}>External Link</option>
                            <option value="interactive" {{ old('type', $resource->type) === 'interactive' ? 'selected' : '' }}>Interactive</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="target_audience" class="block text-sm font-medium text-gray-700 mb-2">Target Audience *</label>
                        <select name="target_audience" id="target_audience" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('target_audience') border-red-500 @enderror">
                            <option value="">Select Audience</option>
                            <option value="primary" {{ old('target_audience', $resource->target_audience) === 'primary' ? 'selected' : '' }}>Primary</option>
                            <option value="secondary" {{ old('target_audience', $resource->target_audience) === 'secondary' ? 'selected' : '' }}>Secondary</option>
                            <option value="university" {{ old('target_audience', $resource->target_audience) === 'university' ? 'selected' : '' }}>University</option>
                            <option value="all" {{ old('target_audience', $resource->target_audience) === 'all' ? 'selected' : '' }}>All</option>
                        </select>
                        @error('target_audience')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level *</label>
                        <select name="difficulty_level" id="difficulty_level" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('difficulty_level') border-red-500 @enderror">
                            <option value="">Select Level</option>
                            <option value="beginner" {{ old('difficulty_level', $resource->difficulty_level) === 'beginner' ? 'selected' : '' }}>Beginner</option>
                            <option value="intermediate" {{ old('difficulty_level', $resource->difficulty_level) === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                            <option value="advanced" {{ old('difficulty_level', $resource->difficulty_level) === 'advanced' ? 'selected' : '' }}>Advanced</option>
                        </select>
                        @error('difficulty_level')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Content URLs -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">Video URL</label>
                        <input type="url" name="video_url" id="video_url" value="{{ old('video_url', $resource->video_url) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('video_url') border-red-500 @enderror">
                        @error('video_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="content_url" class="block text-sm font-medium text-gray-700 mb-2">External Content URL</label>
                        <input type="url" name="content_url" id="content_url" value="{{ old('content_url', $resource->content_url) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('content_url') border-red-500 @enderror">
                        @error('content_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Current Files Display -->
                @if($resource->file_path || $resource->featured_image)
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Files</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        @if($resource->file_path)
                        <div>
                            <p class="text-sm font-medium text-gray-700">Resource File:</p>
                            <a href="{{ asset('storage/' . $resource->file_path) }}" target="_blank" 
                               class="text-green-600 hover:text-green-800 underline">
                                {{ basename($resource->file_path) }}
                            </a>
                        </div>
                        @endif
                        
                        @if($resource->featured_image)
                        <div>
                            <p class="text-sm font-medium text-gray-700">Featured Image:</p>
                            <img src="{{ asset('storage/' . $resource->featured_image) }}" alt="Featured Image" 
                                 class="mt-2 h-20 w-20 object-cover rounded-lg">
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- File Uploads -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-700 mb-2">Resource File</label>
                        <input type="file" name="file" id="file" accept=".pdf,.doc,.docx,.ppt,.pptx,.zip"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('file') border-red-500 @enderror">
                        <p class="mt-1 text-sm text-gray-500">Supported formats: PDF, DOC, DOCX, PPT, PPTX, ZIP (Max: 10MB)</p>
                        @error('file')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">Featured Image</label>
                        <input type="file" name="featured_image" id="featured_image" accept="image/*"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('featured_image') border-red-500 @enderror">
                        <p class="mt-1 text-sm text-gray-500">Supported formats: JPEG, PNG, JPG, GIF (Max: 2MB)</p>
                        @error('featured_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Duration and Tags -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                        <input type="number" name="duration" id="duration" value="{{ old('duration', $resource->duration) }}" min="0" step="0.1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('duration') border-red-500 @enderror">
                        @error('duration')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                        <input type="text" name="tags" id="tags" value="{{ old('tags', is_array($resource->tags) ? implode(', ', $resource->tags) : '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('tags') border-red-500 @enderror">
                        <p class="mt-1 text-sm text-gray-500">Separate tags with commas</p>
                        @error('tags')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Learning Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="learning_objectives" class="block text-sm font-medium text-gray-700 mb-2">Learning Objectives</label>
                        <textarea name="learning_objectives" id="learning_objectives" rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('learning_objectives') border-red-500 @enderror">{{ old('learning_objectives', $resource->learning_objectives) }}</textarea>
                        @error('learning_objectives')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="prerequisites" class="block text-sm font-medium text-gray-700 mb-2">Prerequisites</label>
                        <textarea name="prerequisites" id="prerequisites" rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('prerequisites') border-red-500 @enderror">{{ old('prerequisites', $resource->prerequisites) }}</textarea>
                        @error('prerequisites')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Status Settings -->
                <div class="flex items-center space-x-6">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $resource->is_active) ? 'checked' : '' }}
                               class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured', $resource->is_featured) ? 'checked' : '' }}
                               class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                        <label for="is_featured" class="ml-2 block text-sm text-gray-900">Featured</label>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.educational-resources.index') }}" 
                       class="px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-all duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-lg shadow-md hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                        Update Resource
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
