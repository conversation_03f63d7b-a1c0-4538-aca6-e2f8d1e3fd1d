/* Main Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Sidebar Styles */
#sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.nav-link {
    color: #333;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-link i {
    width: 20px;
}

/* Main Content Styles */
main {
    padding-top: 1.5rem;
}

section {
    margin-bottom: 3rem;
}

h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #eee;
}

/* Card Styles */
.card {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

/* Code Blocks */
pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    margin: 1rem 0;
}

code {
    color: #e83e8c;
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

/* Accordion Styles */
.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0c63e4;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0,0,0,.125);
}

/* Responsive Design */
@media (max-width: 767.98px) {
    #sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }

    main {
        margin-left: 0 !important;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Code Highlighting */
.hljs {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
}

/* Endpoint Method Badges */
.method-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.method-get {
    background-color: #e3f2fd;
    color: #1976d2;
}

.method-post {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.method-put {
    background-color: #fff3e0;
    color: #f57c00;
}

.method-delete {
    background-color: #ffebee;
    color: #c62828;
} 