'use client'

import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'

export default function TestApiPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // Get current user info
    const userStr = localStorage.getItem('user')
    if (userStr) {
      setUser(JSON.parse(userStr))
    }
  }, [])

  const testEndpoint = async (name: string, endpoint: string) => {
    try {
      console.log(`Testing ${name}: ${endpoint}`)
      const response = await apiClient.get(endpoint)
      console.log(`${name} response:`, response)
      setResults(prev => ({
        ...prev,
        [name]: { success: true, data: response }
      }))
    } catch (error) {
      console.error(`${name} error:`, error)
      setResults(prev => ({
        ...prev,
        [name]: { success: false, error: error.message }
      }))
    }
  }

  const testAllEndpoints = async () => {
    setLoading(true)
    setResults({})

    // Test basic endpoints first
    await testEndpoint('User Info', '/user')
    await testEndpoint('Public Scholarships', '/public-scholarships')
    
    // Test partner-specific endpoints
    await testEndpoint('Partner Scholarships Available', '/partner-scholarships/available')
    await testEndpoint('Partner Students', '/partner-students')
    await testEndpoint('Partner Applications', '/partner-scholarships/my-applications')
    await testEndpoint('Partner Statistics', '/partner-students/statistics')
    
    // Test other endpoints
    await testEndpoint('Upcoming Events', '/events/upcoming')
    await testEndpoint('Programs', '/programs')

    setLoading(false)
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">API Testing Page</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Current User Info:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {user ? JSON.stringify(user, null, 2) : 'No user data found'}
        </pre>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Auth Token:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {typeof window !== 'undefined' ? localStorage.getItem('authToken') || 'No token found' : 'Loading...'}
        </pre>
      </div>

      <button
        onClick={testAllEndpoints}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6"
      >
        {loading ? 'Testing...' : 'Test All Endpoints'}
      </button>

      <div className="space-y-4">
        {Object.entries(results).map(([name, result]: [string, any]) => (
          <div key={name} className="border rounded p-4">
            <h3 className="font-semibold mb-2 flex items-center">
              {name}
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.success ? 'SUCCESS' : 'ERROR'}
              </span>
            </h3>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto max-h-40">
              {JSON.stringify(result.success ? result.data : result.error, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  )
}
