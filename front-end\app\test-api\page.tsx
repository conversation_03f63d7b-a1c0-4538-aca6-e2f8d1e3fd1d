'use client'

import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'

export default function TestApiPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [token, setToken] = useState<string | null>(null)

  useEffect(() => {
    // Get current user info and token
    const userStr = localStorage.getItem('user')
    const authToken = localStorage.getItem('authToken')
    if (userStr) {
      setUser(JSON.parse(userStr))
    }
    setToken(authToken)
  }, [])

  const testEndpoint = async (name: string, endpoint: string) => {
    try {
      console.log(`Testing ${name}: ${endpoint}`)
      console.log('Current token:', localStorage.getItem('authToken'))
      console.log('Current user:', localStorage.getItem('user'))

      const response = await apiClient.get(endpoint)
      console.log(`${name} response:`, response)
      setResults((prev: any) => ({
        ...prev,
        [name]: { success: true, data: response }
      }))
    } catch (error: any) {
      console.error(`${name} error:`, error)
      console.error(`${name} error response:`, error.response?.data)
      setResults((prev: any) => ({
        ...prev,
        [name]: { success: false, error: error.message, data: error.response?.data }
      }))
    }
  }

  const testAllEndpoints = async () => {
    setLoading(true)
    setResults({})

    // Test basic endpoints first
    await testEndpoint('User Info', '/user')
    await testEndpoint('Public Scholarships', '/public-scholarships')
    
    // Test partner-specific endpoints
    await testEndpoint('Partner Scholarships Available', '/partner-scholarships/available')
    await testEndpoint('Partner Students', '/partner-students')
    await testEndpoint('Partner Applications', '/partner-scholarships/my-applications')
    await testEndpoint('Partner Statistics', '/partner-students/statistics')
    
    // Test other endpoints
    await testEndpoint('Upcoming Events', '/events/upcoming')
    await testEndpoint('Programs', '/programs')

    setLoading(false)
  }

  const loginAsPartner = async () => {
    try {
      console.log('Attempting login with:', {
        email: '<EMAIL>',
        password: 'password123'
      })

      const response = await apiClient.post('/login', {
        email: '<EMAIL>',
        password: 'password123'
      })

      console.log('Full login response:', response)

      if (response.success && response.data) {
        console.log('User data from login:', response.data.user)
        console.log('User role:', response.data.user.role)
        console.log('Access token:', response.data.access_token)

        localStorage.setItem('authToken', response.data.access_token)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        setUser(response.data.user)
        setToken(response.data.access_token)
        console.log('Login successful - stored in localStorage')
      } else {
        console.error('Login failed - no success or data in response:', response)
      }
    } catch (error) {
      console.error('Login failed with error:', error)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">API Testing Page</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Current User Info:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {user ? JSON.stringify(user, null, 2) : 'No user data found'}
        </pre>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Auth Token:</h2>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {token || 'No token found'}
        </pre>
        <div className="mt-2 space-x-2">
          {!token ? (
            <button
              onClick={loginAsPartner}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Login as Partner
            </button>
          ) : (
            <button
              onClick={() => {
                localStorage.removeItem('authToken')
                localStorage.removeItem('user')
                setToken(null)
                setUser(null)
                console.log('Cleared authentication')
              }}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Logout
            </button>
          )}
        </div>
      </div>

      <button
        onClick={testAllEndpoints}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6"
      >
        {loading ? 'Testing...' : 'Test All Endpoints'}
      </button>

      <div className="space-y-4">
        {Object.entries(results).map(([name, result]: [string, any]) => (
          <div key={name} className="border rounded p-4">
            <h3 className="font-semibold mb-2 flex items-center">
              {name}
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.success ? 'SUCCESS' : 'ERROR'}
              </span>
            </h3>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto max-h-40">
              {JSON.stringify(result.success ? result.data : result.error, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </div>
  )
}
