@extends('layouts.admin')

@section('title', 'Scholarship Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Scholarship Details</h1>
        <div>
            <a href="{{ route('admin.scholarships.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
            </a>
            <a href="{{ route('admin.scholarships.edit', $scholarship->id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Scholarship
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ $scholarship->title }}</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary">Basic Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Title:</strong></td>
                                    <td>{{ $scholarship->title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Slug:</strong></td>
                                    <td><code>{{ $scholarship->slug }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td><strong class="text-success">₦{{ number_format($scholarship->amount, 2) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $scholarship->status === 'open' ? 'success' : ($scholarship->status === 'closed' ? 'danger' : 'secondary') }}">
                                            {{ ucfirst($scholarship->status) }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Application Details</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Deadline:</strong></td>
                                    <td>
                                        {{ $scholarship->application_deadline->format('F d, Y') }}
                                        <br><small class="text-muted">{{ $scholarship->application_deadline->diffForHumans() }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Applicants:</strong></td>
                                    <td>{{ $scholarship->max_applicants ?? 'Unlimited' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $scholarship->created_at->format('M d, Y g:i A') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Updated:</strong></td>
                                    <td>{{ $scholarship->updated_at->format('M d, Y g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary">Description</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $scholarship->description }}
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary">Eligibility Criteria</h6>
                        <div class="bg-light p-3 rounded">
                            {!! nl2br(e($scholarship->eligibility_criteria)) !!}
                        </div>
                    </div>

                    @if($scholarship->requirements)
                        <div class="mb-4">
                            <h6 class="text-primary">Requirements</h6>
                            <ul class="list-group">
                                @foreach($scholarship->requirements as $requirement)
                                    <li class="list-group-item">
                                        <i class="fas fa-check text-success me-2"></i>{{ $requirement }}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ env('FRONTEND_URL', 'https://www.mydomain.com') }}/scholarships/{{ $scholarship->slug }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-external-link-alt me-2"></i>View Public Page
                        </a>
                        <a href="{{ route('admin.scholarships.applications') }}" class="btn btn-outline-success">
                            <i class="fas fa-file-alt me-2"></i>View Applications
                        </a>
                        <a href="{{ route('admin.scholarships.edit', $scholarship->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Scholarship
                        </a>
                        <button class="btn btn-outline-danger" onclick="deleteScholarship({{ $scholarship->id }})">
                            <i class="fas fa-trash me-2"></i>Delete Scholarship
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Application Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ $scholarship->applications_count ?? 0 }}</h4>
                                <small class="text-muted">Total Applications</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $scholarship->approved_applications ?? 0 }}</h4>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-warning">{{ $scholarship->pending_applications ?? 0 }}</h4>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">{{ $scholarship->rejected_applications ?? 0 }}</h4>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteScholarship(scholarshipId) {
    if (confirm('Are you sure you want to delete this scholarship? This action cannot be undone.')) {
        // Add delete functionality here
        console.log('Delete scholarship:', scholarshipId);
    }
}
</script>
@endsection 