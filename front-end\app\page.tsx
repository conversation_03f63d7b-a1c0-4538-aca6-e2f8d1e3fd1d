import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  Users,
  GraduationCap,
  BookOpen,
  ArrowRight,
  Calendar,
  MapPin,
  Star,
  Award,
  Target,
  Lightbulb,
} from "lucide-react"


export default function Home() {
  return (
    <div className="flex flex-col">

      <main className="flex-1">
        {/* Modern Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-amber-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900">
          <div className="container relative z-10 px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <div className="inline-flex items-center gap-2 rounded-full bg-green-100 dark:bg-green-900/30 px-4 py-2 text-sm font-medium text-green-700 dark:text-green-300 mb-8">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Transforming Lives Since 2015
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 dark:text-white leading-tight mb-6">
                Empowering Nigeria Through{" "}
                <span className="bg-gradient-to-r from-green-600 to-amber-500 bg-clip-text text-transparent">
                  Education
                </span>
              </h1>

              <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8 leading-relaxed">
                Providing scholarships, school supplies, and hope to students, underprivileged people, and those in need across Nigeria.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Link href="/donate">
                  <Button
                    size="lg"
                    className="bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full px-8 py-4 text-lg font-semibold"
                  >
                    Donate Now
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 rounded-full px-8 py-4 text-lg font-semibold"
                  >
                    Become a Volunteer
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Little About Us Section */}
        <section className="py-20 bg-white dark:bg-gray-950">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div className="space-y-6">
                <div className="inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200">
                  About Us
                </div>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white leading-tight">
                  Building Brighter Futures Through Education
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  The Halimatu Abdullahi Kofa Knowledge Quest Foundation has been transforming lives across Nigeria since 2015. We believe that education is the key to breaking the cycle of poverty and creating lasting change in communities, supporting students, underprivileged individuals, and those in need.
                </p>
                <p className="text-gray-600 dark:text-gray-300">
                  Our comprehensive approach includes providing scholarships, school supplies, literacy programs, and
                  community development initiatives to ensure every child has access to quality education, regardless of
                  their background.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Link href="/about">
                    <Button className="bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group">
                      Learn More About Us
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </Link>
                  <Link href="/impact">
                    <Button
                      variant="outline"
                      className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white rounded-full px-6 py-3 font-semibold transition-all duration-300"
                    >
                      View Our Impact
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Image */}
              <div className="relative">
                <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                  <Image
                    src="https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=600&h=450&fit=crop&crop=center"
                    alt="Students in classroom receiving quality education"
                    fill
                    sizes="(max-width: 768px) 100vw, 50vw"
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                {/* Floating Stats Card */}
                <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-900 rounded-xl shadow-lg p-4 border border-green-100 dark:border-green-800">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                      <Award className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">9+</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Years of Impact</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Programs Showcase */}
        <section className="py-20 bg-gray-50 dark:bg-gray-900">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4">
                Our Programs
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                How We Create Change
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Discover our comprehensive educational programs that are transforming lives across Nigeria.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Primary Schools */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1566911294210-d11bb2b0ad55?w=400&h=300&fit=crop&crop=center"
                    alt="Primary school children learning in classroom"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Primary Schools
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <BookOpen className="h-5 w-5 text-blue-600" />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Early Foundation</h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Building strong educational foundations with basic literacy, numeracy, and learning skills for children aged 6-12 years.
                  </p>
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-4">
                    Ages 6-12 • Foundation Learning
                  </div>
                  <Link href="/projects">
                    <Button
                      variant="outline"
                      className="w-full rounded-full border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300"
                    >
                      Learn More
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Secondary Schools */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden ring-2 ring-green-500/20">
                <Badge className="absolute top-4 right-4 z-10 bg-green-500 text-white rounded-full">Featured</Badge>
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=300&fit=crop&crop=center"
                    alt="Secondary school students in science laboratory"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Secondary Schools
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Target className="h-5 w-5 text-green-600" />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Academic Excellence</h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Preparing students for WAEC, NECO, and JAMB with comprehensive subject support and career guidance for ages 13-18.
                  </p>
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-4">
                    Ages 13-18 • WAEC/NECO Prep
                  </div>
                  <Link href="/scholarships">
                    <Button className="w-full bg-green-600 hover:bg-green-700 text-white rounded-full">
                      Explore Programs
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* University */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=300&fit=crop&crop=center"
                    alt="University students in graduation ceremony"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    University
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <GraduationCap className="h-5 w-5 text-purple-600" />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Higher Education</h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Comprehensive scholarships and mentorship programs for undergraduate and postgraduate studies across various fields.
                  </p>
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-4">
                    Full Scholarships • Career Mentoring
                  </div>
                  <Link href="/scholarships">
                    <Button
                      variant="outline"
                      className="w-full rounded-full border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-300"
                    >
                      Apply Now
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Impact Section */}
        <section className="py-20 bg-white dark:bg-gray-950">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4">
                Our Impact
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Making a Real Difference
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                See how your support is transforming lives and communities across Northern Nigeria.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-6">
                <Card className="text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">1,200+</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Students Supported</div>
                </Card>

                <Card className="text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl">
                  <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <GraduationCap className="h-6 w-6 text-amber-600" />
                  </div>
                  <div className="text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2">45</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Schools Partnered</div>
                </Card>

                <Card className="text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookOpen className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">5,000+</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Books Donated</div>
                </Card>

                <Card className="text-center p-6 border-green-100 dark:border-green-800 hover:shadow-lg transition-all duration-300 rounded-xl">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">₦12M</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">In Scholarships</div>
                </Card>
              </div>

              {/* Content */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Transforming Communities Through Education
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  Every donation, every scholarship, and every book donated creates a ripple effect that transforms not
                  just individual lives, but entire communities. Our holistic approach ensures sustainable impact that
                  lasts for generations.
                </p>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <Star className="h-3 w-3 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Quality Education Access</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Ensuring every child has access to quality educational resources and opportunities.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <Lightbulb className="h-3 w-3 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Community Development</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Building stronger communities through literacy and educational programs.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                      <Heart className="h-3 w-3 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Sustainable Impact</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Creating lasting change that benefits future generations.
                      </p>
                    </div>
                  </div>
                </div>

                <Link href="/impact">
                  <Button className="bg-green-600 hover:bg-green-700 text-white rounded-full px-6 py-3 font-semibold group">
                    View Full Impact Report
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Upcoming Events */}
        <section className="py-20 bg-gray-50 dark:bg-gray-900">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className="inline-block rounded-lg bg-green-100 dark:bg-green-900/30 px-3 py-1 text-sm text-green-800 dark:text-green-200 mb-4">
                Upcoming Events
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">Join Us in Action</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Participate in our upcoming events and be part of the change in Northern Nigeria.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Event 1 */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?w=400&h=300&fit=crop&crop=center"
                    alt="Back-to-School Initiative Launch"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-amber-500 text-white rounded-full">Featured</Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <Calendar className="h-4 w-4" />
                    <span>June 15, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Back-to-School Initiative Launch
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    Kickstarting the academic year with essential school supplies for underprivileged students.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <MapPin className="h-4 w-4" />
                    <span>Kaduna North Local Government</span>
                  </div>
                  <Button variant="outline" className="w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300">
                    Register Now
                  </Button>
                </CardContent>
              </Card>

              {/* Event 2 */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400&h=300&fit=crop&crop=center"
                    alt="Scholarship Award Ceremony"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <Calendar className="h-4 w-4" />
                    <span>July 22, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Scholarship Award Ceremony
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    Recognizing and awarding scholarships to deserving students.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <MapPin className="h-4 w-4" />
                    <span>Katsina State Secretariat</span>
                  </div>
                  <Button variant="outline" className="w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300">
                    Learn More
                  </Button>
                </CardContent>
              </Card>

              {/* Event 3 */}
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-green-100 dark:border-green-800 rounded-2xl overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=400&h=300&fit=crop&crop=center"
                    alt="Community Literacy Program"
                    fill
                    sizes="(max-width: 768px) 100vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <Calendar className="h-4 w-4" />
                    <span>August 5, 2024</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Community Literacy Program
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    Empowering adults with essential literacy skills for development.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <MapPin className="h-4 w-4" />
                    <span>Kano Municipal Library</span>
                  </div>
                  <Button variant="outline" className="w-full rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300">
                    Join Program
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="text-center mt-12">
              <Link href="/events">
                <Button variant="outline" className="rounded-full px-8 py-3 font-semibold border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300">
                  View All Events
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-green-600 dark:bg-green-800 text-white">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl sm:text-4xl font-bold mb-6">Ready to Make a Difference?</h2>
              <p className="text-lg text-green-100 mb-8 leading-relaxed">
                Join us in our mission to transform lives through education. Whether through donations, volunteering, or
                spreading awareness, every action counts.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/donate">
                  <Button
                    size="lg"
                    className="bg-white text-green-600 hover:bg-gray-100 hover:text-green-700 rounded-full px-8 py-4 font-semibold group shadow-lg"
                  >
                    Donate Today
                    <Heart className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-2 border-white text-white hover:bg-white hover:text-green-600 rounded-full px-8 py-4 font-semibold transition-all duration-300"
                  >
                    Become a Volunteer
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>


    </div>
  )
}
