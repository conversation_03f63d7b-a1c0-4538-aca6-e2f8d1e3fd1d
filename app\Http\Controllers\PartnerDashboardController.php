<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Scholarship;
use App\Models\PartnerStudent;
use App\Models\ScholarshipApplication;

class PartnerDashboardController extends Controller
{
    /**
     * Display the partner organization dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Ensure user is a partner organization
        if (!$user->isPartnerOrganization()) {
            return redirect($user->dashboard_route);
        }

        // Get available scholarships for Primary and Secondary categories
        $availableScholarships = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->whereIn('category', ['primary', 'secondary'])
            ->where('application_deadline', '>', now())
            ->orderBy('application_deadline', 'asc')
            ->take(6) // Limit to 6 for dashboard display
            ->get();

        // Get scholarship statistics
        $scholarshipStats = [
            'total_available' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->whereIn('category', ['primary', 'secondary'])
                ->where('application_deadline', '>', now())
                ->count(),
            'primary_count' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'primary')
                ->where('application_deadline', '>', now())
                ->count(),
            'secondary_count' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'secondary')
                ->where('application_deadline', '>', now())
                ->count(),
            'my_applications' => ScholarshipApplication::where('user_id', $user->id)
                ->orWhere('school_id', $user->id)
                ->count(),
        ];

        return view('dashboards.partner.index', compact('availableScholarships', 'scholarshipStats'));
    }
}
