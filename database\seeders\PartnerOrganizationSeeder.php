<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PartnerOrganization;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class PartnerOrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample partner organizations
        $organizations = [
            [
                'name' => 'Lagos State Primary School',
                'type' => 'school',
                'description' => 'A leading primary school in Lagos State providing quality education to children.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '123 Education Avenue, Victoria Island',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'established_year' => 2020,
                'student_capacity' => 500,
                'current_students' => 350,
                'contact_person' => 'Mrs. <PERSON><PERSON><PERSON>',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-01-01',
                'is_verified' => true,
                'verification_date' => now(),
            ],
            [
                'name' => 'Abuja International Secondary School',
                'type' => 'school',
                'description' => 'A prestigious secondary school in Abuja offering comprehensive education.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '456 Academic Road, Wuse II',
                'city' => 'Abuja',
                'state' => 'FCT',
                'country' => 'Nigeria',
                'established_year' => 2018,
                'student_capacity' => 800,
                'current_students' => 650,
                'contact_person' => 'Dr. Ibrahim Musa',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-01-01',
                'is_verified' => true,
                'verification_date' => now(),
            ],
            [
                'name' => 'Kano Community Primary School',
                'type' => 'school',
                'description' => 'A community-based primary school serving rural areas in Kano State.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '789 Community Street, Fagge',
                'city' => 'Kano',
                'state' => 'Kano',
                'country' => 'Nigeria',
                'established_year' => 2019,
                'student_capacity' => 300,
                'current_students' => 280,
                'contact_person' => 'Malam Sani Abdullahi',
                'contact_person_title' => 'Head Teacher',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-02-01',
                'is_verified' => true,
                'verification_date' => now(),
            ],
            [
                'name' => 'Port Harcourt Girls Secondary School',
                'type' => 'school',
                'description' => 'An all-girls secondary school focused on empowering young women.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '321 Girls Education Lane, GRA',
                'city' => 'Port Harcourt',
                'state' => 'Rivers',
                'country' => 'Nigeria',
                'established_year' => 2017,
                'student_capacity' => 600,
                'current_students' => 520,
                'contact_person' => 'Mrs. Grace Okoro',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'partnership_status' => 'pending',
                'is_verified' => false,
                'verification_date' => null,
            ],
        ];

        foreach ($organizations as $orgData) {
            $org = PartnerOrganization::create($orgData);

            // Create a user account for each organization
            $userData = [
                'first_name' => explode(' ', $orgData['contact_person'])[0] ?? 'Contact',
                'last_name' => explode(' ', $orgData['contact_person'])[1] ?? 'Person',
                'email' => $orgData['contact_person_email'],
                'password' => Hash::make('password123'),
                'phone_number' => $orgData['contact_person_phone'],
                'role' => 'partner_organization',
                'partner_organization_id' => $org->id,
                'status' => 'active',
                'email_verified_at' => now(),
                'city' => $orgData['city'],
                'state' => $orgData['state'],
                'country' => $orgData['country'],
            ];

            $user = User::create($userData);
        }
    }
}
