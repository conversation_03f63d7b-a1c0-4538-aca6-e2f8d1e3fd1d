import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from './use-auth'
import { useToast } from './use-toast'
import { ScholarshipRoutingService, ScholarshipRoutingConfig, RoutingResult } from '@/lib/scholarshipRouting'

export interface UseScholarshipRoutingOptions {
  category?: 'primary' | 'secondary' | 'university'
  scholarshipId?: string
  autoRedirect?: boolean
  showToast?: boolean
}

export interface ScholarshipRoutingState {
  isLoading: boolean
  hasAccess: boolean
  shouldRedirect: boolean
  redirectUrl?: string
  errorMessage?: string
  requiredRole?: string
  categoryMessages: {
    title: string
    description: string
    loginPrompt: string
    accessRequirement: string
  }
}

/**
 * Hook for handling scholarship category-based routing and access control
 */
export function useScholarshipRouting(options: UseScholarshipRoutingOptions = {}) {
  const { category, scholarshipId, autoRedirect = true, showToast = true } = options
  const { user, isLoading: authLoading } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  const [state, setState] = useState<ScholarshipRoutingState>({
    isLoading: true,
    hasAccess: false,
    shouldRedirect: false,
    categoryMessages: ScholarshipRoutingService.getCategoryMessages(category || 'primary')
  })

  // Handle routing logic
  useEffect(() => {
    if (authLoading) {
      setState(prev => ({ ...prev, isLoading: true }))
      return
    }

    if (!category) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        hasAccess: true,
        shouldRedirect: false 
      }))
      return
    }

    const routingResult = ScholarshipRoutingService.determineRouting({
      category,
      scholarshipId,
      redirectUrl: window.location.pathname + window.location.search
    }, user)

    const categoryMessages = ScholarshipRoutingService.getCategoryMessages(category)

    setState({
      isLoading: false,
      hasAccess: !routingResult.shouldRedirect,
      shouldRedirect: routingResult.shouldRedirect,
      redirectUrl: routingResult.redirectUrl,
      errorMessage: routingResult.errorMessage,
      requiredRole: routingResult.requiredRole,
      categoryMessages
    })

    // Handle automatic redirects
    if (autoRedirect && routingResult.shouldRedirect && routingResult.redirectUrl) {
      if (showToast && routingResult.errorMessage) {
        toast({
          title: "Access Required",
          description: routingResult.errorMessage,
          variant: routingResult.requiresAuth ? "default" : "destructive",
        })
      }

      // Small delay to allow toast to show
      setTimeout(() => {
        router.push(routingResult.redirectUrl!)
      }, showToast ? 1000 : 0)
    }

  }, [authLoading, user, category, scholarshipId, autoRedirect, showToast, router, toast])

  // Handle post-login redirect
  useEffect(() => {
    if (user && !authLoading) {
      const redirectUrl = ScholarshipRoutingService.handlePostLoginRedirect()
      if (redirectUrl && redirectUrl !== window.location.pathname + window.location.search) {
        router.push(redirectUrl)
      }
    }
  }, [user, authLoading, router])

  /**
   * Manually trigger routing check
   */
  const checkRouting = (newCategory?: string, newScholarshipId?: string) => {
    const targetCategory = newCategory || category
    if (!targetCategory) return

    const routingResult = ScholarshipRoutingService.determineRouting({
      category: targetCategory as 'primary' | 'secondary' | 'university',
      scholarshipId: newScholarshipId || scholarshipId,
    }, user)

    return routingResult
  }

  /**
   * Get application form URL for current context
   */
  const getApplicationUrl = (targetScholarshipId?: string, targetCategory?: string) => {
    const finalScholarshipId = targetScholarshipId || scholarshipId
    const finalCategory = targetCategory || category

    if (!finalScholarshipId || !finalCategory) {
      return '/scholarships'
    }

    return ScholarshipRoutingService.getApplicationFormUrl(
      finalScholarshipId,
      finalCategory,
      user
    )
  }

  /**
   * Validate access for a specific category
   */
  const validateAccess = (targetCategory?: string) => {
    const finalCategory = targetCategory || category
    if (!finalCategory) return { hasAccess: true }

    return ScholarshipRoutingService.validateUserAccess(finalCategory, user)
  }

  /**
   * Navigate to appropriate dashboard
   */
  const navigateToDashboard = (targetCategory?: string, targetScholarshipId?: string) => {
    const finalCategory = targetCategory || category || 'primary'
    const userRole = user?.role || 'user'
    
    let dashboardUrl = '/dashboard'
    
    if ((finalCategory === 'primary' || finalCategory === 'secondary') && 
        (userRole === 'partner_organization' || user?.partner_organization_id)) {
      dashboardUrl = '/dashboard/partner'
    } else if (finalCategory === 'university' && !user?.partner_organization_id) {
      dashboardUrl = '/dashboard/student'
    }

    if (targetScholarshipId) {
      dashboardUrl += `?scholarship_id=${targetScholarshipId}&category=${finalCategory}`
    }

    router.push(dashboardUrl)
  }

  /**
   * Navigate to login with proper context
   */
  const navigateToLogin = (targetCategory?: string, targetScholarshipId?: string) => {
    const finalCategory = targetCategory || category || 'primary'
    let loginUrl = '/auth/login'

    if (finalCategory === 'primary' || finalCategory === 'secondary') {
      loginUrl += '?type=partner'
    } else if (finalCategory === 'university') {
      loginUrl += '?type=student'
    }

    if (targetScholarshipId) {
      loginUrl += `&scholarship_id=${targetScholarshipId}&category=${finalCategory}`
    }

    // Store current URL for redirect after login
    const currentUrl = window.location.pathname + window.location.search
    localStorage.setItem('redirectAfterLogin', currentUrl)

    router.push(loginUrl)
  }

  return {
    ...state,
    checkRouting,
    getApplicationUrl,
    validateAccess,
    navigateToDashboard,
    navigateToLogin,
    user,
    isAuthenticated: !!user
  }
}

/**
 * Simplified hook for scholarship application pages
 */
export function useScholarshipApplicationRouting(scholarshipId: string, category: string) {
  return useScholarshipRouting({
    category: category as 'primary' | 'secondary' | 'university',
    scholarshipId,
    autoRedirect: true,
    showToast: true
  })
}

/**
 * Hook for scholarship listing pages with category filtering
 */
export function useScholarshipCategoryRouting(category?: string) {
  return useScholarshipRouting({
    category: category as 'primary' | 'secondary' | 'university' | undefined,
    autoRedirect: false,
    showToast: false
  })
}
