<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PartnerOrganization;
use App\Models\Student;
use App\Models\ScholarshipApplication;
use App\Models\StudentProgression;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PartnerAnalyticsController extends Controller
{
    /**
     * Get comprehensive analytics data for partner organization
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $period = $request->get('period', '12months');
            $category = $request->get('category', 'all');

            // Calculate date range based on period
            $dateRange = $this->getDateRange($period);

            // Get overview statistics
            $overview = $this->getOverviewStatistics($partnerOrg, $dateRange, $category);

            // Get application trends
            $applicationTrends = $this->getApplicationTrends($partnerOrg, $dateRange, $category);

            // Get success by category
            $successByCategory = $this->getSuccessByCategory($partnerOrg, $dateRange);

            // Get grade distribution
            $gradeDistribution = $this->getGradeDistribution($partnerOrg);

            // Get monthly progression data
            $monthlyProgression = $this->getMonthlyProgression($partnerOrg, $dateRange);

            // Get top performing students
            $topPerformingStudents = $this->getTopPerformingStudents($partnerOrg, $dateRange);

            return response()->json([
                'success' => true,
                'data' => [
                    'overview' => $overview,
                    'application_trends' => $applicationTrends,
                    'success_by_category' => $successByCategory,
                    'grade_distribution' => $gradeDistribution,
                    'monthly_progression' => $monthlyProgression,
                    'top_performing_students' => $topPerformingStudents,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch analytics data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export analytics report as CSV
     */
    public function export(Request $request): StreamedResponse
    {
        $user = Auth::user();
        $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

        $period = $request->get('period', '12months');
        $category = $request->get('category', 'all');
        $dateRange = $this->getDateRange($period);

        $filename = "analytics-report-{$partnerOrg->name}-" . date('Y-m-d') . ".csv";

        return new StreamedResponse(function () use ($partnerOrg, $dateRange, $category) {
            $handle = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($handle, ['Analytics Report - ' . $partnerOrg->name]);
            fputcsv($handle, ['Generated on: ' . date('Y-m-d H:i:s')]);
            fputcsv($handle, []);

            // Overview section
            fputcsv($handle, ['OVERVIEW STATISTICS']);
            $overview = $this->getOverviewStatistics($partnerOrg, $dateRange, $category);
            fputcsv($handle, ['Total Students', $overview['total_students']]);
            fputcsv($handle, ['Total Applications', $overview['total_applications']]);
            fputcsv($handle, ['Success Rate (%)', $overview['success_rate']]);
            fputcsv($handle, ['Total Amount Awarded (₦)', number_format($overview['total_amount_awarded'])]);
            fputcsv($handle, ['Active Scholarships', $overview['active_scholarships']]);
            fputcsv($handle, ['Pending Applications', $overview['pending_applications']]);
            fputcsv($handle, []);

            // Applications by category
            fputcsv($handle, ['APPLICATIONS BY CATEGORY']);
            fputcsv($handle, ['Category', 'Total Applications', 'Approved', 'Success Rate (%)']);
            $successByCategory = $this->getSuccessByCategory($partnerOrg, $dateRange);
            foreach ($successByCategory as $category) {
                fputcsv($handle, [
                    $category['category'],
                    $category['total'],
                    $category['approved'],
                    $category['success_rate']
                ]);
            }
            fputcsv($handle, []);

            // Grade distribution
            fputcsv($handle, ['GRADE DISTRIBUTION']);
            fputcsv($handle, ['Grade Level', 'Total Students', 'Applications']);
            $gradeDistribution = $this->getGradeDistribution($partnerOrg);
            foreach ($gradeDistribution as $grade) {
                fputcsv($handle, [
                    $grade['grade'],
                    $grade['students'],
                    $grade['applications']
                ]);
            }
            fputcsv($handle, []);

            // Top performing students
            fputcsv($handle, ['TOP PERFORMING STUDENTS']);
            fputcsv($handle, ['Student Name', 'Grade', 'Total Applications', 'Approved', 'Total Amount (₦)']);
            $topStudents = $this->getTopPerformingStudents($partnerOrg, $dateRange);
            foreach ($topStudents as $student) {
                fputcsv($handle, [
                    $student['name'],
                    $student['grade'],
                    $student['applications'],
                    $student['approved'],
                    number_format($student['total_amount'])
                ]);
            }

            fclose($handle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Get date range based on period
     */
    private function getDateRange(string $period): array
    {
        $endDate = Carbon::now();
        
        switch ($period) {
            case '3months':
                $startDate = $endDate->copy()->subMonths(3);
                break;
            case '6months':
                $startDate = $endDate->copy()->subMonths(6);
                break;
            case '12months':
                $startDate = $endDate->copy()->subMonths(12);
                break;
            case 'all':
            default:
                $startDate = Carbon::create(2020, 1, 1); // Far back date
                break;
        }

        return [$startDate, $endDate];
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStatistics(PartnerOrganization $partnerOrg, array $dateRange, string $category): array
    {
        [$startDate, $endDate] = $dateRange;

        // Total students
        $totalStudents = Student::where('school_id', $partnerOrg->id)->count();

        // Applications query
        $applicationsQuery = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg) {
            $query->where('school_id', $partnerOrg->id);
        })->whereBetween('created_at', [$startDate, $endDate]);

        if ($category !== 'all') {
            $applicationsQuery->where('category', $category);
        }

        $totalApplications = $applicationsQuery->count();
        $approvedApplications = $applicationsQuery->where('status', 'approved')->count();
        $pendingApplications = $applicationsQuery->where('status', 'pending')->count();

        // Success rate
        $successRate = $totalApplications > 0 ? round(($approvedApplications / $totalApplications) * 100, 1) : 0;

        // Total amount awarded
        $totalAmountAwarded = $applicationsQuery->where('status', 'approved')
            ->join('scholarships', 'scholarship_applications.scholarship_id', '=', 'scholarships.id')
            ->sum('scholarships.amount');

        // Active scholarships (scholarships with open applications)
        $activeScholarships = DB::table('scholarships')
            ->where('application_deadline', '>', Carbon::now())
            ->where('status', 'active')
            ->count();

        return [
            'total_students' => $totalStudents,
            'total_applications' => $totalApplications,
            'success_rate' => $successRate,
            'total_amount_awarded' => $totalAmountAwarded,
            'active_scholarships' => $activeScholarships,
            'pending_applications' => $pendingApplications,
        ];
    }

    /**
     * Get application trends over time
     */
    private function getApplicationTrends(PartnerOrganization $partnerOrg, array $dateRange, string $category): array
    {
        [$startDate, $endDate] = $dateRange;

        $trends = DB::table('scholarship_applications')
            ->join('students', 'scholarship_applications.student_id', '=', 'students.id')
            ->where('students.school_id', $partnerOrg->id)
            ->whereBetween('scholarship_applications.created_at', [$startDate, $endDate])
            ->when($category !== 'all', function ($query) use ($category) {
                return $query->where('scholarship_applications.category', $category);
            })
            ->select(
                DB::raw('DATE_FORMAT(scholarship_applications.created_at, "%b") as month'),
                DB::raw('COUNT(*) as applications'),
                DB::raw('SUM(CASE WHEN scholarship_applications.status = "approved" THEN 1 ELSE 0 END) as approved'),
                DB::raw('SUM(CASE WHEN scholarship_applications.status = "rejected" THEN 1 ELSE 0 END) as rejected')
            )
            ->groupBy(DB::raw('DATE_FORMAT(scholarship_applications.created_at, "%Y-%m")'))
            ->orderBy(DB::raw('DATE_FORMAT(scholarship_applications.created_at, "%Y-%m")'))
            ->get()
            ->toArray();

        return array_map(function ($trend) {
            return [
                'month' => $trend->month,
                'applications' => (int) $trend->applications,
                'approved' => (int) $trend->approved,
                'rejected' => (int) $trend->rejected,
            ];
        }, $trends);
    }

    /**
     * Get success rates by category
     */
    private function getSuccessByCategory(PartnerOrganization $partnerOrg, array $dateRange): array
    {
        [$startDate, $endDate] = $dateRange;

        $categories = DB::table('scholarship_applications')
            ->join('students', 'scholarship_applications.student_id', '=', 'students.id')
            ->where('students.school_id', $partnerOrg->id)
            ->whereBetween('scholarship_applications.created_at', [$startDate, $endDate])
            ->select(
                'scholarship_applications.category',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN scholarship_applications.status = "approved" THEN 1 ELSE 0 END) as approved')
            )
            ->groupBy('scholarship_applications.category')
            ->get();

        return $categories->map(function ($category) {
            $successRate = $category->total > 0 ? round(($category->approved / $category->total) * 100, 1) : 0;

            return [
                'category' => ucfirst($category->category),
                'total' => (int) $category->total,
                'approved' => (int) $category->approved,
                'success_rate' => $successRate,
            ];
        })->toArray();
    }

    /**
     * Get grade distribution
     */
    private function getGradeDistribution(PartnerOrganization $partnerOrg): array
    {
        $gradeGroups = [
            'Primary 1-3' => ['primary_1', 'primary_2', 'primary_3'],
            'Primary 4-6' => ['primary_4', 'primary_5', 'primary_6'],
            'Secondary 1-3' => ['secondary_1', 'secondary_2', 'secondary_3'],
            'Secondary 4-6' => ['secondary_4', 'secondary_5', 'secondary_6'],
        ];

        $distribution = [];

        foreach ($gradeGroups as $groupName => $grades) {
            $students = Student::where('school_id', $partnerOrg->id)
                ->whereIn('grade_level', $grades)
                ->count();

            $applications = ScholarshipApplication::whereHas('student', function ($query) use ($partnerOrg, $grades) {
                $query->where('school_id', $partnerOrg->id)
                      ->whereIn('grade_level', $grades);
            })->count();

            $distribution[] = [
                'grade' => $groupName,
                'students' => $students,
                'applications' => $applications,
            ];
        }

        return $distribution;
    }

    /**
     * Get monthly progression data
     */
    private function getMonthlyProgression(PartnerOrganization $partnerOrg, array $dateRange): array
    {
        [$startDate, $endDate] = $dateRange;

        $progressions = DB::table('student_progressions')
            ->join('students', 'student_progressions.student_id', '=', 'students.id')
            ->where('students.school_id', $partnerOrg->id)
            ->whereBetween('student_progressions.created_at', [$startDate, $endDate])
            ->select(
                DB::raw('DATE_FORMAT(student_progressions.created_at, "%b") as month'),
                DB::raw('COUNT(*) as progressions'),
                DB::raw('SUM(CASE WHEN student_progressions.status = "completed" THEN 1 ELSE 0 END) as completed')
            )
            ->groupBy(DB::raw('DATE_FORMAT(student_progressions.created_at, "%Y-%m")'))
            ->orderBy(DB::raw('DATE_FORMAT(student_progressions.created_at, "%Y-%m")'))
            ->get()
            ->toArray();

        return array_map(function ($progression) {
            return [
                'month' => $progression->month,
                'progressions' => (int) $progression->progressions,
                'completed' => (int) $progression->completed,
            ];
        }, $progressions);
    }

    /**
     * Get top performing students
     */
    private function getTopPerformingStudents(PartnerOrganization $partnerOrg, array $dateRange): array
    {
        [$startDate, $endDate] = $dateRange;

        $students = DB::table('students')
            ->leftJoin('scholarship_applications', 'students.id', '=', 'scholarship_applications.student_id')
            ->leftJoin('scholarships', function ($join) {
                $join->on('scholarship_applications.scholarship_id', '=', 'scholarships.id')
                     ->where('scholarship_applications.status', '=', 'approved');
            })
            ->where('students.school_id', $partnerOrg->id)
            ->whereBetween('scholarship_applications.created_at', [$startDate, $endDate])
            ->select(
                'students.id',
                'students.full_name as name',
                'students.grade_level as grade',
                DB::raw('COUNT(scholarship_applications.id) as applications'),
                DB::raw('SUM(CASE WHEN scholarship_applications.status = "approved" THEN 1 ELSE 0 END) as approved'),
                DB::raw('COALESCE(SUM(scholarships.amount), 0) as total_amount')
            )
            ->groupBy('students.id', 'students.full_name', 'students.grade_level')
            ->having('applications', '>', 0)
            ->orderByDesc('total_amount')
            ->limit(10)
            ->get();

        return $students->map(function ($student) {
            return [
                'id' => $student->id,
                'name' => $student->name,
                'grade' => ucfirst(str_replace('_', ' ', $student->grade)),
                'applications' => (int) $student->applications,
                'approved' => (int) $student->approved,
                'total_amount' => (int) $student->total_amount,
            ];
        })->toArray();
    }
}
