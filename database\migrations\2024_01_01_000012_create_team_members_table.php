<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('team_members', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('slug', 110)->unique();
            $table->string('position', 100);
            $table->string('department', 100)->nullable();
            $table->text('bio')->nullable();
            $table->string('photo')->nullable();
            $table->string('email', 100)->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('facebook_url')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('achievements')->nullable();
            $table->json('qualifications')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('slug');
            $table->index('department');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('team_members');
    }
}; 