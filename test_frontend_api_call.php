<?php

echo "=== Testing Frontend API Call Simulation ===\n\n";

// Simulate what the frontend is doing
$baseUrl = 'http://127.0.0.1:8000/api/v1';

echo "Frontend API Call: GET {$baseUrl}/public-scholarships/5\n";
echo "This is exactly what the Next.js frontend calls\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/public-scholarships/5');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✓ API Response successful\n";
        $scholarship = $data['data'];
        
        echo "\n=== Data Validation for Frontend ===\n";
        
        // Check documents_required
        echo "documents_required: ";
        if (isset($scholarship['documents_required'])) {
            if (is_array($scholarship['documents_required'])) {
                echo "✓ Array with " . count($scholarship['documents_required']) . " items\n";
                foreach ($scholarship['documents_required'] as $doc) {
                    echo "  - $doc\n";
                }
            } else {
                echo "✗ Not an array: " . gettype($scholarship['documents_required']) . "\n";
            }
        } else {
            echo "✗ Field missing\n";
        }
        
        // Check custom_fields
        echo "\ncustom_fields: ";
        if (isset($scholarship['custom_fields'])) {
            if (is_array($scholarship['custom_fields'])) {
                echo "✓ Array with " . count($scholarship['custom_fields']) . " items\n";
                echo "  Sample fields:\n";
                foreach (array_slice($scholarship['custom_fields'], 0, 3) as $field) {
                    echo "  - " . $field['field_name'] . " (" . $field['field_type'] . ")\n";
                }
            } else {
                echo "✗ Not an array: " . gettype($scholarship['custom_fields']) . "\n";
            }
        } else {
            echo "✗ Field missing\n";
        }
        
        // Check category_instructions
        echo "\ncategory_instructions: ";
        if (isset($scholarship['category_instructions'])) {
            if (is_array($scholarship['category_instructions'])) {
                echo "✓ Array/Object present\n";
                if (isset($scholarship['category_instructions']['required_info'])) {
                    if (is_array($scholarship['category_instructions']['required_info'])) {
                        echo "  required_info: ✓ Array with " . count($scholarship['category_instructions']['required_info']) . " items\n";
                    } else {
                        echo "  required_info: ✗ Not an array\n";
                    }
                } else {
                    echo "  required_info: ✗ Missing\n";
                }
            } else {
                echo "✗ Not an array: " . gettype($scholarship['category_instructions']) . "\n";
            }
        } else {
            echo "✗ Field missing\n";
        }
        
        echo "\n=== Frontend Safety Check ===\n";
        echo "All array fields have proper type checking in frontend code\n";
        echo "Frontend should now handle the data correctly\n";
        
    } else {
        echo "✗ API Error: " . ($data['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "✗ HTTP Error: $httpCode\n";
    echo "Response: $response\n";
}

echo "\n=== Test Complete ===\n";
