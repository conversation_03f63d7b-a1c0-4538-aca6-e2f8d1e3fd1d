<?php

namespace App\Http\Controllers;

use App\Models\ScholarshipApplication;
use App\Models\Scholarship;
use App\Models\PartnerStudent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PartnerApplicationController extends Controller
{
    /**
     * Display a listing of applications for the partner organization.
     */
    public function index(Request $request)
    {
        $partner = Auth::user();
        
        // Ensure user is a partner organization
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get applications submitted by this partner organization
        $query = ScholarshipApplication::with(['scholarship', 'student', 'reviewer'])
            ->where('user_id', $partner->id)
            ->orWhereHas('student', function($q) use ($partner) {
                $q->where('partner_id', $partner->id);
            });

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by scholarship category
        if ($request->filled('category')) {
            $query->whereHas('scholarship', function($q) use ($request) {
                $q->where('category', $request->category);
            });
        }

        // Search by student name or application ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('application_id', 'like', "%{$search}%")
                  ->orWhereHas('student', function($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $applications = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get statistics
        $stats = [
            'total_applications' => ScholarshipApplication::where('user_id', $partner->id)->count(),
            'pending_applications' => ScholarshipApplication::where('user_id', $partner->id)->where('status', 'pending')->count(),
            'approved_applications' => ScholarshipApplication::where('user_id', $partner->id)->where('status', 'approved')->count(),
            'rejected_applications' => ScholarshipApplication::where('user_id', $partner->id)->where('status', 'rejected')->count(),
        ];

        return view('partner.applications.index', compact('applications', 'stats'));
    }

    /**
     * Show the form for creating a new application.
     */
    public function create(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Get partner's students
        $students = PartnerStudent::where('partner_id', $partner->id)
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        // Get available scholarships (open for applications)
        $scholarships = Scholarship::where('is_open', true)
            ->where('application_deadline', '>', now())
            ->whereIn('category', ['primary', 'secondary']) // Partner orgs can only apply for primary/secondary
            ->orderBy('application_deadline')
            ->get();

        $selectedScholarship = null;
        if ($request->filled('scholarship_id')) {
            $selectedScholarship = $scholarships->find($request->scholarship_id);
        }

        return view('partner.applications.create', compact('students', 'scholarships', 'selectedScholarship'));
    }

    /**
     * Store a newly created application.
     */
    public function store(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $validator = Validator::make($request->all(), [
            'scholarship_id' => 'required|exists:scholarships,id',
            'student_id' => 'required|exists:partner_students,id',
            'guardian_name' => 'required|string|max:255',
            'guardian_phone' => 'required|string|max:20',
            'guardian_email' => 'nullable|email|max:255',
            'reason' => 'required|string|min:50|max:1000',
            'supporting_documents' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $scholarship = Scholarship::findOrFail($request->scholarship_id);
        $student = PartnerStudent::where('id', $request->student_id)
            ->where('partner_id', $partner->id)
            ->firstOrFail();

        // Check if student already has an application for this scholarship
        $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarship->id)
            ->where('student_id', $student->id)
            ->first();

        if ($existingApplication) {
            return redirect()->back()
                ->withErrors(['student_id' => 'This student has already applied for this scholarship.'])
                ->withInput();
        }

        // Handle file upload
        $uploadedFiles = [];
        if ($request->hasFile('supporting_documents')) {
            $file = $request->file('supporting_documents');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('scholarship_applications', $filename, 'public');
            $uploadedFiles['supporting_documents'] = $path;
        }

        // Generate unique application ID
        $applicationId = 'HLTKKQ-SCH-' . str_pad(ScholarshipApplication::count() + 1, 4, '0', STR_PAD_LEFT);

        // Create application
        $application = ScholarshipApplication::create([
            'application_id' => $applicationId,
            'scholarship_id' => $scholarship->id,
            'user_id' => $partner->id,
            'student_id' => $student->id,
            'application_type' => 'institutional',
            'guardian_name' => $request->guardian_name,
            'guardian_phone' => $request->guardian_phone,
            'guardian_email' => $request->guardian_email,
            'form_data' => [
                'student_name' => $student->name,
                'student_age' => $student->age,
                'student_class' => $student->class,
                'student_gender' => $student->gender,
                'reason' => $request->reason,
                'guardian_name' => $request->guardian_name,
                'guardian_phone' => $request->guardian_phone,
                'guardian_email' => $request->guardian_email,
            ],
            'uploaded_files' => $uploadedFiles,
            'status' => 'pending',
            'submitted_at' => now(),
        ]);

        // Update student's application history
        $student->addApplicationToHistory([
            'application_id' => $application->application_id,
            'scholarship_title' => $scholarship->title,
            'category' => $scholarship->category,
            'status' => 'pending',
        ]);

        return redirect()->route('partner.applications.show', $application)
            ->with('success', 'Application submitted successfully!');
    }

    /**
     * Display the specified application.
     */
    public function show(ScholarshipApplication $application)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Ensure the application belongs to this partner
        if ($application->user_id !== $partner->id) {
            abort(403, 'Access denied. You can only view your own applications.');
        }

        $application->load(['scholarship', 'student', 'reviewer']);

        return view('partner.applications.show', compact('application'));
    }

    /**
     * Show the form for editing the specified application.
     */
    public function edit(ScholarshipApplication $application)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Ensure the application belongs to this partner
        if ($application->user_id !== $partner->id) {
            abort(403, 'Access denied. You can only edit your own applications.');
        }

        // Only allow editing of pending applications
        if ($application->status !== 'pending') {
            return redirect()->route('partner.applications.show', $application)
                ->with('error', 'You can only edit pending applications.');
        }

        $students = PartnerStudent::where('partner_id', $partner->id)
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        return view('partner.applications.edit', compact('application', 'students'));
    }

    /**
     * Update the specified application.
     */
    public function update(Request $request, ScholarshipApplication $application)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Ensure the application belongs to this partner
        if ($application->user_id !== $partner->id) {
            abort(403, 'Access denied. You can only edit your own applications.');
        }

        // Only allow editing of pending applications
        if ($application->status !== 'pending') {
            return redirect()->route('partner.applications.show', $application)
                ->with('error', 'You can only edit pending applications.');
        }

        $validator = Validator::make($request->all(), [
            'guardian_name' => 'required|string|max:255',
            'guardian_phone' => 'required|string|max:20',
            'guardian_email' => 'nullable|email|max:255',
            'reason' => 'required|string|min:50|max:1000',
            'supporting_documents' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle file upload
        $uploadedFiles = $application->uploaded_files ?? [];
        if ($request->hasFile('supporting_documents')) {
            // Delete old file if exists
            if (isset($uploadedFiles['supporting_documents'])) {
                Storage::disk('public')->delete($uploadedFiles['supporting_documents']);
            }
            
            $file = $request->file('supporting_documents');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('scholarship_applications', $filename, 'public');
            $uploadedFiles['supporting_documents'] = $path;
        }

        // Update form data
        $formData = $application->form_data;
        $formData['reason'] = $request->reason;
        $formData['guardian_name'] = $request->guardian_name;
        $formData['guardian_phone'] = $request->guardian_phone;
        $formData['guardian_email'] = $request->guardian_email;

        // Update application
        $application->update([
            'guardian_name' => $request->guardian_name,
            'guardian_phone' => $request->guardian_phone,
            'guardian_email' => $request->guardian_email,
            'form_data' => $formData,
            'uploaded_files' => $uploadedFiles,
        ]);

        return redirect()->route('partner.applications.show', $application)
            ->with('success', 'Application updated successfully!');
    }

    /**
     * Remove the specified application.
     */
    public function destroy(ScholarshipApplication $application)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        // Ensure the application belongs to this partner
        if ($application->user_id !== $partner->id) {
            abort(403, 'Access denied. You can only delete your own applications.');
        }

        // Only allow deletion of pending applications
        if ($application->status !== 'pending') {
            return redirect()->route('partner.applications.index')
                ->with('error', 'You can only delete pending applications.');
        }

        // Delete uploaded files
        if ($application->uploaded_files) {
            foreach ($application->uploaded_files as $file) {
                Storage::disk('public')->delete($file);
            }
        }

        $application->delete();

        return redirect()->route('partner.applications.index')
            ->with('success', 'Application deleted successfully!');
    }
}
