<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Volunteer;
use App\Models\BlogPost;
use App\Models\Program;
use App\Models\Event;
use App\Models\Donation;
use App\Models\DonationCampaign;
use App\Models\ContactMessage;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Temporarily bypass authentication for testing
        if (!auth()->check()) {
            auth()->loginUsingId(1); // Login as admin user
        }
        // Get comprehensive stats
        $stats = [
            'totalUsers' => User::count(),
            'newUsersThisMonth' => User::whereMonth('created_at', Carbon::now()->month)
                                     ->whereYear('created_at', Carbon::now()->year)
                                     ->count(),
            'totalDonations' => Donation::where('payment_status', 'completed')->sum('amount'),
            'donationsThisMonth' => Donation::where('payment_status', 'completed')
                                           ->whereMonth('created_at', Carbon::now()->month)
                                           ->whereYear('created_at', Carbon::now()->year)
                                           ->sum('amount'),
            'activePrograms' => Program::where('status', 'active')->count(),
            'totalVolunteers' => Volunteer::where('application_status', 'approved')->count(),
            'pendingVolunteers' => Volunteer::where('application_status', 'pending')->count(),
            'scholarshipApplications' => ScholarshipApplication::where('status', 'pending')->count(),
            'totalScholarshipApplications' => ScholarshipApplication::count(),
            'approvedScholarships' => ScholarshipApplication::where('status', 'approved')->count(),
            'activeScholarships' => Scholarship::where('is_open', true)->count()
        ];

        // Get recent activities
        $recentActivities = $this->getRecentActivities();

        // Get recent donations
        $recentDonations = Donation::with(['donor', 'campaign'])
            ->where('payment_status', 'completed')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get donation stats for chart
        $donationStats = Donation::where('payment_status', 'completed')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(amount) as amount')
            )
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentActivities',
            'recentDonations',
            'donationStats'
        ));
    }

    private function getRecentActivities()
    {
        $activities = [];

        // Recent user registrations
        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['id', 'first_name', 'last_name', 'email', 'created_at']);
        
        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_registration',
                'title' => 'New user registered',
                'description' => "{$user->first_name} {$user->last_name} joined the platform",
                'user' => "{$user->first_name} {$user->last_name}",
                'timestamp' => $user->created_at->format('Y-m-d H:i:s'),
                'icon' => 'user-plus'
            ];
        }

        // Recent donations
        $recentDonations = Donation::with('donor')
            ->where('payment_status', 'completed')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentDonations as $donation) {
            $activities[] = [
                'type' => 'donation',
                'title' => 'New donation received',
                'description' => "₦" . number_format($donation->amount, 2) . " donated" . ($donation->donor ? " by {$donation->donor->first_name} {$donation->donor->last_name}" : ''),
                'amount' => $donation->amount,
                'timestamp' => $donation->created_at->format('Y-m-d H:i:s'),
                'icon' => 'heart'
            ];
        }

        // Recent volunteer applications
        $recentVolunteers = Volunteer::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentVolunteers as $volunteer) {
            $activities[] = [
                'type' => 'volunteer_application',
                'title' => 'New volunteer application',
                'description' => "{$volunteer->user->first_name} {$volunteer->user->last_name} applied to volunteer",
                'status' => $volunteer->application_status,
                'timestamp' => $volunteer->created_at->format('Y-m-d H:i:s'),
                'icon' => 'users'
            ];
        }

        // Sort by timestamp
        usort($activities, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        // Return only the 10 most recent activities
        return array_slice($activities, 0, 10);
    }
} 