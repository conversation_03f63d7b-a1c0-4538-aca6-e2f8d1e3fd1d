@extends('layouts.dashboard')

@section('title', 'Apply for Scholarship - Partner Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="[
                    ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                    ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                    ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                    ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                    ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                    ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                    ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                ]"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="[
                        ['label' => 'Dashboard', 'icon' => 'home', 'url' => '/partner-dashboard'],
                        ['label' => 'Manage Students', 'icon' => 'users', 'url' => '/partner/students'],
                        ['label' => 'Applications', 'icon' => 'file-alt', 'url' => '/partner/applications'],
                        ['label' => 'Scholarships', 'icon' => 'graduation-cap', 'url' => '/partner/scholarships', 'active' => true],
                        ['label' => 'Organization Profile', 'icon' => 'building', 'url' => '/partner/profile'],
                        ['label' => 'Reports', 'icon' => 'chart-bar', 'url' => '/partner/reports'],
                        ['label' => 'Support', 'icon' => 'life-ring', 'url' => '/partner/support']
                    ]"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center mb-4">
                        <a href="{{ route('partner.scholarships.index') }}" class="mr-4 p-2 hover:bg-white/20 rounded-lg transition-colors duration-300">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Apply for Scholarship</h1>
                            <p class="text-green-100 text-sm lg:text-base">{{ $scholarship->title }}</p>
                        </div>
                    </div>
                    
                    <!-- Scholarship Info -->
                    <div class="bg-white/10 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-green-200">Category:</span>
                                <span class="ml-2 font-semibold">{{ ucfirst($scholarship->category) }} School</span>
                            </div>
                            <div>
                                <span class="text-green-200">Amount:</span>
                                <span class="ml-2 font-semibold">₦{{ number_format($scholarship->amount, 0) }}</span>
                            </div>
                            <div>
                                <span class="text-green-200">Deadline:</span>
                                <span class="ml-2 font-semibold">{{ $scholarship->application_deadline->format('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Form -->
            <div class="p-6 lg:p-8">
                <div class="max-w-4xl mx-auto">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle mr-2"></i>
                                {{ session('error') }}
                            </div>
                        </div>
                    @endif

                    @if(session('info'))
                        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle mr-2"></i>
                                {{ session('info') }}
                            </div>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('partner.scholarships.store') }}" enctype="multipart/form-data" class="space-y-8">
                        @csrf
                        <input type="hidden" name="scholarship_id" value="{{ $scholarship->id }}">

                        <!-- Student Selection -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                <i class="fas fa-user-graduate text-green-600 mr-2"></i>
                                Select Student
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Student <span class="text-red-500">*</span>
                                    </label>
                                    <select id="student_id" 
                                            name="student_id" 
                                            required
                                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('student_id') border-red-500 @enderror">
                                        <option value="">Select a student</option>
                                        @foreach($students as $student)
                                            <option value="{{ $student->id }}" 
                                                    data-name="{{ $student->name }}"
                                                    data-class="{{ $student->class }}"
                                                    data-age="{{ $student->age }}"
                                                    data-gender="{{ $student->gender }}"
                                                    data-parent-name="{{ $student->parent_name }}"
                                                    data-parent-contact="{{ $student->parent_contact }}"
                                                    {{ old('student_id') == $student->id ? 'selected' : '' }}>
                                                {{ $student->name }} - Class {{ $student->class }} ({{ $student->gender }}, {{ $student->age }} years)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('student_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div id="student-info" class="hidden">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Student Information</h4>
                                    <div class="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                                        <div><span class="font-medium">Name:</span> <span id="info-name">-</span></div>
                                        <div><span class="font-medium">Class:</span> <span id="info-class">-</span></div>
                                        <div><span class="font-medium">Age:</span> <span id="info-age">-</span></div>
                                        <div><span class="font-medium">Gender:</span> <span id="info-gender">-</span></div>
                                        <div><span class="font-medium">Parent:</span> <span id="info-parent">-</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Guardian Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                <i class="fas fa-users text-green-600 mr-2"></i>
                                Guardian Information
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="guardian_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Full Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           id="guardian_name" 
                                           name="guardian_name" 
                                           value="{{ old('guardian_name') }}"
                                           required
                                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('guardian_name') border-red-500 @enderror">
                                    @error('guardian_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="guardian_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Phone Number <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" 
                                           id="guardian_phone" 
                                           name="guardian_phone" 
                                           value="{{ old('guardian_phone') }}"
                                           required
                                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('guardian_phone') border-red-500 @enderror">
                                    @error('guardian_phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label for="guardian_email" class="block text-sm font-medium text-gray-700 mb-2">
                                        Guardian Email Address <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" 
                                           id="guardian_email" 
                                           name="guardian_email" 
                                           value="{{ old('guardian_email') }}"
                                           required
                                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('guardian_email') border-red-500 @enderror">
                                    @error('guardian_email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Custom Fields -->
                        @if(!empty($customFields))
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-clipboard-list text-green-600 mr-2"></i>
                                    Additional Information
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @foreach($customFields as $fieldKey => $fieldLabel)
                                        <div class="md:col-span-2">
                                            <label for="custom_{{ $fieldKey }}" class="block text-sm font-medium text-gray-700 mb-2">
                                                {{ $fieldLabel }}
                                            </label>
                                            <textarea id="custom_{{ $fieldKey }}" 
                                                      name="custom_{{ $fieldKey }}" 
                                                      rows="3"
                                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">{{ old('custom_' . $fieldKey) }}</textarea>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Document Upload -->
                        @if(!empty($documentsRequired))
                            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-file-upload text-green-600 mr-2"></i>
                                    Required Documents
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @foreach($documentsRequired as $index => $document)
                                        <div>
                                            <label for="document_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-2">
                                                {{ $document }} <span class="text-red-500">*</span>
                                            </label>
                                            <input type="file" 
                                                   id="document_{{ $index }}" 
                                                   name="documents[{{ $document }}]" 
                                                   accept=".pdf,.jpg,.jpeg,.png"
                                                   required
                                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                            <p class="mt-1 text-xs text-gray-500">Accepted formats: PDF, JPG, PNG (Max: 5MB)</p>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Submit Button -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex flex-col sm:flex-row gap-4 justify-end">
                                <a href="{{ route('partner.scholarships.index') }}" 
                                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-300 text-center">
                                    Cancel
                                </a>
                                <button type="submit" 
                                        class="px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300 font-medium">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Submit Application
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    if (mobileMenuBtn) mobileMenuBtn.addEventListener('click', openMobileMenu);
    if (mobileSidebarOverlay) mobileSidebarOverlay.addEventListener('click', closeMobileMenu);

    // Student selection functionality
    const studentSelect = document.getElementById('student_id');
    const studentInfo = document.getElementById('student-info');
    const guardianNameInput = document.getElementById('guardian_name');
    const guardianPhoneInput = document.getElementById('guardian_phone');

    if (studentSelect) {
        studentSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (selectedOption.value) {
                // Show student info
                studentInfo.classList.remove('hidden');
                
                // Update info display
                document.getElementById('info-name').textContent = selectedOption.dataset.name || '-';
                document.getElementById('info-class').textContent = selectedOption.dataset.class || '-';
                document.getElementById('info-age').textContent = selectedOption.dataset.age || '-';
                document.getElementById('info-gender').textContent = selectedOption.dataset.gender || '-';
                document.getElementById('info-parent').textContent = selectedOption.dataset.parentName || '-';
                
                // Pre-fill guardian information if available
                if (selectedOption.dataset.parentName && !guardianNameInput.value) {
                    guardianNameInput.value = selectedOption.dataset.parentName;
                }
                if (selectedOption.dataset.parentContact && !guardianPhoneInput.value) {
                    guardianPhoneInput.value = selectedOption.dataset.parentContact;
                }
            } else {
                studentInfo.classList.add('hidden');
            }
        });

        // Trigger change event if there's a pre-selected value
        if (studentSelect.value) {
            studentSelect.dispatchEvent(new Event('change'));
        }
    }
});
</script>
@endsection
