@extends('auth.layout')

@section('title', 'Reset Password')
@section('header-title', 'Reset Password')
@section('header-subtitle', 'Enter your email to receive a password reset link')

@section('content')
    @if (session('status'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
        </div>
    @endif

    @if ($errors->any())
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Error</h6>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form method="POST" action="{{ route('password.email') }}">
        @csrf
        
        <div class="mb-4">
            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-envelope text-muted"></i>
                </span>
                <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email') }}" required autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            <small class="text-muted">We'll send you a link to reset your password.</small>
        </div>

        <div class="d-grid mb-3">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane me-2"></i>Send Reset Link
            </button>
        </div>

        <div class="auth-links">
            <p class="text-muted mb-2">Remember your password?</p>
            <a href="{{ route('login') }}">Back to Login</a>
        </div>
    </form>
@endsection

@push('styles')
<style>
    .input-group-text {
        border-color: #e5e7eb;
    }
    
    .form-control.border-start-0 {
        border-left: none !important;
    }
    
    .form-control.border-start-0:focus {
        border-left: none !important;
        box-shadow: none;
    }
    
    .input-group:focus-within .input-group-text {
        border-color: var(--primary-green);
        background-color: rgba(5, 150, 105, 0.05);
    }
    
    .input-group:focus-within .form-control {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
</style>
@endpush
