/** @type {import('next').NextConfig} */
const nextConfig = {
  // Consider enabling ESLint checks during builds for better code quality
  // eslint: {
  //   ignoreDuringBuilds: true,
  // },
  // Consider enabling TypeScript checks during builds for early error detection
  // typescript: {
  //   ignoreBuildErrors: true,
  // },
  
  // Security headers configuration
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' 'inline-speculation-rules' https: chrome-extension: chrome-extension://9bb310a9-2a7c-4c5f-8b3f-efa4c117c7d2/",
              "style-src 'self' 'unsafe-inline' https:",
              "img-src 'self' data: blob: https:",
              "font-src 'self' data: https:",
              "connect-src 'self' http://localhost:8000 http://127.0.0.1:8000 https:",
              "frame-src 'self' https:",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'"
            ].join('; ')
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]
  },
  
  images: {
    // Using Next.js Image Optimization is recommended for performance.
    // If you have a specific reason to disable it, ensure images are otherwise optimized.
    // unoptimized: true, 
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
      },
    ],
  },
}

export default nextConfig
