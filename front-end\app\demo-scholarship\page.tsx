'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BookOpen,
  GraduationCap,
  School,
  ArrowRight,
  CheckCircle,
  Users,
  Award,
  Target
} from 'lucide-react'
import Link from 'next/link'

export default function DemoScholarshipPage() {
  const scholarshipCategories = [
    {
      id: 'primary',
      title: 'Primary School Scholarship',
      description: 'Supporting young learners in their foundational education journey',
      icon: BookOpen,
      color: 'bg-blue-500',
      borderColor: 'border-blue-200',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      features: [
        'For Primary 1-6 students',
        'Filled by Parent/Guardian',
        'School fee coverage',
        'Educational materials support'
      ],
      eligibility: 'Students aged 6-12 years in primary education',
      amount: '₦50,000 - ₦150,000'
    },
    {
      id: 'secondary',
      title: 'Secondary School Scholarship',
      description: 'Empowering teenagers to complete their secondary education',
      icon: School,
      color: 'bg-green-500',
      borderColor: 'border-green-200',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      features: [
        'For JSS 1 - SS 3 students',
        'Filled by Student',
        'Tuition fee assistance',
        'Examination fee coverage'
      ],
      eligibility: 'Students aged 12-18 years in secondary education',
      amount: '₦100,000 - ₦300,000'
    },
    {
      id: 'university',
      title: 'University Scholarship',
      description: 'Supporting higher education and career development',
      icon: GraduationCap,
      color: 'bg-purple-500',
      borderColor: 'border-purple-200',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      features: [
        'For 100L - 600L students',
        'Filled by Student',
        'Tuition fee support',
        'Research project funding'
      ],
      eligibility: 'University students with valid matriculation number',
      amount: '₦200,000 - ₦500,000'
    }
  ]

  const applicationProcess = [
    {
      step: 1,
      title: 'Choose Category',
      description: 'Select the appropriate scholarship category based on your education level'
    },
    {
      step: 2,
      title: 'Fill Application',
      description: 'Complete the form with accurate information and upload required documents'
    },
    {
      step: 3,
      title: 'Submit & Wait',
      description: 'Submit your application and wait for review (5-10 business days)'
    },
    {
      step: 4,
      title: 'Get Results',
      description: 'Receive notification via email and SMS about your application status'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Scholarship Opportunities
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100">
            Empowering education at every level - from primary school to university
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/scholarship-application">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-4 text-lg">
                Apply Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 text-lg">
              Learn More
            </Button>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-4xl font-bold text-blue-600">500+</div>
              <div className="text-gray-600">Students Supported</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-green-600">₦50M+</div>
              <div className="text-gray-600">Total Scholarships</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-purple-600">95%</div>
              <div className="text-gray-600">Success Rate</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold text-orange-600">3</div>
              <div className="text-gray-600">Education Levels</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scholarship Categories */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Scholarship Categories
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We offer comprehensive scholarship programs tailored to different education levels
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {scholarshipCategories.map((category) => {
              const IconComponent = category.icon
              return (
                <Card key={category.id} className={`border-2 ${category.borderColor} hover:shadow-xl transition-all duration-300 hover:scale-105`}>
                  <CardHeader className={`${category.bgColor} text-center`}>
                    <div className={`inline-flex p-4 rounded-full ${category.color} text-white mx-auto mb-4`}>
                      <IconComponent className="h-8 w-8" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-900">
                      {category.title}
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      {category.description}
                    </CardDescription>
                  </CardHeader>
                  
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                      <ul className="space-y-1">
                        {category.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="space-y-2">
                      <div>
                        <span className="font-semibold text-gray-900">Eligibility: </span>
                        <span className="text-gray-600 text-sm">{category.eligibility}</span>
                      </div>
                      <div>
                        <span className="font-semibold text-gray-900">Amount: </span>
                        <Badge variant="secondary" className={`${category.textColor} font-semibold`}>
                          {category.amount}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>

      {/* Application Process */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Application Process
            </h2>
            <p className="text-lg text-gray-600">
              Simple steps to apply for your scholarship
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {applicationProcess.map((process, index) => (
              <div key={process.step} className="text-center">
                <div className="relative">
                  <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  {index < applicationProcess.length - 1 && (
                    <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-blue-200 -translate-y-0.5"></div>
                  )}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {process.title}
                </h3>
                <p className="text-gray-600">
                  {process.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Apply?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Take the first step towards securing your educational future
          </p>
          <Link href="/scholarship-application">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-12 py-4 text-lg">
              Start Your Application
              <ArrowRight className="ml-2 h-6 w-6" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
