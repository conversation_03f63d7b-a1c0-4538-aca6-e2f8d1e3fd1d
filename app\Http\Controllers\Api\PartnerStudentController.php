<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PartnerStudentController extends Controller
{
    // Laravel 11: Middleware is applied at route level, not in constructor

    /**
     * Get all students managed by this Partner Organization
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $query = Student::where('school_id', $partnerOrg->id)
                ->with(['progressions' => function($q) {
                    $q->orderBy('academic_year', 'desc');
                }]);

            // Filter by grade level
            if ($request->has('grade_level')) {
                $query->whereHas('progressions', function ($q) use ($request) {
                    $q->where('grade_level', $request->grade_level)
                      ->where('is_current', true);
                });
            }

            // Filter by academic year
            if ($request->has('academic_year')) {
                $query->whereHas('progressions', function ($q) use ($request) {
                    $q->where('academic_year', $request->academic_year);
                });
            }

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Search by name or student ID
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('full_name', 'like', "%{$search}%")
                      ->orWhere('student_id', 'like', "%{$search}%");
                });
            }

            $students = $query->orderBy('full_name', 'asc')
                            ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $students,
                'message' => 'Students retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new student
     */
    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            // Validate request
            $validator = $this->validateStudentData($request);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Check if student already exists
            $existingStudent = Student::where('full_name', $request->full_name)
                ->where('school_id', $partnerOrg->id)
                ->where('date_of_birth', $request->date_of_birth)
                ->first();

            if ($existingStudent) {
                return response()->json([
                    'success' => false,
                    'message' => 'A student with this name and date of birth already exists in your organization'
                ], 400);
            }

            // Create student
            $student = Student::create([
                'student_id' => Student::generateStudentId(),
                'full_name' => $request->full_name,
                'date_of_birth' => $request->date_of_birth,
                'age' => $request->age,
                'gender' => $request->gender ?? 'not_specified',
                'parent_name' => $request->parent_name,
                'parent_phone' => $request->parent_phone,
                'parent_email' => $request->parent_email,
                'home_address' => $request->home_address,
                'school_id' => $partnerOrg->id,
                'enrollment_date' => $request->enrollment_date ?? now(),
                'status' => 'active',
                'additional_info' => $request->additional_info,
            ]);

            // Create initial progression record
            StudentProgression::create([
                'student_id' => $student->id,
                'academic_year' => $request->academic_year,
                'grade_level' => $request->grade_level,
                'school_id' => $partnerOrg->id,
                'enrollment_date' => $request->enrollment_date ?? now(),
                'status' => 'active',
                'is_current' => true,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $student->load(['progressions']),
                'message' => 'Student created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific student
     */
    public function show($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $student = Student::where('school_id', $partnerOrg->id)
                ->with(['progressions' => function($q) {
                    $q->orderBy('academic_year', 'desc');
                }])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $student,
                'message' => 'Student retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Student not found or access denied',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update a student
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $student = Student::where('school_id', $partnerOrg->id)->findOrFail($id);

            // Validate request
            $validator = $this->validateStudentData($request, $student->id);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Update student
            $student->update([
                'full_name' => $request->full_name,
                'date_of_birth' => $request->date_of_birth,
                'age' => $request->age,
                'gender' => $request->gender ?? $student->gender,
                'parent_name' => $request->parent_name,
                'parent_phone' => $request->parent_phone,
                'parent_email' => $request->parent_email,
                'home_address' => $request->home_address,
                'additional_info' => $request->additional_info,
                'updated_at' => now(),
            ]);

            // Update current progression if provided
            if ($request->has('grade_level') || $request->has('academic_year')) {
                $currentProgression = StudentProgression::where('student_id', $student->id)
                    ->where('is_current', true)
                    ->first();

                if ($currentProgression) {
                    $currentProgression->update([
                        'grade_level' => $request->grade_level ?? $currentProgression->grade_level,
                        'academic_year' => $request->academic_year ?? $currentProgression->academic_year,
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $student->load(['progressions']),
                'message' => 'Student updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a student
     */
    public function destroy($id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $student = Student::where('school_id', $partnerOrg->id)->findOrFail($id);

            // Check if student has any scholarship applications
            if ($student->applications()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete student with existing scholarship applications'
                ], 400);
            }

            // Delete progressions first
            StudentProgression::where('student_id', $student->id)->delete();

            // Delete student
            $student->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Advance student to next grade level
     */
    public function advanceGrade(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $student = Student::where('school_id', $partnerOrg->id)->findOrFail($id);

            $validator = Validator::make($request->all(), [
                'new_grade_level' => 'required|string|in:primary_1,primary_2,primary_3,primary_4,primary_5,primary_6,secondary_1,secondary_2,secondary_3,secondary_4,secondary_5,secondary_6',
                'academic_year' => 'required|string|regex:/^\d{4}\/\d{4}$/',
                'notes' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get current progression
            $currentProgression = StudentProgression::where('student_id', $student->id)
                ->where('is_current', true)
                ->first();

            if (!$currentProgression) {
                return response()->json([
                    'success' => false,
                    'message' => 'No current progression found for this student'
                ], 400);
            }

            // Mark current progression as not current
            $currentProgression->update([
                'is_current' => false,
                'end_date' => now(),
                'status' => 'completed',
            ]);

            // Create new progression
            $newProgression = StudentProgression::create([
                'student_id' => $student->id,
                'academic_year' => $request->academic_year,
                'grade_level' => $request->new_grade_level,
                'previous_grade_level' => $currentProgression->grade_level,
                'school_id' => $partnerOrg->id,
                'enrollment_date' => now(),
                'status' => 'active',
                'is_current' => true,
                'progression_notes' => $request->notes,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => $student->load(['progressions']),
                    'new_progression' => $newProgression,
                ],
                'message' => 'Student advanced to next grade successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to advance student grade',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student statistics for the Partner Organization
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $stats = [
                'total_students' => Student::where('school_id', $partnerOrg->id)->count(),
                'active_students' => Student::where('school_id', $partnerOrg->id)->where('status', 'active')->count(),
                'students_by_grade' => Student::where('school_id', $partnerOrg->id)
                    ->join('student_progressions', 'students.id', '=', 'student_progressions.student_id')
                    ->where('student_progressions.is_current', true)
                    ->select('student_progressions.grade_level', DB::raw('count(*) as count'))
                    ->groupBy('student_progressions.grade_level')
                    ->get(),
                'recent_enrollments' => Student::where('school_id', $partnerOrg->id)
                    ->where('enrollment_date', '>=', now()->subDays(30))
                    ->count(),
                'scholarship_applications' => Student::where('school_id', $partnerOrg->id)
                    ->withCount('applications')
                    ->get()
                    ->sum('applications_count'),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate student data
     */
    private function validateStudentData(Request $request, $studentId = null)
    {
        $rules = [
            'full_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'age' => 'required|integer|min:5|max:25',
            'gender' => 'nullable|string|in:male,female,not_specified',
            'parent_name' => 'required|string|max:255',
            'parent_phone' => 'required|string|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'parent_email' => 'nullable|email|max:255',
            'home_address' => 'nullable|string|max:500',
            'grade_level' => 'required|string|in:primary_1,primary_2,primary_3,primary_4,primary_5,primary_6,secondary_1,secondary_2,secondary_3,secondary_4,secondary_5,secondary_6',
            'academic_year' => 'required|string|regex:/^\d{4}\/\d{4}$/',
            'enrollment_date' => 'nullable|date',
            'additional_info' => 'nullable|string|max:1000',
        ];

        return Validator::make($request->all(), $rules);
    }
}
