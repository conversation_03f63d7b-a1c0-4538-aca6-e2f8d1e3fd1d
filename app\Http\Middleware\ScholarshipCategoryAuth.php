<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\Scholarship;
use App\Models\Student;

class ScholarshipCategoryAuth
{
    /**
     * Handle an incoming request for scholarship category-based authorization
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();

        // Get scholarship from request (either ID parameter or scholarship_id in body)
        $scholarshipId = $request->route('scholarshipId') 
            ?? $request->route('id') 
            ?? $request->input('scholarship_id');

        if ($scholarshipId) {
            $scholarship = Scholarship::find($scholarshipId);
            
            if ($scholarship && !$this->canAccessScholarshipCategory($user, $scholarship->category)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this scholarship category',
                    'error' => 'Category access denied',
                    'scholarship_category' => $scholarship->category,
                    'user_role' => $user->role
                ], 403);
            }
        }

        // Get student from request for student-specific operations
        $studentId = $request->route('studentId') 
            ?? $request->input('student_id');

        if ($studentId) {
            $student = Student::find($studentId);
            
            if ($student && !$this->canAccessStudentData($user, $student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to student data',
                    'error' => 'Student access denied'
                ], 403);
            }
        }

        return $next($request);
    }

    /**
     * Check if user can access specific scholarship category
     */
    private function canAccessScholarshipCategory($user, string $category): bool
    {
        // Admins can access all categories
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can access primary and secondary categories
        if ($user->role === 'partner_organization') {
            return in_array($category, ['primary', 'secondary']);
        }

        // Individual users (students) can access university category
        if ($user->role === 'user') {
            return $category === 'university';
        }

        return false;
    }

    /**
     * Check if user can access student data
     */
    private function canAccessStudentData($user, Student $student): bool
    {
        // Admins can access all student data
        if ($user->role === 'admin') {
            return true;
        }

        // Partner organizations can only access their own students
        if ($user->role === 'partner_organization') {
            return $student->school_id === $user->partner_organization_id;
        }

        // Individual students can only access their own data
        if ($user->role === 'user') {
            return $student->user_id === $user->id;
        }

        return false;
    }
}
